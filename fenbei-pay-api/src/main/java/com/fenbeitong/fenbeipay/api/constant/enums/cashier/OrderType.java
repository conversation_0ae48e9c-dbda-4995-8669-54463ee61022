package com.fenbeitong.fenbeipay.api.constant.enums.cashier;

import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.OrderTypeEnum;
import com.fenbeitong.finhub.common.constant.SceneTypeEnum;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * 订单类型(场景类型)
 */
public enum OrderType {
    /**
     * 3-出行用车;7, "机票";11, "特惠酒店";15, "火车";20, "优选商城";21, "采购售后单";30, "用餐";40, "国际机票";50, "外卖";60, "美食";99, "保险";100, "用车保险";101, "国内机票保险";102, "酒店保险";103, "火车保险";
     * 123, "卡券专区";124, "电影票";125, "充值中心";126, "分贝通虚拟卡";130, "闪送";131, "快递";911, "回填单";999, "系统操作"
     **/
    //默认商务账户
    UNKNOW(-1, "未知", "uk", FundAccountSubType.BUSINESS_ACCOUNT),
    Taxi(3, "出行用车", "tx", FundAccountSubType.BUSINESS_ACCOUNT),
    Air(7, "机票", "at", FundAccountSubType.BUSINESS_ACCOUNT),
    Hotel(11, "特惠酒店", "ht", FundAccountSubType.BUSINESS_ACCOUNT),
    Train(15, "火车", "tn", FundAccountSubType.BUSINESS_ACCOUNT),
    Mall(20, "优选商城", "ml", FundAccountSubType.BUSINESS_ACCOUNT),
    MallService(21, "采购售后单", "ms", FundAccountSubType.BUSINESS_ACCOUNT),
    Dinner(30, "用餐", "dn", FundAccountSubType.BUSINESS_ACCOUNT),
    IntlAir(40, "国际机票", "ia", FundAccountSubType.BUSINESS_ACCOUNT),
    Takeaway(50, "外卖", "ta", FundAccountSubType.BUSINESS_ACCOUNT),
    MeiShi(60, "美食", "md", FundAccountSubType.BUSINESS_ACCOUNT),
    Insurance(99, "保险", "in", FundAccountSubType.BUSINESS_ACCOUNT),
    TaxiInsurance(100, "用车保险", "ti", FundAccountSubType.BUSINESS_ACCOUNT),
    AirInsurance(101, "国内机票保险", "ai", FundAccountSubType.BUSINESS_ACCOUNT),
    HotelInsurance(102, "酒店保险", "hi", FundAccountSubType.BUSINESS_ACCOUNT),
    TrainInsurance(103, "火车保险", "ni", FundAccountSubType.BUSINESS_ACCOUNT),
    ECard(123, "卡券专区", "ec", FundAccountSubType.BUSINESS_ACCOUNT),
    MovieTicket(124, "电影票", "mt", FundAccountSubType.BUSINESS_ACCOUNT),
    Recharge(125, "充值中心", "re", FundAccountSubType.BUSINESS_ACCOUNT),
    BANK_INDIVIDUAL(126, "分贝通虚拟卡", "bk", FundAccountSubType.BUSINESS_ACCOUNT),
    ACCT_PUBLIC_PAY(128, "对公支付", "ap",FundAccountSubType.UNKNOW),
    EXPRESS_CITY(130, "闪送", "ey", FundAccountSubType.BUSINESS_ACCOUNT),
    BUS(135, "汽车票", "bu", FundAccountSubType.BUSINESS_ACCOUNT),
    SERVICE_PURCHASE(136, "客服采购支付", "sp", FundAccountSubType.BUSINESS_ACCOUNT),
    EXPRESS(131, "快递", "ex", FundAccountSubType.BUSINESS_ACCOUNT),
    Altman(911, "回填单", "am", FundAccountSubType.BUSINESS_ACCOUNT),
    Relief(912, "减免订单","rf",FundAccountSubType.BUSINESS_ACCOUNT),
    Shadow(913, "虚拟订单", "sw",FundAccountSubType.BUSINESS_ACCOUNT),
    SYSTEM(999, "系统操作", "sy", FundAccountSubType.BUSINESS_ACCOUNT),
    REIMBURSEMENT_PAY(129,"报销打款","rp", FundAccountSubType.BUSINESS_ACCOUNT),

    SALARY(141,"发薪","sa",FundAccountSubType.BUSINESS_ACCOUNT),

    //CategoryTypeEnum.FREIGHT_TRANSPORT
    FREIGHT_TRANSPORT(150, "货运", "ft",FundAccountSubType.BUSINESS_ACCOUNT),
    SELF_DRIVER(151, "自驾", "sd",FundAccountSubType.BUSINESS_ACCOUNT),
    CAR_RENTAL(155, "租车", "cr",FundAccountSubType.BUSINESS_ACCOUNT),
    ;
    /**
     * 不检查员工是否离职、停用都能下单的场景集合
     * @return
     */
    public static boolean isNoCheckEmpContractStatusOrderType(Integer key) {
        return key == ECard.key||key == MovieTicket.key||key == Recharge.key||key == Insurance.key||key == ACCT_PUBLIC_PAY.key || key == BANK_INDIVIDUAL.key || key == Taxi.key || key == EXPRESS.key
                || key == SERVICE_PURCHASE.key || key == Altman.key || key == Relief.key || key == Shadow.key
                || key == REIMBURSEMENT_PAY.key || key == SALARY.key || key == FREIGHT_TRANSPORT.key || key == SYSTEM.key;
    }

    private int key;
    private String value;
    /**
     * 订单前缀或者支付单前缀
     */
    private String prefix;

    /**
     * 扣款账户
     */
    private FundAccountSubType accountSubType;

    OrderType(int key, String value, String prefix, FundAccountSubType accountSubType) {
        this.key = key;
        this.value = value;
        this.prefix = prefix;
        this.accountSubType = accountSubType;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getPrefix() {
        return prefix;
    }

    public FundAccountSubType getAccountSubType() {
        return accountSubType;
    }

    public static OrderType getEnum(Integer key) {
        if (key == null) {
            return UNKNOW;
        }
        for (OrderType item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return UNKNOW;
    }

    public static OrderType getEnum(String value) {
        if (StringUtils.isEmpty(value)) {
            return UNKNOW;
        }
        for (OrderType item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return UNKNOW;
    }

    public static String getNameFromCode(Integer code) {
        OrderType anEnum = getEnum(code);
        if (ObjUtils.isEmpty(anEnum)) {
            return null;
        }
        return anEnum.getValue();
    }

    public static boolean isAltman(Integer code) {
        return code == Altman.key;
    }

    public static boolean isBus(Integer code){
        return code == BUS.key;
    }

    public static boolean isBankIndividual(Integer code){
        return code == BANK_INDIVIDUAL.key;
    }

    /**
     * 统一支付(旧版本支付)还能支持的场景
     *
     * @return
     */
    public static Set<Integer> oldPaySupportOrderType() {
        Set<Integer> supportSet = new HashSet<>();
        supportSet.add(Air.getKey());
        supportSet.add(IntlAir.getKey());
        return supportSet;
    }

    public static boolean checkOrderTypeSupportOldPay(Integer key) {
        if (key == null) {
            return false;
        }
        return oldPaySupportOrderType().contains(key);
    }

    public static String getSceneName(Integer orderTypeEnumCode, Integer orderTypeKey) {
        if (orderTypeEnumCode == null || orderTypeKey == null) {
            return null;
        }
        if (orderTypeEnumCode == OrderTypeEnum.BUSINESS.getCode() || orderTypeEnumCode == OrderTypeEnum.COMPANY.getCode()) {
            SceneTypeEnum sceneTypeEnumBusiness = SceneTypeEnum.valueOf(OrderTypeEnum.BUSINESS.getCode(), orderTypeKey);
            SceneTypeEnum sceneTypeEnumCompany = SceneTypeEnum.valueOf(OrderTypeEnum.COMPANY.getCode(), orderTypeKey);
            if (sceneTypeEnumBusiness == null && sceneTypeEnumCompany == null) {
                return null;
            }
            return sceneTypeEnumBusiness != null ? sceneTypeEnumBusiness.getName() : sceneTypeEnumCompany.getName();
        }
        return SceneTypeEnum.valueOf(orderTypeEnumCode, orderTypeKey).getName();
    }

    public static boolean isTaxi(Integer key){
        return key == Taxi.key;
    }

    public static boolean isShadow(Integer key){
        return key == Shadow.getKey();
    }

    public static boolean isBillPay(Integer key){
        return key!= null && key == ACCT_PUBLIC_PAY.getKey();
    }
    
    public static boolean isForHotel(Integer key){
        return key!= null && key == SERVICE_PURCHASE.getKey();
    }

    public static boolean isReimbursement(Integer key){
        return key != null && key == REIMBURSEMENT_PAY.getKey();
    }

    public static boolean isSalary(Integer key){
        return key != null && key == SALARY.getKey();
    }

    public static boolean isCarRental(Integer key){
        return key != null && key == CAR_RENTAL.getKey();
    }
}