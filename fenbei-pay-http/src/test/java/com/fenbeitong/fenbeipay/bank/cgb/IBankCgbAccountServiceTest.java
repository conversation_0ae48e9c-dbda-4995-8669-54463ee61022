package com.fenbeitong.fenbeipay.bank.cgb;

import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.CgbDirectVirtualCardTrapReqDTO;
import com.fenbeitong.fenbeipay.api.service.bank.cgb.IBankCgbAccountService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

public class IBankCgbAccountServiceTest extends SpringBaseHttpTest {

    @Autowired
    IBankCgbAccountService iBankCgbAccountService;

    @Test
    public void addTrapTest() {
        CgbDirectVirtualCardTrapReqDTO cardTrapReqDTO = new CgbDirectVirtualCardTrapReqDTO();
        cardTrapReqDTO.setTrapAmount(new BigDecimal(180000));
        cardTrapReqDTO.setTxnId("cs123");
        cardTrapReqDTO.setApplyTransNo("cs123");
        cardTrapReqDTO.setBankAccountNo("6225687352010334805");
        cardTrapReqDTO.setEmployeeId("632bd5407310ca61ce1c4d50");
        cardTrapReqDTO.setCompanyId("632a83c053feb0470cc4ad2d");
        cardTrapReqDTO.setTBankAccountNo("6225687352010334805");
        iBankCgbAccountService.addTrap(cardTrapReqDTO);
    }
}