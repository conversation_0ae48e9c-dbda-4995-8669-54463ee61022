package com.fenbeitong.fenbeipay.bank.base.manager;

import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardDTO;
import com.fenbeitong.fenbeipay.bank.base.conver.BankCardConver;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

import static org.junit.Assert.*;

public class IBankCardManagerTest extends SpringBaseHttpTest {

    @Autowired
    IBankCardManager iBankCardManager;
    @Test
    public void updateBankCardById() {
        BankCardDTO reqDTO = new BankCardDTO();
        reqDTO.setId(4273L);
        reqDTO.setCardBalance(new BigDecimal("1000"));
        reqDTO.setCardCompanyBalance(new BigDecimal("500"));
        reqDTO.setCardPersonalBalance(new BigDecimal("500"));
        BankCard bankCard = BankCardConver.copyBankCardDTO2BankCard(reqDTO);
        iBankCardManager.updateBankCardById(bankCard,reqDTO.getId());
    }
}