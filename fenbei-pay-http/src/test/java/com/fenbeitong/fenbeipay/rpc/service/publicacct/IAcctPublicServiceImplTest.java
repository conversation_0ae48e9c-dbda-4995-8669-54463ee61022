package com.fenbeitong.fenbeipay.rpc.service.publicacct;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.manager.IBankCardTrapFlowManager;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCostImageReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.req.AcctPublicAmountPageReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.AcctPublicInfoAndAuthRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.spa.SpaReceiptQueryReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.spa.SpaReceiptQueryRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.flow.IAcctFlowService;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

public class IAcctPublicServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private IAcctPublicSearchService iAcctPublicSearchService;

    @Autowired
    private IAcctFlowService iAcctFlowService;
    @Autowired
    private IBankCardTrapFlowManager iBankCardTrapFlowManager;
    @Test
    public void consumeTest() {

        String  json = "{\"bankAccountNo\":\"3110730027033151662\",\"maxPageSize\":500,\"offset\":0,\"pageNo\":1,\"pageSize\":20}";
        AcctPublicAmountPageReqRPCDTO amountPageReqRPCDTO = JSON.parseObject(json, AcctPublicAmountPageReqRPCDTO.class);
        iAcctPublicSearchService.queryAcctPublicFlowAmountV2(amountPageReqRPCDTO);
        System.out.println("Down");
    }

    @Test
    public void updateCostImageStatusTest(){
        AcctCostImageReqDTO acctCostImageReqDTO = new AcctCostImageReqDTO();
        acctCostImageReqDTO.setCostImageStatus(1);
        acctCostImageReqDTO.setCostImageUrl("kkkkk");
        acctCostImageReqDTO.setCostImageTime(new Date());
        acctCostImageReqDTO.setFlowId("FCB202311211641530985869");
        acctCostImageReqDTO.setAccountModel(2);
        acctCostImageReqDTO.setAccountSubType(5);
        acctCostImageReqDTO.setSyncBankTransNo("3");
        acctCostImageReqDTO.setBankName("SPABANK");
        acctCostImageReqDTO.setBankTransNo("1");
        acctCostImageReqDTO.setBankAccountNo("1.00");
        iAcctFlowService.updateCostImageStatus(acctCostImageReqDTO);
        System.out.println("Down");

    }

    @Test
    public void queryReceipt(){
        SpaReceiptQueryReqDTO req = new SpaReceiptQueryReqDTO();
        req.setBizNo("RTR202311211641519868369");
        SpaReceiptQueryRespDTO spaReceiptQueryRespDTO = iBankCardTrapFlowManager.queryReceipt(req);
        System.out.println(JsonUtils.toJson(spaReceiptQueryRespDTO));
    }

    @Test
    public void findAcctAndAuthTest(){
        String companyId = "5d1b1d2f23445f4dca76304b";
        String userId = "621845d349430b0f22d81fd0";
        List<AcctPublicInfoAndAuthRespRPCDTO> acctAndAuth = iAcctPublicSearchService.findAcctAndAuth(companyId, userId);
        System.out.println("账户信息========="+JsonUtils.toJson(acctAndAuth));

    }

}
