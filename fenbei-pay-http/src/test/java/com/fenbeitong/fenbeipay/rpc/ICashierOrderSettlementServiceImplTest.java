package com.fenbeitong.fenbeipay.rpc;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.CashierPayCommonJsonDto;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.cashier.pay.CashierOrderSettlementPayService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.enums.paycenter.PayStatus;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Map;

/**
 * CashierOrderSettlementServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Dec 8, 2018</pre>
 */
public class ICashierOrderSettlementServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private CashierOrderSettlementService cashierOrderSettlementService;

    @Autowired
    private CashierOrderSettlementPayService cashierOrderSettlementPayService;

    @Autowired
    private CashierOrderRefundSettlementService cashierOrderRefundSettlementService;
    @Autowired
    private IBankCardSearchService iBankCardSearchService;
    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: payBQAbility(PersonFBBQAbilityRPCVo personFBBQAbilityRPCVo)
     */
    @Test
    public void testPayBQAbility() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: createOrderTrade(CashierCreateTradeRPCVo cashierCreateTradeRPCVo)
     */
    @Test
    public void testCreateOrderTrade() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: notifyThirdPartyPayResult(String cashierTxnId, String payTxnId, Integer payStatus)
     */
    @Test
    public void testNotifyThirdPartyPayResult() throws Exception {
        String cashierTxnId = "tn881202010240952430442942";
        String payTxnId = "tn101202010240952535705888";
        Integer payStatus = PayStatus.SUCCESS.getKey();
        cashierOrderSettlementService.notifyThirdPartyPayResult(cashierTxnId,payTxnId,payStatus);
    }

    /**
     * Method: getPayDetail(CashierQueryReqRPCVo cashierQueryReqRPCVo)
     */
    @Test
    public void testGetPayDetail() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: getPayResult()
     */
    @Test
    public void testGetPayResult() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: getPayResultListByStatus(Integer status)
     */
    @Test
    public void testGetPayResultListByStatus() throws Exception {
//TODO: Test goes here... 
    }

    @Test
    public void testCreateAndPayBankTrade() throws Exception {
        CashierCreatePayTradeReqVo createPayTradeReqVo = new CashierCreatePayTradeReqVo();
        createPayTradeReqVo.setCompanyId("5747fbc10f0e60e0709d8d7d");
        createPayTradeReqVo.setEmployeeId("5aab96c051bba9669df9b04c");
        createPayTradeReqVo.setOrderType(126);
        createPayTradeReqVo.setFbOrderId("OBK202006241613340642720");
        createPayTradeReqVo.setFbOrderName("测试虚拟卡");
        createPayTradeReqVo.setFbOrderSnapshot("bank_test card 支付");
        createPayTradeReqVo.setTotalPayPrice(new BigDecimal(20));
        createPayTradeReqVo.setCompanyPayPrice(BigDecimal.ZERO);
        createPayTradeReqVo.setCompanyPayRulePrice(new BigDecimal(20));
        CashierPayCommonJsonDto commonJsonDto = new CashierPayCommonJsonDto();
        createPayTradeReqVo.setCommonJson(commonJsonDto);
        createPayTradeReqVo.setCostAttributionId("bank_test1234567890");
        createPayTradeReqVo.setCostAttributionType(1);
        createPayTradeReqVo.setCostAttributionName("test attribution name");
//        createPayTradeReqVo.setCustomerServiceId("");
//        createPayTradeReqVo.setCustomerServiceName("");
        createPayTradeReqVo.setAccountSubType(2);
        createPayTradeReqVo.setBankAccountNo("6666666666666666666");
        createPayTradeReqVo.setBankTransNo("202501073799995080419546181888884");
        createPayTradeReqVo.setBankName(BankNameEnum.XWBANK.getCode());

        CashierCreatePayTradeRespVo payBankTradeOrSaas = cashierOrderSettlementService.createAndPayBankTradeOrSaas(createPayTradeReqVo, false);
        FinhubLogger.info(JSONObject.toJSONString(payBankTradeOrSaas));
    }

    @Test
    public void testCreateRefundBankTrade() throws Exception {
        CashierRefundTradeReqVo cashierRefundTradeReqVo = new CashierRefundTradeReqVo();
        cashierRefundTradeReqVo.setCompanyId("5747fbc10f0e60e0709d8d7d");
        cashierRefundTradeReqVo.setEmployeeId("5aab96c051bba9669df9b04c");
        cashierRefundTradeReqVo.setFbOrderId("OBK202006241613340642720");
        cashierRefundTradeReqVo.setRefundOrderId("bank_refund1234567890");
        cashierRefundTradeReqVo.setCashierTxnId("tx401201910101644511696356");
        cashierRefundTradeReqVo.setTotalRefundAmount(new BigDecimal(20));
        cashierRefundTradeReqVo.setPersonalRefundAmount(BigDecimal.ZERO);
        cashierRefundTradeReqVo.setCompanyRefundAmount(new BigDecimal(20));
        cashierRefundTradeReqVo.setRefundReason("测试退款");
        cashierRefundTradeReqVo.setRemark("退款");
        cashierRefundTradeReqVo.setOperationChannelType(1);
//        cashierRefundTradeReqVo.setCustomerServiceId("");
//        cashierRefundTradeReqVo.setCustomerServiceName("");
        cashierRefundTradeReqVo.setBankAccountNo("6666666666666666666");
        cashierRefundTradeReqVo.setBankTransNo("202501073799995080419546181888884");
        cashierRefundTradeReqVo.setBankName(BankNameEnum.XWBANK.getCode());
        CashierRefundTradeRespVo refundBankTradeOrSass = cashierOrderRefundSettlementService.createRefundBankTradeOrSass(cashierRefundTradeReqVo, false);
        FinhubLogger.info(JSONObject.toJSONString(refundBankTradeOrSass));
    }

    @Test
    public void testDDD(){
        Map<String, Boolean> deleteEmployeeByBank = iBankCardSearchService.isDeleteEmployeeByBank("5747fbc10f0e60e0709d8d7d", "5a2752f123445f3483c70354");
    }


    @Test
    public void createAndAutoPayOrderTrade(){
        //employee_id = 5d70ba9b23445f04bc031156,company_id = 5d70ba9c23445f67856cfdb4
        //employee_id = 5c40552623445f581c6ef1be,company_id = 5b91427b23445f1bccbaf2f7
        String requestString = "{\n" +
                "    \"accountType\": 2,\n" +
                "    \"amountCompliance\": 0.0,\n" +
                "    \"amountNonCompliance\": 0.0,\n" +
                "    \"bizCallbackUrl\": \"localhost\",\n" +
                "    \"businessMode\": 3,\n" +
                "    \"cashierTradeType\": 1,\n" +
                "    \"commonJson\": {\n" +
                "        \"taxi\": {\n" +
                "            \"arrivalAddr\": \"万达广场(1号门)\",\n" +
                "            \"departureAddr\": \"磨子潭路振华路口-公交站附近\",\n" +
                "            \"message\": \"实时-经济型(滴滴)\",\n" +
                "            \"passagerList\": [\n" +
                "                {\n" +
                "                    \"passagerName\": \"测试\",\n" +
                "                    \"passagerPhone\": \"***********\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    },\n" +
                "    \"companyActPayPrice\": 0.0,\n" +
                "    \"companyId\": \"5b91427b23445f1bccbaf2f7\",\n" +
                "    \"companyNoSettlePrice\": 0,\n" +
                "    \"companyPayPrice\": 0.0,\n" +
                "    \"companyPrePay\": 2,\n" +
                "    \"couponAmount\": 0,\n" +
                "    \"currency\": \"cny\",\n" +
                "    \"deadLineMin\": 52560000,\n" +
                "    \"employeeId\": \"5c40552623445f581c6ef1be\",\n" +
                "    \"fbOrderId\": \"62ce6c18b032c99561d33684\",\n" +
                "    \"fbOrderName\": \"出行用车订单支付\",\n" +
                "    \"fbOrderSnapshot\": \"实时-经济型(滴滴)从磨子潭路振华路口-公交站附近到万达广场(1号门)\",\n" +
                "    \"invoiceProvideStatus\": 1,\n" +
                "    \"invoiceProvideType\": 1,\n" +
                "    \"operationChannelType\": 1,\n" +
                "    \"orderChannelType\": 3142,\n" +
                "    \"orderType\": 3,\n" +
                "    \"personalPayPrice\": 3142.0,\n" +
                "    \"sceneInvoiceType\": 27,\n" +
                "    \"totalPayPrice\": 3142.0,\n" +
                "    \"usePersonalBudget\": 0,\n" +
                "    \"wrongPaidFlag\": false\n" +
                "}";
        CashierCreateAutoPayTradePrivateReqVO cashierCreateAutoPayTradePrivateReqVO = JSONObject.parseObject(requestString,CashierCreateAutoPayTradePrivateReqVO.class);
        cashierOrderSettlementService.createAndAutoPayOrderTrade4Private(cashierCreateAutoPayTradePrivateReqVO);
    }

} 
