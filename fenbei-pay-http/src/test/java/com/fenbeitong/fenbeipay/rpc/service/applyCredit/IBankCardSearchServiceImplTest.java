package com.fenbeitong.fenbeipay.rpc.service.applyCredit;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.ApplyCreditDeductionReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankSearchApplyCreditReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankCardCreditApplyResqDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardCreditApplyService;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardCreditApplyMapper;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCreditApply;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.lang.reflect.Executable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/28
 */
public class IBankCardSearchServiceImplTest extends SpringBaseHttpTest {

    @Resource
    private IBankCardCreditApplyService iBankCardCreditApplyService;

    @Autowired
    private BankCardCreditApplyMapper bankCardCreditApplyMapper;

    @Test
    public void listVirtualCardInfoByBankCard() {
        BankSearchApplyCreditReqDTO dto = BankSearchApplyCreditReqDTO.builder()
                .companyId("5747fbc10f0e60e0709d8d7d")
                .employeeId("5a2752f123445f3483c70354")
                .cardModel(1)
                .isNew(1)
                .bankAccountNos(Lists.newArrayList("6236006620000005333", "6236006620000000326"))
                .pageSize(10)
                .pageNo(1).build();
        List<BankCardCreditApplyResqDTO> list = iBankCardCreditApplyService.getBankCardCreditApplyByParam(dto);
        System.out.println(JSONObject.toJSONString(list));
    }

    @Test
    public void testGetApplyList() throws Exception {
        long count = iBankCardCreditApplyService.getMaxCreditApplyCountsByUser("5747fbc10f0e60e0709d8d7d", "5c11cc7023445f5b632c9637", "**********");
        System.out.println("==================count="+count);
    }

    @Test
    public void deductionBalanceForCreditApply()throws Exception{
        List<ApplyCreditDeductionReqDTO> applyCreditList=Lists.newArrayList();
        //'AB123456712', 'AB123456713', 'AB123456713'
        ApplyCreditDeductionReqDTO reqDTO=new  ApplyCreditDeductionReqDTO();
        reqDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
        reqDTO.setEmployeeId("5e1ed56e23445f5b4018ef9e");
        reqDTO.setApplyTransNo("APA202112142123458702669");
        reqDTO.setOperationAmount(BigDecimal.valueOf(0.01));
        reqDTO.setApplyAmount(BigDecimal.valueOf(11));
        reqDTO.setUseBalance(BigDecimal.valueOf(11));
        reqDTO.setCostId("61a875874156836abe386341");
        applyCreditList.add(reqDTO);

        ApplyCreditDeductionReqDTO reqDTO2=new  ApplyCreditDeductionReqDTO();
        reqDTO2.setCompanyId("5d1b1d2f23445f4dca76304b");
        reqDTO2.setEmployeeId("5e1ed56e23445f5b4018ef9e");
        reqDTO2.setApplyTransNo("APA202112171443027798369");
        reqDTO2.setOperationAmount(BigDecimal.valueOf(1));
        reqDTO2.setApplyAmount(BigDecimal.valueOf(0.01));
        reqDTO2.setUseBalance(BigDecimal.valueOf(200));
        reqDTO2.setCostId("61a875874156836abe386341");
        applyCreditList.add(reqDTO2);
        iBankCardCreditApplyService.deductionBalanceForCreditApply( applyCreditList );
    }

    @Test
    public void epositBalanceForCreditApply()throws Exception{
        iBankCardCreditApplyService.epositBalanceForCreditApply("61a875874156836abe386341");
    }
    @Test
    public void testAddCreditApply()throws Exception{
        BankSearchApplyCreditReqDTO dto = BankSearchApplyCreditReqDTO.builder()
                .companyId("5747fbc10f0e60e0709d8d7d")
                .employeeId("5a2752f123445f3483c70354")
                .cardModel(1)
                .isNew(1)
                .bankAccountNos(Lists.newArrayList("6236006620000005333", "6236006620000000326"))
                .pageSize(10)
                .pageNo(1).build();
        ResponsePage<BankCardCreditApplyResqDTO> list= iBankCardCreditApplyService.getBankCardCreditApplyForPage(dto);
        System.out.println(JSONObject.toJSON(list));
    }

}
