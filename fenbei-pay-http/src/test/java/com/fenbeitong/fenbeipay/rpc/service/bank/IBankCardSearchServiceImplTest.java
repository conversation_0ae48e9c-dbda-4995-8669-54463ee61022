package com.fenbeitong.fenbeipay.rpc.service.bank;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardUnBindPettyIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankCardStatisticalResqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.ExportBankCardFlowResqDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankAccountService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/28
 */
public class IBankCardSearchServiceImplTest extends SpringBaseHttpTest {

    @Resource
    private IBankCardSearchService bankCardSearchService;
    @Resource
    private IBankAccountService bankAccountService;

    @Resource
    private IBankCardSearchService service;

    @Test
    public void listVirtualCardInfoByBankCard() {
        List<BankCardDTO> virtualCardList = bankCardSearchService.selectBankCardByCompanyIdAndEmployId("5d1b1d2f23445f4dca76304b",
                "5fb33cdb27f65f61453f2a4b");
        System.out.println(JSONObject.toJSONString(virtualCardList));
    }

    @Test
    public void test() {
        BankCardUnBindPettyIdReqDTO req = new BankCardUnBindPettyIdReqDTO();
        req.setBankName("CGB");
        req.setEmployeeId("5f83ede327f65f17d8c5817a");
        req.setCompanyId("5b9b767c23445f5a69d5f665");
        req.setBizNo("*********");
        bankAccountService.unBindBankCardPettyId(req);
    }

    @Test
    public void list() {
        BankCardSearchReqDTO dto = new BankCardSearchReqDTO();
        dto.setCompanyId("5d1b1d2f23445f4dca76304b");
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setIsBalance(true);
        List<ExportBankCardFlowResqDTO> list = service.exportWebBankCardFlowPage(dto);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void lis2() {
        BankCardSearchReqDTO dto = new BankCardSearchReqDTO();
        dto.setCompanyId("5d1b1d2f23445f4dca76304b");
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setIsBalance(true);
        ResponsePage<BankCardStatisticalResqDTO> data = service.exportStatisticalBankCard(dto);
        System.out.println(JSON.toJSONString(data.getDataList()));
    }

    @Test
    public void exportWebBankCardFlowDataAuth(){
        /**
         * {
         *     "bankAccountNo": "",
         *     "bankName": "",
         *     "companyId": "632a83c053feb0470cc4ad2d",
         *     "employeeName": "",
         *     "employeePhone": "",
         *     "endDate": "2023-09-13",
         *     "isBalance": true,
         *     "maxPageSize": 500,
         *     "offset": 0,
         *     "orgUnitId": "",
         *     "pageNo": 1,
         *     "pageSize": 200,
         *     "stairOrgUnitId": "",
         *     "startDate": "2023-08-03"
         * }
         */
        BankCardSearchReqDTO bankCardSearchReqDTO  = new BankCardSearchReqDTO();
        bankCardSearchReqDTO.setCompanyId("632a83c053feb0470cc4ad2d");
        bankCardSearchReqDTO.setStartDate("2023-08-03");
        bankCardSearchReqDTO.setEndDate("2023-09-13");
        bankCardSearchReqDTO.setIsBalance(true);
        bankCardSearchReqDTO.setMaxPageSize(500);
        bankCardSearchReqDTO.setOffset(0);
        bankCardSearchReqDTO.setPageNo(1);
        bankCardSearchReqDTO.setPageSize(200);
        Boolean departFlag = false;
        List<String> partDataIdList = Lists.newArrayList();
        service.exportWebBankCardFlowDataAuth(bankCardSearchReqDTO,departFlag,partDataIdList);
    }

    @Test
    public void testForUpgrade(){
        bankCardSearchService.select4ZBBankUpgrade("5caeadd7bfd57c20f3a05f56","607ec319aa42271b723208ce");
    }
}
