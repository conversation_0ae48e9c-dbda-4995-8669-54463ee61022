package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import static org.junit.Assert.assertNotNull;

import java.util.Arrays;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.fenbeitong.fenbeipay.api.constant.enums.group.CompanyStateChangeEnum;
import com.fenbeitong.fenbeipay.api.model.vo.group.GroupCompanyValidationReqVO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.AccountCompanyService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2022-08-02 09:28:49 
*/

public class AccountCompanyServiceTest extends SpringBaseHttpTest {

	@Autowired
	private AccountCompanyService accountCompanyService;
	
	@Test
	public void test() {
		accountCompanyService.checkIfCompanyShouldJoinGroup(
				Arrays.asList(GroupCompanyValidationReqVO.builder()
						.companyId("5747fbc10f0e60e0709d8d7d")
						.changeType(CompanyStateChangeEnum.NORMAL_TO_SUBSIDIARY)
						.build()));
	}
	
	@Test
	public void testCheckIfCompanyHasAccount() {
		Boolean result = accountCompanyService.checkIfCompanyHasAccount("5747fbc10f0e60e0709d8d7d");
		assertNotNull(result);
	}
}
