package com.fenbeitong.fenbeipay.rpc.service.trade;

import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctConsumeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctRefundReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubOperationReqRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctAppPayService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.constant.*;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

public class IAcctAppPayServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private IAcctAppPayService iAcctAppPayService;

    @Test
    public void consumeTest() {


        AcctConsumeReqDTO acctConsumeReqDTO = new AcctConsumeReqDTO();
        acctConsumeReqDTO.setCompanyId("605aa96a27f65f27af41f314");
        acctConsumeReqDTO.setOperationAmount(new BigDecimal(1));
        acctConsumeReqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
        acctConsumeReqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        acctConsumeReqDTO.setAccountId("ASA202103241052264740466");
        acctConsumeReqDTO.setCashierTxnId("1234564");
        acctConsumeReqDTO.setBizNo("888884");
        acctConsumeReqDTO.setOperationUserId("*********");
        acctConsumeReqDTO.setOperationUserName("小六");


        iAcctAppPayService.consume(acctConsumeReqDTO);
        try {
            Thread.sleep(20000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println("Down");
    }


    @Test
    public void refundTest() {

        AcctRefundReqDTO refundReqDTO = new AcctRefundReqDTO();
        refundReqDTO.setBizNo("11111");
        refundReqDTO.setOperationType(FundAcctDebitOptType.PUBLIC_CONSUME);
        int key = FundAccountSubType.BUSINESS_ACCOUNT.getKey();
        int key1 = FundAccountModelType.RECHARGE.getKey();
        refundReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        refundReqDTO.setOperationAmount(new BigDecimal(1));
        refundReqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        refundReqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
        refundReqDTO.setAccountId("AGA201701221219438781212");
        refundReqDTO.setBankName(BankNameEnum.FBT.getCode());
        refundReqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");
        iAcctAppPayService.refund(refundReqDTO);
        System.out.println("Down");
    }
}
