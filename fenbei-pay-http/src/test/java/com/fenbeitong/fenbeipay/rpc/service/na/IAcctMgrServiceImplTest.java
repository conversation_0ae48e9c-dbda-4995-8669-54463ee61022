package com.fenbeitong.fenbeipay.rpc.service.na;


import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctAuthCompanyMainTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctAuthModelTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctAuthSubTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctAuthBankEnumRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctAuthCompanyMainTypeRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctAuthModelTypeRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctAuthSubTypeRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessDebit;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class IAcctMgrServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private IAcctMgrService  iAcctMgrService;
    @Test
    public void queryAccountSubInfoList() {
        List<AcctBusinessDebit> acctBusinessDebits = new ArrayList<>();
        AcctBusinessDebit acctBusinessDebit = new AcctBusinessDebit();
        acctBusinessDebit.setBalance(new BigDecimal(1));
        AcctBusinessDebit acctBusinessDebit1 = new AcctBusinessDebit();
        acctBusinessDebit1.setBalance(new BigDecimal(2));
        acctBusinessDebits.add(acctBusinessDebit);
        acctBusinessDebits.add(acctBusinessDebit1);
        BigDecimal reduce = acctBusinessDebits.stream().map(AcctBusinessDebit::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        System.out.println(reduce.toString());
        System.out.println("Down....");
    }


    /**
     * 账户权限/获取平台方
     *
     * @return
     */
    @Test
    public void  findAcctAuthPlatformCode(){

        List<AcctAuthBankEnumRespDTO> acctAuthPlatformCode = iAcctMgrService.findAcctAuthPlatformCode();
        System.out.println("Down....");
    }

    @Test
    public void  findAcctAuthModelType(){
        AcctAuthModelTypeReqDTO acctAuthModelTypeReqDTO = new AcctAuthModelTypeReqDTO();
        acctAuthModelTypeReqDTO.setPlateformKey(1);
        List<AcctAuthModelTypeRespDTO> acctAuthModelType = iAcctMgrService.findAcctAuthModelType(acctAuthModelTypeReqDTO);
        System.out.println("Down....");
    }

    /**
     * 账户权限/获取开户主体
     *
     * @param   --- 账户类型  充值账户 授信账户
     *                                         plateformCode --- 平台     FBT  众邦银行
     * @return
     */
    @Test
    public void findAcctAuthCompanyMainType(){
        AcctAuthCompanyMainTypeReqDTO acctAuthCompanyMainTypeReqDTO = new AcctAuthCompanyMainTypeReqDTO();
        acctAuthCompanyMainTypeReqDTO.setPlateformKey(1);
        acctAuthCompanyMainTypeReqDTO.setFundAcctModel(2);
        List<AcctAuthCompanyMainTypeRespDTO> acctAuthCompanyMainType = iAcctMgrService.findAcctAuthCompanyMainType(acctAuthCompanyMainTypeReqDTO);

        System.out.println("Down....");
    }

    /**
     * 账户权限/获取业务账户列表
     *
     * @param    --- 平台     FBT  众邦银行
     *                                 fundAcctModel --- 账户类型  充值账户 授信账户
     *                                 companyMainType --- 公司主体类型  本主体  其它主体
     * @return
     */
    @Test
    public void findAcctAuthSubTypeTest(){
        AcctAuthSubTypeReqDTO acctAuthSubTypeReqDTO = new AcctAuthSubTypeReqDTO();
        acctAuthSubTypeReqDTO.setCompanyMainType(1);
        acctAuthSubTypeReqDTO.setPlatformKey(1);
        acctAuthSubTypeReqDTO.setFundAcctModel(2);
        List<AcctAuthSubTypeRespDTO> acctAuthSubType = iAcctMgrService.findAcctAuthSubType(acctAuthSubTypeReqDTO);

        System.out.println("Down....");
    }

}
