package com.fenbeitong.fenbeipay.rpc.service.na;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountAlterModelRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountGeneralRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFindRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubOperationRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountComponentService;
import com.fenbeitong.fenbeipay.api.service.na.IAccountGeneralService;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountSub;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.fenbeipay.na.service.AccountSubService;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.RandomUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.fenbeitong.finhub.common.constant.FundAcctGeneralOptType.WITHDRAWAL;

public class IAccountGeneralServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private IAccountGeneralService iAccountGeneralService;
    @Autowired
    private IAccountSubService iAccountSubService;

    @Autowired
    private AccountGeneralService accountGeneralService;

    @Autowired
    private AccountSubService accountSubService;

    @Autowired
    private IAccountComponentService componentService;

    @Test
    public void showAccountSub(){
        AccountSubShowReqRPCDTO reqRPCDTO = new AccountSubShowReqRPCDTO();
        reqRPCDTO.setAccountSubType(2);
        reqRPCDTO.setCompanyId("5d834a8f23445f44790711ff");
        reqRPCDTO.setAccountModel(1);
        reqRPCDTO.setShowStatus(0);
        reqRPCDTO.setBankAccountNo("5d834a8f23445f44790711ff");
        reqRPCDTO.setBankName("FBT");
        iAccountSubService.showAccountSub(reqRPCDTO);
    }

    @Test
    public void queryAccountGeneralInfo() {
        AccountGeneralRespRPCDTO accountGeneralRespRPCDTO = iAccountGeneralService.queryAccountGeneralInfo("57ada5c92528226a805bd7cb");
        System.out.println("test测试获取公司账户信息:");
        System.out.println(accountGeneralRespRPCDTO.toString());
    }

    @Test
    public void queryCompanyAccountInfo() {
        List<String> companyIds = new ArrayList<>();
        companyIds.add("6062c7af27f65fe4a10160c0");
        List<String> strings = JSON.parseArray("[\"6078ff4527f65fcd3ea2adbe\"]", String.class);
        List<AccountGeneralRespRPCDTO> accountGeneralRespRPCDTOS = iAccountGeneralService.queryCompanyAccountInfo(strings);
        accountGeneralRespRPCDTOS.forEach(accountGeneralRespRPCDTO -> {
            System.out.println("test测试获取公司账户信息:");
            System.out.println(accountGeneralRespRPCDTO.toString());
        });
        System.out.println("Down");
    }

    @Test
    public void accountRecharge() {
        String Json = "{\"accountSubType\":5,\"bizNo\":\"G3881300154215C\",\"companyId\":\"6062c7af27f65fe4a10160c0\",\"operationAmount\":555500,\"operationChannelType\":2,\"operationDescription\":\"\",\"operationUserId\":\"Stereo\",\"operationUserName\":\"Stereo\"}";
        AccountGeneralOperationRPCDTO accountGeneralOperationRPCDTO = JSON.parseObject(Json, AccountGeneralOperationRPCDTO.class);
        iAccountGeneralService.accountRecharge(accountGeneralOperationRPCDTO);

        System.out.println("Down");
    }

    @Test
//    @Ignore
    public void transferOut() {
//        5c24a21c05e5eb04be2fbe72
//        5c90593693c6d64a902d1cc3
//        5b5ec0ea3ce30d0f65447f30
//        5b8789cf5d88db7670cb79cf

//        20000
//        50000
//        50000
//        100000
        AccountGeneralTransferRPCDTO accountTransferDTO = new AccountGeneralTransferRPCDTO();
        accountTransferDTO.setAccountModelSub(2);
        accountTransferDTO.setAccountSubType(2);
        accountTransferDTO.setBizNo("605483c4473dfce91f4c04f8");
        accountTransferDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        accountTransferDTO.setOperationAmount(new BigDecimal(100));
        accountTransferDTO.setOperationDescription("1");
        accountTransferDTO.setOperationUserId("601a6c4827f65f069cc8f9d3");
        accountTransferDTO.setOperationUserName("学武");
        iAccountGeneralService.transferOut(accountTransferDTO);


//        AccountGeneralTransferRPCDTO accountTransferDTO1 = new AccountGeneralTransferRPCDTO();
//        accountTransferDTO1.setCompanyId("5c77abcb93c6d67f43dc23c8");
//        accountTransferDTO1.setOperationAmount(BigDecimalUtils.yuan2fen(new BigDecimal(20000)));
//        accountTransferDTO1.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//        accountTransferDTO1.setOperationDescription("出纳(董亚楠)操作总账户转账至商务账户20000元RMB");
//        accountTransferDTO1.setOperationUserId("5a559865d6e12b08f9eb0ad8");
//        accountTransferDTO1.setOperationUserName("董亚楠");
//        accountTransferDTO1.setOperationChannelType(OperationChannelType.STEREO.getKey());
//        accountTransferDTO1.setCustomerServiceId("5a559865d6e12b08f9eb0ad8");
//        accountTransferDTO1.setCustomerServiceName("董亚楠");
//        iAccountGeneralService.transferOut(accountTransferDTO1);
//
//
//        AccountGeneralTransferRPCDTO accountTransferDTO2 = new AccountGeneralTransferRPCDTO();
//        accountTransferDTO2.setCompanyId("5b5ec0ea3ce30d0f65447f30");
//        accountTransferDTO2.setOperationAmount(BigDecimalUtils.yuan2fen(new BigDecimal(50000)));
//        accountTransferDTO2.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//        accountTransferDTO2.setOperationDescription("出纳(董亚楠)操作总账户转账至商务账户50000元RMB");
//        accountTransferDTO2.setOperationUserId("5a559865d6e12b08f9eb0ad8");
//        accountTransferDTO2.setOperationUserName("董亚楠");
//        accountTransferDTO.setOperationChannelType(OperationChannelType.STEREO.getKey());
//        accountTransferDTO.setCustomerServiceId("5a559865d6e12b08f9eb0ad8");
//        accountTransferDTO.setCustomerServiceName("董亚楠");
//        iAccountGeneralService.transferOut(accountTransferDTO2);

//
//        AccountGeneralTransferRPCDTO accountTransferDTO3 = new AccountGeneralTransferRPCDTO();
//        accountTransferDTO3.setCompanyId("5b8789cf5d88db7670cb79cf");
//        accountTransferDTO3.setOperationAmount(BigDecimalUtils.yuan2fen(new BigDecimal(100000)));
//        accountTransferDTO3.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//        accountTransferDTO3.setOperationDescription("出纳(董亚楠)操作总账户转账至商务账户100000元RMB");
//        accountTransferDTO3.setOperationUserId("5a559865d6e12b08f9eb0ad8");
//        accountTransferDTO3.setOperationUserName("董亚楠");
//        accountTransferDTO.setOperationChannelType(OperationChannelType.STEREO.getKey());
//        accountTransferDTO.setCustomerServiceId("5a559865d6e12b08f9eb0ad8");
//        accountTransferDTO.setCustomerServiceName("董亚楠");
//        iAccountGeneralService.transferOut(accountTransferDTO3);
//
//
//        AccountGeneralTransferRPCDTO accountTransferDTO4 = new AccountGeneralTransferRPCDTO();
//        accountTransferDTO4.setCompanyId("5add40e95d88db79393e0938");
//        accountTransferDTO4.setOperationAmount(BigDecimalUtils.yuan2fen(new BigDecimal(50000)));
//        accountTransferDTO4.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//        accountTransferDTO4.setOperationDescription("出纳(董亚楠)操作总账户转账至商务账户50000元RMB");
//        accountTransferDTO4.setOperationUserId("5a559865d6e12b08f9eb0ad8");
//        accountTransferDTO4.setOperationUserName("董亚楠");
//        iAccountGeneralService.transferOut(accountTransferDTO4);
//
//
//        AccountGeneralTransferRPCDTO accountTransferDTO5 = new AccountGeneralTransferRPCDTO();
//        accountTransferDTO5.setCompanyId("5b73e4275f281a73b04936b7");
//        accountTransferDTO5.setOperationAmount(BigDecimalUtils.yuan2fen(new BigDecimal(30000)));
//        accountTransferDTO5.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//        accountTransferDTO5.setOperationDescription("出纳(董亚楠)操作总账户转账至商务账户30000元RMB");
//        accountTransferDTO5.setOperationUserId("5a559865d6e12b08f9eb0ad8");
//        accountTransferDTO5.setOperationUserName("董亚楠");
//        iAccountGeneralService.transferOut(accountTransferDTO5);



//        AccountGeneralTransferRPCDTO accountTransferDTO = new AccountGeneralTransferRPCDTO();
//        accountTransferDTO.setCompanyId("5c107d8523445f330630d1d4");
//        accountTransferDTO.setOperationAmount(new BigDecimal(10000));
//        accountTransferDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//        accountTransferDTO.setOperationDescription("转账至商务账户10000分RMB");
//        accountTransferDTO.setOperationUserId("5c107d8523445f330630d1d5");
//        accountTransferDTO.setOperationUserName("renfj");
//        iAccountGeneralService.transferOut(accountTransferDTO);
//

//        AccountGeneralTransferRPCDTO accountTransferDTO2 = new AccountGeneralTransferRPCDTO();
//        accountTransferDTO2.setCompanyId("5c107d8523445f330630d1d4");
//        accountTransferDTO2.setOperationAmount(new BigDecimal(10000));
//        accountTransferDTO2.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
//        accountTransferDTO2.setOperationDescription("转账至个人账户10000分RMB");
//        accountTransferDTO2.setOperationUserId("5c107d8523445f330630d1d5");
//        accountTransferDTO2.setOperationUserName("renfj");
//        iAccountGeneralService.transferOut(accountTransferDTO2);
//
//        AccountGeneralTransferRPCDTO accountTransferDTO3 = new AccountGeneralTransferRPCDTO();
//        accountTransferDTO3.setCompanyId("5c107d8523445f330630d1d4");
//        accountTransferDTO3.setOperationAmount(new BigDecimal(10000));
//        accountTransferDTO3.setAccountSubType(FundAccountSubType.ENTERPRISE_ACCOUNT.getKey());
//        accountTransferDTO3.setOperationDescription("转账至企业账户10000分RMB");
//        accountTransferDTO3.setOperationUserId("5c107d8523445f330630d1d5");
//        accountTransferDTO3.setOperationUserName("renfj");
//        iAccountGeneralService.transferOut(accountTransferDTO3);
    }

    @Test
    public void cashWithdrawal() {
        AccountGeneralOperationRPCDTO cashWithdrawalCDTO = new AccountGeneralOperationRPCDTO();
        cashWithdrawalCDTO.setCompanyId("5c107d8523445f330630d1d4");
        cashWithdrawalCDTO.setOperationAmount(new BigDecimal(10000));
        cashWithdrawalCDTO.setBizNo("提现订单号**********");
        cashWithdrawalCDTO.setOperationDescription("提现冻结10000分RMB");
        cashWithdrawalCDTO.setOperationUserId("5c107d8523445f330630d1d5");
        cashWithdrawalCDTO.setOperationUserName("renfj");
//        iAccountGeneralService.cashWithdrawal(cashWithdrawalCDTO);
    }

    @Test
    public void confirmationCashWithdrawal() {
        AccountGeneralOperationRPCDTO cashWithdrawalCDTO = new AccountGeneralOperationRPCDTO();
        cashWithdrawalCDTO.setCompanyId("5c107d8523445f330630d1d4");
        cashWithdrawalCDTO.setOperationAmount(new BigDecimal(10000));
        cashWithdrawalCDTO.setBizNo("提现订单号**********");
        cashWithdrawalCDTO.setOperationDescription("确认提现10000分RMB");
        cashWithdrawalCDTO.setOperationUserId("5c107d8523445f330630d1d5");
        cashWithdrawalCDTO.setOperationUserName("renfj");
//        iAccountGeneralService.confirmationCashWithdrawal(cashWithdrawalCDTO);
    }

    /**
     * @Description: 授信模式调额
     * @methodName: adjust
     * @Param: []
     * @return: void
     * @Author: zhangga
     * @Date: 2019/4/2 3:51 PM
     **/
    @Test
    public void adjust() {
        AccountSubOperationReqRPCDTO accountAdjustDTO = new AccountSubOperationReqRPCDTO();
        accountAdjustDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        accountAdjustDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        accountAdjustDTO.setOperationAmount(new BigDecimal(*********));
        accountAdjustDTO.setBizNo("stereoTE**********");
        accountAdjustDTO.setOperationDescription("授信模式调额");
        accountAdjustDTO.setOperationUserId("5c107d8523445f330630d1d5");
        accountAdjustDTO.setOperationUserName("ceshi");
        accountAdjustDTO.setCreditAmount(new BigDecimal(*********));
        AccountSubOperationRespRPCDTO adjust = iAccountSubService.adjust(accountAdjustDTO);
        System.out.println(adjust);
    }

    /**
     * @Description: 还款，只有授信模式才能还款
     * @methodName: repayment
     * @Param: []
     * @return: void
     * @Author: zhangga
     * @Date: 2019/4/2 3:55 PM
     **/
    @Test
    public void repayment() {
        AccountSubRepaymentReqRPCDTO accountSubRepaymentDTO = new AccountSubRepaymentReqRPCDTO();
        accountSubRepaymentDTO.setCompanyId("5f8e8fd927f65f8a6a9be0de");
        accountSubRepaymentDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
        accountSubRepaymentDTO.setRepaymentAmount(new BigDecimal(400));
        accountSubRepaymentDTO.setServiceFee(new BigDecimal(161));
        accountSubRepaymentDTO.setBizNo("*****************");
        accountSubRepaymentDTO.setOperationDescription("个人账户授信模式开通前使用的授信模式还款");
        accountSubRepaymentDTO.setOperationUserId("207303");
        accountSubRepaymentDTO.setOperationUserName("liuwei");
        AccountSubOperationRespRPCDTO repayment = iAccountSubService.repayment(accountSubRepaymentDTO);
    }

    /**
     * @Description: 充值模式，子账户转账到主账户
     * @methodName: subToGeneral
     * @Param: []
     * @return: void
     * @Author: zhangga
     * @Date: 2019/4/2 3:59 PM
     **/
    @Test
    public void subToGeneral() {
        AccountSubOperationReqRPCDTO accountSubToGeneralDTO = new AccountSubOperationReqRPCDTO();
//        accountSubToGeneralDTO.setAccountModel(2);
//        accountSubToGeneralDTO.setAccountSubType(2);
//        accountSubToGeneralDTO.setBizNo("60580ab9473dfcca6ae79d50");
//        accountSubToGeneralDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
//        accountSubToGeneralDTO.setCompanyName("北京分贝金服科技有限公司");
//        accountSubToGeneralDTO.setOperationAmount(new BigDecimal(6));
//        accountSubToGeneralDTO.setOperationDescription("test商务充值到总账户");
//        accountSubToGeneralDTO.setOperationUserId("601a6c4827f65f069cc8f9d3");
//        accountSubToGeneralDTO.setOperationUserName("学武");

        accountSubToGeneralDTO.setAccountModel(2);
        accountSubToGeneralDTO.setAccountSubType(3);
        accountSubToGeneralDTO.setBizNo("60581181473dfcca6ae79e60");
        accountSubToGeneralDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        accountSubToGeneralDTO.setCompanyName("北京分贝金服科技有限公司");
        accountSubToGeneralDTO.setOperationAmount(new BigDecimal(6));
        accountSubToGeneralDTO.setOperationDescription("test个人充值到总账户");
        accountSubToGeneralDTO.setOperationUserId("601a6c4827f65f069cc8f9d3");
        accountSubToGeneralDTO.setOperationUserName("学武");
        AccountSubOperationRespRPCDTO accountSubOperationRespRPCDTO = iAccountSubService.subToGeneral(accountSubToGeneralDTO);
    }

    @Test
    public void testChangeCompanyName() {
        AccountChangeNameReqDTO changeNameReqDTO = new AccountChangeNameReqDTO();
        changeNameReqDTO.setCompanyId("5ec61eec23445f1b43662fb5");
        changeNameReqDTO.setCompanyName("对公支付测试公司1");
        boolean result = iAccountGeneralService.changeCompanyName(changeNameReqDTO);
        Assert.assertTrue(result);
    }

    @Test
    public void consume() {
        AccountSubOperationReqRPCDTO accountConsumeDTO = new AccountSubOperationReqRPCDTO();
    }

    @Test
    public void refund() {
        AccountSubOperationReqRPCDTO accountRefundDTO = new AccountSubOperationReqRPCDTO();
    }

    @Test
    public void frozen() {
        AccountSubFreezenReqRPCDTO accountFrozenDTO = new AccountSubFreezenReqRPCDTO();
    }

    @Test
    public void unfreeze() {
        AccountSubUnfreezeReqRPCDTO accountUnfreezeDTO = new AccountSubUnfreezeReqRPCDTO();
    }

    @Test
    public void consumeFreezen() {
        AccountConsumeFreezeReqRPCDTO consumeFreezeDTO = new AccountConsumeFreezeReqRPCDTO();
    }

    @Test
    public void refundFreezen() {
        AccountRefundFreezeReqRPCDTO refundFreezeDTO = new AccountRefundFreezeReqRPCDTO();
    }

    @Test
    public void getCreditOverView() {
        String companyId = "";
    }

    @Test
    public void findByInitCredit() {
        BigDecimal initCredit = null;
    }

    @Test
    public void queryTotalConsumeAmount() {
        String companyId = "";
        String employeeId = "";
    }

    @Test
    public void querySumAccountSubFlow() {
        Date currentDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(currentDate);
        c.add(Calendar.YEAR, -1);
        Date startTime = c.getTime();
        Date endTime = currentDate;


        List<Integer> operationTypes = new ArrayList<>();
        operationTypes.add(21);
        operationTypes.add(22);
        operationTypes.add(61);
        AccountSubFlowFindReqRPCDTO  flowFindReqRPCDTO = new AccountSubFlowFindReqRPCDTO();
        flowFindReqRPCDTO.setEndTime(endTime);
        flowFindReqRPCDTO.setAccountSubType(5);
        flowFindReqRPCDTO.setStartTime(startTime);
        flowFindReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        flowFindReqRPCDTO.setOperationTypes(operationTypes);
        BigDecimal bigDecimal = iAccountSubService.querySumAccountSubFlow(flowFindReqRPCDTO);
        System.err.println(bigDecimal);

//        BigDecimal bigDecimal1 = iAccountSubService.queryTotalConsumeAmount("5747fbc10f0e60e0709d8d7d", "59ccbf2123445f570b8c83fe");
    }

    @Test
    @Ignore
    public void createAccountId() {

        List<String> ids = accountGeneralService.findIds();
        int idsCount = ids.size();
        int page = idsCount / 1000;
        for (int i = 0; i <= page; i++) {
            List<String> newIds = new ArrayList<>();
            if (i < page) {
                newIds = ids.subList(i * 1000, (i + 1) * 1000);
            } else {
                newIds = ids.subList(i * 1000, idsCount);
            }
            List<AccountGeneral> generals = accountGeneralService.findByCompanyIds(newIds);
            for (AccountGeneral ag : generals) {
                StringBuilder sb = new StringBuilder("AGA");
                String tm = DateUtils.format(ag.getCreateTime(), "yyyyMMddHHmmssSSS");
                sb.append(tm);
                String randomNum = RandomUtils.randomNum(2);
                sb.append(randomNum);
                String userIdHash = String.valueOf(ag.getCompanyId().hashCode());
                String limit = userIdHash.substring(userIdHash.length() - 2);
                sb.append(limit);
                ag.setAccountGeneralId(sb.toString());
                accountGeneralService.updateAccountId(ag);
            }
        }
    }

        @Test
        @Ignore
        public void createSubAccountId() {

            List<String> ids = accountSubService.findIds();
            int idsCount = ids.size();
            int page = idsCount / 1000;
            for (int i = 0; i <= page; i++) {
                List<String> newIds = new ArrayList<>();
                if (i < page) {
                    newIds = ids.subList(i * 1000, (i + 1) * 1000);
                } else {
                    newIds = ids.subList(i * 1000, idsCount);
                }
                List<AccountSub> subs = accountSubService.findByCompanyIdsAndSubType(newIds, FundAccountSubType.ENTERPRISE_ACCOUNT.getKey());
                for (AccountSub ag : subs) {
                    StringBuilder sb = new StringBuilder("ASA");
                    String tm = DateUtils.format(ag.getCreateTime(), "yyyyMMddHHmmssSSS");
                    sb.append(tm);
                    String randomNum = RandomUtils.randomNum(2);
                    sb.append(randomNum);
                    String userIdHash = String.valueOf(ag.getCompanyId().hashCode());
                    String limit = userIdHash.substring(userIdHash.length() - 2);
                    sb.append(limit);
                    ag.setAccountSubId(sb.toString());
                    accountSubService.updateAccountId(ag);
                }

            }
        }

        @Test
        public void charge4Bill(){

            AccountGeneralOperationRPCDTO accountRechargeDTO = new AccountGeneralOperationRPCDTO();
            accountRechargeDTO.setOperationAmount(new BigDecimal(1));
            accountRechargeDTO.setBizNo("111");
            accountRechargeDTO.setCompanyId("222");
            accountRechargeDTO.setOperationUserId("222");
            accountRechargeDTO.setOperationUserName("222");
            accountRechargeDTO.setOperationDescription("老账单多还金额，充值到总账户中");
            iAccountGeneralService.accountRecharge(accountRechargeDTO);

        }

    @Test
    public void defaultTransferAccountTest() {
        String Json = "{\"accountSubType\":1,\"bizNo\":\"*********\",\"companyId\":\"5dbac36823445f3d2369c897\",\"customerServiceId\":\"Stereo\",\"customerServiceName\":\"Stereo\",\"operationChannelType\":2,\"operationDescription\":\"设置默认收款账户\",\"operationUserId\":\"Stereo\",\"operationUserName\":\"Stereo\"}";
        AccountDefaultTransferRPCDTO accountDefaultTransferRPCDTO = JSON.parseObject(Json, AccountDefaultTransferRPCDTO.class);
        boolean b = iAccountGeneralService.defaultTransferAccount(accountDefaultTransferRPCDTO);

        System.out.println("Down");
    }

    @Test
    public void execute() {
        AccountExecuteReqDTO req = new AccountExecuteReqDTO();
        req.setAccountId("APA202210131811251134444");
        req.setAccountType(1);
        req.setAccountModel(1);
        req.setOperationChannelType(2);
        req.setBankCode("LFBANK");
        req.setTargetBankAccountName("北京分贝国际旅行社有限公司");
        req.setTargetAccount("****************");
        req.setTargetBankName("招商银行");
        req.setRequestNo("test111113");
        req.setOperationAmount(new BigDecimal(1));
        req.setOperationType(WITHDRAWAL.getKey());
        componentService.execute(req);
    }

    @Test
    public void rollBack() {
        AccountRollBackReqDTO req = new AccountRollBackReqDTO();
        req.setAccountId("APA202210131811251134444");
        req.setAccountType(1);
        req.setAccountModel(1);
        req.setRequestNo("test111114");
        req.setOriRequestNo("test111113");
        componentService.rollBack(req);
    }
}
