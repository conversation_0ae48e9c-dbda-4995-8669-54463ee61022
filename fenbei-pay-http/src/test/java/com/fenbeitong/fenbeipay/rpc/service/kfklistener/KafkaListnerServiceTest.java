package com.fenbeitong.fenbeipay.rpc.service.kfklistener;

import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierMultipleTradeListRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierMultipleQueryReqRPCVo;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.rpc.service.base.IBaseAcctKafkaServiceImpl;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;

public class KafkaListnerServiceTest extends SpringBaseHttpTest {

    @Autowired
    private IBaseAcctKafkaServiceImpl iBaseAcctKafkaService;

    @Test
    public void queryCashierMultipleSettlementList() {
        ConsumerRecord record = new ConsumerRecord<>("123",1,1L,1,"{\"flowId\":\"ASF202104012144376800966\",\"fundAcctSubType\":2,\"fundAcctSubModel\":2,\"companyId\":\"605aa96a27f65f27af41f314\",\"operationAmount\":-1.00,\"bankName\":\"ZBBANK\",\"payAccountNo\":\"0356000003597880058906639\",\"operationUserId\":\"*********\",\"txnId\":\"1234561\",\"receiveAccountNo\":\"0356000003480165697783852\"}");
        iBaseAcctKafkaService.bankCounsume(record);
        System.out.println("Down");
    }

    @Test
    public void queryRefundf() {
        ConsumerRecord record = new ConsumerRecord<>("123",1,1L,1,"{\"flowId\":\"ASF202104121559084019404\",\"fundAcctSubType\":2,\"fundAcctSubModel\":2,\"companyId\":\"6062c7af27f65fe4a10160c0\",\"operationAmount\":390.00,\"bankName\":\"ZBBANK\",\"payAccountNo\":\"0356000003480165697783852\",\"operationUserId\":\"5d70ba9b23445f04bc031156\",\"txnId\":\"ml883202104121558139622192\",\"orgSysOrdNo\":\"3503849043257198603\",\"receiveAccountNo\":\"0356000003798281345435670\",\"bizNo\":\"OML202104101128310154663\"}");
        iBaseAcctKafkaService.bankRefund(record);
        System.out.println("Down");
    }

    @Test
    public void queryCashierMultipleSettlementListTest() {
        BigDecimal bigDecimal = new BigDecimal("-27000.00");
        BigDecimal bigDecimal1 = bigDecimal.abs().setScale(0,BigDecimal.ROUND_DOWN);
        BigDecimal bigDecimal2 = new BigDecimal("-390.00");
        BigDecimal bigDecimal3 = bigDecimal2.abs().setScale(0,BigDecimal.ROUND_DOWN);
        BigDecimal bigDecimal4 = new BigDecimal("100.00");
        BigDecimal bigDecimal5 = bigDecimal4.abs().setScale(0,BigDecimal.ROUND_DOWN);
        BigDecimal bigDecimal6 = new BigDecimal("80.00");
        BigDecimal bigDecimal7 = bigDecimal6.abs().setScale(0,BigDecimal.ROUND_DOWN);
        System.out.println("Down");
    }

}
