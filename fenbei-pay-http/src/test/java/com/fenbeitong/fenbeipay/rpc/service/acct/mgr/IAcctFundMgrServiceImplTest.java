package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonCreditService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonDebitService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCommonOptRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctGeneralOptRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.ExportAcctFlowTaskDataRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.luastar.swift.base.utils.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

public class IAcctFundMgrServiceImplTest extends SpringBaseHttpTest {



    @Autowired
    private IAcctFundMgrService iAcctFundMgrService;
    @Autowired
    private  UAcctCommonCreditService uAcctCommonCreditService;

    @Autowired
    private UAcctCommonDebitService uAcctCommonDebitService;

    @Autowired
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Autowired
    private com.fenbeitong.fenbeipay.api.service.acct.flow.IAcctFlowService iAcctFlowService;

    @Test
    public void testOne(){

        acctBusinessCreditFlowService.getAccountInstruction("123");

        System.out.println("Down");
    }
    @Test
    public void rechargeBank() {

        String json =
            "{\"bankAccountNo\":\"0356000003798281345435670\",\"bankAcctId\":\"0356000003798281345435670\",\"bankName\":\"ZBBANK\",\"bankTransNo\":\"345987979345111222\",\"bizNo\":\"**********\",\"operationAmount\":10000,\"operationChannelType\":1,\"targetBankAccountNo\":\"444411\",\"targetBankAllName\":\"工商银行\"}";
        AcctBankRechargeOptReqDTO acctBankRechargeOptReqDTO = JSON.parseObject(json, AcctBankRechargeOptReqDTO.class);
        //1.获取账户信息
        AcctGeneralOptRespDTO recharge = iAcctFundMgrService.rechargeByBank(acctBankRechargeOptReqDTO);
        System.out.println("Down");
    }

    @Test
    public void rechargeD0ByBank(){
        String json =
                "{\"bankAccountNo\":\"0356000003798281345435670\",\"bankAcctId\":\"0356000003798281345435670\",\"bankName\":\"ZBBANK\",\"bankTransNo\":\"****************\",\"bizNo\":\"**********11\",\"operationAmount\":10000,\"operationChannelType\":1,\"targetBankAccountNo\":\"444411\",\"targetBankAllName\":\"工商银行\"}";
        AcctBankRechargeOptReqDTO acctBankRechargeOptReqDTO = JSON.parseObject(json, AcctBankRechargeOptReqDTO.class);
        //1.获取账户信息
        AcctGeneralOptRespDTO recharge = iAcctFundMgrService.rechargeD0ByBank(acctBankRechargeOptReqDTO);
        System.out.println("Down");
    }

    @Test
    public void recharge() {
        AcctGeneralBankOptReqDTO reqDTO = new AcctGeneralBankOptReqDTO();
        reqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        reqDTO.setCompanyMainId("5747fbc10f0e60e0709d8d7d");

        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setRemark("转10000");
        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        reqDTO.setOperationUserId("test");
        reqDTO.setOperationUserName("test");

        reqDTO.setBankName(BankNameEnum.FBT.getCode());
        reqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setOperationAmount(new BigDecimal(100));
        reqDTO.setTargetBankAccountNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setTargetBankName(BankNameEnum.FBT.getCode());
        reqDTO.setRemark("转10000");

        //1.获取账户信息
        AcctGeneralOptRespDTO recharge = iAcctFundMgrService.recharge(reqDTO);
    }

    @Test
    public void cashWithdrawal() {
        AcctGeneralOptReqDTO reqDTO = new AcctGeneralOptReqDTO();
        reqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        reqDTO.setCompanyMainId("5747fbc10f0e60e0709d8d7d");

        reqDTO.setBizNo("5747fbc10f0e60e0709d5d6d");
        reqDTO.setRemark("转10000");
        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        reqDTO.setOperationUserId("test");
        reqDTO.setOperationUserName("test");

        reqDTO.setBankName(BankNameEnum.FBT.getCode());
        reqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setOperationAmount(new BigDecimal(1));
        reqDTO.setTargetBankAccountNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setTargetBankName(BankNameEnum.FBT.getCode());
        reqDTO.setRemark("转10000");
        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");

        //1.获取账户信息
        AcctGeneralOptRespDTO generalOptRespDTO = iAcctFundMgrService.cashWithdrawal(reqDTO);
    }


    /**
     {
     "bankAccountNo":"2156330000100006944",
     "bankName":"CGB",
     "companyId":"62441ca65230763cbabe0ca6",
     "companyMainId":"CMA202203301710416588714",
     "operationAmount":130,
     "operationChannelType":5,
     "operationUserId":"62441ca75230763cbabe0ca8",
     "operationUserName":"来者不",
     "targetBankAccountNo":"6214620421000325216",
     "targetBankName":"广发银行",
     "verifyCode":"8888",
     "verifyCodePhoneNum":"***********"
     }
     */
    @Test
    public void cashWithdrawalCgb() {
        AcctGeneralOptReqDTO reqDTO = new AcctGeneralOptReqDTO();
        reqDTO.setCompanyId("62441ca65230763cbabe0ca6");
        reqDTO.setCompanyMainId("CMA202203301710416588714");

        reqDTO.setBizNo("5747fbc10f0e60e0709d5d6d");
        reqDTO.setRemark("转10000");
        reqDTO.setOperationChannelType(OperationChannelType.WEB.getKey());
        reqDTO.setOperationUserId("62441ca75230763cbabe0ca8");
        reqDTO.setOperationUserName("来者不");

        reqDTO.setBankName(BankNameEnum.CGB.getCode());
        reqDTO.setBankAccountNo("2156330000100006944");
        reqDTO.setOperationAmount(new BigDecimal(14));
        reqDTO.setTargetBankAccountNo("6214620421000325216");
        reqDTO.setTargetBankName(BankNameEnum.CGB.getCode());
//        reqDTO.setRemark("转10000");
//        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setVerifyCode("8888");
        reqDTO.setVerifyCodePhoneNum("***********");

        //1.获取账户信息
        AcctGeneralOptRespDTO generalOptRespDTO = iAcctFundMgrService.cashWithdrawal(reqDTO);
    }

    @Test
    public void cashWithdrawalCallBack(){
        iAcctFundMgrService.cashWithdrawalCallBack("AGF202104281450377366312");
        System.out.println("Down");
    }

    @Test
    public void bankAcctWithdrawal4Stereo(){
        AcctGeneralOptReqDTO generalOptReqDTO = new AcctGeneralOptReqDTO();
        generalOptReqDTO.setBankAccountNo( "0356000004040692463636490");
        generalOptReqDTO.setBankName("ZBBANK");
        generalOptReqDTO.setCompanyId("609bd18caa422761b3345664");
        generalOptReqDTO.setCompanyMainId("CMA202105141442006411682");
        generalOptReqDTO.setOperationAmount(BigDecimal.valueOf(22200));
        generalOptReqDTO.setOperationChannelType(2);
        generalOptReqDTO.setOperationUserId("138456");
        generalOptReqDTO.setOperationUserName("王建宇");
        generalOptReqDTO.setTargetBankAccountNo("6227002451621180690");
        generalOptReqDTO.setTargetBankName("中国建设银行");
        generalOptReqDTO.setVerifyCode("8888");
        generalOptReqDTO.setVerifyCodePhoneNum("***********");
        iAcctFundMgrService.bankAcctWithdrawal4Stereo(generalOptReqDTO);
    }

    @Test
    public void transferOut2Others() {
//        AcctTransferDebitReqDTO reqDTO = new AcctTransferDebitReqDTO();
//        reqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
//        reqDTO.setCompanyMainId("5747fbc10f0e60e0709d8d7d");
//        reqDTO.setBankName(BankNameEnum.FBT.getCode());
//        reqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");
//
//        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
//        reqDTO.setRemark("转10000");
//        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
//        reqDTO.setOperationUserId("test");
//        reqDTO.setOperationUserName("test");
//        AcctCommonBaseDTO accountCredit = uAcctCommonDebitService.getAccountByCompanyId(reqDTO.getCompanyId(),FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//        List<AcctTransferBaseInfoDTO> transferList = new ArrayList<>();
//        AcctTransferBaseInfoDTO baseInfoDTO= new AcctTransferBaseInfoDTO();
//        baseInfoDTO.setAccountId(accountCredit.getAccountId());
//        baseInfoDTO.setAccountSubType(accountCredit.getAccountSubType());
//        baseInfoDTO.setOperationAmount(new BigDecimal(100));
//        baseInfoDTO.setRemark("转10000");
//        transferList.add(baseInfoDTO);
//        reqDTO.setTransferList(transferList);
        //1.获取账户信息
        String json = "{\n" +
                "    \"bankAccountNo\": \"3110730027033151286\",\n" +
                "    \"bankName\": \"CITIC\",\n" +
                "    \"companyId\": \"5b91427b23445f1bccbaf2f7\",\n" +
                "    \"companyMainId\": \"CMA202104142037102618257\",\n" +
                "    \"operationChannelType\": 5,\n" +
                "    \"operationUserId\": \"5d70ba9b23445f04bc031156\",\n" +
                "    \"operationUserName\": \"小龙人\",\n" +
                "    \"transferList\": [\n" +
                "        {\n" +
                "            \"accountId\": \"ASA202205171903101355849\",\n" +
                "            \"accountSubType\": 10,\n" +
                "            \"operationAmount\": 100,\n" +
                "            \"remark\": \"\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        AcctTransferDebitReqDTO acctTransferDebitReqDTO = JSON.parseObject(json, AcctTransferDebitReqDTO.class);
        AcctCommonOptRespDTO acctCommonOptRespDTO = iAcctFundMgrService.transferOut2Others(acctTransferDebitReqDTO);
        System.out.println("Down:  "+JSON.toJSONString(acctCommonOptRespDTO));
    }

    @Test
    public void transferOut2General() {

        AcctTransferCommonReqDTO reqDTO = new AcctTransferCommonReqDTO();
        reqDTO.setCompanyId("5b91427b23445f1bccbaf2f7");
        reqDTO.setCompanyMainId("CMA202104142037102618257");

        reqDTO.setBizNo("********");
        reqDTO.setRemark("转100");
        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        reqDTO.setOperationUserId("test");
        reqDTO.setOperationUserName("test");

//        AcctCommonBaseDTO accountCredit = uAcctCommonDebitService.getAccountByCompanyId(reqDTO.getCompanyId(), FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        reqDTO.setAccountId("ASA202205171903101355849");
        reqDTO.setAccountSubType(FundAccountSubType.REIMBURSEMENT_ACCOUNT.getKey());
        reqDTO.setBankName(BankNameEnum.CGB.getCode());
        reqDTO.setBankAccountNo("113");
        reqDTO.setOperationAmount(new BigDecimal(100));
        reqDTO.setTargetBankAccountNo("3110730027033151286");
        reqDTO.setTargetBankName(BankNameEnum.CITIC.getCode());
        reqDTO.setRemark("转100");

        //1.获取账户信息
        AcctCommonOptRespDTO respDTO = iAcctFundMgrService.transferOut2General(reqDTO);
    }

    @Test
    public void adjust() {
        AcctAdjustCreditReqDTO reqDTO = new AcctAdjustCreditReqDTO();
        reqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        reqDTO.setCompanyMainId("5747fbc10f0e60e0709d8d7d");
        reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        reqDTO.setBankName(BankNameEnum.FBT.getCode());
        reqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");

        reqDTO.setOperationAmount(new BigDecimal(100));
        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setRemark("转10000");
        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        reqDTO.setOperationUserId("test");
        reqDTO.setOperationUserName("test");
        //1.获取账户信息
        AcctCommonBaseDTO accountBase = uAcctCommonCreditService.getAccountCredit(reqDTO.getCompanyId(),reqDTO.getAccountSubType());
        reqDTO.setCreditAmount(accountBase.getInitCredit());
        iAcctFundMgrService.adjust(reqDTO);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void adjustFromSub() {
        for (int i = 0; i < 10; i++) {
            try {
                AcctTransferCreditToEachReqDTO reqDTO = new AcctTransferCreditToEachReqDTO();
                reqDTO.setCompanyId("627df3cfff5a97138fc3dc22");
                reqDTO.setCompanyMainId("627df3cfff5a97138fc3dc22");
                reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                reqDTO.setBankName(BankNameEnum.FBT.getCode());
                reqDTO.setBankAccountNo("627df3cfff5a97138fc3dc22");

                reqDTO.setAccountSubTypeTo(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                reqDTO.setTargetBankName(BankNameEnum.FBT.getCode());
                reqDTO.setTargetBankAccountNo("627df3cfff5a97138fc3dc22");

                reqDTO.setOperationAmount(new BigDecimal(100));
                reqDTO.setBizNo("627df3cfff5a97138fc3dc22");
                reqDTO.setRemark("转10000");
                reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
                reqDTO.setOperationUserId("test");
                reqDTO.setOperationUserName("test");
                iAcctFundMgrService.adjustFromSub(reqDTO);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void repayment() {
        AcctRepaymentCreditReqDTO reqDTO = new AcctRepaymentCreditReqDTO();
        reqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        reqDTO.setCompanyMainId("5747fbc10f0e60e0709d8d7d");
        reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        reqDTO.setBankName(BankNameEnum.FBT.getCode());
        reqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");

        reqDTO.setTargetBankName(BankNameEnum.FBT.getCode());
        reqDTO.setTargetBankAccountNo("5747fbc10f0e60e0709d8d7d");

        reqDTO.setRepaymentAmount(new BigDecimal(100));
        reqDTO.setServiceFee(new BigDecimal(10));
        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setRemark("转10000");
        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        reqDTO.setOperationUserId("test");
        reqDTO.setOperationUserName("test");
        iAcctFundMgrService.repayment(reqDTO);
    }

    @Test
    public void exportAcctFlowTaskData() {
        String a = "{\"companyId\":\"1231231\",\"tradeTypes\":[1,2,3],\"operationTypes\":[41,42,43],\"orderTypes\":[1,2,4],\"accountFlowId\":\"\",\"operationUserName\":\"\",\"startTime\":\"2021-08-06 00:00:00\",\"endTime\":\"2021-08-06 23:59:59\",\"fundPlatforms\":[\"FBT\",\"ZBBANK\",\"CITIC\"],\"accountModels\":[\"2\",\"1\"],\"accountSubTypes\":[\"1\",\"2\",\"3\",\"5\",\"6\",\"7\"],\"columns\":[{\"title\":\"交易时间\",\"field\":\"createTime\",\"type\":\"String\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"交易编号\",\"field\":\"accountFlowId\",\"type\":\"String\",\"width\":30,\"defaultValue\":\"\"},{\"title\":\"交易类型\",\"field\":\"tradeTypeName\",\"type\":\"String\",\"width\":30,\"defaultValue\":\"\"},{\"title\":\"业务类型\",\"field\":\"operationTypeDesc\",\"type\":\"String\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"对手账户\",\"field\":\"targetBankShow\",\"type\":\"String\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"对手账户户名\",\"field\":\"targetAccountName\",\"type\":\"String\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"对手账户账号\",\"field\":\"targetAccount\",\"type\":\"String\",\"width\":30,\"defaultValue\":\"\"},{\"title\":\"对手账户所属银行\",\"field\":\"targetBankAllName\",\"type\":\"String\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"交易金额\",\"field\":\"operationAmount\",\"type\":\"Number\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"余额\",\"field\":\"balance\",\"type\":\"Number\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"操作人\",\"field\":\"operationUserName\",\"type\":\"String\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"场景\",\"field\":\"orderTypeName\",\"type\":\"String\",\"width\":15,\"defaultValue\":\"\"},{\"title\":\"关联订单\",\"field\":\"bizNo\",\"type\":\"String\",\"width\":20,\"defaultValue\":\"\"},{\"title\":\"交易说明\",\"field\":\"operationDescription\",\"type\":\"String\",\"width\":20,\"defaultValue\":\"\"}]}";
        ExportAcctFlowTaskDataReqDTO reqDTO = JSON.parseObject(a,ExportAcctFlowTaskDataReqDTO.class);
        ExportAcctFlowTaskDataRespDTO respDTO = iAcctFlowService.exportAcctFlowTaskData(reqDTO);
        System.out.println(JSON.toJSON(respDTO));
    }

    @Test
    public void settlement(){
        //
        AcctSettlementRechargeOptReqDTO acctSettlementRechargeOptReqDTO = new AcctSettlementRechargeOptReqDTO();
        acctSettlementRechargeOptReqDTO.setOperationAmount(new BigDecimal("100"));
        acctSettlementRechargeOptReqDTO.setBankAccountNo("****************");
        acctSettlementRechargeOptReqDTO.setAccountId("ASA202111031743386315715");
        acctSettlementRechargeOptReqDTO.setBizNo("111");
        acctSettlementRechargeOptReqDTO.setCompanyId("617a5ff4bb374470e57e1e6b");
        acctSettlementRechargeOptReqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
        acctSettlementRechargeOptReqDTO.setRemark("转10000");
        acctSettlementRechargeOptReqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        acctSettlementRechargeOptReqDTO.setOperationUserId("test");
        acctSettlementRechargeOptReqDTO.setOperationUserName("test");
        acctSettlementRechargeOptReqDTO.setTargetBankAccountNo("****************");
        acctSettlementRechargeOptReqDTO.setTargetBankName("SPABANK");
        iAcctFundMgrService.settlement(acctSettlementRechargeOptReqDTO);
        System.out.println("Down");
    }

    @Test
    public void adjustTempAmount() {
        AcctAdjustCreditTempAmountReqDTO reqDTO = new AcctAdjustCreditTempAmountReqDTO();
        reqDTO.setCompanyId("627df3cfff5a97138fc3dc22");
        reqDTO.setCompanyMainId("627df3cfff5a97138fc3dc22");
        reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        reqDTO.setBankName(BankNameEnum.FBT.getCode());
        reqDTO.setBankAccountNo("627df3cfff5a97138fc3dc22");

        reqDTO.setOperationAmount(new BigDecimal(100));
        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setRemark("转10000");
        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        reqDTO.setOperationUserId("test");
        reqDTO.setOperationUserName("test");
        reqDTO.setRecoverDate(DateUtils.parse("2022-06-02", DateUtils.FORMAT_DATE_WITH_BAR));
        //1.获取账户信息
        AcctCommonBaseDTO accountBase = uAcctCommonCreditService.getAccountCredit(reqDTO.getCompanyId(),reqDTO.getAccountSubType());
        reqDTO.setCreditAmount(accountBase.getInitCredit());
        reqDTO.setOperationAmount(accountBase.getInitCredit().add(new BigDecimal(1000)));

        iAcctFundMgrService.adjustTempAmount(reqDTO);
    }

    @Test
    public void recoverTempAmount() {
        AcctAdjustCreditTempAmountReqDTO reqDTO = new AcctAdjustCreditTempAmountReqDTO();
        reqDTO.setCompanyId("627df3cfff5a97138fc3dc22");
        reqDTO.setCompanyMainId("627df3cfff5a97138fc3dc22");
        reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        reqDTO.setBankName(BankNameEnum.FBT.getCode());
        reqDTO.setBankAccountNo("627df3cfff5a97138fc3dc22");

        reqDTO.setOperationAmount(new BigDecimal(100));
        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setRemark("转10000");
        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        reqDTO.setOperationUserId("test");
        reqDTO.setOperationUserName("test");
        reqDTO.setRecoverDate(DateUtils.parse("2022-06-02", DateUtils.FORMAT_DATE_WITH_BAR));
        //1.获取账户信息
        AcctCommonBaseDTO accountBase = uAcctCommonCreditService.getAccountCredit(reqDTO.getCompanyId(),reqDTO.getAccountSubType());
        reqDTO.setCreditAmount(accountBase.getInitCredit());
        reqDTO.setOperationAmount(accountBase.getInitCredit().add(new BigDecimal(1000)));

        reqDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        reqDTO.setBankName(BankNameEnum.FBT.getCode());

        reqDTO.setOperationAmount(new BigDecimal(1000));
        reqDTO.setBizNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setRemark("转10000");
        reqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        reqDTO.setOperationUserId("test");
        reqDTO.setOperationUserName("test");
        //1.获取账户信息
        accountBase = uAcctCommonCreditService.getAccountCredit(reqDTO.getCompanyId(),reqDTO.getAccountSubType());
        reqDTO.setCreditAmount(accountBase.getInitCredit());
        reqDTO.setOperationAmount(accountBase.getInitCredit().add(accountBase.getTempAmount()));
        iAcctFundMgrService.recoverTempAmount(reqDTO);
    }

    @Test
    public void recoverTempAmountTask() {
        for (int i = 0; i < 10; i++) {
            try {
                uAcctCommonCreditService.recoverTempAmountTask();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
