package com.fenbeitong.fenbeipay.rpc.extract;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayShowPageReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.ExtractDayShowPageDTO;
import com.luastar.swift.base.json.JsonUtils;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractDayService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-08-29 2:36 下午
 */
@Slf4j
public class AcctExtractDayServiceTest extends SpringBaseHttpTest {

    @Autowired
    IAcctExtractDayService iAcctExtractDayService;

    @Test
    public void queryPage(){
        ExtractDayShowPageReqDTO extractDayShowPageReqDTO = new ExtractDayShowPageReqDTO();
        extractDayShowPageReqDTO.setOffset(10);
        extractDayShowPageReqDTO.setPageSize(10);
        extractDayShowPageReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        extractDayShowPageReqDTO.setStartTime("2022-03-28");
        extractDayShowPageReqDTO.setEndTime("2022-03-28");
        ResponsePage<ExtractDayShowPageDTO> extractDayShowPageDTOResponsePage = iAcctExtractDayService.queryPage(extractDayShowPageReqDTO);
        log.info("resp:{}", JsonUtils.toJson(extractDayShowPageDTOResponsePage));
    }
}
