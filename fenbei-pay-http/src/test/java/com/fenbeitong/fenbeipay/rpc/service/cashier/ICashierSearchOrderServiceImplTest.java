package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierMultipleTradeListRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierSearchMultipleTradeDetailRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierMultipleQueryReqRPCVo;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierSearchDetailReqRPCVo;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierSearchOrderService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ICashierSearchOrderServiceImplTest  extends SpringBaseHttpTest {

    @Autowired
    private ICashierSearchOrderService iCashierSearchOrderService;


    @Test
    public void queryCashierMultipleSettlementList() {
        CashierMultipleQueryReqRPCVo cashierMultipleQueryReqRPCVo = new CashierMultipleQueryReqRPCVo();
        cashierMultipleQueryReqRPCVo.setCompanyId("5abe0b315d88db7fd38d587d");
        cashierMultipleQueryReqRPCVo.setEmployeeId("5f1eb6aa93c6d62785b11e90");
        cashierMultipleQueryReqRPCVo.setFbOrderId("5f243a2205e5eb795c407621");
        CashierMultipleTradeListRPCDTO listRPCDTO =  iCashierSearchOrderService.queryCashierMultipleSettlementList(cashierMultipleQueryReqRPCVo);
    }

    @Test
    public void queryCashierMultipleDistinctFbOrderIdDetail() {
    }

    @Test
    public void batchQueryByPage() {
    }

    @Test
    public void searchMultiplePayDetail() {
        CashierSearchDetailReqRPCVo cashierSearchDetailReqRPCVo = new CashierSearchDetailReqRPCVo();
        cashierSearchDetailReqRPCVo.setCompanyId("5f02dba423445f416f6e08b0");
        cashierSearchDetailReqRPCVo.setEmployeeId("5d70ba9b23445f04bc031156");
        cashierSearchDetailReqRPCVo.setFbOrderId("5f2a211323445f4735b3848f");
        cashierSearchDetailReqRPCVo.setIsFindPayRecord(true);
        CashierSearchMultipleTradeDetailRPCDTO detailRPCDTO = iCashierSearchOrderService.searchMultiplePayDetail(cashierSearchDetailReqRPCVo);
    }

    @Test
    public void searchBatchMultiplePayList() {
    }
}
