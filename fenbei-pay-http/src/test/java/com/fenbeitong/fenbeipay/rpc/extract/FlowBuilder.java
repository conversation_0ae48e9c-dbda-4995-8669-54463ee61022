package com.fenbeitong.fenbeipay.rpc.extract;

import java.math.BigDecimal;
import java.util.Arrays;

import org.junit.Test;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.FlowReconWebListFlowRespDTO;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2022-11-08 10:17:04 
*/

public class FlowBuilder {

	@Test
	public void build() {
		
		FlowReconWebListFlowRespDTO tmp = new FlowReconWebListFlowRespDTO();
		tmp.setBillNo("*********");
		tmp.setOrderId("635fe89e07715f41c9da862c");
		tmp.setProductId("635fe89e07715f41c9da862c");
		tmp.setAmtCompanyAccountPay(BigDecimal.valueOf(60));
		tmp.setInBillCondName("已出账单");
		
		FlowReconWebListFlowRespDTO tmp1 = new FlowReconWebListFlowRespDTO();
		tmp1.setBillNo("*********");
		tmp1.setOrderId("635fe89e07715f41c9da862c");
		tmp1.setProductId("635fe89e07715f41c9da862c");
		tmp1.setAmtCompanyAccountPay(BigDecimal.valueOf(60));
		tmp1.setInBillCondName("已出账单");
		
		System.out.println(JSON.toJSONString(Arrays.asList(tmp, tmp1)));
	}
}
