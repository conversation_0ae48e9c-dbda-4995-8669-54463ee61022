package com.fenbeitong.fenbeipay.rpc.service.petty;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardUnBindPettyIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankPettyReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankPettyDetailRespDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankAccountService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardApplySearchService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankPettySearchService;
import com.fenbeitong.fenbeipay.bank.company.service.costAtt.CostAttributionService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryCostAttributionReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryCostAttributionResqVo;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/9/28
 */
public class IpettySearchServiceImplTest extends SpringBaseHttpTest {

    @Resource
    private IBankPettySearchService bankPettySearchService;
    @Autowired
    private CostAttributionService costAttributionService;
    @Autowired
    private IBankCardApplySearchService iBankCardApplySearchService;

    @Test
    public void testCost(){
        String id = "1000015";
        List<String> array= new ArrayList<>();
        array.add(id);
        iBankCardApplySearchService.queryApplyOrderCostInfo(array);
    }


    @Test
    public void testQueryPettyList()throws Exception {
        BankPettyReqDTO reqDTO=new BankPettyReqDTO();
        reqDTO.setCompanyId("58f96d565f281a53885d9b47");
        //reqDTO.setPettyId("BYJ202009052352273690532");
        //reqDTO.setPettyName("测");
        //reqDTO.setBankAccountNo("6225687352010243733");
        //reqDTO.setBankName("CGB");
        //reqDTO.setEmployeeId("5d67425c23445f6d1406a91d");
        //reqDTO.setEmployeeName("离");
        //reqDTO.setEmployeePhone("***********");
        //reqDTO.setBizNo("5f53720f23445f6c8b395392");
        //reqDTO.setCardModel(2);
        //reqDTO.setStartTime(DateUtil.getDateFromString("2019-12-04 11:45:06","yyyy-MM-dd HH:mm:ss"));
        //reqDTO.setEndTime(DateUtil.getDateFromString("2022-12-04 11:45:06","yyyy-MM-dd HH:mm:ss"));
        //reqDTO.setUserUnitName("小离部门");
        //reqDTO.setOrgUnitId("5dad99bc23445f1d50e10b94");
        //reqDTO.setIsBalance(true);
        reqDTO.setMaxPageSize(100);
        reqDTO.setPageNo(1);
        reqDTO.setPageSize(10);
        reqDTO.setMeaningNo("");
        //reqDTO.setCostAttributionId("5dad99bc23445f1d50e10b94");
        ResponsePage<BankPettyDetailRespDTO> virtualCardList = bankPettySearchService.queryBankPettyList(reqDTO);
        System.out.println(JSONObject.toJSONString(virtualCardList));

        //BankPettyDetailRespDTO respDTO=bankPettySearchService.queryBankPettyByPettyId("BYJ202009052352273690532");
        //System.out.println("=========="+JSONObject.toJSONString(respDTO));
    }
    @Test
    public void testQueryCostAttributionList()throws Exception {
        QueryCostAttributionReqVo reqVo=new QueryCostAttributionReqVo();
        reqVo.setCompanyId("5c126b8a23445f41dcdf5f97");
        ResponsePage<QueryCostAttributionResqVo> rep=costAttributionService.getCostAttList(reqVo);
        System.out.println("=========="+JSONObject.toJSONString(rep));
    }

    @Test
    public void testqueryExpiredBankPettyList()throws Exception {
        Date startDate = new Date();
        Calendar c = new GregorianCalendar();
        c.add(Calendar.DATE, 50);
        Date endDate = c.getTime();
        BankPettyReqDTO bankPettyReqDTO = new BankPettyReqDTO();
        bankPettyReqDTO.setPettyId(null);
        bankPettyReqDTO.setCompanyId(null);
        bankPettyReqDTO.setStartTime(startDate);
        bankPettyReqDTO.setEndTime(endDate);
        bankPettyReqDTO.setPageNo(1);
        bankPettyReqDTO.setPageSize(50);
        try {
            ResponsePage<BankPettyDetailRespDTO> response = bankPettySearchService.queryExpiredBankPettyList(bankPettyReqDTO);
            if (response != null || !CollectionUtils.isEmpty(response.getDataList())) {
                return ;
            }
        } catch (Exception e) {
            System.out.println(e);
        }
    }




}
