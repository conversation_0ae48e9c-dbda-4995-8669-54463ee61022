package com.fenbeitong.fenbeipay.rpc.voucher;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherSourceType;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTaskType;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersBusinessFlowRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskDetailsCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskDetailsRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTempletRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VoucherForStatisticsRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersFlowRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersOperationFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.external.IExternalVoucherRpcService;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTaskService;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTempletService;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.BankCardApplyFlowManger;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherConstant;
import com.fenbeitong.fenbeipay.vouchers.dto.VoucherMetaDTO;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersMetaService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.vouchers.unit.service.UVoucherRecoveryTaskService;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.uc.KafkaEmployeeUpdateMsg;
import com.fenbeitong.usercenter.api.model.enums.employee.OperateType;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * CashierOrderSettlementServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Nov 26, 2018</pre>
 */
public class VoucherServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private IVouchersTempletService iVouchersTempletService;
    @Autowired
    private IVouchersPersonService iVouchersPersonService;
    @Autowired
    private IVouchersTaskService iVouchersTaskService;
    @Autowired
    private BankCardApplyFlowManger bankCardApplyFlowManger;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private RedisDao redisDao;
    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    private VouchersMetaService vouchersMetaService;
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private UVoucherRecoveryTaskService uVoucherRecoveryTaskService;
    @Autowired
    private IExternalVoucherRpcService iExternalVoucherRpcService;
    @Test
    public void testEmployeeLeave() throws Exception {
        KafkaEmployeeUpdateMsg iMessage = new KafkaEmployeeUpdateMsg();
        iMessage.setCompanyId("5c107d8523445f330630d1d4");
        iMessage.setEmployeeId("5cd9450823445f0b1e064095");
        iMessage.setOperateStatus(OperateType.DELETE_OP.getKey());

        //员工离职，回收分贝券
        try {
            if (OperateType.DELETE_OP.getKey() != iMessage.getOperateStatus()) {
                return;
            }
            String companyId = iMessage.getCompanyId();
            String employeeId = iMessage.getEmployeeId();
            List<String> recoveryTaskIds = uVoucherRecoveryTaskService.createWithdrawalTaskByEmployeeId(companyId, employeeId, VoucherConstant.EMPLOYEE_DELETE_OPERATION, VoucherConstant.EMPLOYEE_DELETE_OPERATION);
//            uVoucherRecoveryTaskService.startWithdrawTasks(recoveryTaskIds);
            FinhubLogger.info("【kafka消费】员工离职回收分贝券消费完成 data:{}", iMessage.toString());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券，撤回员工可用分贝券异常】：{}", iMessage.toString(), e);
        }
    }

    /**
     * Method: getAllPayAbility(PersonCashierBaseVo personCashierBaseVo)
     */
    @Test
    public void testCreateBountyGrantTask() throws Exception {
        VouchersTaskRPCDTO vouchersTaskRPCDTO = new VouchersTaskRPCDTO();
        vouchersTaskRPCDTO.setVouchersTaskName("asdasdasdas");
        vouchersTaskRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        vouchersTaskRPCDTO.setOperationUserId("5cd9450823445f0b1e064095");
        vouchersTaskRPCDTO.setOperationUserName("5cd9450823445f0b1e064095");
        vouchersTaskRPCDTO.setOperationUserPhone("15510756518");
        vouchersTaskRPCDTO.setOperationUserDepartment("战船测试");
        vouchersTaskRPCDTO.setBizNo("zhanchuanceshi12345678980");
        vouchersTaskRPCDTO.setIsStart(false);
        VouchersTempletRPCDTO vouchersInfo = new VouchersTempletRPCDTO();
        vouchersInfo.setId(10355L);
        vouchersInfo.setVoucherTempletId("QMB210324182726853623257");
        vouchersInfo.setVoucherName("测试战船后票");
        Date date = new Date();
        vouchersInfo.setVoucherEffectiveTime(date);
        Date dayAdd1 = DateUtils.addDay(date, 1);
        vouchersInfo.setVoucherExpiryTime(dayAdd1);
        vouchersInfo.setVoucherEffectiveDays(1);
        List<String> vtl = Lists.newArrayList("0032", "0062", "0072", "0112", "0202", "0502", "123211", "123212", "1242", "125210", "125220");
        vouchersInfo.setVoucherTypeList(vtl);
        vouchersInfo.setCanTransfer(1);
        vouchersInfo.setVoucherModel(1);
        vouchersInfo.setCostAttributionSign(1);
        vouchersInfo.setVoucherDesc("战船测试");
        vouchersTaskRPCDTO.setVouchersInfo(vouchersInfo);
        List<VouchersTaskDetailsRPCDTO> vouchersTaskDetailsRPCDTOList = new ArrayList<>();
        VouchersTaskDetailsRPCDTO vouchersTaskDetailsRPCDTO = new VouchersTaskDetailsRPCDTO();
        vouchersTaskDetailsRPCDTO.setOperationUserId("5cd9450823445f0b1e064095");
        vouchersTaskDetailsRPCDTO.setVoucherName("测试战船后票");
        vouchersTaskDetailsRPCDTO.setVoucherDenomination(BigDecimal.ONE);
        vouchersTaskDetailsRPCDTO.setVoucherEffectiveTime(date);
        vouchersTaskDetailsRPCDTO.setVoucherExpiryTime(dayAdd1);
        vouchersTaskDetailsRPCDTO.setVoucherTypeList(vtl);
        vouchersTaskDetailsRPCDTO.setCompanyId("");
        vouchersTaskDetailsRPCDTO.setEmployeeId("5cd9450823445f0b1e064095");
        vouchersTaskDetailsRPCDTO.setBizNo("zhanchuanceshi12345678980");
        String voucherTaskId = "QRW210325105635995862357";
        String companyId = "5c107d8523445f330630d1d4";
//        Boolean isStart = true;
        vouchersTaskDetailsRPCDTOList.add(vouchersTaskDetailsRPCDTO);
//        vouchersTaskRPCDTO.setVouchersTaskDetailsRPCDTOList(vouchersTaskDetailsRPCDTOList);
//        VouchersTaskResponseRPCDTO bountyGrantTask = iVouchersTaskService.createBountyGrantTask(vouchersTaskRPCDTO);
//        iVouchersTaskService.addBountyGrantTaskDetails(vouchersTaskDetailsRPCDTO, voucherTaskId, companyId, false);
        iVouchersTaskService.addBountyGrantTaskDetails(vouchersTaskDetailsRPCDTOList, voucherTaskId, companyId, true);
    }

    @Test
    public void testCreateVouchersGrantTask() throws Exception {
        VouchersTaskRPCDTO vouchersTaskRPCDTO = new VouchersTaskRPCDTO();
        vouchersTaskRPCDTO.setVouchersTaskName("asdasdasdas");
        vouchersTaskRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        vouchersTaskRPCDTO.setOperationUserId("5cd9450823445f0b1e064095");
        vouchersTaskRPCDTO.setOperationUserName("5cd9450823445f0b1e064095");
        vouchersTaskRPCDTO.setOperationUserPhone("15510756518");
        vouchersTaskRPCDTO.setOperationUserDepartment("战船测试");
        vouchersTaskRPCDTO.setBizNo("zhanchuanceshi12345678980");
        vouchersTaskRPCDTO.setIsStart(true);
        VouchersTempletRPCDTO vouchersInfo = new VouchersTempletRPCDTO();
        vouchersInfo.setId(10355L);
        vouchersInfo.setVoucherTempletId("QMB210324182726853623257");
        vouchersInfo.setVoucherName("测试战船后票");
        Date date = new Date();
        vouchersInfo.setVoucherEffectiveTime(date);
        Date dayAdd1 = DateUtils.addDay(date, 1);
        vouchersInfo.setVoucherExpiryTime(dayAdd1);
        vouchersInfo.setVoucherEffectiveDays(1);
        List<String> vtl = Lists.newArrayList("0032", "0062", "0072", "0112", "0202", "0502", "123211", "123212", "1242", "125210", "125220");
        vouchersInfo.setVoucherTypeList(vtl);
        vouchersInfo.setCanTransfer(1);
        vouchersInfo.setVoucherModel(1);
        vouchersInfo.setCostAttributionSign(1);
        vouchersInfo.setVoucherDesc("战船测试");
        vouchersTaskRPCDTO.setVouchersInfo(vouchersInfo);
        List<VouchersTaskDetailsRPCDTO> vouchersTaskDetailsRPCDTOList = new ArrayList<>();
        VouchersTaskDetailsRPCDTO vouchersTaskDetailsRPCDTO = new VouchersTaskDetailsRPCDTO();
        vouchersTaskDetailsRPCDTO.setOperationUserId("5cd9450823445f0b1e064095");
        vouchersTaskDetailsRPCDTO.setVoucherName("测试战船后票");
        vouchersTaskDetailsRPCDTO.setVoucherDenomination(BigDecimal.ONE);
        vouchersTaskDetailsRPCDTO.setVoucherEffectiveTime(date);
        vouchersTaskDetailsRPCDTO.setVoucherExpiryTime(dayAdd1);
        vouchersTaskDetailsRPCDTO.setVoucherTypeList(vtl);
        vouchersTaskDetailsRPCDTO.setCompanyId("");
        vouchersTaskDetailsRPCDTO.setEmployeeId("5cd9450823445f0b1e064095");
        vouchersTaskDetailsRPCDTO.setBizNo("zhanchuanceshi12345678980");
        vouchersTaskDetailsRPCDTOList.add(vouchersTaskDetailsRPCDTO);
        vouchersTaskDetailsRPCDTOList.add(vouchersTaskDetailsRPCDTO);
        vouchersTaskRPCDTO.setVouchersTaskDetailsRPCDTOList(vouchersTaskDetailsRPCDTOList);
        iVouchersTaskService.createVouchersGrantTask(vouchersTaskRPCDTO, VoucherTaskType.EXAMINE_APPROVE_GRANT.getValue(), VoucherSourceType.COMPANY_GRANT_EXAMINE_APPROVE.getValue());
    }

    @Test
    public void testCreateGrantTaskByVouchersTemplet() throws Exception {
        VouchersTaskCreateRPCDTO taskCreateRPCDTO = new VouchersTaskCreateRPCDTO();
        taskCreateRPCDTO.setVouchersTaskName("asdasdasdas");
        taskCreateRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        taskCreateRPCDTO.setOperationUserId("5cd9450823445f0b1e064095");
        taskCreateRPCDTO.setOperationUserName("5cd9450823445f0b1e064095");
        taskCreateRPCDTO.setOperationUserPhone("15510756518");
        taskCreateRPCDTO.setOperationUserDepartment("战船测试");
        taskCreateRPCDTO.setBizNo("zhanchuanceshi12345678980");
        taskCreateRPCDTO.setIsStart(true);
        taskCreateRPCDTO.setVoucherTempletId("QMB210324182726853623257");
        taskCreateRPCDTO.setVoucherTaskType(VoucherTaskType.OPEN_API_GRANT.getValue());
        taskCreateRPCDTO.setVoucherSourceType(VoucherSourceType.COMPANY_GRANT.getValue());
        List<VouchersTaskDetailsCreateRPCDTO> vouchersTaskDetailsRPCDTOList = new ArrayList<>();
        VouchersTaskDetailsCreateRPCDTO vouchersTaskDetailsRPCDTO = new VouchersTaskDetailsCreateRPCDTO();
        vouchersTaskDetailsRPCDTO.setVoucherDenomination(BigDecimal.ONE);
        vouchersTaskDetailsRPCDTO.setEmployeeId("5cd9450823445f0b1e064095");
        vouchersTaskDetailsRPCDTO.setBizNo("zhanchuanceshi12345678980");
        vouchersTaskDetailsRPCDTOList.add(vouchersTaskDetailsRPCDTO);
        vouchersTaskDetailsRPCDTOList.add(vouchersTaskDetailsRPCDTO);
        taskCreateRPCDTO.setTaskDetailsCreateRPCDTOS(vouchersTaskDetailsRPCDTOList);
        iVouchersTaskService.createGrantTaskByVouchersTemplet(taskCreateRPCDTO);
    }

    @Test
    public void testConsumer() throws Exception {
        VoucherMetaDTO dto = new VoucherMetaDTO();
        dto.setMetaKey("YHZDYQLS2101191630085APHI5B86");
        dto.setCompanyId("5c107d8523445f330630d1d4");
        dto.setEmployeeId("5cd9450823445f0b1e064095");
        vouchersMetaService.saveVoucherMeta(Lists.newArrayList(dto), "SYSTEM", "SYSTEM");
    }

    @Test
    public void testRPC() throws Exception {

        List<VouchersOperationFlowRespRPCDTO> flowRespRPCDTOS = iVouchersPersonService.queryVoucherFlowList(Lists.newArrayList("QLS2103151811084LUX77394"));
        System.out.println(flowRespRPCDTOS);

    }

    @Test
    public void grantRecoveryStatistics() throws Exception {
        VouchersReqRPCDTO dto = new VouchersReqRPCDTO();
        dto.setCompanyId("5747fbc10f0e60e0709d8d7d");
        dto.setStartTime(DateUtil.getDateFromStringByDefaultDateFormat("2023-01-08 23:59:00"));
        dto.setEndTime(DateUtil.getDateFromStringByDefaultDateFormat("2023-02-07 23:59:00"));

        ResponsePage<VoucherForStatisticsRPCDTO> statistics = iExternalVoucherRpcService.grantRecoveryStatistics(dto);
        System.out.println(JSONObject.toJSONString(statistics));

    }
    @Test
    public void selectConsumeFlowByPage() throws Exception {
        VouchersBusinessFlowRPCDTO dto = new VouchersBusinessFlowRPCDTO();
        dto.setCompanyId("5747fbc10f0e60e0709d8d7d");
        dto.setStartTime(DateUtil.getDateFromStringByDefaultDateFormat("2023-01-08 23:59:00"));
        dto.setEndTime(DateUtil.getDateFromStringByDefaultDateFormat("2023-02-07 23:59:00"));
        ResponsePage<VouchersFlowRPCDTO> flow = iExternalVoucherRpcService.selectConsumeFlowByPage(dto);
        System.out.println(JSONObject.toJSONString(flow));
    }

}
