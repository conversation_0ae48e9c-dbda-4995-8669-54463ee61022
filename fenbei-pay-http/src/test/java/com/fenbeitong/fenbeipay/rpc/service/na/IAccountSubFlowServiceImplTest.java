package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubFindReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubFlowStereoFindReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFindRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubFlowService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class IAccountSubFlowServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private IAccountSubFlowService iAccountSubFlowService;

    @Test
    public void queryAccountSubFlowsByStereoTest() {
        AccountSubFlowStereoFindReqRPCDTO accountSubFlowStereoFindReqRPCDTO = new AccountSubFlowStereoFindReqRPCDTO();
        List<Integer> accountSubTypes = new ArrayList<>();
        accountSubTypes.add(2);
        accountSubFlowStereoFindReqRPCDTO.setAccountSubTypes(accountSubTypes);
        accountSubFlowStereoFindReqRPCDTO.setAccountModel(1);
        accountSubFlowStereoFindReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        accountSubFlowStereoFindReqRPCDTO.setOffset(0);
        accountSubFlowStereoFindReqRPCDTO.setPageNo(1);

        long start = System.currentTimeMillis();
        ResponsePage<AccountSubFlowRespRPCDTO> accountSubFlowRespRPCDTOResponsePage = iAccountSubFlowService.queryAccountSubFlowsByStereo(accountSubFlowStereoFindReqRPCDTO);
        long l = System.currentTimeMillis() - start;
        //0.9秒
        System.out.println("v4.0");

    }

    @Test
    public void queryAccountOperationAmountByBizNoTest() {
        AccountSubFlowStereoFindReqRPCDTO accountSubFlowStereoFindReqRPCDTO = new AccountSubFlowStereoFindReqRPCDTO();
        String bizNo = "AGA201904011736260009957";
        int accountSubType = 2;
        int accountSubOperationType = 21;
        long start = System.currentTimeMillis();
        BigDecimal bigDecimal = iAccountSubFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubType, accountSubOperationType);
        long l = System.currentTimeMillis() - start;
        //1s
        System.out.println("v4.0根据bizNo查询操作账户总额");

    }

    @Test
    public void queryAccountSubFlowByGrantTaskIdTest() {
        String voucherGrantTaskId = "5ca35e39ff9f8e3783eea392";
        int accountSubTyp = 3;
        long start = System.currentTimeMillis();
        AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = iAccountSubFlowService.queryAccountSubFlowByGrantTaskId(voucherGrantTaskId, accountSubTyp);
        long l = System.currentTimeMillis() - start;
        System.out.println("v4.0根据发放任务ID查询账户流水");

    }

    @Test
    public void queryAccountSubFlowByFlowIdTest() {
        AccountSubFlowStereoFindReqRPCDTO accountSubFlowStereoFindReqRPCDTO = new AccountSubFlowStereoFindReqRPCDTO();
        String subFlowId = "ASF201904022106035401412";
//        String subFlowId = "";
//        String subFlowId = "";
//        String subFlowId = "";
        long start = System.currentTimeMillis();
        AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = iAccountSubFlowService.queryAccountSubFlowByFlowId(subFlowId);
        long l = System.currentTimeMillis() - start;
        System.out.println("v4.0根据流水id查询流水");

    }


}
