package com.fenbeitong.fenbeipay.rpc.service.na;


import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountSubOperationType;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFindRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.CompanyCardDistributeAmountCheckResp;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.CompanyCreditInfoDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.ResultRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountSubFlowFindVO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.finhub.common.constant.FundAccountOptType;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;

import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class IAccountSubServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private IAccountSubService iAccountSubService;

    @Test
    public void queryAccountSubInfo() {
        AccountSubFindReqRPCDTO accountSubFindReqRPCDTO = new AccountSubFindReqRPCDTO();
        accountSubFindReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        accountSubFindReqRPCDTO.setAccountSubType(2);
        AccountSubFindRespRPCDTO accountSubFindRespRPCDTO = iAccountSubService.queryAccountSubInfo(accountSubFindReqRPCDTO);
        System.out.println("4.0查询子账户详情,商务账户、个人账户、企业账户");
    }

    @Test
    public void queryActAccountSubInfo() {
        AccountSubFindReqRPCDTO accountSubFindReqRPCDTO = new AccountSubFindReqRPCDTO();
        accountSubFindReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        accountSubFindReqRPCDTO.setAccountSubType(2);
        AccountSubRespDTO accountSubRespDTO = iAccountSubService.queryActAccountSubInfo(accountSubFindReqRPCDTO);
        System.out.println("4.0查询子账户详情,商务账户、个人账户、企业账户");
    }

    @Test
    public void queryActAccountSubInfoTest() {
        AccountSubFindReqRPCDTO accountSubFindReqRPCDTO = new AccountSubFindReqRPCDTO();
        accountSubFindReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        accountSubFindReqRPCDTO.setAccountSubType(2);
        AccountSubRespDTO accountSubRespDTO = iAccountSubService.queryActAccountSubInfo("5c107d8523445f330630d1d4",2);
        System.out.println("4.0查询子账户详情,商务账户、个人账户、企业账户");
    }

    @Test
    public void queryAccountSubBalanceTest() {
        AccountSubFindReqRPCDTO accountSubFindReqRPCDTO = new AccountSubFindReqRPCDTO();
        accountSubFindReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        accountSubFindReqRPCDTO.setAccountSubType(2);
        BigDecimal bigDecimal = iAccountSubService.queryAccountSubBalance("5c107d8523445f330630d1d4", 2);
        System.out.println("4.0查询子账户详情,商务账户、个人账户、企业账户");
    }

    @Test
    public void queryAccountSubInfoListTest() {
        AccountSubFindReqRPCDTO accountSubFindReqRPCDTO = new AccountSubFindReqRPCDTO();
        accountSubFindReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        accountSubFindReqRPCDTO.setAccountSubType(2);
        List<String> companyIds = new ArrayList<>();
        companyIds.add("5c107d8523445f330630d1d4");
        List<AccountSubRespDTO> accountSubRespDTOS = iAccountSubService.queryAccountSubInfoList(companyIds, 2);
        System.out.println("4.0批量查询子账户信息");
    }

    @Test
    public void adjust() {
    }

    @Test
    public void repayment() {
    }

    @Test
    public void subToGeneral() {
    }

    @Test
    public void consume() {
        AccountSubOperationReqRPCDTO accountSubOperationReqRPCDTO = new AccountSubOperationReqRPCDTO();
        accountSubOperationReqRPCDTO.setCompanyId("605aa96a27f65f27af41f314");
        accountSubOperationReqRPCDTO.setAccountSubId("ASA202103241052264740466");
        //商务
        accountSubOperationReqRPCDTO.setAccountSubType(2);
        //1 授信  2充值
        accountSubOperationReqRPCDTO.setAccountModel(2);
        accountSubOperationReqRPCDTO.setFundAccountOptType(FundAccountOptType.CONSUME.getKey());
        accountSubOperationReqRPCDTO.setOperationAmount(new BigDecimal(1));
        accountSubOperationReqRPCDTO.setBizNo("test123646");
        iAccountSubService.consume(accountSubOperationReqRPCDTO);
        System.out.println("Down");
        try {
            Thread.sleep(30000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println("Down.....");
    }

    @Test
    public void adjustToSub() {
        AccountSubAdjustToSubReqRPCDTO reqRPCDTO = new AccountSubAdjustToSubReqRPCDTO();
        reqRPCDTO.setAccountModel(1);
        reqRPCDTO.setAccountSubType(2);
        reqRPCDTO.setAccountSubTypeTo(3);
        reqRPCDTO.setBizNo("60585f75473dfcdbca90756d");
        reqRPCDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        reqRPCDTO.setCompanyName("北京分贝金服科技有限公司");
        reqRPCDTO.setFundAccountOptType(31);
        reqRPCDTO.setOperationAmount(new BigDecimal(78));
        reqRPCDTO.setOperationDescription("test商务授信转到个人授信78分");
        reqRPCDTO.setOperationUserId("601a6c4827f65f069cc8f9d3");
        reqRPCDTO.setOperationUserName("学武");
        iAccountSubService.adjustToSub(reqRPCDTO);
        System.out.println("Down");
    }

    @Test
    public void refund() {
        AccountSubOperationReqRPCDTO accountRefundFreezeReqRPCDTO = new AccountSubOperationReqRPCDTO();
        accountRefundFreezeReqRPCDTO.setCompanyId("605aa96a27f65f27af41f314");
        accountRefundFreezeReqRPCDTO.setAccountSubId("ASA202103241052264740466");
        //商务
        accountRefundFreezeReqRPCDTO.setAccountSubType(2);
        //1 授信  2充值
        accountRefundFreezeReqRPCDTO.setAccountModel(2);
        accountRefundFreezeReqRPCDTO.setFundAccountOptType(FundAccountOptType.REFUND.getKey());
        accountRefundFreezeReqRPCDTO.setOperationAmount(new BigDecimal(1));
        accountRefundFreezeReqRPCDTO.setBizNo("test1235467");
        accountRefundFreezeReqRPCDTO.setReBizNo("test123646");

        iAccountSubService.refund(accountRefundFreezeReqRPCDTO);
        System.out.println("Down");
        System.out.println("Down.....");
    }

    @Test
    public void frozen() {
    }

    @Test
    public void unfreeze() {
    }

    @Test
    public void unfreezeAndConsume() {
    }

    @Test
    public void consumeFreezen() {
    }

    @Test
    public void refundFreezen() {
    }

    //如果激活的账户全是充值账户，该逻辑有问题，会导致空指针，初始授信额度为null参与运算
    @Test
    public void getCreditOverView() {
        CompanyCreditInfoDTO creditOverView = iAccountSubService.getCreditOverView("5747fbc10f0e60e0709d8d7d");
        System.out.println("4.0获取公司信息总览");
        System.out.println(creditOverView.toString());
    }

    @Test
    public void findByInitCredit() {
    }

    @Test
    public void queryTotalConsumeAmount() {
    }

    @Test
    public void queryAccountSubFlowList() {

        AccountSubFlowFindReqRPCDTO accountSubFlowFindReqRPCDTO  = new  AccountSubFlowFindReqRPCDTO();
        accountSubFlowFindReqRPCDTO.setEndTime(new Date());
        accountSubFlowFindReqRPCDTO.setStartTime(DateUtil.changeDateDay(new Date(),-18));
        accountSubFlowFindReqRPCDTO.setCompanyId("5c244e2a23445f57184fd2e9");
        accountSubFlowFindReqRPCDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//        accountSubFlowFindReqRPCDTO.setOperationTypes(Arrays.asList(21,22,23,43));
        iAccountSubService.queryAccountSubFlowList(accountSubFlowFindReqRPCDTO);
        System.out.println("4.0根据企业Id，子账户类别，时间查询子账户流水");
    }

    @Test
    public void queryAbsSumAccountSubFlow() {
        AccountSubFlowFindReqRPCDTO accountSubFlowFindReqRPCDTO  = new  AccountSubFlowFindReqRPCDTO();
        accountSubFlowFindReqRPCDTO.setEndTime(new Date());
        accountSubFlowFindReqRPCDTO.setStartTime(DateUtil.changeDateDay(new Date(),-18));
        accountSubFlowFindReqRPCDTO.setCompanyId("5c244e2a23445f57184fd2e9");
        accountSubFlowFindReqRPCDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        accountSubFlowFindReqRPCDTO.setOperationTypes(Arrays.asList(21,22));
        BigDecimal bigDecimal = iAccountSubService.queryAbsSumAccountSubFlow(accountSubFlowFindReqRPCDTO);
        System.out.println("4.0流水中操作金额绝对值求和");
    }

    @Test
    public void querySumAccountSubFlow() {
        AccountSubFlowFindReqRPCDTO accountSubFlowFindReqRPCDTO  = new  AccountSubFlowFindReqRPCDTO();
        accountSubFlowFindReqRPCDTO.setEndTime(new Date());
        accountSubFlowFindReqRPCDTO.setStartTime(DateUtil.changeDateDay(new Date(),-18));
        accountSubFlowFindReqRPCDTO.setCompanyId("5c244e2a23445f57184fd2e9");
        accountSubFlowFindReqRPCDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        accountSubFlowFindReqRPCDTO.setOperationTypes(Arrays.asList(21,22));
        long start = System.currentTimeMillis();
        BigDecimal bigDecimal = iAccountSubService.querySumAccountSubFlow(accountSubFlowFindReqRPCDTO);
        long l = System.currentTimeMillis() - start;
        // 1.3秒
        System.out.println("4.0流水中操作金额求和,正负有抵扣");
    }

    @Test
    public void querySumAccountSubFlows() {
        AccountSubFlowsFindReqRPCDTO flowsFindReqRPCDTO  = new AccountSubFlowsFindReqRPCDTO();
        flowsFindReqRPCDTO.setEndTime(new Date());
        flowsFindReqRPCDTO.setStartTime(DateUtil.changeDateDay(new Date(),-18));
        flowsFindReqRPCDTO.setCompanyIds(Arrays.asList("5c244e2a23445f57184fd2e9"));
        flowsFindReqRPCDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        flowsFindReqRPCDTO.setOperationTypes(Arrays.asList(AccountSubOperationType.TRANSFER_INTO));
        long start = System.currentTimeMillis();
        List<AccountSubFlowFindVO> vos =  iAccountSubService.querySumCompaniesAccountSubFlow(flowsFindReqRPCDTO);
        long l = System.currentTimeMillis() - start;
        vos.size();
        //1秒
        System.out.println("4.0流水中操作金额求和,正负有抵扣");
    }

    @Test
    public void queryAccountSubInfoOrRedcouponTest() {
        AccountSubFindReqRPCDTO accountSubFindReqRPCDTO = new AccountSubFindReqRPCDTO();
        AccountSubFlowsFindReqRPCDTO flowsFindReqRPCDTO  = new AccountSubFlowsFindReqRPCDTO();
        flowsFindReqRPCDTO.setEndTime(new Date());
        flowsFindReqRPCDTO.setStartTime(DateUtil.changeDateDay(new Date(),-18));
        flowsFindReqRPCDTO.setCompanyIds(Arrays.asList("5c244e2a23445f57184fd2e9"));
        flowsFindReqRPCDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        flowsFindReqRPCDTO.setOperationTypes(Arrays.asList(AccountSubOperationType.TRANSFER_INTO));
        long start = System.currentTimeMillis();
        AccountSubFindRespRPCDTO accountSubFindRespRPCDTO = iAccountSubService.queryAccountSubInfoOrRedcoupon(accountSubFindReqRPCDTO, 15, 1);
        long l = System.currentTimeMillis() - start;
        //25秒
        System.out.println("4.0获取子账户以及红包券余额   需要兼容新账户");
    }

    @Test
    public void disableAccount() {
    }

    @Test
    public void disableAccountSubs(){
        AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO=new AccountSubAbleReqRPCDTO();
        accountSubAbleReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        iAccountSubService.disableAccountSubs(accountSubAbleReqRPCDTO);

    }

    @Test
    public void enableAccountSubs(){
        AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO=new AccountSubAbleReqRPCDTO();
        accountSubAbleReqRPCDTO.setCompanyId("5c107d8523445f330630d1d4");
        iAccountSubService.enableAccountSubs(accountSubAbleReqRPCDTO);

    }


    @Test
    public void checkCompanyCardBalanceTest(){
        CompanyCardDistributeAmountCheckReq req = new CompanyCardDistributeAmountCheckReq();
        req.setCompanyId("5d1b1d2f23445f4dca76304b");
        ArrayList<CompanyCardSubAccountAmountDto> amountList = Lists.newArrayList();
        amountList.add(new CompanyCardSubAccountAmountDto("CGB", new BigDecimal(100)));
        amountList.add(new CompanyCardSubAccountAmountDto("SPABANK", new BigDecimal(10000L)));
        req.setAccountAmountList(amountList);
        System.out.println("req: " + JsonUtils.toJson(req));
        ResultRespDTO<CompanyCardDistributeAmountCheckResp> resp = iAccountSubService.checkCompanyCardBalance(req);

        System.out.println(JsonUtils.toJson(resp));
    }
}
