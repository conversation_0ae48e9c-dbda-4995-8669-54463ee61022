package com.fenbeitong.fenbeipay.rpc.redCoupon;

import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponGrantReqDTO;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * CashierOrderSettlementServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Nov 26, 2018</pre>
 */
public class RedCouponTest extends SpringBaseHttpTest {

    @Autowired
    private IAccountRedcouponService iAccountRedcouponService;

    @Test
    public void grantRedCoupon() throws Exception {
        RedcouponGrantReqDTO redcouponGrantReqDTO = new RedcouponGrantReqDTO();
        redcouponGrantReqDTO.setOrderChannelTypeDesc("全渠道使用");
        iAccountRedcouponService.grantRedcoupon(redcouponGrantReqDTO);
    }

    @Test
    public void testCreateBountyGrantTask() throws Exception {

    }

    @Test
    public void testCreateVouchersGrantTask() throws Exception {

    }

    @Test
    public void testCreateGrantTaskByVouchersTemplet() throws Exception {

    }

    @Test
    public void testConsumer() throws Exception {

    }

    @Test
    public void testRPC() throws Exception {



    }


}
