package com.fenbeitong.fenbeipay.rpc.service.personaccount;

import com.fenbeitong.fenbeipay.api.model.dto.personaccount.FbbActivityGrantDTO;
import com.fenbeitong.fenbeipay.api.service.personaccount.IPersonAccountService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-05-30 4:54 下午
 */

public class PersonAccountServiceTest extends SpringBaseHttpTest {

    @Autowired
    IPersonAccountService personAccountService;

    @Test
    public void grantActivityFbbToPerson() {
        FbbActivityGrantDTO fbbActivityGrantDTO = new FbbActivityGrantDTO();
        fbbActivityGrantDTO.setOrderId("aaaaaaa");
        fbbActivityGrantDTO.setEmployeeId("598ad66823445f722256c7a3");
        fbbActivityGrantDTO.setActivityNo("wcjtest");
        fbbActivityGrantDTO.setBusinessType(5);
        fbbActivityGrantDTO.setGrantAmount(new BigDecimal("0.01"));
        fbbActivityGrantDTO.setShowAlert(true);
        fbbActivityGrantDTO.setCompanyId("aaaaaaaa");
        personAccountService.grantActivityFbbToPerson(fbbActivityGrantDTO);
    }
}
