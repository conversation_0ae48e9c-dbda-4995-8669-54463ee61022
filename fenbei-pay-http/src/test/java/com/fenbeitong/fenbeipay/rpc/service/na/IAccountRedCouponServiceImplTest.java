package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponType;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountGeneralCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponOperationReqDTO;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

public class IAccountRedCouponServiceImplTest extends SpringBaseHttpTest {


    @Autowired
    private IAccountRedcouponService iAccountRedcouponService;


    @Test
    public void testCashierRefund() {
        RedcouponOperationReqDTO operationReqDTO = RedcouponOperationReqDTO.builder()
                .redcouponType(RedcouponType.REDCOUPON.getKey())
                .companyId("5b91427b23445f1bccbaf2f7")
                .operationChannelType(OperationChannelType.NORMAL.getKey())
                .operationAmount(BigDecimal.valueOf(1200))
                .bizNo("OML201912211738181291163")
                .orderType(20)
                .operationUserId("5d70ba9b23445f04bc031156")
                .operationUserName("强仔")
                .operationUserPhone("***********")
                .operationDescription("单元测试退款并回收")
                .build();
        iAccountRedcouponService.cashierRefund(operationReqDTO);
    }

}
