package com.fenbeitong.fenbeipay.rpc.plateAcct;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctFlowSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.BankAcctFlowSearchRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.BankAcctSearchRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IBankAcctService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.rpc.service.base.IBaseAcctKafkaServiceImpl;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * CashierOrderSettlementServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Dec 8, 2018</pre>
 */
public class IBankAcctServiceImplTest extends SpringBaseHttpTest {



    @Autowired
    private IBankAcctService iBankAcctService;
    @Autowired
    private IBaseAcctKafkaServiceImpl iBaseAcctKafkaService;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    @Test
    public void testQueryBankAcctListByPage() throws Exception {
        BankAcctSearchReqDTO bankAcctSearchReqDTO = new BankAcctSearchReqDTO();
        bankAcctSearchReqDTO.setPageSize(10);
        bankAcctSearchReqDTO.setPageNo(1);
        bankAcctSearchReqDTO.setBankAcctId("0356000003480165697783852");
        bankAcctSearchReqDTO.setBankAcctType(1);
        bankAcctSearchReqDTO.setBankName("ZBBANK");
        bankAcctSearchReqDTO.setCompanyMainId("CMA202104231733005023212");

        System.out.println(JSONObject.toJSONString(bankAcctSearchReqDTO));
        ResponsePage<BankAcctSearchRespDTO> bankAcctSearchRespDTOResponsePage = iBankAcctService.queryBankAcctListByPage(bankAcctSearchReqDTO);
        System.out.println(JSONObject.toJSONString(bankAcctSearchRespDTOResponsePage));
    }

    @Test
    public void testQueryBankAcctFlowListByPage() throws Exception {
        BankAcctFlowSearchReqDTO bankAcctFlowSearchReqDTO = new BankAcctFlowSearchReqDTO();
        bankAcctFlowSearchReqDTO.setPageSize(10);
        bankAcctFlowSearchReqDTO.setPageNo(1);
        System.out.println(JSONObject.toJSONString(bankAcctFlowSearchReqDTO));
        ResponsePage<BankAcctFlowSearchRespDTO> bankAcctFlowSearchRespDTOResponsePage = iBankAcctService.queryBankAcctFlowListByPage(bankAcctFlowSearchReqDTO);
        System.out.println(JSONObject.toJSONString(bankAcctFlowSearchRespDTOResponsePage));

    }

    @Test
    public void tsetKafka(){
        String record ="{\"accountFlowId\":\"AFF60c9d428020bfc53401709d066\",\"bankName\":\"ZBBANK\",\"operationAmount\":1,\"txnId\":\"AFF60c9d428020bfc53401709d066\",\"payAccountNo\":\"0356000003480165697783852\",\"receiveAccountNo\":\"0356000003747018535536683\",\"companyId\":\"605aa96a27f65f27af41f314\",\"accountSubType\":3,\"accountModel\":2,\"txnSt\":\"succeeded\",\"accountNo\":\"0356000003480165697783852\",\"bankTransNo\":\"3504228473540972593\"}";
//        iBaseAcctKafkaService.bankResultNotic2(record);
    }


} 
