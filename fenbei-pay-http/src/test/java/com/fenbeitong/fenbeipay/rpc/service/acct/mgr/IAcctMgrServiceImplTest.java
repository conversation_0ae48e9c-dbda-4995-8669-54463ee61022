package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.CompanyAccountInfoDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByMIdBankReqDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.awplus.utils.JsonUtil;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyPlatformAccountConvertDTO;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyPlatformAccountOperateDTO;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.RandomUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

import static com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType.CREDIT;
import static com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType.RECHARGE;
import static org.junit.Assert.assertNotNull;

public class IAcctMgrServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private IAcctMgrService iAcctMgrService;

    @Autowired
    protected ICompanyService iCompanyService;

    @Autowired
    protected UAcctCommonService uAcctCommonService;

    @Test
    public void  queryAcctOverview(){
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO();
        reqDTO.setCompanyId("6062c7af27f65fe4a10160c0");
        AcctOverviewRespDTO acctOverviewRespDTO = iAcctMgrService.queryAcctOverview(reqDTO);
        System.out.println(JsonUtils.toJson(acctOverviewRespDTO));
    }

/*

    @Test
    public void createDefaultFbtAccount_授信() {
        List<AcctCreateSubReqDTO> subList = Lists.newArrayList();
        AcctCreateSubReqDTO subBusCrReqDTO = AcctCreateSubReqDTO.builder()
                .accountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey())
                .accountModel(FundAccountModelType.CREDIT.getKey())
                .activeStatus(FundAcctActStatusEnum.ACTIVATE.getStatus())
                .build();
        subList.add(subBusCrReqDTO);
        AcctCreateSubReqDTO subIndCrReqDTO = AcctCreateSubReqDTO.builder()
                .accountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey())
                .accountModel(FundAccountModelType.CREDIT.getKey())
                .activeStatus(FundAcctActStatusEnum.ACTIVATE.getStatus())
                .build();
        subList.add(subIndCrReqDTO);

        String companyId = RandomUtils.bsonId();
        AcctCreateFbtReqDTO createFbtReqDTO = AcctCreateFbtReqDTO.builder()
                .companyId(companyId)
                .companyName("北京乾隆科技有限公司")
                .companyModel(FundAccountModelType.CREDIT.getKey())
                .companyMainId(companyId)
                .companyMainType(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey())
                .bankAccountNo(companyId)
                .bankName(BankNameEnum.FBT.getCode())
                .operationUserId(RandomUtils.bsonId())
                .operationUserName("乾隆爷")
                .initCredit(BigDecimal.ZERO)
                .operationDescription("搞个公司玩玩")
                .remark("北京乾隆科技有限公司进件搞个公司玩玩")
                .subList(subList).build();
        iAcctMgrService.createDefaultFbtAccount(createFbtReqDTO);
    }
*/




    @Test
    public void createDefaultFbtAccount_充值() {
        List<AcctCreateSubReqDTO> subList = Lists.newArrayList();
        AcctCreateSubReqDTO subBusCrReqDTO = AcctCreateSubReqDTO.builder()
                .accountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey())
                .accountModel(FundAccountModelType.RECHARGE.getKey())
                .activeStatus(FundAcctActStatusEnum.ACTIVATE.getStatus())
                .build();
        subList.add(subBusCrReqDTO);
        AcctCreateSubReqDTO subIndCrReqDTO = AcctCreateSubReqDTO.builder()
                .accountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey())
                .accountModel(FundAccountModelType.RECHARGE.getKey())
                .activeStatus(FundAcctActStatusEnum.ACTIVATE.getStatus())
                .build();
        subList.add(subIndCrReqDTO);

        String companyId = RandomUtils.bsonId();
        AcctCreateFbtReqDTO createFbtReqDTO = AcctCreateFbtReqDTO.builder()
                .companyId(companyId)
                .companyName("北京康熙科技有限公司_充值_单测")
                .companyModel(FundAccountModelType.RECHARGE.getKey())
                .companyMainId(companyId)
                .companyMainType(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey())
                .bankAccountNo(companyId)
                .bankName(BankNameEnum.FBT.getCode())
                .operationUserId(RandomUtils.bsonId())
                .operationUserName("康熙爷")
                .operationDescription("搞个公司玩玩")
                .initCredit(BigDecimal.ZERO)
                .remark("北京康熙科技有限公司_充值_单测进件搞个公司玩玩")
                .subList(subList).build();
        iAcctMgrService.createDefaultFbtAccount(createFbtReqDTO);
    }


    @Test
    public void queryMainBaseInfo(){
        AcctComGwByMIdBankReqDTO reqDTO = new AcctComGwByMIdBankReqDTO();
        reqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        reqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");
        reqDTO.setBankName(BankNameEnum.ZBBANK.getCode());
        reqDTO.setCompanyMainId("5747fbc10f0e60e0709d8d7d");
        String json = "{\"companyId\":\"6062c7af27f65fe4a10160c0\",\"bankName\":\"ZBBANK\",\"bankAccountNo\":\"6203798257917626385\",\"companyMainId\":\"CMA202104021012005387904\"}";
        AcctComGwByMIdBankReqDTO acctComGwByMIdBankReqDTO = JSON.parseObject(json, AcctComGwByMIdBankReqDTO.class);
        AcctCompanyMainDetailRespDTO respDTO = iAcctMgrService.queryMainAndAcct(acctComGwByMIdBankReqDTO);
        System.out.println("Down");
    }

    @Test
    public void createBankAccount() {
        AcctCreateBankReqDTO createBankReqDTO = new AcctCreateBankReqDTO();
        String json = "{\"acctCreateMainReqDTO\":{\"accountNo\":\"*************\",\"agentCardExpiresBegin\":*************,\"agentCardExpiresEnd\":*************,\"agentCardExpiresType\":1,\"agentCardNo\":\"130825199608120029\",\"agentCardOtherUrl\":\"https://image.fenbeitong.com/idcard/2021/07/14/589ac51d5f281a23bf5c00d8/60ee9ff691b491e226655da1.jpg\",\"agentCardOtherUrlHupo\":\"ff80808179d1a53b017aa965da7d2cfe UFT202107151701019776186\",\"agentCardPositiveUrl\":\"https://image.fenbeitong.com/idcard/2021/07/14/589ac51d5f281a23bf5c00d8/60ee9ff291b491e226655da0.jpg\",\"agentCardPositiveUrlHupo\":\"ff80808179d1a53b017aa965dace2cff UFT202107151701019878842\",\"agentEmail\":\"<EMAIL>\",\"agentName\":\"徐蕊\",\"agentPhone\":\"***********\",\"bankBatchName\":\"中国银行股份有限公司盐池支行\",\"bankBrnNo\":\"************\",\"bankCardName\":\"中国银行\",\"bankCardNo\":\"****************\",\"bankCity\":\"北京市\",\"bankCode\":\"************\",\"bankProvince\":\"北京\",\"businessLicenseCode\":\"91110105MA017Q5696\",\"businessLicenseUrl\":\"https://image.fenbeitong.com/license/2021/07/14/589ac51d5f281a23bf5c00d8/60ee9f9a91b491e226655d9a.jpg\",\"businessLicenseUrlHupo\":\"ff80808179d1a53b017aa965da782cfd UFT202107151701019628065\",\"businessName\":\"前置测试企业02\",\"businessTermBegin\":*************,\"businessTermType\":2,\"companyAccountId\":\"BOT202107151701018735155\",\"companyId\":\"589ac51d5f281a23bf5c00d8\",\"companyMainType\":1,\"companyName\":\"前置策四企业02\",\"legalPersonCardExpiresBegin\":*************,\"legalPersonCardExpiresEnd\":*************,\"legalPersonCardExpiresType\":1,\"legalPersonCardOtherUrl\":\"https://image.fenbeitong.com/idcard/2021/07/14/589ac51d5f281a23bf5c00d8/60ee9fd391b491e226655d9e.jpg\",\"legalPersonCardOtherUrlHupo\":\"ff80808179d1a53b017aa965db782d01 UFT202107151701022738950\",\"legalPersonCardPositiveUrl\":\"https://image.fenbeitong.com/idcard/2021/07/14/589ac51d5f281a23bf5c00d8/60ee9fcf91b491e226655d9d.jpg\",\"legalPersonCardPositiveUrlHupo\":\"ff80808179d1a53b017aa965dad22d00 UFT202107151701019800526\",\"legalPersonCardno\":\"130825199608120029\",\"legalPersonName\":\"徐蕊\",\"registeredAddress\":\"北京市测试地址\",\"shortCompanyName\":\"前02\"},\"bankAccountNo\":\"0356000004392385243648043\",\"bankAcctId\":\"0356000004392385243648043\",\"bankName\":\"ZBBANK\",\"companyId\":\"589ac51d5f281a23bf5c00d8\",\"companyMainType\":1,\"companyModel\":2,\"companyName\":\"前置策四企业02\",\"initCredit\":0,\"operationUserId\":\"589ac51d5f281a23bf5c00d9\",\"operationUserName\":\"123\"}";
        AcctCreateBankReqDTO acctCreateBankReqDTO = JSON.parseObject(json, AcctCreateBankReqDTO.class);
        AcctCreateFbtRespDTO bankAccount = iAcctMgrService.createBankAccount(acctCreateBankReqDTO);
        System.out.println("Down");
    }

    @Test
    public void createBankAccountAuth() {
//        List<CompanyPlatformAccountListDTO> comPlatAccListDe = iCompanyService.queryCompanyPlatformAccount("6066870227f65f7759f21a70", FundPlatformEnum.ZBANK.getKey(), RECHARGE.getKey());
//        List<CompanyPlatformAccountListDTO> comPlatAccListCr = iCompanyService.queryCompanyPlatformAccount("6066870227f65f7759f21a70",  FundPlatformEnum.ZBANK.getKey(), CREDIT.getKey());

        CompanyPlatformAccountOperateDTO rechargeDTO = new CompanyPlatformAccountOperateDTO();
        rechargeDTO.setAccountHolder(2);
        rechargeDTO.setCompanyId("6066870227f65f7759f21a70");
        rechargeDTO.setAccountType(RECHARGE.getKey());
        rechargeDTO.setPlatformSide(FundPlatformEnum.ZBANK.getKey());
        CompanyPlatformAccountConvertDTO comPlatAccListDe = iCompanyService.detailCompanyPlatformAccountForTrans(rechargeDTO);
        rechargeDTO.setAccountType(CREDIT.getKey());
        CompanyPlatformAccountConvertDTO comPlatAccListCr = iCompanyService.detailCompanyPlatformAccountForTrans(rechargeDTO);
        //新账户体系 商务账户 个人账户 都创建充值 授信
        System.out.println("Down");
    }

    @Test
    public void enableSubAccount() {
    }

    @Test
    public void disableSubAccount() {
    }

    @Test
    public void activateAcctSub() {
    }

    @Test
    public void showAccountSub() {
    }

    @Test
    public void isActivateAcctSubByAcctId() {
    }

    @Test
    public void changeCompanyName() {
    }

    /**
     * 丝滑升级 调用uc 开启账户管理权限
     */
    @Test
    public void createCompanyPlatformAccountConfig(){
        String companyId = "609892a727f65ff717f0c62c";
        iCompanyService.createCompanyPlatformAccountConfig(companyId);
    }

    @Test
    public void testUpgrade(){
        String companyId = "609892a727f65ff717f0c62c";
        AcctCompanyUpgradeReqDTO acctCompanyUpgradeReqDTO = new AcctCompanyUpgradeReqDTO();
        acctCompanyUpgradeReqDTO.setCompanyId("5b3266f627986323662628eb");
        iAcctMgrService.upgradeAcctCompany(acctCompanyUpgradeReqDTO);
        System.out.println("Down:");
    }


    @Test
    public void testUpgradeDefaultAuthConfig(){
        String companyId = "609892a727f65ff717f0c62c";
        AcctUpgradeDefaultAuthConfigReqDTO acctCompanyUpgradeReqDTO = new AcctUpgradeDefaultAuthConfigReqDTO();
        acctCompanyUpgradeReqDTO.setCompanyId("5ca46fc623445f5447ff74e8");
        AcctEffectiveAccountDTO acctEffectiveAccountDTO = iAcctMgrService.upgradeDefaultAuthConfig(acctCompanyUpgradeReqDTO);

        System.out.println("Down:"+ JsonUtil.toJson(acctEffectiveAccountDTO));
    }

    @Test
    public void testQueryGroupAcctAuth() {
    	List<CompanyAccountInfoDTO> list = iAcctMgrService.queryGroupAcctAuth(GroupAcctReqDTO.builder()
    			.companyId("5d1b1d2f23445f4dca76304b")
    			.groupId("62c7ffe42f5a5b47d1b78a75")
    			.employeeId("6232cf51aff1d82cc8cce6f5")
    			.menuCode("group_account_overview")
    			.build());
    	assertNotNull(list);
    }
    
    @Test
    public void testQueryAppAcctOverview() {
    	AppOverviewRequestDTO req = AppOverviewRequestDTO.builder()
  			  .companyId("5747fbc10f0e60e0709d8d7d")
  			  .bankAcctNo("5747fbc10f0e60e0709d8d7d")
  			  .build();
  	  AcctDebitMainDetailDTO detail = uAcctCommonService.queryAppAcctOverviewDetail(req);
  	  assertNotNull(detail);
    }

    @Test
    public void  queryAcctOverviewTest(){
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO();
        reqDTO.setCompanyId("5b91427b23445f1bccbaf2f7");
        AcctOverviewRespDTO acctOverviewRespDTO = uAcctCommonService.queryWebAcctOverview(reqDTO);
        System.out.println("Down");
    }
}
