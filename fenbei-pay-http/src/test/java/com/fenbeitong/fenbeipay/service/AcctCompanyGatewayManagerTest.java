package com.fenbeitong.fenbeipay.service;

import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyGatewayManager;
import com.fenbeitong.fenbeipay.awplus.utils.JsonUtil;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.gw.AcctCompanyGateway;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description AcctCompanyGatewayManagerTest
 * @date 2022-04-26 2:25 下午
 */

@Slf4j
public class AcctCompanyGatewayManagerTest extends SpringBaseHttpTest {

    @Autowired
    AcctCompanyGatewayManager acctCompanyGatewayManager;

    @Test
    public void findActGwByComIdAndAcctType() {
        AcctCompanyGateway actGwByComIdAndAcctType = acctCompanyGatewayManager.findActGwByComIdAndAcctType(null, 2);
        log.info("resp:{}", JsonUtil.toJson(actGwByComIdAndAcctType));
    }
}
