package com.fenbeitong.fenbeipay.service;

import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditService;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubFlowFindReqRPCDTO;
import com.fenbeitong.fenbeipay.awplus.utils.JsonUtil;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.datasource.CurrentDataSource;
import com.fenbeitong.fenbeipay.core.datasource.DataSourceNames;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessCredit;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessCreditFlow;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description AcctBusinessCreditServiceTest
 * @date 2022-04-24 3:14 下午
 */

@Slf4j
public class AcctBusinessCreditServiceTest extends SpringBaseHttpTest {

    @Autowired
    AcctBusinessCreditService acctBusinessCreditService;

    @Autowired
    AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Test
    public void queryHaveFlowByPageTest() {
        List<AcctBusinessCredit> acctBusinessCredits = acctBusinessCreditService.queryHaveFlowByPage(0, 10);
        log.info("resp:{}", JsonUtil.toJson(acctBusinessCredits));
    }

    @Test
    public void queryAvailableAccountCountHaveFlowTest() {
        Integer integer = acctBusinessCreditService.queryAvailableAccountCountHaveFlow();
        log.info("resp:{}", integer);
    }

    @Test
    public void dataSourceTest() {
        List<AcctBusinessCreditFlow> list = acctBusinessCreditFlowService.queryAllFlowByTime("5747fbc10f0e60e0709d8d7d", DateUtil.getDateFromString("2022-05-30 00:35:35", "yyyy-MM-dd HH:mm:ss"), DateUtil.getDateFromString("2022-05-31 23:35:35", "yyyy-MM-dd HH:mm:ss"));
        System.out.println("=========" + JsonUtils.toJson(list));

    }
}
