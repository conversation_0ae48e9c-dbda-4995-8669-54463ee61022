package com.fenbeitong.fenbeipay.service;

import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierSearchBatchPageReqRPCVo;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-07-12 11:05 上午
 */
@Slf4j
public class CashierSearchOrderServiceTest extends SpringBaseHttpTest {

    @Resource
    CashierSearchOrderService cashierSearchOrderService;

    @Test
    public void searchMultipleBatchByPage(){
        CashierSearchBatchPageReqRPCVo cashierSearchBatchPageReqRPCVo = new CashierSearchBatchPageReqRPCVo();
        cashierSearchBatchPageReqRPCVo.setCategoryTypes(Lists.newArrayList(11));
        cashierSearchBatchPageReqRPCVo.setStartTime(DateUtils.parseDate("2022-05-01"));
        cashierSearchBatchPageReqRPCVo.setEndTime(DateUtils.parseDate("2022-07-01"));
        cashierSearchBatchPageReqRPCVo.setPageNo(1);
        cashierSearchBatchPageReqRPCVo.setPageSize(500);
        cashierSearchOrderService.searchMultipleBatchByPage(cashierSearchBatchPageReqRPCVo);
    }
}
