package com.fenbeitong.fenbeipay.check;

import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctIndividualCreditService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.extract.AcctExtractDay;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcoupon;
import com.fenbeitong.fenbeipay.extract.manager.AcctCheckManagerService;
import com.fenbeitong.fenbeipay.extract.manager.AcctExtractDayManagerService;
import com.fenbeitong.fenbeipay.extract.vo.BankAcctExtractDayRespVO;
import com.fenbeitong.fenbeipay.redcoupon.manager.AccountRedcouponManager;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

import static org.easymock.EasyMock.*;


/**
 * <AUTHOR>
 * @date 2021/3/19
 */
public class AcctCheckManagerServiceTest extends SpringBaseHttpTest {
    @Autowired
    AcctExtractDayManagerService acctExtractDayManagerService;
    @Autowired
    AcctCheckManagerService acctCheckManagerService;
    @Autowired
    private AcctBusinessCreditService acctBusinessCreditService;
    @Autowired
    private AcctIndividualCreditService acctIndividualCreditService;
    @Autowired
    private AccountRedcouponManager accountRedcouponManager;

    @Before
    public void setUp()  {
        acctExtractDayManagerService = niceMock(AcctExtractDayManagerService.class);
    }
    @Test
    public void checkIfEmptyCompany() {
        int availableAccountCount = accountRedcouponManager.queryRedCouponAccountCountHaveFlow();
        List<AccountRedcoupon> acctBusinessCredits = accountRedcouponManager.queryHaveFlowByPage(1, 100);
        System.out.println(acctBusinessCredits);
    }

    @Test
    public void checkElse() {

        try {
            acctCheckManagerService.check(true,"2021-03-19",2,"ZBBANK");
        }catch (FinhubException e){
            Assert.assertEquals(53120103L,e.getCode().longValue());
            System.out.println("checkElse Exception");
        }
    }

    /**
     * 银行rpc接口返回null
     */
    @Test
    public void checkIfOneCompanyButBankDataIsNull() {
        // 将acctExtractDayManagerService设置为使用mock,从而跳过数据库
        ReflectionTestUtils.setField(acctCheckManagerService, "acctExtractDayManagerService", acctExtractDayManagerService);

        //1. record
        List<AcctExtractDay> acctExtractDays = Lists.newArrayList();
        acctExtractDays.add(acctExtractDayBuilderEqual());
        expect(acctExtractDayManagerService.queryAllCompanyAcctExtractDay("2021-03-19", BankNameEnum.FBT.getCode())).andReturn(acctExtractDays);
//        expect(iBankAcctExtractService.queryBankAcctExtractDay("1","2021-03-19","1","1")).andReturn(null);

        //2. replay
        replay(acctExtractDayManagerService);
//        replay(iBankAcctExtractService);
        try {
            acctCheckManagerService.check(true,"2021-03-19",1,"ZBBANK");
        }catch (FinhubException e){
            Assert.assertEquals(53120104L,e.getCode().longValue());
            System.out.println("checkIf Exception");
        }
    }

    /**
     * 对平
     */
//    SpringBaseHttpTest未继承事务，这的注解无效
    @Transactional  //该注解删掉没效果
    @Rollback(true)// 事务自动回滚，默认是true。可以不写
    @Test
    public void checkIfOneCompanyBalanceEqual() {
        // 将acctExtractDayManagerService设置为使用mock,从而跳过数据库
        ReflectionTestUtils.setField(acctCheckManagerService, "acctExtractDayManagerService", acctExtractDayManagerService);

        //1. record
        List<AcctExtractDay> acctExtractDays = Lists.newArrayList();
        acctExtractDays.add(acctExtractDayBuilderEqual());
        expect(acctExtractDayManagerService.queryAllCompanyAcctExtractDay("2021-03-19", BankNameEnum.FBT.getCode())).andReturn(acctExtractDays);

        //2. replay
        replay(acctExtractDayManagerService);
        acctCheckManagerService.check(true,"2021-03-19",1,"ZBBANK");
        System.out.println("checkIfOneCompanyBalanceEqual success");
    }

    /**
     * 不平
     */
    @Transactional  //该注解删掉没效果
    @Rollback(true)// 事务自动回滚，默认是true。可以不写
    @Test
    public void checkIfOneCompanyBalanceNotEqual() {
        // 将acctExtractDayManagerService设置为使用mock,从而跳过数据库
        ReflectionTestUtils.setField(acctCheckManagerService, "acctExtractDayManagerService", acctExtractDayManagerService);

        //1. record
        List<AcctExtractDay> acctExtractDays = Lists.newArrayList();
        acctExtractDays.add(acctExtractDayBuilderNotEqual());
        expect(acctExtractDayManagerService.queryAllCompanyAcctExtractDay("2021-03-19", BankNameEnum.FBT.getCode())).andReturn(acctExtractDays);
        BankAcctExtractDayRespVO bankAcctExtractDayRespDTO = bankAcctExtractDayRespDTOBuilder();

        //2. replay
        replay(acctExtractDayManagerService);
        acctCheckManagerService.check(true,"2021-03-19",1,"ZBBANK");
        System.out.println("checkIfOneCompanyBalanceNotEqual success");
    }

    @Test
    public void exception_53120102() {
        try{
            acctCheckManagerService.check(true,"2021-03-22",1,"ZBBANK");
        }catch (FinhubException e){
            Assert.assertEquals(53120102L,e.getCode().longValue());
        }
        System.out.println("checkIfOneCompanyBalanceNotEqual success");
    }
    @Test
    public void exception_53120103() {
        try{
            acctCheckManagerService.check(true,"2021-03-19",2,"ZBBANK");
        }catch (FinhubException e){
            Assert.assertEquals(53120103L,e.getCode().longValue());
        }
        System.out.println("checkIfOneCompanyBalanceNotEqual success");
    }
    @Test
    public void exception_53120104() {
        ReflectionTestUtils.setField(acctCheckManagerService, "acctExtractDayManagerService", acctExtractDayManagerService);

        //1. record
        List<AcctExtractDay> acctExtractDays = Lists.newArrayList();
        acctExtractDays.add(acctExtractDayBuilderNotEqual());
        expect(acctExtractDayManagerService.queryAllCompanyAcctExtractDay("2021-03-19", BankNameEnum.FBT.getCode())).andReturn(acctExtractDays);

        //2. replay
        replay(acctExtractDayManagerService);

        try{
            acctCheckManagerService.check(true,"2021-03-19",1,"ZBBANK");
        }catch (FinhubException e){
            Assert.assertEquals(53120104L,e.getCode().longValue());
        }
        System.out.println("checkIfOneCompanyBalanceNotEqual success");
    }
    @Test
    public void exception_53120105() {
         ReflectionTestUtils.setField(acctCheckManagerService, "acctExtractDayManagerService", acctExtractDayManagerService);

        //1. record
        List<AcctExtractDay> acctExtractDays = Lists.newArrayList();
        acctExtractDays.add(acctExtractDayBuilderNotEqual());
        expect(acctExtractDayManagerService.queryAllCompanyAcctExtractDay("2021-03-19", BankNameEnum.FBT.getCode())).andReturn(acctExtractDays);


        //2. replay
        replay(acctExtractDayManagerService);
        acctCheckManagerService.check(true,"2021-03-19",1,"ZBBANK");
        System.out.println("checkIfOneCompanyBalanceNotEqual success");
    }
    @Test
    public void exception_53120106() {
        try{
            acctCheckManagerService.check(true,"2021-03-19 00:00:00",1,"ZBBANK");
        }catch (FinhubException e){
            Assert.assertEquals(53120106L,e.getCode().longValue());
        }
        System.out.println("checkIfOneCompanyBalanceNotEqual success");
    }
    private AcctExtractDay acctExtractDayBuilderEqual(){
        AcctExtractDay acctExtractDay = new AcctExtractDay();
        acctExtractDay.setCompanyId("checkIfOneCompanyEqual");
        acctExtractDay.setBankAcctId("checkIfOneCompanyEqual");
        acctExtractDay.setCompanyMainId("checkIfOneCompanyEqual");
        acctExtractDay.setFinalBalance(new BigDecimal("1001"));
        acctExtractDay.setAccountGeneralId("checkIfOneCompanyEqual");
        acctExtractDay.setBankAcctId("checkIfOneCompanyEqual");
        acctExtractDay.setCompanyName("checkIfOneCompanyEqual");
        return acctExtractDay;
    }
    private AcctExtractDay acctExtractDayBuilderNotEqual(){
        AcctExtractDay acctExtractDay = new AcctExtractDay();
        acctExtractDay.setCompanyId("checkIfOneCompanyNotEqual");
        acctExtractDay.setBankAcctId("checkIfOneCompanyNotEqual");
        acctExtractDay.setCompanyMainId("checkIfOneCompanyNotEqual");
        acctExtractDay.setAccountGeneralId("checkIfOneCompanyNotEqual");
        acctExtractDay.setBankAcctId("checkIfOneCompanyNotEqual");
        acctExtractDay.setFinalBalance(new BigDecimal("1000"));
        acctExtractDay.setCompanyName("checkIfOneCompanyEqual");
        return acctExtractDay;
    }

    private BankAcctExtractDayRespVO bankAcctExtractDayRespDTOBuilder(){
        BankAcctExtractDayRespVO bankAcctExtractDayRespDTO = new BankAcctExtractDayRespVO();
        bankAcctExtractDayRespDTO.setBalance(new BigDecimal("1001"));
        bankAcctExtractDayRespDTO.setBankAcctId("checkIfOneCompany");
        bankAcctExtractDayRespDTO.setBankAccountNo("1");
        bankAcctExtractDayRespDTO.setBankName("ZBBANK");
        bankAcctExtractDayRespDTO.setCompanyId("checkIfOneCompany");
        bankAcctExtractDayRespDTO.setCompanyName("1");
        bankAcctExtractDayRespDTO.setExtractId("1");
        bankAcctExtractDayRespDTO.setExtractTime("2021-03-19");
        return bankAcctExtractDayRespDTO;
    }
}