package com.fenbeitong.fenbeipay.check;

import com.fenbeitong.fenbeipay.api.model.dto.extract.req.FlowReconWebBillCodesReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.FlowReconWebBillCodesRespDTO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.extract.service.impl.UFlowReconWebServiceHttpImpl;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class FlowReconWebTest extends SpringBaseHttpTest {

    @Autowired
    UFlowReconWebServiceHttpImpl UFlowReconWebServiceHttpImpl;

    @Test
    public void checkElse() {
        FlowReconWebBillCodesReqDTO flowReconWebBillCodesReqDTO = new FlowReconWebBillCodesReqDTO();
        flowReconWebBillCodesReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        List<FlowReconWebBillCodesRespDTO> billCodes = UFlowReconWebServiceHttpImpl.getBillCodes(flowReconWebBillCodesReqDTO);
        System.out.println(JsonUtils.toJson(billCodes));
    }

}