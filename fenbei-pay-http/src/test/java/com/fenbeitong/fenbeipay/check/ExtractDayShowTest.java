package com.fenbeitong.fenbeipay.check;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayManualRecordReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayShowPageReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayShowReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.ExtractDayShowPageDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.ExtractDayShowRespDTO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.extract.ExtractDayManualRecord;
import com.fenbeitong.fenbeipay.http.controller.extract.BankAcctExtractController;
import com.fenbeitong.fenbeipay.rpc.service.extract.IAcctExtractDayServiceImpl;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


public class ExtractDayShowTest extends SpringBaseHttpTest {
    @Autowired
    IAcctExtractDayServiceImpl iAcctExtractDayServiceImpl;

    @Test
    public void checkElse() {
        ExtractDayShowReqDTO extractDayShowReqDTO = new ExtractDayShowReqDTO();
        extractDayShowReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        extractDayShowReqDTO.setAccountSubType(5);
        extractDayShowReqDTO.setBankAccountNo("3110730027030000013");
        extractDayShowReqDTO.setStartTime("2022-03-28");
        extractDayShowReqDTO.setEndTime("2022-03-30");
        ExtractDayShowRespDTO query = iAcctExtractDayServiceImpl.query(extractDayShowReqDTO);
        FinhubLogger.info("返回结果是{}", JsonUtils.toJson(query));
    }

    @Test
    public void checkElse123() {
        ExtractDayShowReqDTO extractDayShowReqDTO = new ExtractDayShowReqDTO();
        extractDayShowReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        extractDayShowReqDTO.setAccountSubType(0);
        extractDayShowReqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");
        extractDayShowReqDTO.setStartTime("2022-03-14");
        extractDayShowReqDTO.setEndTime("2022-03-30");
        ExtractDayShowRespDTO query = iAcctExtractDayServiceImpl.query(extractDayShowReqDTO);
        FinhubLogger.info("返回结果是{}", JsonUtils.toJson(query));

        checkElse123Page();
    }

    @Test
    public void checkElse123Page() {
        ExtractDayShowPageReqDTO extractDayShowReqDTO = new ExtractDayShowPageReqDTO();
        extractDayShowReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        extractDayShowReqDTO.setAccountSubType(0);
        extractDayShowReqDTO.setBankAccountNo("5747fbc10f0e60e0709d8d7d");
        extractDayShowReqDTO.setStartTime("2022-03-14");
        extractDayShowReqDTO.setEndTime("2022-03-30");
        extractDayShowReqDTO.setPageNo(1);
        extractDayShowReqDTO.setPageSize(10);
        ResponsePage<ExtractDayShowPageDTO> extractDayShowPageDTOResponsePage = iAcctExtractDayServiceImpl.queryPage(extractDayShowReqDTO);
        FinhubLogger.info("返回结果是{}", JsonUtils.toJson(extractDayShowPageDTOResponsePage));
    }


    @Test
    public void getManualRecord() {
        ExtractDayManualRecordReqDTO extractDayShowReqDTO = new ExtractDayManualRecordReqDTO();
        extractDayShowReqDTO.setCompanyId("5747fbc10f0e60e0709d9999");

        ExtractDayManualRecord manualRecord = iAcctExtractDayServiceImpl.getManualRecord(extractDayShowReqDTO);
        FinhubLogger.info("返回结果是--第一次查询{}", JsonUtils.toJson(manualRecord));

        ExtractDayManualRecord extractDayManualRecord = iAcctExtractDayServiceImpl.addManualRecord(extractDayShowReqDTO);
        FinhubLogger.info("返回结果是{}", JsonUtils.toJson(extractDayManualRecord));


        ExtractDayManualRecord manualRecordSearch = iAcctExtractDayServiceImpl.getManualRecord(extractDayShowReqDTO);
        FinhubLogger.info("返回结果是--第二次查询{}", JsonUtils.toJson(manualRecordSearch));

    }

    @Autowired
    BankAcctExtractController BankAcctExtractController;

    @Test
    public void dayExecuteNew(){
        BankAcctExtractController.dayExecuteNew(null,null);
    }

    @Test
    public void creditDayExecuteNew(){
        BankAcctExtractController.creditDayExecuteNew(null,null);
    }


    @Test
    public void manualYesterday(){
        BankAcctExtractController.manualYesterday(null,null);
    }

    @Test
    public void dayExecuteManualHistory(){
        BankAcctExtractController.dayExecuteManualHistory(null,null);
    }

}
