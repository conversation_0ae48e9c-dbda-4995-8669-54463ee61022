package com.fenbeitong.fenbeipay.check;

import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayTaskReqDTO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.extract.ExtractDayTask;
import com.fenbeitong.fenbeipay.extract.manager.ExtractDayTaskManagerService;
import com.fenbeitong.finhub.common.utils.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class ExtractDayTaskTest extends SpringBaseHttpTest {
    @Autowired
    ExtractDayTaskManagerService extractDayTaskManagerService;

    @Test
    public void checkElse() {

        String currentDate = DateUtils.getCurrentDateStr(DateUtils.FORMAT_DATE_PATTERN);
        Integer taskType = new Integer(1); //1：有效日终余额跑批;2：白名单跑批

        ExtractDayTask task = extractDayTaskManagerService.getTask(currentDate, taskType);
        if(task != null){
            System.out.println("当天日终余额跑批已执行");
        }else {
            System.out.println("当天日终余额跑批--开始执行");
            extractDayTaskManagerService.startTask(currentDate,taskType);
        }

        Integer taskType2 = new Integer(2); //1：有效日终余额跑批;2：白名单跑批

        ExtractDayTask task2 = extractDayTaskManagerService.getTask(currentDate, taskType2);
        if(task2 != null){
            System.out.println("当天日终余额跑批已执行");
        }else {
            System.out.println("当天日终余额跑批--开始执行");
            extractDayTaskManagerService.startTask(currentDate,taskType2);
        }

        if(true){
            System.out.println("当天日终余额跑批--正常结束");
            extractDayTaskManagerService.endTask(currentDate, taskType);
        }

        if(true){
            System.out.println("当天日终余额跑批--异常结束");
            extractDayTaskManagerService.errorTask(currentDate, taskType2);
        }
    }


    @Test
    public void getList(){
        ExtractDayTaskReqDTO extractDayTaskReqDTO = new ExtractDayTaskReqDTO();
        List status = new ArrayList<Integer>();
        status.add(1);
        extractDayTaskReqDTO.setTaskStatus(status);
        List types = new ArrayList<Integer>();
        types.add(1);
        extractDayTaskReqDTO.setTaskTypes(types);
        extractDayTaskReqDTO.setStartDate("2022-03-28");
        extractDayTaskReqDTO.setEndDate("2022-03-28");
        extractDayTaskManagerService.queryExtractDayTaskByPage(extractDayTaskReqDTO);
    }

}
