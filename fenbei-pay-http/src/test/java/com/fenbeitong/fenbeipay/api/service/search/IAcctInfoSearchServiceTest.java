package com.fenbeitong.fenbeipay.api.service.search;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctInfoRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.ElectronicAccountRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankCardHoldersRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.saturn.VirtualCardCheckingQueryRpcDTO;
import com.fenbeitong.fenbeipay.api.service.acct.search.IAcctInfoSearchService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.api.service.saturn.IAutoAcctCheckingQueryService;
import com.fenbeitong.fenbeipay.awplus.utils.JsonUtil;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class IAcctInfoSearchServiceTest extends SpringBaseHttpTest {

    @Autowired
    private IAcctInfoSearchService iAcctInfoSearchService;

    @Autowired
    private IBankCardSearchService iBankCardSearchService;

    @Autowired
    private IAutoAcctCheckingQueryService iAutoAcctCheckingQueryService;


    @Test
    public void queryPageAcctAllFlow() {
        AcctInfoRespDTO acctInfoRespDTO = iAcctInfoSearchService.queryByBizNoAndTxnId("ASF202206251454254089328","");
        System.out.println("acctInfoRespDTO==========" + JsonUtil.toJson(acctInfoRespDTO));
    }

    @Test
    public void getCompanyIds(){
        List<String> virtualCardCompanyIds = iBankCardSearchService.getVirtualCardCompanyIds();
        System.out.println(virtualCardCompanyIds);
    }




    @Test
    public void queryAccountInfo(){
        ElectronicAccountRespDTO respDTO = iAcctInfoSearchService.queryAccountInfo("5747fbc10f0e60e0709d8d7d",null);
        System.out.println(JsonUtils.toJson(respDTO));
    }

    @Test
    public void queryVirtualCarInfoByBizNo(){
        VirtualCardCheckingQueryRpcDTO result = iAutoAcctCheckingQueryService.queryVirtualCarInfoByBizNo("100964");
        System.out.println("==========" + JsonUtils.toJson(result));
    }

    @Test
    public void getCardUsersByNameAndEmployeeIds(){
        System.out.println("========");
        BankCardHoldersRespDTO cardUsersByNameAndEmployeeIds =
                iBankCardSearchService.getCardUsersByNameAndEmployeeIds("5747fbc10f0e60e0709d8d7d", null,
                null, 2, 5, null);
        System.out.println(JSON.toJSON(cardUsersByNameAndEmployeeIds));
        System.out.println("========");
    }

}
