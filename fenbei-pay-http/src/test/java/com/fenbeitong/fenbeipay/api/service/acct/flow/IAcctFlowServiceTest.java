package com.fenbeitong.fenbeipay.api.service.acct.flow;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctFlowStereoPageServiceReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctFlowStereoPageServiceResqDTO;
import com.fenbeitong.fenbeipay.awplus.utils.JsonUtil;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.rpc.service.acct.flow.AcctAllFlowServiceImpl;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/1/13
 */
public class IAcctFlowServiceTest extends SpringBaseHttpTest {

    @Autowired
    private IAcctFlowService iAcctFlowService;

    @Autowired
    AcctAllFlowServiceImpl acctAllFlowServiceImpl;
//    @Autowired
//    private UAcctGeneralService uAcctGeneralService;

    @Test
    public void acctFlowSearchStereoPage() {
        String test = "{\n" +
                "    \"accountFlowId\": \"\",\n" +
                "    \"accountModel\": 2,\n" +
            "    \"accountSubType\": 7,\n" +
            // " \"bizNo\": \"aaaa\",\n" +
                "    \"companyId\": \"5747fbc10f0e60e0709d8d7d\",\n" +
            "    \"endTime\": \"2021-12-31 00:00:00\",\n"
            +
                "    \"fundPlatform\": \"FBT\",\n" +
                "    \"operationUserName\": \"\",\n" +
                "    \"pageNo\": 1,\n" +
            "    \"pageSize\": 100,\n" + "    \"startTime\": \"2021-01-01 00:00:00\"\n" +
                "}";
        AcctFlowStereoPageServiceReqDTO stereoPageServiceReqDTO = JsonUtils.toObj(test,AcctFlowStereoPageServiceReqDTO.class);
        ResponsePage<AcctFlowStereoPageServiceResqDTO> responsePage =  iAcctFlowService.acctFlowSearchStereoPage(stereoPageServiceReqDTO);
//        List<AcctFlowStereoPageServiceResqDTO> dataList = responsePage.getDataList();
//        List<AcctFlowForStereoRespDTO> respDTOList = dataList.stream().map(s->{
//            AcctFlowForStereoRespDTO respDTO = new AcctFlowForStereoRespDTO();
//            BeanUtils.copyProperties(s, respDTO);
//            //企业名称
//            List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(s.getCompanyId());
//            if (ObjUtils.isNotEmpty(accountGenerals)) {
//                respDTO.setCompanyName(accountGenerals.get(0).getCompanyName());
//            }
//            if(ObjUtils.isNotBlank(s.getFundPlatform())){
//                //众邦银行间连单独处理
//                if(Objects.equals(s.getFundPlatform(), BankCoreConstant.ZBBANKH_CODE)){
//                    respDTO.setFundPlatformName(BankCoreConstant.ZBBANKH_NAME);
//                }else{
//                    respDTO.setFundPlatformName(FundPlatformEnum.findPlatformByCode(s.getFundPlatform()).getName());
//                }
//            }
//            //业务类型
//            if(!Objects.isNull(s.getOperationTypeDesc())){
//                respDTO.setOperationTypeName(s.getOperationTypeDesc());
//            }
//            //账户类型
//            if(!Objects.isNull(s.getAccountSubType())){
//                respDTO.setAccountSubTypeName(FundAccountSubType.getEnum(s.getAccountSubType()).getValue());
//            }
//            //业务账户类型
//            if(null != s.getAccountModel()){
//                respDTO.setAccountModelName(FundAccountModelType.getEnum(s.getAccountModel()).getValue());
//            }
//            // 场景
//            if(!Objects.isNull(s.getOrderType())){
//                CategoryTypeEnum categoryTypeEnum = CategoryTypeEnum.valueOf(s.getOrderType());
//                respDTO.setOrderType(categoryTypeEnum == null ? "" : categoryTypeEnum.getName());
//            }
//            //操作金额
//            respDTO.setOperationAmount(BigDecimalUtils.fen2yuan(s.getOperationAmount()));
//            //余额
//            respDTO.setBalance(BigDecimalUtils.fen2yuan(s.getBalance()));
//            //操作时间
//            respDTO.setCreateTime(DateUtils.formatTime(s.getCreateTime()));
//            //需要有记账时间的才显示
//            if(!FundAcctSyncBankStatus.noSync(s.getSyncBankStatus())){
//                //银行记账时间
//                respDTO.setSyncBankTime(DateUtils.formatTime(s.getSyncBankTime()));
//            }
//            if(!s.getShowTargetAcct()){
//                respDTO.setTargetAccountName("");
//                respDTO.setTargetBankAllName("");
//            }
//            respDTO.setBillSummaryDesc(s.getBillSummaryDesc());
//            return respDTO;
//        }).collect(Collectors.toList());
    }

    @Test
    public void queryPageAcctAllFlow() {
        AcctFlowStereoPageServiceReqDTO acctFlowStereoPageServiceReqDTO = new AcctFlowStereoPageServiceReqDTO();
        acctFlowStereoPageServiceReqDTO.setPageNo(1);
        acctFlowStereoPageServiceReqDTO.setPageSize(10);
        acctFlowStereoPageServiceReqDTO.setMaxPageSize(500);
        acctFlowStereoPageServiceReqDTO.setOffset(0);
        acctFlowStereoPageServiceReqDTO.setAccountSubType(10);
        acctFlowStereoPageServiceReqDTO.setAccountModel(2);
        acctFlowStereoPageServiceReqDTO.setStartTime(DateUtils.parse("2022-07-01"));
        acctFlowStereoPageServiceReqDTO.setEndTime(DateUtils.parse("2022-09-01"));
        acctFlowStereoPageServiceReqDTO.setAccountFlowId("");
        acctFlowStereoPageServiceReqDTO.setCompanyId("");
        acctFlowStereoPageServiceReqDTO.setOperationUserName("");
        ResponsePage<AcctFlowStereoPageServiceResqDTO> acctFlowStereoPageServiceResqDTOResponsePage =
            acctAllFlowServiceImpl.queryPageAcctAllFlow(acctFlowStereoPageServiceReqDTO);
        logger.info(JsonUtil.toJson(acctFlowStereoPageServiceResqDTOResponsePage));
    }
}