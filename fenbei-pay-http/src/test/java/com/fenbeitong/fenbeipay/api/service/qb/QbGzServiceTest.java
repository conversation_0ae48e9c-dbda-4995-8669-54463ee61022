package com.fenbeitong.fenbeipay.api.service.qb;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.CashierPayCommonJsonDto;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.FbbFlowDto;
import com.fenbeitong.fenbeipay.api.model.dto.personaccount.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.personaccount.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponFlowPageReq;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponGrantRecordPageReq;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponStereoGrantReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.RedcouponAcctFlowDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.RedcouponGrantRecordDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.RedcouponGrantStereoResp;
import com.fenbeitong.fenbeipay.api.service.personaccount.IPersonAccountService;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponSearchService;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-11-16 下午4:21
 */

public class QbGzServiceTest extends SpringBaseHttpTest {

    @Autowired
    private IPersonAccountService iPersonAccountService;

    @Autowired
    private IAccountRedcouponService iAccountRedcouponService;

    @Autowired
    private IAccountRedcouponSearchService iAccountRedcouponSearchService;

    @Test
    public void  grantFbbStereoTest(){
        FbbGrantStereoReq fbbGrantStereoReq = new FbbGrantStereoReq();
        fbbGrantStereoReq.setEmployeeId("62a6ee08b901044d091b7431");
        fbbGrantStereoReq.setEmployeeName("aaaa");
        fbbGrantStereoReq.setEmployeePhone("adacacd");
        fbbGrantStereoReq.setGrantAmount(new BigDecimal(10));
        fbbGrantStereoReq.setCompanyId("fafaffdsfsfsa");
        fbbGrantStereoReq.setOperatorId("wcj");
        fbbGrantStereoReq.setOperatorName("wcj");
        CashierPayCommonJsonDto cashierPayCommonJsonDto = new CashierPayCommonJsonDto();
        FbbFlowDto fbbFlowDto = new FbbFlowDto();
        fbbFlowDto.setActivityTitle("wcj");
        fbbFlowDto.setDescription("wcdaafas");
        cashierPayCommonJsonDto.setFbbAct(fbbFlowDto);
        fbbGrantStereoReq.setCommonJson(cashierPayCommonJsonDto);
        fbbGrantStereoReq.setReason("3eqweq");
        fbbGrantStereoReq.setCustomerShow("fadfasfsa");
        fbbGrantStereoReq.setExpireTime(new Date());
        fbbGrantStereoReq.setShowAlert(false);
        fbbGrantStereoReq.setBizNo("fasfafsfdas1111");

        FbbGrantStereoResp fbbGrantStereoResp = iPersonAccountService.grantFbbStereo(fbbGrantStereoReq);
        FinhubLogger.info("resp{}", JsonUtils.toJson(fbbGrantStereoResp));
    }

    @Test
    public void recallFbbStereoTest(){
        FbbRecallStereoReq fbbRecallStereoReq= new FbbRecallStereoReq();
        fbbRecallStereoReq.setGrantFlowId("62a6ee08b901044d091b7431221116173802899163138");
        fbbRecallStereoReq.setRecallAmount(new BigDecimal("10"));
        fbbRecallStereoReq.setOperatorId("wcjdddd");
        fbbRecallStereoReq.setOperatorName("wcjdddd");
        fbbRecallStereoReq.setCommonJson(new CashierPayCommonJsonDto());
        fbbRecallStereoReq.setReason("wwwww");
        fbbRecallStereoReq.setBizNo("fasfasfsadff");
        FbbRecallStereoResp fbbRecallStereoResp = iPersonAccountService.recallFbbStereo(fbbRecallStereoReq);
        FinhubLogger.info("resp{}", JsonUtils.toJson(fbbRecallStereoResp));
    }


    @Test
    public void queryFbbGrantRecord(){
        FbbGrantRecordPageReq fbbGrantRecordPageReq = new FbbGrantRecordPageReq();
        fbbGrantRecordPageReq.setCompanyId("fafaffdsfsfsa");
        fbbGrantRecordPageReq.setEmployeeId("62a6ee08b901044d091b7431");
        ResponsePage<FbbGrantRecordDTO> fbbGrantRecordDTOResponsePage = iPersonAccountService.queryFbbGrantRecord(fbbGrantRecordPageReq);
        FinhubLogger.info("resp{}",JsonUtils.toJson(fbbGrantRecordDTOResponsePage));
    }
    
    @Test
    public void queryFbbFlowPage(){
        FbbFlowQueryPageReq fbbFlowQueryPageReq = new FbbFlowQueryPageReq();
        fbbFlowQueryPageReq.setEmployeeId("62a6ee08b901044d091b7431");
        ResponsePage<FbbAccountFlowDTO> fbbAccountFlowDTOResponsePage = iPersonAccountService.queryFbbFlowPage(fbbFlowQueryPageReq);
        FinhubLogger.info("resp{}",JsonUtils.toJson(fbbAccountFlowDTOResponsePage));
    }

    @Test
    public void queryFbbAcctFlowDetail(){
        FbbAcctFlowDetailDTO fbbAcctFlowDetailDTO = iPersonAccountService.queryFbbAcctFlowDetail("6386f097aa8fd467b974029f");
        FinhubLogger.info("resp{}", JsonUtils.toJson(fbbAcctFlowDetailDTO));
    }

    @Test
    public void grantRedcoupon(){
        RedcouponStereoGrantReqDTO redcouponGrantReqDTO = new RedcouponStereoGrantReqDTO();
        redcouponGrantReqDTO.setOperationAmount(BigDecimal.TEN);
        redcouponGrantReqDTO.setBizNo("aaaaaaa");
        redcouponGrantReqDTO.setRedcouponExpiryTime(new Date());
        redcouponGrantReqDTO.setUseScope(1);
        redcouponGrantReqDTO.setOrderChannelType("1");
        redcouponGrantReqDTO.setOrderChannelTypeDesc("aaaa");
        redcouponGrantReqDTO.setOrderChannelScope(1);
        redcouponGrantReqDTO.setRedcounponActName("aaaa");
        redcouponGrantReqDTO.setCustomerShow("aaaa");
        redcouponGrantReqDTO.setRedcouponType(1);
        redcouponGrantReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        redcouponGrantReqDTO.setOperationUserId("sys");
        redcouponGrantReqDTO.setOperationUserName("sys");
        redcouponGrantReqDTO.setOperationUserPhone("sys");
        redcouponGrantReqDTO.setOperationChannelType(1);
        redcouponGrantReqDTO.setOperationDescription("sys");
        redcouponGrantReqDTO.setCustomerServiceId("sys");
        redcouponGrantReqDTO.setCustomerServiceName("sys");
        redcouponGrantReqDTO.setRemark("sys");

        RedcouponGrantStereoResp redcouponGrantStereoResp = iAccountRedcouponService.grantRedcouponStereo(redcouponGrantReqDTO);
        FinhubLogger.info("resp{}", JsonUtils.toJson(redcouponGrantStereoResp));
    }

    @Test
    public void queryRedcouponRecordPage(){
        RedcouponGrantRecordPageReq redcouponGrantRecordPageReq = new RedcouponGrantRecordPageReq();
        redcouponGrantRecordPageReq.setCompanyId("5747fbc10f0e60e0709d8d7d");
        redcouponGrantRecordPageReq.setOrderChannelType("-9");
        ResponsePage<RedcouponGrantRecordDTO> redcouponGrantRecordDTOResponsePage = iAccountRedcouponSearchService.queryRedcouponRecordPage(redcouponGrantRecordPageReq);
        FinhubLogger.info("resp{}", JsonUtils.toJson(redcouponGrantRecordDTOResponsePage));
    }

    @Test
    public void queryRedcouponAcctFlowPage(){
        RedcouponFlowPageReq redcouponFlowPageReq = new RedcouponFlowPageReq();
        redcouponFlowPageReq.setCompanyId("5747fbc10f0e60e0709d8d7d");
        redcouponFlowPageReq.setConsumeType(2);
        ResponsePage<RedcouponAcctFlowDTO> redcouponAcctFlowDTOResponsePage = iAccountRedcouponSearchService.queryRedcouponAcctFlowPage(redcouponFlowPageReq);
        FinhubLogger.info("resp{}", JsonUtils.toJson(redcouponAcctFlowDTOResponsePage));
    }
}
