package com.fenbeitong.fenbeipay.api.service.na;

import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountSpaInnerTransferInRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountSpaInnerTransferRespDTO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/1/11
 */

public class IAccountSpaInnerServiceTest extends SpringBaseHttpTest {
    @Autowired
    private IAccountSpaInnerService iAccountSpaInnerService;
    @Test
    public void transferIn4Rollback() {
        AccountSpaInnerTransferInReqDTO accountSpaInnerReqDTO = new AccountSpaInnerTransferInReqDTO();
        accountSpaInnerReqDTO.setAccountInnerId("123");
        accountSpaInnerReqDTO.setOperationAmount(new BigDecimal("100"));
        accountSpaInnerReqDTO.setCompanyId("12345678910111213141516171819202122232425226");
        accountSpaInnerReqDTO.setOperationChannelType(1111);
        accountSpaInnerReqDTO.setBizNo("12345678910111213141516171819202122232425226");
        AccountSpaInnerTransferInRespDTO accountSpaInnerTransferInRespDTO = iAccountSpaInnerService.transferIn(accountSpaInnerReqDTO);
        Assert.assertEquals(Boolean.FALSE,accountSpaInnerTransferInRespDTO.getTransResult());
    }
    @Test
    public void transferInNotExist() {
        AccountSpaInnerTransferInReqDTO accountSpaInnerReqDTO = new AccountSpaInnerTransferInReqDTO();
        accountSpaInnerReqDTO.setAccountInnerId("1233");
        accountSpaInnerReqDTO.setOperationAmount(new BigDecimal("100"));
        accountSpaInnerReqDTO.setCompanyId("123");
        AccountSpaInnerTransferInRespDTO accountSpaInnerTransferInRespDTO = iAccountSpaInnerService.transferIn(accountSpaInnerReqDTO);
        Assert.assertEquals(Boolean.FALSE,accountSpaInnerTransferInRespDTO.getTransResult());
    }
    @Test
    public void transferIn() {
        AccountSpaInnerTransferInReqDTO accountSpaInnerReqDTO = new AccountSpaInnerTransferInReqDTO();
        accountSpaInnerReqDTO.setAccountInnerId("123");
        accountSpaInnerReqDTO.setOperationAmount(new BigDecimal("100"));
        accountSpaInnerReqDTO.setCompanyId("123");
        iAccountSpaInnerService.transferIn(accountSpaInnerReqDTO);
    }

    @Test
    public void transferOut4BalanceNotEnough() {
        AccountSpaInnerTransferOutReqDTO accountSpaInnerTransferOutReqDTO = new AccountSpaInnerTransferOutReqDTO();
        accountSpaInnerTransferOutReqDTO.setAccountInnerId("123");
        accountSpaInnerTransferOutReqDTO.setOperationAmount(new BigDecimal("122"));
        accountSpaInnerTransferOutReqDTO.setCompanyId("123");
        iAccountSpaInnerService.transferOut(accountSpaInnerTransferOutReqDTO);
    }
    @Test
    public void transferOut() {
        AccountSpaInnerTransferOutReqDTO accountSpaInnerTransferOutReqDTO = new AccountSpaInnerTransferOutReqDTO();
        accountSpaInnerTransferOutReqDTO.setAccountInnerId("123");
        accountSpaInnerTransferOutReqDTO.setOperationAmount(new BigDecimal("122"));
        accountSpaInnerTransferOutReqDTO.setCompanyId("123");
        iAccountSpaInnerService.transferOut(accountSpaInnerTransferOutReqDTO);
    }


    @Test
    public void transferInnerToInner(){
        AccountSpaInnerTransferReqDTO accountSpaInnerTransferReqDTO = new AccountSpaInnerTransferReqDTO();
        iAccountSpaInnerService.transferInnerToInner(accountSpaInnerTransferReqDTO);
    }

    @Test
    public void transferQuery(){
        AccountSpaInnerTransferQueryReqDTO accountSpaInnerTransferQueryReqDTO = new AccountSpaInnerTransferQueryReqDTO();
        iAccountSpaInnerService.transferQuery(accountSpaInnerTransferQueryReqDTO);
    }

    /**
     * AcctPublicConsumeReqRPCDTO(
     * 	companyId=618c80d6939dad1696dfaf50,
     * 	bizNo=OPP220112171204085862257,
     * 	bankAccountName=CITIC,
     * 	bankAccountNo=3110730024330000098,
     * 	operationAmount=452600,
     * 	sellerBankNo=1202022329900002018,
     * 	sellerBankName=中国工商银行杭州市艮山支行,
     * 	sellerBankAcctName=浙江顺丰速运有限公司,
     * 	sellerAcctBankCode=************,
     * 	sellerBankBrnNo=************,
     * 	orderType=128,
     * 	operationChannel=1,
     * 	operationDescription=null,
     * 	operationUserId=6194bd73c8f28302068dce34,
     * 	operationUserName=杨梦,
     * 	companyName=浙江开心果数智科技有限公司,
     * 	paymentPurpose=顺丰月结费用2021年12月份,
     * 	remark=null
     * )
     */
    @Test
    public void transferPublicToInner(){
        //标准有效请求
        AccountSpaPublicTransferReqDTO accountSpaPublicTransferReqDTO = new AccountSpaPublicTransferReqDTO();
        accountSpaPublicTransferReqDTO.setAccountPublicId("APB200527141604341224853");
        accountSpaPublicTransferReqDTO.setCompanyId("111");
        accountSpaPublicTransferReqDTO.setBizNo("OPP220112171204085862259");
        accountSpaPublicTransferReqDTO.setBankAccountNo("112");
        accountSpaPublicTransferReqDTO.setBankAccountName(BankNameEnum.getBankEnum("CITIC"));
        accountSpaPublicTransferReqDTO.setOperationAmount(new BigDecimal("452600"));
        accountSpaPublicTransferReqDTO.setSellerBankNo("1202022329900002018");
        accountSpaPublicTransferReqDTO.setSellerBankName("中国工商银行杭州市艮山支行");
        accountSpaPublicTransferReqDTO.setSellerBankAcctName("浙江顺丰速运有限公司");
        accountSpaPublicTransferReqDTO.setSellerAcctBankCode("************");
        accountSpaPublicTransferReqDTO.setSellerBankBrnNo("************");
        accountSpaPublicTransferReqDTO.setOrderType(128);
        accountSpaPublicTransferReqDTO.setOperationChannel(1);
        accountSpaPublicTransferReqDTO.setOperationDescription("111");
        accountSpaPublicTransferReqDTO.setOperationUserId("6194bd73c8f28302068dce34");
        accountSpaPublicTransferReqDTO.setOperationUserName("hl");
        accountSpaPublicTransferReqDTO.setCompanyName("平安内部户");
        accountSpaPublicTransferReqDTO.setPaymentPurpose("顺丰月结费用2021年12月份");

        AccountSpaInnerTransferInReqDTO accountSpaInnerTransferInReqDTO = new AccountSpaInnerTransferInReqDTO();
        accountSpaInnerTransferInReqDTO.setAccountInnerId("123");
        accountSpaInnerTransferInReqDTO.setOperationAmount(new BigDecimal("452600"));
        accountSpaInnerTransferInReqDTO.setCompanyId("123");
        AccountSpaPublicAndInnerTransferReqDTO accountSpaPublicAndInnerTransferReqDTO = new AccountSpaPublicAndInnerTransferReqDTO();
        accountSpaPublicAndInnerTransferReqDTO.setAccountSpaPublicTransferReqDTO(accountSpaPublicTransferReqDTO);
        accountSpaPublicAndInnerTransferReqDTO.setAccountSpaInnerTransferInReqDTO(accountSpaInnerTransferInReqDTO);
        AccountSpaInnerTransferRespDTO accountSpaInnerTransferRespDTO = iAccountSpaInnerService.transferFromPublicToInner(accountSpaPublicAndInnerTransferReqDTO);
//        Assert.assertEquals(Boolean.FALSE,accountSpaInnerTransferRespDTO.getTransResult());
    }

    @Test
    public void transferInnerToPublic(){
        //标准有效请求
        AccountSpaPublicTransferReqDTO accountSpaPublicTransferReqDTO = new AccountSpaPublicTransferReqDTO();
        accountSpaPublicTransferReqDTO.setAccountPublicId("APB200527141604341224853");
        accountSpaPublicTransferReqDTO.setCompanyId("111");
        accountSpaPublicTransferReqDTO.setBizNo("OPP220112171204085862257");
        accountSpaPublicTransferReqDTO.setBankAccountNo("112");
        accountSpaPublicTransferReqDTO.setBankAccountName(BankNameEnum.getBankEnum("CITIC"));
        accountSpaPublicTransferReqDTO.setOperationAmount(new BigDecimal("452600"));
        accountSpaPublicTransferReqDTO.setSellerBankNo("1202022329900002018");
        accountSpaPublicTransferReqDTO.setSellerBankName("中国工商银行杭州市艮山支行");
        accountSpaPublicTransferReqDTO.setSellerBankAcctName("浙江顺丰速运有限公司");
        accountSpaPublicTransferReqDTO.setSellerAcctBankCode("************");
        accountSpaPublicTransferReqDTO.setSellerBankBrnNo("************");
        accountSpaPublicTransferReqDTO.setOrderType(128);
        accountSpaPublicTransferReqDTO.setOperationChannel(1);
        accountSpaPublicTransferReqDTO.setOperationDescription("111");
        accountSpaPublicTransferReqDTO.setOperationUserId("6194bd73c8f28302068dce34");
        accountSpaPublicTransferReqDTO.setOperationUserName("hl");
        accountSpaPublicTransferReqDTO.setCompanyName("平安内部户");
        accountSpaPublicTransferReqDTO.setPaymentPurpose("顺丰月结费用2021年12月份");

        AccountSpaInnerTransferOutReqDTO accountSpaInnerTransferOutReqDTO = new AccountSpaInnerTransferOutReqDTO();
        accountSpaInnerTransferOutReqDTO.setAccountInnerId("123");
        accountSpaInnerTransferOutReqDTO.setOperationAmount(new BigDecimal("452600"));
        accountSpaInnerTransferOutReqDTO.setCompanyId("123");
        AccountSpaPublicAndInnerTransferReqDTO accountSpaPublicAndInnerTransferReqDTO = new AccountSpaPublicAndInnerTransferReqDTO();
        accountSpaPublicAndInnerTransferReqDTO.setAccountSpaPublicTransferReqDTO(accountSpaPublicTransferReqDTO);
        accountSpaPublicAndInnerTransferReqDTO.setAccountSpaInnerTransferOutReqDTO(accountSpaInnerTransferOutReqDTO);
        AccountSpaInnerTransferRespDTO accountSpaInnerTransferRespDTO = iAccountSpaInnerService.transferFromInnerToPublic(accountSpaPublicAndInnerTransferReqDTO);
    }
}