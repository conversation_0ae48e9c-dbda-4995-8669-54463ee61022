package com.fenbeitong.fenbeipay.api.service.cashier;

import com.fenbeitong.bank.ent.api.account.BankEntAccountInfoService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.BizPayChannel;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.BizPayType;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderType;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.CashierPayCommonJsonDto;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.*;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.enums.paycenter.AccountType;
import com.fenbeitong.finhub.common.constant.CashierTradeEnum;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ICashierOrderSettlementV2ServiceTest extends SpringBaseHttpTest {

    @Autowired
    ICashierOrderSettlementV2Service iCashierOrderSettlementV2Service;
    @Autowired
    BankEntAccountInfoService bankEntAccountInfoService;

    @Test
    public void create() {
    }

    @Test
    public void pay() {
    }

    @Test
    public void createAndPay() {
        CashierCreateAndPayReqVO cashierCreateAndPayReqVO = new CashierCreateAndPayReqVO();
        cashierCreateAndPayReqVO.setCompanyId("5d1b1d2f23445f4dca76304b");
        cashierCreateAndPayReqVO.setEmployeeId("T123");
        cashierCreateAndPayReqVO.setFbOrderId("T1234567892026");
        cashierCreateAndPayReqVO.setOrderType(OrderType.ACCT_PUBLIC_PAY.getKey());
        cashierCreateAndPayReqVO.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        cashierCreateAndPayReqVO.setAccountType(AccountType.Public_Type.getKey());
        cashierCreateAndPayReqVO.setBizPayChannel(BizPayChannel.BANK_ENT.name());
        String[] bizPayType = new String[1];
        bizPayType[0] = BizPayType.ACCTPUBLIC.name();
        cashierCreateAndPayReqVO.setBizPayType(bizPayType);
        cashierCreateAndPayReqVO.setTotalPayPrice(new BigDecimal("1"));
        cashierCreateAndPayReqVO.setAccountModel(AccountModelType.RECHARGE.getKey());
        cashierCreateAndPayReqVO.setFbOrderName("对公支付银企直联付款");
        cashierCreateAndPayReqVO.setCurrency("cny");
        String t = "{\"altman\":{\"message\":\"对公支付付款\",\"passagerList\":[{\"passagerName\":\"笑梅\",\"passagerPhone\":\"***********\"}]}}";
        CashierPayCommonJsonDto cashierPayCommonJsonDto = JsonUtils.toObj(t,CashierPayCommonJsonDto.class) ;
        cashierCreateAndPayReqVO.setCommonJson(cashierPayCommonJsonDto);
        CashierBillPayVO cashierBillPayVO = new CashierBillPayVO();
        cashierBillPayVO.setBankAccountNo("123");
        cashierBillPayVO.setReceiveBankAccountName("喵星人");
        cashierBillPayVO.setReceiveBankAccountNo("****************");
        cashierBillPayVO.setReceiveBankCode("************");
        cashierBillPayVO.setReceiveBankName("招商银行中关村支行");

        cashierBillPayVO.setNeedRisk(false);
        cashierCreateAndPayReqVO.setCashierBillPayVO(cashierBillPayVO);
        cashierBillPayVO.setAccountId("UFIDA202207111720258470003");
        iCashierOrderSettlementV2Service.createAndPay(cashierCreateAndPayReqVO);
    }

    @Test
    public void createAndPay4Salary() {
        CashierCreateAndPayReqVO cashierCreateAndPayReqVO = new CashierCreateAndPayReqVO();
        cashierCreateAndPayReqVO.setCompanyId("5d1b1d2f23445f4dca76304b");
        cashierCreateAndPayReqVO.setEmployeeId("T123");
        cashierCreateAndPayReqVO.setFbOrderId("T1234567896039");
        cashierCreateAndPayReqVO.setOrderType(OrderType.SALARY.getKey());
        cashierCreateAndPayReqVO.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        cashierCreateAndPayReqVO.setAccountType(AccountType.Public_Type.getKey());
        cashierCreateAndPayReqVO.setBizPayChannel(BizPayChannel.BANK_ENT.name());
        String[] bizPayType = new String[1];
        bizPayType[0] = BizPayType.SALARY.name();
        cashierCreateAndPayReqVO.setBizPayType(bizPayType);
        cashierCreateAndPayReqVO.setTotalPayPrice(new BigDecimal("1"));
        cashierCreateAndPayReqVO.setAccountModel(AccountModelType.RECHARGE.getKey());
        cashierCreateAndPayReqVO.setFbOrderName("对公支付银企直联付款");
        cashierCreateAndPayReqVO.setCurrency("cny");
        String t = "{\"altman\":{\"message\":\"对公支付付款\",\"passagerList\":[{\"passagerName\":\"笑梅\",\"passagerPhone\":\"***********\"}]}}";
        CashierPayCommonJsonDto cashierPayCommonJsonDto = JsonUtils.toObj(t,CashierPayCommonJsonDto.class) ;
        cashierCreateAndPayReqVO.setCommonJson(cashierPayCommonJsonDto);
        CashierBillPayVO cashierBillPayVO = new CashierBillPayVO();
        cashierBillPayVO.setBankAccountNo("123");
        cashierBillPayVO.setReceiveBankAccountName("喵星人");
        cashierBillPayVO.setReceiveBankAccountNo("****************");
        cashierBillPayVO.setReceiveBankCode("************");
        cashierBillPayVO.setReceiveBankName("招商银行成都分行");
        cashierBillPayVO.setAccountId("UFIDA202207111720258470003");
        cashierBillPayVO.setNeedRisk(false);
        CashierBatchPayVO cashierBatchPayVO = new CashierBatchPayVO();
        cashierBatchPayVO.setBizPayChannel(BizPayChannel.BANK_ENT.name());
        cashierBatchPayVO.setPayerAccountId("UFIDA202207111720258470003");
        cashierBatchPayVO.setPayerAccountName("test");
        cashierBatchPayVO.setPayerAccountType(1);
        cashierBatchPayVO.setPayerBankCode("111");
        cashierBatchPayVO.setPayerBankAccountNo("111");
        cashierBatchPayVO.setPayerUserId("11");
        cashierBatchPayVO.setPayerBankName("11");
        cashierBatchPayVO.setTotalAmount(new BigDecimal("20"));
        cashierBatchPayVO.setTotalNum(2);
        List<CashierBatchDetailPayVO> list = new ArrayList<>();
        CashierBatchDetailPayVO cashierBatchDetailPayVO = new CashierBatchDetailPayVO();
        cashierBatchDetailPayVO.setBatchDetailId("1");
        cashierBatchDetailPayVO.setBatchDetailPayAmount(new BigDecimal("10"));
        cashierBatchDetailPayVO.setPayerAccountType(1);
        cashierBatchDetailPayVO.setPayerAccountId("UFIDA202207111720258470003");
        cashierBatchDetailPayVO.setPayerAccountName("test");
        cashierBatchDetailPayVO.setPayerBankCode("111");
        cashierBatchDetailPayVO.setPayerBankAccountNo("111");
        cashierBatchDetailPayVO.setPayerUserId("111");
        cashierBatchDetailPayVO.setPayerBankName("11");
        cashierBatchDetailPayVO.setReceiverBankName("招商银行深圳分行");
        cashierBatchDetailPayVO.setReceiverBankAccountName("喵星人");
        cashierBatchDetailPayVO.setReceiverBranchBankCode("************");
        cashierBatchDetailPayVO.setReceiverEmployeeId("");
        cashierBatchDetailPayVO.setReceiverBankAccountNo("****************");
        cashierBatchDetailPayVO.setReceiverBranchBankName("招商银行成都分行");
        cashierBatchDetailPayVO.setReceiverEmployeeNo("1");
        cashierBatchDetailPayVO.setReceiverPhone("1");
        cashierBatchDetailPayVO.setReceiverEmployeeIdCard("1");
        cashierBatchDetailPayVO.setReceiverEmployeeIdCardType("01");
        list.add(cashierBatchDetailPayVO);
        CashierBatchDetailPayVO cashierBatchDetailPayVO1 = new CashierBatchDetailPayVO();
        cashierBatchDetailPayVO1.setBatchDetailId("2");
        cashierBatchDetailPayVO1.setBatchDetailPayAmount(new BigDecimal("10"));
        cashierBatchDetailPayVO1.setPayerAccountType(1);
        cashierBatchDetailPayVO1.setPayerAccountId("UFIDA202207111720258470003");
        cashierBatchDetailPayVO1.setPayerAccountName("test");
        cashierBatchDetailPayVO1.setPayerBankCode("111");
        cashierBatchDetailPayVO1.setPayerBankAccountNo("111");
        cashierBatchDetailPayVO1.setPayerUserId("111");
        cashierBatchDetailPayVO1.setPayerBankName("11");
        cashierBatchDetailPayVO1.setReceiverBankName("招商银行深圳分行");
        cashierBatchDetailPayVO1.setReceiverBankAccountName("喵星人");
        cashierBatchDetailPayVO1.setReceiverBranchBankCode("************");
        cashierBatchDetailPayVO1.setReceiverEmployeeId("");
        cashierBatchDetailPayVO1.setReceiverBankAccountNo("****************");
        cashierBatchDetailPayVO1.setReceiverBranchBankName("招商银行成都分行");
        cashierBatchDetailPayVO1.setReceiverEmployeeNo("1");
        cashierBatchDetailPayVO1.setReceiverPhone("1");
        cashierBatchDetailPayVO1.setReceiverEmployeeIdCard("2");
        cashierBatchDetailPayVO1.setReceiverEmployeeIdCardType("01");
        list.add(cashierBatchDetailPayVO1);
        cashierBatchPayVO.setCashierBatchDetailPayVOList(list);
        cashierCreateAndPayReqVO.setCashierBatchPayVO(cashierBatchPayVO);
        iCashierOrderSettlementV2Service.createAndPay(cashierCreateAndPayReqVO);
    }


    @Test
    public void queryByAsync(){
        iCashierOrderSettlementV2Service.queryByAsync("T1234567894039");
    }

    @Test
    public void queryByCron(){
        iCashierOrderSettlementV2Service.queryByCron();
    }
    @Test
    public void payByAsync(){
        iCashierOrderSettlementV2Service.payByAsync("T1234567894039");
    }

    @Test
    public void confirmPay() {
        CashierConfirmPayReqVO cashierPayReqVO = new CashierConfirmPayReqVO();
        cashierPayReqVO.setFbOrderId("T1234567891027");
        iCashierOrderSettlementV2Service.confirmPay(cashierPayReqVO);
    }

    @Test
    public void cancel() {
    }

    @Test
    public void query() {
        CashierQueryReqVO cashierQueryReqVO = new CashierQueryReqVO();
        cashierQueryReqVO.setFbOrderId("T1234567891022");
        CashierQueryRespVO cashierQueryRespVO = iCashierOrderSettlementV2Service.query(cashierQueryReqVO);
        System.out.println(JsonUtils.toJson(cashierQueryRespVO));
    }

    @Test
    public void dishonoured() {
    }

    @Test
    public void notifyRecord(){
        CashierNotifyReqVO cashierNotifyReqVO = new CashierNotifyReqVO();
        cashierNotifyReqVO.setBizNo("T1234567891022");
        cashierNotifyReqVO.setPayFailReason("测试");
        cashierNotifyReqVO.setPayStatus("succeeded");
        cashierNotifyReqVO.setSyncBankTransNo("111");
        cashierNotifyReqVO.setRiskRespCode("1");
        iCashierOrderSettlementV2Service.notify(cashierNotifyReqVO);
    }
}