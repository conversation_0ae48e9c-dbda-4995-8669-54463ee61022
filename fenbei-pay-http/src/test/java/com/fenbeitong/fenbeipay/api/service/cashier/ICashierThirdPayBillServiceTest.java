package com.fenbeitong.fenbeipay.api.service.cashier;

import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierThirdBillRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierThirdBillReqRPCVO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.utils.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

import static org.junit.Assert.*;

public class ICashierThirdPayBillServiceTest extends SpringBaseHttpTest {
    @Autowired
    ICashierThirdPayBillService iCashierThirdPayBillService;
    @Test
    public void queryBill() {
        CashierThirdBillReqRPCVO cashierThirdBillReqRPCVO = new CashierThirdBillReqRPCVO();
        cashierThirdBillReqRPCVO.setBillDate("2022-01-08");
        cashierThirdBillReqRPCVO.setAccount("test");
        cashierThirdBillReqRPCVO.setChannel("WECHAT");
        CashierThirdBillRespRPCDTO cashierThirdBillRespRPCDTO =  iCashierThirdPayBillService.queryBill(cashierThirdBillReqRPCVO);
        System.out.println(cashierThirdBillRespRPCDTO);
    }

    @Test
    public void initTask(){
        //从入参获取或者每日初始化前一天
        String billDate = DateUtils.format(DateUtils.addDay(new Date(),-1), DateUtils.FORMAT_DATE_PATTERN);
        iCashierThirdPayBillService.initTask(billDate);

    }
}