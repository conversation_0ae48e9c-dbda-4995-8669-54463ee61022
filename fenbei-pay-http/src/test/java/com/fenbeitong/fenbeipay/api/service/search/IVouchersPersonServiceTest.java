package com.fenbeitong.fenbeipay.api.service.search;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersFlowReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersOperationFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-02-03 下午2:33
 */

public class IVouchersPersonServiceTest extends SpringBaseHttpTest {

    @Autowired
    IVouchersPersonService iVouchersPersonService;

    @Test
    public void queryVoucherFlow(){
        VouchersFlowReqRPCDTO vouchersFlowReqRPCDTO = new VouchersFlowReqRPCDTO();
        ResponsePage<VouchersOperationFlowRespRPCDTO> vouchersOperationFlowRespRPCDTOResponsePage = iVouchersPersonService.queryVoucherFlow(vouchersFlowReqRPCDTO);
    }
}
