package com.fenbeitong.fenbeipay.api.service.cashier;

import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.AltmanDto;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.CashierPayCommonJsonDto;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.PassageDto;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierAcctPublicPayTradeRPCVo;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.utils.BankConfigUtil;
import com.fenbeitong.fenbeipay.core.utils.SpringUtils;
import com.fenbeitong.finhub.common.constant.BankNameEnum;

import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

import javax.annotation.Resource;

/**
 * @ClassName PublicPayOrderSettlementServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/12 2:19 下午
 * @Version 1.0
 **/
public class PublicPayOrderSettlementServiceTest extends SpringBaseHttpTest {

    @Resource
    private SpringUtils springUtils;

//    @Resource
//    private BankConfigUtil bankConfigUtil;


    @Resource(name = "iCashierOrderSettlementService")
    private ICashierOrderSettlementService cashierOrderSettlementService;

    @Test
    public void createAndPay() {

        CashierAcctPublicPayTradeRPCVo createPayTradePPCVo = new CashierAcctPublicPayTradeRPCVo();

        createPayTradePPCVo.setAmountBank(BigDecimal.valueOf(300L));
        createPayTradePPCVo.setBankAccountNo("3110730027033195484");
        createPayTradePPCVo.setBankName("CITIC");
        createPayTradePPCVo.setBusinessMode(2);

        CashierPayCommonJsonDto jsonDto = new CashierPayCommonJsonDto();
        AltmanDto altmanDto = new AltmanDto();
        altmanDto.setMessage("对公支付付款");
        PassageDto passageDto = new PassageDto();
        passageDto.setPassagerName("笑梅");
        passageDto.setPassagerPhone("***********");
        altmanDto.setPassagerList(Lists.newArrayList(passageDto));
        jsonDto.setAltman(altmanDto);
        createPayTradePPCVo.setCommonJson(jsonDto);
        createPayTradePPCVo.setCompanyId("5d1b1d2f23445f4dca76304b");
        createPayTradePPCVo.setCompanyPayPrice(BigDecimal.valueOf(300L));
        createPayTradePPCVo.setEmployeeId("621845d349430b0f22d81fd0");
        createPayTradePPCVo.setFbOrderId("PUBLIC"+System.currentTimeMillis());
        createPayTradePPCVo.setFbOrderName("对公支付付款");
        createPayTradePPCVo.setFbOrderSnapshot("对公支付付款");
        createPayTradePPCVo.setOperationChannelType(5);
        createPayTradePPCVo.setOrderType(128);
        createPayTradePPCVo.setPaymentPurpose("移动彩铃费用");
        createPayTradePPCVo.setSellerAcctBankCode("************");
        createPayTradePPCVo.setSellerBankAcctName("中国移动通信集团江苏有限公司无锡分公司1");
        createPayTradePPCVo.setSellerBankBrnNo("*************");
        createPayTradePPCVo.setSellerBankName("中国农业银行股份有限公司无锡永乐支行1");
        createPayTradePPCVo.setSellerBankNo("31107300270331954841");
        createPayTradePPCVo.setTotalPayPrice(BigDecimal.valueOf(300L));

        this.cashierOrderSettlementService.createAcctPublicPayTrade(createPayTradePPCVo);

    }

    @Test
    public void testBankConfigUtil() {

        System.out.println(BankConfigUtil.needCallBank(BankNameEnum.ZBBANK.getCode(), "1111"));
    }

}
