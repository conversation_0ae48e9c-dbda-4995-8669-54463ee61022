package com.fenbeitong.fenbeipay.kafka;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankCardFlowInPettyResDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.BankCardApplyFlowManger;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.utils.AutoAcctCheckingEventUtil;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherConstant;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.vouchers.unit.service.UVoucherRecoveryTaskService;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.dech.KafkaDechTradeResultMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;
import com.fenbeitong.finhub.kafka.msg.uc.KafkaEmployeeUpdateMsg;
import com.fenbeitong.usercenter.api.model.enums.employee.OperateType;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

public class TestServiceTest extends SpringBaseHttpTest {
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private UVoucherRecoveryTaskService uVoucherRecoveryTaskService;

    @Autowired
    protected IAcctFundMgrService iAcctFundMgrService;

    @Autowired
    private UAcctCommonService uAcctCommonService;

    @Autowired
    protected AutoAcctCheckingEventUtil autoAcctCheckingEventUtil;

    @Resource
    private BankCardApplyFlowManger bankCardApplyFlowManger;

    @Test
    public void testEmployeeLeave() throws Exception {
        KafkaEmployeeUpdateMsg iMessage = new KafkaEmployeeUpdateMsg();
        iMessage.setCompanyId("5747fbc10f0e60e0709d8d7d");
        iMessage.setEmployeeId("5aab96c051bba9669df9b04c");
        iMessage.setOperateStatus(OperateType.DELETE_OP.getKey());

        //员工离职，回收分贝券
        try {
            if (OperateType.DELETE_OP.getKey() != iMessage.getOperateStatus()) {
                return;
            }
            String companyId = iMessage.getCompanyId();
            String employeeId = iMessage.getEmployeeId();
            uVoucherRecoveryTaskService.createWithdrawalTaskByEmployeeId(companyId, employeeId, VoucherConstant.EMPLOYEE_DELETE_OPERATION, VoucherConstant.EMPLOYEE_DELETE_OPERATION);
            FinhubLogger.info("【kafka消费】员工离职回收分贝券消费完成 data:{}", iMessage.toString());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券，撤回员工可用分贝券异常】：{}", iMessage.toString(), e);
        }
    }

    @Test
    public void bankNotic() throws Exception {
        KafkaDechTradeResultMsg iMessage = JSON.parseObject("{\"accountFlowId\":\"ASF202107011404072760306\",\"bankName\":\"ZBBANK\",\"operationAmount\":38500.00,\"txnId\":\"ht881202107011404019446268\",\"payAccountNo\":\"0356000004261627051511849\",\"receiveAccountNo\":\"0356000003480165697783852\",\"companyId\":\"60d03874f001563818f46d0b\",\"accountSubType\":2,\"accountModel\":2,\"txnSt\":\"succeeded\",\"accountNo\":\"0356000004261627051511849\",\"sysOrdNo\":\"3504312337448243239\",\"syncBankTransNo\":\"***********************\"}", KafkaDechTradeResultMsg.class);
        uAcctCommonService.bankNotic(iMessage);
        System.out.println("Down");
    }


    @Test
    public void bankAutoAcctChecking() throws Exception {
        KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg =new KafkaAutoAcctCheckingMsg();
        kafkaAutoAcctCheckingMsg.setBankAcctId("1");
        kafkaAutoAcctCheckingMsg.setCompanyId("1");
        kafkaAutoAcctCheckingMsg.setCashierTxnId("1");
        kafkaAutoAcctCheckingMsg.setOperationType(5);
        kafkaAutoAcctCheckingMsg.setOperationAmount(new BigDecimal(100));
        kafkaAutoAcctCheckingMsg.setTargetBankName("ZBBANK");
        kafkaAutoAcctCheckingMsg.setBizNo("12334");
        kafkaAutoAcctCheckingMsg.setAccountId("12345");
        kafkaAutoAcctCheckingMsg.setBankTransNo("122345");
        kafkaAutoAcctCheckingMsg.setOperationAmount(new BigDecimal(100));
        // 设置创建时间 QX 2021-12-17
        kafkaAutoAcctCheckingMsg.setCreateTime(new Date());
        autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
        System.out.println("Down");
    }

    @Test
    public void bankCardFlowInPettyPage() {
        ResponsePage<BankCardFlowInPettyResDTO> list = bankCardApplyFlowManger.bankCardFlowInPettyPage(
                "624d81c83f2132343c1c8f8d", 1, 10);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void getGrantSum() {
        BigDecimal grantSum = bankCardApplyFlowManger.getGrantSum("62428ec41efe2057b1d02c70");
        System.out.println(JSON.toJSONString(grantSum));
    }
}