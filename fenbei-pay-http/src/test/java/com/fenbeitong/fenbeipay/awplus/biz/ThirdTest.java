package com.fenbeitong.fenbeipay.awplus.biz;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fenbeitong.fenbeipay.awplus.config.PayConfig;
import com.fenbeitong.fenbeipay.awplus.convert.PayRecordConvert;
import com.fenbeitong.fenbeipay.awplus.model.po.QueryPayRequestPo;
import com.fenbeitong.fenbeipay.awplus.model.wechat.WeChatQueryRequest;
import com.fenbeitong.fenbeipay.awplus.utils.WeChatSignUtil;
import com.fenbeitong.fenbeipay.awplus.utils.XMLUtil;
import com.fenbeitong.fenbeipay.cashier.pay.CashierOrderSettlementPayService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.utils.HttpclientUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ThirdTest extends SpringBaseHttpTest {

    @Autowired
    private CashierOrderSettlementPayService cashierOrderSettlementPayService;

    public static void main(String[] args) throws AlipayApiException {
        String url = "https://openapi.alipay.com/gateway.do";
        String appid = "2017092208870452";
        String key = "MIIEvgIBADANBgkqhkiG9w0BAQEEAASCBKgwggSkAgEAAoIBAQDQ5lF5IlDSXR/jddtEFgdViXSeCJnp8QeYGKoP3BrsRdgn67tRftaVT6wr3kt3wqHB2NtDHflFVajK7JH3yRoJ7HIIZVRoU3Enrp28MRy59NqL4N7spD4JX+DHJ/kkEPIhgDX//l4b/nYWisoLuOKRzg3hfVBFWiaZHy8G6kFjlCTLiO1iDx0bg+9ljhfIWXhlgUSqSD1pfR0oEVWC17wRI49090BhSetAOHf9RV7mqaP2MmeSkrOHVg0RYUGKyA8bE3ibK8zFC4H1x48VPIJ2rzQsBW9GxzhVj/psaICGMn6eYoPcV9xHORfOgTmYvWnkWdgdXVuEhf/UZURV3AMnAgMBAAECggEBAJEr/eKYnmISpV6FQOOIC4C+DXTltVCGWCiyuzxaUbtwrk7P21U/OBB7VcMGsJuBfrgf5AA4jEpKxQqAbNAtyRKsZnK/OHFny3MwPh3SY4w3RMmAdGsvmI0bPDIq7VDLFfmCCATm2K/LnnT77n1G9u0srSjO1UHc7IXszdmYYei4gVmvLwPbOBs29U0wOouEZx22tu9iBYcU8J99mOfWum+AZTyf9Cz6/yJj4XwrhgE+TM3segqVEIoUq/WmTADVbZoOWZdPU0hVmCx2uQYHtH0So6lKa55PT1A3g1SrJ4gXrNVJQDEEQH4hSFz/Gr98qVPfRZJoJ5JG6S0a9vWAfQECgYEA77H/4obgqqHs3yRHmvqKsIKuUhdu/lgmbdCNkZztp9cPCL0dGTzfsYED3nPzZuIAuHsugLU6bg3mMEXmizo3UWEOHyOc2DSOeVro80DF7Zr+P4cIlf9OwbT/FioeergS71g62PeBT1wNMP6bsgCZcjxm2d8EjxmHe1/BA208utcCgYEA3xwM7Zw24PgLQbIoXFx+29b2/ECZAVI2fWcsZCG5JD7JLdWL56pVM97vggSjTYUEkxh79xpqa7OYeqmmrlw/t+3Bm6CbzSy+3PmTzAIlZqZR2GH/NFWS6up0RbmYsTP6qJuanmFUAFY6qM/DwrCEJfg2ezLkl2M5Ljr4UeddwDECgYEA52o5+1FvDS6CS7lyLI1hYyL3/CfFsGgYygME+yXASUAXsr8qndgou+4sdxQJVjYyrem3runJfMGMkOSbvaW6DKaghK3UWuT6/O0pZNkPfuQaPek3/+Fa/yeo85Q8kwyus0fCoQDsZlsgHUJjvUpY23K3dfecJjBmnlWHOrC3ZkcCgYA6aHiF+ABx8t3XjBF+5IQdf3BXZ33m16QGPm1jpWR3tawrQJ2Iv5Hgq3teOWKbF7iz3ZQOg67alUDU0/cb7HCN8UNfP7OqCg3JgdkMbn9mGSKSGE2cYM6+izR3UHhF48q6O0pGLkq0b96ZdRLVtCpUWl/A7vjjHQnKLYv2vTBQ8QKBgAjCjYtJV612Pcax1PbQI3viTabXzdXdcSC2XoW2urBaXf2ocrMXW0RWDEWnmrt9XkSYEntVzLxVi7+FxWBeocQXD2LhN1468s1At2T+Ec9CFZEo95clTndzYjGEGI//RTeYWBkU1/AP2XvleovQRWOBFQ48FZbHFsNgYJe+X751";
        String publickey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjbM+AYYJI9EGgu6A8KkS2mcvgfcz6wZPA6xt8vGPQH/k5H/MxmRWnTm664vfC6aVH89Hby3Kwy9rM8cOpqK7wZt2aDI+Mt0NhCm8jmIwqZth+8OoJ4mxGypWmi6ihz2S3wfmuuJQ7iiXsD+F8rTa9J8UOPmBX4lEMJn7/kxIkDL8FZw6Kf6eE+3v51ABOsPtZowdaqXwHtDAFp0ffgsrCUOox4H/Pbg++FVNzT0zq4Si5Cs/4gI2pDckW1EMYkJqsgJk9X/PbqMl8FLcfeCtXgR0qq4BRQfrQF3QSBKB59lMMJRBSrcMwqYqJqWSJXldbU2YhtBliWhDWpUvmzbzlQIDAQAB";
//        String url = "https://openapi.alipaydev.com/gateway.do";
//        String appid = "2021000118660904";
//        String key = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCaIFSTW/gZgRofhJBH/zK1pWAFOE4Yr5dIdPpGiEc35r6zY6ixqY+2VVU6W/MyPyKwLoJsySk9XF+X2M567M59S7jaItze5rCyNif2He97tO51GwhipehhmIGN05nWXPHCE979SMT7P8DIU0XHbwcNG/liRwhuZVceoVnyvI3eKd5bxG32D9bM5ty23UO6xDX5axG2r27hWRVVzDRTTCA/sk/aqbKG7jfxOAOV8zaC+8rIazd7Mm13ORRGIDmewYdjyTLRTmeteun0CQyODvKpwzvmQ9rGVsEnsAd98EZuIiGn/2fcvrHVlIsUNzhPUCTO6XnLhOTOauRVtKE/r42xAgMBAAECggEADYqVo7TK4YLERsyg2JKFZAw5mXvO8MBMrGMKsjO6/iBZ1O0Gn0VkiQKKUGxPu7RsWcpcTy6FXwzEoVS9H3O62KtUfGXfvf3KvH2hmTAcIGRPJx7O58vn1NtPVqSmuXzFCOYyC1Lfa6u4RmF6mgEZP3+bs5TGGtzwgJJwkrEaTdCA7RxexXTts/8b8PKsBJOoQwx0uwqZ/S4L6CuzifaxhVoefiKjTc57khF+yp9oSlp6Euk8jETrALZBRvWdlehFcnNfY5a2WecjBiJF63qv+7RmoObDyR8sfdA25apaEvSZXSZNvZ7/030vfvN3g+wJJLvUmCGeJx+Weh4nnqmJuQKBgQDTuKiOKzNjZHGBqE6R1kGryOs1RlN6kfqtIIWkmnKB7YKP+taLetnWJSxUmqAg3iG98nJwRHZR28K5Dr2NspUoOOIvSOUrLYvREGLgJ/OqcDH94/F6RdxfS+AFF1dmXoUQ3am11m1mACSWW3HgvzmFRdi0zZKsLo7BCBhPwTL7NwKBgQC6XBfgxzqQtd8VhOHhf4LydgC3NDCxin+0Isw4gbWg/JKim8A80BF5sST8yAJOB5uFQqmTIPkjoGLLYQySsMgCkeyIsSG/XL6YQTlVUB3LuELM2GDt46t7sL516bLDMV+E14J4rL4R1hqMlzSPV7uzJwhaHLP29/2trOeOXihCVwKBgFJeoC0Xf5mmpUZVTaQNYGVN+Lw0TRv9mTIr2fKtJ+mGluofI8MGG6PluHZ95diDs0rr/TvSVtoIDpzisDplhQQuBUlyKb5/rMq8fktJH9Jqbfih6vuwHElzAofYiedI1Ko4IuOctdkeKx3YG9jdbKp9/w5wgCS+xEkzNgopVwVHAoGAag3eyNyYR6I4wlZM9bdLHhBpd9kLnP3kju7+qWmno2YZTNC2JeWDDG3zTqfEKAPMTZoSRtiM7+lmT7d7ID/akxvYMDIdsnllPeBgcFyXmEfUPB8nijn2bP24ZgII6Ww/n6nFZkdoI15k3X1vO+QBvYKJDj8na3TVuPCdFjgfUSECgYAVPiDg0Sy5cQ7zMUHO8LmrI1NMl1LVh3bF2kSKKXKWowvTwFDVxpyWGiQgbngQWf18DJjbGtZgqSN91lEX7t8Fjp1lbh8pG8vqlndhcfkE58NIWLho6NmNBOaSDs1Bk/4kfEV2d63NjcoVHLnKKJny9l2voI0owg9p3kywjwgI5A==";
//        String publickey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApf0ZXf6n/hApbn1mvL5JlABpoa+/wjEt0BmLyZJscSi1oTRhvbCyxFYO2zCogyqSo8g3k+Onx2fI920NwWSm9PPXPDT6yPhySE6kw1/0w3T/1ZUI9OdZzFs2vTRNQrLmJPYGHcccoyMJwYxf53fYK5eX3coKBZa31flOyr/Y/AW8lnT9VtvUMw4+iPLUhsqMfypcl/9obZwJX+mU7vP1yPJ4EWjx3gxb6mmN4SjhA7a/fGoVGXyqHU2ikyDSXCeGCCZS9UkvhuUYUALVNZ7nV/1s+RRRDBOA1EtnvF/YOTmCm20j3QVBfIkW+o8sF4yg3q+96Erf9viO3d6sRMmwfQIDAQAB";
        String format = "json";
        String charset = "utf-8";
        String signType = "RSA2";

        AlipayClient alipayClient = new DefaultAlipayClient(url, appid, key, format, charset, publickey, signType);
        //构建调支付查询接口参数
        QueryPayRequestPo queryPayRequestPo = new QueryPayRequestPo();
        queryPayRequestPo.setOutTradeNo("ht201202107091126338017306");
        AlipayTradeQueryRequest request = PayRecordConvert.alipayTradeQueryRequestConvert(queryPayRequestPo);

        AlipayTradeQueryResponse response = alipayClient.execute(request);

        System.err.println("ss");


        WeChatQueryRequest queryRequest = new WeChatQueryRequest();
        queryRequest.setAppId("wxccae6f23b18c6979");
        queryRequest.setMchId("1419045402");
        queryRequest.setNonceStr(RandomStringUtils.random(32, true, true));
        queryRequest.setOutTradeNo("ta101202302071132018687707");
        queryRequest.setSign(WeChatSignUtil.calculateSign(queryRequest, "6LZZ2hEgXr2n3nDld3liMoZIDhQIDAQA"));
        XMLUtil.instance().setSerializationInclusion(JsonInclude.Include.NON_EMPTY);

        String requestContent = XMLUtil.of(queryRequest);

        String response1 = HttpclientUtils.postBody(PayConfig.WECHAT_QUERY_URL, requestContent);

        System.err.println("sss");
    }

    @Test
    public void third() {
        cashierOrderSettlementPayService.getThirdId(10, 300);
    }
}
