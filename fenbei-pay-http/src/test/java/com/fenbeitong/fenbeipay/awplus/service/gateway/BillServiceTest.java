package com.fenbeitong.fenbeipay.awplus.service.gateway;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayChannel;
import com.fenbeitong.fenbeipay.awplus.model.dto.QueryBillRequestDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.QueryBillResponseDTO;
import com.fenbeitong.fenbeipay.awplus.service.gateway.ali.AliBillServiceImpl;
import com.fenbeitong.fenbeipay.awplus.service.gateway.wechat.WeChatBill3ServiceImpl;
import com.fenbeitong.fenbeipay.awplus.service.gateway.wechat.WeChatBillServiceImpl;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class BillServiceTest extends SpringBaseHttpTest {

    @Autowired
    private WeChatBillServiceImpl weChatBillService;

    @Autowired
    private AliBillServiceImpl aliBillService;

    @Autowired
    private WeChatBill3ServiceImpl weChatBill3Service;
    @Test
    public void bill4WeChat() {

        QueryBillRequestDTO queryBillRequestDTO = new QueryBillRequestDTO();
        queryBillRequestDTO.setBillDate("2022-12-01");
        queryBillRequestDTO.setBillType("ALL");
        queryBillRequestDTO.setTarType("GZIP");
        queryBillRequestDTO.setPayChannel("WeChat");
        QueryBillResponseDTO queryBillResponseDTO = weChatBillService.queryBill(queryBillRequestDTO);
        System.out.println(JsonUtils.toJson(queryBillResponseDTO));
    }

    @Test
    public void bill4Ali() {

        QueryBillRequestDTO queryBillRequestDTO = new QueryBillRequestDTO();
        queryBillRequestDTO.setBillDate("2023-02-16");
        queryBillRequestDTO.setBillType("signcustomer");
        queryBillRequestDTO.setTarType("GZIP");
        queryBillRequestDTO.setPayChannel(CashierPayChannel.ALIPAY.name());
        queryBillRequestDTO.setAppId("****************");
        queryBillRequestDTO.setPayAccount("<EMAIL>");
        QueryBillResponseDTO queryBillResponseDTO = aliBillService.queryBill(queryBillRequestDTO);
        System.out.println(JsonUtils.toJson(queryBillResponseDTO));
    }

    @Test
    public void bill4Ali4DifferentApp() {

        QueryBillRequestDTO queryBillRequestDTO = new QueryBillRequestDTO();
        queryBillRequestDTO.setBillDate("2022-12-07");
        queryBillRequestDTO.setBillType("signcustomer");
        queryBillRequestDTO.setTarType("GZIP");
        queryBillRequestDTO.setPayChannel(CashierPayChannel.ALIPAY.name());
        QueryBillResponseDTO queryBillResponseDTO = aliBillService.queryBill(queryBillRequestDTO);
        System.out.println(JsonUtils.toJson(queryBillResponseDTO));
    }

    @Test
    public void testSign(){
        aliBillService.testSign();
    }

    @Test
    public void bill4Wechat3() {

        QueryBillRequestDTO queryBillRequestDTO = new QueryBillRequestDTO();
        queryBillRequestDTO.setBillDate("2023-01-31");
        queryBillRequestDTO.setBillType("ALL");
        queryBillRequestDTO.setTarType("GZIP");
        queryBillRequestDTO.setPayChannel("WeChat");
        QueryBillResponseDTO queryBillResponseDTO = weChatBill3Service.queryBill3(queryBillRequestDTO);
        System.out.println(JsonUtils.toJson(queryBillResponseDTO));
    }
}