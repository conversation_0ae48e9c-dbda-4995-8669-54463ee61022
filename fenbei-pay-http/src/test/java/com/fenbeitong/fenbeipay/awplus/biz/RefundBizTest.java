package com.fenbeitong.fenbeipay.awplus.biz;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fenbeitong.fenbeipay.awplus.config.PayConfig;
import com.fenbeitong.fenbeipay.awplus.model.dto.ClosePayRequestDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.ClosePayResponseDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.RefundRequestDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.RefundResponseDto;
import com.fenbeitong.fenbeipay.awplus.model.po.PrePayRequestPo;
import com.fenbeitong.fenbeipay.awplus.model.po.QueryPayRequestPo;
import com.fenbeitong.fenbeipay.awplus.model.po.RefundsRequestPo;
import com.fenbeitong.fenbeipay.awplus.model.wechat.WeChatBillQueryRequest;
import com.fenbeitong.fenbeipay.awplus.model.wechat.WeChatQueryRequest;
import com.fenbeitong.fenbeipay.awplus.service.gateway.ali.AliCloseServiceImpl;
import com.fenbeitong.fenbeipay.awplus.service.gateway.ali.pc.AliPcPayServiceImpl;
import com.fenbeitong.fenbeipay.awplus.service.gateway.ali.pc.AliPcRefundServiceImpl;
import com.fenbeitong.fenbeipay.awplus.utils.WeChatSignUtil;
import com.fenbeitong.fenbeipay.awplus.utils.XMLUtil;
import com.fenbeitong.fenbeipay.cashier.pay.CashierOrderSettlementPayService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.HttpclientUtils;
import com.luastar.swift.base.json.JsonUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

import static com.fenbeitong.fenbeipay.core.enums.personpay.PayChannel.PAY_ALI_APP;

public class RefundBizTest extends SpringBaseHttpTest {


    @Autowired
    private RefundBiz refundBiz;

    @Autowired
    private CashierOrderSettlementService cashierOrderSettlementService;

    @Autowired
    private AliPcPayServiceImpl aliPcPayService;
    @Autowired
    private AliPcRefundServiceImpl aliPcRefundService;
    @Autowired
    private AliCloseServiceImpl aliCloseService;

    @Test
    public void refund() {

        RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
        refundRequestDTO.setRefundAmount(new BigDecimal(14019));
        //TODO 为了兼容原来的支付系统保存场景订单ID，后续改为收银台正向父cashierTxnId,并增加逆向父refundTxnId
        refundRequestDTO.setOrderId("OTA202008122001486572615");
        //逆向子交易流水
        refundRequestDTO.setRefundNo("ta203202008122011525910399");
        //正向子交易流水
        refundRequestDTO.setTxnId("ta201202008122010236868165");
        refundRequestDTO.setRefundReason("订单取消");
        refundRequestDTO.setPayChannel(PAY_ALI_APP);
        RefundResponseDto refundResponseDto = refundBiz.refund(refundRequestDTO);
    }

    @Test
    public void aliPcPay() {
        PrePayRequestPo po = new PrePayRequestPo();
        po.setNotifyUrl("http://xxx.com");
        po.setOutTradeNo("tx2012022010414348009714");
        po.setSubject("出行用车订单支付");
        po.setTotalAmount(new BigDecimal(1));
        po.setDeadLineTime(DateUtil.getDateFromString("2022-01-04 20:53:42", "yyyy-MM-dd HH:mm:ss"));
        Object o = aliPcPayService.genPrePayInfo(po);
        System.out.println(JsonUtils.toJson(o));
    }

    @Test
    public void aliPcQuery() {
        QueryPayRequestPo po = new QueryPayRequestPo();
        po.setOutTradeNo("tx201212142091656340373797");
        Object o = aliPcPayService.queryPayDetail(po);
        System.out.println(JsonUtils.toJson(o));
    }

    @Test
    public void aliPcRefund() {
        RefundsRequestPo po = new RefundsRequestPo();
        // 原单号
        po.setTxnId("tx201212132091656340373797");
        // 退款请求号
        po.setRefundNo("rx201212132091656340373797");
        po.setRefundReason("测试退款");
        // 退款金额 /分
        po.setRefundAmount(new BigDecimal(1));
        Object o = aliPcRefundService.refund(po);
        System.out.println(JsonUtils.toJson(o));
    }

    @Test
    public void aliPcRefundQuery() {
        PersonRefundRecord po = new PersonRefundRecord();
        // 原单号
        po.setTxnId("tx201212132091656340373797");
        // 退款请求号
        po.setRefundNo("rx201212132091656340373797");
        Object o = aliPcRefundService.queryRefundDetail(po);
        System.out.println(JsonUtils.toJson(o));
    }

    @Test
    public void aliPcClose() {
        ClosePayRequestDTO req = new ClosePayRequestDTO();
        req.setChargeId("tx201212142091656340373797");
        req.setOrderId("tx201212142091656340373797");
        ClosePayResponseDTO resp = aliCloseService.closeService(req);
        System.out.println(JsonUtils.toJson(resp));
    }

    @Test
    public void weChatBill(){
        WeChatBillQueryRequest queryRequest = new WeChatBillQueryRequest();
        queryRequest.setAppId(PayConfig.WECHAT_APPID);
        queryRequest.setMchId(PayConfig.WECHAT_MCHID);
        queryRequest.setNonceStr(RandomStringUtils.random(32, true, true));
        queryRequest.setBillDate("20221001");
        queryRequest.setBillType("ALL");
        queryRequest.setSign(WeChatSignUtil.calculateSign(queryRequest, PayConfig.WECHAT_PAY_KEY));
        XMLUtil.instance().setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        String requestContent = XMLUtil.of(queryRequest);
        String response = HttpclientUtils.postBody("https://api.mch.weixin.qq.com/pay/downloadbill", requestContent);
        System.out.println(response);
    }
}
