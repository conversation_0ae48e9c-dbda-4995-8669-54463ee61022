package com.fenbeitong.fenbeipay.common;

//import com.aliyun.oss.model.ObjectMetadata;
//import com.fenbeitong.finhub.common.utils.OssHandler;

//import java.io.File;
//import java.util.ArrayList;
//import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/18
 */
public class TestOss {
    public static void main(String[] args) throws Exception {
//        OssHandler o = new OssHandler();
//        o.setAccessKeyId("oghEZG6d3K90utUX");
//        o.setAccessKeySecret("4sibU1g3lnAxAd3qEGN3DT5TxAkEJe");
//        o.setEndpoint("oss-cn-beijing.aliyuncs.com");
//        o.setBucketName("static-fenbeitong");
//        o.initClient();
//        String costUrl = "https://image.fenbeitong.com/public_bank_account_receipt/2022/01/18/4400177950282531800264332/61e61024ef66060d16e36a3c.pdf";
//        String url = costUrl.split("com/")[1];
//        System.out.println(url);
//        ObjectMetadata objectMetadata = o.getObject(url, new File("61e61024ef66060d16e36a3c.pdf"));
//        List<File> fileList = new ArrayList<>();
//        fileList.add(new File("61e61024ef66060d16e36a3c.pdf"));
//        https://image.fenbeitong.com/public_bank_account_receipt/2022/01/18/4400177950282531800264332/61e61024ef66060d16e36a3c.pdf
//        File file = o.getObjectToTemp("/harmony/2019/07/15/export/5d2c287e979d9674d81a49b7.xlsx");
//        File file2 = o.getObjectToTemp("/harmony/2019/07/15/export/5d2c26c6979d9674d81a49b6.xlsx");
//        System.out.println(file.toString());
//        System.out.println(file2.toString());
//        List<ImportColumn> columnList = Lists.newArrayList(new ImportColumn[]{new ImportColumn("订单ID", "col1", ExcelDataType.StringValue), new ImportColumn("始发站", "col2", ExcelDataType.StringValue), new ImportColumn("企业名称", "col3", ExcelDataType.StringValue)});
//        ImportSheet importSheet = new ImportSheet(columnList, LinkedHashMap.class);
//        ExcelUtils.readXlsxExcel(file, new ImportSheet[]{importSheet});
//        System.out.println(JSON.toJSONString(importSheet.getDataList()));
    }
}
