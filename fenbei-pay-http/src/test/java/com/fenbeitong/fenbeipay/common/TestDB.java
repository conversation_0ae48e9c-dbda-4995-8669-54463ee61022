package com.fenbeitong.fenbeipay.common;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class TestDB {

    static String url = "********************************************";

    static String usr = "postgres";

    static String psd = "";



    public static void main(String args[]) {
        Connection conn = null;
        try {
            Class.forName("org.postgresql.Driver");
            conn = DriverManager.getConnection(url, usr, psd);
            Statement st = conn.createStatement();
            ResultSet rs = st.executeQuery("SELECT * FROM account where company_id = '57613d805eac323d0c174237'");
            while (rs.next()) {
                System.out.print(rs.getString(1));
                System.out.print("  ");
                System.out.println(rs.getString(2));
            }
            rs.close();
            st.close();
            conn.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
