package com.fenbeitong.fenbeipay.common.mock;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CompanyPrePayType;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderType;
import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.enums.paycenter.AccountType;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierCreateTradeReqVo;
import com.luastar.swift.base.json.JsonUtils;

import java.math.BigDecimal;
import java.util.Date;

public class MockCashierCreateTradeReqVo {


    public static CashierCreateTradeReqVo mockCreate_success_打车_public0_fbb0_fbq0_third0(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo =new CashierCreateTradeReqVo();
        cashierCreateTradeReqVo.setCompanyId("5747fbc10f0e60e0709d8d7d");
        cashierCreateTradeReqVo.setEmployeeId("598ad66823445f722256c7a3");
        cashierCreateTradeReqVo.setFbOrderId("tx201811251253556");
        //cashierCreateTradeReqVo.setFbOrderId("tx2018112512"+ RandomUtils.randomNum(5));
        cashierCreateTradeReqVo.setFbOrderName("康熙打车");
        cashierCreateTradeReqVo.setFbOrderSnapshot("康熙打车从北京到热河8人大轿500大洋");
        cashierCreateTradeReqVo.setCurrency(PayConstant.CNY);
        cashierCreateTradeReqVo.setOrderType(OrderType.Taxi.getKey());
        cashierCreateTradeReqVo.setBizCallbackUrl("www.baidu.com");
        return cashierCreateTradeReqVo;
    }



    public static CashierCreateTradeReqVo mockCreate_success_因公打车500_public500_fbb0_fbq0_third0(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = mockCreate_success_打车_public0_fbb0_fbq0_third0();
        cashierCreateTradeReqVo.setAccountType(AccountType.Public_Type.getKey());
        cashierCreateTradeReqVo.setPersonalPayPrice(new BigDecimal(0));
        cashierCreateTradeReqVo.setTotalPayPrice(new BigDecimal(500));
        cashierCreateTradeReqVo.setCompanyActPayPrice(new BigDecimal(500));
        cashierCreateTradeReqVo.setCompanyPayPrice(new BigDecimal(500));
        return cashierCreateTradeReqVo;
    }

    public static CashierCreateTradeReqVo mockCreate_success_notpre_因公打车500_public500_fbb0_fbq0_third0(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = mockCreate_success_因公打车500_public500_fbb0_fbq0_third0();
        cashierCreateTradeReqVo.setCompanyPrePay(CompanyPrePayType.NOT_PRE_PAY.getKey());
        return cashierCreateTradeReqVo;
    }

    public static CashierCreateTradeReqVo mockCreate_success_pre_因公打车500_public500_fbb0_fbq0_third0(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = mockCreate_success_因公打车500_public500_fbb0_fbq0_third0();
        cashierCreateTradeReqVo.setCompanyPrePay(CompanyPrePayType.PRE_PAY.getKey());
        return cashierCreateTradeReqVo;
    }


    public static CashierCreateTradeReqVo mockCreate_success_因公打车500_public200_fbb60_fbq100_third140(){

        CashierCreateTradeReqVo cashierCreateTradeReqVo = mockCreate_success_打车_public0_fbb0_fbq0_third0();
        cashierCreateTradeReqVo.setAccountType(AccountType.Public_Type.getKey());
        cashierCreateTradeReqVo.setTotalPayPrice(new BigDecimal(500));
        cashierCreateTradeReqVo.setCompanyActPayPrice(new BigDecimal(200));
        cashierCreateTradeReqVo.setCompanyPayPrice(new BigDecimal(200));
        cashierCreateTradeReqVo.setPersonalPayPrice(new BigDecimal(300));
        return cashierCreateTradeReqVo;
    }

    public static CashierCreateTradeReqVo mockCreate_success_notpre_因公打车500_public200_fbb60_fbq100_third140(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = mockCreate_success_因公打车500_public200_fbb60_fbq100_third140();
        cashierCreateTradeReqVo.setCompanyPrePay(CompanyPrePayType.NOT_PRE_PAY.getKey());
        return cashierCreateTradeReqVo;
    }


    public static CashierCreateTradeReqVo mockCreate_success_pre_因公打车500_public200_fbb60_fbq100_third140(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = mockCreate_success_因公打车500_public200_fbb60_fbq100_third140();
        cashierCreateTradeReqVo.setCompanyPrePay(CompanyPrePayType.PRE_PAY.getKey());
        return cashierCreateTradeReqVo;
    }

    /**
     * 多主体支付
     * @return
     */
    public static CashierCreateTradeReqVo mock_create_multi_pay_paid(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = new CashierCreateTradeReqVo();
        cashierCreateTradeReqVo.setCompanyId("5d1b1d2f23445f4dca76304b");
        cashierCreateTradeReqVo.setPaymentCompanyId("5d1b1d2f23445f4dca76304b");
        cashierCreateTradeReqVo.setEmployeeId("6251506ce2680c558af4cadg");
        cashierCreateTradeReqVo.setFbOrderId("wp2023020910001");
        cashierCreateTradeReqVo.setFbOrderName("多主体扣款");
        cashierCreateTradeReqVo.setFbOrderSnapshot("错花还款");
        cashierCreateTradeReqVo.setCurrency(PayConstant.CNY);
        cashierCreateTradeReqVo.setOrderType(OrderType.Taxi.getKey());
        cashierCreateTradeReqVo.setBizCallbackUrl("localhost");
        cashierCreateTradeReqVo.setAccountType(AccountType.Public_Type.getKey());
        cashierCreateTradeReqVo.setTotalPayPrice(new BigDecimal(1));
        cashierCreateTradeReqVo.setPersonalPayPrice(new BigDecimal(0));
        cashierCreateTradeReqVo.setCompanyPayPrice(new BigDecimal(1));
        cashierCreateTradeReqVo.setDeadlineTime(new Date());
        cashierCreateTradeReqVo.setWrongPaidFlag(false);
//        cashierCreateTradeReqVo.setWrongPaidFbOrderId("wp202211251253557");
        return cashierCreateTradeReqVo;
    }

    //=================因私=====================


    public static CashierCreateTradeReqVo mock_create_wrong_paid(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = new CashierCreateTradeReqVo();
        cashierCreateTradeReqVo.setCompanyId("5f028cb623445f6f42b599b4");
        cashierCreateTradeReqVo.setEmployeeId("62ce9c377896421a9a3a7a97");
        cashierCreateTradeReqVo.setFbOrderId("wp202211251253556");
        cashierCreateTradeReqVo.setFbOrderName("错花还款");
        cashierCreateTradeReqVo.setFbOrderSnapshot("错花还款");
        cashierCreateTradeReqVo.setCurrency(PayConstant.CNY);
        cashierCreateTradeReqVo.setOrderType(OrderType.Taxi.getKey());
        cashierCreateTradeReqVo.setBizCallbackUrl("localhost");
        cashierCreateTradeReqVo.setAccountType(AccountType.Personal_Type.getKey());
        cashierCreateTradeReqVo.setTotalPayPrice(new BigDecimal(1));
        cashierCreateTradeReqVo.setPersonalPayPrice(new BigDecimal(1));
        cashierCreateTradeReqVo.setCompanyPayPrice(new BigDecimal(0));
        cashierCreateTradeReqVo.setDeadlineTime(new Date());
        cashierCreateTradeReqVo.setWrongPaidFlag(true);
        cashierCreateTradeReqVo.setWrongPaidFbOrderId("wp202211251253557");
        return cashierCreateTradeReqVo;
    }
    public static CashierCreateTradeReqVo mockCreate_success_因私打车300_public0_fbb60_fbq100_third140(){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = mockCreate_success_打车_public0_fbb0_fbq0_third0();
        cashierCreateTradeReqVo.setAccountType(AccountType.Personal_Type.getKey());
        cashierCreateTradeReqVo.setTotalPayPrice(new BigDecimal(300));
        cashierCreateTradeReqVo.setPersonalPayPrice(new BigDecimal(300));
        cashierCreateTradeReqVo.setCompanyActPayPrice(new BigDecimal(0));
        cashierCreateTradeReqVo.setCompanyPayPrice(new BigDecimal(0));
        cashierCreateTradeReqVo.setDeadLineMin(30);
        return cashierCreateTradeReqVo;
    }


    public static void main(String[] args) {
        CashierCreateTradeReqVo vo= mockCreate_success_notpre_因公打车500_public200_fbb60_fbq100_third140();
       System.out.println(JsonUtils.toJson(vo));

    }


}
