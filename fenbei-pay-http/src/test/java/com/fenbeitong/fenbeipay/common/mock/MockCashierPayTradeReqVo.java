package com.fenbeitong.fenbeipay.common.mock;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderType;
import com.fenbeitong.fenbeipay.core.enums.personpay.PayChannel;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierPayTradeReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.vouchers.VoucherPayDTO;
import com.luastar.swift.base.json.JsonUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class MockCashierPayTradeReqVo {


    public static CashierPayTradeReqVo mockPay_success_打车_public0_fbb0_fbq0_third0(){
        CashierPayTradeReqVo cashierPayTradeReqVo =new CashierPayTradeReqVo();
        cashierPayTradeReqVo.setCompanyId("58f96d565f281a53885d9b47");
        cashierPayTradeReqVo.setEmployeeId("5d67425c23445f6d1406a91d");
        cashierPayTradeReqVo.setFbOrderId("5f3f96d22798630ef0eff17c");
        cashierPayTradeReqVo.setCashierTxnId("tn881202008211742053983739");
        cashierPayTradeReqVo.setOrderType(OrderType.Train.getKey());
        cashierPayTradeReqVo.setLon(116.531891);
        cashierPayTradeReqVo.setLat(39.969803);
        cashierPayTradeReqVo.setClientIp("************");
        cashierPayTradeReqVo.setClientVersion("V4.2.0");
        cashierPayTradeReqVo.setDevice("ios");
        return cashierPayTradeReqVo;
    }



    public static CashierPayTradeReqVo mockPay_success_因公打车500_public500_fbb0_fbq0_third0(){
        CashierPayTradeReqVo cashierPayTradeReqVo = mockPay_success_打车_public0_fbb0_fbq0_third0();
        cashierPayTradeReqVo.setPersonalPayPrice(new BigDecimal(0));
        cashierPayTradeReqVo.setTotalPayPrice(new BigDecimal(500));
        return cashierPayTradeReqVo;
    }

    public static CashierPayTradeReqVo mockPay_success_notpre_因公打车500_public500_fbb0_fbq0_third0(){
        CashierPayTradeReqVo cashierPayTradeReqVo = mockPay_success_因公打车500_public500_fbb0_fbq0_third0();
        return cashierPayTradeReqVo;
    }

    public static CashierPayTradeReqVo mockPay_success_pre_因公打车500_public500_fbb0_fbq0_third0(){
        CashierPayTradeReqVo cashierPayTradeReqVo = mockPay_success_因公打车500_public500_fbb0_fbq0_third0();
        return cashierPayTradeReqVo;
    }


    public static CashierPayTradeReqVo mockPay_success_因公打车500_public200_fbb60_fbq100_third140(){

        CashierPayTradeReqVo cashierPayTradeReqVo = mockPay_success_打车_public0_fbb0_fbq0_third0();
        cashierPayTradeReqVo.setTotalPayPrice(new BigDecimal(300));
        cashierPayTradeReqVo.setPersonalPayPrice(new BigDecimal(300));
        cashierPayTradeReqVo.setPersonFbbPayPrice(new BigDecimal(300));
        cashierPayTradeReqVo.setPersonVouchersPayPrice(new BigDecimal(0));
        cashierPayTradeReqVo.setThirdPartPayPrice(new BigDecimal(0));
        List<VoucherPayDTO> voucherPayDTOS=new ArrayList<>(2);
        VoucherPayDTO voucherPayDTO=new VoucherPayDTO();
        voucherPayDTO.setVoucherId("5bfcc444c621f124f0ecf75a");
        voucherPayDTO.setBalance(new BigDecimal(60));
        voucherPayDTO.setCurrentOperationAmount(new BigDecimal(60));
        voucherPayDTOS.add(voucherPayDTO);
        voucherPayDTO=new VoucherPayDTO();
        voucherPayDTO.setVoucherId("5bfcc444c621f124f0ecf75b");
        voucherPayDTO.setBalance(new BigDecimal(40));
        voucherPayDTO.setCurrentOperationAmount(new BigDecimal(40));
        voucherPayDTOS.add(voucherPayDTO);
        cashierPayTradeReqVo.setVoucherPayDTOList(voucherPayDTOS);
        cashierPayTradeReqVo.setThirdPartChannel(PayChannel.PAY_ALI_APP.name());
        return cashierPayTradeReqVo;
    }

    public static CashierPayTradeReqVo mockPay_success_notpre_因公打车500_public200_fbb60_fbq100_third140(){
        CashierPayTradeReqVo cashierPayTradeReqVo = mockPay_success_因公打车500_public200_fbb60_fbq100_third140();
        return cashierPayTradeReqVo;
    }


    public static CashierPayTradeReqVo mockPay_success_pre_因公打车500_public200_fbb60_fbq100_third140(){
        CashierPayTradeReqVo cashierPayTradeReqVo = mockPay_success_因公打车500_public200_fbb60_fbq100_third140();
        return cashierPayTradeReqVo;
    }


    //=================因私=====================


    public static CashierPayTradeReqVo mockPay_success_因私打车300_public0_fbb60_fbq100_third140(){
        CashierPayTradeReqVo cashierPayTradeReqVo = mockPay_success_打车_public0_fbb0_fbq0_third0();
        cashierPayTradeReqVo.setTotalPayPrice(new BigDecimal(2100));
        cashierPayTradeReqVo.setPersonalPayPrice(new BigDecimal(300));
        cashierPayTradeReqVo.setPersonFbbPayPrice(new BigDecimal(300));
        return cashierPayTradeReqVo;
    }


    public static void main(String[] args) {
        CashierPayTradeReqVo vo= mockPay_success_notpre_因公打车500_public200_fbb60_fbq100_third140();
        System.out.println(JsonUtils.toJson(vo));

    }



}
