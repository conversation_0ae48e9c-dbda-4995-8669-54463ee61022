package com.fenbeitong.fenbeipay.common.mock;


import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MockPersonRefundRecordParam {


    public List<PersonRefundRecord> mockPersonRefundRecordList(Integer size){
        List<PersonRefundRecord> personRefundRecords=new ArrayList<>(size);
        PersonRefundRecord personRefundRecord;
        for(int i=1;i<=5;i++){
            personRefundRecord = new PersonRefundRecord();
            personRefundRecord.setRefundStatus(PayConstant.payOrderRefundFail);
            personRefundRecord.setRetryNum(i);
            personRefundRecord.setCreateTime(new Date());
            personRefundRecord.setId("5bdafb8223445f778c22f4fb");

        }
        return personRefundRecords;
    }

}
