package com.fenbeitong.fenbeipay.common;

import cn.hutool.core.lang.Snowflake;

import java.util.HashMap;
import java.util.Map;

public class TestSnow {
    public static void main(String[] args) {
        test1();
        test2();
    }

    public static void test1(){
        Map<Long, Long> map = new HashMap<>();
        for (int i = 0; i < 1000000; i++) {
            long id = new Snowflake(2, 5).nextId();
            if (map.containsKey(id)) {
                System.out.println("test1出现重复");
//                break;
            }
//            System.out.println("id1=*********" + id);
            map.put(id, id);
        }
    }
    public static void test2() {
        Snowflake snowflake = new Snowflake(2, 5);
        Map<Long, Long> map = new HashMap<>();
        for (int i = 0; i < 1000000; i++) {
            long id = snowflake.nextId();
            if (map.containsKey(id)) {
                System.out.println("test2出现重复");
                break;
            }
//            System.out.println("id2=*********" + id);
            map.put(id, id);
        }
    }
}
