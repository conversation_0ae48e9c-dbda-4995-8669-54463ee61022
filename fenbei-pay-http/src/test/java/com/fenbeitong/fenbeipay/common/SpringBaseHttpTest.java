package com.fenbeitong.fenbeipay.common;


import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(locations = {"classpath:/spring/spring-swift.xml"})
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles(value="dev")
public class SpringBaseHttpTest extends AbstractJUnit4SpringContextTests {


}
