package com.fenbeitong.fenbeipay.common.mock;

import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundTradeReqVo;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.RandomUtils;

import java.math.BigDecimal;

public class MockCashierRefundTradeReqVo {


    public static CashierRefundTradeReqVo mockRefund_success_打车_public0_fbb0_fbq0_third0(){
        CashierRefundTradeReqVo CashierRefundTradeReqVo =new CashierRefundTradeReqVo();
        CashierRefundTradeReqVo.setCompanyId("5747fbc10f0e60e0709d8d7d");
        CashierRefundTradeReqVo.setEmployeeId("598ad66823445f722256c7a3");
        CashierRefundTradeReqVo.setFbOrderId("tx2018112512"+ RandomUtils.randomNum(5));
        CashierRefundTradeReqVo.setBizCallbackUrl("www.baidu.com");
        return CashierRefundTradeReqVo;
    }



    public static CashierRefundTradeReqVo mockRefund_success_因公打车500_public500_fbb0_fbq0_third0(){
        CashierRefundTradeReqVo CashierRefundTradeReqVo = mockRefund_success_打车_public0_fbb0_fbq0_third0();
        CashierRefundTradeReqVo.setTotalRefundAmount(new BigDecimal(500));
        CashierRefundTradeReqVo.setPersonalRefundAmount(new BigDecimal(0));
        CashierRefundTradeReqVo.setCompanyRefundAmount(new BigDecimal(500));
        CashierRefundTradeReqVo.setRefundReason("朕不想打车了，朕要坐飞机");
        return CashierRefundTradeReqVo;
    }

    public static CashierRefundTradeReqVo mockRefund_success_因公打车500_public200_fbb10_fbq0_third290(){
        CashierRefundTradeReqVo CashierRefundTradeReqVo = mockRefund_success_打车_public0_fbb0_fbq0_third0();
        CashierRefundTradeReqVo.setTotalRefundAmount(new BigDecimal(500));
        CashierRefundTradeReqVo.setPersonalRefundAmount(new BigDecimal(300));
        CashierRefundTradeReqVo.setCompanyRefundAmount(new BigDecimal(200));
        CashierRefundTradeReqVo.setRefundReason("朕不想打车了，朕要坐飞机");
        return CashierRefundTradeReqVo;
    }



    //=================因私=====================


    public static CashierRefundTradeReqVo mockRefund_success_因私打车300_public0_fbb60_fbq100_third140(){
        CashierRefundTradeReqVo CashierRefundTradeReqVo = mockRefund_success_打车_public0_fbb0_fbq0_third0();
        return CashierRefundTradeReqVo;
    }


    public static void main(String[] args) {
        CashierRefundTradeReqVo vo= mockRefund_success_因公打车500_public200_fbb10_fbq0_third290();
       System.out.println(JsonUtils.toJson(vo));

    }


}
