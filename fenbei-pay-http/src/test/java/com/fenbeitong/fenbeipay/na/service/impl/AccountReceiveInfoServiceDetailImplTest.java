package com.fenbeitong.fenbeipay.na.service.impl;

import com.fenbeitong.fenbeipay.api.model.dto.newaccount.req.ReceiveAccountDelReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.newaccount.req.ReceiveAccountReqDTO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoDetailService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;


public class AccountReceiveInfoServiceDetailImplTest extends SpringBaseHttpTest {

    @Autowired
    private AccountReceiveInfoDetailService accountReceiveInfoDetailService;

    @Test
    public void batchAddEmployeeAccount() {
        ReceiveAccountReqDTO accountReqDTO1 = new ReceiveAccountReqDTO();
        accountReqDTO1.setAccountNo("hahha3.com1agin");
        accountReqDTO1.setCompanyId("5d1b1d2f23445f4dca76304b");
        accountReqDTO1.setAccountName("笑梅");
     //   accountReqDTO1.setBankId("");
        accountReqDTO1.setBankName("河北招商银行");
        accountReqDTO1.setUserName("笑梅");
        accountReqDTO1.setUserId("621845d349430b0f22d81fd0");
        accountReqDTO1.setIsDefault(false);
       // accountReqDTO1.setSubBankId("");
        accountReqDTO1.setSubBankName("河北招商银行支行");
        accountReqDTO1.setThirdId("23456");
        accountReqDTO1.setType(2);

        ReceiveAccountReqDTO accountReqDTO2 = new ReceiveAccountReqDTO();
        accountReqDTO2.setAccountNo("hahah.com2agin");
        accountReqDTO2.setCompanyId("5d1b1d2f23445f4dca76304b");
        accountReqDTO2.setAccountName("笑梅");
        accountReqDTO2.setUserName("笑梅");
        //accountReqDTO2.setBankId("");
        accountReqDTO2.setBankName("河北招商银行");
        accountReqDTO2.setUserId("621845d349430b0f22d81fd0");
        accountReqDTO2.setIsDefault(false);
        //accountReqDTO2.setSubBankId("");
        accountReqDTO2.setSubBankName("河北招商银行支行");
        accountReqDTO2.setThirdId("2345");
        accountReqDTO2.setType(2);
        accountReceiveInfoDetailService.batchAddEmployeeAccount(Arrays.asList(accountReqDTO1,accountReqDTO2), false);
    }

    @Test
    public void batchUpdateEmployeeAccount() {
        ReceiveAccountReqDTO accountReqDTO1 = new ReceiveAccountReqDTO();
        accountReqDTO1.setId("63c0c7bdc8de421e8f019dbd");
        accountReqDTO1.setAccountNo("<EMAIL>");
        accountReqDTO1.setCompanyId("5d1b1d2f23445f4dca76304b");
        accountReqDTO1.setAccountName("笑梅");
        accountReqDTO1.setBankId("");
        accountReqDTO1.setBankName("");
        accountReqDTO1.setUserName("笑梅");
        accountReqDTO1.setUserId("621845d349430b0f22d81fd0");
        accountReqDTO1.setIsDefault(false);
        accountReqDTO1.setSubBankId("");
        accountReqDTO1.setSubBankName("");
        accountReqDTO1.setThirdId("234511");
        accountReqDTO1.setType(1);

        ReceiveAccountReqDTO accountReqDTO2 = new ReceiveAccountReqDTO();
        accountReqDTO2.setId("63b53363c09ba48bf5a4293f222");
        accountReqDTO2.setAccountNo("<EMAIL>");
        accountReqDTO2.setCompanyId("5d1b1d2f23445f4dca76304b");
        accountReqDTO2.setAccountName("笑梅");
        accountReqDTO2.setUserName("笑梅");
        accountReqDTO2.setBankId("");
        accountReqDTO2.setBankName("");
        accountReqDTO2.setUserId("621845d349430b0f22d81fd0");
        accountReqDTO2.setIsDefault(false);
        accountReqDTO2.setSubBankId("");
        accountReqDTO2.setSubBankName("");
        accountReqDTO2.setThirdId("2345");
        accountReqDTO2.setType(1);
        accountReceiveInfoDetailService.batchUpdateEmployeeAccount(Arrays.asList(accountReqDTO1,accountReqDTO2), true);
    }

    @Test
    public void batchRemoveEmployeeAccount() {
        ReceiveAccountDelReqDTO delReqDTO = new ReceiveAccountDelReqDTO();
        delReqDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
        delReqDTO.setUserId("621845d349430b0f22d81fd0");
        delReqDTO.setThirdId("2345");

        ReceiveAccountDelReqDTO delReqDTO2 = new ReceiveAccountDelReqDTO();
        delReqDTO2.setCompanyId("5d1b1d2f23445f4dca763043b");
        delReqDTO2.setUserId("621845d349430b0f22d81fed0");
        delReqDTO2.setThirdId("2345");

        accountReceiveInfoDetailService.batchRemoveEmployeeAccount(Arrays.asList(delReqDTO,delReqDTO2));
    }
    @Test
    public void getEmployeeAccountListByType(){
        accountReceiveInfoDetailService.getEmployeeAccountListByType("5d1b1d2f23445f4dca76304b",Arrays.asList("621845d349430b0f22d81fd0"));
    }
}