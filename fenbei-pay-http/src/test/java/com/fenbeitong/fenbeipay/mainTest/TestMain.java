package com.fenbeitong.fenbeipay.mainTest;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryCostAttributionReqVo;
import com.google.common.collect.Lists;

import java.math.BigDecimal;

public class TestMain {

    public static void main(String[] args) throws Exception{
        String ss = String.join(",", Lists.newArrayList());
        System.out.println(ss);
        QueryCostAttributionReqVo reqVo=new QueryCostAttributionReqVo();
        System.out.println(BigDecimal.valueOf(11.00).toString());
        String json = "{\n" +
                "    \"code\": \"Success\",\n" +
                "    \"messages\": \"查询成功\",\n" +
                "    \"data\": {\n" +
                "        \"totalCount\": 4,\n" +
                "        \"dataSet\": [\n" +
                "            {\n" +
                "                \"money\": 0,\n" +
                "                \"rangeType\": 0,\n" +
                "                \"count\": 153\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"requestId\": \"cf9f18db-c676-4896-9308-2b7c7b53ba4a\"\n" +
                "}\n";
        Object jsonArray = JSONObject.parseObject(json).getJSONObject("data").getJSONArray("dataSet");
        System.out.println(jsonArray);
    }
}
