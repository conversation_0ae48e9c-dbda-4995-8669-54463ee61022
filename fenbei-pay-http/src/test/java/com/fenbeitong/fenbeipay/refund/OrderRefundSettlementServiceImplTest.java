package com.fenbeitong.fenbeipay.refund;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierRefundWay;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierTradeReliefRPCVo;
import com.fenbeitong.fenbeipay.bank.softwareSaas.DTO.RefundOrderParamDTO;
import com.fenbeitong.fenbeipay.bank.softwareSaas.RefundOrderForBudgetManager;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundTradeReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundTradeRespVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;

public class OrderRefundSettlementServiceImplTest extends SpringBaseHttpTest {


    @Autowired
    private RefundOrderForBudgetManager refundOrderForBudgetManager;

    @Test
    public void testCreateRefundToEmmployeeAccount() throws Exception {
        RefundOrderParamDTO refundOrderParamDTO=new RefundOrderParamDTO();
        refundOrderParamDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        refundOrderParamDTO.setEmployeeId("623d831cefabc845abd93a08");
        refundOrderParamDTO.setFbOrderId("OBK220328211711029185498");
        refundOrderParamDTO.setRefundOrderId("RBK220330154720191808330");
        refundOrderParamDTO.setRefundAmount(BigDecimal.valueOf(600));
        refundOrderParamDTO.setFbApplyBind(2);
        refundOrderParamDTO.setCheckStatus(2);
        refundOrderParamDTO.setBankAccountNo("5305194085616510156");
        refundOrderParamDTO.setPettyId(null);
        refundOrderParamDTO.setCategoryType(126);
        refundOrderParamDTO.setRefundOrderFlag(true);
        refundOrderParamDTO.setCreateTime(new Date());
        refundOrderForBudgetManager.refundOrderForNomal(refundOrderParamDTO);
    }

    @Test
    public void testCreateRefundToCompany() throws Exception {
        RefundOrderParamDTO refundOrderParamDTO=new RefundOrderParamDTO();
        refundOrderParamDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
        refundOrderParamDTO.setEmployeeId("5e1ed56e23445f5b4018ef9e");
        refundOrderParamDTO.setFbOrderId("OBK211220102017629221599");
        refundOrderParamDTO.setRefundOrderId("RBK220329200527046799643");
        refundOrderParamDTO.setRefundAmount(BigDecimal.valueOf(38));
        refundOrderParamDTO.setFbApplyBind(2);
        refundOrderParamDTO.setCheckStatus(3);
        refundOrderParamDTO.setBankAccountNo("3395223654424515247");
        refundOrderParamDTO.setPettyId(null);
        refundOrderParamDTO.setCategoryType(126);
        refundOrderParamDTO.setRefundOrderFlag(false);
        refundOrderParamDTO.setCreateTime(new Date());
        refundOrderForBudgetManager.refundOrderForNomal(refundOrderParamDTO);
    }

    @Test
    public void testCreateRefundToCompanyForPetty() throws Exception {
        RefundOrderParamDTO refundOrderParamDTO=new RefundOrderParamDTO();
        refundOrderParamDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
        refundOrderParamDTO.setEmployeeId("5e1ed56e23445f5b4018ef9e");
        refundOrderParamDTO.setFbOrderId("OBK211220102017629221599");
        refundOrderParamDTO.setRefundOrderId("RBK220324171534132578744");
        refundOrderParamDTO.setRefundAmount(BigDecimal.valueOf(10));
        refundOrderParamDTO.setFbApplyBind(2);
        refundOrderParamDTO.setCheckStatus(3);
        refundOrderParamDTO.setBankAccountNo("3395223654424515247");
        refundOrderParamDTO.setPettyId(null);
        refundOrderParamDTO.setCategoryType(126);
        refundOrderParamDTO.setRefundOrderFlag(false);
        refundOrderParamDTO.setCreateTime(new Date());
        refundOrderForBudgetManager.refundOrderForNomal(refundOrderParamDTO);
    }
}