package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.acctdech.db.mapper.AcctBusinessDebitFlowMapper;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessDebitFlow;
import com.fenbeitong.fenbeipay.extract.manager.AcctCheckManagerService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.easymock.EasyMock;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.fenbeitong.finhub.common.utils.DateUtils.FORMAT_DATE_TIME_PATTERN;
import static org.easymock.EasyMock.*;

/**
 * <AUTHOR>
 * @date 2021/3/22
 */
public class AcctCheckControllerTest extends SpringBaseHttpTest {


    AcctCheckController acctCheckController = new AcctCheckController();
    HttpRequest httpRequest;
    HttpResponse httpResponse;

    @Before
    public void setUp() {
        httpRequest = niceMock(HttpRequest.class);
        httpResponse = niceMock(HttpResponse.class);

    }

//    @Test
    public void acctCheck() {
        try {
            // 1. Record阶段,记录mock对象上的操作
            AcctCheckController acctCheckController = createMock(AcctCheckController.class);
            httpRequest = createMock(HttpRequest.class);
            EasyMock.expect(httpRequest.getAttributeMap()).andReturn(null);
            // 2. Replay阶段，这一步才是真正的执行被测试的方法。
            acctCheckController.acctCheck(httpRequest,httpResponse);
        }catch (FinhubException e){
            Assert.assertEquals(600L,e.getCode().longValue());
        }
    }
    @Test
    public void paramMissingCheckType() {
        String jobConfig = "{\n" +
                "  \"isRetry\": false,\n" +
                "  \"checkTarget\": \"ZBBANK\",\n" +
                "  \"retryBillTime\": \"2021-03-23\"\n" +
                "}";
        expect(httpRequest.getParameter("jobConfig")).andReturn(jobConfig);
        replay(httpRequest);
        try {
            acctCheckController.acctCheck(httpRequest,httpResponse);
        }catch (ValidateException e){
            boolean isTrue = e.getMessage().contains("不可以为空");
            Assert.assertTrue("参数[retryBillTime]对账日期不可以为空！",isTrue);
        }
    }

    @Test
    public void normalTest() {
        //1.1 mock param
        String jobConfig = "{\n" +
                "  \"isRetry\": false,\n" +
                "  \"checkTarget\": \"ZBBANK\",\n" +
                "  \"checkType\": 1,\n" +
                "  \"retryBillTime\": \"2021-03-23\"\n" +
                "}";
        expect(httpRequest.getParameter("jobConfig")).andReturn(jobConfig);
        replay(httpRequest);

        //1.2 mock acctCheckManagerService service
        AcctCheckManagerService acctCheckManagerService = createNiceMock(AcctCheckManagerService.class);
        acctCheckManagerService.check(true,"2021-03-22",1,"ZBBANK");
        //void mock方法后需要执行
        expectLastCall();
        ReflectionTestUtils.setField(acctCheckController, "acctCheckManagerService", acctCheckManagerService);
        replay(acctCheckManagerService);

        expect(httpResponse.getRequestId()).andReturn("normalExecute_001");
        replay(httpResponse);
        acctCheckController.acctCheck(httpRequest,httpResponse);
    }

    @Resource
    private AcctBusinessDebitFlowMapper acctBusinessDebitFlowMapper;

    @Test
    public void addFlow() {
        AcctBusinessDebitFlow flow;
        for (int i = 10000; i < 13000; i++) {
            flow = new AcctBusinessDebitFlow();
            flow.setAccountFlowId("TEST" + i);
            flow.setAccountId("ASA202112141524034952323");
            flow.setAccountModel(2);
            flow.setAccountSubType(2);
            flow.setBankAccountNo("0356000005252585715273769");
            flow.setBankName("ZBBANK");
            flow.setBankAcctId("0356000005252585715273769");
            flow.setCompanyId("61b6bfbbc5b90e2801f691fe");
            flow.setCompanyMainId("CMA202112141524034681523");
            flow.setOrderType(11);
            flow.setTradeType(41);
            flow.setOperationType(41);
            flow.setOperationTypeDesc("商务消费");
            flow.setBizNo("6203b66be37c7d4233a27073");
            flow.setCashierTxnId("ht881202202092041169715742");
            flow.setBankTransNo("3505576598100906008");
            flow.setCurrentBalance(new BigDecimal(0));
            flow.setOperationAmount(new BigDecimal(1));
            flow.setBalance(new BigDecimal(1));
            flow.setSyncBankAmount(new BigDecimal(1));
            flow.setSyncBankStatus(1);
            flow.setSyncBankTime(new Date());
            flow.setSyncBankTransNo("CCT22020920421401059815");
            flow.setTargetBankName("ZBBANK");
            flow.setTargetBankAcctId("0356000003899687460540421");
            flow.setTargetAccount("03560000038996874605");
            flow.setOperationDescription("商务消费");
            flow.setOperationUserId("61b6bfbbc5b90e2801f691ff");
            flow.setOperationChannelType(1);
            flow.setOperationUserName("凤梨");
            flow.setCallbackNext(DateUtil.getDateFromString("2022-02-09 20:42:14", FORMAT_DATE_TIME_PATTERN));
            flow.setCallbackNum(1);
            flow.setCostImageUrl("https://image.fenbeitong.com/public_bank_account_receipt/2021/05/08/6203905355752214547/6096555bdfe447a6216d3634.pdf");
            flow.setCostImageTime(new Date());
            flow.setCostImageStatus(1);
            int l = i % 9 + 1;
            String time = "2022-04-0"+ l + " 14:03:13";
            flow.setCreateTime(DateUtil.getDateFromString(time, FORMAT_DATE_TIME_PATTERN));
            int insert = acctBusinessDebitFlowMapper.insert(flow);
            if (insert < 1) {
                System.out.println("插入失败 " + i);
            }
            System.out.println(flow.getAccountFlowId());
        }
    }

    // TEST9000 ~ TEST9999
    @Test
    public void delFlow() {
        AcctBusinessDebitFlow flow;
        for (int i = 0 ; i < 10000 ; i++) {
            flow = new AcctBusinessDebitFlow();
            flow.setAccountFlowId("test0421" + i);
            acctBusinessDebitFlowMapper.delete(flow);
        }
    }

    @Test
    public void mergePdf() throws IOException {
        List<File> files = new ArrayList();
        for (int i = 0; i < 100; i++) {
            files.add(new File("/data/pdf/mergedTest0.pdf"));
        }
        PDFMergerUtility mergePdf = new PDFMergerUtility();
        for (File f : files) {
            if(f.exists() && f.isFile()){
                mergePdf.addSource(f);
            }
        }
        mergePdf.setDestinationFileName("/data/pdf/mergedTest00.pdf");
        mergePdf.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
    }
}
