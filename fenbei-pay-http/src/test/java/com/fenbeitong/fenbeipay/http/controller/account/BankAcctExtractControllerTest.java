package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UBankAcctFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UBankAcctService;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctFlowSearchReqDTO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcct;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcctFlow;
import com.fenbeitong.fenbeipay.extract.service.UBankAcctExtractService;
import com.fenbeitong.fenbeipay.http.controller.extract.BankAcctExtractController;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.easymock.EasyMock.niceMock;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-05-31 5:37 下午
 */

public class BankAcctExtractControllerTest extends SpringBaseHttpTest {

    @Autowired
    BankAcctExtractController bankAcctExtractController;

    @Autowired
    private UBankAcctService uBankAcctService;

    @Autowired
    private UBankAcctFlowService uBankAcctFlowService;

    @Autowired
    private UBankAcctExtractService uBankAcctExtractService;
    HttpRequest httpRequest;
    HttpResponse httpResponse;

    @Before
    public void setUp() {
        httpRequest = niceMock(HttpRequest.class);
        httpResponse = niceMock(HttpResponse.class);

    }

    @Test
    public void dayExcuteTest() {
        bankAcctExtractController.dayExecuteNew(httpRequest, httpResponse);
    }

    @Test
    public void a() {
        BankAcctFlowSearchReqDTO reqDTO = new BankAcctFlowSearchReqDTO();
        reqDTO.setPageNo(1);
        reqDTO.setPageSize(500);
        reqDTO.setOperationType(74);
        reqDTO.setTradeStartTime(new Date());
        reqDTO.setTradeEndTime(new Date());
        List<BankAcctFlow> flow = uBankAcctFlowService.queryBankAcctFlowList(reqDTO);
        FinhubLogger.info("修复分贝券退还到收款账户数据，flow：", JsonUtils.toJson(flow));
        for (BankAcctFlow bankAcctFlow : flow) {
            BankAcctFlow update = new BankAcctFlow();
            update.setBankAcctFlowId(bankAcctFlow.getBankAcctFlowId());
            update.setOperationAmount(bankAcctFlow.getOperationAmount().abs());
            update.setUpdateTime(new Date());
            uBankAcctFlowService.updateByFLowIdSelective(update);
        }
        List<String> bankAcctIdList = flow.stream().map(BankAcctFlow::getBankAcctId).collect(Collectors.toList());
        List<BankAcct> bankAcctList = uBankAcctService.queryAll().stream().filter(s -> bankAcctIdList.contains(s.getBankAcctId())).collect(Collectors.toList());
        // 开始时间
        Date startExtractTime = reqDTO.getTradeStartTime();
        // 结束时间
        Date endExtractTime = reqDTO.getTradeEndTime();
        StringBuffer success = new StringBuffer();
        while (!DateUtils.isSameDate(startExtractTime,endExtractTime)){
            Date extractTime = startExtractTime;
            /**
             * 第二参数为空，因此第一个参数也为空
             * QX 2022-03-04
             */
            uBankAcctExtractService.saveBankAcctExtractDayDoHandle(new ArrayList<>(), bankAcctList, new ArrayList<>(), extractTime);
            startExtractTime = DateUtils.addDay(startExtractTime,1);
        }
    }

    @Test
    public void testF(){
        BankAcctFlowSearchReqDTO queryReq2 = new BankAcctFlowSearchReqDTO();
        queryReq2.setBankAcctId("5747fbc10f0e60e0709d8d7d");
        queryReq2.setCompanyMainId("CMA202104231733005023212");
        queryReq2.setOperationType(41);
        queryReq2.setBankAcctType(1);
        String startTime = DateUtils.format(new Date(), DateUtils.FORMAT_DATE_PATTERN) + " 00:00:00";
        queryReq2.setTradeStartTime(DateUtils.parseTime(startTime));
        queryReq2.setTradeEndTime(new Date());
        queryReq2.setOffset(0);
        queryReq2.setPageSize(10);
//        queryReq2.setPageNo(1);
        List<BankAcctFlow> flow = uBankAcctFlowService.queryBankAcctFlowList(queryReq2);
        System.out.println(JsonUtils.toJson(flow));
    }
}
