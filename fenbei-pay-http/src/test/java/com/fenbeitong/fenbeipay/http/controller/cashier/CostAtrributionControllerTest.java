package com.fenbeitong.fenbeipay.http.controller.cashier;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderCostAttributionService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CostAttributionListRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CostAttributionReqVo;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description CostAtrributionControllerTest
 * @date 2022-04-24 2:33 下午
 */

public class CostAtrributionControllerTest extends SpringBaseHttpTest {

    protected HttpRequest request;
    protected HttpResponse response;

    @Autowired
    CashierOrderCostAttributionService cashierOrderCostAttributionService;

    @Test
    public void costAttributionListTest() {
        CostAttributionReqVo costAttributionReqVo = new CostAttributionReqVo();
        costAttributionReqVo.setCompanyId("aaa");
        costAttributionReqVo.setEndTime("2022-04-22");
        costAttributionReqVo.setStartTime("2022-01-01");
        costAttributionReqVo.setPageNo(1);
        costAttributionReqVo.setPageSize(10);
        ResponsePage<CostAttributionListRespVo> costAttributionList =
            cashierOrderCostAttributionService.getCostAttributionList(costAttributionReqVo);
        Assert.assertNotNull(costAttributionList);
    }
}
