package com.fenbeitong.fenbeipay.http.controller.cashier;


import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierPayDetailRPCDTO;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubAbleReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierPayDetailRPCVo;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AccountRedcouponSearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponUpdateUseScopeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.AccountRedcouponRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierSearchOrderService;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubService;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponSearchService;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.vo.vouchers.VoucherPayDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VoucherRefundDTO;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.vo.VouchersPaymentParams;
import com.fenbeitong.fenbeipay.vouchers.vo.VouchersRefundParams;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;
import com.google.common.collect.Lists;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.easymock.EasyMock;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

/**
* CashierOrderSettlementController Tester.
*
* <AUTHOR> name>
* @since <pre>Nov 26, 2018</pre>
* @version 1.0
*/
public class CashierOrderSettlementControllerTest extends SpringBaseHttpTest {

    private CashierOrderSettlementController mockCashierController;
    protected HttpRequest request;
    protected HttpResponse response;

    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    ICashierSearchOrderService iCashierSearchOrderService;
    @Autowired
    IAccountSubService iAccountSubService;
    @Autowired
    IAccountRedcouponSearchService iAccountRedcouponSearchService;
    @Autowired
    CashierSearchOrderService cashierSearchOrderService;


    @Before
public void before() throws Exception {
    mockCashierController = EasyMock.createMock(CashierOrderSettlementController.class);
    request = EasyMock.createMock(HttpRequest.class);
    response = EasyMock.createMock(HttpResponse.class);
}

@After
public void after() throws Exception {
}

/**
*
* Method: getAllPayAbility(HttpRequest request, HttpResponse response)
*
*/
@Test
public void testGetAllPayAbility() throws Exception {
    //mockCashierController.getAllPayAbility();
}

/**
*
* Method: payOrderTrade(HttpRequest request, HttpResponse response)
*
*/
@Test
public void testPayOrderTrade() throws Exception {
//TODO: TestCache goes here...
}

/**
*
* Method: thirdPartyCallBack(HttpRequest request, HttpResponse response)
*
*/
@Test
public void testThirdPartyCallBack() throws Exception {
//TODO: TestCache goes here...
}

/**
*
* Method: getPayResult(HttpRequest request, HttpResponse response)
*
*/
@Test
public void testGetPayResult() throws Exception {
//TODO: TestCache goes here...
}
    @Test
    public void testVoucherConsume() throws Exception {
        VouchersPaymentParams vouchersPaymentParams = new VouchersPaymentParams();
        vouchersPaymentParams.setOrderNo("ml861201910172020260341321");
        vouchersPaymentParams.setEmployeeId("5c11ca2c23445f5b632c9635");
        List<VoucherPayDTO> voucherPayList = Lists.newArrayList(new VoucherPayDTO("FBQ191017201745745032129",new BigDecimal(1750),new BigDecimal(250),"5c107d8523445f330630d1d4"));
        vouchersPaymentParams.setVoucherPayList(voucherPayList);
        vouchersPaymentParams.setCompanyId("5c107d8523445f330630d1d4");
        vouchersPaymentParams.setVoucherTypeList(Lists.newArrayList("0202"));
        vouchersPaymentParams.setOrderSnapshot("共1件商品");
        vouchersPaymentParams.setAccountType(2);
        vouchersPaymentParams.setFbOrderId("OML201910172020253206030");
        vouchersPaymentParams.setBusinessType(20);

        vouchersPersonService.vouchersPayment(vouchersPaymentParams);
    }
    @Test
    public void testVoucherRefund() throws Exception {
        VouchersRefundParams vouchersRefundParams = new VouchersRefundParams();
        List<VoucherRefundDTO> refundVoucherList = Lists.newArrayList(new VoucherRefundDTO("FBQ191017201745745032129",new BigDecimal(250),BigDecimal.ZERO,null,null,null,"5c107d8523445f330630d1d4"));
        vouchersRefundParams.setRefundVoucherList(refundVoucherList);
        vouchersRefundParams.setTotalRefundAmount(new BigDecimal(250));
        vouchersRefundParams.setCompanyId("5c107d8523445f330630d1d4");
        vouchersRefundParams.setOrderNo("OML201910172020253206030");
        vouchersRefundParams.setVoucherTypeList(null);
        vouchersRefundParams.setOrderSnapshot("共1件商品");
        vouchersRefundParams.setAccountType(2);
        vouchersRefundParams.setFbOrderId("OML201910172020253206030");
        vouchersRefundParams.setBusinessType(20);
        vouchersPersonService.vouchersRefund(vouchersRefundParams);
    }
    @Test
    public void searchFlow(){
        CashierPayDetailRPCVo v = new CashierPayDetailRPCVo();
        v.setFbOrderId("ORL202009221106188918502");
        CashierPayDetailRPCDTO dto = iCashierSearchOrderService.searchPayDetail(v);
        System.out.println(dto);
        CashierPayDetailRPCDTO cashierPay = iCashierSearchOrderService.searchPayFlowDetail(v);
        System.out.println(cashierPay);

    }
    @Test
    public void testTrans(){
        AccountSubAbleReqRPCDTO dto = new AccountSubAbleReqRPCDTO();
        dto.setCompanyId("5769f2dc25282207eea5a11a");
        dto.setAccountModel(2);
        dto.setAccountSubType(2);
        iAccountSubService.activityAccountSub(dto);
    }
    @Test
    public void t(){

        AccountRedcouponSearchReqRPCDTO redcouponSearchReqRPCDTO = new AccountRedcouponSearchReqRPCDTO();
        redcouponSearchReqRPCDTO.setCompanyId("5f6ffbc627f65fbad1610472");
        redcouponSearchReqRPCDTO.setPageNo(1);
        redcouponSearchReqRPCDTO.setPageSize(10);
        ResponsePage<AccountRedcouponRespRPCDTO> r =  iAccountRedcouponSearchService.queryAccountCouponList(redcouponSearchReqRPCDTO);
        System.out.println(r);
}

    @Test
    public void cashierSearchOrder(){

        CashierPayDetailRPCVo vo = new CashierPayDetailRPCVo();
        vo.setFbOrderId("OBP202011111143594210568");
        vo.setOrderRootType(1);
        CashierPayDetailRPCDTO dto = cashierSearchOrderService.searchCashierPayDetail(vo);
        System.out.println(dto);
        CashierPayDetailRPCDTO d = cashierSearchOrderService.searchCashierPayFlowDetail(vo.getFbOrderId());
        System.out.println(d);

    }
}
