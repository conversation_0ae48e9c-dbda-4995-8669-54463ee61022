package com.fenbeitong.fenbeipay.cashier.settlement.impl;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierRefundWay;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierTradeReliefRPCVo;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundTrade4ReimbursementReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundTradeReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundTradeRespVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;

public class CashierOrderRefundSettlementServiceImplTest extends SpringBaseHttpTest {


    @Autowired
    private CashierOrderRefundSettlementService cashierOrderRefundSettlementService;

    @Test
    public void testCreateRefundTradeAndCallBiz() throws Exception {
        CashierRefundTradeReqVo cashierRefundTradeReqVo = new CashierRefundTradeReqVo();
        cashierRefundTradeReqVo.setFbOrderId("OML201905131815122553698");
        cashierRefundTradeReqVo.setCompanyId("5747fbc10f0e60e0709d8d7d");
        cashierRefundTradeReqVo.setEmployeeId("5bd2e87e23445f43f9dfc96d");
        cashierRefundTradeReqVo.setCashierTxnId("ml861201905131815124443940");
        cashierRefundTradeReqVo.setTotalRefundAmount(new BigDecimal(50));
        cashierRefundTradeReqVo.setCompanyRefundAmount(new BigDecimal(0));
        cashierRefundTradeReqVo.setPersonalRefundAmount(new BigDecimal(50));
        cashierRefundTradeReqVo.setPersonalThirdRefundPrice(new BigDecimal(0));
        cashierRefundTradeReqVo.setPersonFbbRefundPrice(new BigDecimal(50));
        cashierRefundTradeReqVo.setPersonVouchersRefundPrice(new BigDecimal(0));
        cashierRefundTradeReqVo.setBizCallbackUrl("http://noc-dev.fenbeijinfu.com/internal/noc/biz/mall/callback/refund/v2");
        cashierRefundTradeReqVo.setRefundReason("Test退款");
        CashierRefundTradeRespVo cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTradeAndSaas(cashierRefundTradeReqVo);
    }



    @Test
    public void testCreateRefundTrade4ReliefOrSaas() throws Exception {
        CashierTradeReliefRPCVo cashierTradeReliefRPCVo = CashierTradeReliefRPCVo.builder()
                .cashierPublicRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey())
                .cashierRefundWay(1)
                .bizCallbackUrl("localhost")
                .costAttributionCategory(1)
                .companyId("5d679114bfd57c1de10aa8fa")
                .costAttributionId("5bf263b923445f533561234c")
                .costAttributionTime(new Date())
                .customerServiceId("121")
                .customerServiceName("涛皮儿")
                .employeeId("5d679114bfd57c1de10aa8fb")
                .fbOrderId("ORL202009281741074059807")
                .fbOrderName("火车无座补偿,11111,5f6d9413e4b05d5435ddf5bf")
                .operationChannelType(2)
                .orderType(7)
                .orderSubType(912)
                .personalRefundAmount(BigDecimal.ZERO)
                .publicRefundAmount(BigDecimal.valueOf(123))
                .refundOrderId("ORL202009281741074059807")
                .refundReason("火车无座补偿,11111,5dfc68d823445f2625b28536")
                .totalRefundAmount(BigDecimal.valueOf(123))
                .remark("5f6d9413e4b05d5435ddf5bf")
                .build();
        CashierRefundTradeRespVo cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTrade4ReliefOrSaas(cashierTradeReliefRPCVo,true);
    }

    @Test
    public void createRefundTrade4ReliefOrSaasByDefaultWay() throws Exception {
        CashierTradeReliefRPCVo cashierTradeReliefRPCVo = CashierTradeReliefRPCVo.builder()
                .cashierPublicRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey())
                .cashierRefundWay(1)
                .bizCallbackUrl("localhost")
                .costAttributionCategory(1)
                .companyId("6295f3085bae80679c592963")
                .costAttributionId("5bf263b923445f533561234c")
                .costAttributionTime(new Date())
                .customerServiceId("121")
                .customerServiceName("涛皮儿")
                .employeeId("6487da5d8d167d3f6bcb634e")
                .fbOrderId("66b3116ce4b0d0da6b459c74")
                .fbOrderName("林永超国内机票")
                .operationChannelType(2)
                .orderType(7)
                .orderSubType(0)
                .refundReason("火车无座补偿,11111,5dfc68d823445f2625b28536")
                .totalRefundAmount(BigDecimal.valueOf(72000))
                .remark("66b3116ce4b0d0da6b459c74")
                .build();
        CashierRefundTradeRespVo cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTrade4ReliefOrSaasByDefaultWay(cashierTradeReliefRPCVo,true);
    }


    @Test
    public void refundTrade4Reimbursement(){
        /**
         * {
         *     "bankAccountNo": "0356000005807927678023797",
         *     "bankName": "ZBBANK",
         *     "cashierRefundType": 1,
         *     "cashierTxnId": "ap501202206071514276063057",
         *     "companyId": "6232916b37fdaf5793859db3",
         *     "companyRefundAmount": 480000,
         *     "employeeId": "623291a90f0dc605dca3d817",
         *     "fbOrderId": "OPP220607151427352975836",
         *     "personalRefundAmount": 0,
         *     "publicRefundAmount": 480000,
         *     "refundOrderId": "OPP220607151432119316344",
         *     "refundReason": "账户状态异常:交易处理失败",
         *     "totalRefundAmount": 480000
         * }
         */
        //6239cda46043f741aeffe414,5b91427b23445f1bccbaf2f7
        CashierRefundTrade4ReimbursementReqVo cashierRefundTrade4ReimbursementReqVo = new CashierRefundTrade4ReimbursementReqVo();
        cashierRefundTrade4ReimbursementReqVo.setCashierRefundType(1);
        cashierRefundTrade4ReimbursementReqVo.setCompanyRefundAmount(new BigDecimal("1"));
        cashierRefundTrade4ReimbursementReqVo.setPersonalThirdRefundPrice(BigDecimal.ZERO);
        cashierRefundTrade4ReimbursementReqVo.setPersonFbbRefundPrice(BigDecimal.ZERO);
        cashierRefundTrade4ReimbursementReqVo.setPersonVouchersRefundPrice(BigDecimal.ZERO);
        cashierRefundTrade4ReimbursementReqVo.setRedcouponRefundAmount(BigDecimal.ZERO);
        cashierRefundTrade4ReimbursementReqVo.setCashierRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey());
        cashierRefundTrade4ReimbursementReqVo.setRefundReason("付款失败");
        cashierRefundTrade4ReimbursementReqVo.setRefundOrderId("REI1281");
        cashierRefundTrade4ReimbursementReqVo.setFbOrderId("REI128");
        cashierRefundTrade4ReimbursementReqVo.setBankAccountNo("3110730027033151286");
        cashierRefundTrade4ReimbursementReqVo.setBankName("CITIC");
        cashierRefundTrade4ReimbursementReqVo.setTotalRefundAmount(new BigDecimal("100"));
        cashierRefundTrade4ReimbursementReqVo.setEmployeeId("6239cda46043f741aeffe414");
        cashierRefundTrade4ReimbursementReqVo.setCompanyId("5b91427b23445f1bccbaf2f7");


        cashierOrderRefundSettlementService.refundTrade4Reimbursement(cashierRefundTrade4ReimbursementReqVo);
    }


}