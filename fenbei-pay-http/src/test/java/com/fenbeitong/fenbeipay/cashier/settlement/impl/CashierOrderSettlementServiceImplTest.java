package com.fenbeitong.fenbeipay.cashier.settlement.impl;

import com.fenbeitong.fenbeipay.account.person.PersonAccountService;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderType;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CostAttributionRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.CashierPayCommonJsonDto;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountExtMapper;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;
import com.luastar.swift.base.json.JsonUtils;
import org.easymock.EasyMock;
import org.easymock.IMocksControl;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

import static com.fenbeitong.fenbeipay.common.mock.MockCashierCreateTradeReqVo.mock_create_wrong_paid;
import static com.fenbeitong.fenbeipay.common.mock.MockCashierPayTradeReqVo.mockPay_success_因私打车300_public0_fbb60_fbq100_third140;

/**
 * CashierOrderSettlementServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Nov 26, 2018</pre>
 */
public class CashierOrderSettlementServiceImplTest extends SpringBaseHttpTest {

    @Autowired
    private CashierOrderSettlementService cashierOrderSettlementService;

    @Autowired
    private PersonAccountExtMapper personAccountExtMapper;
    @Autowired
    CashierSearchOrderService cashierSearchOrderService;
    @Before
    public void before() throws Exception {
        IMocksControl iMocksControl = EasyMock.createControl();
       // cashierOrderSettlementMapper = iMocksControl.createMock(CashierOrderSettlementMapper.class)
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: getAllPayAbility(PersonCashierBaseVo personCashierBaseVo)
     */
    @Test
    public void testGetAllPayAbility() throws Exception {
        PersonPayAbilityReqVo personCashierBaseVo = new PersonPayAbilityReqVo();
        PersonPayAbilityRespVo personPayAbilityVo = cashierOrderSettlementService.getAllPayAbility(personCashierBaseVo);
        Assert.assertTrue(personPayAbilityVo.getFbbBalance().compareTo(BigDecimal.ZERO)>0);
    }

    /**
     * Method: createOrderTrade(CashierCreateTradeReqVo cashierCreateTradeReqVo)
     */
    @Test
    public void testCreateOrderTrade() throws Exception {
        CashierCreateTradeReqVo cashierCreateTradeReqVo = mock_create_wrong_paid();
        CashierCreateTradeRespVo cashierCreateTradeRespVo = cashierOrderSettlementService.createOrderTradeAndSaas(cashierCreateTradeReqVo);
        System.out.println(cashierCreateTradeRespVo.getCashierTxnId());
        Assert.assertNotNull(cashierCreateTradeRespVo.getCashierTxnId());
    }

    /**
     * Method: payOrderTrade(CashierPayTradeReqVo cashierPayTradeReqVo)
     */
    @Test
    public void testPayOrderTrade() throws Exception {
        CashierPayTradeReqVo cashierPayTradeReqVo = mockPay_success_因私打车300_public0_fbb60_fbq100_third140();
        CashierPayTradeRespVo cashierPayTradeRespVo = cashierOrderSettlementService.payOrderTradeAndSaas(cashierPayTradeReqVo);
        System.out.println(cashierPayTradeRespVo.getCashierTxnId());
        Assert.assertNotNull(cashierPayTradeRespVo.getCashierTxnId());
    }

    /**
     * Method: callBiz4CashierHadPaid(String fbOrderId, String cashierTxnId, String bizCallbackUrl)
     */
    @Test
    public void testCallBiz4CashierHadPaid() throws Exception {
        PersonAccountService personAccountService=new PersonAccountService();
        BigDecimal decimal=new BigDecimal("0.12");
        int cout = personAccountExtMapper.updateAccountById(decimal, "5c11cc7023445f2a0142540d");
    }

    /**
     * Method: thirdPartyCallBack(String channel, HttpRequest request, HttpResponse response)
     */
    @Test
    public void testThirdPartyCallBack() throws Exception {
//TODO: TestCache goes here...
    }

    /**
     * Method: getPayResult(CashierQueryCommonReqVo cashierQueryCommonReqVo)
     */
    @Test
    public void testGetPayResult() throws Exception {
//TODO: TestCache goes here...
    }

    /**
     * Method: selectCashierSettlementByPayStatus(Integer status)
     */
    @Test
    public void testGetPayResultListByStatus() throws Exception {
//TODO: TestCache goes here...
    }

    /**
     * Method: updateCashierOrderSettlement4HadPay(CashierPayTradeReqVo cashierPayTradeReqVo, CashierOrderSettlement cashierOrderSettlement, PersonAccount personAccount, CashierPayStatus cashierPayStatus)
     */
    @Test
    public void testUpdateCashierOrderSettlement4HadPay() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("updateCashierOrderSettlement4HadPay", CashierPayTradeReqVo.class, CashierOrderSettlement.class, PersonAccount.class, CashierPayStatus.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: updateCashierOrderSettlement4HadDone(String fbOrderId, String cashierTxnId, CashierPayStatus cashierPayStatus)
     */
    @Test
    public void testUpdateCashierOrderSettlement4HadDone() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("updateCashierOrderSettlement4HadDone", String.class, String.class, CashierPayStatus.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: updateCashierOrderSettlement2Pay(CashierPayTradeReqVo cashierPayTradeReqVo, CashierOrderSettlement cashierOrderSettlement, PersonAccount personAccount, CashierPayStatus cashierPayStatus)
     */
    @Test
    public void testUpdateCashierOrderSettlement2Pay() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("updateCashierOrderSettlement2Pay", CashierPayTradeReqVo.class, CashierOrderSettlement.class, PersonAccount.class, CashierPayStatus.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: checkCashierStatus(Integer payStatus)
     */
    @Test
    public void testCheckCashierStatus() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("checkCashierStatus", Integer.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: checkCreateCashierTradeSettle(CashierCreateTradeReqVo cashierCreateTradeReqVo)
     */
    @Test
    public void testCheckCreateCashierTradeSettle() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("checkCreateCashierTradeSettle", CashierCreateTradeReqVo.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: checkCashierTradeSettle(CashierPayTradeReqVo cashierPayTradeReqVo)
     */
    @Test
    public void testCheckCashierTradeSettle() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("checkCashierTradeSettle", CashierPayTradeReqVo.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: saveCashierOrderSettlement(CashierCreateTradeReqVo cashierCreateTradeReqVo, String cashierTxnId, PersonAccount personAccount)
     */
    @Test
    public void testSaveCashierOrderSettlement() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("saveCashierOrderSettlement", CashierCreateTradeReqVo.class, String.class, PersonAccount.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: launchThirdPartyPay(CashierPayTradeReqVo cashierPayTradeReqVo, String cashierTxnId, OrderType orderType, PersonAccount personAccount)
     */
    @Test
    public void testLaunchThirdPartyPay() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("launchThirdPartyPay", CashierPayTradeReqVo.class, String.class, OrderType.class, PersonAccount.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleThirdPartyAndPay(CashierPayTradeReqVo cashierPayTradeReqVo, String cashierTxnId, PersonAccount personAccount, String thirdPartyPayTxnId)
     */
    @Test
    public void testSettleThirdPartyAndPay() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleThirdPartyAndPay", CashierPayTradeReqVo.class, String.class, PersonAccount.class, String.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleFBBAndPay(CashierPayTradeReqVo cashierPayTradeReqVo, String cashierTxnId, PersonAccount personAccount, String fBBPayTxnId)
     */
    @Test
    public void testSettleFBBAndPay() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleFBBAndPay", CashierPayTradeReqVo.class, String.class, PersonAccount.class, String.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleCompanyAndPay(CashierCreateTradeReqVo cashierCreateTradeReqVo, String cashierTxnId, PersonAccount personAccount, String companyPayTxnId)
     */
    @Test
    public void testSettleCompanyAndPayForCashierCreateTradeReqVoCashierTxnIdPersonAccountCompanyPayTxnId() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleCompanyAndPay", CashierCreateTradeReqVo.class, String.class, PersonAccount.class, String.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleCompanyAndPay(CashierPayTradeReqVo cashierPayTradeReqVo, String cashierTxnId, PersonAccount personAccount, String companyPayTxnId)
     */
    @Test
    public void testSettleCompanyAndPayForCashierPayTradeReqVoCashierTxnIdPersonAccountCompanyPayTxnId() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleCompanyAndPay", CashierPayTradeReqVo.class, String.class, PersonAccount.class, String.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleFBQAndPay(String cashierTxnId, OrderType orderType, CashierPayTradeReqVo cashierPayTradeReqVo)
     */
    @Test
    public void testSettleFBQAndPay() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleFBQAndPay", String.class, OrderType.class, CashierPayTradeReqVo.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleCompanyPay(String cashierTxnId, CashierPayTradeReqVo cashierPayTradeReqVo, PersonAccount personAccount)
     */
    @Test
    public void testSettleCompanyPayForCashierTxnIdCashierPayTradeReqVoPersonAccount() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleCompanyPay", String.class, CashierPayTradeReqVo.class, PersonAccount.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleCompanyPay(String cashierTxnId, CashierCreateTradeReqVo cashierCreateTradeReqVo, PersonAccount personAccount)
     */
    @Test
    public void testSettleCompanyPayForCashierTxnIdCashierCreateTradeReqVoPersonAccount() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleCompanyPay", String.class, CashierCreateTradeReqVo.class, PersonAccount.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleCompanyPay(String cashierTxnId, String companyId, BigDecimal companyActPayPrice, PersonAccount personAccount)
     */
    @Test
    public void testSettleCompanyPayForCashierTxnIdCompanyIdCompanyActPayPricePersonAccount() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleCompanyPay", String.class, String.class, BigDecimal.class, PersonAccount.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleThirdPart(String cashierTxnId, String thirdPartyPayTxnId, CashierPayTradeReqVo cashierPayTradeReqVo, PersonAccount personAccount)
     */
    @Test
    public void testSettleThirdPart() throws Exception {
//TODO: TestCache goes here...
/* 
try { 
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleThirdPart", String.class, String.class, CashierPayTradeReqVo.class, PersonAccount.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: settleFBB(String cashierTxnId, CashierPayTradeReqVo cashierPayTradeReqVo, PersonAccount personAccount)
     */
    @Test
    public void testSettleFBB() throws Exception {
//TODO: TestCache goes here...
/*
try {
   Method method = CashierOrderSettlementServiceImpl.getClass().getMethod("settleFBB", String.class, CashierPayTradeReqVo.class, PersonAccount.class);
   method.setAccessible(true);
   method.invoke(<Object>, <Parameters>);
} catch(NoSuchMethodException e) {
} catch(IllegalAccessException e) {
} catch(InvocationTargetException e) {
}
*/
    }
    @Test
    public void cost(){

        List<CostAttributionRPCDTO> dto =  cashierSearchOrderService.searchCostAttributionByOrderId("ORL202009021440376226420");
        System.out.println(JsonUtils.toJson(dto));
    }

    @Test
    public void createAndPayTrade4Reimbursement(){
        CashierPayTrade4ReimbursementReqVO cashierPayTrade4ReimbursementReqVO = new CashierPayTrade4ReimbursementReqVO();
        cashierPayTrade4ReimbursementReqVO.setOrderType(OrderType.REIMBURSEMENT_PAY.getKey());
        cashierPayTrade4ReimbursementReqVO.setAmountBank(new BigDecimal("100"));
        cashierPayTrade4ReimbursementReqVO.setCompanyPayPrice(new BigDecimal("100"));
        cashierPayTrade4ReimbursementReqVO.setCompanyId("5b91427b23445f1bccbaf2f7");
        cashierPayTrade4ReimbursementReqVO.setEmployeeId("6239cda46043f741aeffe414");
        cashierPayTrade4ReimbursementReqVO.setBankName(BankNameEnum.CITIC.getCode());
        cashierPayTrade4ReimbursementReqVO.setFbOrderName("报销打款");
        cashierPayTrade4ReimbursementReqVO.setBankAccountNo("3110730027033151286");
        CashierPayCommonJsonDto commonJsonDto = new CashierPayCommonJsonDto();
        commonJsonDto.setRemark("测试");
        cashierPayTrade4ReimbursementReqVO.setCommonJson(commonJsonDto);
        cashierPayTrade4ReimbursementReqVO.setFbOrderSnapshot("test");
        cashierPayTrade4ReimbursementReqVO.setSellerAcctBankCode("3110730027033195484");
        cashierPayTrade4ReimbursementReqVO.setSellerBankAcctName("北京分贝志远管理咨询合伙企业（有限合");
        cashierPayTrade4ReimbursementReqVO.setSellerBankBrnNo("************");
        cashierPayTrade4ReimbursementReqVO.setSellerBankName("111");
        cashierPayTrade4ReimbursementReqVO.setSellerBankNo("***************");
        cashierPayTrade4ReimbursementReqVO.setFbOrderId("REI141");
        cashierPayTrade4ReimbursementReqVO.setTotalPayPrice(new BigDecimal("100"));
        cashierPayTrade4ReimbursementReqVO.setSellerBankBrnNo("************");
        cashierOrderSettlementService.createAndPayTrade4Reimbursement(cashierPayTrade4ReimbursementReqVO);
    }
} 
