package com.fenbeitong.fenbeipay.cashier.settlement;

import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.*;

public class CashierOrderSettlementServiceTest extends SpringBaseHttpTest {

    @Autowired
    CashierOrderSettlementService cashierOrderSettlementService;

    @Test
    public void getAllPayAbility() {
    }

    @Test
    public void myWallet() {
    }

    @Test
    public void myFbbWallet() {
    }

    @Test
    public void myVoucher() {
    }

    @Test
    public void createOrderTradeAndSaas() {
    }

    @Test
    public void payOrderTradeAndSaas() {
    }

    @Test
    public void payThirdTradeAndCallBiz() {
    }

    @Test
    public void changePriceOrderTrade() {
    }

    @Test
    public void createAndPayOrderTradeOrSass() {
    }

    @Test
    public void createAndPayOrderTrade4NoEmployee() {
    }

    @Test
    public void createAndAutoPayOrderTrade4Private() {
    }

    @Test
    public void checkPayStatusAndCallBizByFbOrderId() {
    }

    @Test
    public void checkPayStatusAndCallBiz() {
    }

    @Test
    public void testCheckPayStatusAndCallBiz() {
    }

    @Test
    public void checkRefundStatusAndCallBiz() {
    }

    @Test
    public void thirdPartyCallBack() {
    }

    @Test
    public void calculateAndSettlePublicAndPay4XeRefund() {
    }

    @Test
    public void payBQAbility() {
    }

    @Test
    public void notifyThirdPartyPayResult() {
    }

    @Test
    public void cancelOrderTrade() {
        List<CashierOrderSettlement> cashierOrderSettlements =  cashierOrderSettlementService.queryCashierCanCancel();
        if (CollectionUtils.isNotEmpty(cashierOrderSettlements)) {
            Assert.assertTrue(cashierOrderSettlements.size()>0);
        }else {
            assertNull(cashierOrderSettlements);
        }
    }

    @Test
    public void cancelOrderTradeAndRefundSaas() {
    }

    @Test
    public void cronPullThirdResult() {
    }

    @Test
    public void cronPullBankCardResult() {
    }

    @Test
    public void cronCancelOrderTrade() {
    }

    @Test
    public void cronCallBiz4HadDone() {
    }

    @Test
    public void cronCallNotEnough() {
    }

    @Test
    public void batchQuery() {
    }

    @Test
    public void fqlSuccessCashierSettlement() {
    }

    @Test
    public void createTrade4XeReliefOrSass() {
    }

    @Test
    public void csUpdateCashier() {
    }

    @Test
    public void payOrderTradeOrSass4CS() {
    }

    @Test
    public void createAndPayBankTradeOrSaas() {
    }

    @Test
    public void queryCashierDetail() {
    }

    @Test
    public void queryCashierOrderSettlement() {
    }

    @Test
    public void queryCashierCanCancel() {
    }

    @Test
    public void selectCashierSettlementByFbOrderIdAndEmpId() {
    }

    @Test
    public void selectCashierSettlementByFbOrderIdAndEmpIdAndTradeId() {
    }

    @Test
    public void synPayOrderTrade() {
    }

    @Test
    public void createOrderAcctPublicPayTrade() {
    }

    @Test
    public void updateCostUnitPath() {
    }

    @Test
    public void callCashierSettlement() {
    }

    @Test
    public void selectCashierSettlementPayByCashierTxnIdAndPayChannel() {
    }

    @Test
    public void updateCashierOrderSettlement() {
    }

    @Test
    public void updateCashierOrderSettlementPay() {
    }

    @Test
    public void getOriginal4ReliefTrade() {
    }

    @Test
    public void createAndPayTrade4Reimbursement() {
    }

    @Test
    public void createAndPayOrderTrade4NoEmployee4OA() {
    }

    @Test
    public void updateCashierOrderSettlement4HaveDone() {
    }

    @Test
    public void selectPageByExample() {
    }
}