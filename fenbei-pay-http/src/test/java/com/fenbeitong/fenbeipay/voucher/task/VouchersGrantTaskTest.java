package com.fenbeitong.fenbeipay.voucher.task;

import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskMapper;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.sass.budget.service.api.dubbo.BudgetRuleDubboServiceFacade;
import com.fenbeitong.sass.budget.service.api.dubbo.response.BudgetRuleResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/1/29 10:13
 * @description:
 */
@Slf4j
public class VouchersGrantTaskTest  extends SpringBaseHttpTest {

    @Autowired
    private VouchersTaskMapper vouchersTaskMapper;
    @Autowired
    private BudgetRuleDubboServiceFacade budgetRuleDubboServiceFacade;

    @Test
    public void testInsertTask(){
        VouchersTask dto = new VouchersTask();
        dto.setId(122132323L);
        dto.setBizNo("1232343uuut");
        dto.setVouchersTaskId("1232343uuut");
        dto.setVouchersTaskType(1);
        dto.setVouchersTaskName("测试");
        dto.setVouchersNumber(1L);
        dto.setGrantType(1);
        dto.setTaskDesc("test");
        CostInfo costInfoContract = new CostInfo();
        //costInfoContract.setCostCategory(new CostInfoContract.CostCategory());
        dto.setCostInfo("test");
        int i = vouchersTaskMapper.insertSelective(dto);
        System.out.println(i);
    }

    @Test
    public void getCostConfig(){
        String companyId = "5747fbc10f0e60e0709d8d7d";
        try {
            log.info("分贝券预算配置查询request, companyId={}", companyId);
            BudgetRuleResponse budgetRuleResponse = budgetRuleDubboServiceFacade.queryBudgetRuleByCompanyId(companyId);
            log.info("分贝券预算配置查询, companyId={}, response={}", companyId, budgetRuleResponse);
        }catch (Exception e){
            log.warn("分贝券预算配置查询失败, companyId={}, e={}", companyId, e);
            throw new FinPayException(GlobalResponseCode.VOUCHER_COST_QUERY_ERROR);
        }
    }



}
