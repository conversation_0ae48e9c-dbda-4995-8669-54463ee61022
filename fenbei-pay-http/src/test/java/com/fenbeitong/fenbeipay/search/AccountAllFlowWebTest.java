package com.fenbeitong.fenbeipay.search;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.vo.sas.SearchVirtualAccountFlowReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.SearchVirtualAccountFlowRespVo;
import com.fenbeitong.fenbeipay.dto.flow.AccountAllFlow;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.pay.search.dbf.mapper.AccountAllFlowMapper;
import com.fenbeitong.pay.search.service.AccountVirtualCardFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
public class AccountAllFlowWebTest extends SpringBaseHttpTest {

    @Autowired
    AccountAllFlowMapper accountAllFlowMapper;

    @Autowired
    private AccountVirtualCardFlowService accountVirtualCardFlowService;

    @Test
    public void testQuery(){
        Example example = new Example(AccountAllFlow.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("flowFlag",6);
        criteria.andEqualTo("accountFlowId","FCB202201010020546888234");
        RowBounds rowBounds = new RowBounds(0,10);
        int count = accountAllFlowMapper.selectCountByExample(example);
        List<AccountAllFlow> select = accountAllFlowMapper.selectByExampleAndRowBounds(example,rowBounds);
        System.out.println(JSONObject.toJSON(select));
    }

    @Test
    public void testQuery2(){
        SearchVirtualAccountFlowReqVo reqVo=new SearchVirtualAccountFlowReqVo();
        reqVo.setCompanyId("5b9b767c23445f5a69d5f665");
        reqVo.setBankName("FBT");
        reqVo.setBankAccountNo("5ff5644ca61410555818691b");
        reqVo.setAccountModel(2);
        reqVo.setTradeType(222);
        reqVo.setOperationType(80);
        reqVo.setStartTime("2021-01-01 08:19:22");
        reqVo.setEndTime("2022-01-31 08:19:22");
        reqVo.setAccountFlowId("FCB202201010819224623234");
        reqVo.setOperationUserName("高金渤");
        reqVo.setCostImageStatus(0);
        reqVo.setFirstDeptId("11");
        reqVo.setLastDeptId("222");
        reqVo.setPageNo(0);
        reqVo.setPageSize(10);
        ResponsePage<SearchVirtualAccountFlowRespVo> page= accountVirtualCardFlowService.searchVirtualAccountFlowByCompany(reqVo);
        System.out.println(JSONObject.toJSON(page));
    }
}
