package com.fenbeitong.fenbeipay.search;

import cn.hutool.json.JSONUtil;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.utils.ObjectUtil;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.BankAcctInfoRespDTO;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementExample;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessCreditFlow;
import com.fenbeitong.fenbeipay.dto.flow.AccountAllFlow;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.pay.search.dbf.mapper.AccountAllFlowMapper;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
@Slf4j
public class AccountAllFlowTest extends SpringBaseHttpTest {

    @Autowired
    AccountAllFlowMapper accountAllFlowMapper;

    @Autowired
    AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Autowired
    CashierOrderSettlementService cashierOrderSettlementService;

    @Autowired
    private AccountGeneralService accountGeneralService;

    @Test
    public void testQuery(){
        List<AcctBusinessCreditFlow> list = acctBusinessCreditFlowService.queryAllFlowByTime("5747fbc10f0e60e0709d8d7d", DateUtil.getDateFromString("2022-05-30 00:35:35", "yyyy-MM-dd HH:mm:ss"), DateUtil.getDateFromString("2022-05-31 23:35:35", "yyyy-MM-dd HH:mm:ss"));
        System.out.println(JsonUtils.toJson(list));

//        CashierOrderSettlementExample example = new CashierOrderSettlementExample();
//        List<CashierOrderSettlement> list1 = cashierOrderSettlementService.selectPageByExample(example);
//        System.out.println("=========="+JsonUtils.toJson(list1).substring(20));

    }

    @Test
    public void test(){
        List<AccountGeneral> generalFlowList = accountGeneralService.findGeneralByBankName(BankNameEnum.LFBANK.getCode());
        System.out.println(JsonUtils.toJson(ObjectUtil.newInstanceList(generalFlowList, BankAcctInfoRespDTO.class)));

    }
}
