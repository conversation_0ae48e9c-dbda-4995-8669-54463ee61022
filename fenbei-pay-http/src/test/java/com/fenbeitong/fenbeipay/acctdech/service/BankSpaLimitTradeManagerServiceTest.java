package com.fenbeitong.fenbeipay.acctdech.service;

import com.fenbeitong.fenbeipay.api.model.vo.bank.SpaBankAcctLimitReqVO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-05-18 11:00 上午
 */

public class BankSpaLimitTradeManagerServiceTest extends SpringBaseHttpTest {
    @Autowired
    BankSpaBankAcctLimitService bankSpaBankAcctLimitService;

    @Test
    public void addCount() {
        SpaBankAcctLimitReqVO spaBankAcctLimitReqVO = new SpaBankAcctLimitReqVO();
        spaBankAcctLimitReqVO.setBankAccountNo("124");
        spaBankAcctLimitReqVO.setAgreementNo("124");
        bankSpaBankAcctLimitService.createBankAcctLimit(spaBankAcctLimitReqVO);
    }
}
