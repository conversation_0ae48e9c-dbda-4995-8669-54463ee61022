package com.fenbeitong.fenbeipay.acctdech.service;

import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-05-18 11:00 上午
 */

public class AcctReimbursementCountServiceTest extends SpringBaseHttpTest {
    @Autowired
    AcctReimbursementCountService acctReimbursementCountService;

    @Test
    public void addCount() {
        acctReimbursementCountService.addCount("wcj_test", "CGB");
    }

    @Test
    public void subtractCount() {
        acctReimbursementCountService.subtractCount("wcj_test", "CGB");
    }


    @Test
    public void getCount() {
        Integer wcj_test = acctReimbursementCountService.getCountByEmployeeId("wcj_test", "CGB");
        System.out.println(wcj_test);
    }
}
