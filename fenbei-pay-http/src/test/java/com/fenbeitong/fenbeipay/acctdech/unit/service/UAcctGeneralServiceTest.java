package com.fenbeitong.fenbeipay.acctdech.unit.service;

import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AcctTransferSaveReqDTO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class UAcctGeneralServiceTest extends SpringBaseHttpTest {
    @Autowired
    UAcctGeneralService uAcctGeneralService;
    @Test
    public void defaultTransferAccount() {
        AcctTransferSaveReqDTO reqDTO = new AcctTransferSaveReqDTO();
        reqDTO.setDefaultTransferAccountType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        reqDTO.setGeneralId("AGA201903211948310006457");
        uAcctGeneralService.defaultTransferAccount(reqDTO);
    }
}