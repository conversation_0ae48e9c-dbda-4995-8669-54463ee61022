package com.fenbeitong.fenbeipay.acctdech.unit.service;

import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCreateReimbursementReqDTO;
import com.fenbeitong.fenbeipay.common.SpringBaseHttpTest;
import com.fenbeitong.finhub.common.constant.FundAccountModelType;
import com.fenbeitong.finhub.common.constant.FundAcctStatusEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/5/17
 */
public class UAcctReimbursementServiceTest extends SpringBaseHttpTest {
    @Autowired
    UAcctReimbursementService uAcctReimbursementService;

    @Test
    public void createAccount(){
        AcctCreateReimbursementReqDTO acctCreateReimbursementReqDTO = new AcctCreateReimbursementReqDTO();
        acctCreateReimbursementReqDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
        acctCreateReimbursementReqDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
        acctCreateReimbursementReqDTO.setAccountGeneralId("113");
        acctCreateReimbursementReqDTO.setBankAccountNo("113");
        acctCreateReimbursementReqDTO.setBankAcctId("113");
        acctCreateReimbursementReqDTO.setActiveStatus(1);
        acctCreateReimbursementReqDTO.setBankAccountAcctName("112");
        acctCreateReimbursementReqDTO.setCompanyId("1");
        acctCreateReimbursementReqDTO.setShowStatus(1);
        acctCreateReimbursementReqDTO.setOperationUserId("hl");
        acctCreateReimbursementReqDTO.setBankName("CGB");
        acctCreateReimbursementReqDTO.setCompanyMainId("112");
        acctCreateReimbursementReqDTO.setCompanyName("112");
        acctCreateReimbursementReqDTO.setCompanyModel(1);
        uAcctReimbursementService.establishAccount(acctCreateReimbursementReqDTO);
    }


}
