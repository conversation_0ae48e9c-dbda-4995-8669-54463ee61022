package com.fenbeitong.fenbeipay.rpc.service.extract;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.acctperson.api.enums.TradeOrderStatusEnum;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.BankUserCardSearchReqDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.BankUserCardInfoRespDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.TradeRespDTO;
import com.fenbeitong.acctperson.api.service.search.IBankUserCardSearchService;
import com.fenbeitong.acctperson.api.service.trade.IBankUserCardTradeService;
import com.fenbeitong.dech.api.enums.CgbTradeStatusEnum;
import com.fenbeitong.dech.api.model.dto.cgb.*;
import com.fenbeitong.dech.api.service.ICgbVirtualCardService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyCardService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.PersonAcctEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.extract.ProcessMethodEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.extract.ProcessStatusEnum;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctVcardBalanceSearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctVcardDiffProcessReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctVcardDiffSearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctVcardBalanceSearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctVcardDiffProcessRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctVcardDiffSearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctVcardDiffProcessService;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardMapper;
import com.fenbeitong.fenbeipay.bank.base.manager.IBankCardManager;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyCard;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.fenbeipay.dto.extract.AcctPersonBankCheck;
import com.fenbeitong.fenbeipay.dto.extract.AcctVcardDiffProcess;
import com.fenbeitong.fenbeipay.extract.db.mapper.AcctPersonBankCheckMapper;
import com.fenbeitong.fenbeipay.extract.db.mapper.AcctVcardDiffProcessMapper;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.fenbeitong.fenbeipay.core.utils.IDGen.CASH_IN;

/**
 * <AUTHOR>
 * @date 2023-01-03 下午12:14
 */
@Service("iAcctVcardDiffProcessService")
public class IAcctVcardDiffProcessServiceImpl implements IAcctVcardDiffProcessService {

    @Resource
    private ICgbVirtualCardService cgbVirtualCardService;

    @Resource
    private BankCardMapper bankCardMapper;

    @Resource
    private IBankCardManager iBankCardManager;

    @Resource
    private AcctVcardDiffProcessMapper acctVcardDiffProcessMapper;

    @Resource
    private AcctCompanyCardService acctCompanyCardService;

    @Resource
    private IBankUserCardSearchService bankUserCardSearchService;

    @Autowired
    private IBankUserCardTradeService iBankUserCardTradeService;

    @Resource
    private AcctPersonBankCheckMapper acctPersonBankCheckMapper;

    @Override
    public ResponsePage<AcctVcardDiffSearchRespRPCDTO> queryPage(AcctVcardDiffSearchReqRPCDTO searchReqRPCDTO) {
        FinhubLogger.info("iAcctVcardDiffProcessService queryPage param{}", JsonUtils.toJson(searchReqRPCDTO));
        ResponsePage<AcctVcardDiffSearchRespRPCDTO> page = new ResponsePage<AcctVcardDiffSearchRespRPCDTO>();
        Example example = buildQueryExample(searchReqRPCDTO);
        int count = acctVcardDiffProcessMapper.selectCountByExample(example);
        List<AcctVcardDiffSearchRespRPCDTO> acctVcardDiffSearchRespRPCDTOS = Lists.newArrayList();
        page.setTotalCount(count);
        if (count == 0) {
            page.setDataList(acctVcardDiffSearchRespRPCDTOS);
            return page;
        }
        RowBounds rowBounds = new RowBounds(searchReqRPCDTO.getOffset(), searchReqRPCDTO.getPageSize());
        List<AcctVcardDiffProcess> acctVcardDiffProcesses =
            acctVcardDiffProcessMapper.selectByExampleAndRowBounds(example, rowBounds);
        for (AcctVcardDiffProcess acctVcardDiffProcess : acctVcardDiffProcesses) {
            AcctVcardDiffSearchRespRPCDTO acctVcardDiffSearchRespRPCDTO = new AcctVcardDiffSearchRespRPCDTO();
            acctVcardDiffSearchRespRPCDTO.setExtractId(acctVcardDiffProcess.getExtractId());
            acctVcardDiffSearchRespRPCDTO.setId(acctVcardDiffProcess.getId());
            acctVcardDiffSearchRespRPCDTO.setEmployeeName(acctVcardDiffProcess.getEmployeeName());
            acctVcardDiffSearchRespRPCDTO.setEmployeePhone(acctVcardDiffProcess.getEmployeePhone());
            acctVcardDiffSearchRespRPCDTO.setCompanyName(acctVcardDiffProcess.getCompanyName());
            acctVcardDiffSearchRespRPCDTO.setBankName(acctVcardDiffProcess.getBankName());
            acctVcardDiffSearchRespRPCDTO
                .setBankNameShow(BankNameEnum.getBankEnum(acctVcardDiffProcess.getBankName()).getName());
            acctVcardDiffSearchRespRPCDTO.setCompanyBankAcctId(acctVcardDiffProcess.getCompanyBankAcctId());
            acctVcardDiffSearchRespRPCDTO.setCompanyBankAccountNo(acctVcardDiffProcess.getCompanyBankAccountNo());
            acctVcardDiffSearchRespRPCDTO.setBankAccountNo(acctVcardDiffProcess.getBankAccountNo());
            acctVcardDiffSearchRespRPCDTO.setAccountType(acctVcardDiffProcess.getAccountType());
            acctVcardDiffSearchRespRPCDTO
                .setAccountTypeShow(PersonAcctEnum.AccountType.getEnum(acctVcardDiffProcess.getAccountType()).msg());
            acctVcardDiffSearchRespRPCDTO.setProcessMethod(acctVcardDiffProcess.getProcessMethod());
            acctVcardDiffSearchRespRPCDTO
                .setProcessMethodShow(ProcessMethodEnum.getEnum(acctVcardDiffProcess.getProcessMethod()).getValue());
            acctVcardDiffSearchRespRPCDTO.setSyncBankTransNo(acctVcardDiffProcess.getSyncBankTransNo());
            acctVcardDiffSearchRespRPCDTO.setBankTransNo(acctVcardDiffProcess.getBankTransNo());
            acctVcardDiffSearchRespRPCDTO.setProcessStatus(acctVcardDiffProcess.getProcessStatus());
            acctVcardDiffSearchRespRPCDTO
                .setProcessStatusShow(ProcessStatusEnum.getEnum(acctVcardDiffProcess.getProcessStatus()).getValue());
            acctVcardDiffSearchRespRPCDTO.setOperationUserName(acctVcardDiffProcess.getOperationUserName());
            acctVcardDiffSearchRespRPCDTO.setRemark(acctVcardDiffProcess.getRemark());
            acctVcardDiffSearchRespRPCDTO.setCreateTime(DateUtils.formatTime(acctVcardDiffProcess.getCreateTime()));
            acctVcardDiffSearchRespRPCDTO.setOperateAmt(acctVcardDiffProcess.getOperateAmt());
            acctVcardDiffSearchRespRPCDTOS.add(acctVcardDiffSearchRespRPCDTO);
        }
        page.setDataList(acctVcardDiffSearchRespRPCDTOS);
        return page;
    }

    private Example buildQueryExample(AcctVcardDiffSearchReqRPCDTO searchReqRPCDTO) {
        Example example = new Example(AcctVcardDiffProcess.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(searchReqRPCDTO.getEmployeePhone())) {
            criteria.andEqualTo("employeePhone", searchReqRPCDTO.getEmployeePhone());
        }
        if (StringUtils.isNotBlank(searchReqRPCDTO.getEmployeeName())) {
            criteria.andLike("employeeName", "%" + searchReqRPCDTO.getEmployeeName() + "%");
        }
        if (StringUtils.isNotBlank(searchReqRPCDTO.getCompanyId())) {
            criteria.andEqualTo("companyId", searchReqRPCDTO.getCompanyId());
        }
        if (StringUtils.isNotBlank(searchReqRPCDTO.getCompanyBankAccountNo())) {
            criteria.andEqualTo("companyBankAccountNo", searchReqRPCDTO.getCompanyBankAccountNo());
        }
        if (StringUtils.isNotBlank(searchReqRPCDTO.getBankAccountNo())) {
            criteria.andEqualTo("bankAccountNo", searchReqRPCDTO.getBankAccountNo());
        }
        if (StringUtils.isNotBlank(searchReqRPCDTO.getBankName())) {
            criteria.andEqualTo("bankName", searchReqRPCDTO.getBankName());
        }
        if (Objects.nonNull(searchReqRPCDTO.getProcessMethod())) {
            criteria.andEqualTo("processMethod", searchReqRPCDTO.getProcessMethod());
        }
        if (Objects.nonNull(searchReqRPCDTO.getProcessStatus())) {
            criteria.andEqualTo("processStatus", searchReqRPCDTO.getProcessStatus());
        }
        if (StringUtils.isNotBlank(searchReqRPCDTO.getCreateTimeFrom())
            && StringUtils.isNotBlank(searchReqRPCDTO.getCreateTimeTo())) {
            criteria.andGreaterThanOrEqualTo("createTime", searchReqRPCDTO.getCreateTimeFrom());
            criteria.andLessThan("createTime", searchReqRPCDTO.getCreateTimeTo());
        }
        return example;
    }

    @Override
    public List<AcctVcardBalanceSearchRespRPCDTO> queryBalance(AcctVcardBalanceSearchReqRPCDTO searchReqRPCDTO) {
        FinhubLogger.info("iAcctVcardDiffProcessService queryBalance param{}", JsonUtils.toJson(searchReqRPCDTO));
        List<AcctVcardBalanceSearchRespRPCDTO> acctVcardBalanceSearchRespRPCDTOS = Lists.newArrayList();
            // 实时查询银行+库
            ValidateUtils.validate(searchReqRPCDTO);
            Example example = new Example(BankCard.class);
            example.createCriteria().andEqualTo("bankAccountNo", searchReqRPCDTO.getBankAccountNo())
                .andEqualTo("bankName", searchReqRPCDTO.getBankName());
            BankCard bankCard = bankCardMapper.selectOneByExample(example);
            if (Objects.isNull(bankCard)) {
                return acctVcardBalanceSearchRespRPCDTOS;
            }
            CgbQueryAccountBalanceRespDTO cgbQueryAccountBalanceRespDTO =
                doRPCQueryCgbVirtualCardBalance(bankCard.getBankAccountNo());
            AcctCompanyCard acctCompanyCard = acctCompanyCardService.findByCompanyIdAndBank(bankCard.getCompanyId(),
                bankCard.getBankName(), bankCard.getCompanyBankAccountNo());
            acctVcardBalanceSearchRespRPCDTOS
                .add(buildBankCardBalanceResp(bankCard, acctCompanyCard, cgbQueryAccountBalanceRespDTO,searchReqRPCDTO.getExtractId()));
            BankUserCardInfoRespDTO bankUserCard = getBankUserCard(bankCard);
            if (Objects.nonNull(bankUserCard)) {
                acctVcardBalanceSearchRespRPCDTOS
                    .add(buildBankUserCardBalanceResp(bankUserCard, acctCompanyCard, cgbQueryAccountBalanceRespDTO,searchReqRPCDTO.getExtractId()));
            }
        return acctVcardBalanceSearchRespRPCDTOS;
    }

    private AcctVcardBalanceSearchRespRPCDTO buildBankUserCardBalanceResp(BankUserCardInfoRespDTO bankUserCard,
        AcctCompanyCard acctCompanyCard, CgbQueryAccountBalanceRespDTO cgbQueryAccountBalanceRespDTO,String extractId) {
        AcctVcardBalanceSearchRespRPCDTO acctVcardBalanceSearchRespRPCDTO = new AcctVcardBalanceSearchRespRPCDTO();
        acctVcardBalanceSearchRespRPCDTO.setExtractId(extractId);
        acctVcardBalanceSearchRespRPCDTO.setQueryTime(DateUtils.formatTime(new Date()));
        acctVcardBalanceSearchRespRPCDTO.setEmployeeId(bankUserCard.getEmployeeId());
        acctVcardBalanceSearchRespRPCDTO.setEmployeeName(bankUserCard.getEmployeeName());
        acctVcardBalanceSearchRespRPCDTO.setEmployeePhone(bankUserCard.getEmployeePhone());
        if (Objects.nonNull(acctCompanyCard)) {
            acctVcardBalanceSearchRespRPCDTO.setCompanyId(acctCompanyCard.getCompanyId());
            acctVcardBalanceSearchRespRPCDTO.setCompanyName(acctCompanyCard.getCompanyName());
            acctVcardBalanceSearchRespRPCDTO.setCompanyBankAccountNo(acctCompanyCard.getBankAccountNo());
        }
        acctVcardBalanceSearchRespRPCDTO.setBankName(bankUserCard.getBankName());
        acctVcardBalanceSearchRespRPCDTO
            .setBankNameShow(BankNameEnum.getBankEnum(bankUserCard.getBankName()).getName());
        acctVcardBalanceSearchRespRPCDTO.setBankAccountNo(bankUserCard.getBankAccountNo());
        acctVcardBalanceSearchRespRPCDTO.setAccountType(PersonAcctEnum.AccountType.USER_CARD.code());
        acctVcardBalanceSearchRespRPCDTO.setAccountTypeShow(PersonAcctEnum.AccountType.USER_CARD.msg());
        acctVcardBalanceSearchRespRPCDTO.setFbtAcctBalance(bankUserCard.getCardBalance());
        if (Objects.nonNull(cgbQueryAccountBalanceRespDTO)
            && Objects.nonNull(cgbQueryAccountBalanceRespDTO.getCurrentAvaBalance())) {
            BigDecimal multiply =
                new BigDecimal(cgbQueryAccountBalanceRespDTO.getCurrentAvaBalance()).multiply(new BigDecimal("100"));
            acctVcardBalanceSearchRespRPCDTO.setBankAcctBalance(multiply);
            if (multiply.subtract(bankUserCard.getCardBalance()).compareTo(BigDecimal.ONE) == 0) {
                acctVcardBalanceSearchRespRPCDTO.setExtractStatus(PersonAcctEnum.ExtractStatus.YES.code());
                acctVcardBalanceSearchRespRPCDTO.setExtractStatusShow(PersonAcctEnum.ExtractStatus.YES.msg());
            } else {
                acctVcardBalanceSearchRespRPCDTO.setExtractStatus(PersonAcctEnum.ExtractStatus.NO.code());
                acctVcardBalanceSearchRespRPCDTO.setExtractStatusShow(PersonAcctEnum.ExtractStatus.NO.msg());
            }
        }
        return acctVcardBalanceSearchRespRPCDTO;
    }

    private AcctVcardBalanceSearchRespRPCDTO buildBankCardBalanceResp(BankCard bankCard,
        AcctCompanyCard acctCompanyCard, CgbQueryAccountBalanceRespDTO cgbQueryAccountBalanceRespDTO,String extractId) {
        AcctVcardBalanceSearchRespRPCDTO acctVcardBalanceSearchRespRPCDTO = new AcctVcardBalanceSearchRespRPCDTO();
        acctVcardBalanceSearchRespRPCDTO.setExtractId(extractId);
        acctVcardBalanceSearchRespRPCDTO.setQueryTime(DateUtils.formatTime(new Date()));
        acctVcardBalanceSearchRespRPCDTO.setEmployeeId(bankCard.getEmployeeId());
        acctVcardBalanceSearchRespRPCDTO.setEmployeeName(bankCard.getEmployeeName());
        acctVcardBalanceSearchRespRPCDTO.setEmployeePhone(bankCard.getEmployeePhone());
        acctVcardBalanceSearchRespRPCDTO.setCompanyId(bankCard.getCompanyId());
        if (Objects.nonNull(acctCompanyCard)) {
            acctVcardBalanceSearchRespRPCDTO.setCompanyName(acctCompanyCard.getCompanyName());
        }
        acctVcardBalanceSearchRespRPCDTO.setBankName(bankCard.getBankName());
        acctVcardBalanceSearchRespRPCDTO.setBankNameShow(BankNameEnum.getBankEnum(bankCard.getBankName()).getName());
        acctVcardBalanceSearchRespRPCDTO.setCompanyBankAccountNo(bankCard.getCompanyBankAccountNo());
        acctVcardBalanceSearchRespRPCDTO.setBankAccountNo(bankCard.getBankAccountNo());
        acctVcardBalanceSearchRespRPCDTO.setAccountType(PersonAcctEnum.AccountType.BANK_CARD.code());
        acctVcardBalanceSearchRespRPCDTO.setAccountTypeShow(PersonAcctEnum.AccountType.BANK_CARD.msg());
        acctVcardBalanceSearchRespRPCDTO.setFbtAcctBalance(bankCard.getCardBalance());
        if (Objects.nonNull(cgbQueryAccountBalanceRespDTO)
            && Objects.nonNull(cgbQueryAccountBalanceRespDTO.getEAccUnableAmt())) {
            BigDecimal multiply =
                new BigDecimal(cgbQueryAccountBalanceRespDTO.getEAccUnableAmt()).multiply(new BigDecimal("100"));
            acctVcardBalanceSearchRespRPCDTO.setBankAcctBalance(multiply);
            if (multiply.subtract(bankCard.getCardBalance()).compareTo(BigDecimal.ONE) == 0) {
                acctVcardBalanceSearchRespRPCDTO.setExtractStatus(PersonAcctEnum.ExtractStatus.YES.code());
                acctVcardBalanceSearchRespRPCDTO.setExtractStatusShow(PersonAcctEnum.ExtractStatus.YES.msg());
            } else {
                acctVcardBalanceSearchRespRPCDTO.setExtractStatus(PersonAcctEnum.ExtractStatus.NO.code());
                acctVcardBalanceSearchRespRPCDTO.setExtractStatusShow(PersonAcctEnum.ExtractStatus.NO.msg());
            }
        }
        return acctVcardBalanceSearchRespRPCDTO;
    }

    private AcctVcardBalanceSearchRespRPCDTO buildAcctBalanceResp(AcctPersonBankCheck acctPersonBankCheck) {
        AcctVcardBalanceSearchRespRPCDTO acctVcardBalanceSearchRespRPCDTO = new AcctVcardBalanceSearchRespRPCDTO();
        acctVcardBalanceSearchRespRPCDTO.setQueryTime(DateUtils.formatTime(acctPersonBankCheck.getCreateTime()));
        acctVcardBalanceSearchRespRPCDTO.setExtractId(acctPersonBankCheck.getExtractId());
        acctVcardBalanceSearchRespRPCDTO.setEmployeeId(acctPersonBankCheck.getEmployeeId());
        acctVcardBalanceSearchRespRPCDTO.setEmployeeName(acctPersonBankCheck.getEmployeeName());
        acctVcardBalanceSearchRespRPCDTO.setEmployeePhone(acctPersonBankCheck.getEmployeePhone());
        acctVcardBalanceSearchRespRPCDTO.setCompanyId(acctPersonBankCheck.getCompanyId());
        acctVcardBalanceSearchRespRPCDTO.setCompanyName(acctPersonBankCheck.getCompanyName());
        acctVcardBalanceSearchRespRPCDTO.setBankName(acctPersonBankCheck.getBankName());
        acctVcardBalanceSearchRespRPCDTO
            .setBankNameShow(BankNameEnum.getBankEnum(acctPersonBankCheck.getBankName()).getName());
        acctVcardBalanceSearchRespRPCDTO.setCompanyBankAccountNo(acctPersonBankCheck.getCompanyBankAccountNo());
        acctVcardBalanceSearchRespRPCDTO.setBankAccountNo(acctPersonBankCheck.getBankAccountNo());
        acctVcardBalanceSearchRespRPCDTO.setAccountType(acctPersonBankCheck.getAccountType());
        acctVcardBalanceSearchRespRPCDTO
            .setAccountTypeShow(PersonAcctEnum.AccountType.getEnum(acctPersonBankCheck.getAccountType()).msg());
        acctVcardBalanceSearchRespRPCDTO.setFbtAcctBalance(acctPersonBankCheck.getFbtAcctBalance());
        acctVcardBalanceSearchRespRPCDTO.setBankAcctBalance(acctPersonBankCheck.getBankAcctBalance());
        acctVcardBalanceSearchRespRPCDTO.setExtractStatus(acctPersonBankCheck.getExtractStatus());
        acctVcardBalanceSearchRespRPCDTO
            .setExtractStatusShow(PersonAcctEnum.ExtractStatus.getEnum(acctPersonBankCheck.getExtractStatus()).msg());
        acctVcardBalanceSearchRespRPCDTO.setRemark(acctPersonBankCheck.getRemark());
        return acctVcardBalanceSearchRespRPCDTO;
    }

    @Override
    public AcctVcardDiffProcessRespRPCDTO diffProcess(AcctVcardDiffProcessReqRPCDTO acctVcardDiffProcessReqRPCDTO) {
        AcctVcardDiffProcessRespRPCDTO acctVcardDiffProcessRespRPCDTO = new AcctVcardDiffProcessRespRPCDTO();
        acctVcardDiffProcessRespRPCDTO.setHandleStatus(true);
        try {
            ValidateUtils.validate(acctVcardDiffProcessReqRPCDTO);
            BankCard bankCard = iBankCardManager.selectBankCardByBankAcctId(acctVcardDiffProcessReqRPCDTO.getBankAccountNo());
            if (Objects.isNull(bankCard)) {
                throw new FinPayException(GlobalResponseCode.VCARD_DIFF_PROCESS_ERROR02);
            }
            if(StringUtils.isBlank(bankCard.getBindAcctNo())){
                throw new FinPayException(GlobalResponseCode.VCARD_DIFF_PROCESS_ERROR07);
            }
            BankUserCardInfoRespDTO bankUserCard = getBankUserCard(bankCard);
            if (Objects.isNull(bankUserCard)) {
                throw new FinPayException(GlobalResponseCode.VCARD_DIFF_PROCESS_ERROR06);
            }
            // 校验传过来的虚拟卡号与库里是否一致
            if (!acctVcardDiffProcessReqRPCDTO.getBankAccountNo().equals(bankCard.getBankAccountNo())) {
                throw new FinPayException(GlobalResponseCode.VCARD_DIFF_PROCESS_ERROR03);
            }
            // 查询银行余额 判断是否大于分贝通侧余额 1分
            CgbQueryAccountBalanceRespDTO cgbQueryAccountBalanceRespDTO =
                doRPCQueryCgbVirtualCardBalance(acctVcardDiffProcessReqRPCDTO.getBankAccountNo());
            if (Objects.isNull(cgbQueryAccountBalanceRespDTO)
                || Objects.isNull(cgbQueryAccountBalanceRespDTO.getCurrentAvaBalance())) {
                throw new FinPayException(GlobalResponseCode.VCARD_DIFF_PROCESS_ERROR01);
            }
            BigDecimal multiply =
                new BigDecimal(cgbQueryAccountBalanceRespDTO.getCurrentAvaBalance()).multiply(new BigDecimal(100));
            BigDecimal subtract = multiply.subtract(bankUserCard.getCardBalance()).subtract(BigDecimal.ONE);
            if (acctVcardDiffProcessReqRPCDTO.getOperateAmt().compareTo(subtract) > 0) {
                throw new FinPayException(GlobalResponseCode.VCARD_DIFF_PROCESS_ERROR05);
            }
            // 查询处理中的差异记录
            List<AcctVcardDiffProcess> acctVcardDiffProcesses =
                queryDiffProcessing(acctVcardDiffProcessReqRPCDTO.getBankAccountNo());
            if (CollectionUtils.isNotEmpty(acctVcardDiffProcesses)) {
                throw new FinPayException(GlobalResponseCode.VCARD_DIFF_PROCESS_ERROR04);
            }
            // 插入差异记录，记录状态处理中
            AcctVcardDiffProcess acctVcardDiffProcess =
                buildAcctVcardDiffProcess(bankUserCard, bankCard, acctVcardDiffProcessReqRPCDTO);
            acctVcardDiffProcessMapper.insertSelective(acctVcardDiffProcess);
            // 调用提现接口
            TradeRespDTO tradeRespDTO = doRpcWithDraw(bankUserCard, bankCard, acctVcardDiffProcessReqRPCDTO);
            updateAcctVcardByTradeResp(tradeRespDTO, acctVcardDiffProcess);
            if(tradeRespDTO.getTradeStatus().equals(TradeOrderStatusEnum.FAIL.getCode().toString())){
                acctVcardDiffProcessRespRPCDTO.setHandleStatus(false);
                acctVcardDiffProcessRespRPCDTO.setErrorMsg(tradeRespDTO.getFailureReason());
            }
        } catch (Exception e) {
            FinhubLogger.error("IAcctVcardDiffProcessServiceImpl diffProcess err=", e);
            acctVcardDiffProcessRespRPCDTO.setHandleStatus(false);
            acctVcardDiffProcessRespRPCDTO.setErrorMsg(e.getMessage());
        }
        return acctVcardDiffProcessRespRPCDTO;
    }

    private void updateAcctVcardByTradeResp(TradeRespDTO tradeRespDTO, AcctVcardDiffProcess acctVcardDiffProcess) {
        AcctVcardDiffProcess update = new AcctVcardDiffProcess();
        update.setId(acctVcardDiffProcess.getId());
        update.setSyncBankTransNo(tradeRespDTO.getOrderId());
        if (tradeRespDTO.getTradeStatus().equals(TradeOrderStatusEnum.FAIL.getCode().toString())) {
            update.setProcessStatus(ProcessStatusEnum.FAIL.getKey());
        } else if (tradeRespDTO.getTradeStatus().equals(TradeOrderStatusEnum.HANDLING.getCode().toString())) {
            update.setProcessStatus(ProcessStatusEnum.PROCESSING.getKey());
        } else if (tradeRespDTO.getTradeStatus().equals(TradeOrderStatusEnum.SUCCESS.getCode().toString())) {
            update.setProcessStatus(ProcessStatusEnum.SUCCESS.getKey());
        } else {
        }
        acctVcardDiffProcessMapper.updateByPrimaryKeySelective(update);
        if (Objects.nonNull(acctVcardDiffProcess.getExtractId())) {
            // 更新对账差异处理状态
            if (ProcessStatusEnum.SUCCESS.getKey().equals(acctVcardDiffProcess.getProcessStatus())) {
                Example example = new Example(AcctPersonBankCheck.class);
                example.createCriteria().andEqualTo("extractId", acctVcardDiffProcess.getExtractId());
                AcctPersonBankCheck acctPersonBankCheck = new AcctPersonBankCheck();
                acctPersonBankCheck.setProcessStatus(PersonAcctEnum.ProcessStatus.WITHDRAW_FINISH.code());
                acctPersonBankCheckMapper.updateByExampleSelective(acctPersonBankCheck, example);
            }
        }
    }

    private TradeRespDTO doRpcWithDraw(BankUserCardInfoRespDTO bankUserCardInfoRespDTO, BankCard bankCard,
        AcctVcardDiffProcessReqRPCDTO acctVcardDiffProcessReqRPCDTO) {
        TradeRespDTO tradeRespDTO = new TradeRespDTO();
        String bizNo = IDGen.genId(CASH_IN);
        try {
            if (BankNameEnum.isCgb(bankUserCardInfoRespDTO.getBankName())) {
                CgbWithdrawReqDTO cgbWithdrawReqDTO = new CgbWithdrawReqDTO();
                cgbWithdrawReqDTO.setTxnId(bizNo);
                cgbWithdrawReqDTO.setSendFlowNo(bizNo);
                cgbWithdrawReqDTO.setPriAccNo(bankCard.getBindAcctNo());
                cgbWithdrawReqDTO.setSubAccNo(bankUserCardInfoRespDTO.getBankAccountNo());
                cgbWithdrawReqDTO.setPayAmount(acctVcardDiffProcessReqRPCDTO.getOperateAmt().toString());
                FinhubLogger.info("cgbVirtualCardService.withdraw req{}", JsonUtils.toJson(cgbWithdrawReqDTO));
                CgbWithdrawRespDTO withdrawRespDTO = cgbVirtualCardService.withdraw(cgbWithdrawReqDTO);
                FinhubLogger.info("cgbVirtualCardService.withdraw resp{}", JsonUtils.toJson(withdrawRespDTO));
                TradeOrderStatusEnum tradeOrderStatusEnum = convertTradeStatus(withdrawRespDTO);
                tradeRespDTO.setTradeStatus(tradeOrderStatusEnum.getCode().toString());
                tradeRespDTO.setOrderId(bizNo);
                tradeRespDTO.setFailureReason(withdrawRespDTO.getErrorMsg());
            }
        } catch (Exception e) {
            FinhubLogger.error("IAcctVcardDiffProcessServiceImpl doRpcWithDraw err=", e);
            // 报错置为处理中
            tradeRespDTO.setOrderId(bizNo);
            tradeRespDTO.setTradeStatus(TradeOrderStatusEnum.HANDLING.getCode().toString());
        }
        return tradeRespDTO;
    }

    private TradeOrderStatusEnum convertTradeStatus(CgbWithdrawRespDTO withdrawRespDTO) {
        if (Objects.isNull(withdrawRespDTO)) {
            return TradeOrderStatusEnum.FAIL;
        }
        if ("33".equals(withdrawRespDTO.getTradeStatus())) {
            return TradeOrderStatusEnum.FAIL;
        } else if ("90".equals(withdrawRespDTO.getTradeStatus())) {
            return TradeOrderStatusEnum.SUCCESS;
        } else if ("99".equals(withdrawRespDTO.getTradeStatus())) {
            return TradeOrderStatusEnum.FAIL;
        }
        return TradeOrderStatusEnum.HANDLING;
    }

    private BankUserCardInfoRespDTO getBankUserCard(BankCard bankCard) {
        BankUserCardInfoRespDTO bankUserCardInfoRespDTO = null;
        try {
            BankUserCardSearchReqDTO bankUserCardSearchReqDTO = new BankUserCardSearchReqDTO();
            bankUserCardSearchReqDTO.setBankName(bankCard.getBankName());
            bankUserCardSearchReqDTO.setEmployeeId(bankCard.getEmployeeId());
            bankUserCardSearchReqDTO.setCompanyId(bankCard.getCompanyId());
            FinhubLogger.info("IAcctVcardDiffProcessServiceImpl#getBankUserCard-queryBankUserCardInfo-in#req:{}",
                JSON.toJSON(bankUserCardSearchReqDTO));
            bankUserCardInfoRespDTO = bankUserCardSearchService.queryBankUserCardInfo(bankUserCardSearchReqDTO);
            FinhubLogger.info("IAcctVcardDiffProcessServiceImpl#getBankUserCard-queryBankUserCardInfo-in#resp:{}",
                JSON.toJSON(bankUserCardInfoRespDTO));
        } catch (Exception e) {
            FinhubLogger.error(
                "IAcctVcardDiffProcessServiceImpl#getBankUserCard-queryBankUserCardInfo-in#获取个人资金账户失败#req:{},err=",
                JSON.toJSON(bankCard), e);
        }
        return bankUserCardInfoRespDTO;
    }

    private AcctVcardDiffProcess buildAcctVcardDiffProcess(BankUserCardInfoRespDTO bankUserCard, BankCard bankCard,
        AcctVcardDiffProcessReqRPCDTO acctVcardDiffProcessReqRPCDTO) {
        AcctVcardDiffProcess acctVcardDiffProcess = new AcctVcardDiffProcess();
        acctVcardDiffProcess.setExtractId(acctVcardDiffProcessReqRPCDTO.getExtractId());
        acctVcardDiffProcess.setEmployeeId(acctVcardDiffProcessReqRPCDTO.getEmployeeId());
        acctVcardDiffProcess.setEmployeeName(bankUserCard.getEmployeeName());
        acctVcardDiffProcess.setEmployeePhone(bankUserCard.getEmployeePhone());
        acctVcardDiffProcess.setBankAccountNo(bankUserCard.getBankAccountNo());
        acctVcardDiffProcess.setBankAcctId(bankUserCard.getBankAcctId());
        acctVcardDiffProcess.setCompanyId(bankCard.getCompanyId());
        AcctCompanyCard acctCompanyCard =
            acctCompanyCardService.findByCompanyIdAndBank(acctVcardDiffProcessReqRPCDTO.getCompanyId(),
                bankUserCard.getBankName(), bankCard.getCompanyBankAccountNo());
        if (Objects.nonNull(acctCompanyCard)) {
            acctVcardDiffProcess.setCompanyName(acctCompanyCard.getCompanyName());
            acctVcardDiffProcess.setCompanyMainId(acctCompanyCard.getCompanyMainId());
        }
        acctVcardDiffProcess.setCompanyBankAccountNo(bankCard.getCompanyBankAccountNo());
        acctVcardDiffProcess.setCompanyBankAcctId(bankCard.getCompanyBankAcctId());
        acctVcardDiffProcess.setAccountType(PersonAcctEnum.AccountType.USER_CARD.code());
        acctVcardDiffProcess.setBankName(bankUserCard.getBankName());
        acctVcardDiffProcess.setProcessStatus(ProcessStatusEnum.PROCESSING.getKey());
        acctVcardDiffProcess.setProcessMethod(ProcessMethodEnum.WITHDRAW.getKey());
        acctVcardDiffProcess.setOperateAmt(acctVcardDiffProcessReqRPCDTO.getOperateAmt());
        acctVcardDiffProcess.setRemark(acctVcardDiffProcessReqRPCDTO.getRemark());
        acctVcardDiffProcess.setOperationUserId(acctVcardDiffProcessReqRPCDTO.getOperationUserId());
        acctVcardDiffProcess.setOperationUserName(acctVcardDiffProcessReqRPCDTO.getOperationUserName());
        acctVcardDiffProcess.setCreateTime(new Date());
        acctVcardDiffProcess.setUpdateTime(new Date());
        return acctVcardDiffProcess;
    }

    private List<AcctVcardDiffProcess> queryDiffProcessing(String bankAccountNo) {
        Example example = new Example(AcctVcardDiffProcess.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bankAccountNo", bankAccountNo);
        criteria.andEqualTo("processStatus", ProcessStatusEnum.PROCESSING.getKey());
        List<AcctVcardDiffProcess> acctVcardDiffProcesses = acctVcardDiffProcessMapper.selectByExample(example);
        return acctVcardDiffProcesses;
    }

    private CgbQueryAccountBalanceRespDTO doRPCQueryCgbVirtualCardBalance(String bankCardNo) {
        CgbQueryAccountBalanceRespDTO cgbCreateAccountTypeOPRespDTO = null;
        try {
            CgbQueryAccountBalanceReqDTO cgbQueryAccountBalanceReqDTO = new CgbQueryAccountBalanceReqDTO();
            cgbQueryAccountBalanceReqDTO.setAccountType("E");
            cgbQueryAccountBalanceReqDTO.setSubAccNo(bankCardNo);
            cgbQueryAccountBalanceReqDTO.setChannel("330");
            cgbQueryAccountBalanceReqDTO.setTxnId("SN" + System.nanoTime());
            FinhubLogger.info(
                "PersonAcctExtractController#doRPCQueryCgbVirtualCardBalance-queryAccountBalance-in#req:{}",
                JSON.toJSONString(cgbQueryAccountBalanceReqDTO));
            cgbCreateAccountTypeOPRespDTO = cgbVirtualCardService.queryAccountBalance(cgbQueryAccountBalanceReqDTO);
            FinhubLogger.info(
                "PersonAcctExtractController#doRPCQueryCgbVirtualCardBalance-queryAccountBalance-in#resp:{}",
                JSON.toJSONString(cgbCreateAccountTypeOPRespDTO));
        } catch (Exception e) {
            FinhubLogger
                .error("PersonAcctExtractController#doRPCQueryCgbVirtualCardBalance-queryAccountBalance-in#err=", e);
        }
        return cgbCreateAccountTypeOPRespDTO;
    }

    @Override
    public void processingHandler() {
        Example example = new Example(AcctVcardDiffProcess.class);
        example.createCriteria().andEqualTo("processStatus", ProcessStatusEnum.PROCESSING.getKey());
        List<AcctVcardDiffProcess> acctVcardDiffProcesses = acctVcardDiffProcessMapper.selectByExample(example);
        for (AcctVcardDiffProcess acctVcardDiffProcess : acctVcardDiffProcesses) {
            try {
                if (BankNameEnum.isCgb(acctVcardDiffProcess.getBankName())) {
                    TradeRespDTO tradeRespDTO = new TradeRespDTO();
                    CgbQueryTradeResultReqDTO cgbQueryTradeResultReqDTO = new CgbQueryTradeResultReqDTO();
                    cgbQueryTradeResultReqDTO.setOrFlowNo(acctVcardDiffProcess.getSyncBankTransNo());
                    cgbQueryTradeResultReqDTO.setTxnId(IDGen.genId("QCGBREC"));
                    // 1：充值 2：提现
                    cgbQueryTradeResultReqDTO.setOrTransType("2");
                    FinhubLogger.info("cgbVirtualCardService.queryTradeResult req{}",JsonUtils.toJson(cgbQueryTradeResultReqDTO));
                    CgbQueryTradeResultRespDTO cgbQueryTradeResultRespDTO =
                        cgbVirtualCardService.queryTradeResult(cgbQueryTradeResultReqDTO);
                    FinhubLogger.info("cgbVirtualCardService.queryTradeResult resp{}",JsonUtils.toJson(cgbQueryTradeResultRespDTO));
                    if (CgbTradeStatusEnum.SUCCESS.getCode().equals(cgbQueryTradeResultRespDTO.getStatus())) {
                        tradeRespDTO.setTradeStatus(TradeOrderStatusEnum.SUCCESS.getCode().toString());
                        tradeRespDTO.setOrderId(acctVcardDiffProcess.getSyncBankTransNo());
                    } else if (CgbTradeStatusEnum.FAIL.getCode().equals(cgbQueryTradeResultRespDTO.getStatus())) {
                        tradeRespDTO.setTradeStatus(TradeOrderStatusEnum.SUCCESS.getCode().toString());
                        tradeRespDTO.setFailureReason(cgbQueryTradeResultRespDTO.getErrorMsg());
                    }else {
                        tradeRespDTO.setTradeStatus(TradeOrderStatusEnum.HANDLING.getCode().toString());
                    }
                    updateAcctVcardByTradeResp(tradeRespDTO, acctVcardDiffProcess);
                }
            } catch (Exception e) {
                FinhubLogger.error("processingHandler queryTradeStatus,err=", e);
            }
        }
    }
}
