package com.fenbeitong.fenbeipay.rpc.service.saturn;


import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.BankUserCardRespDTO;
import com.fenbeitong.acctperson.api.service.search.IBankUserCardSearchService;
import com.fenbeitong.fenbeipay.acctdech.dto.AcctOptFlowPageDTO;
import com.fenbeitong.fenbeipay.acctdech.service.*;
import com.fenbeitong.fenbeipay.acctpublic.manager.AcctPublicFlowManager;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenChangeType;
import com.fenbeitong.fenbeipay.api.constant.enums.accountpublic.SpaInnerAcctTypeEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyType;
import com.fenbeitong.fenbeipay.api.model.dto.saturn.*;
import com.fenbeitong.fenbeipay.api.service.saturn.IAutoAcctCheckingQueryService;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.ApplyCardManager;
import com.fenbeitong.fenbeipay.core.enums.paycenter.AccountType;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublicFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.bank.BankCardApplyFlow;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezenFlow;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.fenbeipay.nf.service.FundFreezenFlowService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersOperationFlowService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.google.common.base.Strings;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 对账系统自动对账查询
 * <AUTHOR>
 * @date 2021/5/18
 */
@Service("iAutoAcctCheckingQueryServiceImpl")
public class IAutoAcctCheckingQueryServiceImpl implements IAutoAcctCheckingQueryService {
    @Autowired
    private AccountGeneralFlowService accountGeneralFlowService;
    @Autowired
    private AccountGeneralService accountGeneralService;
    @Autowired
    private AcctPublicFlowManager acctPublicFlowManager;
    @Autowired
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;
    @Autowired
    private AcctBusinessCreditService acctBusinessCreditService;
    @Autowired
    private AcctBusinessDebitFlowService acctBusinessDebitFlowService;
    @Autowired
    private AcctBusinessDebitService acctBusinessDebitService;
    @Autowired
    private AcctCompanyCardFlowService acctCompanyCardFlowService;
    @Autowired
    private AcctCompanyCardService acctCompanyCardService;
//    @Autowired
//    private TradeCardManagerImpl bankCardBaseManager;
    @Autowired
    private AcctIndividualCreditFlowService acctIndividualCreditFlowService;
    @Autowired
    private AcctIndividualCreditService acctIndividualCreditService;
    @Autowired
    private AcctIndividualDebitFlowService acctIndividualDebitFlowService;
    @Autowired
    private AcctIndividualDebitService acctIndividualDebitService;
    @Autowired
    private FundFreezenFlowService fundFreezenFlowService;
    @Autowired
    private AcctSettlementFlowService acctSettlementFlowService;
    @Autowired
    private AcctSettlementService acctSettlementService;

    @Autowired
    private AccountSpaInnerFlowService accountSpaInnerFlowService;

    @Autowired
    private AcctReimbursementFlowService acctReimbursementFlowService;

    @Autowired
    private AcctReimbursementService acctReimbursementService;

    @Autowired
    private BankAcctFlowService bankAcctFlowService;

    @Autowired
    private ApplyCardManager applyCardManager;

    @Autowired
    private IBankUserCardSearchService iBankUserCardSearchService;

    @Autowired
    private ICompanyService iCompanyService;

    @Autowired
    private VouchersOperationFlowService vouchersOperationFlowService;

    @Override
    public AcctCheckingQueryRpcDTO queryByBizNo(String bizNo) {
        AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO = new AcctCheckingQueryRpcDTO();
        acctCheckingQueryRpcDTO.setBizNo(bizNo);
        // 结算流水
        AcctSettlementFlow acctSettlementFlow = acctSettlementFlowService.queryAccountSubFlowByFlowId(bizNo);
        if (ObjUtils.isNotEmpty(acctSettlementFlow)){
            AcctSettlement accountByAccountId = acctSettlementService.findAccountByAccountId(acctSettlementFlow.getAccountId());
            if(!ObjUtils.isNull(accountByAccountId)){
                acctCheckingQueryRpcDTO.setCompanyId(accountByAccountId.getCompanyId());
                acctCheckingQueryRpcDTO.setCompanyName(accountByAccountId.getCompanyName());
                acctCheckingQueryRpcDTO.setBankAccountName(accountByAccountId.getCompanyName());
            }
            acctCheckingQueryRpcDTO.setTradeType(acctSettlementFlow.getTradeType());
            acctCheckingQueryRpcDTO.setOperationType(acctSettlementFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(acctSettlementFlow.getOperationTypeDesc());
            acctCheckingQueryRpcDTO.setAccountType(acctCheckingQueryRpcDTO.getAccountType());
            acctCheckingQueryRpcDTO.setAccountSubType(FundAccountSubType.SETTLEMENT_ACCOUNT.getKey());
            acctCheckingQueryRpcDTO.setAccountFlowId(acctSettlementFlow.getAccountFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(acctSettlementFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(acctSettlementFlow.getSyncBankTransNo());
        }
        AccountGeneralFlow accountGeneralFlow = accountGeneralFlowService.queryByFlowId(bizNo);
        if (ObjUtils.isNotEmpty(accountGeneralFlow)){
            AccountGeneral byAccountId = accountGeneralService.findByAccountId(accountGeneralFlow.getAccountGeneralId());
            if(!ObjUtils.isNull(byAccountId)){
                acctCheckingQueryRpcDTO.setCompanyId(byAccountId.getCompanyId());
                acctCheckingQueryRpcDTO.setCompanyName(byAccountId.getCompanyName());
                acctCheckingQueryRpcDTO.setBankAccountName(byAccountId.getCompanyMainName());
            }
            acctCheckingQueryRpcDTO.setTradeType(accountGeneralFlow.getTradeType());
            acctCheckingQueryRpcDTO.setOperationType(accountGeneralFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(accountGeneralFlow.getOperationTypeDesc());
            acctCheckingQueryRpcDTO.setAccountType(acctCheckingQueryRpcDTO.getAccountType());
            acctCheckingQueryRpcDTO.setAccountSubType(FundAccountSubType.GENERAL_ACCOUNT.getKey());
            acctCheckingQueryRpcDTO.setAccountFlowId(accountGeneralFlow.getAccountFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(accountGeneralFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(accountGeneralFlow.getSyncBankTransNo());
        }
        AccountPublicFlow acctPublicFlow = acctPublicFlowManager.queryByAcctFlowId(bizNo);
        if (ObjUtils.isNotEmpty(acctPublicFlow)) {
            acctCheckingQueryRpcDTO.setCompanyId(acctPublicFlow.getCompanyId());
            acctCheckingQueryRpcDTO.setCompanyName(acctPublicFlow.getCompanyName());
            acctCheckingQueryRpcDTO.setBankAccountName(acctPublicFlow.getBankAccountName());
            acctCheckingQueryRpcDTO.setTradeType(acctPublicFlow.getTradeType());
            acctCheckingQueryRpcDTO.setOperationType(acctPublicFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(acctPublicFlow.getOperationDescription());
            acctCheckingQueryRpcDTO.setAccountType(AccountType.Public_Type.getKey());
            acctCheckingQueryRpcDTO.setAccountSubType(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey());
            acctCheckingQueryRpcDTO.setAccountFlowId(acctPublicFlow.getAccountFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(acctPublicFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(acctPublicFlow.getSyncBankTransNo());
        }
        //商务授信账户
        AcctBusinessCreditFlow acctBusinessCreditFlow = getAcctBusinessCreditFlow(bizNo);
        if (!ObjUtils.isNull(acctBusinessCreditFlow)){
            builderAcctBusinessCreditRespDTO(acctCheckingQueryRpcDTO,acctBusinessCreditFlow);
            return acctCheckingQueryRpcDTO;
        }
        //商务充值账户
        AcctBusinessDebitFlow acctBusinessDebitFlow = getAcctBusinessDebitFlow(bizNo);
        if (!ObjUtils.isNull(acctBusinessDebitFlow)){
            builderAcctBusinessDebitRespDTO(acctCheckingQueryRpcDTO,acctBusinessDebitFlow);
            return acctCheckingQueryRpcDTO;
        }
        AcctIndividualDebitFlow acctIndividualDebitFlow = acctIndividualDebitFlowService.queryAccountSubFlowByFlowId(bizNo);
        if (!ObjUtils.isNull(acctIndividualDebitFlow)){
            builderAcctIndividualDebitFlowRespDTO(acctCheckingQueryRpcDTO,acctIndividualDebitFlow);
            return acctCheckingQueryRpcDTO;
        }
        AcctIndividualCreditFlow acctIndividualCreditFlow = acctIndividualCreditFlowService.queryAccountSubFlowByFlowId(bizNo);
        if (!ObjUtils.isNull(acctIndividualDebitFlow)){
            builderAcctIndividualCreditFlowRespDTO(acctCheckingQueryRpcDTO,acctIndividualCreditFlow);
            return acctCheckingQueryRpcDTO;
        }
        //个人授信账户
        List<AcctIndividualCreditFlow> acctIndividualCreditFlowList = getAcctIndividualCreditFlow(bizNo);
        if (ObjUtils.isNotEmpty(acctIndividualCreditFlowList)) {
            builderAcctIndividualCreditFlowRespDTO(acctCheckingQueryRpcDTO,acctIndividualCreditFlowList);
            return acctCheckingQueryRpcDTO;
        }
        //个人充值账户
        List<AcctIndividualDebitFlow> acctIndividualDebitFlowList = getAcctIndividualDebitFlow(bizNo);
        if (ObjUtils.isNotEmpty(acctIndividualDebitFlowList)) {
            builderAcctIndividualDebitFlowRespDTO(acctCheckingQueryRpcDTO,acctIndividualDebitFlowList);
            return acctCheckingQueryRpcDTO;
        }
        AcctOptFlowPageDTO acctOptFlowPageDTO = new AcctOptFlowPageDTO();
        acctOptFlowPageDTO.setAccountFlowId(bizNo);
        ResponsePage<AcctCompanyCardFlow> acctCompanyCardFlowResponsePage =  acctCompanyCardFlowService.queryPage(acctOptFlowPageDTO);
        List<AcctCompanyCardFlow> acctCompanyCardFlowList = acctCompanyCardFlowResponsePage.getDataList();
        if (ObjUtils.isNotEmpty(acctCompanyCardFlowList)){
            AcctCompanyCardFlow acctCompanyCardFlow = acctCompanyCardFlowList.get(0);
            List<AcctCompanyCard> acctCompanyCardList = acctCompanyCardService.findByCompanyId(acctCompanyCardFlow.getCompanyId());
            if (acctCompanyCardList!=null && acctCompanyCardList.size() > 0){
                acctCheckingQueryRpcDTO.setCompanyId(acctCompanyCardFlow.getCompanyId());
                acctCheckingQueryRpcDTO.setTradeType(acctCompanyCardFlow.getTradeType());
                acctCheckingQueryRpcDTO.setCompanyName(acctCompanyCardList.get(0).getCompanyName());
                acctCheckingQueryRpcDTO.setBankAccountName(acctCompanyCardFlow.getBankName());
                acctCheckingQueryRpcDTO.setOperationType(acctCompanyCardFlow.getOperationType());
                acctCheckingQueryRpcDTO.setOperationTypeDesc(acctCompanyCardFlow.getOperationDescription());
                acctCheckingQueryRpcDTO.setAccountType(acctCompanyCardFlow.getAccountModel());
                acctCheckingQueryRpcDTO.setAccountSubType(acctCompanyCardFlow.getAccountSubType());
                acctCheckingQueryRpcDTO.setAccountFlowId(acctCompanyCardFlow.getAccountFlowId());
                acctCheckingQueryRpcDTO.setBankTransNo(acctCompanyCardFlow.getBankTransNo());
                acctCheckingQueryRpcDTO.setBassTxnId(acctCompanyCardFlow.getSyncBankTransNo());
            }
        }
//        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryByBizNo(bizNo);
//        if (ObjUtils.isNotEmpty(fundFreezenFlow)){
//            acctCheckingQueryRpcDTO.setTradeType(getTradeTypeByFreezenOptType(fundFreezenFlow.getOperationType()));
//            acctCheckingQueryRpcDTO.setCompanyName(fundFreezenFlow.getCompanyId());
//            acctCheckingQueryRpcDTO.setBankAccountName(fundFreezenFlow.getBankName());
//            acctCheckingQueryRpcDTO.setOperationType(fundFreezenFlow.getOperationType());
//            acctCheckingQueryRpcDTO.setOperationTypeDesc(fundFreezenFlow.getOperationTypeDesc());
//            acctCheckingQueryRpcDTO.setAccountType(fundFreezenFlow.getAccountModel());
//            acctCheckingQueryRpcDTO.setAccountSubType(fundFreezenFlow.getAccountSubType());
//        }

        // 报销子账户
        AcctReimbursementFlow acctReimbursementFlow = getAcctReimbursementFlow(bizNo);
        if (!ObjUtils.isNull(acctReimbursementFlow)){
            builderAcctReimbursementRespDTO(acctCheckingQueryRpcDTO,acctReimbursementFlow);
            return acctCheckingQueryRpcDTO;
        }
        return acctCheckingQueryRpcDTO;
    }

    @Override
    public AcctCheckingQueryRpcDTO queryByBizNoAndTxnId(String bizNo, String txnId) {
        AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO = this.queryByBizNo(bizNo);
        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryBySyncBankTransNo(txnId);
        if (ObjUtils.isNotEmpty(fundFreezenFlow)){
            acctCheckingQueryRpcDTO.setCompanyId(fundFreezenFlow.getCompanyId());
            List<AccountGeneral> byCompanyId = accountGeneralService.findByCompanyId(fundFreezenFlow.getCompanyId());
            if(ObjUtils.isNotEmpty(byCompanyId)){
                acctCheckingQueryRpcDTO.setCompanyName(byCompanyId.get(0).getCompanyName());
            }
            acctCheckingQueryRpcDTO.setTradeType(getTradeTypeByFreezenOptType(fundFreezenFlow.getOperationType()));
            acctCheckingQueryRpcDTO.setOperationType(fundFreezenFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(fundFreezenFlow.getOperationTypeDesc());
            acctCheckingQueryRpcDTO.setAccountType(fundFreezenFlow.getAccountModel());
            acctCheckingQueryRpcDTO.setAccountSubType(fundFreezenFlow.getAccountSubType());
            //
            acctCheckingQueryRpcDTO.setBankTransNo(fundFreezenFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(fundFreezenFlow.getSyncBankTransNo());
        }
        return acctCheckingQueryRpcDTO;
    }

    @Override
    public AcctCheckingSpaPublicRpcDTO queryByFlowIdSpaPublic(String accountFlowId) {
        if(StringUtils.isEmpty(accountFlowId)){
            return null;
        }
        AccountPublicFlow acctPublicFlow = acctPublicFlowManager.queryByAcctFlowId(accountFlowId);
        if (ObjUtils.isNotEmpty(acctPublicFlow)) {
            AcctCheckingSpaPublicRpcDTO ret = new AcctCheckingSpaPublicRpcDTO();
            if(acctPublicFlow.getTradeType() == FundAccountOptType.TRANSFER_IN.getKey()){ // 221:资金入账-转账转入
                ret.setTransType(acctPublicFlow.getTradeType()); // 交易类型
                ret.setInOrOut(1);
            }else if(acctPublicFlow.getTradeType() == FundAccountOptType.TRANSFER_OUT.getKey()){ // 222:资金出账-转账转出
                ret.setTransType(acctPublicFlow.getTradeType()); // 交易类型
                ret.setInOrOut(0);
            }else if(acctPublicFlow.getTradeType() == FundAccountOptType.DISHONOURED.getKey()){ // 43:资金入账-退汇
                ret.setTransType(acctPublicFlow.getTradeType()); // 交易类型
                ret.setInOrOut(1);
            }
            ret.setInDiff(1);// 对公账户，全参与入差异库
            ret.setAccountType(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey()); // 账户类型
            ret.setAccountFlowId(accountFlowId);
            return ret;
        }
        AccountSpaInnerFlow accountSpaInnerFlow = accountSpaInnerFlowService.queryByAccountFlowId(accountFlowId);
        if (ObjUtils.isNotEmpty(accountSpaInnerFlow)) {
            AcctCheckingSpaPublicRpcDTO ret = new AcctCheckingSpaPublicRpcDTO();
            if(accountSpaInnerFlow.getTradeType() == FundAccountOptType.TRANSFER_IN.getKey()){ // 221:资金入账-转账转入
                ret.setTransType(accountSpaInnerFlow.getTradeType()); // 交易类型
                ret.setInOrOut(1);
            }else if(accountSpaInnerFlow.getTradeType() == FundAccountOptType.TRANSFER_OUT.getKey()){ // 222:资金出账-转账转出
                ret.setTransType(accountSpaInnerFlow.getTradeType()); // 交易类型
                ret.setInOrOut(0);
            }else if(accountSpaInnerFlow.getTradeType() == FundAccountOptType.DISHONOURED.getKey()){ // 43:资金入账-退汇
                ret.setTransType(accountSpaInnerFlow.getTradeType()); // 交易类型
                ret.setInOrOut(1);
            }
            if(SpaInnerAcctTypeEnum.INNER_ACCOUNT.getCode().equals(accountSpaInnerFlow.getAccountType())
                    || SpaInnerAcctTypeEnum.TRANSIENT_ACCOUNT.getCode().equals(accountSpaInnerFlow.getAccountType())
                    || SpaInnerAcctTypeEnum.ASSET_SUMMARY_ACCOUNT.getCode().equals(accountSpaInnerFlow.getAccountType()) ){
                ret.setAccountType(accountSpaInnerFlow.getAccountType());// 账户类型
            }
            ret.setInDiff(accountSpaInnerFlow.getShowStatus()); // 内部账户，自己决定是否入差异库
            ret.setAccountFlowId(accountFlowId);
            return ret;
        }
        return null;
    }

    @Override
    public AcctCheckingSuspendRpcDTO queryByFlowIdSuspend(String accountFlowId, Integer bankAcctType) {
        if(StringUtils.isEmpty(accountFlowId) || null == bankAcctType){
            return null;
        }
        BankAcctFlow bankAcctFlow = bankAcctFlowService.queryAcctFlowByFlowIdAndType(accountFlowId, bankAcctType);
        if (ObjUtils.isNotEmpty(bankAcctFlow)) {
            AcctCheckingSuspendRpcDTO suspendRpcDTO = new AcctCheckingSuspendRpcDTO();
            suspendRpcDTO.setAccountType(FundAccountSubType.SUSPEND_ACCOUNT.getKey());
            suspendRpcDTO.setAccountFlowId(accountFlowId);
            suspendRpcDTO.setBizNo(bankAcctFlow.getBizNo());
            suspendRpcDTO.setCompanyId(bankAcctFlow.getCompanyId());
            suspendRpcDTO.setBankAccountName(bankAcctFlow.getBankName());
            suspendRpcDTO.setTradeType(bankAcctFlow.getTradeType());
            suspendRpcDTO.setAccountSubType(FundAccountSubType.SUSPEND_ACCOUNT.getKey());
            suspendRpcDTO.setOperationType(bankAcctFlow.getOperationType());
            suspendRpcDTO.setOperationTypeDesc(bankAcctFlow.getOperationTypeDesc());
            suspendRpcDTO.setBankTransNo(bankAcctFlow.getBankTransNo());
            suspendRpcDTO.setBassTxnId(bankAcctFlow.getSyncBankTransNo());
            return suspendRpcDTO;
        }
        return null;
    }

    @Override
    public VirtualCardCheckingQueryRpcDTO queryVirtualCarInfoByBizNo(String bizNo) {
        if (Strings.isNullOrEmpty(bizNo)) {
            return null;
        }
        VirtualCardCheckingQueryRpcDTO result = new VirtualCardCheckingQueryRpcDTO();
        result.setBizNo(bizNo);
        BankUserCardRespDTO bankUserCardInfo = iBankUserCardSearchService.queryBankPersonCardInfo(bizNo);
        if (ObjUtils.isNotEmpty(bankUserCardInfo)) {
            result.setCompanyId(bankUserCardInfo.getCompanyId());
            Company company = iCompanyService.queryCompanyById(bankUserCardInfo.getCompanyId());
            if (ObjUtils.isNotEmpty(company)) {
                result.setCompanyName(company.getName());
            }
            result.setOpenBankAccountName(bankUserCardInfo.getBankName());
            result.setBankAccountNo(bankUserCardInfo.getBankAccountNo());
            result.setTradeType(bankUserCardInfo.getTradeType());
            result.setOperationType(bankUserCardInfo.getOperationType());
            result.setOperationTypeDesc(FundBankUserCardOptType.getEnum(bankUserCardInfo.getOperationType()).getDesc());
            result.setAccountFlowId(bankUserCardInfo.getUserCardFlowId());
            result.setBankTransNo(bankUserCardInfo.getBankTransNo());
            result.setBassTxnId(bankUserCardInfo.getSyncBankTransNo());
            result.setEmployeeName(bankUserCardInfo.getEmployeeName());
            result.setEmployeePhone(bankUserCardInfo.getEmployeePhone());
            result.setFlowSource("USER_CARD_FLOW");
            return result;
        }
        BankCardApplyFlow bankCardApplyFlow = null;
        try {
            if (!org.apache.commons.lang3.StringUtils.isNumeric(bizNo)) {
                return null;
            }
            Long id = Long.valueOf(bizNo);
            bankCardApplyFlow = applyCardManager.getApplyFlowById(id);
        }catch (Exception e) {
            return null;
        }
        if (ObjUtils.isNotEmpty(bankCardApplyFlow)) {
            result.setCompanyId(bankCardApplyFlow.getCompanyId());
            result.setCompanyName(bankCardApplyFlow.getCompanyName());
            result.setOpenBankAccountName(bankCardApplyFlow.getBankName());
            result.setBankAccountNo(bankCardApplyFlow.getBankAccountNo());
            result.setAccountType(bankCardApplyFlow.getAccountType());
            result.setOperationType(bankCardApplyFlow.getOperationType());
            result.setOperationTypeDesc(null == BankApplyType.getEnum(bankCardApplyFlow.getOperationType()) ? "未知" : BankApplyType.getEnum(bankCardApplyFlow.getOperationType()).getValue());
            result.setAccountFlowId(String.valueOf(bankCardApplyFlow.getId()));
            result.setBankTransNo(bankCardApplyFlow.getBankTransNo());
            result.setEmployeeName(bankCardApplyFlow.getEmployeeName());
            result.setEmployeePhone(bankCardApplyFlow.getEmployeePhone());
            result.setFlowSource("CART_APPLY_FLOW");
            return result;
        }
        return null;
    }

    @Override
    public PlatformAcctCheckingRpcDTO queryPlatformAcctFlow(String accountFlowId) {
        if(StringUtils.isEmpty(accountFlowId)){
            return null;
        }
        BankAcctFlow bankAcctFlow = bankAcctFlowService.queryPlatformAcctFlow(accountFlowId);
        if (ObjUtils.isNotEmpty(bankAcctFlow)) {
            PlatformAcctCheckingRpcDTO platformAcctCheckingDTO = new PlatformAcctCheckingRpcDTO();
            platformAcctCheckingDTO.setAccountType(bankAcctFlow.getBankAcctType());
            platformAcctCheckingDTO.setAccountFlowId(bankAcctFlow.getBankAcctFlowId());
            platformAcctCheckingDTO.setBizNo(bankAcctFlow.getBizNo());
            platformAcctCheckingDTO.setCompanyId(bankAcctFlow.getCompanyId());
            platformAcctCheckingDTO.setBankAccountName(bankAcctFlow.getBankName());
            platformAcctCheckingDTO.setTradeType(bankAcctFlow.getTradeType());
            platformAcctCheckingDTO.setOperationType(bankAcctFlow.getOperationType());
            platformAcctCheckingDTO.setOperationTypeDesc(bankAcctFlow.getOperationTypeDesc());
            platformAcctCheckingDTO.setBankTransNo(bankAcctFlow.getBankTransNo());
            platformAcctCheckingDTO.setBassTxnId(bankAcctFlow.getSyncBankTransNo());
            if (BankAcctTypeEnum.isGuaranteeBankAccount(bankAcctFlow.getBankAcctType())) {
                // 分贝券消费
                Integer type = null;
                if (FundPlatAcctOptType.VOUCHER_CONSUME.getKey() == bankAcctFlow.getOperationType()) {
                    type = 1;
                }
                if (FundPlatAcctOptType.VOUCHER_REFUND.getKey() == bankAcctFlow.getOperationType()) {
                    type = 2;
                }
                List<VouchersOperationFlow> vouchersOperationFlows = vouchersOperationFlowService.queryVouchersFlowByBizNoAndType(bankAcctFlow.getBizNo(), type);
                if (!ObjectUtils.isEmpty(vouchersOperationFlows) && !ObjUtils.isNull(vouchersOperationFlows.get(0))) {
                    platformAcctCheckingDTO.setEmployeeId(vouchersOperationFlows.get(0).getEmployeeId());
                    platformAcctCheckingDTO.setEmployeeName(vouchersOperationFlows.get(0).getEmployeeName());
                    platformAcctCheckingDTO.setEmployeePhone(vouchersOperationFlows.get(0).getEmployeePhone());
                }
            }
            return platformAcctCheckingDTO;
        }
        return null;
    }

    private Integer getTradeTypeByFreezenOptType(Integer operationType){
        if (operationType == FreezenChangeType.FREEZING.getKey()){
            return FundAcctTradeType.FREEZE.getCode();
        }else if (operationType == FreezenChangeType.UNFREEZING.getKey()){
            return FundAcctTradeType.UN_FREEZE.getCode();
        }else if (operationType == FreezenChangeType.PAY.getKey()){
            return FundAcctTradeType.CONSUME.getCode();
        }else if (operationType == FreezenChangeType.REFUND.getKey()){
            return FundAcctTradeType.REFUND.getCode();
        }
        return null;
    }
    private AcctBusinessCreditFlow getAcctBusinessCreditFlow(String bizNo){
        //商务授信账户
        AcctBusinessCreditFlow acctBusinessCreditFlow =  acctBusinessCreditFlowService.queryAccountSubFlowByFlowId(bizNo);
        return acctBusinessCreditFlow;
    }
    private void builderAcctBusinessCreditRespDTO(AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO,AcctBusinessCreditFlow acctBusinessCreditFlow){
        AcctBusinessCredit acctBusinessCredit = acctBusinessCreditService.findByAcctId(acctBusinessCreditFlow.getAccountId());
        acctCheckingQueryRpcDTO.setCompanyId(acctBusinessCredit.getCompanyId());
        acctCheckingQueryRpcDTO.setCompanyName(acctBusinessCredit.getCompanyName());
        acctCheckingQueryRpcDTO.setBankAccountName(acctBusinessCreditFlow.getBankName());
        acctCheckingQueryRpcDTO.setTradeType(acctBusinessCreditFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctBusinessCreditFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctBusinessCreditFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctBusinessCreditFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctBusinessCreditFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctBusinessCreditFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctBusinessCreditFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctBusinessCreditFlow.getSyncBankTransNo());
    }

    private AcctBusinessDebitFlow getAcctBusinessDebitFlow(String bizNo){
        //商务充值账户
        AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryAccountSubFlowByFlowId(bizNo);
        return acctBusinessDebitFlow;
    }

    private void builderAcctBusinessDebitRespDTO(AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO,AcctBusinessDebitFlow acctBusinessDebitFlow){
        AcctBusinessDebit acctBusinessDebit = acctBusinessDebitService.findByAcctId(acctBusinessDebitFlow.getAccountId());
        acctCheckingQueryRpcDTO.setCompanyId(acctBusinessDebit.getCompanyId());
        acctCheckingQueryRpcDTO.setCompanyName(acctBusinessDebit.getCompanyName());
        acctCheckingQueryRpcDTO.setBankAccountName(acctBusinessDebitFlow.getBankName());
        acctCheckingQueryRpcDTO.setTradeType(acctBusinessDebitFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctBusinessDebitFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctBusinessDebitFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctBusinessDebitFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctBusinessDebitFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctBusinessDebitFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctBusinessDebitFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctBusinessDebitFlow.getSyncBankTransNo());
    }

    private List<AcctIndividualCreditFlow> getAcctIndividualCreditFlow(String bizNo){
        List<AcctIndividualCreditFlow> acctIndividualCreditFlowList = acctIndividualCreditFlowService.queryAccountSubFlowByBizNo(bizNo,FundAcctCreditOptType.FROZEN_VOUCHER_GRANT.getKey());
        if (ObjUtils.isEmpty(acctIndividualCreditFlowList)) {
            acctIndividualCreditFlowList = acctIndividualCreditFlowService.queryAccountSubFlowByBizNo(bizNo,FundAcctCreditOptType.FROZEN_VOUCHER_RECALL.getKey());
        }
        return acctIndividualCreditFlowList;

    }
    private void builderAcctIndividualCreditFlowRespDTO(AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO,List<AcctIndividualCreditFlow> acctIndividualCreditFlowList){
        if (ObjUtils.isNotEmpty(acctIndividualCreditFlowList)){
            AcctIndividualCreditFlow acctIndividualCreditFlow = acctIndividualCreditFlowList.get(0);
            AcctIndividualCredit acctIndividualCredit = acctIndividualCreditService.findByAcctId(acctIndividualCreditFlow.getAccountId());
            acctCheckingQueryRpcDTO.setCompanyId(acctIndividualCredit.getCompanyId());
            acctCheckingQueryRpcDTO.setCompanyName(acctIndividualCredit.getCompanyName());
            acctCheckingQueryRpcDTO.setBankAccountName(acctIndividualCredit.getBankName());
            acctCheckingQueryRpcDTO.setTradeType(acctIndividualCreditFlow.getTradeType());
            acctCheckingQueryRpcDTO.setOperationType(acctIndividualCreditFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(acctIndividualCreditFlow.getOperationDescription());
            acctCheckingQueryRpcDTO.setAccountType(acctIndividualCreditFlow.getAccountModel());
            acctCheckingQueryRpcDTO.setAccountSubType(acctIndividualCreditFlow.getAccountSubType());
            acctCheckingQueryRpcDTO.setAccountFlowId(acctIndividualCreditFlow.getAccountFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(acctIndividualCreditFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(acctIndividualCreditFlow.getSyncBankTransNo());
        }
    }

    private List<AcctIndividualDebitFlow> getAcctIndividualDebitFlow(String bizNo){
        List<AcctIndividualDebitFlow> acctIndividualDebitFlowList = acctIndividualDebitFlowService.queryAccountSubFlowByBizNo(bizNo,FundAcctDebitOptType.FROZEN_VOUCHER_GRANT.getKey());
        if (ObjUtils.isEmpty(acctIndividualDebitFlowList)) {
            acctIndividualDebitFlowList = acctIndividualDebitFlowService.queryAccountSubFlowByBizNo(bizNo,FundAcctDebitOptType.FROZEN_VOUCHER_RECALL.getKey());
        }
        return acctIndividualDebitFlowList;

    }
    private void builderAcctIndividualDebitFlowRespDTO(AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO,List<AcctIndividualDebitFlow> acctIndividualDebitFlowList){
        if (ObjUtils.isNotEmpty(acctIndividualDebitFlowList)){
            AcctIndividualDebitFlow acctIndividualDebitFlow = acctIndividualDebitFlowList.get(0);
            AcctIndividualDebit acctIndividualDebit = acctIndividualDebitService.findByAcctId(acctIndividualDebitFlow.getAccountId());
            acctCheckingQueryRpcDTO.setCompanyId(acctIndividualDebit.getCompanyId());
            acctCheckingQueryRpcDTO.setCompanyName(acctIndividualDebit.getCompanyName());
            acctCheckingQueryRpcDTO.setBankAccountName(acctIndividualDebit.getBankName());
            acctCheckingQueryRpcDTO.setTradeType(acctIndividualDebitFlow.getTradeType());
            acctCheckingQueryRpcDTO.setOperationType(acctIndividualDebitFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(acctIndividualDebitFlow.getOperationDescription());
            acctCheckingQueryRpcDTO.setAccountType(acctIndividualDebitFlow.getAccountModel());
            acctCheckingQueryRpcDTO.setAccountSubType(acctIndividualDebitFlow.getAccountSubType());
            acctCheckingQueryRpcDTO.setAccountFlowId(acctIndividualDebitFlow.getAccountFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(acctIndividualDebitFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(acctIndividualDebitFlow.getSyncBankTransNo());
        }
    }
    private void builderAcctIndividualDebitFlowRespDTO(AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO,AcctIndividualDebitFlow acctIndividualDebitFlow){
        AcctIndividualDebit acctIndividualDebit = acctIndividualDebitService.findByAcctId(acctIndividualDebitFlow.getAccountId());
        acctCheckingQueryRpcDTO.setCompanyId(acctIndividualDebit.getCompanyId());
        acctCheckingQueryRpcDTO.setCompanyName(acctIndividualDebit.getCompanyName());
        acctCheckingQueryRpcDTO.setBankAccountName(acctIndividualDebit.getBankName());
        acctCheckingQueryRpcDTO.setTradeType(acctIndividualDebitFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctIndividualDebitFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctIndividualDebitFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctIndividualDebitFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctIndividualDebitFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctIndividualDebitFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctIndividualDebitFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctIndividualDebitFlow.getSyncBankTransNo());
    }
    private void builderAcctIndividualCreditFlowRespDTO(AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO,AcctIndividualCreditFlow acctIndividualCreditFlow){
        AcctIndividualCredit acctIndividualDebit = acctIndividualCreditService.findByAcctId(acctIndividualCreditFlow.getAccountId());
        acctCheckingQueryRpcDTO.setCompanyId(acctIndividualDebit.getCompanyId());
        acctCheckingQueryRpcDTO.setCompanyName(acctIndividualDebit.getCompanyName());
        acctCheckingQueryRpcDTO.setBankAccountName(acctIndividualDebit.getBankName());
        acctCheckingQueryRpcDTO.setTradeType(acctIndividualCreditFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctIndividualCreditFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctIndividualCreditFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctIndividualCreditFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctIndividualCreditFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctIndividualCreditFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctIndividualCreditFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctIndividualCreditFlow.getSyncBankTransNo());
    }


    private AcctReimbursementFlow getAcctReimbursementFlow(String bizNo){
        AcctReimbursementFlow acctReimbursementFlow = acctReimbursementFlowService.queryAccountSubFlowByFlowId(bizNo);
        return acctReimbursementFlow;
    }

    private void builderAcctReimbursementRespDTO(AcctCheckingQueryRpcDTO acctCheckingQueryRpcDTO,AcctReimbursementFlow acctReimbursementFlow){
        AcctReimbursement acctReimbursement = acctReimbursementService.findByAcctId(acctReimbursementFlow.getAccountId());
        acctCheckingQueryRpcDTO.setCompanyId(acctReimbursement.getCompanyId());
        acctCheckingQueryRpcDTO.setCompanyName(acctReimbursement.getCompanyName());
        acctCheckingQueryRpcDTO.setBankAccountName(acctReimbursementFlow.getBankName());
        acctCheckingQueryRpcDTO.setTradeType(acctReimbursementFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctReimbursementFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctReimbursementFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctReimbursementFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctReimbursementFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctReimbursementFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctReimbursementFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctReimbursementFlow.getSyncBankTransNo());
    }
}
