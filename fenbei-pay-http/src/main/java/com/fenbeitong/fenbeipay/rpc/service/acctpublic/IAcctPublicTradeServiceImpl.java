package com.fenbeitong.fenbeipay.rpc.service.acctpublic;

import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonDebitService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctGeneralFlowService;
import com.fenbeitong.fenbeipay.acctpublic.manager.AcctPublicFlowManager;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicSearchService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicTradeDechService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicTradeService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.accountpublic.AccountPublicStatus;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferBaseInfoDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferDebitReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.*;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicTradeService;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 对公支付交易
 * 支付。充值。提现
 * 退款
 *
 * @since 4.0.0
 */
@Service("iAcctPublicTradeService")
public class IAcctPublicTradeServiceImpl implements IAcctPublicTradeService {

    @Autowired
    private AcctPublicTradeService acctPublicTradeService;

    @Autowired
    protected AcctPublicFlowManager acctPublicFlowManager;

    @Autowired
    protected AcctPublicTradeDechService acctPublicTradeDechService;
    @Autowired
    protected AcctPublicSearchService acctPublicSearchService;
    @Autowired
    protected DingDingMsgService dingDingMsgService;

    @Autowired
    private UAcctCommonDebitService uAcctCommonDebitService;

    @Autowired
    protected UAcctGeneralFlowService uAcctGeneralFlowService;

    @Autowired
    private AcctCompanyMainService acctCompanyMainService;

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AcctPublicRechargeRespRPCDTO acctPublicCashRecharge(AcctPublicRechargeReqRPCDTO rechargeReqRPCDTO) {
        FinhubLogger.info("【充值—>对公账户充值模式】accountPublicRecharge 参数：{}", rechargeReqRPCDTO);
        try {
            //校验字段
            rechargeReqRPCDTO.checkReq(rechargeReqRPCDTO);
            return acctPublicTradeService.acctPublicCashRecharge(rechargeReqRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【充值—>对公账户充值模式系统异常】accountRecharge 参数：{}", JsonUtils.toJson(rechargeReqRPCDTO), e);
            String msgError = "【对公账户】【充值】异常:" + rechargeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【充值—>对公账户充值模式系统异常】accountRecharge 参数：{}", JsonUtils.toJson(rechargeReqRPCDTO), e);
            String msgError = "【对公账户】【充值】【请求参数】异常:" + rechargeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【充值—>对公账户充值模式系统异常】accountRecharge 参数：{}", JsonUtils.toJson(rechargeReqRPCDTO), e);
            String msgError = "【对公账户】【充值】系统异常:" + rechargeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AcctPublicCashWithdRespRPCDTO acctPublicCashWithdrawal(AcctPublicCashWithdReqRPCDTO cashWithdReqRPCDTO) {
        FinhubLogger.info("【提现—>对账户充值模式】acctPublicCashWithdrawal 参数：{}", cashWithdReqRPCDTO);
        try {
            //校验字段
            cashWithdReqRPCDTO.checkReq(cashWithdReqRPCDTO);
            return acctPublicTradeService.acctPublicCashWithdrawal(cashWithdReqRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【提现—>对公账户充值模式系统异常】acctPublicCashWithdrawal 参数：{}", JsonUtils.toJson(cashWithdReqRPCDTO), e);
            String msgError = "【对公账户】【提现】异常:" + cashWithdReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【提现—>对公账户充值模式系统异常】acctPublicCashWithdrawal 参数：{}", JsonUtils.toJson(cashWithdReqRPCDTO), e);
            String msgError = "【对公账户】【提现】参数异常:" + cashWithdReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【提现—>对公账户充值模式系统异常】acctPublicCashWithdrawal 参数：{}", JsonUtils.toJson(cashWithdReqRPCDTO), e);
            String msgError = "【对公账户】【提现】系统异常:" + cashWithdReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AcctPublicCashWithdRespRPCDTO acctPublicCancelWithdrawal(AcctPublicCancelWithdrawalRPCDTO cancelWithdrawalRPCDTO) {
        FinhubLogger.info("【取消—>提现对公账户】acctPublicCancelWithdrawal 参数：{}", cancelWithdrawalRPCDTO);
        try {
            //校验字段
            cancelWithdrawalRPCDTO.checkReq(cancelWithdrawalRPCDTO);
            return acctPublicTradeService.acctPublicCancelWithdrawal(cancelWithdrawalRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【取消—>提现对公账户系统异常】acctPublicCancelWithdrawal 参数：{}",
                    JsonUtils.toJson(cancelWithdrawalRPCDTO), e);
            String msgError = "【对公账户】【取消提现】异常:" + cancelWithdrawalRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【取消—>提现对公账户系统异常】acctPublicCancelWithdrawal 参数：{}",
                    JsonUtils.toJson(cancelWithdrawalRPCDTO), e);
            String msgError = "【对公账户】【取消提现】参数异常:" + cancelWithdrawalRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【取消—>提现对公账户系统异常】acctPublicCancelWithdrawal 参数：{}",
                    JsonUtils.toJson(cancelWithdrawalRPCDTO), e);
            String msgError = "【对公账户】【取消提现】系统异常:" + cancelWithdrawalRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Deprecated
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AcctPublicConsumeRespRPCDTO acctPublicConsume(AcctPublicConsumeReqRPCDTO consumeReqRPCDTO) {
        FinhubLogger.warn("Deprecated PublicPay acctPublicConsume()");
        FinhubLogger.info("Deprecated PublicPay 【消费—>对账户充值模式】acctPublicConsume 参数：{}", JsonUtils.toJson(consumeReqRPCDTO));
        try {
            //校验字段
            consumeReqRPCDTO.checkReq(consumeReqRPCDTO);
            AccountPublic accountPublic = acctPublicSearchService.queryByCompanyIdAndBankAccountNo(consumeReqRPCDTO.getCompanyId(),
                    consumeReqRPCDTO.getBankAccountNo());
            if (ObjUtils.isEmpty(accountPublic)){
                throw new FinPayException(GlobalResponseCode.ACCOUNT_PUBLIC_ORDER_NO_EXIST);
            }
            if(!AccountPublicStatus.isNormal(accountPublic.getAccountPublicStatus())){
                throw new FinPayException(GlobalResponseCode.ACCOUNT_PUBLIC_NORMAL_PAY);
            }
            // 查询主体信息
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(accountPublic.getCompanyId(), accountPublic.getCompanyMainId(), accountPublic.getBankAccountName());
            if (ObjUtils.isEmpty(acctCompanyMain)) {
                throw new FinPayException(GlobalResponseCode.MAIN_COMPANY_NO_EXIST);
            }
            //4.0银行直连扣款
            if(accountPublic.getDirectAcctType()==1){
               return acctPublicTradeDechService.acctPublicBankConsume(consumeReqRPCDTO, acctCompanyMain);
            }
            //老账户扣款（琥珀扣款）
            return acctPublicTradeService.acctPublicConsume(consumeReqRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【消费—>对公账户充值模式系统异常】acctPublicConsume 参数：{}",
                    JsonUtils.toJson(consumeReqRPCDTO), e);
            String msgError = "【对公账户】【消费】系统异常:" + consumeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【消费—>对公账户充值模式系统异常】acctPublicConsume 参数：{}",
                    JsonUtils.toJson(consumeReqRPCDTO), e);
            String msgError = "【对公账户】【消费】请求参数异常:" + consumeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【消费—>对公账户充值模式系统异常】acctPublicConsume 参数：{}",
                    JsonUtils.toJson(consumeReqRPCDTO), e);
            String msgError = "【对公账户】【消费】系统异常:" + consumeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AcctPublicCancelConsumeRespRPCDTO acctPublicCancelConsume(AcctPublicCancelConsumeReqRPCDTO cancelConsumeReqRPCDTO) {
        FinhubLogger.info("【退款—>对账户充值模式】acctPublicRefund 参数：{}", JsonUtils.toJson(cancelConsumeReqRPCDTO));
        try {
            //校验字段
            cancelConsumeReqRPCDTO.checkReq(cancelConsumeReqRPCDTO);
            AccountPublic accountPublic = acctPublicSearchService.queryByCompanyIdAndBankAccountNo(cancelConsumeReqRPCDTO.getCompanyId(),
                    cancelConsumeReqRPCDTO.getBankAccountNo());
            if (ObjUtils.isEmpty(accountPublic)){
                throw new FinPayException(GlobalResponseCode.ACCOUNT_PUBLIC_ORDER_NO_EXIST);
            }
            //4.0银行直连扣款
            if(accountPublic.getDirectAcctType()==1){
                return acctPublicTradeDechService.acctPublicCancelConsume(cancelConsumeReqRPCDTO);
            }
            //老账户扣款（湖泊扣款）
            return acctPublicTradeService.acctPublicCancelConsume(cancelConsumeReqRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【退款—>对公账户充值模式系统异常】acctPublicRefund 参数：{}", JsonUtils.toJson(cancelConsumeReqRPCDTO), e);
            String msgError = "【对公账户】【取消消费】异常:" + cancelConsumeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【退款—>对公账户充值模式系统异常】acctPublicRefund 参数：{}", JsonUtils.toJson(cancelConsumeReqRPCDTO), e);
            String msgError = "【对公账户】【取消消费】请求参数异常:" + cancelConsumeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【退款—>对公账户充值模式系统异常】acctPublicRefund 参数：{}", JsonUtils.toJson(cancelConsumeReqRPCDTO), e);
            String msgError = "【对公账户】【取消消费】系统异常:" + cancelConsumeReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public BigDecimal queryTotalConsumeAmountByOrderId(String orderId) {
        return acctPublicFlowManager.queryTotalConsumeAmountByOrderId(orderId);
    }

    @Override
    public AcctPublicDishonouredRespRPCDTO dishonoured(AcctPublicDishonouredReqRPCDTO acctPublicDishonouredReqRPCDTO) {
        FinhubLogger.info("【退汇】dishonoured 参数：{}", acctPublicDishonouredReqRPCDTO);
        try {
            AccountPublic accountPublic = acctPublicSearchService.queryByCompanyIdAndBankAccountNo(acctPublicDishonouredReqRPCDTO.getCompanyId(),
                    acctPublicDishonouredReqRPCDTO.getBankAccountNo());
            if (ObjUtils.isEmpty(accountPublic)){
                throw new FinPayException(GlobalResponseCode.ACCOUNT_PUBLIC_ORDER_NO_EXIST);
            }
            // 如果是中信（中信手动退汇，操作余额账户划钱）
            if(BankNameEnum.isCitic(accountPublic.getBankAccountName())){
                uAcctCommonDebitService.onlyTransferOut2OthersInner(createTransferReqDTO(accountPublic, acctPublicDishonouredReqRPCDTO));
                // 查询充值流水
                AccountGeneralFlow accountGeneralFlow = uAcctGeneralFlowService.queryByBankTransNo(acctPublicDishonouredReqRPCDTO.getBankTransNo());
                if(ObjUtils.isNull(accountGeneralFlow)){
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_PUBLIC_DISHONOURED_BANKTRANSNO_NO_EXIST);
                }
                acctPublicDishonouredReqRPCDTO.setBizTime(accountGeneralFlow.getSyncBankTime());
            }
            return acctPublicTradeDechService.dishonoured(acctPublicDishonouredReqRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【退款—>对公账户充值模式系统异常】acctPublicRefund 参数：{}", JsonUtils.toJson(acctPublicDishonouredReqRPCDTO), e);
            String msgError = "【对公账户】【取消消费】异常:" + acctPublicDishonouredReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【退款—>对公账户充值模式系统异常】acctPublicRefund 参数：{}", JsonUtils.toJson(acctPublicDishonouredReqRPCDTO), e);
            String msgError = "【对公账户】【取消消费】请求参数异常:" + acctPublicDishonouredReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【退款—>对公账户充值模式系统异常】acctPublicRefund 参数：{}", JsonUtils.toJson(acctPublicDishonouredReqRPCDTO), e);
            String msgError = "【对公账户】【取消消费】系统异常:" + acctPublicDishonouredReqRPCDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    private AcctTransferDebitReqDTO createTransferReqDTO(AccountPublic accountPublic, AcctPublicDishonouredReqRPCDTO acctPublicDishonouredReqRPCDTO) {
        AcctTransferDebitReqDTO acctTransferDebitReqDTO = new AcctTransferDebitReqDTO();
        BeanUtils.copyProperties(accountPublic, acctTransferDebitReqDTO);
        acctTransferDebitReqDTO.setBankName(accountPublic.getBankAccountName());
        List<AcctTransferBaseInfoDTO> subAcct = new ArrayList<>();
        AcctTransferBaseInfoDTO transferBaseInfoDTO = new AcctTransferBaseInfoDTO();
        transferBaseInfoDTO.setOperationAmount(acctPublicDishonouredReqRPCDTO.getOperationAmount());
        transferBaseInfoDTO.setAccountSubType(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey());
        transferBaseInfoDTO.setAccountId(accountPublic.getAccountPublicId());
        subAcct.add(transferBaseInfoDTO);
        acctTransferDebitReqDTO.setTransferList(subAcct);
        acctTransferDebitReqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
        acctTransferDebitReqDTO.setOperationUserName(CoreConstant.SYSTEM);
        return acctTransferDebitReqDTO;
    }
}
