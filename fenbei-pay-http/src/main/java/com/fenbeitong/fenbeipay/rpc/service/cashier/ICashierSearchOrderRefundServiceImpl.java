package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierPayDetailRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierSearchMultipleRefundTradeRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierPayDetailRPCVo;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierSearchBatchPageReqRPCVo;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierSearchOrderRefundService;
import com.fenbeitong.pay.search.service.CashierSearchOrderRefundService;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("iCashierSearchOrderRefundService")
public class ICashierSearchOrderRefundServiceImpl implements ICashierSearchOrderRefundService {


    @Autowired
    private CashierSearchOrderRefundService cashierSearchOrderRefundService;

    @Override
    public ResponsePage<CashierSearchMultipleRefundTradeRPCDTO> batchRefundQueryByPage(CashierSearchBatchPageReqRPCVo queryReqRPCVo) {
        ValidateUtils.validate(queryReqRPCVo);
        return cashierSearchOrderRefundService.searchMultipleBatchRefundByPage(queryReqRPCVo);
    }

    @Override
    public CashierPayDetailRPCDTO searchCashierRefundDetail(CashierPayDetailRPCVo cashierPayDetailRPCVo) {
        if (ObjUtils.isNull(cashierPayDetailRPCVo) || ObjUtils.isBlank(cashierPayDetailRPCVo.getFbOrderId()) || ObjUtils.isBlank(cashierPayDetailRPCVo.getRefundOrderId())) {
            return null;
        }
        return cashierSearchOrderRefundService.searchCashierRefundDetail(cashierPayDetailRPCVo.getFbOrderId(), cashierPayDetailRPCVo.getRefundOrderId());
    }

    @Override
    public List<CashierPayDetailRPCDTO> searchCashierRefundDetail4Saturn(String refundOrderId) {
        return cashierSearchOrderRefundService.searchCashierRefundDetail4Saturn(refundOrderId);
    }

    @Override
    public CashierPayDetailRPCDTO searchAccountRefundFlowDetail(CashierPayDetailRPCVo cashierPayDetailRPCVo) {
        return cashierSearchOrderRefundService.searchAccountRefundFlowDetail(cashierPayDetailRPCVo);
    }

    @Override
    public List<CashierPayDetailRPCDTO> searchCashierRefundDetail4SaturnByFbOrderId(String fbOrderId) {
        return cashierSearchOrderRefundService.searchCashierRefundDetail4SaturnByFbOrderId(fbOrderId);
    }
}
