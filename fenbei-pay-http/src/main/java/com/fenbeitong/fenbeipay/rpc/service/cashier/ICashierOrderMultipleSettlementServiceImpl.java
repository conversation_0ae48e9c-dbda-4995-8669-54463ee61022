package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierMultipleCreateTradeRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierMultipleCreateTradeRPCVo;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderMultipleSettlementService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierMultipleOrderSettlementService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierCreateTradeReqVo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service("iCashierOrderMultipleSettlementService")
public class ICashierOrderMultipleSettlementServiceImpl implements ICashierOrderMultipleSettlementService {

    @Autowired
    private CashierMultipleOrderSettlementService cashierMultipleOrderSettlementService;

    @Autowired
    private CashierOrderSettlementService  cashierOrderSettlementService;


    @Override
    public CashierMultipleCreateTradeRPCDTO createOrderMultiplePayTradeAndSaas(CashierMultipleCreateTradeRPCVo cashierMultipleCreateTradeRPCVo) {
        FinhubLogger.info("【收银台】【RPC创建支付交易参数】：{}", cashierMultipleCreateTradeRPCVo.toString());
        CashierCreateTradeReqVo cashierCreateTradeReqVo = new CashierCreateTradeReqVo();
        BeanUtils.copyProperties(cashierMultipleCreateTradeRPCVo, cashierCreateTradeReqVo);
        CashierMultipleCreateTradeRPCDTO tradeRPCDTO = null;
        try {
            tradeRPCDTO = cashierMultipleOrderSettlementService.createOrderMultiplePayTradeAndSaas(cashierMultipleCreateTradeRPCVo);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【收银台】【RPC创建支付交易异常】：{}", cashierMultipleCreateTradeRPCVo.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【收银台】【RPC创建支付交易异常】：{}", cashierMultipleCreateTradeRPCVo.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("【收银台】【RPC创建支付交易异常】：{}", cashierMultipleCreateTradeRPCVo.toString(), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.error("【收银台】【RPC创建支付交易异常】：{}", cashierMultipleCreateTradeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【收银台】【RPC创建支付交易异常】：{}", cashierMultipleCreateTradeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        //通知
        CompletableFuture.runAsync(() -> cashierOrderSettlementService.checkPayStatusAndCallBiz(cashierMultipleCreateTradeRPCVo.getFbOrderId(),cashierMultipleCreateTradeRPCVo.getFbTradeId(), cashierMultipleCreateTradeRPCVo.getEmployeeId()));
        return tradeRPCDTO;
    }


}
