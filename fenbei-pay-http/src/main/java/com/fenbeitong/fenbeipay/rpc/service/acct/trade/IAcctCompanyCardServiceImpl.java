package com.fenbeitong.fenbeipay.rpc.service.acct.trade;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenChangeType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctConsumeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctRefundReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCompanyCardRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOperationRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.upgrade.TransferFreezeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByAcctTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountCardApplyAmountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountCardRefundAmountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountConsumeFreezeReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountRefundFreezeReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubOperationRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctCompanyCardService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.dto.freezen.FreezenOperationReqDTO;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezen;
import com.fenbeitong.fenbeipay.nf.service.FundFreezenFlowService;
import com.fenbeitong.fenbeipay.nf.service.FundFreezenService;
import com.fenbeitong.fenbeipay.nf.unit.service.UFundFreezenService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.utils.RandomUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.FREEZEN_BUDGET_FREEZING_ERROR;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.FREEZEN_BUDGET_UNFREEZING_ERROR;

/**
 * 企业虚拟卡
 */
@Service("iAcctCompanyCardService")
public class IAcctCompanyCardServiceImpl implements IAcctCompanyCardService {

    @Autowired
    private UAcctCompanyCardService uAcctCompanyCardService;
    @Autowired
    private UFundFreezenService uFundFreezenService;
    @Autowired
    private UAcctBusinessDebitService uAcctBusinessDebitService;
    @Autowired
    private UAcctBusinessCreditService uAcctBusinessCreditService;
    @Autowired
    protected UCompanySwitchService uCompanySwitchService;
    @Autowired
    protected AcctCompanyGatewayService acctCompanyGatewayService;

    @Autowired
    private FundFreezenService fundFreezenService;

    @Autowired
    private FundFreezenFlowService fundFreezenFlowService;

    @Override
    public List<AcctCompanyCardRespDTO> getCompanyAcctCard(String companyId, String platformName, String bankName) {
        try {
            return uAcctCompanyCardService.findDtoByBankName(companyId,platformName,bankName);
        }catch (FinPayException e) {
            FinhubLogger.error("【获取企业虚拟卡信息】参数：{}", companyId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }catch (Exception e) {
            FinhubLogger.error("【获取企业虚拟卡信息】参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void frozen(AccountCardApplyAmountReqDTO reqDTO) {
        FinhubLogger.info("【虚拟卡】申请额度冻结公司资金，入参：{}", JSON.toJSONString(reqDTO));
        try {
            //扣减账户 增加冻结池 银行侧扣款 看是否存在企业虚拟卡账户 并且与网关生效一致
            FreezenOperationReqDTO freezeBudgetDTO = new FreezenOperationReqDTO();
            BeanUtils.copyProperties(reqDTO, freezeBudgetDTO);
            boolean companySwitch = uCompanySwitchService.isCompanySwitch(reqDTO.getCompanyId());
            if(companySwitch){
                AccountSubOperationRespRPCDTO respRPCDTO = uAcctCompanyCardService.frozen(reqDTO);
                freezeBudgetDTO.setBankTransNo(respRPCDTO.getBankTransNo());
                freezeBudgetDTO.setAccountId(respRPCDTO.getAccountSubId());
            } else {
                AcctConsumeReqDTO acctConsumeReqDTO = new AcctConsumeReqDTO();
                BeanUtils.copyProperties(reqDTO, acctConsumeReqDTO);
                acctConsumeReqDTO.setBankName(BankNameEnum.FBT.getCode());
                acctConsumeReqDTO.setBankAccountNo(reqDTO.getTargetAccount());
                acctConsumeReqDTO.setTargetBankName(BankNameEnum.FBT.getCode());
                acctConsumeReqDTO.setTargetBankAccountNo(reqDTO.getBankAccountNo());
                acctConsumeReqDTO.setFundAccountOptType(FundAccountOptType.CONSUME.getKey());
                acctConsumeReqDTO.setOperationType(FundAcctDebitOptType.FROZEN_BANK_RECALL);
                acctConsumeReqDTO.setCompanyCardOptType(FundAcctCompanyCardOptType.FROZEN_BANK_RECALL);
                AcctOperationRespDTO respDTO;
                if(FundAccountModelType.isRecharge(reqDTO.getAccountModel())){
                    respDTO = uAcctBusinessDebitService.consume(acctConsumeReqDTO);
                }else {
                    acctConsumeReqDTO.setFundAcctCreditOptType(FundAcctCreditOptType.FROZEN_BANK_RECALL);
                    respDTO = uAcctBusinessCreditService.consume(acctConsumeReqDTO);
                }
                freezeBudgetDTO.setAccountId(respDTO.getAccountSubId());
            }
            //加入冻结池
            freezeBudgetDTO.setBankName(reqDTO.getFundPlatformName());
            freezeBudgetDTO.setBankAcctId(reqDTO.getPayAccountNo());
            freezeBudgetDTO.setVerifyNo(reqDTO.getBizNo());
            freezeBudgetDTO.setAccountSubType(reqDTO.getAccountSubType());
            freezeBudgetDTO.setFreezenUseType(FreezenUseType.getEnumByBankName(reqDTO.getBankName()));
            freezeBudgetDTO.setBankAccountNo(reqDTO.getTargetAccount());
            uFundFreezenService.freezeBudget4Card(freezeBudgetDTO);
        }catch (FinPayException e) {
            FinhubLogger.warn("【虚拟卡冻结】参数：{}", JSON.toJSONString(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }catch (Exception e) {
            FinhubLogger.error("【虚拟卡冻结】参数：{}", JSON.toJSONString(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void unfreeze(AccountCardRefundAmountReqDTO reqDTO) {
        try {
            //如果平台是分贝通 扣除商务充值账户
            FreezenOperationReqDTO freezeBudgetDTO = new FreezenOperationReqDTO();
            BeanUtils.copyProperties(reqDTO, freezeBudgetDTO);
            if(FundAccountSubType.isComCardAccount(reqDTO.getAccountSubType())){
                AccountSubOperationRespRPCDTO respRPCDTO = uAcctCompanyCardService.unfreeze(reqDTO);
            }else {
                AcctRefundReqDTO acctRefundReqDTO = new AcctRefundReqDTO();
                BeanUtils.copyProperties(reqDTO, acctRefundReqDTO);
                acctRefundReqDTO.setBankName(BankNameEnum.FBT.getCode());
                acctRefundReqDTO.setBankAccountNo(reqDTO.getTargetAccount());
                acctRefundReqDTO.setTargetBankName(BankNameEnum.FBT.getCode());
                acctRefundReqDTO.setTargetBankAccountNo(reqDTO.getBankAccountNo());
                acctRefundReqDTO.setOperationType(FundAcctDebitOptType.FROZEN_BANK_REFUND);
                acctRefundReqDTO.setFundAccountOptType(FundAccountOptType.REFUND.getKey());
                acctRefundReqDTO.setCompanyCardOptType(FundAcctCompanyCardOptType.FROZEN_BANK_REFUND);
                //区分商务充值/授信
                AcctOperationRespDTO respDTO;
                if(FundAccountModelType.isRecharge(reqDTO.getAccountModel())){
                    respDTO = uAcctBusinessDebitService.refund(acctRefundReqDTO);
                }else {
                    acctRefundReqDTO.setAcctCreditOptType(FundAcctCreditOptType.FROZEN_BANK_REFUND);
                    respDTO = uAcctBusinessCreditService.refund(acctRefundReqDTO);
                }
            }
            //扣减冻结池
            freezeBudgetDTO.setBankName(reqDTO.getFundPlatformName());
            freezeBudgetDTO.setVerifyNo(reqDTO.getBizNo());
            freezeBudgetDTO.setAccountSubType(reqDTO.getAccountSubType());
            freezeBudgetDTO.setFreezenUseType(FreezenUseType.getEnumByBankName(reqDTO.getBankName()));
            freezeBudgetDTO.setAccountId(reqDTO.getAccountId());
            uFundFreezenService.unfreezeBudget4Card(freezeBudgetDTO);
        }catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡解冻】参数：{}", reqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }catch (Exception e) {
            FinhubLogger.error("【虚拟卡解冻】参数：{}", reqDTO, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public Boolean refundFreezeBudget(AccountRefundFreezeReqRPCDTO reqDTO) {
        try {
            FreezenOperationReqDTO freezeBudgetDTO = new FreezenOperationReqDTO();
            BeanUtils.copyProperties(reqDTO, freezeBudgetDTO);
            freezeBudgetDTO.setAccountId(reqDTO.getAccountSubId());
            freezeBudgetDTO.setVerifyNo(reqDTO.getBizNo());
            uFundFreezenService.refundFreezeBudget4Card(freezeBudgetDTO);
        }catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡解冻】参数：{}", reqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }catch (Exception e) {
            FinhubLogger.error("【虚拟卡解冻】参数：{}", reqDTO, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean consumeFreezeBudget(AccountConsumeFreezeReqRPCDTO freezeDTO) {
        try {
            FreezenOperationReqDTO freezeBudgetDTO = new FreezenOperationReqDTO();
            BeanUtils.copyProperties(freezeDTO, freezeBudgetDTO);
            freezeBudgetDTO.setAccountId(freezeDTO.getAccountSubId());
            freezeBudgetDTO.setVerifyNo(freezeDTO.getBizNo());
            uFundFreezenService.payFreezeBudget4Card(freezeBudgetDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡冻结】参数：{}", freezeDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡冻结】参数：{}", freezeDTO, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        return Boolean.TRUE;
    }

    /**
     * 升级用
     * @param transferFreezeReqDTO 迁移冻结池
     */
    @Override
    public void transferFreeze4Upgrade(TransferFreezeReqDTO transferFreezeReqDTO) {
        FundFreezen fundFreezen = uFundFreezenService.queryFundFreezen(transferFreezeReqDTO.getEleAccountId(), FreezenUseType.BANK_CG_ACCOUNT);
        FundFreezen oldFundFrozen = uFundFreezenService.queryFundFreezen(transferFreezeReqDTO.getOldAccountId(), FreezenUseType.BANK_CG_ACCOUNT);
        if (Objects.isNull(fundFreezen)) {
            AcctCommonBaseDTO currentAvailableAccount = acctCompanyGatewayService.findActCommonByAcctType(new AcctComGwByAcctTypeReqDTO(transferFreezeReqDTO.getCompanyId(), FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey()));

            FreezenOperationReqDTO freezeBudgetDTO = new FreezenOperationReqDTO();
            //新电子账户
            freezeBudgetDTO.setBankAccountNo(currentAvailableAccount.getBankAccountNo());
            freezeBudgetDTO.setBankName(BankNameEnum.CGB.getCode());
            freezeBudgetDTO.setAccountGeneralId(currentAvailableAccount.getAccountGeneralId());
            freezeBudgetDTO.setCompanyMainId(currentAvailableAccount.getCompanyMainId());
            freezeBudgetDTO.setAccountId(currentAvailableAccount.getAccountId());
            freezeBudgetDTO.setCompanyId(currentAvailableAccount.getCompanyId());
            freezeBudgetDTO.setAccountSubType(currentAvailableAccount.getAccountSubType());
            freezeBudgetDTO.setFreezenId(RandomUtils.bsonId());
            freezeBudgetDTO.setFreezenUseType(FreezenUseType.getEnum(oldFundFrozen.getUseType()));
            freezeBudgetDTO.setAccountModel(currentAvailableAccount.getAccountModel());
            freezeBudgetDTO.setBankAcctId(currentAvailableAccount.getBankAcctId());
            fundFreezen = fundFreezenService.createFreezen(transferFreezeReqDTO.getCompanyId(),freezeBudgetDTO);
        }
        //旧账户冻结金额减少
        if (!fundFreezenService.unfreezeBudget(oldFundFrozen, transferFreezeReqDTO.getAmount())) {
            FinhubLogger.error("【冻结池】解冻操作时，余额不足，无法扣除金额，需要解冻的金额:{}，冻结池余额:{}，请求参数：{}", transferFreezeReqDTO.getAmount(), fundFreezen.getFreezeBalance(), transferFreezeReqDTO);
            throw new FinPayException(FREEZEN_BUDGET_UNFREEZING_ERROR);
        }
        String upId = IDGen.genUpgradeId(transferFreezeReqDTO.getCompanyId());
        //先从旧账户扣减
        FreezenOperationReqDTO oldFrozenRequest = new FreezenOperationReqDTO();
        oldFrozenRequest.setFreezenFlowId(IDGen.genFreezenFlowId(transferFreezeReqDTO.getCompanyId()));
        oldFrozenRequest.setOperationAmount(transferFreezeReqDTO.getAmount());
        oldFrozenRequest.setOperationDescription("解冻");
        oldFrozenRequest.setBizNo(upId);
        oldFrozenRequest.setOperationUserId(transferFreezeReqDTO.getEmployeeId());
        oldFrozenRequest.setOperationUserName(transferFreezeReqDTO.getEmployeeName());
        oldFrozenRequest.setVerifyNo(upId);
        BigDecimal unFreezeBalance = oldFundFrozen.getFreezeBalance().subtract(transferFreezeReqDTO.getAmount());
        fundFreezenFlowService.saveOperationFlow(oldFundFrozen, oldFrozenRequest, unFreezeBalance, FreezenChangeType.UNFREEZING, FundAcctSyncBankStatus.NO_SYNC.getCode());


        //冻结操作
        if (!fundFreezenService.freezeBudget(fundFreezen, transferFreezeReqDTO.getAmount())) {
            throw new FinPayException(FREEZEN_BUDGET_FREEZING_ERROR);
        }
        String upId4Frozen = IDGen.genUpgradeId(transferFreezeReqDTO.getCompanyId());
        FreezenOperationReqDTO newFrozenRequest = new FreezenOperationReqDTO();
        newFrozenRequest.setFreezenFlowId(IDGen.genFreezenFlowId(transferFreezeReqDTO.getCompanyId()));
        newFrozenRequest.setOperationAmount(transferFreezeReqDTO.getAmount());
        newFrozenRequest.setOperationDescription("冻结");
        newFrozenRequest.setBizNo(upId4Frozen);
        newFrozenRequest.setOperationUserId(transferFreezeReqDTO.getEmployeeId());
        newFrozenRequest.setOperationUserName(transferFreezeReqDTO.getEmployeeName());
        newFrozenRequest.setVerifyNo(upId4Frozen);
        //再给新账户追加
        BigDecimal freezeBalance = fundFreezen.getFreezeBalance().add(transferFreezeReqDTO.getAmount());
        fundFreezenFlowService.saveOperationFlow(fundFreezen, newFrozenRequest, freezeBalance, FreezenChangeType.FREEZING, FundAcctSyncBankStatus.NO_SYNC.getCode());
    }

}