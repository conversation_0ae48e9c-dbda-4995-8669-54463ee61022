package com.fenbeitong.fenbeipay.rpc.service.acct.trade;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.api.enums.AuthPayStatusEnum;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthPayRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthPayRespDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.QueryAccountBalanceRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.QueryAccountBalanceRespDTO;
import com.fenbeitong.dech.api.service.airwallex.AirwallexAuthPayService;
import com.fenbeitong.fenbeipay.acctdech.service.PayCompanyAuthService;
import com.fenbeitong.fenbeipay.api.model.dto.AuthRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.QueryAuthRequestDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctConsumeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctRefundReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOperationRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByAcctTypeReqDTO;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctAppPayService;
import com.fenbeitong.fenbeipay.api.service.gateway.IAcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierChannelPayOrderMapper;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierChannelPayOrder;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.dto.acctdech.CompanyAuthRecord;
import com.fenbeitong.fenbeipay.rpc.service.acct.IAcctAbstractService;
import com.fenbeitong.fenbeipay.rpc.service.auth.CompanyAuthInnerService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.concurrent.*;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.*;

/**
 * 因公支付接口
 * <AUTHOR>
 */
@Service("iAcctAppPayService")
public class IAcctAppPayServiceImpl extends IAcctAbstractService implements IAcctAppPayService {

    @Autowired
    private IAcctCompanyGatewayService iAcctCompanyGatewayService;
    @Autowired
    private DingDingMsgService dingDingMsgService;
    //TODO 查询airwallex帐户
    @Autowired
    CashierChannelPayOrderMapper cashierChannelPayOrderMapper;
    // 初始化线程池
    ExecutorService threadPoolOfAirwallex = Executors.newFixedThreadPool(5);
    @Autowired
    AirwallexAuthPayService airwallexAuthPayService;
    @Autowired
    CompanyAuthInnerService companyAuthInnerService;
    @Autowired
    PayCompanyAuthService payCompanyAuthService;
    @Override
    public AcctOperationRespDTO consume(AcctConsumeReqDTO accountConsumeDTO) throws FinAccountNoEnoughException {
        FinhubLogger.info("【账户消费】consume 参数：{}", accountConsumeDTO);
        try {
            // 查询帐户和帐户余额
            boolean isAirwallex = queryAccountOfAirwallex(accountConsumeDTO.getCompanyId());
            if (isAirwallex){
                checkBalanceAvailable(accountConsumeDTO);
                return airwallexPay(accountConsumeDTO);
            }
            return businessPay(accountConsumeDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        }  catch (FinPayException e) {
            FinhubLogger.error("【账户消费】consume 参数：{}", JsonUtils.toJson(accountConsumeDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【账户消费】consume 参数：{}", JsonUtils.toJson(accountConsumeDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【账户消费】consume 参数：{}", JsonUtils.toJson(accountConsumeDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctOperationRespDTO refund(AcctRefundReqDTO accountRefundDTO) throws FinAccountNoEnoughException {
        try {

            ValidateUtils.validate(accountRefundDTO);
            if(ObjUtils.isEmpty(accountRefundDTO.getAccountModel())){
                throw new FinPayException(ACCOUNT_SUB_MODEL_NO_REFUND);
            }
            CashierChannelPayOrder cashierChannelPayOrder = cashierChannelPayOrderMapper.queryByFbOrderId(accountRefundDTO.getBizNo());
            // 查询帐户和帐户余额
            boolean isAirwallexOrder = (cashierChannelPayOrder != null);
            if (isAirwallexOrder){
                return airwallexRefund(accountRefundDTO,cashierChannelPayOrder);
            }
            return businessRefund(accountRefundDTO);

        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】refund 参数：{}", accountRefundDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】refund 参数：{}", accountRefundDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】refund 参数：{}", accountRefundDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    public boolean queryAccountOfAirwallex(String companyId) {
        try {
            CompanyAuthRecord companyAuthRecord = payCompanyAuthService.queryAuthRecordByCompanyId(companyId);
            return companyAuthRecord != null && companyAuthRecord.getStatus() == 1;
        }catch (Exception e){
            FinhubLogger.warn("queryAuthResultByAirwallex:{}" + companyId,e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode());
        }
    }
    public void checkBalanceAvailable(AcctConsumeReqDTO accountConsumeDTO){
        //查询余额
        BigDecimal balanceOfAirwallex = BigDecimal.ZERO;
        QueryAccountBalanceRequestDTO queryAccountBalanceRequestDTO = new QueryAccountBalanceRequestDTO();
        queryAccountBalanceRequestDTO.setCompanyId(accountConsumeDTO.getCompanyId());
        try {
            QueryAccountBalanceRespDTO queryAccountBalanceRespDTO = airwallexAuthPayService.queryAWAccountBalance(queryAccountBalanceRequestDTO);
            FinhubLogger.info("查询airwallex账户余额->{} 操作金额->{}", JSON.toJSONString(queryAccountBalanceRespDTO), accountConsumeDTO.getOperationAmount());
            if (ObjUtils.isNotEmpty(queryAccountBalanceRespDTO)){
                balanceOfAirwallex = queryAccountBalanceRespDTO.getAvailableAmount();
            }
            if (BigDecimalUtils.hasPrice(accountConsumeDTO.getOperationAmount())) {
                if(accountConsumeDTO.getOperationAmount().compareTo(balanceOfAirwallex)>0){
                    throw new FinAccountNoEnoughException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getCode(),GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getMsg(),GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getType());
                }
            }
        }catch (Exception e){
            FinhubLogger.warn("queryAWAccountBalance:{}" + accountConsumeDTO.getCompanyId(),e);
            throw new FinAccountNoEnoughException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getCode(),GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getMsg(),GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getType());
        }
    }
    public AcctOperationRespDTO airwallexPay(AcctConsumeReqDTO accountConsumeDTO){
        //创建订单cashier_airwallex_pay_order
        CashierChannelPayOrder cashierChannelPayOrder = new CashierChannelPayOrder();
        cashierChannelPayOrder.setId(IDGen.genId("AWX"));
        cashierChannelPayOrder.setChannel("AIRWALLEX");
        cashierChannelPayOrder.setOrderType("PAY");
        cashierChannelPayOrder.setFbOrderId(accountConsumeDTO.getBizNo());
        cashierChannelPayOrder.setCompanyId(accountConsumeDTO.getCompanyId());
        cashierChannelPayOrder.setCashierTxnId(accountConsumeDTO.getCashierTxnId());
        cashierChannelPayOrder.setPayTxnId(IDGen.genId("AWX"));
        cashierChannelPayOrder.setAmount(accountConsumeDTO.getOperationAmount());
//        cashierChannelPayOrder.setRefundTxnId();
//        cashierChannelPayOrder.setPayAccount();
//        cashierChannelPayOrder.setReceiveAccount();
        cashierChannelPayOrder.setOrderStatus(0);
        int count = cashierChannelPayOrderMapper.insert(cashierChannelPayOrder);
        if (count > 0){
            AirwallexTask airwallexTask = new AirwallexTask(cashierChannelPayOrder);
            Future<CashierChannelPayOrder> airwallexTaskResult = threadPoolOfAirwallex.submit(airwallexTask);
            // 主线程获取异步任务的执行结果
            try {
                CashierChannelPayOrder remoteCallResult  = airwallexTaskResult.get(10L,TimeUnit.SECONDS);
                cashierChannelPayOrderMapper.updateStatus(remoteCallResult.getId(),remoteCallResult.getOrderStatus());
                //查询扣款结果
                AcctOperationRespDTO respRPCDTO = new AcctOperationRespDTO();
                respRPCDTO.setBankName("Airwallex");
                respRPCDTO.setBankAccountNo(remoteCallResult.getPayAccount());
                return respRPCDTO;
            } catch (InterruptedException | ExecutionException e) {
                FinhubLogger.warn("");
                e.printStackTrace();
                throw new FinhubException(GlobalResponseCode.ACCOUNT_AIRWALLEX_ERROR.getCode());
            } catch (TimeoutException e) {
                FinhubLogger.warn("");
                throw new FinhubException(GlobalResponseCode.ACCOUNT_AIRWALLEX_ERROR.getCode());
            }
        }else {
            throw new FinhubException(GlobalResponseCode.ACCOUNT_AIRWALLEX_ERROR.getCode());
        }
    }

    public AcctOperationRespDTO airwallexRefund(AcctRefundReqDTO acctRefundReqDTO,CashierChannelPayOrder cashierChannelPayOrderOfPay){


        //创建订单cashier_airwallex_pay_order
        CashierChannelPayOrder cashierChannelPayOrder = new CashierChannelPayOrder();
        cashierChannelPayOrder.setId(IDGen.genId("AWX"));
        cashierChannelPayOrder.setChannel("AIRWALLEX");
        cashierChannelPayOrder.setOrderType("REFUND");
        cashierChannelPayOrder.setFbOrderId(acctRefundReqDTO.getBizNo());
        cashierChannelPayOrder.setPayTxnId(cashierChannelPayOrderOfPay.getPayTxnId());
        cashierChannelPayOrder.setCompanyId(acctRefundReqDTO.getCompanyId());
        cashierChannelPayOrder.setCashierTxnId(acctRefundReqDTO.getCashierTxnId());
        cashierChannelPayOrder.setRefundTxnId(acctRefundReqDTO.getRefundTxnId());
        cashierChannelPayOrder.setPayAccount(cashierChannelPayOrderOfPay.getReceiveAccount());
        cashierChannelPayOrder.setReceiveAccount(cashierChannelPayOrderOfPay.getPayAccount());
        cashierChannelPayOrder.setOrderStatus(4);
        cashierChannelPayOrder.setAmount(acctRefundReqDTO.getOperationAmount());
        int count = cashierChannelPayOrderMapper.insert(cashierChannelPayOrder);
        if (count > 0){
            AirwallexTask airwallexTask = new AirwallexTask(cashierChannelPayOrder);
            Future<CashierChannelPayOrder> airwallexTaskResult = threadPoolOfAirwallex.submit(airwallexTask);
            // 主线程获取异步任务的执行结果
            try {
                CashierChannelPayOrder remoteCallResult  = airwallexTaskResult.get(10L,TimeUnit.SECONDS);
                cashierChannelPayOrderMapper.updateStatus(remoteCallResult.getId(),remoteCallResult.getOrderStatus());
                //查询扣款结果
                AcctOperationRespDTO respRPCDTO = new AcctOperationRespDTO();
                respRPCDTO.setBankName("Airwallex");
                respRPCDTO.setBankAccountNo(cashierChannelPayOrderOfPay.getReceiveAccount());
                respRPCDTO.setTargetBankAccountNo(cashierChannelPayOrderOfPay.getPayAccount());
                return respRPCDTO;
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                FinhubLogger.warn("",e);
                throw new FinhubException(GlobalResponseCode.ACCOUNT_AIRWALLEX_ERROR.getCode());
            }
        }else {
            throw new FinhubException(GlobalResponseCode.ACCOUNT_AIRWALLEX_ERROR.getCode());
        }
    }

    // 静态内部类，实现Callable接口的任务类
    class AirwallexTask implements Callable<CashierChannelPayOrder> {
        protected CashierChannelPayOrder cashierChannelPayOrder;

        public AirwallexTask(CashierChannelPayOrder cashierChannelPayOrder) {
            this.cashierChannelPayOrder = cashierChannelPayOrder;
        }

        @Override
        public CashierChannelPayOrder call() {
            AuthPayRequestDTO authPayRequestDTO = new AuthPayRequestDTO();
            authPayRequestDTO.setAmount(cashierChannelPayOrder.getAmount());
            authPayRequestDTO.setCompanyId(cashierChannelPayOrder.getCompanyId());
            authPayRequestDTO.setOrderId(cashierChannelPayOrder.getFbOrderId());
            AuthPayRespDTO authPayRespDTO = null;
            if ("PAY".equals(cashierChannelPayOrder.getOrderType())) {
                authPayRequestDTO.setRequestId(cashierChannelPayOrder.getPayTxnId());
                authPayRequestDTO.setReference("PAY");
                authPayRespDTO = airwallexAuthPayService.syncPay(authPayRequestDTO);
            }else {
                authPayRequestDTO.setRequestId(IDGen.genId("AWX"));
                authPayRequestDTO.setReference("REFUND");
                authPayRespDTO = airwallexAuthPayService.refund(authPayRequestDTO);
            }
            if (ObjUtils.isNotEmpty(authPayRespDTO)) {
                AuthPayStatusEnum authPayStatusEnum = AuthPayStatusEnum.getByEcode(authPayRespDTO.getStatus());
                if (authPayStatusEnum == null){
                    cashierChannelPayOrder.setPayAccount(authPayRespDTO.getCompanyId());
                    cashierChannelPayOrder.setOrderStatus(1);
                }else {
                    if (Objects.equals(authPayStatusEnum.getCode(), AuthPayStatusEnum.SETTLED.getCode())) {
                        cashierChannelPayOrder.setPayAccount(authPayRespDTO.getCompanyId());
                        cashierChannelPayOrder.setOrderStatus(2);
                    }else if (Objects.equals(authPayStatusEnum.getCode(), AuthPayStatusEnum.FAILED.getCode())){
                        cashierChannelPayOrder.setPayAccount(authPayRespDTO.getCompanyId());
                        cashierChannelPayOrder.setOrderStatus(3);
                    }else {
                        cashierChannelPayOrder.setPayAccount(authPayRespDTO.getCompanyId());
                        cashierChannelPayOrder.setOrderStatus(1);
                    }
                }
            }
            return this.cashierChannelPayOrder;
        }
    }

    public AcctOperationRespDTO businessPay(AcctConsumeReqDTO accountConsumeDTO){
        AcctOperationRespDTO respRPCDTO;
        //获取网关信息
        AcctComGwByAcctTypeReqDTO comGwReqDTO = new AcctComGwByAcctTypeReqDTO(accountConsumeDTO.getCompanyId(),FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        AcctCommonBaseDTO actGwDto = iAcctCompanyGatewayService.findActCommonByAcctType(comGwReqDTO);
        if(ObjUtils.isEmpty(actGwDto)){
            throw new FinPayException(GW_APP_PAY_NOT_EXIST);
        }
        if (BigDecimalUtils.hasPrice(accountConsumeDTO.getOperationAmount())) {
            if(accountConsumeDTO.getOperationAmount().compareTo(actGwDto.getBalance())>0){
                throw new FinAccountNoEnoughException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_ENOUGH.getCode(),GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_ENOUGH.getMsg(),GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_ENOUGH.getType());
            }
        }
        BeanUtils.copyProperties(actGwDto,accountConsumeDTO);
        accountConsumeDTO.setBankName(actGwDto.getBankName());
        accountConsumeDTO.setBankAccountNo(actGwDto.getBankAccountNo());
        BankNameEnum bankNameEnum = BankNameEnum.valueOf(actGwDto.getBankName());
        if(Objects.nonNull(bankNameEnum)){
            accountConsumeDTO.setTargetBankName(actGwDto.getBankName());
        }
        accountConsumeDTO.setOperationType(FundAcctDebitOptType.PUBLIC_CONSUME);
        //校验字段
        ValidateUtils.validate(accountConsumeDTO);
        if(FundAccountModelType.isRecharge(actGwDto.getAccountModel())){
            //充值
            accountConsumeDTO.setOperationType(FundAcctDebitOptType.PUBLIC_CONSUME);
            respRPCDTO = uAcctBusinessDebitService.consume(accountConsumeDTO);
            return respRPCDTO;
        }else if(FundAccountModelType.isCredit(actGwDto.getAccountModel())){
            //授信
            accountConsumeDTO.setFundAcctCreditOptType(FundAcctCreditOptType.PUBLIC_CONSUME);
            respRPCDTO = uAcctBusinessCreditService.consume(accountConsumeDTO);
            return respRPCDTO;
        }else {
            throw new FinPayException(GW_APP_PAY_NOT_EXIST);
        }
    }

    public AcctOperationRespDTO businessRefund(AcctRefundReqDTO accountRefundDTO){
        AcctOperationRespDTO respRPCDTO;
        //对手账户
        String bankName = accountRefundDTO.getBankName();
        //各银行平台
        if (Objects.nonNull(bankName)&&BankNameEnum.isNotFbt(bankName)) {
            //众邦等银行
            BankNameEnum bankNameEnum = BankNameEnum.valueOf(bankName);
            //TODO 是bankName非空还是bankNameEnum 非UNKNOWN
            if (Objects.nonNull(bankNameEnum)) {
                //分贝通在各个银行的收款账户
                accountRefundDTO.setTargetBankName(bankName);
            }else{
                dingDingMsgService.sendMsg("【新资金账户4.0:退款时，银行未知】员工Id：" + JsonUtils.toJson(accountRefundDTO));
                throw new FinPayException(BANK_BUSINESS_REFUND_UNBANK_ERROR);
            }
        }else {
            //分贝通平台
            accountRefundDTO.setBankName(BankNameEnum.FBT.getCode());
            accountRefundDTO.setBankAccountNo(accountRefundDTO.getCompanyId());
            accountRefundDTO.setTargetBankAccountNo(accountRefundDTO.getCompanyId());
            accountRefundDTO.setTargetBankName(BankNameEnum.FBT.getCode());
        }
        if(FundAccountModelType.isRecharge(accountRefundDTO.getAccountModel())){
            //充值
            accountRefundDTO.setOperationType(FundAcctDebitOptType.PUBLIC_CONSUME_REFUND);
            respRPCDTO = uAcctBusinessDebitService.refund(accountRefundDTO);
            return respRPCDTO;
        }else if(FundAccountModelType.isCredit(accountRefundDTO.getAccountModel())){
            //授信
            accountRefundDTO.setAcctCreditOptType(FundAcctCreditOptType.PUBLIC_CONSUME_REFUND);
            respRPCDTO = uAcctBusinessCreditService.refund(accountRefundDTO);
            return respRPCDTO;
        }else {
            throw new FinPayException(GW_APP_PAY_NOT_EXIST);
        }
    }
}
