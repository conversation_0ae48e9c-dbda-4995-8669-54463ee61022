package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierCreatePayTradeRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierCreatePayTradeRPCVo;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementLimitRequestService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.core.annotation.LimitRequest;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.common.LimitRequestEnum;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierCreatePayTradeReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierCreatePayTradeRespVo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service("iCashierOrderSettlementLimitRequestService")
public class ICashierOrderSettlementLimitRequestServiceImpl implements ICashierOrderSettlementLimitRequestService {

    @Autowired
    private CashierOrderSettlementService cashierOrderSettlementService;

    /**
     * 创建支付交易流水+支付完成+返回支付结果+无管控预算
     * 使用场景：回填单+审批单，只能有商务账户(因公)扣款
     * 创建支付流水号，并且支付后，直接返回支付结果，没有支付回掉，没有APP上用户点击"立即支付"
     *
     * @param createPayTradePPCVo
     * @return
     */
    @Override
    @LimitRequest(LimitRequestEnum.CREATE_PAY_ORDER_TRADE)
    public CashierCreatePayTradeRPCDTO createAndPayOrderTrade(CashierCreatePayTradeRPCVo createPayTradePPCVo) {
        FinhubLogger.info("【频次限制接口】【收银台】【RPC回填单创建并支付参数】：{}", createPayTradePPCVo.toString());
        createPayTradePPCVo = ValidateUtils.validate(createPayTradePPCVo);
        CashierCreatePayTradeReqVo createPayTradeReqVo = new CashierCreatePayTradeReqVo();
        BeanUtils.copyProperties(createPayTradePPCVo, createPayTradeReqVo);
        //校验参数
        ValidateUtils.validate(createPayTradeReqVo);
        boolean sassBudget = false;
        CashierCreatePayTradeRespVo createPayTradeRespVo = null;
        try {
            createPayTradeRespVo = cashierOrderSettlementService.createAndPayOrderTradeLimit(createPayTradeReqVo, sassBudget);
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【频次限制接口】【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), e);
            throw e;
        } catch (FinPayException e) {
            FinhubLogger.error("【频次限制接口】【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("【频次限制接口】【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.error("【频次限制接口】【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【频次限制接口】【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        CashierCreatePayTradeRPCDTO createPayTradeRPCDTO = new CashierCreatePayTradeRPCDTO();
        BeanUtils.copyProperties(createPayTradeRespVo, createPayTradeRPCDTO);
        //回调场景并发送对账消息
        CompletableFuture.runAsync(() -> cashierOrderSettlementService.callCashierSettlement(createPayTradeRPCDTO.getFbOrderId(), createPayTradeRPCDTO.getCashierTxnId(), createPayTradeRPCDTO.getEmployeeId()));
        return createPayTradeRPCDTO;
    }
}