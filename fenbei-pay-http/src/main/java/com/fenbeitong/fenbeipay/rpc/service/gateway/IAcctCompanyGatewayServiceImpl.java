package com.fenbeitong.fenbeipay.rpc.service.gateway;

import com.fenbeitong.fenbeipay.acctdech.unit.service.AcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByAcctTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByBankNameReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByComIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwCreateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwUpdateListReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwUpdateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctComGwAcctRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctComGwRespDTO;
import com.fenbeitong.fenbeipay.api.service.gateway.IAcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 网关接口
 */
@Service("iAcctCompanyGatewayService")
public class IAcctCompanyGatewayServiceImpl implements IAcctCompanyGatewayService {

    @Autowired
    private AcctCompanyGatewayService acctCompanyGatewayService;

    @Override
    public void initActGws(AcctComGwCreateReqDTO reqDTO) {
        FinhubLogger.info("【支付网关】addActGws 参数：{}", reqDTO);
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            acctCompanyGatewayService.initActGws(reqDTO);
        }  catch (FinPayException e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    @Override
    public void updateActGws(AcctComGwUpdateListReqDTO reqDTO) {
        FinhubLogger.info("【支付网关】findActGwsDto 参数：{}", reqDTO);
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            acctCompanyGatewayService.updateActGws(reqDTO);
        }  catch (FinPayException e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void updateActGw(AcctComGwUpdateReqDTO reqDTO) {
        FinhubLogger.info("【支付网关】findActGwsDto 参数：{}", reqDTO);
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            acctCompanyGatewayService.updateActGw(reqDTO);
        }  catch (FinPayException e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】addActGws 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 获取所有激活的账户网关，不包含账户详情
     * @param reqDTO
     * @return
     * @throws FinhubException
     */
    @Override
    public AcctComGwRespDTO findActGwsByComId(AcctComGwByComIdReqDTO reqDTO) throws FinhubException {
        FinhubLogger.info("【支付网关】findActGwsDto 参数：{}", reqDTO);
        AcctComGwRespDTO gwRespDTO;
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            gwRespDTO = acctCompanyGatewayService.findActGwsByComId(reqDTO);
            return gwRespDTO;
        }  catch (FinPayException e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    /**
     * 根据公司Id&账户类型获取为网关信息，不包含账户详情
     * @param reqDTO
     * @return
     * @throws FinhubException
     */
    @Override
    public AcctComGwAcctRespDTO findActGwByAcctType(AcctComGwByAcctTypeReqDTO reqDTO) throws FinhubException {
        FinhubLogger.info("【支付网关】findActGwDto 参数：{}", JsonUtils.toJson(reqDTO));
        AcctComGwAcctRespDTO acctRespDTO;
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            acctRespDTO = acctCompanyGatewayService.findActGwByAcctType(reqDTO);
            return acctRespDTO;
        }  catch (FinPayException e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<AcctCommonBaseDTO> findActCommonByComId(AcctComGwByAcctTypeReqDTO reqDTO) throws FinhubException {
        FinhubLogger.info("【支付网关】findActGwDto 参数：{}", reqDTO);
        List<AcctCommonBaseDTO> dtoList;
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            dtoList  = acctCompanyGatewayService.findActCommonByComId(reqDTO);
            return dtoList;
        }  catch (FinPayException e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    /**
     * 根据公司Id&账户类型获取为网关信息，包含账户详情
     * @param reqDTO
     * @return
     * @throws FinhubException
     */
    @Override
    public AcctCommonBaseDTO findActCommonByAcctType(AcctComGwByAcctTypeReqDTO reqDTO) throws FinhubException {
        FinhubLogger.info("【支付网关】findActGwDto 参数：{}", reqDTO);
        AcctCommonBaseDTO acctRespDTO;
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            acctRespDTO = acctCompanyGatewayService.findActCommonByAcctType(reqDTO);
            return acctRespDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】findActGwsDto 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<AcctCommonBaseDTO> findActCommonByBankName(AcctComGwByBankNameReqDTO reqDTO) throws FinhubException {
        FinhubLogger.info("【支付网关】findActCommonByBankName 参数：{}", reqDTO);
        List<AcctCommonBaseDTO> dtoList;
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            dtoList  = acctCompanyGatewayService.findActCommonByBackName(reqDTO);
            return dtoList;
        }  catch (FinPayException e) {
            FinhubLogger.error("【支付网关】findActCommonByBankName 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】findActCommonByBankName 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】findActCommonByBankName 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<AcctCommonBaseDTO> findActCommonByBank(AcctComGwByBankReqDTO reqDTO) throws FinhubException {
        FinhubLogger.info("【支付网关】findActCommonByBank 参数：{}", reqDTO);
        List<AcctCommonBaseDTO> actCommonByBank;
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            actCommonByBank = acctCompanyGatewayService.findActCommonByBank(reqDTO);
            return actCommonByBank;
        }  catch (FinPayException e) {
            FinhubLogger.error("【支付网关】findActCommonByBank 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付网关】findActCommonByBank 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【支付网关】findActCommonByBank 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


}
