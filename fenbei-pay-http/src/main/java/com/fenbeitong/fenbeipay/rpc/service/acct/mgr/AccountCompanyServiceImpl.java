package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.bank.api.service.IBankPaymentService;
import com.fenbeitong.fenbei.settlement.base.enums.account.AccountModelEnum;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanySwitchService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctGeneralService;
import com.fenbeitong.fenbeipay.api.constant.enums.group.CompanyStateChangeEnum;
import com.fenbeitong.fenbeipay.api.model.vo.group.GroupCompanyValidationReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.group.GroupCompanyValidationVO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.AccountCompanyService;
import com.fenbeitong.fenbeipay.core.constant.personpay.CompanySwitchConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanySwitch;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.CompanyMainTypeEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyPlatformAccountListDTO;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.google.common.collect.Lists;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2022-07-28 10:40:29 
*/
@Service("accountCompanyService")
public class AccountCompanyServiceImpl implements AccountCompanyService {
	
	@Autowired
	private UAcctCommonService uAcctCommonService;
	
	@Autowired
	private ICompanyService iCompanyService;
	
	@Autowired
    private AcctCompanySwitchService acctCompanySwitchService;
	
	@Autowired
	private IBankPaymentService iBankPaymentService;
	
	@Autowired
    protected UAcctGeneralService uAcctGeneralService;
	
	private static final String TIPS_ACCOUNT3 = "3.0版本账户不可切换集团版，请先升级企业账户";
	
	private static final String TIPS_CREATE_ACCT_AUTH = "公司存在其他主体开户权限";
	
	private static final String TIPS_PUB_PAY = "公司存在进行中的对公付款";
	
	private static final String tips = "不满足关联为集团公司的要求，请检查：\n1.是否已完成账户升级；\n2.是否有生效的其他主体账户；\n3.是否有生效的其他主体开户权限；\n4.是否有未完结的对公付款；";

	@Override
	public GroupCompanyValidationVO checkIfCompanyShouldJoinGroup(List<GroupCompanyValidationReqVO> requests) {
		FinhubLogger.info("checkIfCompanyShouldJoinGroup--,param->{}", JSON.toJSONString(requests));
		// 公司是否是3.0账户、是否存在其他主体启用账户、是否存在其他主体开户权限、是否存在进行中的对公付款 
		if (CollectionUtils.isEmpty(requests)) {
			FinhubLogger.warn("checkIfCompanyShouldJoinGroup param is null");
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		List<GroupCompanyValidationVO> resp = Lists.newArrayList();
		
		for (GroupCompanyValidationReqVO request : requests) {
			if (StringUtils.isBlank(request.getCompanyId()) || Objects.isNull(request.getChangeType())) {
				FinhubLogger.warn("checkIfCompanyShouldJoinGroup illegal param: param is null");
				throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
			}
			
			GroupCompanyValidationVO vo = GroupCompanyValidationVO.builder()
					.companyId(request.getCompanyId())
					.enable(true)
					.reasons(Lists.newArrayList())
					.build();
			resp.add(vo);
			
			List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(request.getCompanyId());
	    	if (CollectionUtils.isEmpty(accountGenerals)) {
	    		FinhubLogger.warn("no account exists companyId->{}", request.getCompanyId());
	    	} else {
	    		List<AcctCompanySwitch> acu =  acctCompanySwitchService.getAcctCompanySwitchByCompanyId(request.getCompanyId());
				if (CollectionUtils.isEmpty(acu) || 
						acu.stream()
						.noneMatch(s -> Objects.equals(CompanySwitchConstant.NORMAL.getKey(), s.getAccountStatus()))) {
					vo.setEnable(false);
					vo.getReasons().add(TIPS_ACCOUNT3);
				}
				
				if (CompanyStateChangeEnum.NORMAL_TO_SUBSIDIARY == request.getChangeType()) {
					try {
						uAcctCommonService.checkIfCompanyHaveOtherEntityAccount(request.getCompanyId());
					} catch (FinPayException e) {
						if (Objects.equals(GlobalResponseCode.GROUP_COMPANY_HAS_OTHER_ENTITY.getCode(), e.getCode())) {
							vo.setEnable(false);
							vo.getReasons().add(GlobalResponseCode.GROUP_COMPANY_HAS_OTHER_ENTITY.getMsg());
						}
					}
					
					List<CompanyPlatformAccountListDTO> cpas = iCompanyService.queryCompanyPlatformAccountList(request.getCompanyId());
					boolean hasOtherEntityAuthority = Optional.of(cpas).orElse(Collections.emptyList())
							.stream()
							.anyMatch(cp -> Objects.equals(CompanyMainTypeEnum.COMPANY_MAIN_OTHER.getKey(), cp.getAccountHolder()) && 
									Objects.equals(1, cp.getPermissionStatus()));
					if (hasOtherEntityAuthority) {
						vo.setEnable(false);
						vo.getReasons().add(TIPS_CREATE_ACCT_AUTH);
					}
					
					boolean existsPublicPayInProgress = iBankPaymentService.isPublicPaymentInProgress(request.getCompanyId());
					if (existsPublicPayInProgress) {
						vo.setEnable(false);
						vo.getReasons().add(TIPS_PUB_PAY);
					}
				}
	    	}
			
			Optional.of(vo).filter(v -> !v.isEnable() && CollectionUtils.isNotEmpty(v.getReasons()))
				.ifPresent(v -> {
					String msg = v.getReasons().stream().collect(Collectors.joining("\n"));
					v.setReasons(Arrays.asList(msg));
					v.setMessage(msg);
				});
		}
		
		List<GroupCompanyValidationVO> tmp = resp.stream().filter(r -> !r.isEnable() && CollectionUtils.isNotEmpty(r.getReasons())).collect(Collectors.toList());
		if (requests.size() > 1 && CollectionUtils.isNotEmpty(tmp)) {
			String cnames = tmp.parallelStream()
					.map(GroupCompanyValidationVO :: getCompanyId)
					.map(iCompanyService :: queryCompanyById)
					.filter(Objects :: nonNull)
					.map(Company :: getName)
					.collect(Collectors.joining("、"));
			GroupCompanyValidationVO result = GroupCompanyValidationVO.builder()
					.enable(false)
					.message(cnames + tips)
					.build();
			FinhubLogger.info("checkIfCompanyShouldJoinGroup result is {}", JSON.toJSONString(result));
			return result;
		}
		
		FinhubLogger.info("checkIfCompanyShouldJoinGroup result is {}", JSON.toJSONString(resp.get(0)));
		return resp.get(0);
	}

	@Override
	public Boolean checkIfCompanyHasAccount(String companyId) {
		if (StringUtils.isBlank(companyId)) {
			FinhubLogger.warn("checkIfCompanyHasAccount param is null");
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		List<AccountGeneral> accounts = uAcctGeneralService.findByCompanyId(companyId);
		
		return Optional.ofNullable(accounts).orElse(Collections.emptyList())
				.stream()
				.anyMatch(acc -> Objects.equals(AccountModelEnum.RECHARGE_MODEL.getKey(), acc.getAccountModel()) && 
						!BankNameEnum.isFbt(acc.getBankName()));
	}
}
