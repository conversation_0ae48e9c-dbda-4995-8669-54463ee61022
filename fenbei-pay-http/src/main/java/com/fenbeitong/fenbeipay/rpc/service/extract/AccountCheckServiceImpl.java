package com.fenbeitong.fenbeipay.rpc.service.extract;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fenbeitong.fenbeipay.api.base.PageBeanReqDTO;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AccountCheckDiffQueryDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AccountCheckDiffDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.CheckCompanyWhitelistDTO;
import com.fenbeitong.fenbeipay.api.service.extract.AccountCheckService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.extract.manager.AccountCheckDiffService;
import com.fenbeitong.fenbeipay.extract.manager.CheckCompanyWhitelistService;
import com.fenbeitong.fenbeipay.extract.model.dto.CheckCompanyWhitelist;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2022-11-03 02:52:33 
*/
@Service("accountCheckService")
public class AccountCheckServiceImpl implements AccountCheckService {

	@Value("${check.whitelist.enable:false}")
	private boolean whitelistEnable;
	
	@Autowired
	private AccountCheckDiffService accountCheckDiffService;
	
	@Autowired
	private CheckCompanyWhitelistService checkCompanyWhitelistService;
	
	@Autowired
	private ICompanyService companyService;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addCompanyWhitelist(String[] companyIds) {
		if (Objects.isNull(companyIds) || companyIds.length == 0) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		for (String companyId : companyIds) {
			Company company = companyService.queryCompanyById(companyId);
			if (Objects.isNull(company)) {
				throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
			}
			
			CheckCompanyWhitelist model = CheckCompanyWhitelist.builder()
					.companyId(companyId)
					.companyName(company.getName())
					.fbId(company.getAlias())
					.status(1)
					.build();
			checkCompanyWhitelistService.save(model);
		}
		
		return true;
	}

	@Override
	public boolean isCompanyInWhitelist(String companyId) {
		if (StringUtils.isBlank(companyId)) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		if (whitelistEnable) {
			return true;
		}
		
		CheckCompanyWhitelist whitelist = checkCompanyWhitelistService.queryByCompanyId(companyId);
		return Objects.nonNull(whitelist);
	}

	@Override
	public boolean deleteCompanyFromWhitelist(String companyId) {
		if (StringUtils.isBlank(companyId)) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		boolean done = checkCompanyWhitelistService.deleteByCompanyId(companyId);
		return done;
	}

	@Override
	public ResponsePage<CheckCompanyWhitelistDTO> queryByPage(PageBeanReqDTO page) {
		if (Objects.isNull(page) || Objects.isNull(page.getPageNo()) || Objects.isNull(page.getPageSize())) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
 		ResponsePage<CheckCompanyWhitelistDTO> resp = checkCompanyWhitelistService.queryByPage(page);
		return resp;
	}

	@Override
	public ResponsePage<AccountCheckDiffDTO> queryCheckDiff(AccountCheckDiffQueryDTO query) {
		if (Objects.isNull(query)) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		ResponsePage<AccountCheckDiffDTO> resp = accountCheckDiffService.query(query);
		return resp;
	}

}
