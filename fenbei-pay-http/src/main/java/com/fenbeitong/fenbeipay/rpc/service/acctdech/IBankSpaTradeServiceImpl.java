package com.fenbeitong.fenbeipay.rpc.service.acctdech;

import com.fenbeitong.fenbeipay.acctdech.service.BankSpaBankAcctLimitService;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.BankAcctLimitRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.bank.SpaBankAcctLimitReqVO;
import com.fenbeitong.fenbeipay.api.service.acctdech.IBankSpaTradeService;
import com.fenbeitong.fenbeipay.dto.bank.BankSpaAcctLimit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("iBankSpaTradeService")
public class IBankSpaTradeServiceImpl implements IBankSpaTradeService {
    @Autowired
    BankSpaBankAcctLimitService bankSpaBankAcctLimitService;

    /**
     * 同步交易
     * @param spaBankAcctLimitReqVO 限额请求参数
     */
    public void syncTrade(SpaBankAcctLimitReqVO spaBankAcctLimitReqVO){
        bankSpaBankAcctLimitService.createBankAcctLimit(spaBankAcctLimitReqVO);
    }

    @Override
    public BankAcctLimitRespDTO queryAcctLimit(SpaBankAcctLimitReqVO spaBankAcctLimitReqVO) {
        BankSpaAcctLimit bankSpaAcctLimit = bankSpaBankAcctLimitService.getBankAcctLimits(spaBankAcctLimitReqVO.getBankAccountNo());
        BankAcctLimitRespDTO bankAcctLimitRespDTO = new BankAcctLimitRespDTO();
        bankAcctLimitRespDTO.setBankAccountNo(spaBankAcctLimitReqVO.getBankAccountNo());
        if (bankSpaAcctLimit != null){
            bankAcctLimitRespDTO.setLimitInYearAmount(bankSpaAcctLimit.getLimitInYearAmount());
            bankAcctLimitRespDTO.setLimitOutYearAmount(bankSpaAcctLimit.getLimitOutYearAmount());
            bankAcctLimitRespDTO.setLimitInDayAmount(bankSpaAcctLimit.getLimitInDayAmount());
            bankAcctLimitRespDTO.setLimitOutDayAmount(bankSpaAcctLimit.getLimitOutDayAmount());
        }
        return bankAcctLimitRespDTO;
    }
}
