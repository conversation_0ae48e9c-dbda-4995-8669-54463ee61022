package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.fenbeitong.fenbeipay.acctdech.service.BankAcctFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.BankAcctService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UBankAcctFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UBankAcctService;
import com.fenbeitong.fenbeipay.acctdech.utils.ObjectUtil;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.BankAcctTransDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.BankAcctTransReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOperationRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.BankAcctInfoRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctFlowSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctFlowUpdateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.BankAcctFlowSearchRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.BankAcctMainRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.BankAcctSearchRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IBankAcctService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.AutoAcctCheckingEventUtil;
import com.fenbeitong.fenbeipay.core.utils.BankConfigUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcct;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcctFlow;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.finhub.common.constant.BankAcctTypeEnum;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundPlatAcctOptType;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;
import com.luastar.swift.base.json.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName IBankAcctServiceImpl
 * @Description: 银行账户管理实现
 * <AUTHOR>
 * @Date 2021/4/25
 **/
@Service("iBankAcctService")
public class IBankAcctServiceImpl implements IBankAcctService {

    @Autowired
    private UBankAcctService uBankAcctService;

    @Autowired
    private UBankAcctFlowService uBankAcctFlowService;

    @Autowired
    private AccountGeneralService accountGeneralService;

    @Autowired
    private AutoAcctCheckingEventUtil autoAcctCheckingEventUtil;

    @Autowired
    protected BankAcctFlowService bankAcctFlowService;

    @Autowired
    private BankAcctService bankAcctService;

    @Override
    public ResponsePage<BankAcctSearchRespDTO> queryBankAcctListByPage(BankAcctSearchReqDTO queryReq) {
        return uBankAcctService.queryBankAcctListByPage(queryReq);
    }

    @Override
    public ResponsePage<BankAcctFlowSearchRespDTO> queryBankAcctFlowListByPage(BankAcctFlowSearchReqDTO queryReq) {
        return uBankAcctFlowService.queryBankAcctFlowListByPage(queryReq);
    }

    @Override
    public List<BankAcctMainRespDTO> queryMainList() {
        return uBankAcctService.queryMainList();
    }

    @Override
    public AcctOperationRespDTO recharge(String bankName, String bankAccountNo, Integer bankAcctType,
        BankAcctTransReqDTO bankAcctTransReqDTO) {
        AcctOperationRespDTO acctOperationRespDTO = null;
        try {
            BankAcct bankAcct = uBankAcctService.findBankAcctByBankAccountNo(bankName, bankAccountNo, bankAcctType);
            if (Objects.isNull(bankAcct)) {
                throw new FinPayException(GlobalResponseCode.INNER_NO_EXIST);
            }
            BankAcctTransDTO bankAcctTransDTO = completBankAcctTransDTO(bankAcctTransReqDTO, bankAcct);
            acctOperationRespDTO = uBankAcctService.addBalanceByAccountId(bankAcct.getAccountId(),
                bankAcctTransDTO.getOperationAmount(), bankAcctTransDTO);
        } catch (Exception e) {
            FinhubLogger.error("bankAcctService|recharge 异常：", e);
            throw e;
        }
        return acctOperationRespDTO;
    }

    @Override
    public AcctOperationRespDTO deduction(String bankName, String bankAccountNo, Integer bankAcctType,
        BankAcctTransReqDTO bankAcctTransReqDTO) {
        AcctOperationRespDTO acctOperationRespDTO = null;
        try {
            BankAcct bankAcct = uBankAcctService.findBankAcctByBankAccountNo(bankName, bankAccountNo, bankAcctType);
            if (Objects.isNull(bankAcct)) {
                throw new FinPayException(GlobalResponseCode.INNER_NO_EXIST);
            }
            BankAcctTransDTO bankAcctTransDTO = completBankAcctTransDTO(bankAcctTransReqDTO, bankAcct);
            acctOperationRespDTO = uBankAcctService.reduceBalanceByAccountId(bankAcct.getAccountId(),
                bankAcctTransDTO.getOperationAmount(), bankAcctTransDTO);
        } catch (Exception e) {
            FinhubLogger.error("bankAcctService|deduction 异常：", e);
            throw e;
        }
        return acctOperationRespDTO;
    }

    private BankAcctTransDTO completBankAcctTransDTO(BankAcctTransReqDTO bankAcctTransReqDTO, BankAcct bankAcct) {
        BankAcctTransDTO bankAcctTransDTO = new BankAcctTransDTO();
        BeanUtils.copyProperties(bankAcctTransReqDTO, bankAcctTransDTO, "operationType");
        bankAcctTransDTO.setOperationType(FundPlatAcctOptType.getEnum(bankAcctTransReqDTO.getOperationType()));
        if (StringUtils.isNotEmpty(bankAcctTransDTO.getBankAccountNo())) {
            AccountGeneral accountGeneral =
                accountGeneralService.findByBank(bankAcct.getBankName(), bankAcctTransDTO.getBankAccountNo());
            if (Objects.nonNull(accountGeneral)) {
                bankAcctTransDTO.setCompanyId(accountGeneral.getCompanyId());
                bankAcctTransDTO.setCompanyMainId(accountGeneral.getCompanyMainId());
                bankAcctTransDTO.setBankAcctId(accountGeneral.getBankAcctId());
                bankAcctTransDTO.setAccountModel(accountGeneral.getAccountModel());
                bankAcctTransDTO.setBankName(accountGeneral.getCompanyMainName());
            } else {
                bankAcctTransDTO.setCompanyId(bankAcct.getCompanyId());
                bankAcctTransDTO.setBankAcctId(bankAcctTransDTO.getBankAccountNo());
            }
        } else {
            bankAcctTransDTO.setCompanyId(bankAcct.getCompanyId());
            bankAcctTransDTO.setBankAcctId(bankAcctTransDTO.getBankAccountNo());
        }
        return bankAcctTransDTO;
    }

    @Override
    public BankAcctSearchRespDTO findBankAcctByBankAccountNo(String bankName, String bankAccountNo,
        Integer bankAcctType) {
        BankAcct bankAcctByBankAccountNo =
            uBankAcctService.findBankAcctByBankAccountNo(bankName, bankAccountNo, bankAcctType);
        if (Objects.nonNull(bankAcctByBankAccountNo)) {
            BankAcctSearchRespDTO bankAcctSearchRespDTO = new BankAcctSearchRespDTO();
            BeanUtils.copyProperties(bankAcctByBankAccountNo, bankAcctSearchRespDTO);
            return bankAcctSearchRespDTO;
        }
        return null;
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public Integer updateFlowByFlowId(String acctFlowId, BankAcctFlowUpdateReqDTO bankAcctFlowUpdateDTO) {
        if(StringUtils.isBlank(acctFlowId)){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        FinhubLogger.info("更新 BankAcctFlow 银行流水号 req={}", JsonUtils.toJson(bankAcctFlowUpdateDTO));
        BankAcctFlow bankAcctFlow = new BankAcctFlow();
        bankAcctFlow.setBankTransNo(bankAcctFlowUpdateDTO.getBankTransNo());
        bankAcctFlow.setSyncBankTransNo(bankAcctFlowUpdateDTO.getSyncBankTransNo());
        bankAcctFlow.setBankAcctFlowId(acctFlowId);
        int i = uBankAcctFlowService.updateByFLowIdSelective(bankAcctFlow);
        if (i > 0){
            BankAcctFlow flow = bankAcctFlowService.queryAcctFlowByFlowIdAndType(acctFlowId, BankAcctTypeEnum.CHARGE_ACCOUNT.getCode());
            if (!Objects.isNull(flow)) {
                // 发送对账消息
                if (BankConfigUtil.needCallBank(flow.getBankName(), flow.getCompanyId())) {
                    if (BankNameEnum.isSpa(flow.getBankName())) {
                        sendAutoAcctCheckingMsg(flow);
                    }
                    if (BankNameEnum.isCgb(bankAcctFlow.getBankName())) {
                        // 挂帐认款不发对账消息
                        if (bankAcctFlow.getOperationType() != FundPlatAcctOptType.CREDIT_SUBSCRIP.getKey()) {
                            sendAutoAcctCheckingMsg(flow);
                        }
                    }
                }
            }
        }
        return i;
    }

    @Override
    public List<BankAcctInfoRespDTO> findBankAcctList(String bankName) {
        BankAcctSearchReqDTO bankAcctSearchReqDTO = new BankAcctSearchReqDTO();
        bankAcctSearchReqDTO.setBankName(bankName);
        List<BankAcct> list = bankAcctService.queryBySerachReq(bankAcctSearchReqDTO);
        return ObjectUtil.newInstanceList(list, BankAcctInfoRespDTO.class);
    }

    private void sendAutoAcctCheckingMsg(BankAcctFlow bankAcctFlow){
        KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
        BeanUtils.copyProperties(bankAcctFlow, kafkaAutoAcctCheckingMsg);
        kafkaAutoAcctCheckingMsg.setAccountSubType(FundAccountSubType.SUSPEND_ACCOUNT.getKey());
        kafkaAutoAcctCheckingMsg.setAccountFlowId(bankAcctFlow.getBankAcctFlowId());
        autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
    }

    @Override
    public List<BankAcctInfoRespDTO> findGenBankAcctList(String bankName) {
        List<AccountGeneral> generalFlowList = accountGeneralService.findGeneralByBankName(bankName);
        return ObjectUtil.newInstanceList(generalFlowList, BankAcctInfoRespDTO.class);
    }

    @Override
    public List<BankAcctSearchRespDTO> queryBankAcctList(String bankName, Integer bankAcctType) {
        List<BankAcct> bankAcctList = uBankAcctService.queryBankAcctList(bankName, bankAcctType);
        return ObjectUtil.newInstanceList(bankAcctList, BankAcctSearchRespDTO.class);
    }
}
