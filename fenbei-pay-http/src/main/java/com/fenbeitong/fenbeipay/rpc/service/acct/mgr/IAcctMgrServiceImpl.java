package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.acctdech.AcctCompanyBindCardStatusEnum;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.CompanyAccountInfoDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.req.UpdateAcctPublicReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByAcctTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByComIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByMIdBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwCreateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubShowReqRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyAccountInfo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.rpc.service.acct.IAcctAbstractService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyPlatformAccountConvertDTO;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyPlatformAccountOperateDTO;
import com.fenbeitong.usercenter.api.model.dto.company.GroupCompany;
import com.fenbeitong.usercenter.api.model.enums.company.EffectiveAccountEnum;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType.CREDIT;
import static com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType.RECHARGE;
import static com.fenbeitong.finhub.common.constant.FundAcctActStatusEnum.UN_ACTIVATE;
import static com.fenbeitong.finhub.common.constant.FundAcctShowStatusEnum.SHOW;
import static com.fenbeitong.finhub.common.constant.FundAcctShowStatusEnum.UN_SHOW;

/**
 * <AUTHOR>
 * @date  2021/1/12
 * 资金账户-账户管理
 */
@Service("iAcctMgrService")
public class IAcctMgrServiceImpl extends IAcctAbstractService implements IAcctMgrService {


    @Override
    public AcctOverviewRespDTO queryAcctOverview(AcctOverviewReqDTO reqDTO) {
        try {
            ValidateUtils.validate(reqDTO);
            return uAcctCommonService.queryAcctOverview(reqDTO);
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【新账户V4.0系统异常】queryAcctOverview 参数：{},账户余额不足", reqDTO.toString(), e);
            throw e;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】queryAcctOverview 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryAcctOverview 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryAcctOverview 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctGroupOverviewRespDto queryGroupAcctOverview(String groupId) {
        try {
            return uAcctCommonService.queryGroupAcctOverview(groupId);
        } catch (FinPayException payEx) {
            FinhubLogger.error("【查询集团下各企业账户信息系统异常】queryGroupAcctOverview 参数：{}", groupId, payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【查询集团下各企业账户信息系统异常】queryGroupAcctOverview 参数：{}", groupId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AllAcctByCompanyModelRespDTO getAllAcctByCompanyModel(AcctByCompanyModelReqDTO reqDTO) throws FinhubException {
        try {
            ValidateUtils.validate(reqDTO);
            AllAcctByCompanyModelRespDTO respDTO = uAcctCommonService.allAcctByCompanyModel(reqDTO);
            return respDTO;
        }catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】getAllAcctByCompanyModel 参数：{}==={}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】getAllAcctByCompanyModel 参数：{}==={}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】getAllAcctByCompanyModel yic参数：{}==={}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 企业账户-账户管理
     * @param reqDTO AcctOverviewReqDTO
     * @return  AcctOverviewSimpleRespDTO
     */
    @Override
    public AcctOverviewSimpleRespDTO queryMainAcctMgr(AcctOverviewReqDTO reqDTO) throws FinhubException {
        try {
            if (StringUtils.isEmpty(reqDTO.getCompanyId())) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_UPDATE_ERROR);
            }
            //校验字段
            ValidateUtils.validate(reqDTO);
            AcctOverviewSimpleRespDTO simpleRespDTO = uAcctCommonService.queryAcctOverviewMgr(reqDTO);
            return simpleRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】queryMainAcctMgr 参数：{},账户余额不足", reqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainAcctMgr 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainAcctMgr 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainAcctMgr 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<AcctBaseMainRespDTO> queryMainAcctList(AcctOverviewReqDTO reqDTO) throws FinhubException {
        try {
            if (StringUtils.isEmpty(reqDTO.getCompanyId())) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_UPDATE_ERROR);
            }
            //校验字段
            ValidateUtils.validate(reqDTO);
            return uAcctCommonService.queryMainAcctView(reqDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】queryMainAcctList 参数：{},账户余额不足", reqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainAcctList 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainAcctList 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainAcctList 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 查询主体信息以及对应的账户信息
     *
     * @param reqDTO
     * @return
     * @throws FinhubException
     */
    @Override
    public AcctCompanyMainDetailRespDTO queryMainAndAcct(AcctComGwByMIdBankReqDTO reqDTO) throws FinhubException {
        try {
            if (StringUtils.isEmpty(reqDTO.getCompanyId())) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_UPDATE_ERROR);
            }
            return uAcctCommonService.queryCompanyMainDetailInfo4Recharge(reqDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】queryMainBaseInfo 参数：{},账户余额不足", reqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainBaseInfo 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainBaseInfo 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainBaseInfo 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 查询主体信息以及对应的账户信息
     *
     * @param reqDTO
     * @return
     * @throws FinhubException
     */
    @Override
    public AcctCompanyMainDataRespDTO queryMainOnly(AcctCompanyMainDataReqDTO reqDTO) throws FinhubException {
        try {
            //校验字段
            ValidateUtils.validate(reqDTO);
            return uAcctCommonService.queryMainOnly(reqDTO);
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainOnly 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainOnly 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】queryMainOnly 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public AcctCreateFbtRespDTO createDefaultFbtAccount(AcctCreateFbtReqDTO createFbtReqDTO) throws FinAccountNoEnoughException {
        AcctCreateFbtRespDTO respDTO = new AcctCreateFbtRespDTO();
        FinhubLogger.info("【新账户V4.0系统，创建账户】createDefaultFbtAccount 参数：{}", createFbtReqDTO);
        try {
            //校验字段
            ValidateUtils.validate(createFbtReqDTO);
            String accountGeneralId = uAcctGeneralService.createFbtAccount(createFbtReqDTO, createFbtReqDTO.getCompanyName());

            AcctCreateSubDTO acctCreateSubDTO = new AcctCreateSubDTO();
            BeanUtils.copyProperties(createFbtReqDTO, acctCreateSubDTO);
            acctCreateSubDTO.setAccountGeneralId(accountGeneralId);
            //增加网关信息----如果没有前置权限配置，如果有分贝通默认都为非激活，不需要创建网关。
            List<CompanyPlatformAccountConvertDTO> companyPlatformAccountConvertDTOS = iCompanyService.queryCompanyPlatformAccountForTrans(createFbtReqDTO.getCompanyId());
            Boolean effectiveStatus = iCompanyService.queryAccountEffectiveStatus(createFbtReqDTO.getCompanyId());
            if(Objects.isNull(effectiveStatus) || !effectiveStatus) {
                AcctComGwCreateReqDTO reqDTO = new AcctComGwCreateReqDTO(createFbtReqDTO.getCompanyId(), createFbtReqDTO.getCompanyModel());
                acctCompanyGatewayService.initActGws(reqDTO);
            }
            //新账户体系 商务账户 个人账户 都创建充值 授信
            List<AcctCreateSubReqDTO> fbtReqDTOSubList = createFbtReqDTO.getSubList();
            //获取权限
            CompanyPlatformAccountOperateDTO rechargeDTO = new CompanyPlatformAccountOperateDTO();
            rechargeDTO.setAccountHolder(createFbtReqDTO.getCompanyMainType());
            rechargeDTO.setCompanyId(createFbtReqDTO.getCompanyId());
            rechargeDTO.setAccountType(RECHARGE.getKey());
            rechargeDTO.setPlatformSide(FundPlatformEnum.FBT.getKey());
            CompanyPlatformAccountConvertDTO comPlatAccListDe = iCompanyService.detailCompanyPlatformAccountForTrans(rechargeDTO);
            rechargeDTO.setAccountType(CREDIT.getKey());
            CompanyPlatformAccountConvertDTO comPlatAccListCr = iCompanyService.detailCompanyPlatformAccountForTrans(rechargeDTO);
            if (CollectionUtils.isNotEmpty(fbtReqDTOSubList)) {
                //没有权限前置配置，走老逻辑
                if(Objects.isNull(effectiveStatus) || !effectiveStatus) {
                    for (AcctCreateSubReqDTO createSubReqDTO : fbtReqDTOSubList) {
                        BeanUtils.copyProperties(createSubReqDTO, acctCreateSubDTO);
                        //默认激活一定可用
                        acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
                        acctCreateSubDTO.setActiveStatus(createSubReqDTO.getActiveStatus());
                        acctCreateSubDTO.setShowStatus(FundAcctActStatusEnum.isAct(createSubReqDTO.getActiveStatus()) ? SHOW.getStatus() : UN_SHOW.getStatus());
                        uAcctCommonService.createAccountSubByFbt(createFbtReqDTO, acctCreateSubDTO, comPlatAccListDe, comPlatAccListCr);
                    }
                }else {
                    //有权限前置配置，走新逻辑
                    for (AcctCreateSubReqDTO createSubReqDTO : fbtReqDTOSubList) {
                        BeanUtils.copyProperties(createSubReqDTO, acctCreateSubDTO);
                        //默认激活一定可用
                        acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.ENABLE.getStatus());
                        acctCreateSubDTO.setActiveStatus(UN_ACTIVATE.getStatus());
                        acctCreateSubDTO.setShowStatus(FundAcctActStatusEnum.isAct(createSubReqDTO.getActiveStatus()) ? SHOW.getStatus() : UN_SHOW.getStatus());
                        uAcctCommonService.createAccountSubByFbtAuthBefore(createFbtReqDTO, acctCreateSubDTO, comPlatAccListDe, comPlatAccListCr);
                    }
                    //所有账户已经创建完成并且生效，通知UC前置配置失效
                    boolean canDisable = uAcctCommonService.allAuthBeforeAcctCreate(companyPlatformAccountConvertDTOS, createFbtReqDTO.getCompanyId());
                    if(canDisable) {
                        iCompanyService.updateAccountInvalid(createFbtReqDTO.getCompanyId());
                    }
                }
            }
            //增加4.0白名单
            AcctCompanySwitchAddReqDTO addReqDTO = new AcctCompanySwitchAddReqDTO();
            addReqDTO.setCompanyId(createFbtReqDTO.getCompanyId());
            uCompanySwitchService.createAcctCompanySwitch(addReqDTO);
            return respDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}账户余额不足", createFbtReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", createFbtReqDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", createFbtReqDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", createFbtReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public AcctCreateFbtRespDTO createOverseaAccount(AcctCreateBankReqDTO createBankReq) {
    	FinhubLogger.info("【新账户V4.0系统，创建账户】createOverseaAccount 参数：{}", createBankReq);
        ValidateUtils.validate(createBankReq);
        
      //获取权限
        FundPlatformEnum platformByName = FundPlatformEnum.findPlatFrom(createBankReq.getBankName(), FundAcctDirectAcctTypeEnum.BANK.getKey());
        CompanyPlatformAccountOperateDTO rechargeDTO = new CompanyPlatformAccountOperateDTO();
        rechargeDTO.setAccountHolder(createBankReq.getCompanyMainType());
        rechargeDTO.setCompanyId(createBankReq.getCompanyId());
        rechargeDTO.setAccountType(RECHARGE.getKey());
        rechargeDTO.setPlatformSide(platformByName.getKey());
        CompanyPlatformAccountConvertDTO comPlatAccList = iCompanyService.detailCompanyPlatformAccountForTrans(rechargeDTO);
        FinhubLogger.info("detailCompanyPlatformAccountForTrans req:{}, resp:{} ", JSON.toJSONString(rechargeDTO) , JSON.toJSONString(comPlatAccList));
        
        if (Objects.isNull(comPlatAccList) || 
        		Objects.isNull(comPlatAccList.getOverSeaEnterpriseCard()) || 
        		!FundAcctActStatusEnum.isAct(comPlatAccList.getOverSeaEnterpriseCard())) {
        	throw new FinhubException(GlobalResponseCode.NO_AUTH.getCode(), "没有开通开户权限");
        }
        
    	AcctCreateFbtRespDTO resp = new AcctCreateFbtRespDTO();
    	createCompanyMain(createBankReq);
    	String accountGeneralId = uAcctGeneralService.createAccountByBank(createBankReq);
        
        uAcctCommonService.createOverseaAccount(accountGeneralId, createBankReq, comPlatAccList);
    	return resp;
    }
    
    private AcctCompanyMain createCompanyMain(AcctCreateBankReqDTO createBankReq) {
    	//保存主体信息
        AcctCreateMainReqDTO createMainReqDTO = createBankReq.getAcctCreateMainReqDTO();
        ValidateUtils.validate(createMainReqDTO);
        AcctCompanyMainReqDTO companyMainReqDTO = AcctCompanyMainReqDTO.builder().build();
        BeanUtils.copyProperties(createMainReqDTO, companyMainReqDTO);
        if(BankNameEnum.isCitic(createBankReq.getBankName())){
            companyMainReqDTO.setBankBusinessName("北京分贝通科技有限公司待结算户-" + companyMainReqDTO.getBusinessName());
        } else {
            companyMainReqDTO.setBankBusinessName(companyMainReqDTO.getBusinessName());
        }
        AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.saveAcctCompanyMain(companyMainReqDTO);
        createBankReq.setCompanyMainId(acctCompanyMain.getCompanyMainId());
        createBankReq.setCompanyMainType(acctCompanyMain.getCompanyMainType());
        createBankReq.setCompanyMainName(companyMainReqDTO.getBusinessName());
        return acctCompanyMain;
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public AcctCreateFbtRespDTO createBankAccount(AcctCreateBankReqDTO createBankReqDTO) throws FinhubException {
        FinhubLogger.info("【新账户V4.0系统，创建账户】createBankAccount 参数：{}", createBankReqDTO);
        ValidateUtils.validate(createBankReqDTO);
        AcctCreateFbtRespDTO respDTO = new AcctCreateFbtRespDTO();
        try {
        	AcctCompanyMain acctCompanyMain = createCompanyMain(createBankReqDTO);
            // 平安可以绑定多张卡，绑定卡关系维护到新表中
            if (BankNameEnum.isSpa(createBankReqDTO.getBankName())) {
                AcctCompanyBindCard acctCompanyBindCard = buildAcctCompanyBindCard(acctCompanyMain);
                acctCompanyBindCardService.saveAcctCompanyBindCard(acctCompanyBindCard);
            }

            String accountGeneralId = uAcctGeneralService.createAccountByBank(createBankReqDTO);
            //创建结算账户
            uAcctCommonService.creatAcctSettlementByBank(accountGeneralId, createBankReqDTO);
            //获取权限
            FundPlatformEnum platformByName = FundPlatformEnum.findPlatFrom(createBankReqDTO.getBankName(), FundAcctDirectAcctTypeEnum.BANK.getKey());
            CompanyPlatformAccountOperateDTO rechargeDTO = new CompanyPlatformAccountOperateDTO();
            rechargeDTO.setAccountHolder(createBankReqDTO.getCompanyMainType());
            rechargeDTO.setCompanyId(createBankReqDTO.getCompanyId());
            rechargeDTO.setAccountType(RECHARGE.getKey());
            rechargeDTO.setPlatformSide(platformByName.getKey());
            CompanyPlatformAccountConvertDTO comPlatAccListDe = iCompanyService.detailCompanyPlatformAccountForTrans(rechargeDTO);
            FinhubLogger.info("detailCompanyPlatformAccountForTrans req:{}, resp:{} ", JsonUtils.toJson(rechargeDTO) ,JsonUtils.toJson(comPlatAccListDe));
            rechargeDTO.setAccountType(CREDIT.getKey());
            CompanyPlatformAccountConvertDTO comPlatAccListCr = iCompanyService.detailCompanyPlatformAccountForTrans(rechargeDTO);
            FinhubLogger.info("detailCompanyPlatformAccountForTrans req:{}, resp:{} ", JsonUtils.toJson(rechargeDTO) ,JsonUtils.toJson(comPlatAccListDe));

            //新账户体系 商务账户 个人账户 都创建充值 授信
            uAcctCommonService.createAccountSubByBankDebit(accountGeneralId, createBankReqDTO, comPlatAccListDe);
            uAcctCommonService.createAccountSubByBankCredit(accountGeneralId, createBankReqDTO, comPlatAccListCr);
            //通知UC
            //所有账户已经创建完成并且生效，通知UC前置配置失效
           Boolean effectiveStatus = iCompanyService.queryAccountEffectiveStatus(createBankReqDTO.getCompanyId());
            if(Objects.nonNull(effectiveStatus) && effectiveStatus) {
                List<CompanyPlatformAccountConvertDTO> companyPlatformAccountConvertDTOS = iCompanyService.queryCompanyPlatformAccountForTrans(createBankReqDTO.getCompanyId());
                if(CollectionUtils.isNotEmpty(companyPlatformAccountConvertDTOS)){
                    boolean canDisable = uAcctCommonService.allAuthBeforeAcctCreate(companyPlatformAccountConvertDTOS, createBankReqDTO.getCompanyId());
                    if (canDisable) {
                        iCompanyService.updateAccountInvalid(createBankReqDTO.getCompanyId());
                    }
                }
            }
            return respDTO;
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}账户余额不足", createBankReqDTO.toString(), e);
            throw e;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", createBankReqDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", createBankReqDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", createBankReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public AcctUpdateRespDTO updateBankAccount(AcctCreateBankReqDTO createBankReqDTO) throws FinhubException {
        FinhubLogger.info("【修改企业信息同步账户】updateBankAccount 参数：{}", JsonUtils.toJson(createBankReqDTO));
        ValidateUtils.validate(createBankReqDTO);
        AcctUpdateRespDTO respDTO = new AcctUpdateRespDTO();
        try {
            //校验字段
            ValidateUtils.validate(createBankReqDTO);
            // 修改余额账户信息
            String companyMainId = uAcctGeneralService.updateAccountByBank(createBankReqDTO);
            // 修改对公付款账户信息
            uAcctCommonService.updatePublicAcctByBank(createBankReqDTO);
            AcctCreateMainReqDTO createMainReqDTO = createBankReqDTO.getAcctCreateMainReqDTO();
            ValidateUtils.validate(createMainReqDTO);
            AcctCompanyMainReqDTO companyMainReqDTO = AcctCompanyMainReqDTO.builder().build();
            BeanUtils.copyProperties(createMainReqDTO, companyMainReqDTO);
            companyMainReqDTO.setCompanyMainId(companyMainId);
            if(BankNameEnum.isCitic(createBankReqDTO.getBankName())){
                companyMainReqDTO.setBankBusinessName("北京分贝通科技有限公司待结算户-" + companyMainReqDTO.getBusinessName());
            }else {
                companyMainReqDTO.setBankBusinessName(companyMainReqDTO.getBusinessName());
            }
            // 主体信息
            AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.updateAcctCompanyMain(companyMainReqDTO);
            BeanUtils.copyProperties(createBankReqDTO, respDTO);
            return respDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【修改企业信息同步账户】updateBankAccount 参数：{}", createBankReqDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【修改企业信息同步账户】updateBankAccount 参数：{}", createBankReqDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【修改企业信息同步账户】updateBankAccount 参数：{}", createBankReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public CompanyMainRespDTO createAcctCompanyMain(AcctCreateMainReqDTO acctCreateMainReqDTO) throws FinhubException {
        try {
            FinhubLogger.info("【新账户V4.0系统，创建账户主体】createAcctCompanyMain 参数：{}", acctCreateMainReqDTO);
            ValidateUtils.validate(acctCreateMainReqDTO);
            //保存主体信息
            AcctCompanyMainReqDTO companyMainReqDTO = AcctCompanyMainReqDTO.builder().build();
            BeanUtils.copyProperties(acctCreateMainReqDTO, companyMainReqDTO);
            AcctCompanyMain entity = uAcctCompanyMainService.saveAcctCompanyMain(companyMainReqDTO);
            CompanyMainRespDTO resp = new CompanyMainRespDTO();
            BeanUtils.copyProperties(entity, resp);
            return resp;
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}账户余额不足", acctCreateMainReqDTO.toString(), e);
            throw e;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", acctCreateMainReqDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", acctCreateMainReqDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】createDefaultFbtAccount 参数：{}", acctCreateMainReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    @Override
    public void enableSubAccount(AcctActiveSubReqDTO reqDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(reqDTO);
            
            String accountId = reqDTO.getAccountId();
            if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType()) &&
                    FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                uAcctBusinessDebitService.enableAccountById(accountId);
            } else if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType()) &&
                    FundAccountModelType.isCredit(reqDTO.getAccountModel())) {
                uAcctBusinessCreditService.enableAccountById(accountId);
            } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType()) &&
                    FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                uAcctIndividualDebitService.enableAccountById(accountId);
            } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType()) &&
                    FundAccountModelType.isCredit(reqDTO.getAccountModel())) {
                uAcctIndividualCreditService.enableAccountById(accountId);
            } else if (FundAccountSubType.isComCardAccount(reqDTO.getAccountSubType())) {
                uAcctCompanyCardService.enableAccountById(reqDTO.getOperationUserId(), accountId);
            } else if (FundAccountSubType.isComPublicCardAccount(reqDTO.getAccountSubType())) {
            	checkIfCompanyHaveOtherEntityAccount(reqDTO);
            	
                UpdateAcctPublicReqRPCDTO updateAcctPublicReqRPCDTO = new UpdateAcctPublicReqRPCDTO();
                updateAcctPublicReqRPCDTO.setAccountPublicId(reqDTO.getAccountId());
                updateAcctPublicReqRPCDTO.setOperationChannel(OperationChannelType.STEREO.getKey());
                updateAcctPublicReqRPCDTO.setOperationUserId(reqDTO.getOperationUserId());
                updateAcctPublicReqRPCDTO.setOperationUserName(OperationChannelType.STEREO.getValue());
                acctPublicService.enableAcctPublic(updateAcctPublicReqRPCDTO);
            } else if (FundAccountSubType.isReimbursement(reqDTO.getAccountSubType())){
                uAcctReimbursementService.enableAccountById(accountId);
            } else if (FundAccountSubType.isOverseaAcct(reqDTO.getAccountSubType())) {
                acctOverseaService.enableAccountById(accountId);
            }
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【新账户V4.0系统异常】enableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw e;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】enableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("【新账户V4.0系统异常】enableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】enableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】enableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }
    
    private void checkIfCompanyHaveOtherEntityAccount(AcctActiveSubReqDTO request) {
    	String groupId = iRCompanyService.getCompanyRelGroupId(request.getCompanyId());
    	if (StringUtils.isBlank(groupId)) {
    		FinhubLogger.info("OtherEntity:公司->{}非集团版，不校验", request.getCompanyId());
    		return;
    	}
    	
    	List<GroupCompany> companies = iRCompanyService.listGroupCompanyInfo(groupId);
    	boolean isHq = Optional.ofNullable(companies).orElse(Collections.emptyList())
    			.stream()
    			.filter(c -> StringUtils.equals(request.getCompanyId(), c.getId()))
    			.anyMatch(c -> Objects.equals(1, c.getCompanyType()));
    	if (isHq) {
    		FinhubLogger.info("OtherEntity:公司->{}是总公司，不校验", request.getCompanyId());
    		return;
    	}
    	try {
			uAcctCommonService.checkIfCompanyHaveOtherEntityAccount(request.getCompanyId(), request.getAccountId());
		} catch (FinPayException e) {
			if (Objects.equals(GlobalResponseCode.GROUP_COMPANY_HAS_OTHER_ENTITY.getCode(), e.getCode())) {
				throw new FinhubException(GlobalResponseCode.GROUP_COMPANY_HAS_OTHER_ENTITY.getCode(), "集团子公司不可启用其他主体账户");
			} else {
				throw e;
			}
		}
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public void disableSubAccount(AcctActiveSubReqDTO reqDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(reqDTO);
            String accountId = reqDTO.getAccountId();
            if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType()) &&
                    FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                uAcctBusinessDebitService.disableAccountById(accountId);
            } else if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType()) &&
                    FundAccountModelType.isCredit(reqDTO.getAccountModel())) {
                uAcctBusinessCreditService.disableAccountById(accountId);
            } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType()) &&
                    FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                uAcctIndividualDebitService.disableAccountById(accountId);
            } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType()) &&
                    FundAccountModelType.isCredit(reqDTO.getAccountModel())) {
                uAcctIndividualCreditService.disableAccountById(accountId);
            } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey())) {
                uAcctCompanyCardService.disableAccountById(reqDTO.getOperationUserId(), accountId);
            } else if (FundAccountSubType.isComPublicCardAccount(reqDTO.getAccountSubType())) {
                UpdateAcctPublicReqRPCDTO updateAcctPublicReqRPCDTO = new UpdateAcctPublicReqRPCDTO();
                updateAcctPublicReqRPCDTO.setAccountPublicId(reqDTO.getAccountId());
                updateAcctPublicReqRPCDTO.setOperationChannel(OperationChannelType.STEREO.getKey());
                updateAcctPublicReqRPCDTO.setOperationUserId(reqDTO.getOperationUserId());
                updateAcctPublicReqRPCDTO.setOperationUserName(OperationChannelType.STEREO.getValue());
                acctPublicService.disableAcctPublic(updateAcctPublicReqRPCDTO);
            } else if (FundAccountSubType.isReimbursement(reqDTO.getAccountSubType())) {
                uAcctReimbursementService.disableAccountById(accountId);
            } else if (FundAccountSubType.isOverseaAcct(reqDTO.getAccountSubType())) {
                acctOverseaService.disableAccountById(accountId);
            }
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【新账户V4.0系统异常】disableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw e;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】disableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】disableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】disableSubAccount 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean preActivityAccountSub(AcctUpdateCommonReqDTO acctUpdateCommonReqDTO) {
//        AcctCommonOptRespDTO commonOptRespDTO = new AcctCommonOptRespDTO();
        return false;
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public void activateAcctSub(List<AcctUpdateCommonReqDTO> reqDTOS) {
        try {
            //改生效帐户
            uAcctCommonService.changeActivateAcct(reqDTOS);
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【新账户V4.0系统异常】activateAcctSub 参数：{},={}", JsonUtils.toJson(reqDTOS), e);
            throw e;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】activateAcctSub 参数：{},={}", JsonUtils.toJson(reqDTOS), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】activateAcctSub 参数：{},={}", JsonUtils.toJson(reqDTOS), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】activateAcctSub 参数：{},={}", JsonUtils.toJson(reqDTOS), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void showAccountSub(AcctUpdateCommonReqDTO reqDTO) {
        try {
            reqDTO.checkReq();
            AccountSubShowReqRPCDTO subShowReqRPCDTO = new AccountSubShowReqRPCDTO();
            BeanUtils.copyProperties(reqDTO, subShowReqRPCDTO);
            if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType())) {
                if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                    uAcctBusinessDebitService.showAccount(subShowReqRPCDTO);
                } else {
                    uAcctBusinessCreditService.showAccount(subShowReqRPCDTO);
                }
            } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType())) {
                if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                    uAcctIndividualDebitService.showAccount(subShowReqRPCDTO);
                } else {
                    uAcctIndividualCreditService.showAccount(subShowReqRPCDTO);
                }
            } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey())) {
                uAcctCompanyCardService.showAccount(reqDTO);
            } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey())) {
                acctPublicDechService.updateShowAcctPublic(reqDTO);
            } else if (FundAccountSubType.isReimbursement(reqDTO.getAccountSubType())){
                uAcctReimbursementService.showAccount(subShowReqRPCDTO);
            } else if (FundAccountSubType.isOverseaAcct(reqDTO.getAccountSubType())){
                acctOverseaService.showAccount(subShowReqRPCDTO);
            }
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【新账户V4.0系统异常】showAccountSub 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw e;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】showAccountSub 参数：{},={}", JsonUtils.toJson(reqDTO), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】showAccountSub 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】showAccountSub 参数：{},={}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public Boolean isActivateAcctSubByAcctId(String accountId) {
        try {
            if (StringUtils.isEmpty(accountId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            AcctCompanyCard acctCompanyCard = uAcctCompanyCardService.findAcctCompanyCardByAcctId(accountId);
            if (acctCompanyCard == null){
                //兼容逻辑
                return acctCompanyGatewayService.isActGwByAcctId(accountId);
            }
            return FundAcctActStatusEnum.isAct(acctCompanyCard.getActiveStatus());
//            return acctCompanyGatewayService.isActGwByAcctId(accountId);
        }  catch (FinPayException e) {
            FinhubLogger.error("【新账户V4.0系统异常】isActivateAcctSubByAcctId 参数：{}", accountId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户V4.0系统异常】isActivateAcctSubByAcctId 参数：{}", accountId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】isActivateAcctSubByAcctId 参数：{}", accountId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public Boolean isReimbursementActivateByAcctId(String accountId, Integer accountSubType) {
        try {
            if (StringUtils.isEmpty(accountId)
                    || accountSubType == null
                    || !FundAccountSubType.isReimbursement(accountSubType)
            ) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            return uAcctReimbursementService.isActiveByAccountId(accountId);
        } catch (Exception e) {
            FinhubLogger.error("【报销账户状态更新异常】isActivateAcctSubByAcctId 参数：{}", accountId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctCommonOptRespDTO changeCompanyName(AcctChangeNameReqDTO changeNameReqDTO) throws FinAccountNoEnoughException {
        FinhubLogger.info("【新账户V4.0系统，创建账户】changeCompanyName 参数：{}", changeNameReqDTO);
        AcctCommonOptRespDTO respDTO = new AcctCommonOptRespDTO();
        try {
            //校验字段
            ValidateUtils.validate(changeNameReqDTO);
            uAcctCommonService.changeCompanyName(changeNameReqDTO);
            return respDTO;
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【新账户4.0系统异常】changeCompanyName 参数：{}账户余额不足", changeNameReqDTO.toString(), e);
            throw e;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】changeCompanyName 参数：{}", changeNameReqDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户4.0系统异常】changeCompanyName 参数：{}", changeNameReqDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】changeCompanyName 参数：{}", changeNameReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 更改企业合作模式 生效的帐户
     * @param respDTO ChangeCompModelAndActAcctReqDTO
     * @return
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public void changeCompModelAndActAcct(ChangeCompModelAndActAcctReqDTO respDTO){
        try {
            //校验字段
            ValidateUtils.validate(respDTO);
            //改企业模式
            uAcctCommonService.changeCompanyModel(respDTO);
            //改生效帐户
            uAcctCommonService.changeActivateAcct4ChangeCompanyModel(respDTO.getAcctUpdateCommonReqDTO());

        }  catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】更改企业合作模式 生效的帐户 参数：{}=={}",JsonUtils.toJson(respDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户4.0系统异常】更改企业合作模式 生效的帐户 参数：{}=={}", JsonUtils.toJson(respDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】更改企业合作模式 生效的帐户 参数：{}=={}", JsonUtils.toJson(respDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctGeneralRpcRespDTO findByAccountGeneralId(String accountGeneralId) {
        try {
            AccountGeneral accountGeneral = uAcctGeneralService.findByAccountId(accountGeneralId);
            if(Objects.isNull(accountGeneral)){
                return null;
            }
            AcctGeneralRpcRespDTO accountGeneralVO = new AcctGeneralRpcRespDTO();
            BeanUtils.copyProperties(accountGeneral,accountGeneralVO);
            return accountGeneralVO;
        }  catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】根据accountGeneralId查询余额账户 参数：{}=={}",accountGeneralId);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }  catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】根据accountGeneralId查询余额账户 参数：{}=={}", accountGeneralId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctGeneralRpcRespDTO findByBankNameAndBankAcctId(String bankName, String bankAcctId) {
        try {
            AccountGeneral accountGeneral = uAcctGeneralService.findByBank(bankName, bankAcctId);
            if(Objects.isNull(accountGeneral)){
                return null;
            }
            AcctGeneralRpcRespDTO accountGeneralVO = new AcctGeneralRpcRespDTO();
            BeanUtils.copyProperties(accountGeneral,accountGeneralVO);
            return accountGeneralVO;
        }  catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】根据accountGeneralId查询余额账户 参数：{},{}",bankName, bankAcctId);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }  catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】根据accountGeneralId查询余额账户 参数：{},{}" ,bankName, bankAcctId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    //============================账户权限管理=====================================

    @Override
    public List<AcctAuthBankEnumRespDTO> findAcctAuthPlatformCode() {
        List<AcctAuthBankEnumRespDTO> resp = new ArrayList<>();
        List<FundPlatformEnum> platformBankEnum = FundAcctAuthCheckEnum.getPlatformBankEnum();
        if (CollectionUtils.isNotEmpty(platformBankEnum)) {
            platformBankEnum.stream().forEach(e -> {
                if (Objects.nonNull(e) && e.getKey() != -1) {
                    AcctAuthBankEnumRespDTO respRPCDTO = new AcctAuthBankEnumRespDTO();
                    respRPCDTO.setKey(e.getKey());
                    respRPCDTO.setName(e.getName());
                    resp.add(respRPCDTO);
                }
            });
        }
        return resp;
    }

    @Override
    public List<AcctAuthModelTypeRespDTO> findAcctAuthModelType(AcctAuthModelTypeReqDTO param) {
        List<AcctAuthModelTypeRespDTO> resultList = new ArrayList<>();
        if (Objects.isNull(param.getPlateformKey())) {
            return resultList;
        }
        List<FundAccountModelType> modelTypes = FundAcctAuthCheckEnum.getModelType(param.getPlateformKey());
        if (CollectionUtils.isNotEmpty(modelTypes)) {
            modelTypes.stream().forEach(e -> {
                if (Objects.nonNull(e)) {
                    AcctAuthModelTypeRespDTO resp = new AcctAuthModelTypeRespDTO();
                    resp.setKey(e.getKey());
                    resp.setName(e.getMsg4Client());
                    resultList.add(resp);
                }
            });
        }
        return resultList;
    }

    @Override
    public List<AcctAuthCompanyMainTypeRespDTO> findAcctAuthCompanyMainType(AcctAuthCompanyMainTypeReqDTO req) {
        List<AcctAuthCompanyMainTypeRespDTO> resultList = new ArrayList<>();
        if (Objects.isNull(req.getFundAcctModel()) || 
        		Objects.isNull(req.getPlateformKey()) || 
                StringUtils.isBlank(req.getCompanyId())) {
            return resultList;
        }
        List<CompanyMainTypeEnum> companyMainType = FundAcctAuthCheckEnum.getCompanyMainType(req.getPlateformKey(), req.getFundAcctModel());
        if (CollectionUtils.isNotEmpty(companyMainType)) {
            companyMainType.stream().forEach(e -> {
                if (Objects.nonNull(e) && !(CompanyMainTypeEnum.COMPANY_MAIN_OTHER == e && isCompanyInGroup(req.getCompanyId()))) {
                	AcctAuthCompanyMainTypeRespDTO resp = new AcctAuthCompanyMainTypeRespDTO();
                    resp.setKey(e.getKey());
                    resp.setName(e.getName());
                    resultList.add(resp);
                }
            });
        }
        return resultList;
    }
    
    boolean isCompanyInGroup(String companyId) {
    	String groupId = iRCompanyService.getCompanyRelGroupId(companyId);
    	if (StringUtils.isBlank(groupId)) {
    		return false;
    	}
    	
    	List<GroupCompany> list = iRCompanyService.listGroupCompanyInfo(groupId);
    	if (CollectionUtils.isEmpty(list)) {
    		return false;
    	}
    	return list.stream()
    			.filter(gc -> StringUtils.equals(companyId, gc.getId()))
    			.allMatch(gc -> Objects.equals(2, gc.getCompanyType()));
    }

    @Override
    public List<AcctAuthSubTypeRespDTO> findAcctAuthSubType(AcctAuthSubTypeReqDTO acctAuthSubTypeReqRPCDTO) {
        List<AcctAuthSubTypeRespDTO> respRPCDTOs = new ArrayList<>();
        if (Objects.isNull(acctAuthSubTypeReqRPCDTO.getFundAcctModel())
                || Objects.isNull(acctAuthSubTypeReqRPCDTO.getPlatformKey())
                || Objects.isNull(acctAuthSubTypeReqRPCDTO.getCompanyMainType())) {
            return respRPCDTOs;
        }
        List<FundAccountSubType> accountSubType = FundAcctAuthCheckEnum.getAccountSubType(acctAuthSubTypeReqRPCDTO.getPlatformKey(), acctAuthSubTypeReqRPCDTO.getFundAcctModel(), acctAuthSubTypeReqRPCDTO.getCompanyMainType());
        if (CollectionUtils.isNotEmpty(accountSubType)) {
            accountSubType.stream().forEach(e -> {
                if (Objects.nonNull(e)) {
                    AcctAuthSubTypeRespDTO respRPCDTO = new AcctAuthSubTypeRespDTO();
                    respRPCDTO.setKey(e.getKey());
                    respRPCDTO.setName(e.getValue());
                    respRPCDTOs.add(respRPCDTO);
                }
            });
        }
        return respRPCDTOs;
    }

    @Override
    public AcctCompanyUpgradedRespDTO upgradeAcctCompany(AcctCompanyUpgradeReqDTO acctCompanyUpgradeReqDTO) {
        try {
            return uAcctCommonService.upgradeAcctCompany(acctCompanyUpgradeReqDTO);
        }  catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】企业升级 参数：{}=={}",JsonUtils.toJson(acctCompanyUpgradeReqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户4.0系统异常】企业升级 参数：{}=={}", JsonUtils.toJson(acctCompanyUpgradeReqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】企业升级 参数：{}=={}", JsonUtils.toJson(acctCompanyUpgradeReqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctEffectiveAccountDTO upgradeDefaultAuthConfig(AcctUpgradeDefaultAuthConfigReqDTO acctUpgradeDefaultAuthConfigReqDTO) {
        AcctEffectiveAccountDTO acctEffectiveAccountDTO = new AcctEffectiveAccountDTO();
        AcctComGwByComIdReqDTO acctComGwByComIdReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctComGwByComIdReqDTO.setCompanyId(acctUpgradeDefaultAuthConfigReqDTO.getCompanyId());
        List<AcctCommonBaseDTO> gatewayAcctCommon = acctCompanyGatewayService.findActCommonByComId(acctComGwByComIdReqDTO);
        if(CollectionUtils.isEmpty(gatewayAcctCommon)){
            return acctEffectiveAccountDTO;
        }
        for(AcctCommonBaseDTO acctCommonBaseDTO : gatewayAcctCommon){
            acctEffectiveAccountDTO.setCompanyId(acctUpgradeDefaultAuthConfigReqDTO.getCompanyId());
            if(FundAccountSubType.isBusinessAccount(acctCommonBaseDTO.getAccountSubType())) {
                if(BankNameEnum.isFbt(acctCommonBaseDTO.getBankName())){
                    if(FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())){
                        acctEffectiveAccountDTO.setBusinessConsume(EffectiveAccountEnum.BusinessConsume1.getKey());
                    }else if(FundAccountModelType.isCredit(acctCommonBaseDTO.getAccountModel())){
                        acctEffectiveAccountDTO.setBusinessConsume(EffectiveAccountEnum.BusinessConsume2.getKey());
                    }
                }else if(BankNameEnum.isZBBank(acctCommonBaseDTO.getBankName())){
                    if(FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())){
                        acctEffectiveAccountDTO.setBusinessConsume(EffectiveAccountEnum.BusinessConsume3.getKey());
                    }
                }
                continue;
            }
            if(FundAccountSubType.isIndividualAccount(acctCommonBaseDTO.getAccountSubType())) {
                if(BankNameEnum.isFbt(acctCommonBaseDTO.getBankName())){
                    if(FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())){
                        acctEffectiveAccountDTO.setPersonalConsume(EffectiveAccountEnum.PersonalConsume1.getKey());
                    }else if(FundAccountModelType.isCredit(acctCommonBaseDTO.getAccountModel())){
                        acctEffectiveAccountDTO.setPersonalConsume(EffectiveAccountEnum.PersonalConsume2.getKey());
                    }
                }else if(BankNameEnum.isZBBank(acctCommonBaseDTO.getBankName())){
                    if(FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())){
                        acctEffectiveAccountDTO.setPersonalConsume(EffectiveAccountEnum.PersonalConsume3.getKey());
                    }
                }
                continue;
            }
            if(FundAccountSubType.isComCardAccount(acctCommonBaseDTO.getAccountSubType())) {
                if(BankNameEnum.isFbt(acctCommonBaseDTO.getBankName())){
                    if(FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())){
                        acctEffectiveAccountDTO.setBusinessConsume(EffectiveAccountEnum.VirtualCard1.getKey());
                    }
                }else if(BankNameEnum.isZBBank(acctCommonBaseDTO.getBankName())){
                    if(FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())){
                        acctEffectiveAccountDTO.setBusinessConsume(EffectiveAccountEnum.VirtualCard2.getKey());
                    }
                }
                continue;
            }
        }
        List<AccountPublic> accountPublics = acctPublicDechService.queryAcctPublicByCompanyId(acctUpgradeDefaultAuthConfigReqDTO.getCompanyId());
        if(CollectionUtils.isEmpty(accountPublics)){
            return acctEffectiveAccountDTO;
        }
        AcctEffectiveAccountCorporatePaymentDTO acctEffectiveAccountCorporatePaymentDTO = new AcctEffectiveAccountCorporatePaymentDTO();
        for(AccountPublic accountPublic : accountPublics){

            if(BankNameEnum.isZBBank(accountPublic.getBankAccountName())){
                acctEffectiveAccountCorporatePaymentDTO.setCorporatePaymentZB(EffectiveAccountEnum.CorporatePayment1.getKey());
            }else if(BankNameEnum.isCitic(accountPublic.getBankAccountName())){
                acctEffectiveAccountCorporatePaymentDTO.setCorporatePaymentZB(EffectiveAccountEnum.CorporatePayment2.getKey());
            }
        }
        acctEffectiveAccountDTO.setCorporatePayment(acctEffectiveAccountCorporatePaymentDTO);
        return acctEffectiveAccountDTO;
    }

    @Override
    public AcctBalanceAdjustRespDTO acctBalanceAdjust(AcctBalanceAdjustReqDTO acctBalanceAdjustReqDTO) {
        try {
            AcctBalanceAdjustRespDTO acctBalanceAdjustRespDTO = uAcctCommonService.acctBalanceAdjust(acctBalanceAdjustReqDTO);
            return acctBalanceAdjustRespDTO;
        }  catch (FinPayException e) {
            FinhubLogger.error("【FBT余额账户】调额 参数：{}=={}",JsonUtils.toJson(acctBalanceAdjustReqDTO),e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("FBT余额账户】调额 参数：{}=={}", JsonUtils.toJson(acctBalanceAdjustReqDTO),e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("FBT余额账户】调额 参数：{}=={}", JsonUtils.toJson(acctBalanceAdjustReqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctBalanceAdjustRespDTO acctBalanceAdjust(AcctBalanceAdjustReqDTO fromAcct, AcctBalanceAdjustReqDTO toAcct) {
        try {
            AcctBalanceAdjustRespDTO acctBalanceAdjustRespDTO = uAcctCommonService.acctBalanceAdjust(fromAcct,toAcct);
            return acctBalanceAdjustRespDTO;
        }  catch (FinPayException e) {
            FinhubLogger.error("【FBT余额账户】调额 参数：{},{}=={}",JsonUtils.toJson(fromAcct),JsonUtils.toJson(toAcct),e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("FBT余额账户】调额 参数：{},{}=={}",JsonUtils.toJson(fromAcct),JsonUtils.toJson(toAcct));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("FBT余额账户】调额 参数：{},{}=={}",JsonUtils.toJson(fromAcct),JsonUtils.toJson(toAcct));
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean updateAcctContract(String contractUrl, String bankName, String bankAcctNo) {
        try {
            return uAcctCommonService.updateAcctContract(contractUrl, bankName, bankAcctNo);
        }  catch (FinPayException e) {
            FinhubLogger.error("【更新电子合同】参数：{},{}", contractUrl, bankAcctNo, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("更新电子合同】参数：{},{}", contractUrl, bankAcctNo, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }
    
    @Override
    public List<CompanyAccountInfoDTO> queryGroupAcctAuth(GroupAcctReqDTO reqDTO) {
    	List<CompanyAccountInfo> list = uAcctCommonService.queryGroupAcctAuth(reqDTO);
    	List<CompanyAccountInfoDTO> resp = Optional.ofNullable(list).orElse(Collections.emptyList())
    			.stream()
    			.filter(Objects :: nonNull)
    			.map(companyAccountInfoConverter :: convert)
    			.collect(Collectors.toList());
    	return resp;
    }

    @Override
    public boolean zxUpdateAcctMain(String bankName, String bankAcctNo) {
        try {
            return uAcctCommonService.zxUpdateAcctMain(bankName, bankAcctNo);
        }  catch (FinPayException e) {
            FinhubLogger.error("【中信账户升级】参数：{}", bankAcctNo, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【中信账户升级】参数：{}", bankAcctNo, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean changeAccountMainType(String companyId, String bankName, String bankAcctNo) {
        try {
            AccountGeneral accountGeneral = uAcctGeneralService.findByCompanyIdAndBank(companyId, bankName, bankAcctNo);
            if (Objects.isNull(accountGeneral)) {
                FinhubLogger.error("【切换账户本主体】未查询到账户信息 companyId={}, bankName={}, bankAcctNo={}", companyId, bankName, bankAcctNo);
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            // 查询主体信息
            AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.findAcctCompanyMain(accountGeneral.getCompanyId(), accountGeneral.getCompanyMainId(), accountGeneral.getBankName());
            if (Objects.isNull(acctCompanyMain)) {
                FinhubLogger.error("【切换账户本主体】其他主体未查询到主体信息 accountGeneral 参数：{}", JsonUtils.toJson(accountGeneral));
                throw new FinPayException(GlobalResponseCode.MAIN_COMPANY_NO_EXIST);
            }
            // 判断账户生效状态
            // 商务
            AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(companyId, bankName, bankAcctNo);
            boolean businessActiveStatus = Objects.isNull(acctBusinessDebit) ? false : FundAcctActStatusEnum.isAct(acctBusinessDebit.getActiveStatus());
            // 个人
            AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(companyId, bankName, bankAcctNo);
            boolean individualActiveStatus = Objects.isNull(acctIndividualDebit) ? false : FundAcctActStatusEnum.isAct(acctIndividualDebit.getActiveStatus());
            // 虚拟卡
            AcctCompanyCard acctCompanyCard = uAcctCompanyCardService.findByCompanyIdAndBank(companyId, bankName, bankAcctNo);
            boolean companyCardActiveStatus = Objects.isNull(acctCompanyCard) ? false : FundAcctActStatusEnum.isAct(acctCompanyCard.getActiveStatus());
//            // 对公付款
//            AccountPublic accountPublic = acctPublicSearchService.findByComIdAndMIdAndBank(companyId, accountGeneral.getCompanyMainId(), bankName, bankAcctNo);
//            boolean accountPublicActiveStatus = ObjUtils.isEmpty(accountPublic) ? false : FundAcctActStatusEnum.isAct(accountPublic.getBankStatus());
//            // 报销打款
//            AcctReimbursement acctReimbursement = uAcctReimbursementService.findCompanyIdAndBank(companyId, bankName, bankAcctNo);
//            boolean acctReimbursementActiveStatus = ObjUtils.isEmpty(acctReimbursement) ? false : FundAcctActStatusEnum.isAct(acctReimbursement.getActiveStatus());
//            if (businessActiveStatus || individualActiveStatus || companyCardActiveStatus || accountPublicActiveStatus || acctReimbursementActiveStatus) {
            if (businessActiveStatus || individualActiveStatus || companyCardActiveStatus) {
                FinhubLogger.error("【切换账户本主体】存在生效账户 accountGeneral 参数：{}, 商务={}, 个人={}, 虚拟卡={}{}", JsonUtils.toJson(accountGeneral), businessActiveStatus, individualActiveStatus, companyCardActiveStatus);
                throw new FinPayException(GlobalResponseCode.HAVE_ACTIVE_STATUS_ACCOUNT);
            }
            uAcctCommonService.changeAccountMainType(accountGeneral, CompanyMainTypeEnum.COMPANY_MAIN_OTHER);
            return true;
        }  catch (FinPayException e) {
            FinhubLogger.error("【切换账户本主体】参数：{}", bankAcctNo, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【切换账户本主体】参数：{}", bankAcctNo, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    private AcctCompanyBindCard buildAcctCompanyBindCard(AcctCompanyMain acctCompanyMain) {
        AcctCompanyBindCard acctCompanyBindCard = new AcctCompanyBindCard();
        acctCompanyBindCard.setCompanyId(acctCompanyMain.getCompanyId());
        acctCompanyBindCard.setCompanyName(acctCompanyMain.getCompanyName());
        acctCompanyBindCard.setCompanyMainId(acctCompanyMain.getCompanyMainId());
        acctCompanyBindCard.setBankCode(acctCompanyMain.getBankCode());
        acctCompanyBindCard.setBankCardName(acctCompanyMain.getBankCardName());
        acctCompanyBindCard.setBankBrnNo(acctCompanyMain.getBankBrnNo());
        acctCompanyBindCard.setBankBatchName(acctCompanyMain.getBankBatchName());
        acctCompanyBindCard.setBankCardNo(acctCompanyMain.getBankCardNo());
        acctCompanyBindCard.setBankProvince(acctCompanyMain.getBankProvince());
        acctCompanyBindCard.setBankCity(acctCompanyMain.getBankCity());
        acctCompanyBindCard.setBusinessName(acctCompanyMain.getBusinessName());
        acctCompanyBindCard.setStatus(AcctCompanyBindCardStatusEnum.BIND.getCode());
        return acctCompanyBindCard;
    }
}
