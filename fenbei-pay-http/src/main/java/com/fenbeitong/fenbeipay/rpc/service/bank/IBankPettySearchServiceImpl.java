package com.fenbeitong.fenbeipay.rpc.service.bank;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.PageBeanReqDTO;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.CardModelType;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.PettyType;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.RoundPettyStatus;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankPettyReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.*;
import com.fenbeitong.fenbeipay.api.service.bank.IBankPettySearchService;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardCostAttributionMapper;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardCreditApplyExtMapper;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankPettyMapper;
import com.fenbeitong.fenbeipay.bank.base.dto.BankPettyCostAttDto;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.BankCardApplyFlowManger;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.BankPettyManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.CreditApplyOperationManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.CreditDistributeManager;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCostAttribution;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCreditApply;
import com.fenbeitong.fenbeipay.dto.bank.BankPetty;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saasplus.api.service.apply.IApplyOrderService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: RPC查询备用金接口
 * @ClassName: IBankPettySearchServiceImpl
 * @Author: zhangga
 * @CreateDate: 2020/7/27 6:20 PM
 * @UpdateUser:
 * @UpdateDate: 2020/7/27 6:20 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iBankPettySearchService")
public class IBankPettySearchServiceImpl implements IBankPettySearchService {
    @Autowired
    private BankPettyManager bankPettyManager;
    @Autowired
    private BankPettyMapper bankPettyMapper;
    @Autowired
    private IApplyOrderService iApplyOrderService;

    @Autowired
    private BankCardCostAttributionMapper bankCardCostAttributionMapper;

    @Resource
    private BankCardApplyFlowManger bankCardApplyFlowManger;

    @Autowired
    public BankCardCreditApplyExtMapper bankCardCreditApplyMapper;

    @Autowired
    public CreditApplyOperationManager creditApplyOperationManager;
    @Autowired
    public CreditDistributeManager creditDistributeManager;


    @Override
    public List<BankPettyDetailRespDTO> queryBankPettyByCompanyId(String companyId) {
        FinhubLogger.info("备用金查询接口queryBankPettyByCompanyId(),参数:{}",companyId);
        try {
            if (ObjUtils.isBlank(companyId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            List<BankPetty> bankPettyList = bankPettyManager.queryBankPettyByCompanyId(companyId);
            if (ObjUtils.isEmpty(bankPettyList)) {
                return Lists.newArrayList();
            }
            List<BankPettyDetailRespDTO> dataList = new ArrayList<>();
            bankPettyList.forEach(bankPetty -> {
                BankPettyDetailRespDTO dto = new BankPettyDetailRespDTO();
                BeanUtils.copyProperties(bankPetty, dto);
                dataList.add(dto);
            });
            return dataList;
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数companyId：{}", companyId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public BankPettyDetailRespDTO queryBankPettyByEmployeeId(String companyId, String employeeId) {
        FinhubLogger.info("备用金查询接口queryBankPettyByEmployeeId(),参数companyId:{},employeeId:{}",companyId,employeeId);
        //throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_BUS_REMOVE);
        try {
            if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            BankPetty bankPetty = bankPettyManager.queryBankPettyByEmployeeId(companyId, employeeId);
            if (bankPetty == null) {
                return null;
            }
            BankPettyDetailRespDTO detailRespDTO = new BankPettyDetailRespDTO();
            BeanUtils.copyProperties(bankPetty, detailRespDTO);
            return detailRespDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数companyId:{},employeeId:{}", companyId, employeeId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数companyId:{},employeeId:{}", companyId, employeeId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数companyId:{},employeeId:{}", companyId, employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public ResponsePage<BankPettyDetailRespDTO> queryBankPettyByCardNo(String companyId, String bankAccountNo, PageBeanReqDTO pageBean) {
        FinhubLogger.info("备用金查询接口queryBankPettyByCardNo(),参数companyId:{},bankAccountNo:{}",companyId,bankAccountNo);
        //throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_BUS_REMOVE);
        try {
            if (ObjUtils.isEmpty(companyId)||ObjUtils.isBlank(bankAccountNo) || pageBean == null) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            BankPettyReqDTO reqDTO = new BankPettyReqDTO();
            reqDTO.setCompanyId(companyId);
            reqDTO.setBankAccountNo(bankAccountNo);
            reqDTO.setPageNo(pageBean.getPageNo());
            reqDTO.setPageSize(pageBean.getPageSize());
            return queryBankPettyList(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数bankAccountNo:{},pageBean:{}", bankAccountNo, JSONObject.toJSONString(pageBean), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数bankAccountNo:{},pageBean:{}", bankAccountNo, JSONObject.toJSONString(pageBean), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数bankAccountNo:{},pageBean:{}", bankAccountNo, JSONObject.toJSONString(pageBean), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public BankPettyDetailRespDTO queryBankPettyByPettyId(String pettyId) {
        FinhubLogger.info("备用金查询接口queryBankPettyByPettyId(),参数pettyId:{}",pettyId);
        try {
            if (ObjUtils.isBlank(pettyId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            FinhubLogger.info("查询pettyDetail init pettyId:{}", pettyId);
            BankPetty bankPetty = bankPettyManager.queryBankPettyPettyId(pettyId);
            if (bankPetty == null) {
                return null;
            }
            //循环备用金，申请金额需要从申请单获取
            if (bankPetty.getPettyType() != null && bankPetty.getPettyType().equals(PettyType.ROUND.getKey())){
                Example example = new Example(BankCardCreditApply.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("companyId",bankPetty.getCompanyId());
                criteria.andEqualTo("employeeId",bankPetty.getEmployeeId());
                criteria.andEqualTo("applyTransNo", bankPetty.getApplyTransNo());
                criteria.andEqualTo("cardModel", CardModelType.PETTY.getKey());
                example.setOrderByClause("create_time asc");
                BankCardCreditApply apply = bankCardCreditApplyMapper.selectOneByExample(example);
                if (!ObjectUtils.isEmpty(apply)){
                    bankPetty.setApplySum(apply.getApplyAmount());
                }
            }
            Example example2 = new Example(BankCardCreditApply.class);
            Example.Criteria criteria = example2.createCriteria();
            criteria.andEqualTo("companyId",bankPetty.getCompanyId());
            criteria.andEqualTo("employeeId",bankPetty.getEmployeeId());
            criteria.andEqualTo("applyTransNo", bankPetty.getApplyTransNo());
            criteria.andEqualTo("cardModel", CardModelType.PETTY.getKey());
            BankCardCreditApply apply = bankCardCreditApplyMapper.selectOneByExample(example2);

            //获取备用金的费用相关信息
            List<BankCardCostAttributionRespDTO> costAtt=queryCostAttributionByPettyId(bankPetty.getPettyId());
            List<String> costAttNames=costAtt.stream().map(BankCardCostAttributionRespDTO::getCostAttributionName).collect(Collectors.toList());
            String ss = String.join(",", costAttNames);
            if (!StringUtils.isEmpty(ss) && ss.endsWith(";")) {//以防数据库字段中获取的值出现"；"结尾的情况
                ss = ss.substring(0, ss.length() - 1);
            }
            BankPettyDetailRespDTO dto = new BankPettyDetailRespDTO();
            BeanUtils.copyProperties(bankPetty, dto);
            dto.setBizNo(StringUtils.isEmpty(bankPetty.getMeaningNo()) ? bankPetty.getBizNo() : bankPetty.getMeaningNo());//优先获取meaning_no，不存在时取id
            BigDecimal paySum = BigDecimalUtils.hasNoPrice(bankPetty.getPaySum()) ? BigDecimal.ZERO : bankPetty.getPaySum();
            BigDecimal refundSum = BigDecimalUtils.hasNoPrice(bankPetty.getRefundSum()) ? BigDecimal.ZERO : bankPetty.getRefundSum();
            dto.setConsumeSum(paySum.subtract(refundSum));
            BigDecimal repaymentSum = BigDecimalUtils.hasNoPrice(bankPetty.getCardRepaymentSum()) ? BigDecimal.ZERO :bankPetty.getCardRepaymentSum().subtract(bankPetty.getCardRepaymentRefundSum());

            dto.setNoReimbursedSum(BigDecimalUtils.hasPrice(paySum)?paySum.subtract(refundSum).subtract(bankPetty.getReimbursedSum()).subtract(repaymentSum):BigDecimal.ZERO);
            dto.setCardRepaymentSum(repaymentSum);
            dto.setCostAttributionName(ss);

            Integer pettyType = dto.getPettyType();
            if (PettyType.COMMON.getKey() == pettyType || PettyType.SINGLE.getKey() == pettyType) {
                dto.setGrantSum(bankPetty.getApplySum());
            }
            if (ObjUtils.isNotEmpty(apply)) {
                dto.setApplyReason(apply.getApplyReason());
            }
            return dto;
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数pettyId:{}", pettyId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数pettyId:{}", pettyId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数pettyId:{}", pettyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public int updateRoundStatusyByPettyId(String pettyId, Integer roundstatus) {
        FinhubLogger.info("备用金查询接口updateRoundStatusyByPettyId(),参数pettyId:{},roundstatus:{}",pettyId,roundstatus);
        if (ObjUtils.isBlank(pettyId) || ObjUtils.isBlank(roundstatus)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        FinhubLogger.info("查询pettyDetail init pettyId:{}", pettyId);

        BankPetty bankPetty = new BankPetty();
        bankPetty.setPettyId(pettyId);
        bankPetty.setUpdateTime(new Date());
        bankPetty.setRoundStatus(roundstatus);
        //终止循环状态
        Example example = new Example(BankPetty.class);
        example.createCriteria().andEqualTo("pettyId", pettyId);
        int count = bankPettyMapper.updateByExampleSelective(bankPetty, example);
        if (count < 1) {
            throw new FinPayException(GlobalResponseCode.ROUND_PETTY_MODIFY_ROUND_STATUS_ERROR);
        }
        return count;
    }

    @Override
    public ResponsePage<BankPettyDetailRespDTO> queryBankPettyList(BankPettyReqDTO reqDTO) {
        FinhubLogger.info("备用金查询接口queryBankPettyList(),参数reqDTO:{}",reqDTO);
        try {
            if(StringUtils.isEmpty(reqDTO.getCompanyId())){
                throw new FinhubException(GlobalResponseCode.TOKEN_EXPIRE.getCode(),
                        GlobalResponseCode.TOKEN_EXPIRE.getType(),
                        GlobalResponseCode.TOKEN_EXPIRE.getMsg());
            }
            FinhubLogger.info("查询pettyList init param:{}", JSONObject.toJSON(reqDTO));
            ResponsePage<BankPettyCostAttDto> bankPettyByPage = bankPettyManager.selectBankPettyByPage(reqDTO);
            ResponsePage<BankPettyDetailRespDTO> responsePage = new ResponsePage<>();
            if (bankPettyByPage == null || CollectionUtils.isEmpty(bankPettyByPage.getDataList())) {
                return responsePage;
            }
            responsePage.setTotalCount(bankPettyByPage.getTotalCount());
            List<BankPettyDetailRespDTO> dataList = new ArrayList<>();
            for(BankPettyCostAttDto bankPetty : bankPettyByPage.getDataList()) {
                BankPettyDetailRespDTO dto = new BankPettyDetailRespDTO();
                BeanUtils.copyProperties(bankPetty, dto);
                BigDecimal paySum = BigDecimalUtils.hasNoPrice(bankPetty.getPaySum()) ? BigDecimal.ZERO : bankPetty.getPaySum();
                BigDecimal refundSum = BigDecimalUtils.hasNoPrice(bankPetty.getRefundSum()) ? BigDecimal.ZERO : bankPetty.getRefundSum();
                BigDecimal repaymentSum = BigDecimalUtils.hasNoPrice(bankPetty.getCardRepaymentSum()) ? BigDecimal.ZERO :bankPetty.getCardRepaymentSum().subtract(bankPetty.getCardRepaymentRefundSum());
                BigDecimal noReimbursedSum =BigDecimalUtils.hasNoPrice(bankPetty.getPaySum()) ? BigDecimal.ZERO :
                        paySum.subtract(refundSum).subtract(bankPetty.getReimbursedSum()).subtract(repaymentSum);
                dto.setNoReimbursedSum(noReimbursedSum);
                dto.setCardRepaymentSum(repaymentSum);
                dto.setReimbursedSum(bankPetty.getReimbursedSum());
                dto.setConsumeSum(paySum.subtract(refundSum));

                String costAttributionName = bankPetty.getCostAttributionName();
                if (ObjUtils.isNotEmpty(costAttributionName) && (costAttributionName.charAt(costAttributionName.length()-1) == ';' || costAttributionName.charAt(costAttributionName.length()-1) == ',')) {
                    costAttributionName = costAttributionName.substring(0, costAttributionName.length()-1);
                }
                dto.setCostAttributionName(costAttributionName);
                dataList.add(dto);
            }
            if (CollUtil.isNotEmpty(dataList)) {
                List<String> bizNoList = dataList.stream().filter(petty -> ObjUtils.isNotEmpty(petty.getBizNo())).map(BankPettyDetailRespDTO::getBizNo).collect(Collectors.toList());
                List<BankCardCreditApplyResqDTO> bankCardCreditApplyList = creditApplyOperationManager.getBankCardCreditApplyByBizNoList(bizNoList);
                Map<String, BankCardCreditApplyResqDTO> bizNoMap = new HashMap<>();
                Map<String, BankCardCreditApplyResqDTO> map = bankCardCreditApplyList.stream().filter(card -> ObjUtils.isNotEmpty(card.getBizNo())).collect(Collectors.toMap(BankCardCreditApplyResqDTO::getBizNo, Function.identity()));
                if (map != null) {
                    bizNoMap = map;
                }
                Map<String, BankCardCreditApplyResqDTO> finalBizNoMap = bizNoMap;
                dataList.forEach(item ->{
                    BankCardCreditApplyResqDTO bankCardCreditApplyResqDTO = finalBizNoMap.get(item.getBizNo());
                    if (ObjUtils.isNotEmpty(bankCardCreditApplyResqDTO)) {
                        item.setApplyReason(bankCardCreditApplyResqDTO.getApplyReason());
                    }
                });
            }

            responsePage.setDataList(dataList);
            return responsePage;
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数reqDTO:{}", JSONObject.toJSONString(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数reqDTO:{}", JSONObject.toJSONString(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，请求参数reqDTO:{}", JSONObject.toJSONString(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<BankPettySimpleResqDTO> findSimpleBankPetty(List<String> pettyIds) {
        FinhubLogger.info("备用金查询接口findSimpleBankPetty,参数pettyIds:{}",pettyIds);
        try {
            if (CollectionUtils.isEmpty(pettyIds)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            return bankPettyManager.findBankPettyPettyIds(pettyIds);

        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，findSimpleBankPetty:{}", JsonUtils.toJson(pettyIds), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，findSimpleBankPetty:{}", JsonUtils.toJson(pettyIds), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡备用金查询】异常，findSimpleBankPetty:{}", JsonUtils.toJson(pettyIds), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<BankCardCostAttributionRespDTO> queryCostAttributionByPettyId(String pettyId) {
        FinhubLogger.info("备用金查询接口queryCostAttributionByPettyId,参数pettyId:{}",pettyId);
        List<BankCardCostAttributionRespDTO> bankCardCostAttributionRespDTOS = Lists.newArrayList();
        BankPetty bankPetty = bankPettyManager.queryBankPettyPettyId(pettyId);
        if(ObjUtils.isNull(bankPetty)){
            return bankCardCostAttributionRespDTOS;
        }
        List<BankCardCostAttribution> cardCostAttributions = bankCardCostAttributionMapper.queryCostAttributionByApplyTransNo(bankPetty.getApplyTransNo());
        if(CollectionUtils.isNotEmpty(cardCostAttributions)){
            for (BankCardCostAttribution cardCostAttribution : cardCostAttributions){
                BankCardCostAttributionRespDTO respDTO = new BankCardCostAttributionRespDTO();
                BeanUtils.copyProperties(cardCostAttribution,respDTO);
                bankCardCostAttributionRespDTOS.add(respDTO);
            }
        }
        return bankCardCostAttributionRespDTOS;
    }
    @Override
    public Map<String,String> queryBizNoByPettyIds(List<String> pettyIds) {
        if (CollectionUtils.isEmpty(pettyIds)) {
            return new HashMap<>();
        }
        long cur1 = System.currentTimeMillis();
        List<BankPettyCostAttDto> bankPettyPetty = bankPettyManager.findBizNoPettyIds(pettyIds);
        if (ObjUtils.isNull(bankPettyPetty)) {
            FinhubLogger.info("【洗数据meaningNo】未获取到备用金数据");
            return new HashMap<>();
        }
        long cur2 = System.currentTimeMillis();
        FinhubLogger.info("【洗数据meaningNo】获取备用金的数据,花费时间:{}ms", cur2 - cur1);
        List<String> bizNos = bankPettyPetty.stream().map(BankPettyCostAttDto::getBizNo).collect(Collectors.toList());
        Map<String, String> meaningNoById = iApplyOrderService.getApplyMeaningNoById(bizNos);//id---->meaning_no
        long cur3 = System.currentTimeMillis();
        FinhubLogger.info("【洗数据meaningNo】从saas-plus中获取meaningNo数据成功,花费时间:{}ms",cur3 - cur2);
        List<List<BankPetty>> list = new ArrayList<>();
        long size = 1L;
        List<BankPetty> aa = new ArrayList<>();
        list.add(aa);
        for (String bizNo : meaningNoById.keySet()) {
            if (size > 5000L) {
                //数据过多，分次执行
                size = 0L;
                List<BankPetty> bb = new ArrayList<>();
                list.add(bb);
            }
            BankPetty petty = new BankPetty();
            petty.setBizNo(bizNo);
            String meaningNo = meaningNoById.get(bizNo);
            petty.setMeaningNo(StringUtils.isEmpty(meaningNo) ? bizNo : meaningNo);
            list.get(list.size() - 1).add(petty);//每次都是list最后一个对象增加值
            size++;
        }
        Map<String, String> returnMap = new HashMap<>(bankPettyPetty.size());
        for (BankPettyCostAttDto dto : bankPettyPetty) {
            returnMap.put(dto.getPettyId(), meaningNoById.get(dto.getBizNo()));
        }
        FinhubLogger.info("【洗数据meaningNo】从saas-plus中获取到了id与meaningNo的关系,一共{}条", meaningNoById.size());
        CompletableFuture.runAsync(()-> {
            FinhubLogger.info("【洗数据meaningNo】异步更新tb_bank_petty表");
            if (CollectionUtils.isNotEmpty(list)) {
                for (List<BankPetty> bankPetties : list) {
                    if (CollectionUtils.isEmpty(bankPetties)) {
                        continue;
                    }
                    bankPettyManager.updatePettyMeaningNoByBizNo(bankPetties);
                }
            }
        });
        FinhubLogger.info("【洗数据meaningNo】将tb_bank_petty中meaning_no赋值成功");
        return returnMap;
    }

    @Override
    public ResponsePage<BankCardFlowInPettyResDTO> bankCardFlowInPettyPage(String applyOrderId, Integer pageNo, Integer pageSize) {
        FinhubLogger.info("备用金查询接口bankCardFlowInPettyPage,参数applyOrderId：{}",applyOrderId);
        return bankCardApplyFlowManger.bankCardFlowInPettyPage(applyOrderId,pageNo,pageSize);
    }

    @Override
    public BigDecimal getGrantSum(String applyOrderId) {
        FinhubLogger.info("备用金查询接口getGrantSum,参数applyOrderId：{}",applyOrderId);
        return bankCardApplyFlowManger.getGrantSum(applyOrderId);
    }

    @Override
    public ResponsePage<BankPettyDetailRespDTO> queryExpiredBankPettyList(BankPettyReqDTO reqDTO) {
        FinhubLogger.info("备用金查询接口queryExpiredBankPettyList,参数reqDTO：{}", JSON.toJSONString(reqDTO));
        ResponsePage<BankPettyDetailRespDTO> responsePage = new ResponsePage<>();
        ResponsePage<BankPetty> bankPettyResponsePage = bankPettyManager.queryExpiredBankPettyList(reqDTO);
        if (bankPettyResponsePage == null && ObjUtils.isEmpty(bankPettyResponsePage.getDataList())) {
            return responsePage;
        }
        responsePage.setTotalCount(bankPettyResponsePage.getTotalCount());
        List<BankPettyDetailRespDTO> dataList = new ArrayList<>();
        bankPettyResponsePage.getDataList().forEach(bankPetty -> {
            BankPettyDetailRespDTO dto = new BankPettyDetailRespDTO();
            BeanUtils.copyProperties(bankPetty, dto);
            dataList.add(dto);
        });
        responsePage.setDataList(dataList);
        return responsePage;
    }

    @Override
    public List<String> findBankAccountNosByCompanyIdAndPettyId(String companyId, String pettyId) {
        return creditDistributeManager.findBankAccountNosByCompanyIdAndPettyId(companyId,pettyId);
    }

    @Override
    public List<String> findPettyIdsByCompanyIdAndBankAccountNo(String companyId, String bankAccountNo) {
        return creditDistributeManager.findPettyIdsByCompanyIdAndBankAccountNo(companyId,bankAccountNo);
    }

    @Override
    public void findPettyOfRoundAndUpdateToStop() {

        BankPetty bankPetty = new BankPetty();
        bankPetty.setUpdateTime(new Date());
        bankPetty.setRoundStatus(RoundPettyStatus.DISCARD_NO_PETTY.getKey());
        //终止循环状态
        Example example = new Example(BankPetty.class);
        example.createCriteria()
                .andEqualTo("pettyType", PettyType.ROUND.getKey())
                .andNotEqualTo("roundStatus",RoundPettyStatus.DISCARD_NO_PETTY.getKey())
                .andEqualTo("bankName", BankNameEnum.ZBBANK.getCode());
        int count = bankPettyMapper.updateByExampleSelective(bankPetty, example);
        FinhubLogger.info("ZBANK ROUND STOP:" + count);
    }
}
