package com.fenbeitong.fenbeipay.rpc.service.bank;

import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardModifyService;
import com.fenbeitong.fenbeipay.bank.base.conver.BankCardConver;
import com.fenbeitong.fenbeipay.bank.base.manager.IBankCardManager;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.finhub.common.constant.FinhubMessageType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.ILLEGAL_ARGUMENT;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
@Service("iBankCardModifyService")
public class BankCardModifyServiceImpl implements IBankCardModifyService {

    @Resource
    private IBankCardManager bankCardManager;

    @Override
    public Integer updateBankCardByBankUid(BankCardDTO record, String bankUid) {
        if (ObjUtils.isNull(record)) {
            throw new FinhubException(ILLEGAL_ARGUMENT.getCode(), FinhubMessageType.TIP_TOAST, ILLEGAL_ARGUMENT.getMsg());
        }
        BankCard bankCard = BankCardConver.copyBankCardDTO2BankCard(record);
        return bankCardManager.updateBankCardByBankUid(bankCard, bankUid);
    }

    @Override
    public Integer updateBankCardById(BankCardDTO record, Long id) {
        if (ObjUtils.isNull(record)) {
            throw new FinhubException(ILLEGAL_ARGUMENT.getCode(), FinhubMessageType.TIP_TOAST, ILLEGAL_ARGUMENT.getMsg());
        }
        BankCard bankCard = BankCardConver.copyBankCardDTO2BankCard(record);
        return bankCardManager.updateBankCardById(bankCard, id);
    }

    @Override
    public Integer removeBindAcctNo(String bankUid) {
        return bankCardManager.removeBindAcctNo(bankUid);
    }
}
