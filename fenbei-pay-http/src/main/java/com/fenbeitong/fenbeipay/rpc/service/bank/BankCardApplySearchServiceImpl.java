package com.fenbeitong.fenbeipay.rpc.service.bank;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.VirtualCardApplyOrderCostRespDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardApplySearchService;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankPettyMapper;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.BankPettyManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.CreditApplyOperationManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.SearchCardManager;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCostAttribution;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCreditApplyFlow;
import com.fenbeitong.fenbeipay.dto.bank.BankPetty;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.exception.GlobalCoreResponseCode;
import com.fenbeitong.saasplus.api.model.dto.bill.FinanceCostInfoDetailDTO;
import com.fenbeitong.saasplus.api.service.cost.IFinanceCostInfoDetailRpcService;
import com.fenbeitong.saasplus.api.service.virtualCard.ApplyOrderCostInfoVO;
import com.fenbeitong.saasplus.api.service.virtualCard.TbBankOrderRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2022/7/29 3:34 PM
 */
@Slf4j
@Service("iBankCardApplySearchService")
public class BankCardApplySearchServiceImpl implements IBankCardApplySearchService {

    @Resource
    private IFinanceCostInfoDetailRpcService iFinanceCostInfoDetailRpcService;

    @Autowired
    private CreditApplyOperationManager creditApplyOperationManager;

    @Autowired
    private BankPettyMapper bankPettyMapper;

    @Autowired
    private TbBankOrderRpcService tbBankOrderRpcService;

    /**
     * 根据CostId 查询申请单/费用/费用归属等信息
     * @param costIdList
     * @return
     */
    @Override
    public List<VirtualCardApplyOrderCostRespDTO> queryApplyOrderCostInfo(List<String> costIdList) {
        List<VirtualCardApplyOrderCostRespDTO> result = new ArrayList<>();
        log.info("RPC queryApplyOrderCostInfo, costCatagoryIdList: {}.", JSON.toJSONString(costIdList));
        if(CollectionUtils.isEmpty(costIdList)){
            return result;
        }
        List<FinanceCostInfoDetailDTO> financeCostInfoDetailDTOS = iFinanceCostInfoDetailRpcService.queryFinanceCostInfoDetailList(transferToInteger(costIdList));
        if(CollectionUtils.isEmpty(financeCostInfoDetailDTOS)){
            log.info("RPC queryApplyOrderCostInfo result is NULL, costCatagoryIdList: {}.", JSON.toJSONString(costIdList));
            return result;
        }
        Map<String, BigDecimal> pettyPrice = getPettyPirceMap(financeCostInfoDetailDTOS);
        // 将CostId 列表按照是否有pettyId 划分成两种：备用金类型和虚拟卡额度类型
        Map<Integer, Set<String>> costIdPettyMap = filterPetty(financeCostInfoDetailDTOS, true);
        Map<Integer, Set<String>> costIdCostNoMap = filterPetty(financeCostInfoDetailDTOS, false);

        List<VirtualCardApplyOrderCostRespDTO> pettyCostList = queryPettyCost(costIdPettyMap, pettyPrice);
        List<VirtualCardApplyOrderCostRespDTO> vcAmountCostList = queryVcAmountCost(costIdCostNoMap);
        result = merge(pettyCostList, vcAmountCostList);
        return result;
    }

    /**
     * 统计每笔备用金承担的交易金额
     *
     * @param financeCostInfoDetailDTOS
     * @return pettyId-price 键值对
     */
    private Map<String, BigDecimal> getPettyPirceMap(List<FinanceCostInfoDetailDTO> financeCostInfoDetailDTOS) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(financeCostInfoDetailDTOS) ){
            return resultMap;
        }
        for(FinanceCostInfoDetailDTO financeCostInfoDetailDTO: financeCostInfoDetailDTOS){
            if(StringUtils.isBlank(financeCostInfoDetailDTO.getPettyId()) || financeCostInfoDetailDTO.getPrice() == null){
                continue;
            }
            BigDecimal price = resultMap.get(financeCostInfoDetailDTO.getPettyId());
            if(price != null){
                price = price.add(financeCostInfoDetailDTO.getPrice());
                resultMap.put(financeCostInfoDetailDTO.getPettyId(), price);
            }else {
                resultMap.put(financeCostInfoDetailDTO.getPettyId(), financeCostInfoDetailDTO.getPrice());
            }
        }
        return resultMap;
    }

    /**
     * 同样CostId 的数据进行合并
     *
     * @param pettyCostList
     * @param vcAmountCostList
     * @return
     */
    private List<VirtualCardApplyOrderCostRespDTO> merge(List<VirtualCardApplyOrderCostRespDTO> pettyCostList, List<VirtualCardApplyOrderCostRespDTO> vcAmountCostList) {
        if (CollectionUtils.isEmpty(pettyCostList)) {
            return vcAmountCostList;
        }
        if (CollectionUtils.isEmpty(vcAmountCostList)) {
            return pettyCostList;
        }
        List<VirtualCardApplyOrderCostRespDTO> result = new ArrayList<>();
        Map<String, VirtualCardApplyOrderCostRespDTO> resultMap = new HashMap<>();
        for (VirtualCardApplyOrderCostRespDTO dto : pettyCostList) {
            resultMap.put(dto.getCostId(), dto);
        }
        for (VirtualCardApplyOrderCostRespDTO dto : vcAmountCostList) {
            VirtualCardApplyOrderCostRespDTO tmp = resultMap.get(dto.getCostId());
            if (tmp != null && !CollectionUtils.isEmpty(tmp.getApplyOrderIdList())) {
                if (!tmp.getApplyOrderIdList().containsAll(dto.getApplyOrderIdList())) {
                    tmp.getApplyOrderList().addAll(dto.getApplyOrderList());
                    tmp.getApplyOrderIdList().addAll(dto.getApplyOrderIdList());
                }
            } else {
                resultMap.put(dto.getCostId(), dto);
            }
        }
        result.addAll(resultMap.values());
        return result;
    }

    /**
     * 虚拟卡额度申请类型的费用查询
     *
     * @param costIdCostNoMap
     * @return
     */
    private List<VirtualCardApplyOrderCostRespDTO> queryVcAmountCost(Map<Integer, Set<String>> costIdCostNoMap) {
        List<VirtualCardApplyOrderCostRespDTO> result = new ArrayList<>();
        for(Map.Entry<Integer, Set<String>>entry: costIdCostNoMap.entrySet()){
            List<String> costNoList = new ArrayList<>();
            costNoList.addAll(entry.getValue());
            List<BankCardCreditApplyFlow> list = creditApplyOperationManager.getVirtualCreditApplyFlow(null, costNoList);
            if (CollectionUtils.isEmpty(list)) {
                log.info("getVirtualCreditApplyFlow is null, costNoList:{}.", JSON.toJSONString(costNoList));
                continue;
            }
            Map<String, VirtualCardApplyOrderCostRespDTO> costCategoryApplyOrderMap = buildCostCategoryApplyOrderMap(entry.getKey(), list);
            if(MapUtils.isEmpty(costCategoryApplyOrderMap)){
                continue;
            }
            for(Map.Entry<String, VirtualCardApplyOrderCostRespDTO> entry2: costCategoryApplyOrderMap.entrySet()){
                VirtualCardApplyOrderCostRespDTO virtualCardApplyOrderCostRespDTO = entry2.getValue();
                Map<String, ApplyOrderCostInfoVO> applyOrderCostInfoVOMap = tbBankOrderRpcService.queryApplyOrderCostInfoVOList(virtualCardApplyOrderCostRespDTO.getApplyOrderIdList());
                log.info("queryApplyOrderCostInfoVOList applyOrderCostInfoVOMap:{}.", JSON.toJSONString(applyOrderCostInfoVOMap));
                assembleVirtualCardApplyOrderCostRespDTO(virtualCardApplyOrderCostRespDTO, applyOrderCostInfoVOMap);
                result.add(virtualCardApplyOrderCostRespDTO);
            }
        }
        return result;
    }

    /**
     * 备用金类型报销单查询
     * @param costIdPettyMap
     * @return
     */
    private List<VirtualCardApplyOrderCostRespDTO> queryPettyCost(Map<Integer, Set<String>> costIdPettyMap, Map<String, BigDecimal> pettyPrice) {
        List<VirtualCardApplyOrderCostRespDTO> result = new ArrayList<>();
        if(costIdPettyMap == null || costIdPettyMap.isEmpty()){
            return result;
        }
        for (Map.Entry<Integer, Set<String>> entry : costIdPettyMap.entrySet()) {
            if(CollectionUtils.isEmpty(entry.getValue())){
                continue;
            }
            Set<String> pettyIdList = entry.getValue();
            Example example = new Example(BankPetty.class);
            example.createCriteria().andIn("pettyId", pettyIdList);
            List<BankPetty> list = bankPettyMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<String> applyOrderIdList = new ArrayList<>();
            List<VirtualCardApplyOrderCostRespDTO.ApplyOrder> applyOrderList = new ArrayList<>();
            list.stream().forEach(bankPetty ->{
                applyOrderIdList.add(bankPetty.getBizNo());
                VirtualCardApplyOrderCostRespDTO.ApplyOrder applyOrder = new VirtualCardApplyOrderCostRespDTO.ApplyOrder();
                applyOrder.setApplyOrderId(bankPetty.getBizNo());
                applyOrder.setWriteOffAmount(pettyPrice.get(bankPetty.getPettyId()));
                applyOrderList.add(applyOrder);
            });
            VirtualCardApplyOrderCostRespDTO virtualCardApplyOrderCostRespDTO = new VirtualCardApplyOrderCostRespDTO();
            virtualCardApplyOrderCostRespDTO.setCostId(String.valueOf(entry.getKey()));
            virtualCardApplyOrderCostRespDTO.setApplyOrderIdList(applyOrderIdList);
            virtualCardApplyOrderCostRespDTO.setApplyOrderList(applyOrderList);
            Map<String, ApplyOrderCostInfoVO> applyOrderCostInfoVOMap = tbBankOrderRpcService.queryApplyOrderCostInfoVOList(applyOrderIdList);


            assembleVirtualCardApplyOrderCostRespDTO(virtualCardApplyOrderCostRespDTO,applyOrderCostInfoVOMap );
            result.add(virtualCardApplyOrderCostRespDTO);
        }
        return result;

    }

    /**
     * isPetty = true, 返回costId 和 pettyId 映射关系；否则返回 costId 和 costNo 映射关系
     * @param financeCostInfoDetailDTOS
     * @param isPetty
     * @return
     */
    private Map<Integer, Set<String>> filterPetty(List<FinanceCostInfoDetailDTO> financeCostInfoDetailDTOS, boolean isPetty) {
        Map<Integer, Set<String>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(financeCostInfoDetailDTOS)) {
            return result;
        }
        financeCostInfoDetailDTOS.stream().forEach(financeCostInfoDetailDTO -> {
            Set<String> tmpList = result.get(financeCostInfoDetailDTO.getCostId());
            if (tmpList == null) {
                tmpList = new HashSet<>();
            }
            if (isPetty) {
                if(!StringUtils.isBlank(financeCostInfoDetailDTO.getPettyId())){
                    tmpList.add(financeCostInfoDetailDTO.getPettyId());
                }
            } else if(StringUtils.isBlank(financeCostInfoDetailDTO.getPettyId())){ // pettyId 为null 才是虚拟卡额度核销
                tmpList.add(financeCostInfoDetailDTO.getCostNo());
            }
            result.put(financeCostInfoDetailDTO.getCostId(), tmpList);
        });
        return result;
    }

    private List<Integer> transferToInteger(List<String> costIdList) {
        List<Integer> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(costIdList)) {
            return result;
        }
        costIdList.stream().forEach(costId -> result.add(Integer.valueOf(costId)));
        return result;
    }

    /**
     * 对virtualCardApplyOrderCostRespDTO 填充费用归属信息
     * @param virtualCardApplyOrderCostRespDTO
     * @param applyOrderCostInfoVOMap
     */
    private void assembleVirtualCardApplyOrderCostRespDTO(VirtualCardApplyOrderCostRespDTO virtualCardApplyOrderCostRespDTO, Map<String, ApplyOrderCostInfoVO> applyOrderCostInfoVOMap) {
        List<VirtualCardApplyOrderCostRespDTO.ApplyOrder> applyOrderList = virtualCardApplyOrderCostRespDTO.getApplyOrderList();
        if (CollectionUtils.isEmpty(applyOrderList)) {
            throw new FinhubException(GlobalCoreResponseCode.CARD_ORDER_PARAM_ERROR.getCode(), "查询数据异常");
        }
        for (VirtualCardApplyOrderCostRespDTO.ApplyOrder applyOrder : applyOrderList) {
            ApplyOrderCostInfoVO applyOrderCostInfoVO = applyOrderCostInfoVOMap.get(applyOrder.getApplyOrderId());
            if(applyOrderCostInfoVO == null){
                continue;
            }
            applyOrder.setApplyOrderNo(applyOrderCostInfoVO.getMeaningNo());
            applyOrder.setCostCategoryId(applyOrderCostInfoVO.getCostCategoryId());
            applyOrder.setCostCategoryName(applyOrderCostInfoVO.getCostCategoryName());
            applyOrder.setCostAttributionGroupList(transfer(applyOrderCostInfoVO.getCostAttributionGroupList()));
        }
    }

    private List<VirtualCardApplyOrderCostRespDTO.CostAttributionGroup> transfer(List<ApplyOrderCostInfoVO.CostAttributionGroup> costAttributionGroupList) {
        String jsonValue = JSON.toJSONString(costAttributionGroupList);
        return JSON.parseArray(jsonValue, VirtualCardApplyOrderCostRespDTO.CostAttributionGroup.class);
    }

    /**
     * 按照costId 进行分组
     *
     * @param bankCardCreditApplyFlowList
     * @return
     */
    private Map<String, VirtualCardApplyOrderCostRespDTO> buildCostCategoryApplyOrderMap(Integer costId, List<BankCardCreditApplyFlow> bankCardCreditApplyFlowList) {
        Map<String, VirtualCardApplyOrderCostRespDTO> result = new HashMap<>();

        List<String> applyOrderIdList = new ArrayList<>();
        List<VirtualCardApplyOrderCostRespDTO.ApplyOrder> applyOrderList = new ArrayList<>();

        for (BankCardCreditApplyFlow bankCardCreditApplyFlow : bankCardCreditApplyFlowList) {
            applyOrderIdList.add(bankCardCreditApplyFlow.getBizNo());

            VirtualCardApplyOrderCostRespDTO.ApplyOrder applyOrder = new VirtualCardApplyOrderCostRespDTO.ApplyOrder();
            applyOrder.setApplyOrderId(bankCardCreditApplyFlow.getBizNo());
            if (bankCardCreditApplyFlow.getDeductionAmount() != null) {
                BigDecimal amount = bankCardCreditApplyFlow.getDeductionAmount().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                applyOrder.setWriteOffAmount(amount);
            }
            applyOrderList.add(applyOrder);

        }
        VirtualCardApplyOrderCostRespDTO virtualCardApplyOrderCostRespDTO = new VirtualCardApplyOrderCostRespDTO();
        virtualCardApplyOrderCostRespDTO.setCostId(String.valueOf(costId));
        virtualCardApplyOrderCostRespDTO.setApplyOrderList(applyOrderList);
        virtualCardApplyOrderCostRespDTO.setApplyOrderIdList(applyOrderIdList);

        result.put(String.valueOf(costId), virtualCardApplyOrderCostRespDTO);
        return result;
    }

}
