package com.fenbeitong.fenbeipay.rpc.service.bank;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.dech.api.model.dto.spabank.req.QueryTransactionDetailReqDto;
import com.fenbeitong.dech.api.model.dto.spabank.req.SpaQueryCardBalanceReqDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.QueryTransactionDetailDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.QueryTransactionDetailRespDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaPersonAcctRespBaseDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaQueryCardBalanceRespDto;
import com.fenbeitong.dech.api.service.spabank.ISpaBankPersonAcctService;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.BankCardTrapFlowMapper;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyCardFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyCardService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyCardService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardInfoReqDto;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.*;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctCompanyCardService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankAccountService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.bank.base.conver.*;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.*;
import com.fenbeitong.fenbeipay.bank.base.dto.CreateRefundOrderDto;
import com.fenbeitong.fenbeipay.bank.base.dto.RefundOrderTypeDto;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.*;
import com.fenbeitong.fenbeipay.bank.company.order.manager.TradeCardManager;
import com.fenbeitong.fenbeipay.bank.company.service.trade.BankCardFundFlowService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.virtualcard.VirtualCardTradeFundType;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.service.message.MessageService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.*;
import com.fenbeitong.fenbeipay.core.utils.notice.EmailContract;
import com.fenbeitong.fenbeipay.core.utils.notice.NoticeUtils;
import com.fenbeitong.fenbeipay.core.utils.notice.SmsContract;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyCard;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyCardFlow;
import com.fenbeitong.fenbeipay.dto.bank.*;
import com.fenbeitong.fenbeipay.rpc.service.base.IBaseConsumerKafkaServiceImpl;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaCardRefundCreditMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaPushMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.fenbeitong.finhub.kafka.producer.impl.KafkaProducerPublisher;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupReceiverVO;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupVO;
import com.fenbeitong.saas.api.service.message.setting.IMessageSettingService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeBaseInfo;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeOrgUnitDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeSimpleInfoContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.google.common.base.Objects;
import com.google.common.collect.Maps;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.*;


/**
 * 账户的操作业务
 */
@Service("iBankAccountService")
public class IBankAccountServiceImpl extends IBaseConsumerKafkaServiceImpl implements IBankAccountService {

    @Autowired
    public ApplyCardManager applyCardManager;
    @Autowired
    public CreditApplyManager creditApplyManager;
    @Autowired
    private BankPettyManager bankPettyManager;
    @Autowired
    private CreditDistributeManager creditDistributeManager;
    @Autowired
    public IBaseEmployeeExtService iBaseEmployeeExtService;
    @Autowired
    public BankPettyExtMapper bankPettyExtMapper;
    @Autowired
    public BankPettyMapper bankPettyMapper;
    @Autowired
    public BankCardExtMapper bankCardMapper;
    @Autowired
    private TradeCardManager tradeCardManager;
    @Autowired
    private SearchCardManager searchCardManager;
    @Autowired
    protected IPrivilegeService iPrivilegeService;
    @Autowired
    public BankCardApplyFlowMapper bankCardApplyFlowMapper;
    @Autowired
    public BankCardTradeFlowMapper bankCardTradeFlowMapper;
    @Autowired
    public IAcctCompanyCardService iAcctCompanyCardService;
    @Autowired
    private AcctCompanyCardService acctCompanyCardService;
    @Autowired
    private AcctCompanyCardFlowService acctCompanyCardFlowService;
    @Autowired
    private BankCardCreditApplyMapper bankCardCreditApplyMapper;
    @Autowired
    public MessageService messageService;
    @Autowired
    private KafkaProducerPublisher kafkaProducerPublisher;
    @Autowired
    private BankCardTrapFlowMapper bankCardTrapFlowMapper;

    /**
     * 备用金短信模板ID.企业虚拟卡额度不足
     */
    public static String ROUND_PETTY_MSG_TEMP_NO_AMOUNT_ID = "649942571e5a583d26be0dee";
    public static String CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID = "64afae723ca66d6e7b9653de";

    public static String CREDIT_REFUND_BIZ_CODE  = "credit_refund_remind";

    @Autowired
    protected UAcctCompanyCardService uAcctCompanyCardService;

    @Autowired
    protected EmployeeService employeeService;
    @Autowired
    private RedissonService redissonService;

    @Autowired
    private IMessageSettingService iMessageSettingService;

    @Autowired
    private ISpaBankPersonAcctService iSpaBankPersonAcctService;
    @Autowired
    private IBankCardSearchService iBankCardSearchService;
    @Value("${exception.remind.profile}")
    private String currentEnvironment;
    @Autowired
    BankCardFundFlowService bankCardFundFlowService;

    @Autowired
    VirtualCardCreditManager virtualCardCreditManager;

    @Override
    public BankCreateAccountRespDTO createBankAccount(BankCreateAccountReqDTO reqDTO) {
        FinhubLogger.info("开户参数=={}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            return applyCardManager.createBankAccount(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝通虚拟卡开户异常】createAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝通虚拟卡开户验证异常】createAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝通虚拟卡开户系统异常】createAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(),
                    EXCEPTION.getMsg());
        }
    }

    @Override
    public BankBindAccountRespDTO bindAccount(BankBindAccountReqDTO reqDTO) {
        FinhubLogger.info("用户分贝通虚拟卡和公司绑定参数=={}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            BankBindAccountRespDTO bankBindAccountRespDTO = applyCardManager.bindAccount(reqDTO);
            //埋点记录event消息:绑卡成功
            EventParams eventParams = EventParams.build(**********)
                    .userid(reqDTO.getEmployeeId())
                    .status(true)
                    .supplier(reqDTO.getBankName())
                    .put("bankAccountNo", reqDTO.getBankAccountNo());
            FinhubLogger.event(reqDTO.getCompanyId(), eventParams);
            return bankBindAccountRespDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【用户分贝通虚拟卡和公司绑定异常】bindAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【用户分贝通虚拟卡和公司绑定验证异常】bindAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【用户分贝通虚拟卡和公司绑定系统异常】bindAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public BankUnbindAccountRespDTO unBindAccount(BankUnbindAccountReqDTO reqDTO) {
        FinhubLogger.info("用户分贝通虚拟卡和公司解绑参数=={}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            //保存分贝通虚拟卡的调额申请流水
            return applyCardManager.unBindAccount(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.warn("【银用户分贝通虚拟卡和公司解绑异常】unbindAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.warn("【银用户分贝通虚拟卡和公司解绑异常】unbindAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.warn("【用户分贝通虚拟卡和公司解绑验证异常】unbindAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【用户分贝通虚拟卡和公司解绑系统异常】unbindAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    //@Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BankApplyCreditRespDTO applyCredit(BankApplyCreditReqDTO reqDTO) {
        FinhubLogger.info("分贝通虚拟卡申请额度参数=={}", JsonUtils.toJson(reqDTO));

        if (CollUtil.isEmpty(reqDTO.getDistributeDataList())) {
            List<BankCardDistributeReqDTO> distributeDataList = Lists.newArrayList();
            BankCardDistributeReqDTO bankCardDistributeReqDTO = new BankCardDistributeReqDTO();
            bankCardDistributeReqDTO.setBankAccountNo(reqDTO.getBankAccountNo());
            bankCardDistributeReqDTO.setBankName(reqDTO.getBankName());
            bankCardDistributeReqDTO.setDistributeAmount(reqDTO.getOperationAmount());
            distributeDataList.add(bankCardDistributeReqDTO);
            reqDTO.setDistributeDataList(distributeDataList);
        }
        //标记为一人一卡发放
        reqDTO.setOneFlag(true);

        BankCardBatchDistributeRespDTO bankCardBath = this.batchDistributeCredit(reqDTO);
        if (ObjUtils.isEmpty(bankCardBath)) {
            return null;
        }
        return bankCardBath.getBankApplyCredit();
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BankCardRetryRefundCreditRespDTO retryRefundCredit(String applyTransNo) {
        try {
            List<BankCardCreditDistribute> creditApplys = creditDistributeManager.queryDistributeByTransNo(applyTransNo);
            if (CollectionUtils.isEmpty(creditApplys)) {
                throw new FinPayException(GlobalResponseCode.BANK_CARD_APPLY_NOT_EXIST_ERROR);
            }
            List<BankCardCreditDistribute> collect = creditApplys.stream().filter(p -> BankTrapStatus.FAIL.getKey() == p.getTrapStatus()).collect(Collectors.toList());
            if (collect.size()==0) {
                throw new FinPayException(GlobalResponseCode.BANK_CARD_APPLY_NOT_SUPPORT_RETRY_ERROR);
            }
            if (collect.size()>1) {
                throw new FinPayException(GlobalResponseCode.BANK_CARD_APPLY_DISTRIBUTE_MUTI_ERROR);
            }
            BankCardCreditDistribute creditApply = creditApplys.get(0);

            return retryRefundDistributeCredit(creditApply.getApplyTransNo(), creditApply.getDistributeOrderNo());
        } catch (FinPayException e) {
            FinhubLogger.warn("【分贝通虚拟卡退还企业额度重试异常】retryRefundCredit=applyTransNo：{}", applyTransNo, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.warn("【分贝通虚拟卡退还企业额度重试异常】retryRefundCredit=applyTransNo：{}", applyTransNo, e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BankCardRetryRefundCreditRespDTO retryRefundDistributeCredit(String applyTransNo, String distributeOrderNo) {
        String distributeOrderNoExisted = virtualCardCreditManager.retryRefundDistributeCredit(applyTransNo,distributeOrderNo);
        if (distributeOrderNoExisted == null){
            throw new FinPayException(GlobalResponseCode.BANK_CARD_APPLY_NOT_EXIST_ERROR);
        }
        BankCardRetryRefundCreditRespDTO respDTO = new BankCardRetryRefundCreditRespDTO();
        try {
            BankCardCreditDistribute creditApply = creditDistributeManager.getDistributeByDistributeOrderNo(distributeOrderNoExisted);
            if (creditApply == null) {
                throw new FinPayException(GlobalResponseCode.BANK_CARD_APPLY_NOT_EXIST_ERROR);
            }
            if (BankTrapStatus.FAIL.getKey() != creditApply.getTrapStatus()) {
                throw new FinPayException(GlobalResponseCode.BANK_CARD_APPLY_NOT_SUPPORT_RETRY_ERROR);
            }

            String bankName = creditApply.getBankName();
            BankSearchCardReqDTO reqDTO = BankSearchCardReqDTO.builder().employeeId(creditApply.getEmployeeId()).bankName(bankName).bankAccountNo(creditApply.getBankAccountNo()).build();
            BankSearchCardDetailRespDTO bankSearchCardDetailRespDTO = searchCardManager.searchBankCardDetail(reqDTO);
            if (bankSearchCardDetailRespDTO == null) {
                throw new FinPayException(GlobalResponseCode.BANK_CARD_ACCOUNT_NO_EXIST);
            }
            if (bankSearchCardDetailRespDTO.getCardBalance().compareTo(creditApply.getDistributeAmount()) <0){
                throw new FinPayException(GlobalResponseCode.BANK_CARD_ACCOUNT_NO_BALANCE);
            }

            //更新圈存状态为圈存中，圈存完成根据圈存结果再更新圈存状态
            creditDistributeManager.updateTrapStatus(distributeOrderNoExisted,"",BankTrapStatus.CREATE,BigDecimal.ZERO);
            respDTO.setCompanyId(creditApply.getCompanyId());
            respDTO.setEmployeeId(creditApply.getEmployeeId());
            respDTO.setBankAccountNo(creditApply.getBankAccountNo());
            respDTO.setBankName(creditApply.getBankName());
            respDTO.setTrapErrorAmount(creditApply.getTrapErrorAmount());
            return respDTO;
        } catch (FinPayException e) {
            FinhubLogger.warn("【分贝通虚拟卡退还企业额度重试异常】retryRefundCredit=applyTransNo：{}", applyTransNo, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.warn("【分贝通虚拟卡退还企业额度重试异常】retryRefundCredit=applyTransNo：{}", applyTransNo, e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public BankCardBatchDistributeRespDTO batchDistributeCredit(BankApplyCreditReqDTO reqDTO) {
        FinhubLogger.info("分贝通虚拟卡批量发放额度参数：{}", JsonUtils.toJson(reqDTO));

        if(CollUtil.isEmpty(reqDTO.getDistributeDataList())){
            FinhubLogger.warn("分贝通虚拟卡批量发放额度参数distributeDataList is null");
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }

        List<BankCardDistributeReqDTO> grantDataList = reqDTO.getDistributeDataList();
        if (grantDataList == null || grantDataList.isEmpty()) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }
        if (grantDataList.size()==1){
            //此时说明仍然是一人一卡的下发，兼容一下表数据
            reqDTO.setBankAccountNo(grantDataList.get(0).getBankAccountNo());
            reqDTO.setBankName(grantDataList.get(0).getBankName());
            reqDTO.setOneFlag(true);
        }
        BigDecimal operationAmount = reqDTO.getOperationAmount();
        BigDecimal sumGrantAmount = grantDataList.stream().map(BankCardDistributeReqDTO::getDistributeAmount).reduce(BigDecimal::add).orElse(new BigDecimal(0));
        if (sumGrantAmount.compareTo(operationAmount) != 0) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }

        //创建申请和下发单
        BankCardCreditAggregateOrderDTO aggregateOrderDTO = createApplyAndDistributeOrderLock(reqDTO);
        BankCardBatchDistributeRespDTO result = new BankCardBatchDistributeRespDTO();
        try{
            List<BankCardCreditDistributeRespDTO> distributeDetailList = new ArrayList<>();
            BankCardCreditApply applyOrder = aggregateOrderDTO.getApplyOrder();
            result.setBizNo(reqDTO.getBizNo());
            result.setApplyTransNo(applyOrder.getApplyTransNo());
            //遍历发放额度
            for (BankCardCreditDistribute distributeOrder : aggregateOrderDTO.getDistributeOrders()) {
                distributeCredit(reqDTO, result, aggregateOrderDTO.getEmployeeContract(), distributeDetailList, applyOrder, distributeOrder);
            }
            result.setDistributeDetailList(distributeDetailList);
            return result;
        } catch (FinhubException e){
            FinhubLogger.warn("额度实际下发金额失败告警,reqDTO={}",JsonUtils.toJson(reqDTO), e);
            return result;
        } catch (Exception e){
            FinhubLogger.error("额度实际下发金额失败异常,reqDTO={}",JsonUtils.toJson(reqDTO), e);
            return result;
        }
    }

    private BankCardCreditAggregateOrderDTO createApplyAndDistributeOrderLock(BankApplyCreditReqDTO reqDTO) {
        String lockKey = "pay:batchyDistributeCredit:"+ reqDTO.getEmployeeId();
        tryLock(lockKey);

        //创建申请和下发单
        try {
            return creditApplyManager.createApplyAndDistributeOrder(reqDTO);
        } catch (FinhubException e){
            FinhubLogger.warn("创建额度下单发生告警,reqDTO={}",JsonUtils.toJson(reqDTO), e);
            throw e;
        } catch (Exception e){
            FinhubLogger.error("创建额度下单发生错误,reqDTO={}",JsonUtils.toJson(reqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(EXCEPTION);
        }finally {
            unLock(lockKey);
        }
    }

    private void distributeCredit(BankApplyCreditReqDTO reqDTO, BankCardBatchDistributeRespDTO result, EmployeeContract employeeContract, List<BankCardCreditDistributeRespDTO> distributeDetailList, BankCardCreditApply applyOrder, BankCardCreditDistribute distributeOrder) {
        distributeCredit(reqDTO, result, employeeContract, distributeDetailList, applyOrder, distributeOrder, true);
    }

    private void distributeCredit(BankApplyCreditReqDTO reqDTO, BankCardBatchDistributeRespDTO result, EmployeeContract employeeContract, List<BankCardCreditDistributeRespDTO> distributeDetailList, BankCardCreditApply applyOrder, BankCardCreditDistribute distributeOrder, Boolean sendMsgFlag) {
        try {
            reqDTO.setDistributeCardInfo(distributeOrder.getBankName(), distributeOrder.getBankAccountNo(), distributeOrder.getDistributeAmount());
            //单卡时需要有返回值
            BankApplyCreditRespDTO bankApplyCredit = distributeCredit(applyOrder, distributeOrder, reqDTO, employeeContract);
            result.setBankApplyCredit(bankApplyCredit);
        } catch (FinhubException e){

            BankCardCreditDistributeRespDTO distributeRespDTO = new BankCardCreditDistributeRespDTO();
            BeanUtils.copyProperties(distributeOrder, distributeRespDTO);
            distributeRespDTO.setFailureReasonCode(e.getCode());
            distributeDetailList.add(distributeRespDTO);

            //循环备用金重试失败，不需要保存所有单据,循环备用金list的lenth=1
            if (reqDTO.getBizNo().startsWith("RBYJ")) {
                creditApplyManager.deleteByApplyTransNo(applyOrder.getApplyTransNo());
                creditDistributeManager.deleteDistributeById(distributeOrder.getId());
            }else {
                FinhubLogger.warn("【分贝通虚拟卡下发额度异常】distributeOrder={}", JsonUtils.toJson(distributeOrder), e);
                distributeOrder.setDistributeStatus(CreditDistributeStatus.FAIL.getCode());
                distributeOrder.setFailureReason(e.getMessage());
                creditDistributeManager.updateDistributeStatus(distributeOrder);

                //更新申请状态为失败
                creditApplyManager.updateApplyOrder(applyOrder.getApplyTransNo(),ApplyCreditStatus.FAIL);
                if (!reqDTO.getBizNo().startsWith("RBYJ") && (e.getCode() == BANK_CARD_ROUND_PETTY_ACCOUNT_NOT_ENOUGH.getCode() || e.getCode() == BANK_CARD_BUSINESS_ACCOUNT_NOT_ENOUGH.getCode())) {
                    if (sendMsgFlag){
                        DecimalFormat decimalFormat = new DecimalFormat("0.00");
                        String formatAmount = decimalFormat.format(com.fenbeitong.common.utils.bigdecimal.BigDecimalUtils.fen2yuan(distributeOrder.getDistributeAmount()));
                        String msg = "由于企业账户余额不足，您有¥" + formatAmount + "虚拟卡额度发放失败，请联系管理员进行充值，余额充足后将为您重新下发额度";
                        BankApplyCreditReqDTO msgDto = BankApplyCreditReqDTO.builder().build();
                        BeanUtils.copyProperties(reqDTO, msgDto);
                        msgDto.setOperationAmount(distributeOrder.getDistributeAmount());
                        msgDto.setPettyId(distributeOrder.getPettyId());
                        openFile(msgDto, ROUND_PETTY_MSG_TEMP_NO_AMOUNT_ID, msg, "虚拟卡额度发放失败");
                    }
                }
            }
        } catch (Exception e) {
            BankCardCreditDistributeRespDTO distributeRespDTO = new BankCardCreditDistributeRespDTO();
            BeanUtils.copyProperties(distributeOrder, distributeRespDTO);
            distributeRespDTO.setFailureReasonCode(EXCEPTION.getCode());
            distributeDetailList.add(distributeRespDTO);

            //循环备用金重试失败，不需要保存所有单据,循环备用金list的lenth=1
            if (reqDTO.getBizNo().startsWith("RBYJ")) {
                FinhubLogger.warn("循环备用金下发失败，applyTransNo={}，bizNo={}", applyOrder.getApplyTransNo(), reqDTO.getBizNo());
                creditApplyManager.deleteByApplyTransNo(applyOrder.getApplyTransNo());
                creditDistributeManager.deleteDistributeById(distributeOrder.getId());
            }else {
                FinhubLogger.error("【分贝通虚拟卡下发额度系统异常】distributeOrder:{}", JsonUtils.toJson(distributeOrder), e);
                distributeOrder.setDistributeStatus(CreditDistributeStatus.FAIL.getCode());
                distributeOrder.setFailureReason(EXCEPTION.getMsg());
                //更新虚拟卡发放单状态 信息流状态 失败
                creditDistributeManager.updateDistributeStatus(distributeOrder);

                //更新申请状态为失败
                creditApplyManager.updateApplyOrder(applyOrder.getApplyTransNo(),ApplyCreditStatus.FAIL);
            }
        } finally {
            //此部分逻辑，理论应该接入消息后，由消息部分统一进行处理（待接入）
            //查询所有的发放单是否都已经是终态，并且都是成功才是成功，如果部分失败
            //循环备用金肯定是用消费的某个卡后，做的循环下发（所以本质属于是一人一卡）
            try{
                List<BankCardCreditDistribute> distributes = creditDistributeManager.queryDistributeByTransNo(applyOrder.getApplyTransNo());
                if (CollectionUtils.isEmpty(distributes)){
                    FinhubLogger.warn("下发单信息不存在，applyTransNo={}", applyOrder.getApplyTransNo());
                    return;
                }
                //不是终态的记录
                List<BankCardCreditDistribute> notFinalCollect = distributes.stream()
                        .filter(p -> (!CreditDistributeStatus.isSuccess(p.getDistributeStatus())&&!CreditDistributeStatus.isDisbledStatus(p.getDistributeStatus())))
                        .collect(Collectors.toList());
                List<BankCardCreditDistribute> disabledCollect = distributes.stream()
                        .filter(p -> CreditDistributeStatus.isDisbledStatus(p.getDistributeStatus()))
                        .collect(Collectors.toList());
                //下发单都已经是终态，并且都是成功的时候，调整申请单为成功，如果存在失败，失败的下发单会做重试逻辑，最终由重试处理
                if (CollectionUtils.isEmpty(notFinalCollect)){
                    //成功
                    if(CollectionUtils.isEmpty(disabledCollect)){
                        creditApplyManager.updateApplyOrder(applyOrder.getApplyTransNo(),ApplyCreditStatus.SUCCESS);
                        //下发预算迁移走（循环备用金预算暂时保留）
                        if (reqDTO.getBizNo().startsWith("RBYJR")) {
                            reqDTO.setOperationAmount(applyOrder.getApplyAmount());
                            creditApplyManager.budgetConsume(reqDTO, applyOrder.getFbCardNo());
                        }
                        return;
                    }
                    //部分成功
                    creditApplyManager.updateApplyOrder(applyOrder.getApplyTransNo(),ApplyCreditStatus.PART_SUCCESS);
                }
            } catch (Exception e){
                FinhubLogger.error("申请单终态处理执行失败", e);
            }
        }
    }

    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BankApplyCreditRespDTO distributeCredit(BankCardCreditApply applyOrder, BankCardCreditDistribute distributeOrder, BankApplyCreditReqDTO reqDTO, EmployeeContract employeeContract) {
        FinhubLogger.info("实际下发参数信息：reqDTO={}, distributeOrder={}, applyOrder.getCreateTime()={}, employeeContract={}", 
                JsonUtils.toJson(reqDTO), JsonUtils.toJson(distributeOrder), applyOrder.getCreateTime(), JsonUtils.toJson(employeeContract));

        //校验是否可以给此卡发放额度
        BankCheckApplyRespDto bankCheckApplyRespDto = checkApply(convertToBankCheckApplyReqDto(distributeOrder));
        if (!bankCheckApplyRespDto.getCanApply()) {
            throw new FinPayException(GlobalResponseCode.BANK_CARD_ACCOUNT_ERROR);
        }

        //虚拟卡冻结池/账户信息流操作
        BankApplyCreditRespDTO result = applyCardManager.applyCredit(reqDTO, distributeOrder, applyOrder.getCreateTime(), employeeContract);

        //更新虚拟卡发放单状态
        distributeOrder.setDistributeStatus(CreditDistributeStatus.SUCCESS.getCode());
        creditDistributeManager.updateDistributeStatus(distributeOrder);

        //成功更新备用金金额
        updatePettyAmount(distributeOrder, reqDTO);

        result.setDistributeOrderNo(distributeOrder.getDistributeOrderNo());
        result.setApplyTransNo(distributeOrder.getApplyTransNo());
        return result;

    }

    private BankCheckApplyReqDto convertToBankCheckApplyReqDto(BankCardCreditDistribute distributeOrder) {
        BankCheckApplyReqDto checkApplyReqDto = new BankCheckApplyReqDto();
        checkApplyReqDto.setCompanyId(distributeOrder.getCompanyId());
        checkApplyReqDto.setEmployeeId(distributeOrder.getEmployeeId());
        checkApplyReqDto.setBankName(distributeOrder.getBankName());
        checkApplyReqDto.setBankAccountNo(distributeOrder.getBankAccountNo());
        return checkApplyReqDto;
    }

    private void updatePettyAmount(BankCardCreditDistribute distributeOrder, BankApplyCreditReqDTO reqDTO) {
        BigDecimal distributeAmount = distributeOrder.getDistributeAmount();
        //--循环下发
        if (StringUtils.isNotBlank(distributeOrder.getPettyId()) && distributeOrder.getBizNo().startsWith("RBYJ")) {
            bankPettyExtMapper.updateRoundAmount(distributeAmount,distributeAmount,distributeAmount, distributeOrder.getPettyId());
        }else {
            boolean isRoundPetty = (ObjUtils.isNotBlank(reqDTO.getPettyType()) && reqDTO.getPettyType()== PettyType.ROUND.getKey());
            BankPetty bankPetty = new BankPetty();
            bankPetty.setBalance(distributeAmount);
            bankPetty.setGrantSum(distributeAmount);
            if (isRoundPetty){
                bankPetty.setRoundRefundSum(distributeAmount);
            }else {
                bankPetty.setRoundRefundSum(BigDecimal.ZERO);
            }
            bankPettyExtMapper.updateRoundAmount(bankPetty.getBalance(),bankPetty.getRoundRefundSum(),bankPetty.getGrantSum(), distributeOrder.getPettyId());
        }
    }

    private void openFile(BankApplyCreditReqDTO reqDTO,String tempId,String msg,String title) {
        BankPetty bankPetty = null;
        if (StringUtils.isNotBlank(reqDTO.getPettyId())){
            Example example = new Example(BankPetty.class);
            example.createCriteria().andEqualTo("pettyId", reqDTO.getPettyId());
            bankPetty = bankPettyMapper.selectOneByExample(example);
        }
        //发送短信
        sendMsg(reqDTO,bankPetty,tempId);
        //发送push
        pushAlert(reqDTO,bankPetty,msg,title,title);
        //消息中心
        sendMsgCenter(reqDTO, bankPetty,msg);
        //发送web消息
        sendWebPush(reqDTO,msg,title);

        FinhubLogger.info("预算不足，短信发送成功 msg = {}", JSON.toJSONString(reqDTO));
    }
    private void sendWebPush(BankApplyCreditReqDTO reqDTO, String msg, String title) {
        try {
            //发送web
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setMsgSubType(BizType.BankIndividualApply.getCode());
            kafkaWebMessageMsg.setBizType(BizType.BankIndividualApply.getCode());
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setCompanyId(reqDTO.getCompanyId());
            kafkaWebMessageMsg.setReceiver(reqDTO.getEmployeeId());
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setSender(reqDTO.getEmployeeId());
            kafkaWebMessageMsg.setTitle(title);
            JSONObject json = new JSONObject();
            json.put("bankName", reqDTO.getBankName());
            json.put("bankAccountNo", reqDTO.getBankAccountNo());
            kafkaWebMessageMsg.setInfo(json.toJSONString());
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);

        } catch (Exception e) {
            FinhubLogger.error("循环备用金push发送失败");
            e.printStackTrace();
        }
    }

    private void sendMsgCenter(BankApplyCreditReqDTO reqDTO,BankPetty bankPetty, String msg) {
        try {

            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(BizType.BankIndividualApply.getCode());
            if (bankPetty != null){
                kafkaSaasMessageMsg.setBizOrder(bankPetty.getBizNo());
            }else {
                kafkaSaasMessageMsg.setBizOrder(reqDTO.getBizNo());
            }
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(reqDTO.getCompanyId());
            kafkaSaasMessageMsg.setReceiver(reqDTO.getEmployeeId());
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(reqDTO.getEmployeeId());
            kafkaSaasMessageMsg.setTitle("虚拟卡额度发放失败");
            FinhubLogger.info("循环备用金消息中心推送参数={}",JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("循环备用金消息中心发送失败！！！");
            e.printStackTrace();
        }
    }
    private void pushAlert(BankApplyCreditReqDTO reqDTO, BankPetty bankPetty,String msg,String title,String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            if (bankPetty != null){
                msgInfo.put("id", bankPetty.getBizNo());
            }else {
                msgInfo.put("id", reqDTO.getBizNo());
            }
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", ApplyType.Petty.getValue());
            msgInfo.put("order_type", String.valueOf(BizType.BankIndividualApply.getCode()));

            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(0+"");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(reqDTO.getEmployeeId());
            kafkaPushMsg.setCompanyId(reqDTO.getCompanyId());
            FinhubLogger.info("循环备用金push消息参数kafkaPushMsg:{}",kafkaPushMsg);
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("发送PUSH失败", e);
        }
    }
    private void sendMsg(BankApplyCreditReqDTO reqDTO, BankPetty bankPetty,String tempId) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            if (bankPetty != null){
                String employeePhone = bankPetty.getEmployeePhone();
                phones.add(employeePhone);
            }else {
                EmployeeBaseInfo employeeBaseInfo =  employeeService.getEmployeeBaseInfo(reqDTO.getCompanyId(),reqDTO.getEmployeeId());
                phones.add(employeeBaseInfo.getPhone());
            }
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", BigDecimalUtils.fenToYuan(reqDTO.getOperationAmount()));
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("预算不足，短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("循环备用金预算不足，短信发送失败！！！");
            e2.printStackTrace();
        }
    }

    @Override
    public BankCardAcctRechargeCreditRespDTO rechargeCardAcctCredit(RechargeCardAcctCreditReqDTO reqDTO) {
        FinhubLogger.info("【虚拟卡：广发银行充值补圈存】参数=={}", JsonUtils.toJson(reqDTO));
        reqDTO.checkReq();
        try {
            return applyCardManager.rechargeCardAcctBalance(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡：广发银行充值补圈存】rechargeCardAcctCredit=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡：广发银行充值补圈存】applyCrrechargeCardAcctCreditedit=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public BankCardAcctRechargeCreditRespDTO rechargeCardAcctCredit4SpaBank(RechargeCardAcctCreditReqDTO reqDTO) {
        FinhubLogger.info("【虚拟卡：平安银行错花还款补圈存】参数=={}", JsonUtils.toJson(reqDTO));
        reqDTO.checkReq();
        try {
            return applyCardManager.rechargeCardAcctBalance4SpaBank(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡：平安银行错花还款补圈存】rechargeCardAcctCredit4SpaBank=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡：平安银行错花还款补圈存】rechargeCardAcctCredit4SpaBank =参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    /**
     * 平安额度退回检测
     * 1. 检测消费金额: 现在检测到可回收金额为 (1w - 当日消费金额)
     * 2. 可回收金额: 1w - 当日消费金额 - 当日已回收金额
     * 3. 如果当日可回收金额为0,card_balance > 0 且 card_acct_balance > 0则拒绝
     * 4. 如果当日可回收金额大于0,则计算当前可以回收金额 且 card_acct_balance > 可回收金额 则拒绝
     * 4. 检测2分钟内是否有额度回收记录,
     */
    public void check4RefundCreditOfSpa(BankCard bankCard,BigDecimal applyAmount){
        boolean isCanRefund = spaRefundCheck(applyAmount,bankCard);
        if (!isCanRefund){
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_APPLY_REFUND_CREDIT_ERROR_NOT_ENOUGH);
        }
        //申请退还的部分
        BigDecimal canRefundOfApplyAmount = calSpaOperationAmount(applyAmount,bankCard);
        
        BigDecimal totalPay = queryTotalAmountPay(bankCard.getEmployeeId());
        BigDecimal b1w = new BigDecimal("1000000");
        BigDecimal canRefundAmount = b1w.subtract(totalPay);
        // 申请金额中的可退还金额比当日平安可退还金额大则拦截
        if (canRefundOfApplyAmount.compareTo(canRefundAmount) > 0){
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_APPLY_REFUND_CREDIT_ERROR_NOT_ENOUGH, "当日可退金额为" + BigDecimalUtils.fenToYuan(canRefundAmount));
        }
    }


    /**
     * 计算额度退回时平安的解圈存金额
     * @param refundApplyAmount 额度退还金额
     */
    public BigDecimal calSpaOperationAmount(BigDecimal refundApplyAmount,BankCard bankCard){
        SpaPersonAcctRespBaseDto<SpaQueryCardBalanceRespDto> spaBankBalanceCheck  = spaBankBalanceCheck(bankCard);
        // 失败
        if (!"000000".equals(spaBankBalanceCheck.getCode())) {
            return refundApplyAmount;
        }
        //银行侧余额
        BigDecimal bankBalance =  spaBankBalanceCheck.getData().getBalance();
        BigDecimal refundAmount = bankCard.getCardAcctBalance().compareTo(refundApplyAmount)>0?refundApplyAmount:bankCard.getCardAcctBalance();
        if (bankBalance.compareTo(refundAmount) >= 0){
            FinhubLogger.info("【平安银行】额度退回金额计算,用户为:{},申请金额:{},卡余额：{},卡实际余额:{},银行实际余额:{},退还金额:{}"
                    ,bankCard.getEmployeeId(),refundApplyAmount,bankCard.getCardBalance(),bankCard.getCardAcctBalance(),bankBalance,refundAmount);
            return refundAmount;
        }else {
            FinhubLogger.error("【平安银行】额度退回金额异常,用户为:{}",bankCard.getEmployeeId());
            return BigDecimal.ZERO;
        }
    }
    /**
     * 平安虚拟卡余额检验方法
     */
    public SpaPersonAcctRespBaseDto<SpaQueryCardBalanceRespDto> spaBankBalanceCheck(BankCard bankCard){

        SpaQueryCardBalanceReqDto spaQueryCardBalanceReqDto = new SpaQueryCardBalanceReqDto();
        spaQueryCardBalanceReqDto.setRealName(bankCard.getEmployeeName());
        spaQueryCardBalanceReqDto.setCertNo(bankCard.getIdEntityCard());
        spaQueryCardBalanceReqDto.setCertType("1");
        spaQueryCardBalanceReqDto.setPhoneNo(bankCard.getEmployeePhone());
        spaQueryCardBalanceReqDto.setBankCardNo(bankCard.getBankAccountNo());
//        spaQueryCardBalanceReqDto.setSpaOpenBusinessNo(apply.getSpaOpenBusinessNo());
        spaQueryCardBalanceReqDto.setIsBankCard(Boolean.TRUE);
        FinhubLogger.info("spaBankBalanceCheck.queryBalance.查询余额: queryCardBalance req {} ", JSONObject.toJSONString(spaQueryCardBalanceReqDto));
        SpaPersonAcctRespBaseDto<SpaQueryCardBalanceRespDto> spaQueryCardBalanceRespDto = iSpaBankPersonAcctService.queryCardBalance(spaQueryCardBalanceReqDto);
        FinhubLogger.info("spaBankBalanceCheck.queryBalance.查询余额: queryCardBalance resp {} ", JSONObject.toJSONString(spaQueryCardBalanceRespDto));
        return spaQueryCardBalanceRespDto;
    }

    public boolean spaRefundCheck(BigDecimal refundApplyAmount,BankCard bankCard){

        SpaPersonAcctRespBaseDto<SpaQueryCardBalanceRespDto> spaBankBalanceCheck  = spaBankBalanceCheck(bankCard);
        // 失败
        if (!"000000".equals(spaBankBalanceCheck.getCode())) {
//            throw new FinhubException(1, spaQueryCardBalanceRespDto.getMessage());
            return true;
        }
        BigDecimal bankBalance =  spaBankBalanceCheck.getData().getBalance();
        //银行余额大于退还余额,拒绝退还,等余额一致了再退
        BigDecimal b1w0 = new BigDecimal("1000000");
        // 允许回收流水有问题
        if (bankCard.getCardBalance().compareTo(refundApplyAmount) > 0
                && refundApplyAmount.compareTo(bankCard.getCardAcctBalance()) > 0
        ){
            return false;
        }
        if (bankBalance.compareTo(refundApplyAmount) > 0 && bankBalance.compareTo(b1w0) > 0 && refundApplyAmount.compareTo(b1w0) > 0){
            return false;
        }
        //银行余额小于退还金额,分情况
        if (bankBalance.compareTo(refundApplyAmount) < 0){
            //1000000
            BigDecimal b1w = new BigDecimal("1000000");
            //退还金额大于1w
            if (refundApplyAmount.compareTo(b1w) > 0){
                //银行余额大于1w 不能退
                if (bankCard.getCardAcctBalance().compareTo(b1w) > 0){
                    return false;
                }
            }
        }
        return true;
    }
    /**
     * 退还额度
     */
    @Override
//    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BankRefundCreditRespDTO refundCredit(BankRefundCreditReqDTO reqDTO) {
       return virtualCardCreditManager.refundCredit(reqDTO);
    }

    /**
     this.setIs_check(setup.getIsChecked());
     this.setApp_notice(setup.getIntVal1());
     this.setMail_notice(setup.getIntVal2());
     this.setPhone_notice(setup.getIntVal3());
     this.setEmployee_refund(Integer.parseInt(setup.getStrVal1()));
     this.setCompany_refund(Integer.parseInt(setup.getStrVal2()));
     this.setSystem_refund(Integer.parseInt(setup.getStrVal3()));
     */

    public void sendNoticeMsg(Integer type , BankCard bankCard , BigDecimal operAmount , String bizNo){
        try {
            String title = "虚拟卡额度退还成功";
            //查询配置
            FinhubLogger.info("额度回收 查询消息配置信息 companyId = {}" ,bankCard.getCompanyId());
            List<MessageSetupVO> messageSetupVOS = iMessageSettingService.queryCompanyMessageSetupWithDefault(bankCard.getCompanyId(),Collections.singletonList(CREDIT_REFUND_BIZ_CODE));
            FinhubLogger.info("额度回收 查询消息配置信息 companyId = {}，res = {}" ,bankCard.getCompanyId() ,messageSetupVOS);
            if (messageSetupVOS != null && messageSetupVOS.size() > 0){
                MessageSetupVO messageSetupVO = messageSetupVOS.get(0);
                if (messageSetupVO.getIsChecked() > 0){
                    if (type == BankApplyCreditType.REFUND_CREDIT.getBankApplyType().getKey() &&  Integer.valueOf(messageSetupVO.getStrVal1()) > 0){
                        sendCompanyNotice(messageSetupVO,type,bankCard,title,operAmount,bizNo);
                    }
                    if (type == BankApplyCreditType.COMPANY_REFUND_CREDIT.getBankApplyType().getKey() && Integer.valueOf(messageSetupVO.getStrVal2()) > 0){
                        sendCompanyNotice(messageSetupVO,type,bankCard,title ,operAmount,bizNo);
                    }
                    if (type == BankApplyCreditType.SYSTEM_REFUND_CREDIT.getBankApplyType().getKey() && Integer.valueOf(messageSetupVO.getStrVal3()) > 0){
                        sendCompanyNotice(messageSetupVO,type,bankCard,title ,operAmount,bizNo);
                    }
                }else {
                    sendPresonNotice(type,bankCard,operAmount,bizNo,title ,true,true,true);
                }
            }
        }catch (Exception e){
            FinhubLogger.warn("额度回收发送通知消息推送异常，employeeId={}, applyAmount={},applyCreditType={}",
                    bankCard.getEmployeeId(), operAmount, type , e );
        }
    }

    private void sendCompanyNotice(MessageSetupVO messageSetupVO ,Integer type , BankCard bankCard ,String title , BigDecimal operAmount , String bizNo){
        List<MessageSetupReceiverVO> messageSetupReceiverVOS = iMessageSettingService.queryMessageReceiverList(bankCard.getCompanyId(),CREDIT_REFUND_BIZ_CODE);
        if (CollectionUtils.isEmpty(messageSetupReceiverVOS)){
            return ;
        }
        List<String> userIdList = messageSetupReceiverVOS.stream().map(MessageSetupReceiverVO::getUserId).collect(Collectors.toList());

        String message = bankCard.getEmployeeName() + BankNameEnum.getBankEnum(bankCard.getBankName()).getName() +"的¥"+ BigDecimalUtils.fenToYuan(operAmount) +"元可用额度";
        if (type == BankApplyCreditType.REFUND_CREDIT.getBankApplyType().getKey())
            message = message + "退还成功";
        if (type == BankApplyCreditType.COMPANY_REFUND_CREDIT.getBankApplyType().getKey())
            message = message + "被企业回收";
        if (type == BankApplyCreditType.SYSTEM_REFUND_CREDIT.getBankApplyType().getKey())
            message = message + "已退还至企业";
        String sendMsg = message;
        if (messageSetupVO.getIntVal1() > 0 && CollectionUtils.isNotEmpty(userIdList) ){//APP通知
            userIdList.stream().forEach(userId ->{
                try {
                    FinhubLogger.info("虚拟卡退还额度成功  发送站内信给配置人员  入参 req = {},msg = {}", userId , sendMsg);
                    sendWebPush4CreditRefund(bankCard.getCompanyId(),userId,bankCard.getBankName(),bankCard.getBankAccountNo(),sendMsg,title);
                    sendMsgCenten4CreditRefund(bankCard.getCompanyId(),userId,bizNo,sendMsg,title);
                    pushAlert4CreditRefund(bankCard.getCompanyId(),userId,bizNo,sendMsg,title,null);
                }catch (Exception e){
                    FinhubLogger.info("虚拟卡额度退还成功,发送站内通知给配置人员失败 userId = {} , message = {}", userId , sendMsg , e);
                }
            });
            //发给自己
            sendPresonNotice(type,bankCard,operAmount,bizNo,title ,true,false,false);
        }
        if (messageSetupVO.getIntVal2() > 0 && CollectionUtils.isNotEmpty(userIdList)  ){//邮件通知
            List<EmployeeOrgUnitDTO> employeeOrgUnitDTOS = employeeService.getEmployeeInfoList(bankCard.getCompanyId(),userIdList);
            Set<String> mailSet = employeeOrgUnitDTOS.stream().map(EmployeeOrgUnitDTO::getEmail).collect(Collectors.toSet());

            FinhubLogger.info("虚拟卡退还额度成功  发送邮件给配置人员  入参 req = {},msg = {}", mailSet , message);
            //发给配置人员
            sendCreditRefundMail(mailSet,message);
            //发给自己
            sendPresonNotice(type,bankCard,operAmount,bizNo,title ,false,false,true);
        }
        if (messageSetupVO.getIntVal3() > 0 && CollectionUtils.isNotEmpty(userIdList)  ){//短信通知
            FinhubLogger.info("虚拟卡退还额度成功  发送短信给配置人员  入参 req = {},msg = {}", userIdList , message);
            List<EmployeeOrgUnitDTO> employeeOrgUnitDTOS = employeeService.getEmployeeInfoList(bankCard.getCompanyId(),userIdList);
            employeeOrgUnitDTOS.stream().forEach(employee ->{
                try {
                    sendMsg4CreditRefund(employee.getPhoneNum(),CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID,sendMsg);
                }catch (Exception e){
                    FinhubLogger.info("虚拟卡额度退还成功,发送短信给配置人员失败 employeeId = {} , message = {}", employee.getEmployeeId() , sendMsg , e);
                }
            });
            //发给自己
            sendPresonNotice(type,bankCard,operAmount,bizNo,title ,false,true,false);
        }
    }

    private void sendPresonNotice(Integer type , BankCard bankCard , BigDecimal operAmount , String bizNo , String title ,
                                  Boolean appWebFlag , Boolean smsFlag ,Boolean emailFlag){
        // 没有开启通知  只发消息给本人即可
        String message = "您"+ BankNameEnum.getBankEnum(bankCard.getBankName()).getName() +"的¥"+ BigDecimalUtils.fenToYuan(operAmount) +"元可用额度";
        if (type == BankApplyCreditType.REFUND_CREDIT.getBankApplyType().getKey())
            message = message + "退还成功";
        if (type == BankApplyCreditType.COMPANY_REFUND_CREDIT.getBankApplyType().getKey())
            message = message + "被企业回收";
        if (type == BankApplyCreditType.SYSTEM_REFUND_CREDIT.getBankApplyType().getKey())
            message = message + "已退还至企业";
        if (appWebFlag){
            sendWebPush4CreditRefund(bankCard.getCompanyId(),bankCard.getEmployeeId(),bankCard.getBankName(),bankCard.getBankAccountNo(),message,title);
            sendMsgCenten4CreditRefund(bankCard.getCompanyId(),bankCard.getEmployeeId(),bizNo,message,title);
            pushAlert4CreditRefund(bankCard.getCompanyId(),bankCard.getEmployeeId(),bizNo,message,title,null);
        }
        EmployeeOrgUnitDTO employeeOrgUnitDTO =  employeeService.getEmployeeInfo(bankCard.getCompanyId(),bankCard.getEmployeeId());
        if (emailFlag){
            FinhubLogger.info("虚拟卡额度退还 发送邮件给本人信息 employeeId = {}, email = {}", bankCard.getEmployeeId() , employeeOrgUnitDTO.getEmail());
            if (StringUtils.isNotBlank(employeeOrgUnitDTO.getEmail())){
                sendCreditRefundMail(Collections.singleton(employeeOrgUnitDTO.getEmail()),message);
            }
        }
        if (smsFlag){
            sendMsg4CreditRefund(employeeOrgUnitDTO.getPhoneNum(),CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID,message);
        }
    }

    private void sendMsg4CreditRefund(String phone,String tempId ,String message) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            phones.add(phone);
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", message);
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("虚拟卡额度退还成功,短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("虚拟卡额度退还，短信发送失败！！！");
            e2.printStackTrace();
        }
    }

    private void sendWebPush4CreditRefund(String companyId , String employeeId ,String bankName , String bankAccountNo, String msg, String title) {
        try {
            //发送web
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setMsgSubType(BizType.BankCreditRefund.getCode());
            kafkaWebMessageMsg.setBizType(BizType.BankCreditRefund.getCode());
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setCompanyId(companyId);
            kafkaWebMessageMsg.setReceiver(employeeId);
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setSender(employeeId);
            kafkaWebMessageMsg.setTitle(title);
            JSONObject json = new JSONObject();
            json.put("bankName", bankName);
            json.put("bankAccountNo",bankAccountNo);
            kafkaWebMessageMsg.setInfo(json.toJSONString());
            FinhubLogger.info("虚拟卡额度退还成功推送WebPush 参数={}",JsonUtils.toJson(kafkaWebMessageMsg));
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("虚拟卡额度退还成功消息发送失败");
            e.printStackTrace();
        }
    }

    private void sendMsgCenten4CreditRefund(String companyId , String employeeId , String bizNo , String msg , String title) {
        try {
            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(BizType.BankCreditRefund.getCode());
            kafkaSaasMessageMsg.setBizOrder(bizNo);
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(companyId);
            kafkaSaasMessageMsg.setReceiver(employeeId);
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(employeeId);
            kafkaSaasMessageMsg.setTitle(title);
            FinhubLogger.info("虚拟卡额度退还成功推送 MsgCenten 参数={}",JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("虚拟卡额度退还成功消息中心发送失败！！！");
            e.printStackTrace();
        }
    }

    private void pushAlert4CreditRefund(String companyId , String employeeId , String bizNo,String msg,String title,String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", ApplyType.BankIndividual.getValue());
            msgInfo.put("order_type", String.valueOf(BizType.BankCreditRefund.getCode()));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(0+"");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("额度退还消息通知push消息参数kafkaPushMsg:{}",kafkaPushMsg);
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("发送PUSH失败", e);
        }
    }

    public void sendCreditRefundMail(Set<String> emailSet , String msg) {
        try {
            EmailContract emailContract = new EmailContract();
            Set<String> checkList = new HashSet<>();
            emailSet.stream().forEach(email ->{
                if (EmailCheckUtils.emailFormat(email)){
                    checkList.add(email);
                }
            });
            if (CollectionUtils.isEmpty(checkList)){
                return ;
            }
            FinhubLogger.info("sendCreditRefundMail req = {},msg = {}", emailSet , msg);
            // 收件人
            emailContract.setToList(checkList);
            // 邮件标题
            String subject = "【分贝通】额度回收成功";
            emailContract.setSubject(subject);
            StringBuffer text = new StringBuffer();
            text.append("您好").append("\n");
            text.append(msg);
            emailContract.setText(text.toString());
            // 发送邮件
            NoticeUtils.sendEmail(emailContract);
        }catch (Exception e) {
            FinhubLogger.error("发送邮件失败 email = {},msg = {}",emailSet,msg , e);
        }
    }

    /**
     * 额度回收消息
     * @param reqDTO
     */
    private void pushRefundCreditMsg(BankRefundCreditReqDTO reqDTO, String bankName, String bankAccountNo, BigDecimal amount) {
        FinhubLogger.info("额度回收消息推送start，employeeId={}, applyAmount={},applyCreditType={}",reqDTO.getEmployeeId(), reqDTO.getApplyAmount(), reqDTO.getApplyCreditType());
        CompletableFuture.runAsync(()->{
            KafkaCardRefundCreditMsg refundCreditMsg = new KafkaCardRefundCreditMsg();
            try {
                refundCreditMsg.setBankName(bankName);
                refundCreditMsg.setBankAccountNo(bankAccountNo);
                refundCreditMsg.setRefundCreditType(reqDTO.getApplyCreditType());
                refundCreditMsg.setCompanyId(reqDTO.getCompanyId());
                EmployeeContract employeeContract = employeeService.queryEmployeeInfo(reqDTO.getEmployeeId(), reqDTO.getCompanyId());
                if (java.util.Objects.isNull(employeeContract)){
                    throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_ACCOUNT_INFO_ERROR);
                }
                refundCreditMsg.setEmployeeId(reqDTO.getEmployeeId());
                refundCreditMsg.setEmployeeName(employeeContract.getName());
                refundCreditMsg.setRefundTotalAmount(amount);
                fillApplyInfos(reqDTO, amount, refundCreditMsg);
                //境内虚拟卡
                refundCreditMsg.setCardType(1);
                refundCreditMsg.setOperateTime(DateUtils.getUnixTimestamp());

                kafkaProducerPublisher.publish(refundCreditMsg);

                FinhubLogger.info("额度回收消息推送end，employeeId={}, msg={}",reqDTO.getEmployeeId(), JsonUtils.toJson(refundCreditMsg));

            } catch (Exception e){
                FinhubLogger.warn("额度回收消息推送异常，employeeId={}, applyAmount={},applyCreditType={}，refundCreditMsg={}",
                        reqDTO.getEmployeeId(), reqDTO.getApplyAmount(), reqDTO.getApplyCreditType(), JsonUtils.toJson(refundCreditMsg));
            }
        });
    }

    private void fillApplyInfos(BankRefundCreditReqDTO reqDTO, BigDecimal amount, KafkaCardRefundCreditMsg refundCreditMsg) {
        List<KafkaCardRefundCreditMsg.ApplyInfo> applyInfos = new ArrayList<>();
        //备用金模式，申请单信息是备用金关联的
        if (StringUtils.isNotBlank(reqDTO.getPettyId())){
            BankPetty bankPetty = bankPettyManager.queryBankPettyPettyId(reqDTO.getPettyId());
            KafkaCardRefundCreditMsg.ApplyInfo applyInfo = new KafkaCardRefundCreditMsg.ApplyInfo();
            applyInfo.setApplyId(bankPetty.getBizNo());
            applyInfo.setRefundAmount(amount);
            applyInfos.add(applyInfo);
        } else {
            //普通模式，需要关联申请单的情况，需要客户端传过来
            List<BankRefundSelectedCreditApplyReqDTO> creditApplyList = reqDTO.getSelectedCreditApplyList();
            if (CollectionUtils.isNotEmpty(creditApplyList)){
                creditApplyList.forEach(p -> {
                    KafkaCardRefundCreditMsg.ApplyInfo applyInfo = new KafkaCardRefundCreditMsg.ApplyInfo();
                    BankCardCreditApply creditApply = creditApplyManager.queryByapplyTransNo(p.getApplyId());
                    if (ObjUtils.isNotEmpty(creditApply)){
                        applyInfo.setApplyId(creditApply.getBizNo());
                        applyInfo.setRefundAmount(p.getUseBalance());
                        applyInfos.add(applyInfo);
                    }else{
                        FinhubLogger.warn("交易单号查不到对应的申请单，applyTransNo={}", p.getApplyId());
                    }
                });
            }
        }
        refundCreditMsg.setApplyInfos(applyInfos);
    }
    /**
     * 备用金退还额度
     */
    @Override
    public BankBatchRefundCreditRespDTO refundCreditByPetty(BankRefundCreditReqDTO reqDTO) {
        return virtualCardCreditManager.refundCreditByPetty(reqDTO);
//        FinhubLogger.info("员工退还备用金额度==参数{}", JsonUtils.toJson(reqDTO));
//        ValidateUtils.validate(reqDTO);
//        if (StringUtils.isBlank(reqDTO.getPettyId()) || BigDecimalUtils.hasNoPrice(reqDTO.getApplyAmount())){
//            FinhubLogger.warn("【员工退还备用金额度参数异常】pettyId={},applyAmount={}", reqDTO.getPettyId(), reqDTO.getApplyAmount());
//            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.ILLEGAL_ARGUMENT);
//        }
//
//        //获取备用金信息
//        BankPetty bankPetty = bankPettyManager.queryBankPettyPettyId(reqDTO.getPettyId());
//        if (ObjUtils.isNull(bankPetty)){
//            FinhubLogger.warn("【员工退还备用金额度参数异常】备用金信息不存在，pettyId={}", reqDTO.getPettyId());
//            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_PETTY_NO_EXIST);
//        }
//        if (BigDecimalUtils.hasNoPrice(bankPetty.getBalance())){
//            FinhubLogger.warn("【员工退还备用金额度参数异常】备用金金额信息错误，pettyId={}，balance={}", reqDTO.getPettyId(), bankPetty.getBalance());
//            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_UNBIND_PETTYID_ERROR);
//        }
//
//        //获取备用金关联卡信息
//        List<BankCard> bankCards = bankCardMapper.queryBankCardByPettyId(reqDTO.getCompanyId(), reqDTO.getEmployeeId(), reqDTO.getPettyId(), BigDecimal.ZERO);
//        if (CollectionUtils.isEmpty(bankCards)){
//            FinhubLogger.error("【员工退还备用金额度参数异常】备用金对应的卡金额大于0的不存在，pettyId={}", reqDTO.getPettyId());
//            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_AND_PETTY_PRICE_ERROR);
//        }
//
//        //备用金余额和多卡余额是否一致校验
//        BigDecimal totalCardBalance = bankCards.stream().map(BankCard::getCardCompanyBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
//        if (BigDecimalUtils.differentPrice(reqDTO.getApplyAmount(), totalCardBalance)) {
//            throw new FinPayException(GlobalResponseCode.BANK_CARD_AND_PETTY_PRICE_ERROR);
//        }
//        BankSpaAcctLimit bankSpaAcctLimit = bankSpaBankAcctLimitService.getBankAcctLimits(bankCards.get(0).getBankAcctId());
//        if (bankSpaAcctLimit != null ){
//            BigDecimal yearTotalOut = reqDTO.getApplyAmount().add(bankSpaAcctLimit.getLimitOutYearAmount());
//            BigDecimal yearLimit = new BigDecimal("********");
//            if (yearTotalOut.compareTo(yearLimit) > 0){
//                throw new FinPayException(GlobalResponseCode.BANK_CARD_APPLY_REFUND_LIMIT_ERROR);
//            }
//        }
//
//
//
//        BankBatchRefundCreditRespDTO respDTO = new BankBatchRefundCreditRespDTO();
//        Map<String,BankCardCreditDistribute> cardCreditDistributes = new HashMap<>();
//
//        creditApplyManager.createApplyAndDistribute(reqDTO, bankCards, respDTO, cardCreditDistributes);
//
//        List<BankRefundCreditDistributeRespDTO> distributeRespDTOList = new ArrayList<>();
//        try {
//            //卡和账户金额变更处理
//            bankCards.forEach(bankCard -> {
//                BankCardCreditDistribute creditDistribute = cardCreditDistributes.get(bankCard.getFbCardNo());
//                if (BankNameEnum.isSpa(bankCard.getBankName())){
//                    check4RefundCreditOfSpa(bankCard,creditDistribute.getDistributeAmount());
//                }
//                FinhubLogger.info("备用金额度回收，卡信息fbCardNo={}，creditDistribute={}", bankCard.getFbCardNo(),JsonUtils.toJson(creditDistribute));
//                BankRefundCreditDistributeRespDTO bankRefundCreditRespDTO = applyCardManager.applyRefundCreditPetty(reqDTO, creditDistribute, bankCard);
//                FinhubLogger.info("备用金额度回收，卡信息fbCardNo={},额度回收成功", bankCard.getFbCardNo());
//                distributeRespDTOList.add(bankRefundCreditRespDTO);
//                updateApplyOrderStatus(creditDistribute);
//                //给三方推送额度回收消息
//                pushRefundCreditMsg(reqDTO, bankCard.getBankName(), bankCard.getBankAccountNo(), bankCard.getCardCompanyBalance());
//            });
//            respDTO.setDistributeRespDTOList(distributeRespDTOList);
//            return respDTO;
//        } catch (FinPayException e) {
//            FinhubLogger.warn("【员工退还备用金app额度异常】refundCredit=参数：{}", JsonUtils.toJson(reqDTO), e);
//            throw new FinhubException(e.getCode(), e.getType(), e.getMessage(), e.getTitle());
//        } catch (ValidateException e) {
//            FinhubLogger.warn("【员工退还备用金额度验证异常】refundCredit=参数：{}", JsonUtils.toJson(reqDTO), e);
//            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
//        } catch (Exception e) {
//            FinhubLogger.error("【员工退还备用金额度系统异常】refundCredit=参数：{}", JsonUtils.toJson(reqDTO), e);
//            throw new FinhubException(EXCEPTION.getCode(),EXCEPTION.getType(), EXCEPTION.getMsg());
//        }
    }

    private void updateApplyOrderStatus(BankCardCreditDistribute creditDistribute){
        //更新审批单
        try{
            List<BankCardCreditDistribute> distributes = creditDistributeManager.queryDistributeByTransNo(creditDistribute.getApplyTransNo());
            if (CollectionUtils.isEmpty(distributes)){
                FinhubLogger.warn("下发单信息不存在，applyTransNo={}", creditDistribute.getApplyTransNo());
                return;
            }
            //不是终态的记录
            List<BankCardCreditDistribute> notFinalCollect = distributes.stream()
                    .filter(p -> (!CreditDistributeStatus.isSuccess(p.getDistributeStatus())&&!CreditDistributeStatus.isDisbledStatus(p.getDistributeStatus())))
                    .collect(Collectors.toList());
            List<BankCardCreditDistribute> successCollect = distributes.stream()
                    .filter(p -> CreditDistributeStatus.isSuccess(p.getDistributeStatus()))
                    .collect(Collectors.toList());
            List<BankCardCreditDistribute> disabledCollect = distributes.stream()
                    .filter(p -> CreditDistributeStatus.isDisbledStatus(p.getDistributeStatus()))
                    .collect(Collectors.toList());
            //下发单都已经是终态，并且都是成功的时候，调整申请单为成功，如果存在失败，失败的下发单会做重试逻辑，最终由重试处理
            if (CollectionUtils.isEmpty(notFinalCollect)){
                //失败
                if (CollectionUtils.isEmpty(successCollect)){
                    creditApplyManager.updateApplyOrder(creditDistribute.getApplyTransNo(),ApplyCreditStatus.FAIL);
                    return;
                }

                //成功
                if(CollectionUtils.isEmpty(disabledCollect)){
                    creditApplyManager.updateApplyOrder(creditDistribute.getApplyTransNo(),ApplyCreditStatus.SUCCESS);
                    return;
                }

                //部分成功
                creditApplyManager.updateApplyOrder(creditDistribute.getApplyTransNo(),ApplyCreditStatus.PART_SUCCESS);
            }
        } catch (Exception e){
            FinhubLogger.error("申请单终态处理执行失败", e);
        }

    }

    @Override
    public BankCardAcctWithdrawalCreditRespDTO withdrawalCardAcctCredit(BankCardAcctWithdrawalCreditReqDTO reqDTO) {
        FinhubLogger.info("【虚拟卡：广发银行提现解圈存】参数=={}", JsonUtils.toJson(reqDTO));
        reqDTO.checkReq();
        try {
            return applyCardManager.withdrawalCardAcctBalance(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【虚拟卡：广发银行提现解圈存】withdrawalCardAcctCredit=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡：广发银行提现解圈存】withdrawalCardAcctCredit=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void cutPettyCardModel(BankCardCutPettyModelReqDTO bankCardCutPettyModelReqDTO) {
        FinhubLogger.info("【切换虚拟卡备用金模式】==参数{}", JsonUtils.toJson(bankCardCutPettyModelReqDTO));
        try {
            ValidateUtils.validate(bankCardCutPettyModelReqDTO);
            applyCardManager.cutPettyCardModelType(bankCardCutPettyModelReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【切换虚拟卡备用金模式异常】disableBankAccount=参数：{}", JsonUtils.toJson(bankCardCutPettyModelReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【切换虚拟卡备用金模式验证异常】disableBankAccount=参数：{}", JsonUtils.toJson(bankCardCutPettyModelReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【切换虚拟卡备用金模式系统异常】disableBankAccount=参数：{}", JsonUtils.toJson(bankCardCutPettyModelReqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }

    }

    @Override
    public void cutEmployeeNormalCardModel(BankCardCutNormalModelReqDTO bankCardCutNormalModelReqDTO) {
        FinhubLogger.info("【切换虚拟卡普通模式】==参数{}", JsonUtils.toJson(bankCardCutNormalModelReqDTO));
        try {
            ValidateUtils.validate(bankCardCutNormalModelReqDTO);
            BankPetty bankPetty = bankPettyManager.queryBankPettyByEmployeeId(bankCardCutNormalModelReqDTO.getCompanyId(), bankCardCutNormalModelReqDTO.getEmployeeId());
            if (ObjUtils.isNotEmpty(bankPetty)){
                FinhubLogger.error("切换虚拟卡普通模式,备用金还需要还款{}",JsonUtils.toJson(bankPetty));
                throw new FinPayException(GlobalResponseCode.BANK_CARD_CUT_NORMAL_AMOUNT_MODEL_ERROR);
            }
            applyCardManager.cutNormalCardModelType(bankCardCutNormalModelReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【切换虚拟卡普通模式异常】disableBankAccount=参数：{}", JsonUtils.toJson(bankCardCutNormalModelReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【切换虚拟卡普通模式验证异常】disableBankAccount=参数：{}", JsonUtils.toJson(bankCardCutNormalModelReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【切换虚拟卡普通模式系统异常】disableBankAccount=参数：{}", JsonUtils.toJson(bankCardCutNormalModelReqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public BankDisableAccountRespDTO disableBankCardAccount(BankDisableBankCardReqDTO reqDTO) {
        FinhubLogger.info("企业操作禁用个人银行卡==参数{}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            return applyCardManager.disableBankCardAccount(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【企业操作禁用个人银行卡异常】disableBankAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【企业操作禁用个人银行卡验证异常】disableBankAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【企业操作禁用个人银行卡系统异常】disableBankAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public BankEnableAccountRespDTO enableBankCardAccount(BankEnableAccountReqDTO reqDTO) {
        FinhubLogger.info("企业操作启用个人银行卡==参数{}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            return applyCardManager.enableBankCardAccount(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【企业操作启用个人银行卡异常】disableBankAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【企业操作启用个人银行卡验证异常】disableBankAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【企业操作启用个人银行卡系统异常】disableBankAccount=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public void cutCompanyNormalCardModel(CompanyCutNormalModelReqDTO reqDTO) {
        FinhubLogger.info("【公司备用金模式切为普通模式】==参数{}", JsonUtils.toJson(reqDTO));
        throw new FinhubException(EXCEPTION.getCode(),
                EXCEPTION.getType(), EXCEPTION.getMsg());
    }

    @Override
    public void refundCreditAccount(BankApplyCreditReqDTO reqDTO) {

        try {
            //额度直接退到商务账户
            BankRefundCreditReqDTO bankRefundCreditReqDTO = BankRefundCreditReqDTO.builder()
                    .bankAccountNo(reqDTO.getBankAccountNo())
                    .applyAmount(reqDTO.getOperationAmount())
                    //.distributeOrderNo()（此接口如果要用需要这两个赋值才行）
                    //.transApplyNo()
                    .bizNo(reqDTO.getBizNo())
                    .companyId(reqDTO.getCompanyId())
                    .companyName(reqDTO.getCompanyName())
                    .employeeId(reqDTO.getEmployeeId())
                    .operationChannel(reqDTO.getOperationChannel())
                    .operationDescription(reqDTO.getOperationDescription())
                    .operationUserId(reqDTO.getOperationUserId())
                    .operationUserName(reqDTO.getOperationUserName())
                    .bankName(reqDTO.getBankName())
                    .bankApplyCreditType(reqDTO.getBankApplyCreditType())
                    .pettyId(reqDTO.getPettyId())
                    .accountSubId(reqDTO.getAccountSubId())
                    .accountModel(reqDTO.getAccountModel())
                    .build();
            applyCardManager.refundCreditAccount(bankRefundCreditReqDTO);
            //保存备用金上还款退回记录
            if(ObjUtils.isNull(reqDTO.getPettyId())){
                bankPettyExtMapper.updateCardRepayment(BigDecimal.ZERO,BigDecimal.ZERO,reqDTO.getOperationAmount(),reqDTO.getPettyId());
            }
            //虚拟卡 备用金上 增加还款金额
            bankCardMapper.addBankRepaymentSumByCardNo(reqDTO.getBankAccountNo(),reqDTO.getCardRepaymentSum(),reqDTO.getCardRepaymentRefundSum());
        }catch (FinPayException e){
            FinhubLogger.error("refundCreditAccount error param:{}",JsonUtils.toJson(reqDTO),e);
        }
    }


    /**
     * 还款-员工错花
     * @param reqDTO
     * @return
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BankRefundTradeRespDTO repaymentRefund(BankRefundTradeReqDTO reqDTO) {

        FinhubLogger.info("分贝通虚拟卡错花还款参数=={}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            reqDTO.setBankApplyCreditType(BankApplyCreditType.CARD_REPAYMENT.getKey());
            //根据用户查询： 担心员工离职
            BankCard bankCard = searchCardManager.queryByBankNameAndAccountNo(reqDTO.getBankName(), reqDTO.getBankAccountNo());
            //验证是否可以错花还款
            BankCardTradeFlow cardTradeFlow = tradeCardManager.checkRefund(reqDTO);
            //确认退款类型
            RefundOrderTypeDto refundOrderTypeDto = queryRefundType(reqDTO, bankCard, cardTradeFlow);
            //还款 更新备用金还款金额
            if (StringUtils.isNotBlank(reqDTO.getPettyId())) {
                bankPettyExtMapper.updateCardRepayment(BigDecimal.ZERO,BigDecimal.ZERO,reqDTO.getOperationAmount(),reqDTO.getPettyId());
            }
            // 正常还款 增加备用金额度; 备用金切换/企业切模式 备用金额度不增加
            if(!refundOrderTypeDto.applyRefund() && StringUtils.isNotBlank(reqDTO.getPettyId())){
                bankPettyExtMapper.updateCardRepayment(reqDTO.getOperationAmount(),BigDecimal.ZERO,BigDecimal.ZERO,reqDTO.getPettyId());
            }
            //操作退款
            refundEmployeeTrade(reqDTO, bankCard, refundOrderTypeDto,cardTradeFlow);
            //虚拟卡上更新还款金额
            bankCardMapper.addBankRepaymentSumByCardNo(reqDTO.getBankAccountNo(),reqDTO.getOperationAmount(),BigDecimal.ZERO);
            //正常错花还款
            if (!refundOrderTypeDto.applyRefund()) {
                return BankRefundTradeRespDTO.of(bankCard.getCardBalance().add(reqDTO.getOperationAmount()), Boolean.TRUE);
            }else {
                //1不是相同企业。2.解绑状态 和 3.分贝通虚拟卡已经禁用是低版本套餐
                return BankRefundTradeRespDTO.of(executeRefundCredit(reqDTO, bankCard, refundOrderTypeDto), Boolean.FALSE);
            }
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝通虚拟卡还款异常】repaymentRefund 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝通虚拟卡还款验证异常】repaymentRefund 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),  GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝通虚拟卡还款系统异常】repaymentRefund 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    public BankRefundTradeRespDTO refundEmployeeTrade(BankRefundTradeReqDTO reqDTO, BankCard bankCard, RefundOrderTypeDto
            refundOrderTypeDto,BankCardTradeFlow cardTradeFlow) {
        FinhubLogger.info("错花还款消费退款操作:金额退回虚拟卡{}=={},{}", JsonUtils.toJson(reqDTO),JsonUtils.toJson(bankCard),refundOrderTypeDto.toString());
        //如果卡：解绑||退款不同公司。退款不用加到卡上
        //理论上不会出现离职后还能还款的情况，所以这种场景目前不考虑
        if (!refundOrderTypeDto.noCompanyBankCard()) {
            //增加卡余额
            refundBankCardByWrongPaid(bankCard.getId(), reqDTO.getOperationAmount(),cardTradeFlow.getTradeFundType());
        }
        //消费时资金为个人资金时
        if (!VirtualCardTradeFundType.isPersonalFund(cardTradeFlow.getTradeFundType())) {
            //操作退款
            iAcctCompanyCardService.refundFreezeBudget(AccountSubConver.toRefundFreezenReq(reqDTO, FundPlatformEnum.findPlatformByKey(bankCard.getFundPlatform()).getCode()));
        }
        BankCardTradeType bankCardTradeType = reqDTO.getType(refundOrderTypeDto.getDitCompany(), refundOrderTypeDto.getUnbindBankCard(), refundOrderTypeDto.getOffBankCard(), refundOrderTypeDto.getIsCutAcctModel(),refundOrderTypeDto.getRetPetty(),refundOrderTypeDto.getRetNormal());

        //保存交易流水
        BankCardTradeFlow bankCardTradeFlow = BankCardTradeFlowConver.toRefundBankCardTradeFlow(reqDTO, bankCard,bankCardTradeType,cardTradeFlow);
        saveBankCardTradeFlow(bankCardTradeFlow, GlobalResponseCode.BANK_CARD_REFUND_ERROR);

        //金额变动虚拟卡的消费流水
        saveBankCardApplyFlow(BankCardApplyFlowConver.addBankCardPaymentRefundFlow(bankCard, reqDTO, bankCardTradeType.getValue(),cardTradeFlow),GlobalResponseCode.BANK_CARD_SAVE_APPLY_FLOW);
        return BankCardTradeFlowConver.toBankRefundTradeRespDTO(bankCardTradeFlow);

    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public Boolean repaymentRefundByRefund(BankRefundCreditReqDTO reqDTO) {

        try {
            FinhubLogger.info("repaymentRefundByRefund param :{}",JsonUtils.toJson(reqDTO));
            BankCard bankCard = queryCardExistByCompanyEmployee(reqDTO.getBankAccountNo());
            if (!checkNotSameCompany(reqDTO.getEmployeeId(), reqDTO.getCompanyId()) && !BankCardStatus.isUnbind(bankCard.getCardStatus())) {
                //卡上减额度
                bankCardMapper.updateBankRepaymentSumByCardNo(reqDTO.getBankAccountNo(), reqDTO.getApplyAmount(), BigDecimal.ZERO, reqDTO.getCardRepaymentRefundSum());
            }

            if (BankNameEnum.isCgb(bankCard.getBankName()) && FundAcctDirectAcctTypeEnum.isBank(bankCard.getDirectAcctType())){
                //广发直联错花还款退回,把资金添加到个人资金
                bankCardMapper.refundBankCardByWrongPaidRefund(bankCard.getId(), reqDTO.getApplyAmount(), reqDTO.getApplyAmount());
            }
            //消费解冻
            iAcctCompanyCardService.consumeFreezeBudget(AccountSubConver.toRefundFreeze(reqDTO, bankCard));
            //分贝通虚拟卡保存一条操作的退还记录
            BankCardApplyFlow bankCardApplyFlow = BankCardApplyFlowConver.refundCreditSubAccountReq(reqDTO, bankCard);
            bankCardApplyFlow.setOperationType(BankApplyType.CARD_REPAYMENT_REFUND.getKey());
            if (checkNotSameCompany(reqDTO.getEmployeeId(), reqDTO.getCompanyId()) || BankCardStatus.isUnbind(bankCard.getCardStatus())) {
                bankCardApplyFlow.setEmployeeId(reqDTO.getEmployeeId());
                bankCardApplyFlow.setEmployeeName(reqDTO.getOperationUserName());
                bankCardApplyFlow.setCurrentAmount(reqDTO.getApplyAmount());
                bankCardApplyFlow.setBalance(BigDecimal.ZERO);
            } else {
                bankCardApplyFlow.setCurrentAmount(bankCard.getCardBalance());
                bankCardApplyFlow.setBalance(bankCard.getCardBalance().subtract(reqDTO.getApplyAmount()));
            }
            if (BankNameEnum.isCgb(bankCard.getBankName())) {
                bankCardApplyFlow.setCurrentCardAcctAmount(bankCard.getCardAcctBalance());
                bankCardApplyFlow.setOperationCardAcctAmount(BigDecimal.ZERO);
                bankCardApplyFlow.setCardAcctBalance(bankCard.getCardAcctBalance());
            }
            //资金类型设置
            if (BankNameEnum.isCgb(bankCard.getBankName()) && FundAcctDirectAcctTypeEnum.isBank(bankCard.getDirectAcctType())){
                bankCardApplyFlow.setTradeFundType(VirtualCardTradeFundType.PERSONAL.getType());
                //广发直联实际操作金额为0
                bankCardApplyFlow.setCurrentAmount(bankCard.getCardBalance());
                bankCardApplyFlow.setOperationAmount(BigDecimal.ZERO);
                bankCardApplyFlow.setBalance(bankCard.getCardBalance());
                bankCardApplyFlow.setOperationCompanyAmount(reqDTO.getApplyAmount());
                bankCardApplyFlow.setCurrentCompanyAmount(bankCard.getCardCompanyBalance());
                bankCardApplyFlow.setCompanyBalance(bankCard.getCardCompanyBalance().subtract(reqDTO.getApplyAmount()));
            }else {
                bankCardApplyFlow.setTradeFundType(VirtualCardTradeFundType.COMPANY.getType());
                bankCardApplyFlow.setOperationCompanyAmount(reqDTO.getApplyAmount());
                bankCardApplyFlow.setCurrentCompanyAmount(bankCard.getCardCompanyBalance());
                bankCardApplyFlow.setCompanyBalance(bankCard.getCardCompanyBalance().subtract(reqDTO.getApplyAmount()));
            }

            saveBankCardApplyFlow(bankCardApplyFlow,GlobalResponseCode.BANK_CARD_ACCOUNT_FLOW_ERROR);

            if(!ObjUtils.isNull(reqDTO.getPettyId())){
                //备用金 增加还款退回记录,扣减备用金余额
                bankPettyExtMapper.updateCardRepayment(reqDTO.getCardRepaymentRefundSum().negate(),reqDTO.getCardRepaymentRefundSum(),BigDecimal.ZERO,reqDTO.getPettyId());
            }
            return true;
        }catch (Exception e){
            FinhubLogger.error("repaymentRefundByRefund error:{}",reqDTO,e);
            return false;
        }
    }

    @Override
    public void bankLogout(BankCardLogoutReqDTO reqDTO) {

        try {
            ValidateUtils.validate(reqDTO);
            applyCardManager.bankLogout(reqDTO);
        } catch (FinPayException e) {
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public BankCheckApplyRespDto checkApply(BankCheckApplyReqDto reqDto) {

        try {
            ValidateUtils.validate(reqDto);
            return applyCardManager.checkApply(reqDto);
        } catch (FinPayException e) {
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            throw new FinhubException(EXCEPTION.getCode(),
                    EXCEPTION.getType(), EXCEPTION.getMsg());
        }

    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public Boolean removeCreateInfo(String bankAccountNo) {
        try {
            BankCard bankCard = queryCardExistByCompanyEmployee(bankAccountNo);
            removeBankCardCreateInfo(bankCard.getBankAccountNo(),bankCard.getEmployeeId());
            removeBankCardApplyFlow(bankCard.getBankAccountNo(),bankCard.getEmployeeId());
            removeBankCardTradeFlow(bankCard.getBankAccountNo(),bankCard.getEmployeeId());
            return true;
        }catch (Exception e){
            FinhubLogger.error("removeCreateInfo error bankAccountNo:{}",bankAccountNo);
        }
        return false;
    }

    @Override
    public Boolean updateBankCardInfo(List<BankCardInfoReqDto> reqDtoList) {

        for (BankCardInfoReqDto reqDto : reqDtoList){
            Example example = new Example(BankCard.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("employeeId", reqDto.getEmployeeId());
            criteria.andEqualTo("bankUid", reqDto.getBankUid());
            criteria.andEqualTo("bankAccountNo",reqDto.getBankAccountNo());
            BankCard bankCard = new BankCard();
            bankCard.setBindAcctNo(reqDto.getBindAcctNo());
            bankCardMapper.updateByExampleSelective(bankCard,example);
        }
        return true;
    }

    /**
     * @Description: 公司id+员工id+银行卡号+银行名
     * @Param: [companyId, employeeId, bankAccountNo]
     * @return: BankCard
     */
    public BankCard queryCardExistByCompanyEmployee(String companyId, String employeeId, String bankName, String bankAccountNo) {

        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("companyId", companyId);
        criteria.andEqualTo("employeeId", employeeId);
        criteria.andEqualTo("bankName", bankName);
        criteria.andEqualTo("bankAccountNo", bankAccountNo);
        BankCard bankCardBean = bankCardMapper.selectOneByExample(example);
        if (ObjUtils.isEmpty(bankCardBean)) {
            throw new FinPayException(GlobalResponseCode.BANK_CARD_ACCOUNT_NO_EXIST);
        }
        return bankCardBean;
    }

    public BankCard queryCardExistByCompanyEmployee(String bankAccountNo) {

        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bankAccountNo", bankAccountNo);
        BankCard bankCardBean = bankCardMapper.selectOneByExample(example);
        if (ObjUtils.isEmpty(bankCardBean)) {
            throw new FinPayException(GlobalResponseCode.BANK_CARD_ACCOUNT_NO_EXIST);
        }
        return bankCardBean;
    }
    /**
     * @Description: 保存分贝通虚拟卡的调额申请流水
     * @Param: [reqDTO]
     */
    public void saveBankCardApplyFlow(BankCardApplyFlow bankCardApplyFlow, GlobalResponseCode msg) {
        int count = bankCardApplyFlowMapper.insert(bankCardApplyFlow);
        if (count != 1) {
            throw new FinPayException(msg);
        }
    }

    public void removeBankCardCreateInfo(String bankAccountNo,String employeeId) {

        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("employeeId", employeeId);
        criteria.andEqualTo("bankAccountNo", bankAccountNo);
        BankCard bankCard = new BankCard();
        bankCard.setEmployeeId(bankAccountNo);
        int count = bankCardMapper.updateByExampleSelective(bankCard,example);
        if (count != 1) {
            throw new FinPayException(EXCEPTION);
        }
    }

    public void removeBankCardApplyFlow(String bankAccountNo,String employeeId) {
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("employeeId", employeeId);
        BankCardApplyFlow bankCardApplyFlow = new BankCardApplyFlow();
        bankCardApplyFlow.setEmployeeId(bankAccountNo);
        int count = bankCardApplyFlowMapper.updateByExampleSelective(bankCardApplyFlow,example);
        if (count < 1) {
            throw new FinPayException(EXCEPTION);
        }
    }

    public void removeBankCardTradeFlow(String bankAccountNo,String employeeId) {
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("employeeId", employeeId);
        BankCardCreditApply bankCardCreditApply = new BankCardCreditApply();
        bankCardCreditApply.setEmployeeId(bankAccountNo);
        int count = bankCardCreditApplyMapper.updateByExampleSelective(bankCardCreditApply,example);
        if (count < 1) {
            throw new FinPayException(EXCEPTION);
        }
    }


    /**
     * @Description: 银行卡交易流水
     * @Param: [reqDTO, bankCard]
     * @return: BankCardTradeFlow
     */
    public void saveBankCardTradeFlow(BankCardTradeFlow bankCardTradeFlow, GlobalResponseCode msg) {
        int count = bankCardTradeFlowMapper.insert(bankCardTradeFlow);
        if (count != 1) {
            throw new FinPayException(msg);
        }
    }

    /**
     */
    private void refundBankCardByWrongPaid(Long id, BigDecimal operationAmount,Integer tradeFundType) {
        //默认操作企业余额
        boolean isUsePersonal = VirtualCardTradeFundType.isPersonalFund(tradeFundType);
        BigDecimal operationCardCompanyBalance =  isUsePersonal? BigDecimal.ZERO: operationAmount;
        BigDecimal operationCardPersonalBalance = isUsePersonal? operationAmount: BigDecimal.ZERO;
        if (bankCardMapper.refundBankCardByWrongPaid(id, operationAmount,operationCardCompanyBalance,operationCardPersonalBalance) != 1) {
            throw new FinPayException(GlobalResponseCode.BANK_CARD_REFUND_ERROR);
        }
    }

    /**
     * @Description: 掉UC接口
     * @Param: [companyId, employeeId]
     * @return: EmployeeContract
     */
    public EmployeeContract getEmployeeContract(String companyId, String employeeId) {
        try {
            EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(companyId, employeeId);
            if (ObjUtils.isNotEmpty(employeeContract) && ObjUtils.isNotEmpty(employeeContract.getCompany_id())) {
                return employeeContract;
            }
            FinhubLogger.info("用户没绑定公司参数{}:{}==返回结果{}", companyId, employeeId, JsonUtils.toJson(employeeContract));
            throw new FinPayException(EXCEPTION);
        } catch (Exception e) {
            FinhubLogger.info("获取UC用户信息异常:公司{}={}", companyId, employeeId);
            throw new FinPayException(EXCEPTION);
        }
    }

    /**
     * @Description: 查询退款是否需要退还保证里面只有一个为ture
     * @Param: [reqDTO, bankCard, cardTradeFlow]
     * @return: com.fenbeitong.fenbeipay.bank.hupo.base.dto.RefundOrderTypeDto
     */
    public RefundOrderTypeDto queryRefundType(BankRefundTradeReqDTO reqDTO, BankCard bankCard, BankCardTradeFlow cardTradeFlow) {
        RefundOrderTypeDto typeDto = RefundOrderTypeDto.of();
        //1步查询企业不同，不同企业true 相同false
        typeDto.setDitCompany(checkNotSameCompany(reqDTO.getEmployeeId(), cardTradeFlow.getCompanyId()));
        //2步如果企业相同：才需要验证解绑状态
        if (!typeDto.getDitCompany()) {
            typeDto.setUnbindBankCard(BankCardStatus.isUnbind(bankCard.getCardStatus()));
        }
        //3步只有企业相同||解绑状态：才查询用分贝通虚拟卡开关
        if (!(typeDto.getDitCompany() || typeDto.getUnbindBankCard())) {
            typeDto.setOffBankCard(checkCompanyPackage(reqDTO.getCompanyId()));
        }
        //4步如果消费的账户和
        if (!(typeDto.getDitCompany() || typeDto.getUnbindBankCard())|| !typeDto.getOffBankCard()) {
            typeDto.setIsCutAcctModel(checkIsCutAcctModel(reqDTO));
        }
        //上一笔备用金已经过期限，备用金已经过期,则需要退还企业
        if (ObjUtils.isNotBlank(reqDTO.getPettyId())){
            BankPetty bankPetty = bankPettyManager.queryBankPettyPettyId(reqDTO.getPettyId());
            Date date = new Date();
            if (bankPetty.getExpireDate() != null && date.after(bankPetty.getExpireDate())){
                typeDto.setRetPetty(true);
            }
        }
        //单独判断：退还备用金和前3种权限其中有可能并存：备用金转了普通模式
        if (ObjUtils.isNotBlank(reqDTO.getPettyId()) && (ObjUtils.isEmpty(bankCard.getPettyId()) ||!Objects.equal(reqDTO.getPettyId(),bankCard.getPettyId()))){
            typeDto.setRetPetty(true);
        }
        //单独判断：退还备用金和前3种权限其中有可能并存:普通转备用金退企业
        if (ObjUtils.isBlank(reqDTO.getPettyId()) && (ObjUtils.isNotEmpty(bankCard.getPettyId())|| CardModelType.isPetty(bankCard.getCardModel()))){
            typeDto.setRetNormal(true);
        }
        if(BankCardStatus.isStopUse(bankCard.getCardStatus())){
            typeDto.setCardStopUse(true);
        }
        return typeDto;
    }

    /**
     * @Description: 验证企业是否可以用分贝通虚拟卡
     * @Param: [companyId]
     * @return: java.lang.Boolean
     */
    public Boolean checkCompanyPackage(String companyId){
        Boolean isResult=false;
        try {
            String code = "m_bank_individual";
            Map<String, Boolean> detailResult = iPrivilegeService.queryFunctionMoudle(companyId,code);
            if(ObjUtils.isEmpty(detailResult)){
                //为空，没有套餐信息
                throw new FinPayException(GlobalResponseCode.BANK_CARD_REFUND_COMPANY_PACKAGE_NULL);
            }
            if (!detailResult.get(code)) {
                //基础版本
                isResult= true;
            }
            return isResult;
        } catch (Exception e) {
        }
        return isResult;
    }


    /**
     * @Description: 不同企业true 相同false
     * @Param: [reqDTO, cardTradeFlow]
     * @return: java.lang.Boolean
     */
    public Boolean checkNotSameCompany(String employeeId, String companyId) {
        EmployeeSimpleInfoContract employeeSimpleInfo = iBaseEmployeeExtService.getEmployeeInfoByEmloyeeId(employeeId);
        if (ObjUtils.isNotEmpty(employeeSimpleInfo)) {
            if (ObjUtils.isBlank(employeeSimpleInfo.getCompany_id())) {
                return true;
            } else if (!Objects.equal(employeeSimpleInfo.getCompany_id(), companyId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 不同的账户id就返回ture
     */
    public Boolean checkIsCutAcctModel(BankRefundTradeReqDTO reqDTO){
        AcctCommonBaseDTO acctCommonBaseDTO = uAcctCompanyCardService.getAcctCompanyCard(reqDTO.getCompanyId(),reqDTO.getBankName());
        if (ObjUtils.isEmpty(acctCommonBaseDTO)) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_AUTH_ACCOUNT_ERROR);
        }
        if (!Objects.equal(acctCommonBaseDTO.getAccountId(), reqDTO.getAccountSubId())) {
            return true;
        }
        return false;
    }

    /**
     * 退冻结池额度退企业额度
     * 记录退额订单
     *
     * @Param: [reqDTO, bankCard]
     * @return: java.math.BigDecimal
     */
    private BigDecimal executeRefundCredit(BankRefundTradeReqDTO reqDTO, BankCard bankCard, RefundOrderTypeDto refundOrderTypeDto) {
        FinhubLogger.info("消费退款操作验证是否还额:是不相同公司{},卡是状态{} 是基础版本{}", refundOrderTypeDto.toString());

        //保存申请单
        CreateRefundOrderDto createRefundOrderDto = BankCardCreditAppConver.addRefundCreditOrder(reqDTO);
        BankCardCreditApply bankCardCreditApply = creditApplyManager.saveRefundCreditOrder(createRefundOrderDto);
        BankCardCreditDistribute bankCardCreditDistribute = BankCardDistributeConver.toBankCardCreditApplyDistribute(reqDTO, bankCard,VirtualOpenTypeEnum.RETURN.getKey(), bankCardCreditApply);
        creditDistributeManager.save(bankCardCreditDistribute);
        //备用金增加退还额度
        bankPettyExtMapper.onlyAddReturnSum(reqDTO.getOperationAmount(), reqDTO.getPettyId());
        //直接操作退企业
        if (refundOrderTypeDto.noCompanyBankCard()) {
            BankRefundCreditReqDTO bankRefundCreditReqDTO = BankCardCreditAppConver.refundTradeToBankRefundCreditReqDTO(reqDTO, bankCard, bankCardCreditDistribute);
            return applyCardManager.refundCreditAccount(bankRefundCreditReqDTO);
        }
        //扣卡退企业
        BankRefundCreditReqDTO bankRefundCreditReqDTO = BankCardCreditAppConver.toBankCardRefundCreditReqDTO(reqDTO, bankCard, bankCardCreditDistribute);
        return applyCardManager.refundCredit(bankRefundCreditReqDTO);

    }

    @Override
    public void unBindBankCardPettyId(BankCardUnBindPettyIdReqDTO reqDTO) {
        try {
            ValidateUtils.validate(reqDTO);
            applyCardManager.unBindBankCardPettyId(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.warn("【审批单操作置空银行卡备用金异常】unBindBankCardPettyId=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.warn("【审批单操作置空银行卡备用金异常】unBindBankCardPettyId=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.warn("【审批单操作置空银行卡备用验证异常】unBindBankCardPettyId=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【审批单操作置空银行卡备用系统异常】unBindBankCardPettyId=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public ResponsePage<BankCardCreditDistributeRespDTO> queryBankCardCreditDistributePage(BankCardCreditDistributeReqDTO reqDTO) {
        FinhubLogger.info("【虚拟卡发放单查询】请求参数reqDTO:{}", JSONObject.toJSONString(reqDTO));
        try{
            ValidateUtils.validate(reqDTO);
            return creditDistributeManager.selectBankPettyByPage(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.warn("【虚拟卡发放单查询】异常，请求参数reqDTO:{}", JSONObject.toJSONString(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.warn("【虚拟卡发放单查询】异常，请求参数reqDTO:{}", JSONObject.toJSONString(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡发放单查询】异常，请求参数reqDTO:{}", JSONObject.toJSONString(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<BankCardCreditDistributeRespDTO> getDistributeByPettyId(String employeeId, String pettyId) {
        FinhubLogger.info("【虚拟卡发放单查询】employeeId={},pettyId={}", employeeId, pettyId);
        try{
            return creditDistributeManager.getDistributeByPettyId(employeeId, pettyId);
        } catch (FinPayException e) {
            FinhubLogger.warn("【虚拟卡发放单查询】警告employeeId={},pettyId={}", employeeId, pettyId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.warn("【虚拟卡发放单查询】警告employeeId={},pettyId={}", employeeId, pettyId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡发放单查询】异常employeeId={},pettyId={}", employeeId, pettyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<BankCardCreditDistributeRespDTO> getDistributeBySaasApplyNo(String saasApplyNo) {
        FinhubLogger.info("【虚拟卡发放单查询】saasApplyNo={}", saasApplyNo);
        try{
            return creditDistributeManager.getDistributeBySaasApplyNo(saasApplyNo);
        } catch (FinPayException e) {
            FinhubLogger.warn("【虚拟卡发放单查询】警告saasApplyNo={}", saasApplyNo, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.warn("【虚拟卡发放单查询】警告saasApplyNo={}", saasApplyNo, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【虚拟卡发放单查询】异常saasApplyNo={}", saasApplyNo, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 获取下发失败的下发单列表
     */
    @Override
    public List<BankCardCreditDistributeRespDTO> queryFailDistribute(Integer start){
        FinhubLogger.info("获取失败的下发单列表，start={}", start);
        List<BankCardCreditDistribute> distributes = creditDistributeManager.queryFailDistribute(start);
        if (CollectionUtils.isEmpty(distributes)){
            return null;
        }
        List<BankCardCreditDistributeRespDTO> list = new ArrayList<>();
        distributes.forEach(distribute ->{
            BankCardCreditDistributeRespDTO distributeRespDTO = new BankCardCreditDistributeRespDTO();
            BeanUtils.copyProperties(distribute, distributeRespDTO);
            list.add(distributeRespDTO);
        });

        FinhubLogger.info("获取失败的下发单列表返回，start={},list={}", start, JsonUtils.toJson(list));
        return list;
    }

    /**强制解锁时间设置-毫秒*/
    public static final long PAY_LOCK_TIME_REFUND = 60*1000L;

    /**等待时间-毫秒**/
    public static final long PAY_WAIT_TIME_REFUND = 500L;


    private void tryLock(String lockPayKey) {
        boolean b = false;
        try {
            b = redissonService.tryLock(PAY_WAIT_TIME_REFUND,PAY_LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockPayKey);
        } catch (InterruptedException e) {
            FinhubLogger.error("f付款获取分布式锁失败,lockPayKey={}", lockPayKey,e);
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_PETTY_OPEN_ING);
        }
        if(!b){
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_PETTY_OPEN_ING);
        }
    }
    private void unLock(String lockPayKey) {
        try {
            redissonService.unLock(lockPayKey);
        } catch (Exception e) {
            FinhubLogger.warn("recordVouchersOperationFlow unlock fail err=", e);
        }
    }

    @Override
    public void retryDistributeCredit(CreditDistributeRetryReqDTO retryReqDTO){
        FinhubLogger.info("重试获取下发单信息下发单信息，distributeOrderNo={}", retryReqDTO.getDistributeOrderNo());

        if(ObjUtils.isNull(retryReqDTO.getDistributeOrderNo())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }

        String lockKey = "pay:retryDistributeCredit:"+retryReqDTO.getDistributeOrderNo();
        tryLock(lockKey);

        BankCardCreditDistribute distribute = creditDistributeManager.getDistributeByDistributeOrderNo(retryReqDTO.getDistributeOrderNo());
        if (java.util.Objects.isNull(distribute)){
            FinhubLogger.warn("下发单信息不存在，distributeOrderNo={}", retryReqDTO.getDistributeOrderNo());
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_DISTRIBUTE_NOT_EXISTED);
        }
        if (!CreditDistributeStatus.isFailStatus(distribute.getDistributeStatus())){
            FinhubLogger.warn("不是失败的下发单，distributeOrderNo={}，distributeStatus={}", retryReqDTO.getDistributeOrderNo(),distribute.getDistributeStatus());
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_DISTRIBUTE_STATUS_ERROR);
        }

        //增加备用金一致性问题的处理
        //1.如果存在备用金，当前的生效的备用金id和下发单的备用金id不一致，提示当前这条记录不能重发
        //2.如果不存在备用金，并且是普通模式，下发单上存在备用金id，则不能重发
        BankPetty bankPetty = bankPettyManager.queryBankPettyByEmployeeId(distribute.getCompanyId(), distribute.getEmployeeId());
        String pettyId = distribute.getPettyId();//存在，是备用金模式下发，不存在是普通模式下发

        Integer distributeModel = StringUtils.isBlank(pettyId)? CardModelType.NORMAL.getKey() : CardModelType.PETTY.getKey();

        BankCard bankCard = searchCardManager.queryByBankNameAndAccountNo(distribute.getBankName(), distribute.getBankAccountNo());
        if (ObjUtils.isNull(bankCard)){
            FinhubLogger.warn("下发单的卡信息不存在，distributeOrderNo={}", retryReqDTO.getDistributeOrderNo());
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_ACCOUNT_NO_EXIST);
        }
        if (!java.util.Objects.equals(distributeModel, bankCard.getCardModel())){
            FinhubLogger.warn("下发单的模式和卡模式信息不一致，distributeOrderNo={}", retryReqDTO.getDistributeOrderNo());
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_MODEL_CHANGED_ERROR);
        }

        if (StringUtils.isNotBlank(pettyId) && java.util.Objects.nonNull(bankPetty)){
            if(!Objects.equal(bankPetty.getPettyId(), pettyId)){
                FinhubLogger.warn("失败的下发单关联的备用金和当前备用金不一致，distributeOrderNo={}，pettyId={},BankPettyId={}", retryReqDTO.getDistributeOrderNo(), pettyId, bankPetty.getPettyId());
                throw FinhubExceptionUtil.exceptionFrom(DISTRIBUTE_INFO_CHANGE_ERROR);
            }
        }
        if (StringUtils.isBlank(pettyId) && java.util.Objects.nonNull(bankPetty)){
            FinhubLogger.warn("失败的下发单是普通下发，当前是备用金模式，distributeOrderNo={}，BankPettyId={}", retryReqDTO.getDistributeOrderNo(), bankPetty.getPettyId());
            throw FinhubExceptionUtil.exceptionFrom(DISTRIBUTE_INFO_CHANGE_ERROR);
        }
        //兼容重新下发，已经有新的备用金消费过的情况，此时卡上记录的备用金id已经更换了，需要修正为此下发备用金
        if (StringUtils.isNotBlank(pettyId)&& !Objects.equal(bankCard.getPettyId(), pettyId)){
            bankCardMapper.updateByPrimaryKey(BankPettyConver.toBankCard(bankCard, pettyId));
        }

        BankCardBatchDistributeRespDTO result = new BankCardBatchDistributeRespDTO();
        List<BankCardCreditDistributeRespDTO> distributeDetailList = new ArrayList<>();
        BankCardCreditApply applyOrder = creditApplyManager.queryByapplyTransNo(distribute.getApplyTransNo());
        EmployeeContract employeeContract = getEmployeeContract(distribute.getEmployeeId(), distribute.getCompanyId());
        boolean isPettyModel = !StringUtils.isBlank(pettyId);
        BankPetty bankPettyForSetType = null;
        //如果是备用金模式
        if (isPettyModel){
            //查询发放失败的那条记录，查询条件为失败下发单的pettyId
            bankPettyForSetType = bankPettyManager.queryBankPettyPettyId(pettyId);
        }

        BankApplyCreditReqDTO reqDTO = getBankApplyCreditReqDTO(distribute, bankPettyForSetType);
        distributeCredit(reqDTO, result, employeeContract, distributeDetailList, applyOrder, distribute, false);

    }

    @Override
    public void applyDistributeSetDisabled(CreditDistributeRetryReqDTO retryReqDTO){
        FinhubLogger.info("失败的下发单手动设置为失效，distributeOrderNo={}", retryReqDTO.getDistributeOrderNo());
        BankCardCreditDistribute distribute = creditDistributeManager.getDistributeByDistributeOrderNo(retryReqDTO.getDistributeOrderNo());
        if (java.util.Objects.isNull(distribute)){
            FinhubLogger.warn("下发单信息不存在，distributeOrderNo={}", retryReqDTO.getDistributeOrderNo());
            return;
        }
        if (!CreditDistributeStatus.isFailStatus(distribute.getDistributeStatus())){
            FinhubLogger.warn("不是失败的下发单，distributeOrderNo={}，distributeStatus={}", retryReqDTO.getDistributeOrderNo(),distribute.getDistributeStatus());
            return;
        }

        distribute.setDistributeStatus(CreditDistributeStatus.DISABLED.getCode());
        //更新下发单状态为失效
        creditDistributeManager.updateDistributeStatus(distribute);
        updateApplyOrderStatus(distribute);

    }

    @Override
    public List<BankCardDetailWithApplyRspDTO> queryBankCardDetailList(String companyId, String applyTransNo) {
        List<BankCardCreditDistribute> bankCardCreditDistributeDTOS = creditDistributeManager.queryDistributeDetailsByApplyTransNo(companyId, applyTransNo);

        List<BankCardDetailWithApplyRspDTO> bankCardDetailRspDTOList = Lists.newArrayList();
        for (BankCardCreditDistribute distributeDTO : bankCardCreditDistributeDTOS) {
            BankCardDetailWithApplyRspDTO bankCardDetailRspDTO = new BankCardDetailWithApplyRspDTO();
            bankCardDetailRspDTO.setBankAccountNo(distributeDTO.getBankAccountNo());
            bankCardDetailRspDTO.setBankName(distributeDTO.getBankName());
            bankCardDetailRspDTO.setBankNameShow(genBankShow(BankNameEnum.getBankEnum(distributeDTO.getBankName()).getName(), distributeDTO.getBankAccountNo()));
            bankCardDetailRspDTO.setBankIcon(BankNameEnum.getBankEnum(distributeDTO.getBankName()).getBankIconWebBig());
            bankCardDetailRspDTO.setApplyAmount(distributeDTO.getDistributeAmount());
            // 查虚拟卡最近下发记录：下发额度，下发时间
            BankCardCreditDistribute bankCardCreditDistribute = creditDistributeManager.queryLatestDistributeDetail(companyId, distributeDTO.getBankAccountNo());
            bankCardDetailRspDTO.setLatestApplyAmount(bankCardCreditDistribute.getDistributeAmount());
            bankCardDetailRspDTO.setLatestApplyTime(bankCardCreditDistribute.getCreateTime());
            bankCardDetailRspDTOList.add(bankCardDetailRspDTO);
        }
        return bankCardDetailRspDTOList;
    }

    @Override
    public void handleBalance(BankCardBaseReqDTO applyReq) {

    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public VirtualCardAccountFlowResqDTO addReturnFlow(BankCardFlowBaseReqDTO req) {
        FinhubLogger.info("addReturnFlow start data ={} ",JsonUtils.toJson(req));
        //补充解圈存流水
        //人工子账户退回流水
        String txnId = req.getTxnId();
        String orgTxnId = req.getOrgTxnId();
        String bankTransNo = req.getBankTransNo();
        if(StringUtils.isBlank(txnId) || StringUtils.isBlank(orgTxnId) || StringUtils.isBlank(bankTransNo)
        || StringUtils.isBlank(req.getCostImageUrl()) || req.getSubTrapAmount() ==null){
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_APPLY_NOT_EXIST_ERROR);
        }
        BankCardCreditDistribute distribute = creditDistributeManager.getDistributeByDistributeOrderNo(orgTxnId);
        EmployeeContract employeeContract = null;
        try {
            // 离职人员可能查不到
            employeeContract = getEmployeeContract(distribute.getEmployeeId(), distribute.getCompanyId());
        } catch (Exception e) {
            FinhubLogger.info("用户没绑定公司参数{}:{}", distribute.getEmployeeId(), distribute.getCompanyId());
        }
        if (ObjUtils.isEmpty(employeeContract)) {
            // 查不到离职人员时 初始化个空对象 从入参尝试获取一些基础信息 避免后续空指针
            employeeContract = new EmployeeContract();
            employeeContract.setName(req.getEmployeeName());
            employeeContract.setCompany_name(req.getCompanyName());
        }
        if(ObjUtils.isEmpty(distribute) ){
            throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_APPLY_NOT_EXIST_ERROR);
        }
        //二类卡到子账户流水
         BigDecimal amount = distribute.getDistributeAmount();
        if(req.getAmount()!=null){
            amount = req.getAmount();
        }

        List<AcctCompanyCard> cCards = acctCompanyCardService.findByBankName(distribute.getCompanyId(), "SPABANK", "SPABANK");
        AcctCompanyCard cCard = cCards.get(0);
        //参照圈存的本人流水
        BankCardTrapFlow oldFlow = bankCardTrapFlowMapper.selectByPrimaryKey(req.getCardTrapId());
        if(ObjUtils.isNotEmpty(oldFlow) && !oldFlow.getTxnId().equals(bankTransNo)){
            BankCardTrapFlow addTrapFlow = new BankCardTrapFlow();
            BeanUtils.copyProperties(oldFlow, addTrapFlow);
            addTrapFlow.setId(IDGen.genId(IDGen.BANK_CARD_TRAP_FLOW));
            addTrapFlow.setPayBankAccountNo(distribute.getBankAccountNo());
            addTrapFlow.setPayBankAccountName(employeeContract.getName());
            addTrapFlow.setReceiveBankAccountNo(cCard.getBankAccountNo());
            addTrapFlow.setReceiveBankAccountName(employeeContract.getCompany_name());
            addTrapFlow.setTrapType("02");
            addTrapFlow.setTrapNo(orgTxnId);
            addTrapFlow.setSubTrapNo(req.getBassTxnId());
            addTrapFlow.setTrapStatus("90");
            addTrapFlow.setOperationAmount(amount);
            addTrapFlow.setTrapOperationType("solveTrap");
            addTrapFlow.setCreateTime(new Date());
            addTrapFlow.setUpdateTime(new Date());
            addTrapFlow.setTxnId(req.getBankTransNo());
            addTrapFlow.setSubOperationAmount(req.getSubTrapAmount());
            addTrapFlow.setCostImageUrl(req.getCostImageUrl());
            addTrapFlow.setCostImageTime(new Date());
            addTrapFlow.setCostImageStatus(1);
            bankCardTrapFlowMapper.insert(addTrapFlow);
        }
        //补充企业账户流水 退回申请 如果已存在企业流水  则不在补
        VirtualCardAccountFlowResqDTO res = new VirtualCardAccountFlowResqDTO();
        if(StringUtils.isBlank(req.getAccountFlowId())){
            return res;
        }
        //先组装流水  再更新账户余额
        AcctCompanyCardFlow addCardFlow = new AcctCompanyCardFlow();
        AcctCompanyCardFlow oldCardFlow = acctCompanyCardFlowService.queryByFlowId(req.getAccountFlowId());

        BeanUtils.copyProperties(oldCardFlow, addCardFlow);
        String flowId = IDGen.genBankCardAccountId(distribute.getCompanyId());
        addCardFlow.setAccountFlowId(flowId);
        addCardFlow.setBizNo(orgTxnId);
        addCardFlow.setOperationAmount(amount);
        addCardFlow.setCurrentBalance(cCard.getBalance());
        BigDecimal balance = cCard.getBalance().add(amount);
        addCardFlow.setBalance(balance);
        addCardFlow.setSyncBankStatus(1);
        addCardFlow.setSyncBankTime(null);
        addCardFlow.setSyncBankTransNo(req.getBankTransNo());
        addCardFlow.setTargetBankAcctId(distribute.getBankAccountNo());
        addCardFlow.setOperationUserId(distribute.getEmployeeId());
        addCardFlow.setCreateTime(new Date());
        addCardFlow.setCostImageUrl(req.getCostImageUrl());
        addCardFlow.setCostImageTime(new Date());
        addCardFlow.setCostImageStatus(1);
        acctCompanyCardFlowService.saveAccountCardFlow(addCardFlow);
        acctCompanyCardService.addBalance(cCard.getAccountId(), amount);
        BeanUtils.copyProperties(addCardFlow, res);
        return res;
    }

    @Override
    public VirtualCardAccountFlowResqDTO addApplyFlow(BankCardFlowBaseReqDTO req) {
        VirtualCardAccountFlowResqDTO res = new VirtualCardAccountFlowResqDTO();
        if(StringUtils.isBlank(req.getAccountFlowId()) || StringUtils.isBlank(req.getBackFlowId()) || ObjUtils.isEmpty(req.getAmount())){
            return res;
        }
        AcctCompanyCardFlow oldCardFlow = acctCompanyCardFlowService.queryByFlowId(req.getAccountFlowId());
        AcctCompanyCardFlow oldBackFlow = acctCompanyCardFlowService.queryByFlowId(req.getBackFlowId());

        if(ObjUtils.isEmpty(oldCardFlow)){
            return res;
        }
        List<AcctCompanyCard> cCards = acctCompanyCardService.findByBankName(oldCardFlow.getCompanyId(), "SPABANK", "SPABANK");
        AcctCompanyCard cCard = cCards.get(0);
        AcctCompanyCardFlow addCardFlow = new AcctCompanyCardFlow();
        BeanUtils.copyProperties(oldCardFlow, addCardFlow);
        String flowId = IDGen.genBankCardAccountId(oldCardFlow.getCompanyId());
        addCardFlow.setAccountFlowId(flowId);
        addCardFlow.setBizNo(oldBackFlow.getAccountFlowId()+":回收失败，额度退回");
        BigDecimal amount = req.getAmount();
        addCardFlow.setOperationAmount(amount);
        addCardFlow.setCurrentBalance(cCard.getBalance());
        BigDecimal balance = cCard.getBalance().subtract(amount);
        if(balance.compareTo(BigDecimal.ZERO)<0){
            throw new FinPayException(FREE_CREDIT_INIT_ERROR);
        }
        addCardFlow.setBalance(balance);
        addCardFlow.setSyncBankStatus(1);
        addCardFlow.setSyncBankTime(null);
        addCardFlow.setSyncBankTransNo(oldBackFlow.getBankTransNo());
        addCardFlow.setCreateTime(new Date());
        addCardFlow.setCostImageUrl(oldBackFlow.getCostImageUrl());
        addCardFlow.setCostImageTime(new Date());
        addCardFlow.setCostImageStatus(1);
        acctCompanyCardFlowService.saveAccountCardFlow(addCardFlow);
        acctCompanyCardService.reduceBalance(cCard.getAccountId(), amount);
        BeanUtils.copyProperties(addCardFlow, res);

        return res;
    }

    private String genBankShow(String bankName, String bankAccountNo) {
        if (StringUtils.isBlank(bankAccountNo) || bankAccountNo.length() < 4) {
            return bankName;
        }
        String cardNo = bankAccountNo.substring(bankAccountNo.length() - 4);
        return bankName + "(" + cardNo + ")";
    }


    @NotNull
    private BankApplyCreditReqDTO getBankApplyCreditReqDTO(BankCardCreditDistribute distribute, BankPetty bankPetty) {
        BankApplyCreditReqDTO reqDTO = new BankApplyCreditReqDTO();
        reqDTO.setCompanyId(distribute.getCompanyId());
        reqDTO.setEmployeeId(distribute.getEmployeeId());
        reqDTO.setBankName(distribute.getBankName());
        reqDTO.setBankAccountNo(distribute.getBankAccountNo());
        reqDTO.setOperationAmount(distribute.getDistributeAmount());
        reqDTO.setPettyId(distribute.getPettyId());
        reqDTO.setBizNo(distribute.getBizNo());
        reqDTO.setBankApplyCreditType(distribute.getType());
        reqDTO.setOperationUserId(distribute.getEmployeeId());
        reqDTO.setOperationChannel(distribute.getOperationChannel());
        if (java.util.Objects.nonNull(bankPetty)){
            reqDTO.setPettyType(bankPetty.getPettyType());
        }
        //reqDTO.setAttributions();
        reqDTO.setOperationUserName(distribute.getEmployeeName());
        reqDTO.setApplyReason(distribute.getApplyReason());
        //reqDTO.setApplyReasonDesc();
        return reqDTO;
    }


    /**
     * 查询当日已经转账金额
     * @param employeeId 员工ID
     * @return 当日已经发放金额
     */
    public BigDecimal queryTotalAmountPay(String employeeId){
        BankSearchCardDetailRespDTO bankCardDetail = iBankCardSearchService.queryByEmployeeAndBankName(employeeId,BankNameEnum.SPABANK.getCode());
        if (bankCardDetail == null) {
            return BigDecimal.ZERO;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String startDate = dateFormat.format(new Date());
        String endDate = dateFormat.format(new Date());
        QueryTransactionDetailReqDto qryTransListReqDto = new QueryTransactionDetailReqDto();
        qryTransListReqDto.setAgreementNo(bankCardDetail.getBankUid());
        qryTransListReqDto.setStartDate(startDate);
        qryTransListReqDto.setEndDate(endDate);
        qryTransListReqDto.setPageNo("1");
        qryTransListReqDto.setPageSize("1000"); //平安银行无分页  赋值1000 当天全部交易数量
        FinhubLogger.info("【平安虚拟卡】查询账户交易明细，入参：{}", JSON.toJSONString(qryTransListReqDto));
        SpaPersonAcctRespBaseDto<QueryTransactionDetailRespDto> transactionDetailRes = iSpaBankPersonAcctService.queryTransactionDetail(qryTransListReqDto);
        if ("test".equals(currentEnvironment) || "dev".equals(currentEnvironment) ){
            return new BigDecimal("1000");
        }
        if (ObjUtils.isEmpty(transactionDetailRes)) {
            return BigDecimal.ZERO;
        }
        FinhubLogger.info("【平安虚拟卡】同步交易流水结果 eid = {}，res = {}", employeeId , JSON.toJSONString(transactionDetailRes));

        if (ObjUtils.isEmpty(transactionDetailRes.getData())) {
            return BigDecimal.ZERO;
        }
        if (transactionDetailRes.getData().getResultList().size()<=0) {
            return BigDecimal.ZERO;
        }
        QueryTransactionDetailRespDto data = transactionDetailRes.getData();
        List<QueryTransactionDetailDto> transactionList = data.getResultList();
        //计算今天累计消费
        return transactionList.stream()
                .filter(s -> "D".equals(s.getDcFlag()) && s.getTranAmt()!= null)
                .map(QueryTransactionDetailDto::getTranAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
