package com.fenbeitong.fenbeipay.rpc.service.extract;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.*;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractDayService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.extract.AcctExtractDay;
import com.fenbeitong.fenbeipay.dto.extract.ExtractDayManualRecord;
import com.fenbeitong.fenbeipay.dto.extract.ExtractDayShow;
import com.fenbeitong.fenbeipay.extract.db.mapper.AcctExtractDayMapper;
import com.fenbeitong.fenbeipay.extract.db.mapper.ExtractDayManualRecordMapper;
import com.fenbeitong.fenbeipay.extract.manager.impl.ExtractDayManualManagerServiceImpl;
import com.fenbeitong.fenbeipay.extract.service.UAcctExtractDayService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.pay.search.dbf.mapper.ExtractDayWebMapper;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;

/**
 * 对账日切RPC接口实现
 * Date 2021/3/17
 * <AUTHOR>
 *
 **/
@Service("iAcctExtractDayService")
public class IAcctExtractDayServiceImpl implements IAcctExtractDayService {

    @Autowired
    private UAcctExtractDayService uAcctExtractDayService;


    @Autowired
    ExtractDayWebMapper extractDayWebMapper;

    @Autowired
    private AcctExtractDayMapper acctExtractDayMapper;

    @Autowired
    ExtractDayManualRecordMapper extractDayManualRecordMapper;

    @Autowired
    ExtractDayManualManagerServiceImpl extractDayManualManagerServiceImpl;

    /**
     * 账单关联流水，是否走大数据库标识
     * QX 2022-03-05
     */
    @Value("${db.fenbeitong1.onOff}")
    private boolean flowBillOn;
    
    private static final String QUERY_DEADLINE = "2021-01-01 00:00:00";

    @Override
    public ResponsePage<AcctExtractDaySearchRespRPCDTO> queryByPage(AcctExtractDaySearchReqRPCDTO queryReq) {
        return uAcctExtractDayService.queryByPage(queryReq);
    }
    
    @Override
    public AcctExtractDaySearchRespRPCDTO queryCompanyOpeningClosingBalance(AcctExtractDaySearchReqRPCDTO queryParam) {
    	if (Objects.isNull(queryParam) || 
    			Objects.isNull(queryParam.getBillStartTime()) || 
    			Objects.isNull(queryParam.getBillEndTime()) || 
    			Objects.isNull(queryParam.getAccountSubType()) ||  // ExtractAccountTypeEnum
    			StringUtils.isAnyBlank(queryParam.getCompanyId())) {
    		throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
    	}
    	return uAcctExtractDayService.queryCompanyOpeningClosingBalance(queryParam);
    }
    

    @Override
    public ResponsePage<AcctCreditSearchRespRpcDTO> queryCreditAcctByPage(AcctCreditSearchReqRpcDTO acctCreditSearchReqRpcDTO) {
        return uAcctExtractDayService.queryCreditAcctByPage(acctCreditSearchReqRpcDTO);
    }

    @Override
    public int updateExtractDay(String key, String value, Integer accountType) {
        return uAcctExtractDayService.updateExtractDay(key, value, accountType);
    }

    @Override
    public ResponsePage<ExtractDayTaskRespRpcDTO> queryExtractDayTaskByPage(ExtractDayTaskReqDTO query) {
        FinhubLogger.info("日切余额任务列表查询，入参{}", JsonUtils.toJson(query));
        // 将如 2022-04-01 00:00:00 转换为 2022-04-01，因为数据库是字符串，否则查询不到数据。 QX 2022-04-02
        if(!StringUtils.isBlank(query.getStartDate())){
            query.setStartDate(DateUtils.formatDate(DateUtils.parseDate(query.getStartDate())));

        }
        if(!StringUtils.isBlank(query.getEndDate())){
            query.setEndDate(DateUtils.formatDate(DateUtils.parseDate(query.getEndDate())));
        }
        return uAcctExtractDayService.queryExtractDayTaskByPage(query);
    }

    @Override
    public void executeAgain(String taskDate, Integer taskType) {
        FinhubLogger.info("日切余额任务，重新执行，入参{}, {}", taskDate, taskType);
        uAcctExtractDayService.executeAgain(taskDate, taskType);
    }

    /**
     * 日终余额展示，页面逻辑
     * @param queryReq
     * @return
     */
    @Override
    public ExtractDayShowRespDTO query(ExtractDayShowReqDTO queryReq) {
        FinhubLogger.info("日切余额单日web查询，入参{}", JsonUtils.toJson(queryReq));
        String startTime = queryReq.getStartTime();
        String endTime = queryReq.getEndTime();
        if(ObjUtils.isBlank(startTime) || ObjUtils.isBlank(endTime)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "开始或结束时间为空");
        }
        Date startTimeDate = DateUtils.parseDate(startTime);
        Date endTimeDate = DateUtils.parseDate(endTime);
        int count = DateUtil.daysBetweenDay(startTimeDate, endTimeDate) + 1; // 总条数等于日期间隔+1
        
        Date deadline = DateUtils.parseDate(QUERY_DEADLINE);
        if (startTimeDate.before(deadline) || endTimeDate.before(deadline)) {
        	throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "2021年1月1日之前无数据，请重填选日期后搜索。");
        }
        
        ExtractDayShowRespDTO extractDayShowRespDTO = new ExtractDayShowRespDTO();

        List<ExtractDayShow> extractDayShows;
        //联调复杂度高，腾讯云兼容也差
        if(false&&flowBillOn){
            extractDayShows = getExtractDayShowsBigData(queryReq);
        }else{
            extractDayShows = getExtractDayShows(queryReq);
        }

        Map<String, ExtractDayShow> allDate = getAllDate(extractDayShows);

        boolean haveNo = false;
        Date billTime = startTimeDate; // 默认为开始日期
        BigDecimal incomeAll =  BigDecimal.ZERO;
        BigDecimal expenditureAll =  BigDecimal.ZERO;
        List<ExtractDayShowDTO> list = Lists.newArrayList();
        for (int i = 0; i < count; i++){
            ExtractDayShowDTO extractDayShow = new ExtractDayShowDTO(); // 返回值
            ExtractDayShow one = allDate.get(DateUtils.formatDate(billTime)); // yyyy-MM-dd
            if(one != null){

                BigDecimal beginBalance = one.getBeginBalance()  == null?  BigDecimal.ZERO : one.getBeginBalance();
                BigDecimal income = one.getIncome() == null?  BigDecimal.ZERO : one.getIncome();
                BigDecimal expenditure = one.getExpenditure() == null?  BigDecimal.ZERO : one.getExpenditure();
                BigDecimal finalBalance = one.getFinalBalance() == null?  BigDecimal.ZERO : one.getFinalBalance();

                incomeAll = incomeAll.add(income); // 收入求和
                expenditureAll = expenditureAll.add(expenditure); // 支出求和

                extractDayShow.setHaveValue(new Integer("1")); // 有值
                extractDayShow.setBillTime(DateUtils.formatDate(one.getBillTime())); // 记账日期
                extractDayShow.setBeginBalance(BigDecimalUtils.fenToYuan(beginBalance).toString());// 期初余额
                extractDayShow.setIncome(BigDecimalUtils.fenToYuan(income).toString()); // 本期收入
                extractDayShow.setExpenditure(BigDecimalUtils.fenToYuan(expenditure).toString()); // 本期支出
                extractDayShow.setFinalBalance(BigDecimalUtils.fenToYuan(finalBalance).toString()); // 期末余额
            }else {
                haveNo = true;
                extractDayShow.setHaveValue(new Integer("0")); // 无值
                extractDayShow.setBillTime(DateUtils.formatDate(billTime)); // 记账日期
                extractDayShow.setBeginBalance("--");// 期初余额
                extractDayShow.setIncome("--"); // 本期收入
                extractDayShow.setExpenditure("--"); // 本期支出
                extractDayShow.setFinalBalance("--"); // 期末余额
            }
            list.add(extractDayShow);
            // 继续下一天
            Date nextDay = DateUtils.plusDays(billTime, 1);
            billTime = nextDay;
        }
        // list是按照开始日期填充，需求是：按照时间倒排序，因此转换；
        Collections.reverse(list);

        // 设置汇总的期初、期末余额。
        if(extractDayShows.size()>0){
            ExtractDayShow extractDayShowBegin = new ExtractDayShow();
            ExtractDayShow extractDayShowAfter = new ExtractDayShow();

            if(extractDayShows.size() ==1 ){
                extractDayShowBegin = extractDayShows.get(0);
                extractDayShowAfter = extractDayShows.get(0);
            }
            /**
             * 数据库按照降序排序，因此期初取：最后一个，期末取：第一个
             */
            if(extractDayShows.size() >1){
                extractDayShowBegin = extractDayShows.get(extractDayShows.size()-1);
                extractDayShowAfter = extractDayShows.get(0);
            }
            BigDecimal beginBalance = extractDayShowBegin.getBeginBalance()  == null?  BigDecimal.ZERO : extractDayShowBegin.getBeginBalance();
            BigDecimal finalBalance = extractDayShowAfter.getFinalBalance() == null?  BigDecimal.ZERO : extractDayShowAfter.getFinalBalance();
            extractDayShowRespDTO.setBeginBalanceAll(BigDecimalUtils.fenToYuan(beginBalance).toString());
            extractDayShowRespDTO.setFinalBalanceAll(BigDecimalUtils.fenToYuan(finalBalance).toString());
        }

        extractDayShowRespDTO.setHaveValue(haveNo ? new Integer("0") : new Integer("1"));
        extractDayShowRespDTO.setIncomeAll(BigDecimalUtils.fenToYuan(incomeAll).toString());
        extractDayShowRespDTO.setExpenditureAll(BigDecimalUtils.fenToYuan(expenditureAll).toString());
        extractDayShowRespDTO.setExtractDayShowDTO(list);
        extractDayShowRespDTO.setCount(count); // 总条数

        return extractDayShowRespDTO;

    }

    private Map<String, ExtractDayShow> getAllDate(List<ExtractDayShow> extractDayShows){
        Map<String, ExtractDayShow> map = new LinkedHashMap<String, ExtractDayShow>();
        for (ExtractDayShow one : extractDayShows) {
            map.put(DateUtils.formatDate(one.getBillTime()), one);
        }
        return map;
    }

    @Override
    public ResponsePage<ExtractDayShowPageDTO> queryPage(ExtractDayShowPageReqDTO queryReq) {
        long startTime = System.currentTimeMillis();
        FinhubLogger.info("日切余额单日web导出，入参{}", JsonUtils.toJson(queryReq));
        List<ExtractDayShow> dbList;
        int totalCount = 0;
        //联调复杂度高，腾讯云兼容也差
        if(false&&flowBillOn){
            // 走大数据
            dbList = getExtractDayShowsBigDataPage(queryReq);
            totalCount = getExtractDayShowsBigDataPageCount(queryReq);
        }else{
            // 走业务库
            dbList = getExtractDayShowsPage(queryReq);
            totalCount = getExtractDayShowsPageCount(queryReq);
        }
        if(ObjUtils.isEmpty(dbList)){
            ResponsePage<ExtractDayShowPageDTO> responsePage = new ResponsePage<>();
            responsePage.setTotalCount(totalCount);
            responsePage.setDataList(new ArrayList<>());
            FinhubLogger.info("日切余额单日web导出结束，耗时{}ms" ,(System.currentTimeMillis() - startTime));
            return responsePage;
        }else {
            List<ExtractDayShowPageDTO> list = Lists.newArrayList();
            for (ExtractDayShow one: dbList){
                ExtractDayShowPageDTO retOne = new ExtractDayShowPageDTO();
                // 类型不一致，不能copy
                // BeanUtils.copyProperties(one, retOne);
                retOne.setBillTime(DateUtils.formatDate(one.getBillTime()));
                retOne.setBeginBalance(BigDecimalUtils.fenToYuan(one.getBeginBalance()).toString());
                retOne.setIncome(BigDecimalUtils.fenToYuan(one.getIncome()).toString());
                retOne.setExpenditure(BigDecimalUtils.fenToYuan(one.getExpenditure()).toString());
                retOne.setFinalBalance(BigDecimalUtils.fenToYuan(one.getFinalBalance()).toString());
                list.add(retOne);
            }
            ResponsePage<ExtractDayShowPageDTO> responsePage = new ResponsePage<>();
            responsePage.setTotalCount(totalCount);
            responsePage.setDataList(list);
            FinhubLogger.info("日切余额单日web导出结束，耗时{}ms" ,(System.currentTimeMillis() - startTime));
            return responsePage;
        }
    }

    @Override
    public void updateById(ExtractDayUpdateReqDTO updateReq) {
        AcctExtractDay acctExtractDay = new AcctExtractDay();
        acctExtractDay.setId(updateReq.getId());
        acctExtractDay.setBeginBalance(updateReq.getBeginBalance());
        acctExtractDay.setIncome(updateReq.getIncome());
        acctExtractDay.setExpenditure(updateReq.getExpenditure());
        acctExtractDay.setFinalBalance(updateReq.getFinalBalance());
        acctExtractDay.setExtractStatus(updateReq.getExtractStatus());
        acctExtractDayMapper.updateByPrimaryKeySelective(acctExtractDay);
    }


    /**
     * 走大数据的，求数量
     * @param queryReq
     * @return
     */
    private int getExtractDayShowsBigDataPageCount(ExtractDayShowPageReqDTO queryReq) {
        ExtractDayShowReqDTO extractDayShowReqDTO = new ExtractDayShowReqDTO();
        BeanUtils.copyProperties(queryReq, extractDayShowReqDTO);
        Example example = getExampleBigDate(extractDayShowReqDTO);
        return extractDayWebMapper.selectCountByExample(example);
    }
    /**
     * 走大数据的查询，无分页列表查询
     * @param queryReq
     * @return
     */
    private List<ExtractDayShow> getExtractDayShowsBigData(ExtractDayShowReqDTO queryReq){
        Example example = getExampleBigDate(queryReq);
        return extractDayWebMapper.selectByExample(example);
    }
    /**
     * 走大数据的查询，分页查询
     * @param queryReq
     * @return
     */
    private List<ExtractDayShow> getExtractDayShowsBigDataPage(ExtractDayShowPageReqDTO queryReq){
        if (ObjUtils.isNotBlank(queryReq.getStartTime()) && ObjUtils.isNotBlank(queryReq.getEndTime())) {
        	Date deadline = DateUtils.parseDate(QUERY_DEADLINE);
        	Date startDate = DateUtils.parseDate(queryReq.getStartTime());
            queryReq.setStartDate(startDate.before(deadline) ? deadline : startDate);
            queryReq.setEndDate(DateUtils.parseDate(queryReq.getEndTime()));
        }
        return extractDayWebMapper.selectPage(queryReq);
    }

    /**
     * 走业务库的数量
     * @param queryReq
     * @return
     */
    private int getExtractDayShowsPageCount(ExtractDayShowPageReqDTO queryReq) {
        ExtractDayShowReqDTO extractDayShowReqDTO = new ExtractDayShowReqDTO();
        BeanUtils.copyProperties(queryReq, extractDayShowReqDTO);
        Example example = getExample(extractDayShowReqDTO);
        return acctExtractDayMapper.selectCountByExample(example);
    }
    /**
     * 走业务库的查询。无分页查询
     * @param queryReq
     * @return
     */
    private List<ExtractDayShow> getExtractDayShows(ExtractDayShowReqDTO queryReq){
        Example example = getExample(queryReq);
        List<AcctExtractDay> extractDayShows = acctExtractDayMapper.selectByExample(example);
        List<ExtractDayShow> ret = Lists.newArrayList();
        for (AcctExtractDay one :extractDayShows) {
            ExtractDayShow extractDayShow = new ExtractDayShow();
            BeanUtils.copyProperties(one, extractDayShow);
            ret.add(extractDayShow);
        }
        return ret;
    }
    /**
     * 走业务库的查询，分页查询
     * @param queryReq
     * @return
     */
    private List<ExtractDayShow> getExtractDayShowsPage(ExtractDayShowPageReqDTO queryReq){
        ExtractDayShowReqDTO extractDayShowReqDTO = new ExtractDayShowReqDTO();
        BeanUtils.copyProperties(queryReq, extractDayShowReqDTO);
        Example example = getExample(extractDayShowReqDTO);
        RowBounds rowBounds = new RowBounds(queryReq.getOffset(), queryReq.getPageSize());
        List<AcctExtractDay> extractDayShows = acctExtractDayMapper.selectByExampleAndRowBounds(example, rowBounds);
        List<ExtractDayShow> ret = Lists.newArrayList();
        for (AcctExtractDay one :extractDayShows) {
            ExtractDayShow extractDayShow = new ExtractDayShow();
            BeanUtils.copyProperties(one, extractDayShow);
            ret.add(extractDayShow);
        }
        return ret;
    }

    /**
     * 走大数据的构造
     * @param queryReq
     * @return
     */
    private Example getExampleBigDate(ExtractDayShowReqDTO queryReq){
        Example example = new Example(ExtractDayShow.class);
        getComExample(example, queryReq);
        return example;
    }
    /**
     * 走业务库的构造
     * @param queryReq
     * @return
     */
    private Example getExample(ExtractDayShowReqDTO queryReq){
        Example example = new Example(AcctExtractDay.class);
        getComExample(example, queryReq);
        return example;
    }

    public void getComExample(Example example, ExtractDayShowReqDTO queryReq){
        Example.Criteria criteria = example.createCriteria();
        Date deadline = DateUtils.parseDate(QUERY_DEADLINE);
        if (ObjUtils.isNotBlank(queryReq.getStartTime()) && ObjUtils.isNotBlank(queryReq.getEndTime())) {
        	Date startTimeDate = DateUtils.parseDate(queryReq.getStartTime());
            criteria.andBetween("billTime", startTimeDate.before(deadline) ? deadline : startTimeDate, DateUtils.parseDate(queryReq.getEndTime()));
        }
        if(ObjUtils.isNotBlank(queryReq.getCompanyId())){
            criteria.andEqualTo("companyId", queryReq.getCompanyId());
        }
        if(ObjUtils.isNotEmpty(queryReq.getAccountSubType())){
            criteria.andEqualTo("accountSubType", queryReq.getAccountSubType());
        }
        if(ObjUtils.isNotBlank(queryReq.getBankAccountNo())){
            criteria.andEqualTo("bankAccountNo", queryReq.getBankAccountNo());
        }
        if(ObjUtils.isNotEmpty(queryReq.getAccountModel())){
            criteria.andEqualTo("accountModel", queryReq.getAccountModel());
        }
        example.orderBy("billTime").desc(); // 按照记账日期降序
    }

    /**
     * 获取触发记录
     * @param queryReq ExtractDayManualRecordReqDTO
     * @return ExtractDayManualRecord
     */
    public ExtractDayManualRecord getManualRecord(ExtractDayManualRecordReqDTO queryReq) {
        FinhubLogger.info("日切余额获取触发记录，入参{}", JsonUtils.toJson(queryReq));
        queryReq.checkReq();
        ExtractDayManualRecord extractDayManualRecordExample = extractDayManualManagerServiceImpl.getByCompanyId(queryReq.getCompanyId());
        return extractDayManualRecordExample == null ? new ExtractDayManualRecord() : extractDayManualRecordExample;
    }

    /**
     * 添加触发记录
     * @param queryReq 请求参数
     * @return ExtractDayManualRecord
     */
    public ExtractDayManualRecord addManualRecord(ExtractDayManualRecordReqDTO queryReq) {
        FinhubLogger.info("日切余额添加触发记录，入参{}", JsonUtils.toJson(queryReq));
        queryReq.checkReq();
        ExtractDayManualRecord extractDayManualRecord = new ExtractDayManualRecord();
        // 如果不为空，加入
        ExtractDayManualRecord extractDayManualRecordExample = extractDayManualManagerServiceImpl.getByCompanyId(queryReq.getCompanyId());
        if(extractDayManualRecordExample == null){
            extractDayManualManagerServiceImpl.insert(queryReq);
        }
        return extractDayManualRecord;
    }

    public int deleteById(Long id) {
        FinhubLogger.info("手工删除日切余额记录，入参{}", id);        int i = acctExtractDayMapper.deleteByPrimaryKey(id);
        FinhubLogger.info("手工删除日切余额记录完成，数量{}", i);
        return i;
    }

    public int deleteManualRecord(Long id) {
        FinhubLogger.info("手工删除白名单记录，入参{}", id);
        int i = extractDayManualRecordMapper.deleteByPrimaryKey(id);
        FinhubLogger.info("手工删除白名单完成，数量{}", i);
        return i;
    }
}
