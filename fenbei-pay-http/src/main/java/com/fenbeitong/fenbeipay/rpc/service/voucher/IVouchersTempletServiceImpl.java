package com.fenbeitong.fenbeipay.rpc.service.voucher;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponseInfoPage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTypeEnum;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VoucherForStatisticsRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTempletResponseRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTypeRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTempletService;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersOperationService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersPerson;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersType;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTempletService;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTypeService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: java类作用描述
 * @ClassName: IVouchersTempletServiceImpl
 * @Author: zhangga
 * @CreateDate: 2019/5/21 2:51 PM
 * @UpdateUser:
 * @UpdateDate: 2019/5/21 2:51 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iVouchersTempletService")
public class IVouchersTempletServiceImpl implements IVouchersTempletService {
    @Autowired
    private IVouchersOperationService iVouchersOperationService;
    @Autowired
    private VouchersTypeService vouchersTypeService;
    @Autowired
    private VouchersTempletService vouchersTempletService;


    @Override
    public VouchersTempletResponseRPCDTO queryVouchersTemplet(String vouchersTempletId) {
        try {
            VouchersTempletResponseRPCDTO dto = new VouchersTempletResponseRPCDTO();
            VouchersTemplet vouchersTemplet = vouchersTempletService.getVouchersTempletById(vouchersTempletId);
            BeanUtils.copyProperties(vouchersTemplet, dto);
            List<String> voucherTypes = JSONArray.parseArray(vouchersTemplet.getVoucherTypeList(), String.class);
            List<String> distinctVoucherType = VoucherTypeEnum.getDistinctVoucherType(voucherTypes);
            List<String> voucherTypeNames = new ArrayList<>();
            if (ObjUtils.isNotEmpty(distinctVoucherType)) {
                voucherTypeNames = vouchersTypeService.getVouchersTypeNames(distinctVoucherType);
            }
            dto.setVoucherTypeNames(voucherTypeNames);
            dto.setVoucherTypeList(distinctVoucherType);
            dto.setVoucherBusinessType(distinctVoucherType);
            return dto;
        } catch (FinPayException e) {
            FinhubLogger.error("【查询分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【查询分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【查询分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public Map<String, VouchersTempletResponseRPCDTO> queryVouchersTemplets(List<String> vouchersTempletIds) {
        try {
            Map<String, VouchersTempletResponseRPCDTO> dtoMap = new HashMap<>();
            List<VouchersTemplet> vouchersTemplets = vouchersTempletService.getVouchersTempletByIds(null, vouchersTempletIds);
            vouchersTemplets.forEach(vouchersTemplet -> {
                VouchersTempletResponseRPCDTO dto = new VouchersTempletResponseRPCDTO();
                BeanUtils.copyProperties(vouchersTemplet, dto);
                List<String> voucherTypes = JSONArray.parseArray(vouchersTemplet.getVoucherTypeList(), String.class);
                List<String> distinctVoucherType = VoucherTypeEnum.getDistinctVoucherType(voucherTypes);
                List<String> voucherTypeNames = new ArrayList<>();
                if (ObjUtils.isNotEmpty(distinctVoucherType)) {
                    voucherTypeNames = vouchersTypeService.getVouchersTypeNames(distinctVoucherType);
                }
                dto.setVoucherTypeNames(voucherTypeNames);
                dto.setVoucherTypeList(distinctVoucherType);
                dto.setVoucherBusinessType(distinctVoucherType);
                dtoMap.put(vouchersTemplet.getVoucherTempletId(), dto);
            });
            return dtoMap;
        } catch (FinPayException e) {
            FinhubLogger.error("【查询分贝券模板异常】，模板Ids：{}", vouchersTempletIds, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【查询分贝券模板异常】，模板Ids：{}", vouchersTempletIds, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【查询分贝券模板异常】，模板Ids：{}", vouchersTempletIds, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void useVouchersTemplet(String vouchersTempletId, String bizNo, String bizName, String operationUserId) {
        try {
            iVouchersOperationService.useVouchersTemplet(vouchersTempletId, bizNo, bizName, operationUserId);
        } catch (FinPayException e) {
            FinhubLogger.error("【使用分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【使用分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【使用分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void removeVouchersTempletUse(String vouchersTempletId, String bizNo, String operationUserId) {
        try {
            iVouchersOperationService.removeVouchersTempletUse(vouchersTempletId, bizNo, operationUserId);
        } catch (FinPayException e) {
            FinhubLogger.error("【解除使用分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【解除使用分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【解除使用分贝券模板异常】，模板Id：{}", vouchersTempletId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public Map<String, VouchersTypeRespRPCDTO> getVouchersTypeMap() {
        List<VouchersType> vouchersTypeList = vouchersTypeService.getVouchersTypeList();
        Map<String, VouchersTypeRespRPCDTO> result = new HashMap<>();
        vouchersTypeList.forEach(vouchersType -> {
            VouchersTypeRespRPCDTO dto = new VouchersTypeRespRPCDTO();
            BeanUtils.copyProperties(vouchersType, dto);
            result.put(vouchersType.getTypeKey(), dto);
        });
        return result;
    }
}
