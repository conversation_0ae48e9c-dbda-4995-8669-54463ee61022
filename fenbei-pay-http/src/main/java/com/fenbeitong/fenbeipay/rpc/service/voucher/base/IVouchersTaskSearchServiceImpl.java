package com.fenbeitong.fenbeipay.rpc.service.voucher.base;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTasksReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskDetailsRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.voucher.VouchersInvoiceTaskStatisticsVO;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersTaskSearchService;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.enums.voucher.VoucherTaskStatus;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTasksRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 分贝券查询RPC服务
 * @ClassName: IVouchersSearchServiceImpl
 * @Author: zhangga
 * @CreateDate: 2020/11/10 3:07 下午
 * @UpdateUser:
 * @UpdateDate: 2020/11/10 3:07 下午
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iVouchersTaskSearchService")
public class IVouchersTaskSearchServiceImpl implements IVouchersTaskSearchService {
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;

    @Override
    public List<VouchersTaskRespDTO> queryVouchersTask(String companyId, List<String> voucherTaskIdList, Integer pageNo, Integer pageSize) throws FinhubException {
        VouchersTasksRequestDTO tasksRequestDTO = new VouchersTasksRequestDTO();
        tasksRequestDTO.setVouchersTaskIds(voucherTaskIdList);
        tasksRequestDTO.setCompanyId(companyId);
        tasksRequestDTO.setPageNo(pageNo == null ? 1 : pageNo);
        tasksRequestDTO.setPageSize(pageSize == null ? 10 : pageSize);
        ResponsePage<VouchersTask> responsePage = vouchersTaskHandleService.queryCanInvoiceVoucherTasks(tasksRequestDTO);
        return getVouchersTaskRespDTOS(responsePage);
    }

    @Override
    public List<VouchersTaskRespDTO> queryVouchersTask(VouchersTasksReqRPCDTO tasksReqRPCDTO) throws FinhubException {
        VouchersTasksRequestDTO tasksRequestDTO = new VouchersTasksRequestDTO();
        BeanUtils.copyProperties(tasksReqRPCDTO, tasksRequestDTO);
        ResponsePage<VouchersTask> responsePage = vouchersTaskHandleService.queryVoucherTaskByPage(tasksRequestDTO);
        return getVouchersTaskRespDTOS(responsePage);
    }

    @Override
    public ResponsePage<VouchersTaskDetailsRespDTO> queryVouchersTaskDetails(String companyId, List<String> voucherTaskIdList, Integer pageNo, Integer pageSize) throws FinhubException {
        PageBean pageBean = new PageBean(pageNo, pageSize);
        ResponsePage<VouchersTaskDetailsRespDTO> responsePage = vouchersTaskHandleService.selectVouchersByTaskId(companyId, voucherTaskIdList, pageBean);
        return responsePage;
    }

    @Override
    public ResponsePage<VouchersTaskDetailsRespDTO> queryVouchersGrantTaskDetails(String companyId, List<String> voucherTaskIdList, Integer pageNo, Integer pageSize) throws FinhubException {
        PageBean pageBean = new PageBean(pageNo, pageSize);
        ResponsePage<VouchersTaskDetailsRespDTO> responsePage = vouchersTaskHandleService.selectGrantVouchersByTaskId(companyId, voucherTaskIdList, pageBean);
        return responsePage;
    }

    @Override
    public VouchersInvoiceTaskStatisticsVO queryVouchersInvoiceTasksStatistics(String companyId, List<String> voucherTaskIdList) throws FinhubException {
        return vouchersTaskHandleService.getVouchersInvoiceTaskStatistics(companyId, voucherTaskIdList);
    }

    private List<VouchersTaskRespDTO> getVouchersTaskRespDTOS(ResponsePage<VouchersTask> responsePage) {
        List<VouchersTaskRespDTO> dataList = new ArrayList<>();
        responsePage.getDataList().forEach(vouchersTask -> {
            VouchersTaskRespDTO dto = new VouchersTaskRespDTO();
            BeanUtils.copyProperties(vouchersTask, dto);
            dto.setStatusName(VoucherTaskStatus.getMsgFromValue(dto.getStatus()));
            dto.setVouchersOperationAmount(vouchersTask.getVouchersOperationAmount().subtract(vouchersTask.getTotalRecoveryAmount()));
            dataList.add(dto);
        });
        return dataList;
    }
}
