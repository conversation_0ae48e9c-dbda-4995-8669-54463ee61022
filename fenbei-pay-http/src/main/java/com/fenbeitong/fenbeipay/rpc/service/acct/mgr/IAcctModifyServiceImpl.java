package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctGeneralService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCompanyMainReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCreateBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCreateMainReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctUpdateRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctModifyService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022年11月25日 15:54
 * @description
 */
@Service("iAcctModifyService")
public class IAcctModifyServiceImpl implements IAcctModifyService {

    @Autowired
    protected UAcctGeneralService uAcctGeneralService;

    @Autowired
    protected UAcctCommonService uAcctCommonService;

    @Autowired
    protected UAcctCompanyMainService uAcctCompanyMainService;

    @Override
    public AcctUpdateRespDTO updateBankAccountName(AcctCreateBankReqDTO createBankReqDTO) throws FinhubException {
        FinhubLogger.info("【企业信息修改-修改银行账户名称-同步账户】updateBankAccountName 参数：{}", JsonUtils.toJson(createBankReqDTO));
        ValidateUtils.validate(createBankReqDTO);
        AcctUpdateRespDTO respDTO = new AcctUpdateRespDTO();
        try {
            //校验字段
            ValidateUtils.validate(createBankReqDTO);
            // 修改余额账户信息
            String companyMainId = uAcctGeneralService.updateAccountByBank(createBankReqDTO);
            // 修改对公付款账户信息
            uAcctCommonService.updatePublicAcctByBank(createBankReqDTO);
            // 修改主体信息
            AcctCreateMainReqDTO createMainReqDTO = createBankReqDTO.getAcctCreateMainReqDTO();
            AcctCompanyMainReqDTO companyMainReqDTO = AcctCompanyMainReqDTO.builder().build();
            BeanUtils.copyProperties(createMainReqDTO, companyMainReqDTO);
            companyMainReqDTO.setCompanyMainId(companyMainId);
            if(BankNameEnum.isCitic(createBankReqDTO.getBankName())){
                companyMainReqDTO.setBankBusinessName("北京分贝通科技有限公司待结算户-" + companyMainReqDTO.getBusinessName());
            } else if (BankNameEnum.isCgb(createBankReqDTO.getBankName())) {
                companyMainReqDTO.setBankBusinessName(null);
            } else {
                companyMainReqDTO.setBankBusinessName(companyMainReqDTO.getBusinessName());
            }
            AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.updateAcctCompanyMain(companyMainReqDTO);
            BeanUtils.copyProperties(createBankReqDTO, respDTO);
            return respDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【企业信息修改-修改银行账户名称-同步账户】updateBankAccountName 参数：{}", createBankReqDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【企业信息修改-修改银行账户名称-同步账户】updateBankAccountName 参数：{}", createBankReqDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【企业信息修改-修改银行账户名称-同步账户】updateBankAccountName 参数：{}", createBankReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctUpdateRespDTO updateCompanyMainInfo(AcctCreateBankReqDTO createBankReqDTO) throws FinhubException {
        FinhubLogger.info("【企业信息修改-修改账户其他信息-同步主体】updateCompanyMainInfo 参数：{}", JsonUtils.toJson(createBankReqDTO));
        AcctUpdateRespDTO respDTO = new AcctUpdateRespDTO();
        try {
            //校验字段
            ValidateUtils.validate(createBankReqDTO);
            AccountGeneral accountGeneral = uAcctGeneralService.findByCompanyIdAndBankNameAndBankAcctId(createBankReqDTO.getCompanyId(), createBankReqDTO.getBankName(), createBankReqDTO.getBankAcctId());
            if (ObjUtils.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_ENTERPRISE_NOT_EXIST);
            }
            AcctCreateMainReqDTO createMainReqDTO = createBankReqDTO.getAcctCreateMainReqDTO();
            ValidateUtils.validate(createMainReqDTO);
            AcctCompanyMainReqDTO companyMainReqDTO = AcctCompanyMainReqDTO.builder().build();
            BeanUtils.copyProperties(createMainReqDTO, companyMainReqDTO);
            companyMainReqDTO.setCompanyMainId(accountGeneral.getCompanyMainId());
            AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.updateAcctCompanyMain(companyMainReqDTO);
            BeanUtils.copyProperties(createBankReqDTO, respDTO);
            return respDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【企业信息修改-修改账户其他信息-同步主体】updateCompanyMainInfo 参数：{}", createBankReqDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【企业信息修改-修改账户其他信息-同步主体】updateCompanyMainInfo 参数：{}", createBankReqDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【企业信息修改-修改账户其他信息-同步主体】updateCompanyMainInfo 参数：{}", createBankReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }
}
