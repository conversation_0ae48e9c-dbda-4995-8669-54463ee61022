package com.fenbeitong.fenbeipay.rpc.service.acct.trade;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.service.AcctOverseaService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOverseaRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.OverseaAcctRefundAmountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.OverseaCardApplyAmountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubOperationRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.trade.AcctOverseaCardService;
import com.fenbeitong.fenbeipay.dto.oversea.AcctOversea;
import com.fenbeitong.finhub.common.utils.FinhubLogger;

/**
 * <AUTHOR>
 * @date 2024/08/05
 */
@Service("acctOverseaCardService")
public class AcctOverseaCardServiceImpl implements AcctOverseaCardService {

	@Autowired
	private AcctOverseaService acctOverseaService;
	
	/* (non-Javadoc)
	 * @see com.fenbeitong.fenbeipay.api.service.acct.trade.AcctOverseaCardService#getCompanyOverseaAcctCard(java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public List<AcctOverseaRespDTO> queryCompanyOverseaAcctInfor(String companyId, String platformName, String bankName) {
		List<AcctOversea> accts = acctOverseaService.queryCompanyOverseaAcctInfor(companyId, bankName);
		if (CollectionUtils.isEmpty(accts)) {
			return Collections.emptyList();
		}
		
		List<AcctOverseaRespDTO> resp = accts.stream().map(oa -> {
			AcctOverseaRespDTO dto = new AcctOverseaRespDTO();
			BeanUtils.copyProperties(oa, dto);
			return dto;
		}).collect(Collectors.toList());
		
		return resp;
	}

	/* (non-Javadoc)
	 * @see com.fenbeitong.fenbeipay.api.service.acct.trade.AcctOverseaCardService#issueCredit(com.fenbeitong.fenbeipay.api.model.dto.na.req.OverseaCardApplyAmountReqDTO)
	 */
	@Override
	public AccountSubOperationRespRPCDTO issueCredit(OverseaCardApplyAmountReqDTO request) {
		FinhubLogger.info("海外卡账户额度下发请求->{}", JSON.toJSONString(request));
		AccountSubOperationRespRPCDTO resp = acctOverseaService.issueCredit(request);
		return resp;
	}

	/* (non-Javadoc)
	 * @see com.fenbeitong.fenbeipay.api.service.acct.trade.AcctOverseaCardService#recoverCredit(com.fenbeitong.fenbeipay.api.model.dto.na.req.OverseaAcctRefundAmountReqDTO)
	 */
	@Override
	public AccountSubOperationRespRPCDTO recoverCredit(OverseaAcctRefundAmountReqDTO request) {
		FinhubLogger.info("海外卡账户额度回收请求->{}", JSON.toJSONString(request));
		AccountSubOperationRespRPCDTO resp = acctOverseaService.recoverCredit(request);
		return resp;
	}

}
