package com.fenbeitong.fenbeipay.rpc.service.acct.trade;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctReimbursementService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctRefundReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctReimbursementReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOperationRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctReimbursementService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.rpc.service.acct.IAcctAbstractService;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/27
 */
@Service("iAcctReimbursementService")
public class AcctReimbursementServiceImpl extends IAcctAbstractService implements IAcctReimbursementService {
    @Autowired
    UAcctReimbursementService uAcctReimbursementService;
    @Autowired
    DingDingMsgService dingDingMsgService;
    @Override
    public AcctOperationRespDTO consume(AcctReimbursementReqDTO acctReimbursementReqDTO) {
        FinhubLogger.info("【报销账户付款】acctConsumeReqDTO 参数：{}", JsonUtils.toJson(acctReimbursementReqDTO));
        try {
            //校验字段
//            acctConsumeReqDTO.checkReq(acctConsumeReqDTO);
            return uAcctReimbursementService.consume(acctReimbursementReqDTO);
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【报销账户付款系统异常】余额不足 accountRecharge 参数：{}", JsonUtils.toJson(acctReimbursementReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinPayException e) {
            FinhubLogger.error("【报销账户付款系统异常】accountRecharge 参数：{}", JsonUtils.toJson(acctReimbursementReqDTO), e);
            String msgError = "【报销账户付款】异常:" + acctReimbursementReqDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【报销账户付款】accountRecharge 参数：{}", JsonUtils.toJson(acctReimbursementReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【报销账户付款系统异常】accountRecharge 参数：{}", JsonUtils.toJson(acctReimbursementReqDTO), e);
            String msgError = "【报销账户付款】系统异常:" + acctReimbursementReqDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctOperationRespDTO refund(AcctRefundReqDTO acctRefundReqDTO) {
        FinhubLogger.info("【报销账户付款】acctConsumeReqDTO 参数：{}", acctRefundReqDTO);
        try {
            //校验字段
//            acctConsumeReqDTO.checkReq(acctConsumeReqDTO);
            return uAcctReimbursementService.refund(acctRefundReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【报销账户付款系统异常】accountRecharge 参数：{}", JsonUtils.toJson(acctRefundReqDTO), e);
            String msgError = "【报销账户付款】异常:" + acctRefundReqDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【报销账户付款】accountRecharge 参数：{}", JsonUtils.toJson(acctRefundReqDTO), e);
            String msgError = "【报销账户付款】【请求参数】异常:" + acctRefundReqDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【报销账户付款系统异常】accountRecharge 参数：{}", JsonUtils.toJson(acctRefundReqDTO), e);
            String msgError = "【报销账户付款】系统异常:" + acctRefundReqDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctOperationRespDTO dishonoured(AcctRefundReqDTO acctRefundReqDTO) {
        FinhubLogger.info("【报销账户付款】acctConsumeReqDTO 参数：{}", acctRefundReqDTO);
        try {
            //校验字段
//            acctConsumeReqDTO.checkReq(acctConsumeReqDTO);
            return uAcctReimbursementService.dishonoured(acctRefundReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【报销账户付款系统异常】accountRecharge 参数：{}", JsonUtils.toJson(acctRefundReqDTO), e);
            String msgError = "【报销账户付款】异常:" + acctRefundReqDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【报销账户付款】accountRecharge 参数：{}", JsonUtils.toJson(acctRefundReqDTO), e);
            String msgError = "【报销账户付款】【请求参数】异常:" + acctRefundReqDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【报销账户付款系统异常】accountRecharge 参数：{}", JsonUtils.toJson(acctRefundReqDTO), e);
            String msgError = "【报销账户付款】系统异常:" + acctRefundReqDTO.getBankAccountNo() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }
}
