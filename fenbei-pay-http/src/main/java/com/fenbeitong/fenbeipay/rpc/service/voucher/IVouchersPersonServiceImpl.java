package com.fenbeitong.fenbeipay.rpc.service.voucher;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherFlowType;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersFlowReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersWithdrawRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VoucherCostAttributionRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersFlow4ExportRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersOperationFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersRecoveryRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersOperationService;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersSearchService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderCostAttributionService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersOperationFlowService;
import com.fenbeitong.finhub.common.constant.VoucherCostSignEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: java类作用描述
 * @ClassName: IVouchersPersonServiceImpl
 * @Author: zhangga
 * @CreateDate: 2019/4/22 11:34 AM
 * @UpdateUser:
 * @UpdateDate: 2019/4/22 11:34 AM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iVouchersPersonService")
public class IVouchersPersonServiceImpl implements IVouchersPersonService {

    @Autowired
    private IVouchersSearchService iVouchersSearchService;
    @Autowired
    private IVouchersOperationService iVouchersOperationService;
    @Autowired
    private CashierOrderCostAttributionService cashierOrderCostAttributionService;
    @Autowired
    private VouchersOperationFlowService vouchersOperationFlowService;

    @Override
    public boolean queryIsHaveUsableVouchers(String companyId, String employeeId) throws FinhubException {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersSearchService.queryIsHaveUsableVouchers(companyId, employeeId);
        } catch (FinPayException e) {
            FinhubLogger.error("【查询员工是否有可用分贝券异常】，员工Id：{}，公司Id：{}", employeeId, companyId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【查询员工是否有可用分贝券异常】，员工Id：{}，公司Id：{}", employeeId, companyId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【查询员工是否有可用分贝券异常】，员工Id：{}，公司Id：{}", employeeId, companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean withdrawalVouchersByEmployeeIds(VouchersWithdrawRPCDTO vouchersWithdrawRPCDTO) throws FinhubException {
        ValidateUtils.validate(vouchersWithdrawRPCDTO);
        if (null == vouchersWithdrawRPCDTO || ObjUtils.isEmpty(vouchersWithdrawRPCDTO.getEmployeeIds())) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersOperationService.withdrawalVouchersByEmployeeIds(vouchersWithdrawRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【撤回员工可用分贝券异常】：{}", vouchersWithdrawRPCDTO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【撤回员工可用分贝券异常】：{}", vouchersWithdrawRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【撤回员工可用分贝券异常】：{}", vouchersWithdrawRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public ResponsePage<VouchersRecoveryRespRPCDTO> queryAdvanceInvoiceRecoveryVoucher(VouchersFlowReqRPCDTO vouchersFlowReqRPCDTO) throws FinhubException {
        if (ObjUtils.isBlank(vouchersFlowReqRPCDTO.getCompanyId())) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "companyId 不能为空");
        }
        return iVouchersSearchService.queryAdvanceInvoiceRecoveryVoucher(vouchersFlowReqRPCDTO);
    }

    @Override
    public ResponsePage<VouchersOperationFlowRespRPCDTO> queryVoucherFlow(VouchersFlowReqRPCDTO vouchersFlowReqRPCDTO) throws FinhubException {
        ResponsePage<VouchersOperationFlowRespRPCDTO> responsePage = iVouchersSearchService.queryVoucherFlow(vouchersFlowReqRPCDTO);
        List<VouchersOperationFlowRespRPCDTO> dataList = responsePage.getDataList();
        List<String> fbOrderIds = dataList.stream().map(VouchersOperationFlowRespRPCDTO::getFbOrderId).distinct().collect(Collectors.toList());
        Map<String, List<CashierOrderCostAttribution>> costMap = cashierOrderCostAttributionService.getCashierOrderCostAttributionByFbOrderIds(fbOrderIds);
        if (ObjUtils.isEmpty(costMap)) {
            return responsePage;
        }
        dataList.forEach(data -> {
            List<CashierOrderCostAttribution> costAttributions = costMap.get(data.getFbOrderId());
            if (ObjUtils.isNotEmpty(costAttributions)) {
                List<VoucherCostAttributionRPCDTO> voucherCostAttributions = new ArrayList<>();
                costAttributions.forEach(attr -> {
                    VoucherCostAttributionRPCDTO dto = new VoucherCostAttributionRPCDTO();
                    BeanUtils.copyProperties(attr, dto);
                    voucherCostAttributions.add(dto);
                });
                data.setVoucherCostAttributions(voucherCostAttributions);
            }
        });
        return responsePage;
    }

    @Override
    public VouchersFlow4ExportRespRPCDTO queryVoucherFlow4Export(VouchersFlowReqRPCDTO vouchersFlowReqRPCDTO) throws FinhubException {
        FinhubLogger.info("stereo分贝券流水导出参数：{}", JSONObject.toJSONString(vouchersFlowReqRPCDTO));
        return iVouchersSearchService.queryVoucherFlow4Export(vouchersFlowReqRPCDTO);
    }

    @Override
    public BigDecimal queryAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(String companyId, Integer accountSubType, Integer accountModelType) {
        if (ObjUtils.isBlank(companyId)) {
            return BigDecimal.ZERO;
        }
        return iVouchersSearchService.queryAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(companyId, accountSubType, accountModelType);
    }

    @Override
    public List<VouchersOperationFlowRespRPCDTO> queryVoucherFlowList(List<String> vouchersFlowIds) throws FinhubException {
        List<VouchersOperationFlowRespRPCDTO> dataList = iVouchersSearchService.queryVoucherFlowList(vouchersFlowIds);
        if (ObjUtils.isEmpty(dataList)) {
            return dataList;
        }
        List<String> fbOrderIds = dataList.stream().map(VouchersOperationFlowRespRPCDTO::getFbOrderId).distinct().collect(Collectors.toList());
        Map<String, List<CashierOrderCostAttribution>> costMap = cashierOrderCostAttributionService.getCashierOrderCostAttributionByFbOrderIds(fbOrderIds);
        if (ObjUtils.isEmpty(costMap)) {
            return dataList;
        }
        dataList.forEach(data -> {
            List<CashierOrderCostAttribution> costAttributions = costMap.get(data.getFbOrderId());
            Integer costAttributionSign = data.getCostAttributionSign();
            if (ObjUtils.isNotEmpty(costAttributions) && costAttributionSign != null && costAttributionSign == VoucherCostSignEnum.HAS_COST_ATTRIBUTION.getValue()) {
                List<VoucherCostAttributionRPCDTO> voucherCostAttributions = new ArrayList<>();
                costAttributions.forEach(attr -> {
                    VoucherCostAttributionRPCDTO dto = new VoucherCostAttributionRPCDTO();
                    BeanUtils.copyProperties(attr, dto);
                    voucherCostAttributions.add(dto);
                });
                data.setVoucherCostAttributions(voucherCostAttributions);
            }
        });
        return dataList;
    }

    @Override
    public List<VouchersOperationFlowRespRPCDTO> queryVoucherFlowByBizNo(String bizNo, Integer operationType) throws FinhubException {
        List<VouchersOperationFlow> vouchersOperationFlows = vouchersOperationFlowService.queryVouchersFlowByBizNoAndType(bizNo,operationType);
        if (CollectionUtils.isNotEmpty(vouchersOperationFlows)){
            List<VouchersOperationFlowRespRPCDTO> vouchersOperationFlowRespRPCDTOS = Lists.newArrayList();
            for (VouchersOperationFlow vouchersOperationFlow:vouchersOperationFlows){
                VouchersOperationFlowRespRPCDTO vouchersOperationFlowRespRPCDTO = new VouchersOperationFlowRespRPCDTO();
                BeanUtils.copyProperties(vouchersOperationFlow,vouchersOperationFlowRespRPCDTO);
                vouchersOperationFlowRespRPCDTOS.add(vouchersOperationFlowRespRPCDTO);
            }
            return vouchersOperationFlowRespRPCDTOS;
        }
        return null;
    }

    @Override
    public List<VouchersOperationFlowRespRPCDTO> queryVoucherFlowByReBizNo(String bizNo, String reBizNo, Integer operationType) throws FinhubException {
        List<VouchersOperationFlow> vouchersOperationFlows = vouchersOperationFlowService.queryVouchersFlowByBizNoAndType(bizNo,reBizNo,operationType);
        if (CollectionUtils.isNotEmpty(vouchersOperationFlows)){
            List<VouchersOperationFlowRespRPCDTO> vouchersOperationFlowRespRPCDTOS = Lists.newArrayList();
            for (VouchersOperationFlow vouchersOperationFlow:vouchersOperationFlows){
                FinhubLogger.info("queryVoucherFlowByReBizNo isRefund:{}", VoucherFlowType.isRefund(vouchersOperationFlow.getType()));
                VouchersOperationFlowRespRPCDTO vouchersOperationFlowRespRPCDTO = new VouchersOperationFlowRespRPCDTO();
                BeanUtils.copyProperties(vouchersOperationFlow,vouchersOperationFlowRespRPCDTO);
                vouchersOperationFlowRespRPCDTOS.add(vouchersOperationFlowRespRPCDTO);
            }
            return vouchersOperationFlowRespRPCDTOS;
        }

        return null;
    }
}
