package com.fenbeitong.fenbeipay.rpc.service.acctdech;

import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyBindCardService;
import com.fenbeitong.fenbeipay.api.constant.enums.acctdech.AcctCompanyBindCardStatusEnum;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.AcctCompanyBindCardQueryDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.AcctCompanyBindCardReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.AcctCompanyBindCardUpdateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.AddAcctCompanyBindCardReqDTO;
import com.fenbeitong.fenbeipay.api.service.acctdech.IAcctCompanyBindCardService;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyBindCard;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountGeneralService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-09-19 17:24:57
 * @Version 1.0
 **/
@Service("iAcctCompanyBindCardService")
public class IAcctCompanyBindCardServiceImpl implements IAcctCompanyBindCardService {

    @Autowired
    private AcctCompanyBindCardService acctCompanyBindCardService;
    @Autowired
    private UAccountGeneralService uAccountGeneralService;

    @Override
    public Boolean saveAcctCompanyBindCard(AddAcctCompanyBindCardReqDTO acctCompanyBindCardReqDTO) {
        AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(acctCompanyBindCardReqDTO.getCompanyId(), acctCompanyBindCardReqDTO.getBankName(), acctCompanyBindCardReqDTO.getBankAcctId());
        if (ObjUtils.isEmpty(accountGeneral)) {
            return false;
        }
        AcctCompanyBindCard existAcctCompanyBindCard = acctCompanyBindCardService.queryByCompanyMainIdAndBindCardNo(accountGeneral.getCompanyMainId(), acctCompanyBindCardReqDTO.getBankCardNo());
        if (ObjUtils.isNull(existAcctCompanyBindCard)) {
            AcctCompanyBindCard acctCompanyBindCard = new AcctCompanyBindCard();
            BeanUtils.copyProperties(acctCompanyBindCardReqDTO, acctCompanyBindCard);
            acctCompanyBindCard.setCompanyId(accountGeneral.getCompanyId());
            acctCompanyBindCard.setCompanyName(accountGeneral.getCompanyName());
            acctCompanyBindCard.setCompanyMainId(accountGeneral.getCompanyMainId());
            return acctCompanyBindCardService.saveAcctCompanyBindCard(acctCompanyBindCard);
        }else {
            return updateStatusByBindCardId(AcctCompanyBindCardStatusEnum.BIND.getCode(), existAcctCompanyBindCard.getBindCardId());
        }
    }

    @Override
    public Boolean updateStatusByBindCardId(Integer status, String bindCardId) {
        return acctCompanyBindCardService.updateStatusByBindCardId(status, bindCardId);
    }

    @Override
    public List<AcctCompanyBindCardReqDTO> queryByCompanyMainId(String companyMainId) {
        List<AcctCompanyBindCard> acctCompanyBindCardList = acctCompanyBindCardService.queryByCompanyMainId(companyMainId);
        if (CollectionUtils.isEmpty(acctCompanyBindCardList)) {
            return Lists.newArrayList();
        }
        return acctCompanyBindCardList.stream().map(x->{
            AcctCompanyBindCardReqDTO acctCompanyBindCardReqDTO = new AcctCompanyBindCardReqDTO();
            BeanUtils.copyProperties(x, acctCompanyBindCardReqDTO);
            return acctCompanyBindCardReqDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AcctCompanyBindCardReqDTO> queryByCondition(AcctCompanyBindCardQueryDTO queryDTO) {
        AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(queryDTO.getCompanyId(), queryDTO.getBankName(), queryDTO.getBankAccountNo());
        if (ObjUtils.isEmpty(accountGeneral)) {
            return Lists.newArrayList();
        }
        return queryByCompanyMainId(accountGeneral.getCompanyMainId());
    }

    @Override
    public Boolean updateByBindCardId(AcctCompanyBindCardUpdateReqDTO acctCompanyBindCardUpdateReqDTO) {
        AcctCompanyBindCard acctCompanyBindCard = new AcctCompanyBindCard();
        BeanUtils.copyProperties(acctCompanyBindCardUpdateReqDTO, acctCompanyBindCard);
        return acctCompanyBindCardService.updateByBindCardId(acctCompanyBindCard);
    }

}
