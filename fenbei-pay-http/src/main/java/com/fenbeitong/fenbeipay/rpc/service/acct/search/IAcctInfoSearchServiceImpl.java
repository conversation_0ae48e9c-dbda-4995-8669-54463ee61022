package com.fenbeitong.fenbeipay.rpc.service.acct.search;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.service.*;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenChangeType;
import com.fenbeitong.fenbeipay.api.constant.enums.accountpublic.AccountPublicStatus;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderRootTypeEnum;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctInfoDetailDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctInfoRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.ElectronicAccountRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierSearchDetailReqRPCVo;
import com.fenbeitong.fenbeipay.api.service.acct.search.IAcctInfoSearchService;
import com.fenbeitong.fenbeipay.cashier.manager.CashierOrderSettlementManager;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezenFlow;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.nf.service.FundFreezenFlowService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service("iAcctInfoSearchService")
public class IAcctInfoSearchServiceImpl implements IAcctInfoSearchService {

    @Autowired
    private AccountGeneralFlowService accountGeneralFlowService;

    @Autowired
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Autowired
    private AcctBusinessDebitFlowService acctBusinessDebitFlowService;

    @Autowired
    private AcctIndividualCreditFlowService acctIndividualCreditFlowService;

    @Autowired
    private AcctIndividualDebitFlowService acctIndividualDebitFlowService;

    @Autowired
    private FundFreezenFlowService fundFreezenFlowService;

    @Autowired
    private AcctCompanyMainService acctCompanyMainService;

    @Autowired
    private BankAcctService bankAcctService;

    @Autowired
    private AcctBusinessDebitService acctBusinessDebitService;

    @Autowired
    private AcctBusinessCreditService acctBusinessCreditService;

    @Autowired
    private AcctIndividualCreditService acctIndividualCreditService;

    @Autowired
    private AcctIndividualDebitService acctIndividualDebitService;

    @Autowired
    private AcctPublicService acctPublicService;

    @Autowired
    private AcctCompanyCardService acctCompanyCardService;

    @Autowired
    private AcctReimbursementService acctReimbursementService;
    @Autowired
    private CashierOrderSettlementManager cashierOrderSettlementManager;

    @Override
    public AcctInfoRespDTO queryByBizNoAndTxnId(String bizNo, String txnId) {
        AcctInfoRespDTO acctInfoRespDTO = queryByBizNo(bizNo);
        if (StringUtils.isEmpty(txnId)) {
            return acctInfoRespDTO;
        }
        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryBySyncBankTransNo(txnId);
        if (ObjUtils.isNotEmpty(fundFreezenFlow)) {
            acctInfoRespDTO.setTradeType(getTradeTypeByFreezenOptType(fundFreezenFlow.getOperationType()));
            acctInfoRespDTO.setOperationType(fundFreezenFlow.getOperationType());
            acctInfoRespDTO.setOperationTypeDesc(fundFreezenFlow.getOperationTypeDesc());
            acctInfoRespDTO.setAccountType(fundFreezenFlow.getAccountModel());
            acctInfoRespDTO.setAccountSubType(fundFreezenFlow.getAccountSubType());
            acctInfoRespDTO.setBankTransNo(fundFreezenFlow.getBankTransNo());
            acctInfoRespDTO.setBassTxnId(fundFreezenFlow.getSyncBankTransNo());
            acctInfoRespDTO.setCompanyId(fundFreezenFlow.getCompanyId());
            acctInfoRespDTO.setFrozenFlowId(fundFreezenFlow.getFreezenFlowId());
            // TODO 银行补账取值
            // 收付款账号
            acctInfoRespDTO.setPayAccountNo(fundFreezenFlow.getBankAccountNo());
            acctInfoRespDTO.setReceiveAccountName(fundFreezenFlow.getTargetBankName());
            acctInfoRespDTO.setReceiveAccountNo(fundFreezenFlow.getTargetBankAcctId());
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(fundFreezenFlow.getCompanyId(), fundFreezenFlow.getCompanyMainId(), fundFreezenFlow.getBankName());
            if (!ObjUtils.isNull(acctCompanyMain)) {
                acctInfoRespDTO.setPayAccountName(acctCompanyMain.getBusinessName());
            }
            acctInfoRespDTO.setAccountId(fundFreezenFlow.getAccountId());
        }
        return acctInfoRespDTO;
    }

    @Override
    public String queryConsumeBankTradeId(String accountId, Integer tradeType, String cashierTxnId) {
        AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryConsumeInfo(accountId,tradeType,cashierTxnId);
        if (ObjUtils.isNotEmpty(acctBusinessDebitFlow) && StringUtils.isNotEmpty(acctBusinessDebitFlow.getBankTransNo())) {
            return acctBusinessDebitFlow.getBankTransNo();
        }
        AcctBusinessCreditFlow acctBusinessCreditFlow = acctBusinessCreditFlowService.queryConsumeInfo(accountId,tradeType,cashierTxnId);
        if (ObjUtils.isNotEmpty(acctBusinessCreditFlow) && StringUtils.isNotEmpty(acctBusinessCreditFlow.getBankTransNo())) {
            return acctBusinessCreditFlow.getBankTransNo();
        }
        return null;
    }

    @Override
    public ElectronicAccountRespDTO queryAccountInfo(String companyId, String accountId) {
        ElectronicAccountRespDTO respDTO = new ElectronicAccountRespDTO();
        if (Strings.isNullOrEmpty(accountId)) {
            // 防止accountId传""，导致查不到数据的情况
            accountId = null;
        }
        // 商务消费
        List<AcctBusinessDebit> businessDebitList = acctBusinessDebitService.findAccountInfo(companyId, accountId);
        List<AcctBusinessCredit> businessCreditList = acctBusinessCreditService.findAccountInfo(companyId, accountId);
        respDTO.setBusinessAccount(buildBusinessAccount(businessDebitList, businessCreditList));

        // 补助福利
        List<AcctIndividualDebit> acctIndividualDebitList = acctIndividualDebitService.findAccountInfo(companyId, accountId);
        List<AcctIndividualCredit> acctIndividualCreditList = acctIndividualCreditService.findAccountInfo(companyId, accountId);
        respDTO.setIndividualAccount(buildIndividualAccount(acctIndividualDebitList, acctIndividualCreditList));

        // 员工报销
        List<AcctReimbursement> acctReimbursementList = acctReimbursementService.findAccountInfo(companyId, accountId);
        respDTO.setReimbursementAccount(buildReimbursementAccount(acctReimbursementList));

        // 对公付款
        List<AccountPublic> accountPublicList = acctPublicService.findAccountInfo(companyId, accountId);
        respDTO.setPublicAccount(buildPublicAccount(accountPublicList));

        // 备用金
        List<AcctCompanyCard> acctCompanyCardList = acctCompanyCardService.findAccountInfo(companyId, accountId);
        respDTO.setImprestAccount(buildImprestAccount(acctCompanyCardList));

        respDTO.setAccountType(respDTO.getAccountType(accountId));

        return respDTO;
    }

    @Override
    public AcctInfoDetailDTO queryAccountByFbOrderId(String employeeId,String fbOrderId) {
        FinhubLogger.info("queryAccountByFbOrderId.req==={}",fbOrderId);
        CheckUtils.create()
                .addCheckBlank(fbOrderId,"单号是空！")
                .check();
        CashierSearchDetailReqRPCVo cashierSearchDetailReqRPCVo = new CashierSearchDetailReqRPCVo();
        cashierSearchDetailReqRPCVo.setFbOrderId(fbOrderId);
/*        CashierSearchMultipleTradeDetailRPCDTO detailRPCDTO = iCashierSearchOrderService.searchMultiplePayDetail(cashierSearchDetailReqRPCVo);
        if (ObjUtils.isEmpty(detailRPCDTO)) {
            return null;
        }*/

        CashierOrderSettlement cashierOrder = cashierOrderSettlementManager.selectCashierSettlementByFbOrderIdAndEmpId(fbOrderId,"" , employeeId, OrderRootTypeEnum.ROOT_ORDER.getKey());
        if (ObjUtils.isEmpty(cashierOrder)) {
            return null;
        }
        AcctInfoDetailDTO result = new AcctInfoDetailDTO();
        Integer accountSubType = cashierOrder.getConsumerAccountSubType();
        String accountSubId = cashierOrder.getAccountSubId();
        Integer accountModel = cashierOrder.getAccountModel();
        if(FundAccountSubType.isComCardAccount(accountSubType)){
            AcctCompanyCard acctCompanyCard = acctCompanyCardService.queryByAccountId(accountSubId);
            if (ObjUtils.isEmpty(acctCompanyCard)) {
                return null;
            }
            result.setBankAccountNo(acctCompanyCard.getBankAccountNo());
            result.setBankName(acctCompanyCard.getBankName());
            result.setBankAcctId(acctCompanyCard.getBankAcctId());
            result.setCompanyId(acctCompanyCard.getCompanyId());
            result.setCompanyName(acctCompanyCard.getCompanyName());
        }else {
            if (StringUtils.isBlank(accountSubId) || accountModel == null) {
                return null;
            }
            result.setAccountId(accountSubId);
            result.setAccountModel(accountModel);
            if (FundAccountModelType.isRecharge(accountModel)) {
                AcctBusinessDebit acctBusinessDebit = acctBusinessDebitService.getAccountByAccountId(accountSubId);
                if (ObjUtils.isNotEmpty(acctBusinessDebit)) {
                    result.setBankAccountNo(acctBusinessDebit.getBankAccountNo());
                    result.setBankName(acctBusinessDebit.getBankName());
                    result.setBankAcctId(acctBusinessDebit.getBankAcctId());
                    result.setCompanyId(acctBusinessDebit.getCompanyId());
                    result.setCompanyName(acctBusinessDebit.getCompanyName());
                }
            }else {
                AcctBusinessCredit acctBusinessCredit = acctBusinessCreditService.findAccountByAccountId(accountSubId);
                if (ObjUtils.isNotEmpty(acctBusinessCredit)) {
                    result.setBankAccountNo(acctBusinessCredit.getBankAccountNo());
                    result.setBankName(acctBusinessCredit.getBankName());
                    result.setBankAcctId(acctBusinessCredit.getBankAcctId());
                    result.setCompanyId(acctBusinessCredit.getCompanyId());
                    result.setCompanyName(acctBusinessCredit.getCompanyName());
                }
            }
        }
        FinhubLogger.info("queryAccountByFbOrderId.result==={}", JSON.toJSON(result));
        return result;
    }

    private List<AcctInfoDetailDTO> buildBusinessAccount(List<AcctBusinessDebit> businessDebitList, List<AcctBusinessCredit> businessCreditList) {
        List<AcctInfoDetailDTO> list = Lists.newArrayList();
        if (!ObjUtils.isNull(businessDebitList)) {
            businessDebitList.stream().forEach(b -> {
                AcctInfoDetailDTO acctInfoDetailDTO = new AcctInfoDetailDTO();
                acctInfoDetailDTO.setAccountId(b.getAccountId());
                acctInfoDetailDTO.setAccountModel(b.getAccountModel());
                acctInfoDetailDTO.setBankAccountNo(b.getBankAccountNo());
                acctInfoDetailDTO.setBankName(b.getBankName());
                acctInfoDetailDTO.setBankAcctId(b.getBankAcctId());
                acctInfoDetailDTO.setCompanyId(b.getCompanyId());
                acctInfoDetailDTO.setCompanyMainId(b.getCompanyMainId());
                acctInfoDetailDTO.setCompanyName(b.getCompanyName());
                acctInfoDetailDTO.setAccountStatus(b.getAccountStatus());
                buildCompanyMain(acctInfoDetailDTO, b.getCompanyId(), b.getCompanyMainId(), b.getBankName());
                list.add(acctInfoDetailDTO);
            });
        }
        if (!ObjUtils.isNull(businessCreditList)) {
            businessCreditList.stream().forEach(b -> {
                AcctInfoDetailDTO acctInfoDetailDTO = new AcctInfoDetailDTO();
                acctInfoDetailDTO.setAccountId(b.getAccountId());
                acctInfoDetailDTO.setAccountModel(b.getAccountModel());
                acctInfoDetailDTO.setBankAccountNo(b.getBankAccountNo());
                acctInfoDetailDTO.setBankName(b.getBankName());
                acctInfoDetailDTO.setBankAcctId(b.getBankAcctId());
                acctInfoDetailDTO.setCompanyId(b.getCompanyId());
                acctInfoDetailDTO.setCompanyMainId(b.getCompanyMainId());
                acctInfoDetailDTO.setCompanyName(b.getCompanyName());
                acctInfoDetailDTO.setAccountStatus(b.getAccountStatus());
                buildCompanyMain(acctInfoDetailDTO, b.getCompanyId(), b.getCompanyMainId(), b.getBankName());
                list.add(acctInfoDetailDTO);
            });
        }
        return list;
    }

    private List<AcctInfoDetailDTO> buildIndividualAccount(List<AcctIndividualDebit> acctIndividualDebitList, List<AcctIndividualCredit> acctIndividualCreditList) {
        List<AcctInfoDetailDTO> list = Lists.newArrayList();
        if (!ObjUtils.isNull(acctIndividualDebitList)) {
            acctIndividualDebitList.stream().forEach(b -> {
                AcctInfoDetailDTO acctInfoDetailDTO = new AcctInfoDetailDTO();
                acctInfoDetailDTO.setAccountId(b.getAccountId());
                acctInfoDetailDTO.setAccountModel(b.getAccountModel());
                acctInfoDetailDTO.setBankAccountNo(b.getBankAccountNo());
                acctInfoDetailDTO.setBankName(b.getBankName());
                acctInfoDetailDTO.setBankAcctId(b.getBankAcctId());
                acctInfoDetailDTO.setCompanyId(b.getCompanyId());
                acctInfoDetailDTO.setCompanyMainId(b.getCompanyMainId());
                acctInfoDetailDTO.setCompanyName(b.getCompanyName());
                acctInfoDetailDTO.setAccountStatus(b.getAccountStatus());
                buildCompanyMain(acctInfoDetailDTO, b.getCompanyId(), b.getCompanyMainId(), b.getBankName());
                list.add(acctInfoDetailDTO);
            });
        }
        if (!ObjUtils.isNull(acctIndividualCreditList)) {
            acctIndividualCreditList.stream().forEach(b -> {
                AcctInfoDetailDTO acctInfoDetailDTO = new AcctInfoDetailDTO();
                acctInfoDetailDTO.setAccountId(b.getAccountId());
                acctInfoDetailDTO.setAccountModel(b.getAccountModel());
                acctInfoDetailDTO.setBankAccountNo(b.getBankAccountNo());
                acctInfoDetailDTO.setBankName(b.getBankName());
                acctInfoDetailDTO.setBankAcctId(b.getBankAcctId());
                acctInfoDetailDTO.setCompanyId(b.getCompanyId());
                acctInfoDetailDTO.setCompanyMainId(b.getCompanyMainId());
                acctInfoDetailDTO.setCompanyName(b.getCompanyName());
                acctInfoDetailDTO.setAccountStatus(b.getAccountStatus());
                buildCompanyMain(acctInfoDetailDTO, b.getCompanyId(), b.getCompanyMainId(), b.getBankName());
                list.add(acctInfoDetailDTO);
            });
        }
        return list;
    }

    private List<AcctInfoDetailDTO> buildReimbursementAccount(List<AcctReimbursement> acctReimbursementList) {
        List<AcctInfoDetailDTO> list = Lists.newArrayList();
        if (!ObjUtils.isNull(acctReimbursementList)) {
            acctReimbursementList.stream().forEach(b -> {
                AcctInfoDetailDTO acctInfoDetailDTO = new AcctInfoDetailDTO();
                acctInfoDetailDTO.setAccountId(b.getAccountId());
                acctInfoDetailDTO.setAccountModel(b.getAccountModel());
                acctInfoDetailDTO.setBankAccountNo(b.getBankAccountNo());
                acctInfoDetailDTO.setBankName(b.getBankName());
                acctInfoDetailDTO.setBankAcctId(b.getBankAcctId());
                acctInfoDetailDTO.setCompanyId(b.getCompanyId());
                acctInfoDetailDTO.setCompanyMainId(b.getCompanyMainId());
                acctInfoDetailDTO.setCompanyName(b.getCompanyName());
                acctInfoDetailDTO.setAccountStatus(b.getAccountStatus());
                buildCompanyMain(acctInfoDetailDTO, b.getCompanyId(), b.getCompanyMainId(), b.getBankName());
                list.add(acctInfoDetailDTO);
            });
        }
        return list;
    }

    private List<AcctInfoDetailDTO> buildPublicAccount(List<AccountPublic> accountPublicList) {
        List<AcctInfoDetailDTO> list = Lists.newArrayList();
        if (!ObjUtils.isNull(accountPublicList)) {
            accountPublicList.stream().forEach(b -> {
                AcctInfoDetailDTO acctInfoDetailDTO = new AcctInfoDetailDTO();
                acctInfoDetailDTO.setAccountId(b.getAccountPublicId());
                acctInfoDetailDTO.setAccountModel(b.getAccountModel());
                acctInfoDetailDTO.setBankAccountNo(b.getBankAccountNo());
                acctInfoDetailDTO.setBankName(b.getBankAccountName());
                acctInfoDetailDTO.setBankAcctId(b.getBankAcctId());
                acctInfoDetailDTO.setCompanyId(b.getCompanyId());
                acctInfoDetailDTO.setCompanyMainId(b.getCompanyMainId());
                acctInfoDetailDTO.setCompanyName(b.getCompanyName());
                acctInfoDetailDTO.setAccountStatus(0);
                if (AccountPublicStatus.DISABLE.getKey() == b.getAccountPublicStatus() || AccountPublicStatus.UNBIND.getKey() == b.getAccountPublicStatus()) {
                    acctInfoDetailDTO.setAccountStatus(1);
                }
                buildCompanyMain(acctInfoDetailDTO, b.getCompanyId(), b.getCompanyMainId(), b.getBankAccountName());
                list.add(acctInfoDetailDTO);
            });
        }
        return list;
    }

    private List<AcctInfoDetailDTO> buildImprestAccount(List<AcctCompanyCard> acctCompanyCardList) {
        List<AcctInfoDetailDTO> list = Lists.newArrayList();
        if (!ObjUtils.isNull(acctCompanyCardList)) {
            acctCompanyCardList.stream().forEach(b -> {
                AcctInfoDetailDTO acctInfoDetailDTO = new AcctInfoDetailDTO();
                acctInfoDetailDTO.setAccountId(b.getAccountId());
                acctInfoDetailDTO.setAccountModel(b.getAccountModel());
                acctInfoDetailDTO.setBankAccountNo(b.getBankAccountNo());
                acctInfoDetailDTO.setBankName(b.getBankName());
                acctInfoDetailDTO.setBankAcctId(b.getBankAcctId());
                acctInfoDetailDTO.setCompanyId(b.getCompanyId());
                acctInfoDetailDTO.setCompanyMainId(b.getCompanyMainId());
                acctInfoDetailDTO.setCompanyName(b.getCompanyName());
                acctInfoDetailDTO.setAccountStatus(b.getAccountStatus());
                buildCompanyMain(acctInfoDetailDTO, b.getCompanyId(), b.getCompanyMainId(), b.getBankName());
                list.add(acctInfoDetailDTO);
            });
        }
        return list;
    }

    private void buildCompanyMain(AcctInfoDetailDTO acctInfoDetailDTO, String companyId, String companyMainId, String bankName) {
        AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(companyId, companyMainId, bankName);
        if (ObjUtils.isNotEmpty(acctCompanyMain)) {
            acctInfoDetailDTO.setBusinessLicenseCode(acctCompanyMain.getBusinessLicenseCode());
            acctInfoDetailDTO.setBankAccountName(acctCompanyMain.getBusinessName());
            acctInfoDetailDTO.setBankBatchCode(acctCompanyMain.getBankBrnNo());
            acctInfoDetailDTO.setBankBatchName(acctCompanyMain.getBankBatchName());
        }
    }


    private AcctInfoRespDTO queryByBizNo(String bizNo) {
        if (StringUtils.isEmpty(bizNo)) {
            return null;
        }
        AcctInfoRespDTO acctCheckingQueryRpcDTO = null;
//        acctCheckingQueryRpcDTO.setBizNo(bizNo);
        AccountGeneralFlow accountGeneralFlow = accountGeneralFlowService.queryByFlowId(bizNo);
        if (ObjUtils.isNotEmpty(accountGeneralFlow)) {
            acctCheckingQueryRpcDTO = new AcctInfoRespDTO();
            // 收付款账号
            acctCheckingQueryRpcDTO.setPayAccountNo(accountGeneralFlow.getBankAccountNo());
            acctCheckingQueryRpcDTO.setReceiveAccountNo(accountGeneralFlow.getTargetBankAcctId());
            BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(accountGeneralFlow.getTargetBankAcctId(),accountGeneralFlow.getTargetBankName());
            if(!Objects.isNull(bankAcct)){
                acctCheckingQueryRpcDTO.setReceiveAccountName(bankAcct.getCompanyMainName());
            }
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(accountGeneralFlow.getCompanyId(), accountGeneralFlow.getCompanyMainId(), accountGeneralFlow.getBankName());
            if (!ObjUtils.isNull(acctCompanyMain)) {
                acctCheckingQueryRpcDTO.setPayAccountName(acctCompanyMain.getBusinessName());
            }
            acctCheckingQueryRpcDTO.setTradeType(accountGeneralFlow.getTradeType());
            acctCheckingQueryRpcDTO.setOperationType(accountGeneralFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(accountGeneralFlow.getOperationTypeDesc());
            acctCheckingQueryRpcDTO.setAccountType(acctCheckingQueryRpcDTO.getAccountType());
            acctCheckingQueryRpcDTO.setAccountSubType(FundAccountSubType.GENERAL_ACCOUNT.getKey());
            acctCheckingQueryRpcDTO.setAccountFlowId(accountGeneralFlow.getAccountFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(accountGeneralFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(accountGeneralFlow.getSyncBankTransNo());
            acctCheckingQueryRpcDTO.setCompanyId(accountGeneralFlow.getCompanyId());
            acctCheckingQueryRpcDTO.setBankName(accountGeneralFlow.getBankName());
            acctCheckingQueryRpcDTO.setOperationAmount(accountGeneralFlow.getOperationAmount());
            acctCheckingQueryRpcDTO.setBizNo(accountGeneralFlow.getBizNo());
        }
        //商务授信账户
        AcctBusinessCreditFlow acctBusinessCreditFlow = getAcctBusinessCreditFlow(bizNo);
        if (!ObjUtils.isNull(acctBusinessCreditFlow)) {
            return builderAcctBusinessCreditRespDTO(acctCheckingQueryRpcDTO, acctBusinessCreditFlow);
        }
        //商务充值账户
        AcctBusinessDebitFlow acctBusinessDebitFlow = getAcctBusinessDebitFlow(bizNo);
        if (!ObjUtils.isNull(acctBusinessDebitFlow)) {
            return builderAcctBusinessDebitRespDTO(acctCheckingQueryRpcDTO, acctBusinessDebitFlow);
        }
        AcctIndividualDebitFlow acctIndividualDebitFlow = acctIndividualDebitFlowService.queryAccountSubFlowByFlowId(bizNo);
        if (!ObjUtils.isNull(acctIndividualDebitFlow)) {
            return builderAcctIndividualDebitFlowRespDTO(acctCheckingQueryRpcDTO, acctIndividualDebitFlow);
        }
        AcctIndividualCreditFlow acctIndividualCreditFlow = acctIndividualCreditFlowService.queryAccountSubFlowByFlowId(bizNo);
        if (!ObjUtils.isNull(acctIndividualDebitFlow)) {
            return builderAcctIndividualCreditFlowRespDTO(acctCheckingQueryRpcDTO, acctIndividualCreditFlow);
        }
        //个人授信账户
        List<AcctIndividualCreditFlow> acctIndividualCreditFlowList = getAcctIndividualCreditFlow(bizNo);
        if (ObjUtils.isNotEmpty(acctIndividualCreditFlowList)) {
            return builderAcctIndividualCreditFlowRespDTO(acctCheckingQueryRpcDTO, acctIndividualCreditFlowList);
        }
        //个人充值账户
        List<AcctIndividualDebitFlow> acctIndividualDebitFlowList = getAcctIndividualDebitFlow(bizNo);
        if (ObjUtils.isNotEmpty(acctIndividualDebitFlowList)) {
            return builderAcctIndividualDebitFlowRespDTO(acctCheckingQueryRpcDTO, acctIndividualDebitFlowList);
        }

        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryByFreezenFlowId(bizNo);
        if (ObjUtils.isNotEmpty(fundFreezenFlow)){
            acctCheckingQueryRpcDTO.setTradeType(getTradeTypeByFreezenOptType(fundFreezenFlow.getOperationType()));
            // 收付款账号
            acctCheckingQueryRpcDTO.setPayAccountNo(fundFreezenFlow.getBankAccountNo());
            acctCheckingQueryRpcDTO.setReceiveAccountNo(fundFreezenFlow.getTargetBankAcctId());
            BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(fundFreezenFlow.getTargetBankAcctId(),fundFreezenFlow.getTargetBankName());
            if(!Objects.isNull(bankAcct)){
                acctCheckingQueryRpcDTO.setReceiveAccountName(bankAcct.getCompanyMainName());
            }
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(fundFreezenFlow.getCompanyId(), fundFreezenFlow.getCompanyMainId(), fundFreezenFlow.getBankName());
            if (!ObjUtils.isNull(acctCompanyMain)) {
                acctCheckingQueryRpcDTO.setPayAccountName(acctCompanyMain.getBusinessName());
            }
            acctCheckingQueryRpcDTO.setOperationType(fundFreezenFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(fundFreezenFlow.getOperationTypeDesc());
            acctCheckingQueryRpcDTO.setAccountType(fundFreezenFlow.getAccountModel());
            acctCheckingQueryRpcDTO.setAccountSubType(fundFreezenFlow.getAccountSubType());
            acctCheckingQueryRpcDTO.setBizNo(fundFreezenFlow.getBizNo());
            acctCheckingQueryRpcDTO.setOperationAmount(fundFreezenFlow.getOperationAmount());
            acctCheckingQueryRpcDTO.setCompanyId(fundFreezenFlow.getCompanyId());
            acctCheckingQueryRpcDTO.setAccountFlowId(fundFreezenFlow.getFreezenFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(fundFreezenFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(fundFreezenFlow.getSyncBankTransNo());
            acctCheckingQueryRpcDTO.setBankName(fundFreezenFlow.getBankName());
            acctCheckingQueryRpcDTO.setFrozenFlowId(fundFreezenFlow.getFreezenFlowId());
            acctCheckingQueryRpcDTO.setAccountId(fundFreezenFlow.getAccountId());

        }
        return acctCheckingQueryRpcDTO;
    }

    private Integer getTradeTypeByFreezenOptType(Integer operationType) {
        if (operationType == FreezenChangeType.FREEZING.getKey()) {
            return FundAcctTradeType.FREEZE.getCode();
        } else if (operationType == FreezenChangeType.UNFREEZING.getKey()) {
            return FundAcctTradeType.UN_FREEZE.getCode();
        } else if (operationType == FreezenChangeType.PAY.getKey()) {
            return FundAcctTradeType.CONSUME.getCode();
        } else if (operationType == FreezenChangeType.REFUND.getKey()) {
            return FundAcctTradeType.REFUND.getCode();
        }
        return null;
    }

    private AcctBusinessCreditFlow getAcctBusinessCreditFlow(String bizNo) {
        //商务授信账户
        AcctBusinessCreditFlow acctBusinessCreditFlow = acctBusinessCreditFlowService.queryAccountSubFlowByFlowId(bizNo);
        return acctBusinessCreditFlow;
    }

    private AcctInfoRespDTO builderAcctBusinessCreditRespDTO(AcctInfoRespDTO acctCheckingQueryRpcDTO, AcctBusinessCreditFlow acctBusinessCreditFlow) {
        if (ObjUtils.isNull(acctCheckingQueryRpcDTO)) {
            acctCheckingQueryRpcDTO = new AcctInfoRespDTO();
        }
        // 收付款账号
        acctCheckingQueryRpcDTO.setPayAccountNo(acctBusinessCreditFlow.getBankAccountNo());
        acctCheckingQueryRpcDTO.setReceiveAccountNo(acctBusinessCreditFlow.getTargetBankAcctId());
        BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(acctBusinessCreditFlow.getTargetBankAcctId(),acctBusinessCreditFlow.getTargetBankName());
        if(!Objects.isNull(bankAcct)){
            acctCheckingQueryRpcDTO.setReceiveAccountName(bankAcct.getCompanyMainName());
        }
        AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(acctBusinessCreditFlow.getCompanyId(), acctBusinessCreditFlow.getCompanyMainId(), acctBusinessCreditFlow.getBankName());
        if (!ObjUtils.isNull(acctCompanyMain)) {
            acctCheckingQueryRpcDTO.setPayAccountName(acctCompanyMain.getBusinessName());
        }
        acctCheckingQueryRpcDTO.setRefundTxnId(acctBusinessCreditFlow.getRefundTxnId());
        acctCheckingQueryRpcDTO.setCashierTxnId(acctBusinessCreditFlow.getCashierTxnId());
        acctCheckingQueryRpcDTO.setTradeType(acctBusinessCreditFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctBusinessCreditFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctBusinessCreditFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctBusinessCreditFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctBusinessCreditFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctBusinessCreditFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctBusinessCreditFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctBusinessCreditFlow.getSyncBankTransNo());
        acctCheckingQueryRpcDTO.setCompanyId(acctBusinessCreditFlow.getCompanyId());
        acctCheckingQueryRpcDTO.setBankName(acctBusinessCreditFlow.getBankName());
        acctCheckingQueryRpcDTO.setOperationAmount(acctBusinessCreditFlow.getOperationAmount());
        acctCheckingQueryRpcDTO.setBizNo(acctBusinessCreditFlow.getBizNo());
        acctCheckingQueryRpcDTO.setAccountId(acctBusinessCreditFlow.getAccountId());
        return acctCheckingQueryRpcDTO;
    }

    private AcctBusinessDebitFlow getAcctBusinessDebitFlow(String bizNo) {
        //商务充值账户
        AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryAccountSubFlowByFlowId(bizNo);
        return acctBusinessDebitFlow;
    }

    private AcctInfoRespDTO builderAcctBusinessDebitRespDTO(AcctInfoRespDTO acctCheckingQueryRpcDTO, AcctBusinessDebitFlow acctBusinessDebitFlow) {
        if (ObjUtils.isNull(acctCheckingQueryRpcDTO)) {
            acctCheckingQueryRpcDTO = new AcctInfoRespDTO();
        }
        // 收付款账号
        acctCheckingQueryRpcDTO.setPayAccountNo(acctBusinessDebitFlow.getBankAccountNo());
        acctCheckingQueryRpcDTO.setReceiveAccountNo(acctBusinessDebitFlow.getTargetBankAcctId());
        BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(acctBusinessDebitFlow.getTargetBankAcctId(),acctBusinessDebitFlow.getTargetBankName());
        if(!Objects.isNull(bankAcct)){
            acctCheckingQueryRpcDTO.setReceiveAccountName(bankAcct.getCompanyMainName());
        }
        AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(acctBusinessDebitFlow.getCompanyId(), acctBusinessDebitFlow.getCompanyMainId(), acctBusinessDebitFlow.getBankName());
        if (!ObjUtils.isNull(acctCompanyMain)) {
            acctCheckingQueryRpcDTO.setPayAccountName(acctCompanyMain.getBusinessName());
        }
        acctCheckingQueryRpcDTO.setRefundTxnId(acctBusinessDebitFlow.getRefundTxnId());
        acctCheckingQueryRpcDTO.setCashierTxnId(acctBusinessDebitFlow.getCashierTxnId());
        acctCheckingQueryRpcDTO.setTradeType(acctBusinessDebitFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctBusinessDebitFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctBusinessDebitFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctBusinessDebitFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctBusinessDebitFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctBusinessDebitFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctBusinessDebitFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctBusinessDebitFlow.getSyncBankTransNo());
        acctCheckingQueryRpcDTO.setCompanyId(acctBusinessDebitFlow.getCompanyId());
        acctCheckingQueryRpcDTO.setBankName(acctBusinessDebitFlow.getBankName());
        acctCheckingQueryRpcDTO.setOperationAmount(acctBusinessDebitFlow.getOperationAmount());
        acctCheckingQueryRpcDTO.setBizNo(acctBusinessDebitFlow.getBizNo());
        acctCheckingQueryRpcDTO.setAccountId(acctBusinessDebitFlow.getAccountId());
        return acctCheckingQueryRpcDTO;
    }

    private List<AcctIndividualCreditFlow> getAcctIndividualCreditFlow(String bizNo) {
        List<AcctIndividualCreditFlow> acctIndividualCreditFlowList = acctIndividualCreditFlowService.queryAccountSubFlowByBizNo(bizNo, FundAcctCreditOptType.FROZEN_VOUCHER_GRANT.getKey());
        if (ObjUtils.isEmpty(acctIndividualCreditFlowList)) {
            acctIndividualCreditFlowList = acctIndividualCreditFlowService.queryAccountSubFlowByBizNo(bizNo, FundAcctCreditOptType.FROZEN_VOUCHER_RECALL.getKey());
        }
        return acctIndividualCreditFlowList;

    }

    private AcctInfoRespDTO builderAcctIndividualCreditFlowRespDTO(AcctInfoRespDTO acctCheckingQueryRpcDTO, List<AcctIndividualCreditFlow> acctIndividualCreditFlowList) {
        if (ObjUtils.isNull(acctCheckingQueryRpcDTO)) {
            acctCheckingQueryRpcDTO = new AcctInfoRespDTO();
        }
        if (ObjUtils.isNotEmpty(acctIndividualCreditFlowList)) {
            AcctIndividualCreditFlow acctIndividualCreditFlow = acctIndividualCreditFlowList.get(0);
            // 收付款账号
            acctCheckingQueryRpcDTO.setPayAccountNo(acctIndividualCreditFlow.getBankAccountNo());
            acctCheckingQueryRpcDTO.setReceiveAccountNo(acctIndividualCreditFlow.getTargetBankAcctId());
            BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(acctIndividualCreditFlow.getTargetBankAcctId(),acctIndividualCreditFlow.getTargetBankName());
            if(!Objects.isNull(bankAcct)){
                acctCheckingQueryRpcDTO.setReceiveAccountName(bankAcct.getCompanyMainName());
            }
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(acctIndividualCreditFlow.getCompanyId(), acctIndividualCreditFlow.getCompanyMainId(), acctIndividualCreditFlow.getBankName());
            if (!ObjUtils.isNull(acctCompanyMain)) {
                acctCheckingQueryRpcDTO.setPayAccountName(acctCompanyMain.getBusinessName());
            }
            acctCheckingQueryRpcDTO.setTradeType(acctIndividualCreditFlow.getTradeType());
            acctCheckingQueryRpcDTO.setOperationType(acctIndividualCreditFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(acctIndividualCreditFlow.getOperationDescription());
            acctCheckingQueryRpcDTO.setAccountType(acctIndividualCreditFlow.getAccountModel());
            acctCheckingQueryRpcDTO.setAccountSubType(acctIndividualCreditFlow.getAccountSubType());
            acctCheckingQueryRpcDTO.setAccountFlowId(acctIndividualCreditFlow.getAccountFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(acctIndividualCreditFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(acctIndividualCreditFlow.getSyncBankTransNo());
            acctCheckingQueryRpcDTO.setCompanyId(acctIndividualCreditFlow.getCompanyId());
            acctCheckingQueryRpcDTO.setBankName(acctIndividualCreditFlow.getBankName());
            acctCheckingQueryRpcDTO.setOperationAmount(acctIndividualCreditFlow.getOperationAmount());
            acctCheckingQueryRpcDTO.setBizNo(acctIndividualCreditFlow.getBizNo());
            acctCheckingQueryRpcDTO.setAccountId(acctIndividualCreditFlow.getAccountId());

            // 账户关联冻结
            Integer freezenChangeType = FreezenChangeType.UNFREEZING.getKey();
            if (FundAcctDebitOptType.FROZEN_VOUCHER_GRANT.getKey() == acctIndividualCreditFlow.getOperationType()) {
                freezenChangeType = FreezenChangeType.FREEZING.getKey();
            }
            FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryFreezenByAccountIdAndBizNo(acctIndividualCreditFlow.getAccountId(),acctIndividualCreditFlow.getBizNo(),freezenChangeType);
            if(!Objects.isNull(bankAcct)){
                acctCheckingQueryRpcDTO.setFrozenFlowId(fundFreezenFlow.getFreezenFlowId());
            }

            return acctCheckingQueryRpcDTO;
        }
        return null;
    }

    private List<AcctIndividualDebitFlow> getAcctIndividualDebitFlow(String bizNo) {
        List<AcctIndividualDebitFlow> acctIndividualDebitFlowList = acctIndividualDebitFlowService.queryAccountSubFlowByBizNo(bizNo, FundAcctDebitOptType.FROZEN_VOUCHER_GRANT.getKey());
        if (ObjUtils.isEmpty(acctIndividualDebitFlowList)) {
            acctIndividualDebitFlowList = acctIndividualDebitFlowService.queryAccountSubFlowByBizNo(bizNo, FundAcctDebitOptType.FROZEN_VOUCHER_RECALL.getKey());
        }
        return acctIndividualDebitFlowList;

    }

    private AcctInfoRespDTO builderAcctIndividualDebitFlowRespDTO(AcctInfoRespDTO acctCheckingQueryRpcDTO, List<AcctIndividualDebitFlow> acctIndividualDebitFlowList) {
        if (ObjUtils.isNull(acctCheckingQueryRpcDTO)) {
            acctCheckingQueryRpcDTO = new AcctInfoRespDTO();
        }
        if (ObjUtils.isNotEmpty(acctIndividualDebitFlowList)) {
            AcctIndividualDebitFlow acctIndividualDebitFlow = acctIndividualDebitFlowList.get(0);
            // 收付款账号
            acctCheckingQueryRpcDTO.setPayAccountNo(acctIndividualDebitFlow.getBankAccountNo());
            acctCheckingQueryRpcDTO.setReceiveAccountNo(acctIndividualDebitFlow.getTargetBankAcctId());
            BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(acctIndividualDebitFlow.getTargetBankAcctId(),acctIndividualDebitFlow.getTargetBankName());
            if(!Objects.isNull(bankAcct)){
                acctCheckingQueryRpcDTO.setReceiveAccountName(bankAcct.getCompanyMainName());
            }
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(acctIndividualDebitFlow.getCompanyId(), acctIndividualDebitFlow.getCompanyMainId(), acctIndividualDebitFlow.getBankName());
            if (!ObjUtils.isNull(acctCompanyMain)) {
                acctCheckingQueryRpcDTO.setPayAccountName(acctCompanyMain.getBusinessName());
            }
            acctCheckingQueryRpcDTO.setTradeType(acctIndividualDebitFlow.getTradeType());
            acctCheckingQueryRpcDTO.setOperationType(acctIndividualDebitFlow.getOperationType());
            acctCheckingQueryRpcDTO.setOperationTypeDesc(acctIndividualDebitFlow.getOperationDescription());
            acctCheckingQueryRpcDTO.setAccountType(acctIndividualDebitFlow.getAccountModel());
            acctCheckingQueryRpcDTO.setAccountSubType(acctIndividualDebitFlow.getAccountSubType());
            acctCheckingQueryRpcDTO.setAccountFlowId(acctIndividualDebitFlow.getAccountFlowId());
            acctCheckingQueryRpcDTO.setBankTransNo(acctIndividualDebitFlow.getBankTransNo());
            acctCheckingQueryRpcDTO.setBassTxnId(acctIndividualDebitFlow.getSyncBankTransNo());
            acctCheckingQueryRpcDTO.setCompanyId(acctIndividualDebitFlow.getCompanyId());
            acctCheckingQueryRpcDTO.setBankName(acctIndividualDebitFlow.getBankName());
            acctCheckingQueryRpcDTO.setOperationAmount(acctIndividualDebitFlow.getOperationAmount());
            acctCheckingQueryRpcDTO.setBizNo(acctIndividualDebitFlow.getBizNo());
            acctCheckingQueryRpcDTO.setAccountId(acctIndividualDebitFlow.getAccountId());

            // 账户关联冻结
            Integer freezenChangeType = FreezenChangeType.UNFREEZING.getKey();
            if (FundAcctDebitOptType.FROZEN_VOUCHER_GRANT.getKey() == acctIndividualDebitFlow.getOperationType()) {
                freezenChangeType = FreezenChangeType.FREEZING.getKey();
            }
            FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryFreezenByAccountIdAndBizNo(acctIndividualDebitFlow.getAccountId(),acctIndividualDebitFlow.getBizNo(),freezenChangeType);
            if(!Objects.isNull(bankAcct)){
                acctCheckingQueryRpcDTO.setFrozenFlowId(fundFreezenFlow.getFreezenFlowId());
            }

            return acctCheckingQueryRpcDTO;
        }
        return null;
    }

    private AcctInfoRespDTO builderAcctIndividualDebitFlowRespDTO(AcctInfoRespDTO acctCheckingQueryRpcDTO, AcctIndividualDebitFlow acctIndividualDebitFlow) {
        if (ObjUtils.isNull(acctCheckingQueryRpcDTO)) {
            acctCheckingQueryRpcDTO = new AcctInfoRespDTO();
        }
        // 收付款账号
        acctCheckingQueryRpcDTO.setPayAccountNo(acctIndividualDebitFlow.getBankAccountNo());
        acctCheckingQueryRpcDTO.setReceiveAccountNo(acctIndividualDebitFlow.getTargetBankAcctId());
        BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(acctIndividualDebitFlow.getTargetBankAcctId(),acctIndividualDebitFlow.getTargetBankName());
        if(!Objects.isNull(bankAcct)){
            acctCheckingQueryRpcDTO.setReceiveAccountName(bankAcct.getCompanyMainName());
        }
        AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(acctIndividualDebitFlow.getCompanyId(), acctIndividualDebitFlow.getCompanyMainId(), acctIndividualDebitFlow.getBankName());
        if (!ObjUtils.isNull(acctCompanyMain)) {
            acctCheckingQueryRpcDTO.setPayAccountName(acctCompanyMain.getBusinessName());
        }
        acctCheckingQueryRpcDTO.setTradeType(acctIndividualDebitFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctIndividualDebitFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctIndividualDebitFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctIndividualDebitFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctIndividualDebitFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctIndividualDebitFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctIndividualDebitFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctIndividualDebitFlow.getSyncBankTransNo());
        acctCheckingQueryRpcDTO.setCompanyId(acctIndividualDebitFlow.getCompanyId());
        acctCheckingQueryRpcDTO.setBankName(acctIndividualDebitFlow.getBankName());
        acctCheckingQueryRpcDTO.setOperationAmount(acctIndividualDebitFlow.getOperationAmount());
        acctCheckingQueryRpcDTO.setBizNo(acctIndividualDebitFlow.getBizNo());
        acctCheckingQueryRpcDTO.setAccountId(acctIndividualDebitFlow.getAccountId());

        // 账户关联冻结
        Integer freezenChangeType = FreezenChangeType.UNFREEZING.getKey();
        if (FundAcctDebitOptType.FROZEN_VOUCHER_GRANT.getKey() == acctIndividualDebitFlow.getOperationType()) {
            freezenChangeType = FreezenChangeType.FREEZING.getKey();
        }
        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryFreezenByAccountIdAndBizNo(acctIndividualDebitFlow.getAccountId(),acctIndividualDebitFlow.getBizNo(),freezenChangeType);
        if(!Objects.isNull(bankAcct)){
            acctCheckingQueryRpcDTO.setFrozenFlowId(fundFreezenFlow.getFreezenFlowId());
        }

        return acctCheckingQueryRpcDTO;
    }

    private AcctInfoRespDTO builderAcctIndividualCreditFlowRespDTO(AcctInfoRespDTO acctCheckingQueryRpcDTO, AcctIndividualCreditFlow acctIndividualCreditFlow) {
        if (ObjUtils.isNull(acctCheckingQueryRpcDTO)) {
            acctCheckingQueryRpcDTO = new AcctInfoRespDTO();
        }
        // 收付款账号
        acctCheckingQueryRpcDTO.setPayAccountNo(acctIndividualCreditFlow.getBankAccountNo());
        acctCheckingQueryRpcDTO.setReceiveAccountNo(acctIndividualCreditFlow.getTargetBankAcctId());
        BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(acctIndividualCreditFlow.getTargetBankAcctId(),acctIndividualCreditFlow.getTargetBankName());
        if(!Objects.isNull(bankAcct)){
            acctCheckingQueryRpcDTO.setReceiveAccountName(bankAcct.getCompanyMainName());
        }
        AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(acctIndividualCreditFlow.getCompanyId(), acctIndividualCreditFlow.getCompanyMainId(), acctIndividualCreditFlow.getBankName());
        if (!ObjUtils.isNull(acctCompanyMain)) {
            acctCheckingQueryRpcDTO.setPayAccountName(acctCompanyMain.getBusinessName());
        }
        acctCheckingQueryRpcDTO.setTradeType(acctIndividualCreditFlow.getTradeType());
        acctCheckingQueryRpcDTO.setOperationType(acctIndividualCreditFlow.getOperationType());
        acctCheckingQueryRpcDTO.setOperationTypeDesc(acctIndividualCreditFlow.getOperationDescription());
        acctCheckingQueryRpcDTO.setAccountType(acctIndividualCreditFlow.getAccountModel());
        acctCheckingQueryRpcDTO.setAccountSubType(acctIndividualCreditFlow.getAccountSubType());
        acctCheckingQueryRpcDTO.setAccountFlowId(acctIndividualCreditFlow.getAccountFlowId());
        acctCheckingQueryRpcDTO.setBankTransNo(acctIndividualCreditFlow.getBankTransNo());
        acctCheckingQueryRpcDTO.setBassTxnId(acctIndividualCreditFlow.getSyncBankTransNo());
        acctCheckingQueryRpcDTO.setCompanyId(acctIndividualCreditFlow.getCompanyId());
        acctCheckingQueryRpcDTO.setBankName(acctIndividualCreditFlow.getBankName());
        acctCheckingQueryRpcDTO.setOperationAmount(acctIndividualCreditFlow.getOperationAmount());
        acctCheckingQueryRpcDTO.setBizNo(acctIndividualCreditFlow.getBizNo());
        acctCheckingQueryRpcDTO.setAccountId(acctIndividualCreditFlow.getAccountId());

        // 账户关联冻结
        Integer freezenChangeType = FreezenChangeType.UNFREEZING.getKey();
        if (FundAcctDebitOptType.FROZEN_VOUCHER_GRANT.getKey() == acctIndividualCreditFlow.getOperationType()) {
            freezenChangeType = FreezenChangeType.FREEZING.getKey();
        }
        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryFreezenByAccountIdAndBizNo(acctIndividualCreditFlow.getAccountId(),acctIndividualCreditFlow.getBizNo(),freezenChangeType);
        if(!Objects.isNull(bankAcct)){
            acctCheckingQueryRpcDTO.setFrozenFlowId(fundFreezenFlow.getFreezenFlowId());
        }
        return acctCheckingQueryRpcDTO;
    }


}
