package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fenbeitong.fenbeipay.account.dto.GroupTransferWhitelist;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acct.GroupTransferWhitelistQueryReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.GroupTransferWhitelistDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.GroupAcctTransferService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.na.service.GroupTransferWhitelistService;
import com.fenbeitong.usercenter.api.model.dto.group.GroupInfoDTO;
import com.fenbeitong.usercenter.api.service.group.IGroupManagementService;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2022-12-01 06:05:27 
*/
@Service("groupAcctTransferService")
public class GroupAcctTransferServiceImpl implements GroupAcctTransferService {

	@Autowired
	private IGroupManagementService groupManagementService;
	
	@Autowired
	private GroupTransferWhitelistService groupTransferWhitelistService;
	
	@Override
	public boolean addGroupWhitelist(String[] groupIds) {
		if (Objects.isNull(groupIds) || groupIds.length == 0) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		for (String groupId : groupIds) {
			GroupInfoDTO group = groupManagementService.getById(groupId);
			if (Objects.nonNull(group)) {
				GroupTransferWhitelist whitelist = GroupTransferWhitelist.builder()
						.groupId(groupId)
						.groupName(group.getName())
						.groupFbId(group.getFbId())
						.headCompanyId(group.getHeadquartersCompanyId())
						.headCompanyName(group.getHeadquartersCompanyName())
						.headCompanyFbId(group.getHeadquartersCompanyFbId())
						.status(1)
						.build();
				groupTransferWhitelistService.save(whitelist);
			}
		}
		
		return true;
	}

	@Override
	public boolean isGroupInWhitelist(String groupId) {
		if (StringUtils.isBlank(groupId)) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		GroupTransferWhitelist whitelist = groupTransferWhitelistService.queryByGroupId(groupId);
		return Objects.nonNull(whitelist);
	}

	@Override
	public boolean deleteGroupFromWhitelist(String groupId) {
		if (StringUtils.isBlank(groupId)) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		boolean done = groupTransferWhitelistService.deleteByGroupId(groupId);
		return done;
	}

	@Override
	public ResponsePage<GroupTransferWhitelistDTO> queryByPage(GroupTransferWhitelistQueryReqDTO page) {
		if (Objects.isNull(page)) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		ResponsePage<GroupTransferWhitelistDTO> resp = groupTransferWhitelistService.queryByPage(page);
		return resp;
	}

}
