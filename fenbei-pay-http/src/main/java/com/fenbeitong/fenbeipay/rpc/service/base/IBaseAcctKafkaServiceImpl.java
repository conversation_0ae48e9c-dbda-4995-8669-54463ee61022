package com.fenbeitong.fenbeipay.rpc.service.base;

import com.fenbeitong.dech.api.enums.AccountStatusEnum;
import com.fenbeitong.fenbeipay.acctdech.service.*;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctGeneralService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctReimbursementService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UBankAcctService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicDechService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenChangeType;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.ApplyCardManager;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.AutoAcctCheckingEventUtil;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcctFlow;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezenFlow;
import com.fenbeitong.fenbeipay.nf.service.FundFreezenFlowService;
import com.fenbeitong.fenbeipay.vouchers.unit.service.UVoucherTaskService;
import com.fenbeitong.finhub.common.constant.BankAcctTypeEnum;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundAcctGeneralOptType;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.consumer.KafkaConsumerUtils;
import com.fenbeitong.finhub.kafka.msg.dech.KafkaDechCashOutMsg;
import com.fenbeitong.finhub.kafka.msg.dech.KafkaDechTradeResultMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaBankConsumeTaskMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaBankRefundTaskMsg;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;
/**
 * kakfa消息
 */
@Service
public class IBaseAcctKafkaServiceImpl {
    @Autowired
    private AcctPublicDechService acctPublicDechService;

    @Autowired
    protected AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Autowired
    protected AcctBusinessDebitFlowService acctBusinessDebitFlowService;

    @Autowired
    protected AcctBusinessCreditService acctBusinessCreditService;

    @Autowired
    protected AcctBusinessDebitService acctBusinessDebitService;

    @Autowired
    protected RedissonService redissonService;

    @Autowired
    private UAcctGeneralService uAcctGeneralService;

    @Autowired
    private UAcctCommonService uAcctCommonService;

    @Autowired
    private UVoucherTaskService uVoucherTaskService;
    @Autowired
    protected FundFreezenFlowService fundFreezenFlowService;
    @Autowired
    protected DingDingMsgService dingDingMsgService;

    @Autowired
    protected UBankAcctService uBankAcctService;

    @Autowired
    private UAcctReimbursementService uAcctReimbursementService;

    @Autowired
    private ApplyCardManager applyCardManager;

    @Autowired
    private BankAcctFlowService bankAcctFlowService;

    @Autowired
    private AutoAcctCheckingEventUtil autoAcctCheckingEventUtil;
    /**
     * 强制解锁时间设置
     */
    private static final long LOCK_TIME_REFUND = 10000L;

    /**
     * 等待时间
     **/
    private static final long WAIT_TIME_REFUND = 0L;

    @Autowired
    private RedisDao redisDao;

    /**
     * dech提现发送消息
     * @param  record
     */
    @KafkaListener(topics = {"topic_dech_cash_out"})
    public void bankDechCashOutListener(ConsumerRecord<?, ?> record) {
        FinhubLogger.info("【kafka消息 ===dech提现发送消息信息操作：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), record.value());
        KafkaDechCashOutMsg iMessage = KafkaConsumerUtils.invokeIMessage(record.value().toString(),
                KafkaDechCashOutMsg.class);
        if (ObjUtils.isEmpty(iMessage)) {
            FinhubLogger.info("【kafka消费：dech提现发送消息:不需要处理消息为空】消费 data:" + iMessage.toString());
            return;
        }
        /*
          对公付款，发送对账消息。
          QX 2021-12-22 SERVER-4917
         */
        //对公帐户-付款业务成功时-做流水的上账状态更新操作，失败不在此处理，有收银台调用RPC
        if (AccountStatusEnum.isSuccess(iMessage.getStatus()) && FundAccountSubType.isComPublicCardAccount(iMessage.getAccountSubType())) {
            try {
                acctPublicDechService.updateDechCashOutListener(iMessage);
                FinhubLogger.info("【分贝通处理对公帐户kafka】"
                    + ":" + iMessage.toString());
            } catch (Exception e) {
                FinhubLogger.error("【分贝通处理帐户上账修改信息失败】data：{}==={}", JsonUtils.toJson(iMessage), e);
                dingDingMsgService.sendMsg("【分贝通处理帐户:银行转账对公上账发送消息修改信息失败】员工Id：" + iMessage.getFbOrderId());
            }
            // FIXME 以下代码逻辑是否能够成功update数据？是否有意义？
            try {
                acctPublicDechService.updateDechCashOutCostImageListener(iMessage);
                FinhubLogger.info("【分贝通处理对公帐户kafka】电子回单消息变更 data:" + iMessage.toString());
            } catch (Exception e) {
                FinhubLogger.error("【分贝通处理帐户上账修改信息失败】data：{}==={}", JsonUtils.toJson(iMessage), e);
                dingDingMsgService.sendMsg("【分贝通处理帐户:电子回单消息变更失败】员工Id：" + iMessage.getFbOrderId());
            }
        }
        /*
          余额账户，参与对账。成功，发送对账消息
          QX 2021-12-21
         */
        //余额账户-提现业务成功时-上账状态+电子回单获取
        if (AccountStatusEnum.isSuccess(iMessage.getStatus()) && FundAccountSubType.isGeneralAccount(iMessage.getAccountSubType())) {
            try {
                String fbOrderId = iMessage.getFbOrderId();
                if (StringUtils.isBlank(fbOrderId)) {
                    FinhubLogger.info("【提现成功：分贝通处理帐户kafka】dech提现发送消息变更消息完成 data:" + iMessage.toString());
                }
                uAcctGeneralService.cashWithdrawalSucc(iMessage);
                FinhubLogger.info("【提现成功：分贝通处理帐户kafka】dech提现发送消息变更消息完成 data:" + iMessage.toString());
            } catch (Exception e) {
                FinhubLogger.error("【提现成功：分贝通处理帐户修改信息失败】data：{}==={}", JsonUtils.toJson(iMessage), e);
                dingDingMsgService.sendMsg("【提现成功：分贝通处理帐户:dech提现成功发送消息修改信息失败】员工Id：" + iMessage.getFbOrderId());
            }
        }
        /*
          余额账户，参与对账。失败，发对账消息
          QX 2021-12-21
         */
        //余额账户-提现业务失败时-做金额回退操作
        if (AccountStatusEnum.isFail(iMessage.getStatus()) && FundAccountSubType.isGeneralAccount(iMessage.getAccountSubType())) {
            try {
                String fbOrderId = iMessage.getFbOrderId();
                if (StringUtils.isBlank(fbOrderId)) {
                    FinhubLogger.info("【提现失败：分贝通处理帐户kafka】dech提现发送消息变更消息完成 data:" + iMessage.toString());
                }
                uAcctGeneralService.cashWithdrawalCallBack(fbOrderId);
                FinhubLogger.info("【提现失败：分贝通处理帐户kafka】dech提现发送消息变更消息完成 data:" + iMessage.toString());
            } catch (Exception e) {
                FinhubLogger.error("【提现失败：分贝通处理帐户修改信息失败】data：{}==={}", JsonUtils.toJson(iMessage), e);
                dingDingMsgService.sendMsg("【提现失败：分贝通处理帐户:dech提现发送消息修改信息失败】员工Id：" + iMessage.getFbOrderId());
            }
        }

         /*
          余额账户，参与对账。退汇，发对账消息
          QX 2021-12-21
         */
        //余额账户-提现业务失败时-做金额回退操作
        if ("refund".equals(iMessage.getStatus()) && FundAccountSubType.isGeneralAccount(iMessage.getAccountSubType())) {
            try {
                String fbOrderId = iMessage.getFbOrderId();
                if (StringUtils.isBlank(fbOrderId)) {
                    FinhubLogger.info("【提现失败：分贝通处理帐户kafka】dech提现发送消息变更消息完成 data:" + iMessage.toString());
                }
                uAcctGeneralService.cashWithdrawalRefundCallBack(iMessage);
                FinhubLogger.info("【提现失败：分贝通处理帐户kafka】dech提现发送消息变更消息完成 data:" + iMessage.toString());
            } catch (Exception e) {
                FinhubLogger.error("【提现失败：分贝通处理帐户修改信息失败】data：{}==={}", JsonUtils.toJson(iMessage), e);
                dingDingMsgService.sendMsg("【提现失败：分贝通处理帐户:dech提现发送消息修改信息失败】员工Id：" + iMessage.getFbOrderId());
            }
        }

        if (AccountStatusEnum.isSuccess(iMessage.getStatus()) && 0 == iMessage.getAccountSubType()) {
            uBankAcctService.cashWithdrawalSucc(iMessage);
            sendMsg(iMessage);
        }
        //平台银行账户-提现业务失败时-做金额回退操作
        //TODO 魔法值 0
        if (AccountStatusEnum.isFail(iMessage.getStatus()) && 0 == iMessage.getAccountSubType()) {
            try {
                uBankAcctService.cashWithdrawalCallBack(iMessage);
                FinhubLogger.info("【提现失败：分贝通处理帐户kafka】dech提现发送消息变更消息完成 data:" + iMessage.toString());
            } catch (Exception e) {
                FinhubLogger.error("【提现失败：分贝通处理帐户修改信息失败】data：{}==={}", JsonUtils.toJson(iMessage), e);
                dingDingMsgService.sendMsg("【提现失败：分贝通处理帐户:dech提现发送消息修改信息失败】员工Id：" + iMessage.getFbOrderId());
            }
        }

        //平台银行账户-提现业务退汇时-做金额回退操作
        if ("refund".equals(iMessage.getStatus()) && 0 == iMessage.getAccountSubType()) {
            try {
                uBankAcctService.cashWithdrawalRefundCallBack(iMessage);
                FinhubLogger.info("【提现失败：分贝通处理帐户kafka】dech提现发送消息变更消息完成 data:" + iMessage.toString());
            } catch (Exception e) {
                FinhubLogger.error("【提现失败：分贝通处理帐户修改信息失败】data：{}==={}", JsonUtils.toJson(iMessage), e);
                dingDingMsgService.sendMsg("【提现失败：分贝通处理帐户:dech提现发送消息修改信息失败】员工Id：" + iMessage.getFbOrderId());
            }
        }

        //报销账户
        uAcctReimbursementService.cashWithdrawalCallBack(iMessage);
    }

    private void sendMsg(KafkaDechCashOutMsg iMessage){
        // 广发收款圈存提现参与对账
        if (!BankNameEnum.isCgb(iMessage.getBankName())) {
            return;
        }
        BankAcctFlow bankAcctFlow = bankAcctFlowService.queryByBizNoAndOperationType(iMessage.getFbOrderId(),FundAcctGeneralOptType.WITHDRAWAL.getKey());
        if (ObjUtils.isNull(bankAcctFlow)) {
            return;
        }
        if (BankAcctTypeEnum.isReceiptBankAccount(bankAcctFlow.getBankAcctType()) || BankAcctTypeEnum.TRAP_ACCOUNT.getCode() == bankAcctFlow.getBankAcctType()) {
            KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
            BeanUtils.copyProperties(bankAcctFlow, kafkaAutoAcctCheckingMsg);
            // 圈存
            kafkaAutoAcctCheckingMsg.setAccountSubType(13);
            if (BankAcctTypeEnum.isReceiptBankAccount(bankAcctFlow.getBankAcctType())) {
                kafkaAutoAcctCheckingMsg.setAccountSubType(12);
            }
            kafkaAutoAcctCheckingMsg.setAccountFlowId(bankAcctFlow.getBankAcctFlowId());
            autoAcctCheckingEventUtil.sendAccountChangeMsgEvent(kafkaAutoAcctCheckingMsg);
        }
    }


    @KafkaListener(topics = {"topic_bank_consume_acct_event"})
    public void bankCounsume(ConsumerRecord<?, ?> record) {
        Object topic_bank_consume_acct_event = redisDao.getRedisTemplate().opsForValue().get("topic_bank_consume_acct_event");
        if (ObjUtils.isNull(topic_bank_consume_acct_event)) {
            FinhubLogger.info("【kafka消息 ===因公消费上账接收消息信息操作：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), record.value());
            KafkaBankConsumeTaskMsg iMessage = KafkaConsumerUtils.invokeIMessage(record.value().toString(),
                    KafkaBankConsumeTaskMsg.class);
            uAcctCommonService.syncBankConsume(iMessage);
        }else {
            FinhubLogger.info("处理积压消息【kafka消息 ===因公消费上账接收消息信息操作：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), record.value());
        }
    }

    @KafkaListener(topics = {"topic_bank_refund_acct_event"})
    public void bankRefund(ConsumerRecord<?, ?> record) {
        FinhubLogger.info("【kafka消息 ===因公消费退款接收消息信息操作：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), record.value());
        KafkaBankRefundTaskMsg iMessage = KafkaConsumerUtils.invokeIMessage(record.value().toString(),
                KafkaBankRefundTaskMsg.class);
        uAcctCommonService.syncBankRefund(iMessage);
    }


    @KafkaListener(topics = {"topic_dech_trade_result"})
    public void bankResultNotic(ConsumerRecord<?, ?> record) {
        FinhubLogger.info("【kafka消息 ===银行上账结果通知：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), record.value());
        KafkaDechTradeResultMsg iMessage = KafkaConsumerUtils.invokeIMessage(record.value().toString(),
                KafkaDechTradeResultMsg.class);
        if (ObjUtils.isNull(iMessage)) {
            FinhubLogger.info("【kafka消费：银行上账结果通知 message为空");
            return;
        }
        String accountFlowId = iMessage.getAccountFlowId();
        Integer accountSubType = iMessage.getAccountSubType();
        Integer accountModel = iMessage.getAccountModel();
        if(ObjUtils.isBlank(accountFlowId)||ObjUtils.isNull(accountSubType)||ObjUtils.isNull(accountModel)){
            FinhubLogger.warn("kafka消费：银行上账结果通知 参数异常:{}", record.value());
        }
        String lockKey = MessageFormat.format(RedisKeyConstant.ACCOUNT_MSG_SYNC_BANK_KEY, accountFlowId);
        try {
            boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                FinhubLogger.info("【kafka消息 ===银行上账结果通知，未获取到锁{}", JsonUtils.toJson(iMessage));
                return;
            }

            uAcctCommonService.bankNotic(iMessage);
            //虚拟卡额度退回消息
            handlerVirtualCardByMsg(iMessage);
            //发券上账成功，启动发券任务
            handlerVoucherTaskByMsg(accountSubType, accountModel, accountFlowId, iMessage.getTxnSt());
            if (iMessage.getAccountSubType() == 0) {
                //担保户和收款户互转
                uBankAcctService.bankAcctTransfer4StereoSuccess(iMessage);
                uBankAcctService.bankAcctTransfer4StereoFailed(iMessage);
            }
            FinhubLogger.info("【kafka消息 ===银行上账结果通知，消息消费完成{}", JsonUtils.toJson(iMessage));
        } catch (Exception e) {
            FinhubLogger.error("4.0【新账户系统异常】银行上账结果通知，尝试加锁异常", e);
        } finally {
            try{
                redissonService.unLock(lockKey);
            } catch (Exception e){
                FinhubLogger.warn("释放锁失败", e);
            }
        }
    }
    public void handlerVirtualCardByMsg(KafkaDechTradeResultMsg iMessage){
        // 备用金账户
        if (FundAccountSubType.isComCardAccount(iMessage.getAccountSubType())) {
            applyCardManager.updateCreditTrapStatus(iMessage);
            applyCardManager.updateCreditDistributeTrapStatus(iMessage);
            if (BankNameEnum.isSpa(iMessage.getBankName())) {
                //额度下发
                applyCardManager.handleCardApplyCreditNotice(iMessage);
            }
        }
    }
    private void handlerVoucherTaskByMsg(Integer accountSubType, Integer accountModel, String freezenFlowId, String txnSt) {
        if (!FundAccountSubType.isIndividualAccount(accountSubType)) {
            return;
        }
        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryByFreezenFlowId(freezenFlowId);
        if(ObjUtils.isNull(fundFreezenFlow)){
            FinhubLogger.error("【新账户系统异常】银行上账结果通知，分贝券发放，冻结流水不存在,冻结流水id:{}", freezenFlowId);
            //发送钉钉消息
            dingDingMsgService.sendMsg("【新账户系统异常】银行上账结果通知，分贝券发放，冻结流水id：" + freezenFlowId);
            return;
        }
        String bizNo = fundFreezenFlow.getBizNo();
        String companyId = fundFreezenFlow.getCompanyId();
        Integer operationType = fundFreezenFlow.getOperationType();
        if(!FreezenChangeType.isFreezing(operationType)){
            return;
        }
        if (AccountStatusEnum.isFail(txnSt)) {
            uVoucherTaskService.failedVoucherTask(bizNo, companyId, "银行上账失败");
            return;
        }
        if (AccountStatusEnum.isSuccess(txnSt)) {
            uVoucherTaskService.startTaskNeedNotDeductionAccount(bizNo, companyId);
        }
    }
}
