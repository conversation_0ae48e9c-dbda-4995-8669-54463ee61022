package com.fenbeitong.fenbeipay.rpc.service.redcoupon;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponConsumType;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponOperationType;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponScopeType;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.FundFreezenFindRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.*;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponSearchService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezen;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcoupon;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponFlow;
import com.fenbeitong.fenbeipay.nf.unit.service.UFundFreezenService;
import com.fenbeitong.fenbeipay.redcoupon.manager.AccountRedcouponGrantRecordManager;
import com.fenbeitong.finhub.common.constant.FundAccountModelType;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.pay.search.dto.AccountRedcouponFlowReqDTO;
import com.fenbeitong.pay.search.dto.AccountRedcouponReqDTO;
import com.fenbeitong.pay.search.service.AccountRedcouponFlowService;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 红包券rpc查询实现类
 * @ClassName: IAccountRedcouponSearchServiceImpl
 * @Author: zhangga
 * @CreateDate: 2019/12/9 3:25 PM
 * @UpdateUser:
 * @UpdateDate: 2019/12/9 3:25 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iAccountRedcouponSearchService")
public class IAccountRedcouponSearchServiceImpl implements IAccountRedcouponSearchService {

    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    private AccountRedcouponFlowService accountRedcouponFlowService;
    @Autowired
    private UFundFreezenService uFundFreezenService;

    @Autowired
    private AccountRedcouponGrantRecordManager accountRedcouponGrantRecordManager;


    @Override
    public AccountRedcouponRespRPCDTO queryAccountCouponInfo(String companyId) throws FinhubException {
        //参数校验
        if (ObjUtils.isBlank(companyId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            AccountRedcouponRespRPCDTO dto = new AccountRedcouponRespRPCDTO();
            AccountRedcoupon accountRedcoupon = accountRedcouponSearchService.queryAccountCouponInfo(companyId);
            if (accountRedcoupon == null) {
                return null;
            }
            BeanUtils.copyProperties(accountRedcoupon, dto);
            FundFreezen fundFreezen = uFundFreezenService.queryByCompanyIdAndUseTypeModel(companyId, FundAccountSubType.REDCOUPON_ACCOUNT.getKey(), FreezenUseType.INDIVIDUAL_VOUCHERS.getKey(), FundAccountModelType.RECHARGE.getKey());
            if (ObjUtils.isNotEmpty(fundFreezen)) {
                FundFreezenFindRespRPCDTO findRespRPCDTO = new FundFreezenFindRespRPCDTO();
                BeanUtils.copyProperties(fundFreezen, findRespRPCDTO);
                dto.setFundFreezenFindRespRPCDTO(findRespRPCDTO);
            }
            return dto;
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券查询异常】参数：{}", companyId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券查询异常】参数：{}", companyId, e);
            throw e;
        }
    }

    @Override
    public ResponsePage<AccountRedcouponRespRPCDTO> queryAccountCouponList(AccountRedcouponSearchReqRPCDTO accountRedcouponReqRPCDTO) throws FinhubException {
        if (null == accountRedcouponReqRPCDTO) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            AccountRedcouponReqDTO queryDto = new AccountRedcouponReqDTO();
            BeanUtils.copyProperties(accountRedcouponReqRPCDTO, queryDto);
            ResponsePage<AccountRedcouponRespRPCDTO> data = new ResponsePage<>();
            ResponsePage<AccountRedcoupon> responsePage = accountRedcouponSearchService.queryAccountCouponList(queryDto);
            List<String> companyIds = new ArrayList<>();
            responsePage.getDataList().forEach(redcoupon->{
                companyIds.add(redcoupon.getCompanyId());
            });
            Map<String, FundFreezen> freezenMap = uFundFreezenService.queryByCompanyIdsAndUseType(companyIds, FundAccountSubType.REDCOUPON_ACCOUNT.getKey(), FreezenUseType.INDIVIDUAL_VOUCHERS.getKey(), FundAccountModelType.RECHARGE.getKey());
            List<AccountRedcouponRespRPCDTO> dataList = new ArrayList<>();
            if (ObjUtils.isNotEmpty(responsePage.getDataList())) {
                responsePage.getDataList().forEach(accountRedcoupon -> {
                    AccountRedcouponRespRPCDTO dto = new AccountRedcouponRespRPCDTO();
                    BeanUtils.copyProperties(accountRedcoupon, dto);
                    dto.setUseScopeDesc(RedcouponScopeType.getEnum(accountRedcoupon.getUseScope()).getDesc());
                    FundFreezen fundFreezen = freezenMap.get(accountRedcoupon.getCompanyId());
                    if (null != fundFreezen) {
                        FundFreezenFindRespRPCDTO findRespRPCDTO = new FundFreezenFindRespRPCDTO();
                        BeanUtils.copyProperties(fundFreezen, findRespRPCDTO);
                        dto.setFundFreezenFindRespRPCDTO(findRespRPCDTO);
                    }
                    dataList.add(dto);
                });
            }
            data.setDataList(dataList);
            data.setTotalCount(responsePage.getTotalCount());
            return data;
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券查询异常】参数：{}", accountRedcouponReqRPCDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券查询异常】参数：{}", accountRedcouponReqRPCDTO, e);
            throw e;
        }
    }

    @Override
    public ResponsePage<AccountRedcouponFlowRespRPCDTO> queryAccountCouponFlowList(AccountRedcouponFlowReqRPCDTO accountRedcouponFlowReqRPCDTO) throws FinhubException {
        if (null == accountRedcouponFlowReqRPCDTO) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            AccountRedcouponFlowReqDTO queryDto = new AccountRedcouponFlowReqDTO();
            BeanUtils.copyProperties(accountRedcouponFlowReqRPCDTO, queryDto);
            ResponsePage<AccountRedcouponFlowRespRPCDTO> data = new ResponsePage<>();
            ResponsePage<AccountRedcouponFlow> responsePage = accountRedcouponSearchService.queryAccountCouponFlowList(queryDto);
            List<AccountRedcouponFlowRespRPCDTO> dataList = getAccountRedcouponFlowRespRPCDTOList(responsePage.getDataList());
            //去掉红包券使用范围修改流水
            List<Integer> operationTypes = Lists.newArrayList(RedcouponOperationType.RED_COUPON_SCOPE_BUSINESS.getKey(),
                    RedcouponOperationType.RED_COUPON_SCOPE_PERSON.getKey(),RedcouponOperationType.RED_COUPON_SCOPE_ALL.getKey());
            dataList = dataList.stream().filter(datas -> !operationTypes.contains(datas.getOperationType())).collect(Collectors.toList());
            data.setDataList(dataList);
            data.setTotalCount(responsePage.getTotalCount());
            return data;
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券查询异常】参数：{}", accountRedcouponFlowReqRPCDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券查询异常】参数：{}", accountRedcouponFlowReqRPCDTO, e);
            throw e;
        }
    }

    @Override
    public List<AccountRedcouponFlowRespRPCDTO> queryAccountCouponOperationFlow(String companyId, String redcouponId) throws FinhubException {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(redcouponId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            AccountRedcouponFlowReqDTO queryDTO = new AccountRedcouponFlowReqDTO();
            // 只查询操作类型为 11-发放，12-撤回，13-修改有效期的类型
            queryDTO.setOperationTypeList(RedcouponOperationType.LOG_SHOW_TYPE);
            queryDTO.setCompanyId(companyId);
            queryDTO.setRedcouponId(redcouponId);
            queryDTO.setPageNo(1);
            queryDTO.setPageSize(100);
            ResponsePage<AccountRedcouponFlow> responsePage = accountRedcouponSearchService.queryAccountCouponFlowList(queryDTO);
            List<AccountRedcouponFlowRespRPCDTO> dataList = getAccountRedcouponFlowRespRPCDTOList(responsePage.getDataList());
            return dataList;
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券查询异常】参数：{}，{}", companyId, redcouponId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券查询异常】参数：{}，{}", companyId, redcouponId, e);
            throw e;
        }
    }

    @Override
    public BigDecimal queryAccountCouponOperationAmountByBizNo(String bizNo, int redcouponType, List<Integer> operationTypes) {
        return accountRedcouponSearchService.queryAccountCouponOperationAmountByBizNo(bizNo, redcouponType, operationTypes);
    }

    @Override
    public BigDecimal queryAccountCouponOperationAmountByReBizNo(String reBizNo, Integer redcouponType, Integer operationType) {
        return accountRedcouponSearchService.queryAccountCouponOperationAmountByReBizNo(reBizNo, redcouponType, operationType);
    }

    @Override
    public ResponsePage<AccountRedcouponGrantRespRPCDTO> findAccountCouponGrantList(RedcouponGrantFlowReqDTO reqDTO) {
        try {
            ValidateUtils.validate(reqDTO);
            AccountRedcoupon accountRedcoupon = accountRedcouponSearchService.findAccountCouponByRedcouponId(reqDTO.getAccountRedcouponId());
            return accountRedcouponFlowService.findAccountCouponGrantList(reqDTO, accountRedcoupon);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券查询异常】参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券查询异常】参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<AccountRedcouponGrantRespRPCDTO> exportAccountCouponGrantList(AccountRedcouponSearchReqRPCDTO reqRPCDTO) {
        try {
            ValidateUtils.validate(reqRPCDTO);
            List<AccountRedcoupon> accountRedcoupons = accountRedcouponSearchService.findAccountCouponGrantList(reqRPCDTO);
            if (CollectionUtils.isEmpty(accountRedcoupons)){
                return Lists.newArrayList();
            }
            return accountRedcouponFlowService.findAccountCouponFlowGrantList(accountRedcoupons);
        } catch (FinPayException e) {
            FinhubLogger.error("【导出红包券查询异常】参数：{}", JsonUtils.toJson(reqRPCDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【导出红包券查询异常】参数：{}", JsonUtils.toJson(reqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    //=======================================Private Method====================================

    private List<AccountRedcouponFlowRespRPCDTO> getAccountRedcouponFlowRespRPCDTOList(List<AccountRedcouponFlow> responsePage) {
        List<AccountRedcouponFlowRespRPCDTO> dataList = new ArrayList<>();
        if (ObjUtils.isNotEmpty(responsePage)) {
            responsePage.forEach(accountRedcoupon -> {
                AccountRedcouponFlowRespRPCDTO dto = new AccountRedcouponFlowRespRPCDTO();
                BeanUtils.copyProperties(accountRedcoupon, dto);
                if (
                        dto.getOperationType() == RedcouponOperationType.STEREO_RECALL.getKey()
                        || dto.getOperationType() == RedcouponOperationType.PUBLIC_CONSUME.getKey()
                        || dto.getOperationType() == RedcouponOperationType.SYSTEM_TIME_RECALL.getKey()
                ) {
                    dto.setOperationAmount(dto.getOperationAmount().negate());
                }
                dataList.add(dto);
            });
        }
        return dataList;
    }


    @Override
    public ResponsePage<RedcouponGrantRecordDTO> queryRedcouponRecordPage(RedcouponGrantRecordPageReq redcouponGrantRecordPageReq) {
        ValidateUtils.validate(redcouponGrantRecordPageReq);
        ResponsePage<RedcouponGrantRecordDTO> redcouponGrantRecordDTOResponsePage = new ResponsePage<>();
        int count = accountRedcouponGrantRecordManager.count(redcouponGrantRecordPageReq);
        redcouponGrantRecordDTOResponsePage.setTotalCount(count);
        List<RedcouponGrantRecordDTO> accountRedcouponGrantRecords = Lists.newArrayList();
        if(count>=0){
            accountRedcouponGrantRecords = accountRedcouponGrantRecordManager.queryList(redcouponGrantRecordPageReq);
        }
        redcouponGrantRecordDTOResponsePage.setDataList(accountRedcouponGrantRecords);
        return redcouponGrantRecordDTOResponsePage;
    }

    @Override
    public ResponsePage<RedcouponAcctFlowDTO> queryRedcouponAcctFlowPage(RedcouponFlowPageReq redcouponFlowPageReq) {
        if(Objects.nonNull(redcouponFlowPageReq)&&Objects.nonNull(redcouponFlowPageReq.getConsumeType())){
            redcouponFlowPageReq.setOperationTypes(RedcouponConsumType.getOperateTypes(redcouponFlowPageReq.getConsumeType()));
        }
        ResponsePage<RedcouponAcctFlowDTO> redcouponAcctFlowDTOResponsePage = new ResponsePage<>();
        int count = accountRedcouponFlowService.selectCount(redcouponFlowPageReq);
        redcouponAcctFlowDTOResponsePage.setTotalCount(count);
        if(count>0){
             List<RedcouponAcctFlowDTO> redcouponAcctFlowDTOS = accountRedcouponFlowService.selectPage(redcouponFlowPageReq);
             redcouponAcctFlowDTOResponsePage.setDataList(redcouponAcctFlowDTOS);
        }
        return redcouponAcctFlowDTOResponsePage;
    }

    @Override
    public RedcouponGrantRecordDTO queryGrantRecordByRecordId(String grantRecordId) {
        return accountRedcouponGrantRecordManager.queryRecordDTOByRecordId(grantRecordId);
    }

    @Override
    public RedcouponFlowDetailDTO queryFlowByFlowId(String flowId) {
        RedcouponFlowDetailDTO redcouponFlowDetailDTO = new RedcouponFlowDetailDTO();
        if(StringUtils.isBlank(flowId)){
            return redcouponFlowDetailDTO;
        }
        RedcouponAcctFlowDTO redcouponAcctFlowDTO = accountRedcouponFlowService.queryFlowByFlowId( flowId);
        redcouponFlowDetailDTO.setRedcouponAcctFlowDTO(redcouponAcctFlowDTO);
        if(Objects.nonNull(redcouponAcctFlowDTO)){
            RedcouponGrantRecordDTO redcouponGrantRecordDTO = accountRedcouponGrantRecordManager.queryRecordDTOByRecordId(redcouponAcctFlowDTO.getGrantRecordId());
            redcouponFlowDetailDTO.setRedcouponGrantRecordDTO(redcouponGrantRecordDTO);
        }
        return redcouponFlowDetailDTO;
    }

    @Override
    public List<RedcouponGrantRecordDTO> queryValidGrantRecordByAcctId(String redcouponId) {
        List<RedcouponGrantRecordDTO> redcouponGrantRecordDTOS = accountRedcouponGrantRecordManager.queryValidGrantRecordByAcctId(redcouponId);
        return redcouponGrantRecordDTOS;
    }
}
