package com.fenbeitong.fenbeipay.rpc.service.na;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.QueryAccountBalanceRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.QueryAccountBalanceRespDTO;
import com.fenbeitong.dech.api.service.airwallex.AirwallexAuthPayService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditRecoverTaskService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyCardService;
import com.fenbeitong.fenbeipay.acctdech.service.PayCompanyAuthService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCommonOptRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByAcctTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwBySubAndBankName2ReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwBySubAndBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.*;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountFlowVO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountSubFlowFindVO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubService;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.SearchCardManager;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.dto.freezen.FreezenOperationReqDTO;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierPayRedcouponFilterVo;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezen;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcoupon;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountGeneralService;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountSubService;
import com.fenbeitong.fenbeipay.nf.unit.service.UFundFreezenService;
import com.fenbeitong.fenbeipay.redcoupon.service.AccountRedcouponService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service("iAccountSubService")
public class IAccountSubServiceImpl implements IAccountSubService {

    @Autowired
    private UAccountGeneralService uAccountGeneralService;

    @Autowired
    private UAccountSubService uAccountSubService;

    @Autowired
    private UFundFreezenService uFundFreezenService;

    @Autowired
    private AccountRedcouponService accountRedcouponService;

    @Autowired
    private SearchCardManager searchCardManager;

    @Autowired
    private IVouchersPersonService iVouchersPersonService;

    @Autowired
    private UAcctBusinessCreditFlowService uAcctBusinessCreditFlowService;

    @Autowired
    private UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    private UAcctBusinessDebitFlowService uAcctBusinessDebitFlowService;

    @Autowired
    private UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    private UAcctIndividualCreditFlowService uAcctIndividualCreditFlowService;

    @Autowired
    private UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    private UAcctIndividualDebitFlowService uAcctIndividualDebitFlowService;

    @Autowired
    private UAcctIndividualDebitService uAcctIndividualDebitService;

    @Autowired
    private AcctCompanyGatewayService acctCompanyGatewayService;

    @Autowired
    protected UAcctCommonService uAcctCommonService;

    @Autowired
    protected AcctBusinessCreditRecoverTaskService acctBusinessCreditRecoverTaskService;
    @Autowired
    protected UAcctCommonCreditService uAcctCommonCreditService;

    @Autowired
    private AcctCompanyCardService acctCompanyCardService;

    @Autowired
    protected RedissonService redissonService;


    @Autowired
    private DingDingMsgService dingDingMsgService;

    @Autowired
    PayCompanyAuthService payCompanyAuthService;
    @Autowired
    AirwallexAuthPayService airwallexAuthPayService;

    @Override
    public AccountSubFindRespRPCDTO queryAccountSubInfo(AccountSubFindReqRPCDTO accountSubFindDTO) throws FinhubException {
        try {
            ValidateUtils.validate(accountSubFindDTO);
            // 公司下个人消费或者企业消费下的 处于激活状态的子账户
            //虚拟卡 accountSubType = 5
            AcctCommonBaseDTO accountSub = null;
            AcctComGwByAcctTypeReqDTO acctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
            acctTypeReqDTO.setFundAccountType(accountSubFindDTO.getAccountSubType());
            acctTypeReqDTO.setCompanyId(accountSubFindDTO.getCompanyId());
            if(ObjUtils.isEmpty(accountSubFindDTO.getCompanyId())){
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
            }
            accountSub = acctCompanyGatewayService.findActCommonByAcctType(acctTypeReqDTO);
            if (ObjUtils.isEmpty(accountSub)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
            }

            AccountSubFindRespRPCDTO accountSubFindRespRPCDTO = new AccountSubFindRespRPCDTO();
            BeanUtils.copyProperties(accountSub, accountSubFindRespRPCDTO);
            FundFreezenFindRespRPCDTO findRespRPCDTO;
            if (accountSubFindDTO.isFindVoucher()) {
                findRespRPCDTO = new FundFreezenFindRespRPCDTO();
                FundFreezen fundFreezen = uFundFreezenService.queryByCompanyIdAndUseTypeModel(accountSubFindDTO.getCompanyId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountSubFindDTO.getAccountModel(), FreezenUseType.INDIVIDUAL_VOUCHERS.getKey());
                if (ObjUtils.isNotEmpty(fundFreezen)) {
                    BeanUtils.copyProperties(fundFreezen, findRespRPCDTO);
                    accountSubFindRespRPCDTO.setFundFreezenFindRespRPCDTO(findRespRPCDTO);
                }
            }
            // 查询帐户和帐户余额
            boolean isAirwallex = queryAccountOfAirwallex(accountSubFindDTO.getCompanyId());
            if (isAirwallex){
                accountSubFindRespRPCDTO.setBalance(checkBalanceAvailable(accountSubFindDTO.getCompanyId()));
            }
            return accountSubFindRespRPCDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】alterAccountModel 参数：{},账户余额不足", accountSubFindDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】queryAccountSubInfo 参数：{}", accountSubFindDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】queryAccountSubInfo 参数：{}", accountSubFindDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】queryAccountSubInfo 参数：{}", accountSubFindDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    public boolean queryAccountOfAirwallex(String companyId) {
        try {
            CompanyAuthRecord companyAuthRecord = payCompanyAuthService.queryAuthRecordByCompanyId(companyId);
            return companyAuthRecord != null && companyAuthRecord.getStatus() == 1;
        }catch (Exception e){
            FinhubLogger.warn("queryAuthResultByAirwallex:{}" + companyId,e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode());
        }
    }
    public BigDecimal checkBalanceAvailable(String companyId){
        //查询余额
        BigDecimal balanceOfAirwallex = BigDecimal.ZERO;
        QueryAccountBalanceRequestDTO queryAccountBalanceRequestDTO = new QueryAccountBalanceRequestDTO();
        queryAccountBalanceRequestDTO.setCompanyId(companyId);
        try {
            QueryAccountBalanceRespDTO queryAccountBalanceRespDTO = airwallexAuthPayService.queryAWAccountBalance(queryAccountBalanceRequestDTO);
            FinhubLogger.info("查询airwallex账户余额->{}", JSON.toJSONString(queryAccountBalanceRespDTO));
            if (ObjUtils.isNotEmpty(queryAccountBalanceRespDTO)){
                balanceOfAirwallex = queryAccountBalanceRespDTO.getAvailableAmount();
            }
            return balanceOfAirwallex;
        }catch (Exception e){
            FinhubLogger.warn("queryAWAccountBalance:{}" + companyId,e);
            throw new FinAccountNoEnoughException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getCode(),GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getMsg(),GlobalResponseCode.ACCOUNT_SUB_BUSINESS_AIRWALLEX_NOT_ENOUGH.getType());
        }
    }

    /**
     * 虚拟卡额度下发时，检查公司账户是否余额充足
     * @param req
     * @return
     */
    @Override
    public ResultRespDTO<CompanyCardDistributeAmountCheckResp> checkCompanyCardBalance(CompanyCardDistributeAmountCheckReq req) {
        try {
            FinhubLogger.info("checkCompanyCardBalance调用，req:{}", req);
            ResultRespDTO<CompanyCardDistributeAmountCheckResp> resp = new ResultRespDTO<>();
            resp.setCode(0);
            ValidateUtils.validate(req);
            if (req.getAccountAmountList().size() == 0) {
                throw new ValidateException("参数accountAmountList为空");
            }
            List<AcctCompanyCard> companyCardSubAccountList = acctCompanyCardService.getAccountListByActiveStatus(req.getCompanyId(), FundAcctActStatusEnum.ACTIVATE.getStatus());
            if (companyCardSubAccountList.size() > 1) {
                // 一人多卡企业
                for (CompanyCardSubAccountAmountDto accountAmountDto : req.getAccountAmountList()) {
                    boolean checkSuccess = false;
                    for (AcctCompanyCard account : companyCardSubAccountList) {
                        if (account.getBankName().equals(accountAmountDto.getBankName())) {
                            if (account.getBalance().compareTo(accountAmountDto.getAmount()) < 0) {
                                // 金额不足
                                resp.setData(new CompanyCardDistributeAmountCheckResp(false, GlobalResponseCode.ACCOUNT_SUB_NOT_ENOUGH.getCode(), "备用金电子账户余额不足"));
                                FinhubLogger.info("checkCompanyCardBalance调用正常返回，resp:{}", resp);
                                return resp;
                            } else {
                                // 这个子账户 银行、金额 校验通过
                                checkSuccess = true;
                                break;
                            }
                        }
                    }
                    if (!checkSuccess) {
                        // 未找到已激活的账户
                        resp.setData(new CompanyCardDistributeAmountCheckResp(false, GlobalResponseCode.ACCOUNT_SUB_STATUS_DISABLE.getCode(), "备用金电子账户不存在或未激活"));
                        FinhubLogger.error("checkCompanyCardBalance调用，一人多卡路径，备用金电子账户不存在或未激活，req:{}, resp:{}", req, resp);
                        return resp;
                    }
                }
                resp.setData(new CompanyCardDistributeAmountCheckResp(true, null, null));
                FinhubLogger.info("checkCompanyCardBalance调用正常返回，resp:{}", resp);
                return resp;
            } else { // companyCardSubAccountList.size() <= 1，走原有逻辑查询（同queryAccountSubInfo）
                if (req.getAccountAmountList().size() != 1) {
                    throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "企业仅有一个激活备用金账户（非一人多卡），查询入参却传多个账户参数。");
                }
                // 非一人多卡，使用原有查询逻辑
                AcctComGwByAcctTypeReqDTO acctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
                acctTypeReqDTO.setFundAccountType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
                acctTypeReqDTO.setCompanyId(req.getCompanyId());
                AcctCommonBaseDTO accountSub = acctCompanyGatewayService.findActCommonByAcctType(acctTypeReqDTO);
                if (ObjUtils.isEmpty(accountSub)) {
                    throw new FinhubException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST.getCode(), GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST.getMsg());
                }
                CompanyCardSubAccountAmountDto accountAmountDto = req.getAccountAmountList().get(0);
                ValidateUtils.validate(accountAmountDto);
                if (!accountSub.getBankName().equals(accountAmountDto.getBankName())) {
                    throw new FinhubException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST.getCode(), "查询账户银行与备用金电子账户的银行不一致");
                }
                if (!FundAcctActStatusEnum.isAct(accountSub.getActiveStatus())) {
                    // throw new FinhubException(GlobalResponseCode.ACCOUNT_SUB_STATUS_DISABLE.getCode(), "备用金电子账户未激活");
                    FinhubLogger.error("checkCompanyCardBalance调用，一人一卡路径，备用金电子账户不存在或未激活，{}", req);
                    resp.setData(new CompanyCardDistributeAmountCheckResp(false, GlobalResponseCode.ACCOUNT_SUB_STATUS_DISABLE.getCode(), "备用金电子账户未激活"));
                }
                if (accountSub.getBalance().compareTo(accountAmountDto.getAmount()) < 0) {
                    // throw new FinhubException(GlobalResponseCode.ACCOUNT_SUB_NOT_ENOUGH.getCode(), "备用金电子账户余额不足");
                    resp.setData(new CompanyCardDistributeAmountCheckResp(false, GlobalResponseCode.ACCOUNT_SUB_NOT_ENOUGH.getCode(), "备用金电子账户余额不足"));
                }
                resp.setData(new CompanyCardDistributeAmountCheckResp(true, null, null));
                FinhubLogger.info("checkCompanyCardBalance调用正常返回，resp:{}", resp);
                return resp;
            }
        } catch (Exception e) {
            ResultRespDTO<CompanyCardDistributeAmountCheckResp> resp = new ResultRespDTO<>();
            if (e instanceof FinhubException) {
                FinhubLogger.error("调用checkCompanyCardBalance发生业务错误，req：{}, errormsg：{}", req, e.getMessage(), e);
                resp.setCode(((FinhubException) e).getCode());
                resp.setMsg(e.getMessage());
            } else if (e instanceof ValidateException) {
                resp.setCode(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
                resp.setMsg(e.getMessage());
            } else {
                FinhubLogger.error("调用checkCompanyCardBalance发生系统异常，req：{}, errormsg：{}", req, e.getMessage(), e);
                resp.setCode(GlobalResponseCode.EXCEPTION.getCode());
                resp.setMsg(e.getMessage());
            }
            return resp;
        }
    }


    @Override
    public AccountSubRespDTO queryActAccountSubInfo(AccountSubFindReqRPCDTO accountSubFindDTO) throws FinhubException {
        AcctComGwByAcctTypeReqDTO acctComGwByAcctTypeReqDTO = new AcctComGwByAcctTypeReqDTO();
        acctComGwByAcctTypeReqDTO.setFundAccountType(accountSubFindDTO.getAccountSubType());
        acctComGwByAcctTypeReqDTO.setCompanyId(accountSubFindDTO.getCompanyId());
        AcctCommonBaseDTO accountSub = acctCompanyGatewayService.findActCommonByAcctType(acctComGwByAcctTypeReqDTO);
        if (Objects.isNull(accountSub)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
        }
        AccountSubRespDTO dto = new AccountSubRespDTO();
        BeanUtils.copyProperties(accountSub, dto);
        dto.setAccountSubId(accountSub.getAccountId());
        return dto;
    }

    @Override
    public AccountSubRespDTO queryActAccountSubInfo(String companyId, Integer accountSubType) throws FinhubException {
        if (ObjUtils.isBlank(companyId)) {
            return null;
        }
        AccountSubFindReqRPCDTO accountSubFindReqRPCDTO = new AccountSubFindReqRPCDTO();
        accountSubFindReqRPCDTO.setCompanyId(companyId);
        accountSubFindReqRPCDTO.setAccountSubType(accountSubType);
        return queryActAccountSubInfo(accountSubFindReqRPCDTO);
    }

    @Override
    public BigDecimal queryAccountSubBalance(String companyId, Integer accountSubType) throws FinhubException {
        //激活的子账户
        AcctComGwBySubAndBankReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwBySubAndBankReqDTO();
        acctComGwBySubAndBankReqDTO.setBankAccountNo(companyId);
        acctComGwBySubAndBankReqDTO.setFundAccountType(accountSubType);
        acctComGwBySubAndBankReqDTO.setCompanyId(companyId);
        acctComGwBySubAndBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
        List<AcctCommonBaseDTO> accountSubs = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
        if (ObjUtils.isNull(accountSubs.get(0)) || accountSubs.get(0).getAccountStatus() == CoreConstant.STATUS_DISABLE) {
            return BigDecimal.ZERO;
        }
        return accountSubs.get(0).getBalance();
    }


    @Override
    public List<AccountSubRespDTO> queryAccountSubInfoList(List<String> companyIds, Integer accountSubType) throws FinhubException {
        try {
            if (ObjUtils.isEmpty(companyIds) || ObjUtils.isEmpty(accountSubType)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            List<AccountSubRespDTO> dataList = new ArrayList<>();
            AcctComGwBySubAndBankName2ReqDTO acctComGwBySubAndBankName2ReqDTO = new AcctComGwBySubAndBankName2ReqDTO();
            acctComGwBySubAndBankName2ReqDTO.setBankName(BankNameEnum.FBT.getCode());
            acctComGwBySubAndBankName2ReqDTO.setFundAccountType(accountSubType);
            acctComGwBySubAndBankName2ReqDTO.setCompanyIds(companyIds);
            List<AcctCommonBaseDTO> accountSubs = acctCompanyGatewayService.findActCommonBySubAndBankName(acctComGwBySubAndBankName2ReqDTO);
            if (ObjUtils.isEmpty(accountSubs)) {
                return dataList;
            }
            accountSubs.forEach(accountSub -> {
                AccountSubRespDTO dto = new AccountSubRespDTO();
                BeanUtils.copyProperties(accountSub, dto);
                dataList.add(dto);
            });
            return dataList;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】queryAccountSubInfoList 参数：{},账户余额不足", companyIds, accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】queryAccountSubInfoList 参数：{}", companyIds, payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】queryAccountSubInfoList 参数：{}", companyIds, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountAlterModelRespRPCDTO alterAccountModel(AccountAlterModelReqRPCDTO accountAlterModelDTO) throws FinhubException {
        return null;
    }


    /**
     * 调额
     *
     * @param accountAdjustDTO
     * @return
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO adjust(AccountSubOperationReqRPCDTO accountAdjustDTO) throws FinhubException {
        try {
            ValidateUtils.validate(accountAdjustDTO);

            AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
            accountGeneralBankRPCDTO.setBankNo(accountAdjustDTO.getCompanyId());
            accountGeneralBankRPCDTO.setCompanyId(accountAdjustDTO.getCompanyId());
            accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            AcctAdjustCreditReqDTO acctTransferCreditReqDTO = new AcctAdjustCreditReqDTO();
            BeanUtils.copyProperties(accountAdjustDTO, acctTransferCreditReqDTO);
            acctTransferCreditReqDTO.setBankAccountNo(accountAdjustDTO.getCompanyId());
            acctTransferCreditReqDTO.setBankName(BankNameEnum.FBT.getCode());
            AcctCommonOptRespDTO adjust = null;
            if (FundAccountSubType.isBusinessAccount(accountAdjustDTO.getAccountSubType())) {
                adjust = uAcctBusinessCreditService.adjust(acctTransferCreditReqDTO);
            } else if (FundAccountSubType.isIndividualAccount(accountAdjustDTO.getAccountSubType())) {
                adjust = uAcctIndividualCreditService.adjust(acctTransferCreditReqDTO);
            } else {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_NOT_EXIST);
            }
            if (Objects.isNull(adjust)) {
                return null;
            }
            AccountSubOperationRespRPCDTO accountSubOperationRespRPCDTO = new AccountSubOperationRespRPCDTO();
            BeanUtils.copyProperties(adjust, accountSubOperationRespRPCDTO);
            accountSubOperationRespRPCDTO.setAccountSubId(adjust.getAccountId());
            return accountSubOperationRespRPCDTO;
//            return uAccountSubService.adjust(accountAdjustDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】adjust 参数：{},账户余额不足", accountAdjustDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", accountAdjustDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", accountAdjustDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", accountAdjustDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO repayment(AccountSubRepaymentReqRPCDTO accountSubRepaymentDTO) throws FinhubException {
        try {
            ValidateUtils.validate(accountSubRepaymentDTO);
            AccountSubOperationRespRPCDTO subOperationRespRPCDTO = new AccountSubOperationRespRPCDTO();
            //授信模式才可以
//            AccountSub accountSub = uAccountSubService.findByCompanyIdAndAccountSubType(accountSubRepaymentDTO.getCompanyId(), accountSubRepaymentDTO.getAccountSubType(), AccountModelType.CREDIT.getKey());
            if (FundAccountSubType.isBusinessAccount(accountSubRepaymentDTO.getAccountSubType().intValue())) {
                AcctBusinessCredit businessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(accountSubRepaymentDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubRepaymentDTO.getCompanyId());
                if (Objects.isNull(businessCredit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
                }
                //根据子账户类型，账户模式模式，激活状态决定是否可以操作
                if (ObjUtils.isNotEmpty(accountSubRepaymentDTO.getFundAccountOptType())) {
                    boolean checkOpt = FundAcctCheckOptEnum.checkOpt(FundAccountSubType.BUSINESS_ACCOUNT.getKey(), FundAccountModelType.CREDIT.getKey(), businessCredit.getActiveStatus(), accountSubRepaymentDTO.getFundAccountOptType());
                    if (!checkOpt) {
                        throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_OPT_CHECK_ERROR);
                    }
                }
                accountSubRepaymentDTO.setAccountSubId(businessCredit.getAccountId());
            } else if (FundAccountSubType.isIndividualAccount(accountSubRepaymentDTO.getAccountSubType().intValue())) {
                AcctIndividualCredit individualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(accountSubRepaymentDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubRepaymentDTO.getCompanyId());
                if (Objects.isNull(individualCredit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
                }
                //根据子账户类型，账户模式模式，激活状态决定是否可以操作
                if (ObjUtils.isNotEmpty(accountSubRepaymentDTO.getFundAccountOptType())) {
                    boolean checkOpt = FundAcctCheckOptEnum.checkOpt(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), FundAccountModelType.CREDIT.getKey(), individualCredit.getActiveStatus(), accountSubRepaymentDTO.getFundAccountOptType());
                    if (!checkOpt) {
                        throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_OPT_CHECK_ERROR);
                    }
                }
                accountSubRepaymentDTO.setAccountSubId(individualCredit.getAccountId());
            } else {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
            }
            AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
            accountGeneralBankRPCDTO.setBankNo(accountSubRepaymentDTO.getCompanyId());
            accountGeneralBankRPCDTO.setCompanyId(accountSubRepaymentDTO.getCompanyId());
            accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            //先扣服务费(消费)，再扣应还金额(账单还款)
            if (accountSubRepaymentDTO.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                AccountGeneralOperationRPCDTO accountConsumeDTO = new AccountGeneralOperationRPCDTO();
                BeanUtils.copyProperties(accountSubRepaymentDTO, accountConsumeDTO);
                accountConsumeDTO.setOperationAmount(accountSubRepaymentDTO.getServiceFee());
                //服务费按照消费
                accountConsumeDTO.setFundAccountOptType(FundAccountOptType.CONSUME.getKey());
                accountConsumeDTO.setBankName(BankNameEnum.FBT.getCode());
                accountConsumeDTO.setBankNo(accountGeneral.getBankAccountNo());
                uAccountGeneralService.serviceChargeConsume(accountConsumeDTO);
            }
            if (accountSubRepaymentDTO.getRepaymentAmount().compareTo(BigDecimal.ZERO) > 0) {
                AccountGeneralOperationRPCDTO billRepaymentDTO = new AccountGeneralOperationRPCDTO();
                BeanUtils.copyProperties(accountSubRepaymentDTO, billRepaymentDTO);
                billRepaymentDTO.setOperationAmount(accountSubRepaymentDTO.getRepaymentAmount());
                billRepaymentDTO.setBankName(BankNameEnum.FBT.getCode());
                billRepaymentDTO.setBankNo(accountGeneral.getBankAccountNo());
                uAccountGeneralService.billRepayment(billRepaymentDTO);
//                subOperationRespRPCDTO = uAccountSubService.repayment(accountSubRepaymentDTO);
                // 对手账户   从余额账户？
                AcctRepaymentCreditReqDTO acctRepaymentCreditReqDTO = new AcctRepaymentCreditReqDTO();
                BeanUtils.copyProperties(accountSubRepaymentDTO, acctRepaymentCreditReqDTO);
                acctRepaymentCreditReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
                acctRepaymentCreditReqDTO.setTargetBankName(accountGeneral.getBankName());
                acctRepaymentCreditReqDTO.setTargetBankAcctId(accountGeneral.getBankAcctId());
                acctRepaymentCreditReqDTO.setBankAccountNo(accountGeneral.getCompanyId());
                acctRepaymentCreditReqDTO.setBankName(BankNameEnum.FBT.getCode());
                if (FundAccountSubType.isBusinessAccount(accountSubRepaymentDTO.getAccountSubType().intValue())) {
                    AcctCommonOptRespDTO repayment = uAcctBusinessCreditService.repayment(acctRepaymentCreditReqDTO);
                    BeanUtils.copyProperties(repayment, subOperationRespRPCDTO);
                    subOperationRespRPCDTO.setAccountSubId(repayment.getAccountId());
                    subOperationRespRPCDTO.setAccountFlowId(repayment.getAccountFlowId());
                } else {
                    AcctCommonOptRespDTO repayment = uAcctIndividualCreditService.repayment(acctRepaymentCreditReqDTO);
                    BeanUtils.copyProperties(repayment, subOperationRespRPCDTO);
                    subOperationRespRPCDTO.setAccountSubId(repayment.getAccountId());
                    subOperationRespRPCDTO.setAccountFlowId(repayment.getAccountFlowId());
                }
//                subOperationRespRPCDTO = uAcctService.repayment(accountSubRepaymentDTO);
            }
            return subOperationRespRPCDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】repayment 参数：{},账户余额不足", accountSubRepaymentDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】repayment 参数：{}", accountSubRepaymentDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】repayment 参数：{}", accountSubRepaymentDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 转移额度到指定账户（先调减，后调增）
     *
     * @param reqRPCDTO
     * @return
     * @throws FinhubException
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO adjustToSub(AccountSubAdjustToSubReqRPCDTO reqRPCDTO) throws FinhubException {
        boolean lock = false;
        String lockKey = CoreConstant.ACCT_ADJUST_CREDIT_PREFIX + reqRPCDTO.getCompanyId();
        try {
            FinhubLogger.info("转移额度adjustToSub reqDto:{}", JSONObject.toJSONString(reqRPCDTO));
            lock = redissonService.tryLock(CoreConstant.ACCT_ADJUST_CREDIT_WAIT_TIME, CoreConstant.ACCT_ADJUST_CREDIT_LOCK_TIME, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                FinhubLogger.error("转移额度lock error adjustToSub reqDto:{}", JSONObject.toJSONString(reqRPCDTO));
                throw new FinhubException(GlobalResponseCode.REDIS_GET_LOCK_ERROR.getCode(), GlobalResponseCode.REDIS_GET_LOCK_ERROR.getType(), GlobalResponseCode.REDIS_GET_LOCK_ERROR.getMsg());
            }
            AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(reqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), reqRPCDTO.getCompanyId());
            AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(reqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), reqRPCDTO.getCompanyId());
            // 参数校验，操作金额，操作类型校验
            adjustToSubCheck(reqRPCDTO, acctBusinessCredit ,acctIndividualCredit);
            // 构建操作参数
            BigDecimal operationAmount = reqRPCDTO.getOperationAmount();
            if (FundAccountSubType.isBusinessAccount(reqRPCDTO.getAccountSubType())) {
                operationAmount = operationAmount.negate();
            }
            AcctTransferCreditToEachReqDTO reqDTO = new AcctTransferCreditToEachReqDTO();
            BeanUtils.copyProperties(reqRPCDTO, reqDTO);
            AcctBusinessCreditRecoverTask businessRecoverTask = acctBusinessCreditRecoverTaskService.queryByAccountId(acctBusinessCredit.getAccountId());
            uAcctCommonCreditService.adjustTempFromSub(reqDTO, acctBusinessCredit, acctIndividualCredit, businessRecoverTask, operationAmount);
            return AccountSubOperationRespRPCDTO.of(acctBusinessCredit.getAccountId());
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】adjustToSub 参数：{}", reqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        } finally {
            if (lock) {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    FinhubLogger.error("adjustToSub释放锁失败,lockKey：{}", lockKey, e);
                }
            }
        }
    }


    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO subToGeneral(AccountSubOperationReqRPCDTO generalDTO) throws FinhubException {
        try {
            ValidateUtils.validate(generalDTO);
            //与激活状态无关
//            AccountSub accountSub = uAccountSubService.findByCompanyIdAndAccountSubType(generalDTO.getCompanyId(), generalDTO.getAccountSubType(), AccountModelType.RECHARGE.getKey());
            if (FundAccountSubType.isBusinessAccount(generalDTO.getAccountSubType().intValue())) {
                AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(generalDTO.getCompanyId(), BankNameEnum.FBT.getCode(), generalDTO.getCompanyId());
                if (Objects.isNull(acctBusinessDebit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
                }
                //根据子账户类型，账户模式模式，激活状态决定是否可以操作
                if (ObjUtils.isNotEmpty(generalDTO.getFundAccountOptType())) {
                    boolean checkOpt = FundAcctCheckOptEnum.checkOpt(FundAccountSubType.BUSINESS_ACCOUNT.getKey(), FundAccountModelType.RECHARGE.getKey(), acctBusinessDebit.getActiveStatus(), generalDTO.getFundAccountOptType());
                    if (!checkOpt) {
                        throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_OPT_CHECK_ERROR);
                    }
                }
                generalDTO.setAccountSubId(acctBusinessDebit.getAccountId());
            } else if (FundAccountSubType.isIndividualAccount(generalDTO.getAccountSubType().intValue())) {
                AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(generalDTO.getCompanyId(), BankNameEnum.FBT.getCode(), generalDTO.getCompanyId());
                if (Objects.isNull(acctIndividualDebit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
                }
                //根据子账户类型，账户模式模式，激活状态决定是否可以操作
                if (ObjUtils.isNotEmpty(generalDTO.getFundAccountOptType())) {
                    boolean checkOpt = FundAcctCheckOptEnum.checkOpt(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), FundAccountModelType.RECHARGE.getKey(), acctIndividualDebit.getActiveStatus(), generalDTO.getFundAccountOptType());
                    if (!checkOpt) {
                        throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_OPT_CHECK_ERROR);
                    }
                }
                generalDTO.setAccountSubId(acctIndividualDebit.getAccountId());
            } else {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
            }

            //主账户
            AccountGeneralTransferRPCDTO operationRPCDTO = new AccountGeneralTransferRPCDTO();
            BeanUtils.copyProperties(generalDTO, operationRPCDTO);
            AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
            accountGeneralBankRPCDTO.setBankNo(operationRPCDTO.getCompanyId());
            accountGeneralBankRPCDTO.setCompanyId(operationRPCDTO.getCompanyId());
            accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            operationRPCDTO.setBankNo(accountGeneral.getBankAccountNo());
            operationRPCDTO.setBankName(accountGeneral.getBankName());
            operationRPCDTO.setRemark(generalDTO.getOperationDescription());
            //扣款
//            AccountSubOperationRespRPCDTO subOperationRespRPCDTO = uAccountSubService.transferOut(generalDTO);
            AccountSubOperationRespRPCDTO subOperationRespRPCDTO = null;
            AcctCommonOptRespDTO acctCommonOptRespDTO = null;
            AcctTransferCommonReqDTO acctTransferCommonReqDTO = new AcctTransferCommonReqDTO();
            BeanUtils.copyProperties(generalDTO, acctTransferCommonReqDTO);
            acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
            acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
            acctTransferCommonReqDTO.setBankAccountNo(generalDTO.getCompanyId());
            acctTransferCommonReqDTO.setBankName(BankNameEnum.FBT.getCode());
            acctTransferCommonReqDTO.setRemark(generalDTO.getOperationDescription());
            if (FundAccountSubType.isBusinessAccount(generalDTO.getAccountSubType().intValue())) {
                acctCommonOptRespDTO = uAcctBusinessDebitService.transferOut(acctTransferCommonReqDTO);
            } else if (FundAccountSubType.isIndividualAccount(generalDTO.getAccountSubType().intValue())) {
                acctCommonOptRespDTO = uAcctIndividualDebitService.transferOut(acctTransferCommonReqDTO);
            }
            //加款
            uAccountGeneralService.transferInto(operationRPCDTO);
            return AccountSubOperationRespRPCDTO.of(Objects.isNull(acctCommonOptRespDTO) ? "" : acctCommonOptRespDTO.getAccountId());
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】subToGeneral 参数：{}", generalDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 消费
     * Deprecated from 4.0
     *
     * @param accountConsumeDTO
     * @return
     */
    @Deprecated
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO consume(AccountSubOperationReqRPCDTO accountConsumeDTO) throws FinhubException {
        FinhubLogger.info("consume:{}",JsonUtils.toJson(accountConsumeDTO));
        dingDingMsgService.sendMsg("Deprecated interface consume called ");
        //已无引用，清理所有实现
        return new AccountSubOperationRespRPCDTO();
    }

    /**
     * 消费退款
     * Deprecated from 4.0
     *
     * @param accountRefundDTO
     * @return
     */
    @Deprecated
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO refund(AccountSubOperationReqRPCDTO accountRefundDTO) throws FinAccountNoEnoughException {
        FinhubLogger.info("refund:{}",JsonUtils.toJson(accountRefundDTO));
        dingDingMsgService.sendMsg("Deprecated interface refund called");
        //已无引用，清理所有实现
        return new AccountSubOperationRespRPCDTO();
    }

    /**
     * 冻结
     *
     * @param accountFrozenDTO
     * @return
     */
    @Deprecated
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO frozen(AccountSubFreezenReqRPCDTO accountFrozenDTO) throws FinAccountNoEnoughException {
        try {
            FinhubLogger.info("冻结参数:{}", JsonUtils.toJson(accountFrozenDTO));
            ValidateUtils.validate(accountFrozenDTO);
            //减扣账
            AccountSubOperationRespRPCDTO respRPCDTO = uAccountSubService.frozen(accountFrozenDTO);
            //加入冻结池
            FreezenOperationReqDTO freezeBudgetDTO = new FreezenOperationReqDTO();
            BeanUtils.copyProperties(accountFrozenDTO, freezeBudgetDTO);
            freezeBudgetDTO.setOperationAmount(accountFrozenDTO.getOperationAmount());
            freezeBudgetDTO.setAccountId(respRPCDTO.getAccountSubId());
            freezeBudgetDTO.setAccountModel(respRPCDTO.getAccountModel());
            freezeBudgetDTO.setAccountSubType(respRPCDTO.getAccountSubType());
            freezeBudgetDTO.setVerifyNo(accountFrozenDTO.getBizNo());
            uFundFreezenService.freezeBudget(freezeBudgetDTO);
            return respRPCDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】frozen 参数：{}", accountFrozenDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    /**
     * 解冻 分贝券解冻原路返回
     *
     * @param accountUnfreezeDTO
     * @return
     */
    @Deprecated
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO unfreeze(AccountSubUnfreezeReqRPCDTO accountUnfreezeDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountUnfreezeDTO);
            //增加账户
            AccountSubOperationRespRPCDTO operationRespRPCDTO = uAccountSubService.unfreeze(accountUnfreezeDTO);
            //减扣冻结池
            FreezenOperationReqDTO freezeBudgetDTO = new FreezenOperationReqDTO();
            BeanUtils.copyProperties(accountUnfreezeDTO, freezeBudgetDTO);
            freezeBudgetDTO.setAccountId(operationRespRPCDTO.getAccountSubId());
            freezeBudgetDTO.setAccountModel(operationRespRPCDTO.getAccountModel());
            uFundFreezenService.unFreezeBudget(freezeBudgetDTO);
            return operationRespRPCDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】unfreeze 参数：{}账户余额不足", accountUnfreezeDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】unfreeze 参数：{}", JsonUtils.toJson(accountUnfreezeDTO), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】unfreeze 参数：{}", JsonUtils.toJson(accountUnfreezeDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】unfreeze 参数：{}", accountUnfreezeDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    /**
     * 解冻并消费，如：酒店因公超标，因公支付部分先冻结，再解冻并消费
     *
     * @param accountUnfreezeDTO
     * @return
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO unfreezeAndConsume(AccountSubUnfreezeReqRPCDTO accountUnfreezeDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountUnfreezeDTO);
            //先解冻
            unfreeze(accountUnfreezeDTO);
            //再消费
            AccountSubOperationReqRPCDTO operationReqRPCDTO = new AccountSubOperationReqRPCDTO();
            BeanUtils.copyProperties(accountUnfreezeDTO, operationReqRPCDTO);
            operationReqRPCDTO.setOperationAmount(accountUnfreezeDTO.getConsumeAmount());
            AccountSubOperationRespRPCDTO operationRespRPCDTO = uAccountSubService.consume(operationReqRPCDTO);
            return operationRespRPCDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】unfreezeAndConsume 参数：{}", accountUnfreezeDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }


    /**
     * 消费冻结池：如:使用分贝券支付，直接消费冻结金额
     *
     * @param consumeFreezeDTO
     * @return
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO consumeFreezen(AccountConsumeFreezeReqRPCDTO consumeFreezeDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(consumeFreezeDTO);
            FreezenOperationReqDTO freezeDTO = new FreezenOperationReqDTO();
            BeanUtils.copyProperties(consumeFreezeDTO, freezeDTO);
            freezeDTO.setOperationAmount(consumeFreezeDTO.getOperationAmount());
            freezeDTO.setAccountId(consumeFreezeDTO.getAccountSubId());
            uFundFreezenService.payFreezeBudget(freezeDTO);
            return new AccountSubOperationRespRPCDTO();
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】consumeFreezen 参数：{}", consumeFreezeDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    /**
     * 退款冻结池【不对二方系统开放,目前只有分贝券消费使用】
     * 使用场景：
     * 如:使用分贝券退款，直接退款为冻结金额
     *
     * @param refundFreezeDTO
     * @return
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public AccountSubOperationRespRPCDTO refundFreezen(AccountRefundFreezeReqRPCDTO refundFreezeDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(refundFreezeDTO);
            FreezenOperationReqDTO freezeDTO = new FreezenOperationReqDTO();
            BeanUtils.copyProperties(refundFreezeDTO, freezeDTO);
            freezeDTO.setOperationAmount(refundFreezeDTO.getOperationAmount());
            uFundFreezenService.refundFreezeBudget(freezeDTO);
            return new AccountSubOperationRespRPCDTO();
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】refundFreezen 参数：{}", refundFreezeDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    //=========================2.2.1版本================

    /**
     * @Description: 获取公司信息总览
     * @Param: [companyId]
     * @return: com.fenbeitong.fenbeipay.api.model.dto.na.resp.CompanyCreditInfoDTO
     * @Author: wh
     * @Date: 2019/3/28 12:30 PM
     */
    @Override
    public CompanyCreditInfoDTO getCreditOverView(String companyId) throws FinAccountNoEnoughException {
        try {
            AccountSubFindReqRPCDTO accountSubFindReqRPCDTO = new AccountSubFindReqRPCDTO();
            accountSubFindReqRPCDTO.setFindVoucher(true);
            accountSubFindReqRPCDTO.setCompanyId(companyId);
            AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
            accountGeneralBankRPCDTO.setBankNo(companyId);
            accountGeneralBankRPCDTO.setCompanyId(companyId);
            accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
//            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyId(companyId);
//            accountSubFindReqRPCDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//            AccountSub accountSubBu = uAccountSubService.findActByCompanyIdAndAccountSubType(companyId, FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            AcctComGwBySubAndBankReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwBySubAndBankReqDTO();
            acctComGwBySubAndBankReqDTO.setBankAccountNo(companyId);
            acctComGwBySubAndBankReqDTO.setFundAccountType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            acctComGwBySubAndBankReqDTO.setCompanyId(companyId);
            acctComGwBySubAndBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
            List<AcctCommonBaseDTO> acctBusiness = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
            if (CollectionUtils.isEmpty(acctBusiness)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
            }
            AcctCommonBaseDTO accountSubBu = acctBusiness.get(0);
//            accountSubFindReqRPCDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
//            AccountSub accountSubIn = uAccountSubService.findActByCompanyIdAndAccountSubType(companyId, FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            acctComGwBySubAndBankReqDTO.setFundAccountType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            List<AcctCommonBaseDTO> acctIndividuals = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
            if (CollectionUtils.isEmpty(acctIndividuals)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
            }
            AcctCommonBaseDTO accountSubIn = acctIndividuals.get(0);

            BigDecimal availableVoucherBalance = iVouchersPersonService.queryAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(companyId, accountSubIn.getAccountSubType(), accountSubIn.getAccountModel());
            CompanyCreditInfoDTO companyCreditInfoDTO = new CompanyCreditInfoDTO(accountSubBu.getInitCredit(), accountSubBu.getBalance(), availableVoucherBalance, accountSubIn.getBalance(), accountSubIn.getInitCredit(),
                    CompanyCooperatingModel.getByKey(accountGeneral.getCompanyModel()),
                    FundAccountModelType.getEnum(accountSubBu.getAccountModel()), FundAccountModelType.getEnum(accountSubIn.getAccountModel()));
            return companyCreditInfoDTO;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】getCreditOverView 参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @Description: 根据授信额度，无用
     * @methodName: findByInitCredit
     * @Param: [initCredit]
     * @return: java.util.List<com.fenbeitong.fenbeipay.api.model.vo.na.AccountSubVO>
     * @Author: zhangga
     * @Date: 2019/3/29 7:19 PM
     **/
//    @Override
//    public List<AccountSubVO> findByInitCredit(BigDecimal initCredit) throws FinAccountNoEnoughException {
//        try {
//            if (ObjUtils.isEmpty(initCredit)) {
//                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
//            }
//            List<AccountSub> byInitCredit = uAccountSubService.findByInitCredit(initCredit);
//            List<AccountSubVO> voList = new ArrayList<>();
//            byInitCredit.forEach(accountSub -> {
//                AccountSubVO vo = new AccountSubVO();
//                BeanUtils.copyProperties(byInitCredit, vo);
//                voList.add(vo);
//            });
//            return voList;
//        } catch (FinPayException payEx) {
//            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
//        } catch (ValidateException e) {
//            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
//        } catch (Exception e) {
//            FinhubLogger.error("【新账户系统异常】findByInitCredit 参数：{},参数：{}", initCredit, e);
//            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
//        }
//    }


    /**
     * @Description: 获取员工商务账户消费额度查询（companyId 和 employeeId）
     * @Param: [companyId, employeeId]
     * @return: java.math.BigDecimal
     * @Author: wh
     * @Date: 2019/3/30 11:33 AM
     */
    @Override
    public BigDecimal queryTotalConsumeAmount(String companyId, String employeeId) throws FinAccountNoEnoughException {
        try {
            if (StringUtils.isBlank(companyId) || StringUtils.isBlank(employeeId)) {
                throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
            }
            AcctComGwBySubAndBankReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwBySubAndBankReqDTO();
            acctComGwBySubAndBankReqDTO.setBankAccountNo(companyId);
            acctComGwBySubAndBankReqDTO.setFundAccountType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            acctComGwBySubAndBankReqDTO.setCompanyId(companyId);
            acctComGwBySubAndBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
            List<AcctCommonBaseDTO> acctBusiness = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
            if (CollectionUtils.isEmpty(acctBusiness)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
            }
            AcctCommonBaseDTO accountSub = acctBusiness.get(0);
//            AccountSub accountSub = uAccountSubService.findActByCompanyIdAndAccountSubType(companyId, FundAccountSubType.BUSINESS_ACCOUNT.getKey());
//            if (ObjUtils.isNull(accountSub)) {
//                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
//            }
            if (FundAccountModelType.isRecharge(accountSub.getAccountModel())) {
                return uAcctBusinessDebitFlowService.queryTotalConsumeAmount(accountSub.getAccountId(), employeeId);
            } else if (FundAccountModelType.isCredit(accountSub.getAccountModel())) {
                return uAcctBusinessCreditFlowService.queryTotalConsumeAmount(accountSub.getAccountId(), employeeId);
            } else {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
            }
//            return uAccountSubService.queryTotalConsumeAmount(accountSub.getAccountSubId(), employeeId);
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】queryTotalConsumeAmount 参数：{},参数：{}", companyId, employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @param accountSubFlowFindReqRPCDTO
     * @return
     * @throws FinAccountNoEnoughException
     */
    @Override
    @Deprecated
    public List<AccountSubFlowRespRPCDTO> queryAccountSubFlowList(AccountSubFlowFindReqRPCDTO accountSubFlowFindReqRPCDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountSubFlowFindReqRPCDTO);
            List<AccountSubFlowRespRPCDTO> dataList = new ArrayList<>();
            if (FundAccountSubType.isBusinessAccount(accountSubFlowFindReqRPCDTO.getAccountSubType())) {
                List<AccountFlowVO> businessCreditFlows = uAcctBusinessCreditFlowService.queryAccountSubFlowList(accountSubFlowFindReqRPCDTO);
                if (ObjUtils.isNotEmpty(businessCreditFlows)) {
                    businessCreditFlows.forEach(accountSubFlow -> {
                        AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                        BeanUtils.copyProperties(accountSubFlow, dto);
                        dataList.add(dto);
                    });
                }
                List<AccountFlowVO> businessDebitFlows = uAcctBusinessDebitFlowService.queryAccountSubFlowList(accountSubFlowFindReqRPCDTO);
                if (ObjUtils.isNotEmpty(businessDebitFlows)) {
                    businessDebitFlows.forEach(accountSubFlow -> {
                        AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                        BeanUtils.copyProperties(accountSubFlow, dto);
                        dataList.add(dto);
                    });
                }
            } else if (FundAccountSubType.isIndividualAccount(accountSubFlowFindReqRPCDTO.getAccountSubType())) {
                List<AccountFlowVO> individualCreditFlows = uAcctIndividualCreditFlowService.queryAccountSubFlowList(accountSubFlowFindReqRPCDTO);
                if (ObjUtils.isNotEmpty(individualCreditFlows)) {
                    individualCreditFlows.forEach(accountSubFlow -> {
                        AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                        BeanUtils.copyProperties(accountSubFlow, dto);
                        dataList.add(dto);
                    });
                }
                List<AccountFlowVO> individualDebitFlows = uAcctIndividualDebitFlowService.queryAccountSubFlowList(accountSubFlowFindReqRPCDTO);
                if (ObjUtils.isNotEmpty(individualDebitFlows)) {
                    individualDebitFlows.forEach(accountSubFlow -> {
                        AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                        BeanUtils.copyProperties(accountSubFlow, dto);
                        dataList.add(dto);
                    });
                }
            } else {
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType());
            }
//            List<AccountSubFlow> accountSubFlows = uAccountSubService.queryAccountSubFlowList(accountSubFlowFindReqRPCDTO)};
//            List<AccountSubFlowRespRPCDTO> dataList = new ArrayList<>();
//            if (ObjUtils.isNotEmpty(accountSubFlows)) {
//                accountSubFlows.forEach(accountSubFlow -> {
//                    AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
//                    BeanUtils.copyProperties(accountSubFlow, dto);
//                    dataList.add(dto);
//                });
//            }
            return dataList;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】queryAbsSumAccountSubFlow 参数：{}", accountSubFlowFindReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public BigDecimal queryAbsSumAccountSubFlow(AccountSubFlowFindReqRPCDTO accountSubFlowFindReqRPCDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountSubFlowFindReqRPCDTO);
            accountSubFlowFindReqRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            accountSubFlowFindReqRPCDTO.setBankNo(accountSubFlowFindReqRPCDTO.getCompanyId());
            if (FundAccountSubType.isBusinessAccount(accountSubFlowFindReqRPCDTO.getAccountSubType())) {
                BigDecimal businessCredit = uAcctBusinessCreditFlowService.queryAbsSumAccountSubFlow(accountSubFlowFindReqRPCDTO);
                BigDecimal businessDebit = uAcctBusinessDebitFlowService.queryAbsSumAccountSubFlow(accountSubFlowFindReqRPCDTO);
                businessCredit = Objects.isNull(businessCredit) ? BigDecimal.ZERO : businessCredit;
                businessDebit = Objects.isNull(businessDebit) ? BigDecimal.ZERO : businessDebit;
                return businessCredit.add(businessDebit);
            } else if (FundAccountSubType.isIndividualAccount(accountSubFlowFindReqRPCDTO.getAccountSubType())) {
                BigDecimal individualCredit = uAcctIndividualCreditFlowService.queryAbsSumAccountSubFlow(accountSubFlowFindReqRPCDTO);
                BigDecimal individualDebit = uAcctIndividualDebitFlowService.queryAbsSumAccountSubFlow(accountSubFlowFindReqRPCDTO);
                individualCredit = Objects.isNull(individualCredit) ? BigDecimal.ZERO : individualCredit;
                individualDebit = Objects.isNull(individualDebit) ? BigDecimal.ZERO : individualDebit;
                return individualCredit.add(individualDebit);
            } else {
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType());
            }
//            return uAccountSubService.queryAbsSumAccountSubFlow(accountSubFlowFindReqRPCDTO);
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】queryAbsSumAccountSubFlow 参数：{}", accountSubFlowFindReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    @Override
    @Deprecated
    public BigDecimal querySumAccountSubFlow(AccountSubFlowFindReqRPCDTO accountSubFlowFindReqRPCDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountSubFlowFindReqRPCDTO);
            accountSubFlowFindReqRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            accountSubFlowFindReqRPCDTO.setBankNo(accountSubFlowFindReqRPCDTO.getCompanyId());
            if (FundAccountSubType.isBusinessAccount(accountSubFlowFindReqRPCDTO.getAccountSubType())) {
                BigDecimal businessCredit = uAcctBusinessCreditFlowService.querySumAccountSubFlow(accountSubFlowFindReqRPCDTO);
                BigDecimal businessDebit = uAcctBusinessDebitFlowService.querySumAccountSubFlow(accountSubFlowFindReqRPCDTO);
                businessCredit = Objects.isNull(businessCredit) ? BigDecimal.ZERO : businessCredit;
                businessDebit = Objects.isNull(businessDebit) ? BigDecimal.ZERO : businessDebit;
                return businessCredit.add(businessDebit);
            } else if (FundAccountSubType.isIndividualAccount(accountSubFlowFindReqRPCDTO.getAccountSubType())) {
                BigDecimal individualCredit = uAcctIndividualCreditFlowService.querySumAccountSubFlow(accountSubFlowFindReqRPCDTO);
                BigDecimal individualDebit = uAcctIndividualDebitFlowService.querySumAccountSubFlow(accountSubFlowFindReqRPCDTO);
                individualCredit = Objects.isNull(individualCredit) ? BigDecimal.ZERO : individualCredit;
                individualDebit = Objects.isNull(individualDebit) ? BigDecimal.ZERO : individualDebit;
                return individualCredit.add(individualDebit);
            } else {
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType());
            }
//            return uAccountSubService.querySumAccountSubFlow(accountSubFlowFindReqRPCDTO);
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】querySumAccountSubFlow 参数：{}", accountSubFlowFindReqRPCDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】querySumAccountSubFlow 参数：{}", accountSubFlowFindReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】querySumAccountSubFlow 参数：{}", accountSubFlowFindReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<AccountSubFlowFindVO> querySumCompaniesAccountSubFlow(AccountSubFlowsFindReqRPCDTO accountSubFlowsFindReqRPCDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountSubFlowsFindReqRPCDTO);
            return uAccountSubService.querySumCompaniesAccountSubFlow(accountSubFlowsFindReqRPCDTO);
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】querySumCompaniesAccountSubFlow 参数：{}", accountSubFlowsFindReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 获取子账户以及红包券余额
     * 如果categoryType为null,表示红包券不受场景限制（火车票不让用红包券）
     * 如果operationChannel为null,表示红包券不受操作渠道限制（Stereo不让用红包券）
     *
     * @param accountSubFindDTO
     * @param categoryType
     * @return
     * @since 3.8.1
     */
    @Override
    public AccountSubFindRespRPCDTO queryAccountSubInfoOrRedcoupon(AccountSubFindReqRPCDTO accountSubFindDTO, Integer categoryType, Integer operationChannel) {
        AccountSubFindRespRPCDTO accountSubFindRespRPCDTO = queryAccountSubInfo(accountSubFindDTO);
        try {
            AccountRedcoupon accountRedcoupon = accountRedcouponService.selectByCompanyIdAndType(accountSubFindDTO.getCompanyId(), RedcouponType.REDCOUPON.getKey());
            if (null != accountRedcoupon && CashierPayRedcouponFilterVo.checkRedcouponUse(categoryType, operationChannel)) {
                accountSubFindRespRPCDTO.setRedcouponBalance(accountRedcoupon.getBalance());
            }
        } catch (FinPayException e) {
            FinhubLogger.info("【新账户系统】querySubAccountBalanceOrRedcoupon红包券不存在 参数{}", accountSubFindDTO.toString(), e);
        }

        return accountSubFindRespRPCDTO;
    }


    @Override
    public FundFreezenRespRPCDTO queryFundFreezenInfoByCompanyIdAndUseType(String companyId, FundAccountSubType accountSubType, FreezenUseType freezenUseType, Integer accountModel) {
        List<FundFreezen> fundFreezens = uFundFreezenService.queryByCompanyIdAndUseType(companyId, accountSubType.getKey(), accountModel, freezenUseType.getKey());
        FundFreezenRespRPCDTO respRPCDTO = new FundFreezenRespRPCDTO();
        if (ObjUtils.isNotEmpty(fundFreezens)) {
            BeanUtils.copyProperties(fundFreezens.get(0), respRPCDTO);
        }
        return respRPCDTO;
    }

    /**
     * * 预切换-检查是否有虚拟卡未退还额度
     *
     * @param companyId
     * @param accountSubType
     * @param accountModel
     * @return
     */
    @Override
    public BigDecimal queryBankFundFreezenInfoByCompanyId(String companyId, Integer accountSubType, Integer accountModel) {
        List<Integer> freezenUseTypes = Lists.newArrayList();
        freezenUseTypes.add(FreezenUseType.BANK_ACCOUNT.getKey());
        freezenUseTypes.add(FreezenUseType.BANK_CG_ACCOUNT.getKey());
        if (ObjUtils.isEmpty(accountModel)) {
//            AccountSub accountSubAct = uAccountSubService.findActByCompanyIdAndAccountSubType(companyId, accountSubType);
            AcctComGwBySubAndBankReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwBySubAndBankReqDTO();
            acctComGwBySubAndBankReqDTO.setBankAccountNo(companyId);
            acctComGwBySubAndBankReqDTO.setFundAccountType(accountSubType);
            acctComGwBySubAndBankReqDTO.setCompanyId(companyId);
            acctComGwBySubAndBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
            List<AcctCommonBaseDTO> accountSubs = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
            if (CollectionUtils.isEmpty(accountSubs)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
            }
            accountModel = accountSubs.get(0).getAccountModel();
        }
        List<FundFreezen> fundFreezens = uFundFreezenService.queryByCompanyIdAndUseTypes(companyId, accountSubType, Arrays.asList(accountModel), freezenUseTypes);
        BigDecimal amountAll = BigDecimal.ZERO;
        if (ObjUtils.isNotEmpty(fundFreezens)) {
            amountAll = fundFreezens.stream().filter(e->BankNameEnum.FBT.getCode().equals(e.getBankName())).map(FundFreezen::getFreezeBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return amountAll;
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public boolean disableAccount(AccountSubReqRPCDTO accountSubReqRPCDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountSubReqRPCDTO);
            if (FundAccountSubType.isBusinessAccount(accountSubReqRPCDTO.getAccountSubType().intValue())) {
                //商务充值
                boolean businessDebit = true;
                AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(accountSubReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubReqRPCDTO.getCompanyId());
                if (Objects.nonNull(acctBusinessDebit)) {
                    accountSubReqRPCDTO.setAccountSubId(acctBusinessDebit.getAccountId());
                    businessDebit = uAcctBusinessDebitService.disableAccountById(accountSubReqRPCDTO.getAccountSubId());
                }
                //商务授信
                boolean businessCredit = true;
                AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(accountSubReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubReqRPCDTO.getCompanyId());
                if (Objects.nonNull(acctBusinessCredit)) {
                    accountSubReqRPCDTO.setAccountSubId(acctBusinessCredit.getAccountId());
                    businessCredit = uAcctBusinessCreditService.disableAccountById(accountSubReqRPCDTO.getAccountSubId());
                }
                return businessDebit && businessCredit;
            } else if (FundAccountSubType.isIndividualAccount(accountSubReqRPCDTO.getAccountSubType().intValue())) {
                //个人充值
                boolean individualDebit = true;
                AcctIndividualDebit companyIdAndBank = uAcctIndividualDebitService.findCompanyIdAndBank(accountSubReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubReqRPCDTO.getCompanyId());
                if (Objects.nonNull(companyIdAndBank)) {
                    accountSubReqRPCDTO.setAccountSubId(companyIdAndBank.getAccountId());
                    individualDebit = uAcctIndividualDebitService.disableAccountById(accountSubReqRPCDTO.getAccountSubId());
                }
                //个人授信
                boolean individualCredit = true;
                AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(accountSubReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubReqRPCDTO.getCompanyId());
                if (Objects.nonNull(acctIndividualCredit)) {
                    accountSubReqRPCDTO.setAccountSubId(acctIndividualCredit.getAccountId());
                    individualCredit = uAcctIndividualCreditService.disableAccountById(accountSubReqRPCDTO.getAccountSubId());
                }
                return individualDebit && individualCredit;
            } else {
                return false;
            }
//            return uAccountSubService.disableAccount(accountSubReqRPCDTO);
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】disableAccount 参数：{}", accountSubReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public boolean disableAccountSubs(AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountSubAbleReqRPCDTO);
            AccountSubReqRPCDTO accountSubReqRPCDTO = new AccountSubReqRPCDTO();
            BeanUtils.copyProperties(accountSubAbleReqRPCDTO, accountSubReqRPCDTO);
            if (ObjUtils.isEmpty(accountSubAbleReqRPCDTO.getAccountSubType())) {
                boolean businessCreditDisResult = businessCreditDisResult(accountSubReqRPCDTO);
                boolean businessDebitDisResult = businessDebitDisResult(accountSubReqRPCDTO);
                boolean invidualCreditDisResult = invidualCreditDisResult(accountSubReqRPCDTO);
                boolean invidualDebitDisResult = invidualDebitDisResult(accountSubReqRPCDTO);
                if (businessCreditDisResult && businessDebitDisResult && invidualCreditDisResult && invidualDebitDisResult) {
                    return true;
                } else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_UPDATE_ERROR);
                }
//                return uAccountSubService.disableAccountSubsAll(accountSubAbleReqRPCDTO.getCompanyId());
            } else {
                boolean resultCredit = true;
                boolean resultDebit = true;
                if (FundAccountSubType.isBusinessAccount(accountSubAbleReqRPCDTO.getAccountSubType())) {
                    resultCredit = businessCreditDisResult(accountSubReqRPCDTO);
                    resultDebit = businessDebitDisResult(accountSubReqRPCDTO);
                } else if (FundAccountSubType.isIndividualAccount(accountSubAbleReqRPCDTO.getAccountSubType())) {
                    resultCredit = invidualCreditDisResult(accountSubReqRPCDTO);
                    resultDebit = invidualDebitDisResult(accountSubReqRPCDTO);
                } else {
                    throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.EXCEPTION.getMsg());
                }
                if (resultCredit && resultDebit) {
                    return true;
                } else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_UPDATE_ERROR);
                }
            }
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】disableAccounts 参数：{}", accountSubAbleReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    private boolean invidualDebitDisResult(AccountSubReqRPCDTO accountSubReqRPCDTO) {
        boolean resultDebit = true;
        AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(accountSubReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubReqRPCDTO.getCompanyId());
        if (Objects.nonNull(acctIndividualDebit)) {
            accountSubReqRPCDTO.setAccountSubId(acctIndividualDebit.getAccountId());
            resultDebit = uAcctIndividualDebitService.disableAccountById(accountSubReqRPCDTO.getAccountSubId());
        }
        return resultDebit;
    }

    private boolean invidualCreditDisResult(AccountSubReqRPCDTO accountSubReqRPCDTO) {
        boolean resultCredit = true;
        AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(accountSubReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubReqRPCDTO.getCompanyId());
        if (Objects.nonNull(acctIndividualCredit)) {
            accountSubReqRPCDTO.setAccountSubId(acctIndividualCredit.getAccountId());
            resultCredit = uAcctIndividualCreditService.disableAccountById(accountSubReqRPCDTO.getAccountSubId());
        }
        return resultCredit;
    }

    private boolean businessDebitDisResult(AccountSubReqRPCDTO accountSubReqRPCDTO) {
        boolean resultDebit = true;
        AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(accountSubReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubReqRPCDTO.getCompanyId());
        if (Objects.nonNull(acctBusinessDebit)) {
            accountSubReqRPCDTO.setAccountSubId(acctBusinessDebit.getAccountId());
            resultDebit = uAcctBusinessDebitService.disableAccountById(accountSubReqRPCDTO.getAccountSubId());
        }
        return resultDebit;
    }

    private boolean businessCreditDisResult(AccountSubReqRPCDTO accountSubReqRPCDTO) {
        boolean resultCredit = true;
        AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(accountSubReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubReqRPCDTO.getCompanyId());
        if (Objects.nonNull(acctBusinessCredit)) {
            accountSubReqRPCDTO.setAccountSubId(acctBusinessCredit.getAccountId());
            resultCredit = uAcctBusinessCreditService.disableAccountById(accountSubReqRPCDTO.getAccountSubId());
        }
        return resultCredit;
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public boolean enableAccountSubs(AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(accountSubAbleReqRPCDTO);
            if (ObjUtils.isEmpty(accountSubAbleReqRPCDTO.getAccountSubType())) {
                boolean businessCreditResult = businessCreditResult(accountSubAbleReqRPCDTO);
                boolean businessDebitResult = businessDebitResult(accountSubAbleReqRPCDTO);
                boolean individualCreditResult = individualCreditResult(accountSubAbleReqRPCDTO);
                boolean individualDebitResult = individualDebitResult(accountSubAbleReqRPCDTO);
                if (individualDebitResult && individualCreditResult && businessDebitResult && businessCreditResult) {
                    return true;
                } else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_UPDATE_ERROR);
                }
//                return uAccountSubService.enableAccountSubsAll(accountSubAbleReqRPCDTO.getCompanyId());
            } else {
//                AccountSubReqRPCDTO accountSubReqRPCDTO = new AccountSubReqRPCDTO();
//                BeanUtils.copyProperties(accountSubAbleReqRPCDTO, accountSubReqRPCDTO);
//                return uAccountSubService.enableAccount(accountSubReqRPCDTO);
                boolean resultCredit = true;
                boolean resultDebit = true;
                if (FundAccountSubType.isBusinessAccount(accountSubAbleReqRPCDTO.getAccountSubType())) {
                    resultCredit = businessCreditResult(accountSubAbleReqRPCDTO);
                    resultDebit = businessDebitResult(accountSubAbleReqRPCDTO);
                } else if (FundAccountSubType.isIndividualAccount(accountSubAbleReqRPCDTO.getAccountSubType())) {
                    resultCredit = individualCreditResult(accountSubAbleReqRPCDTO);
                    resultDebit = individualDebitResult(accountSubAbleReqRPCDTO);
                } else {
                    throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.EXCEPTION.getMsg());
                }
                return resultCredit && resultDebit;
            }
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】enableAccounts 参数：{}", accountSubAbleReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    private boolean individualDebitResult(AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO) {
        boolean resultDebit = true;
        AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(accountSubAbleReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubAbleReqRPCDTO.getCompanyId());
        if (Objects.nonNull(acctIndividualDebit)) {
            accountSubAbleReqRPCDTO.setAccountSubId(acctIndividualDebit.getAccountId());
            resultDebit = uAcctIndividualDebitService.enableAccountById(accountSubAbleReqRPCDTO.getAccountSubId());
        }
        return resultDebit;
    }

    private boolean individualCreditResult(AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO) {
        boolean resultCredit = true;
        AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(accountSubAbleReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubAbleReqRPCDTO.getCompanyId());
        if (Objects.nonNull(acctIndividualCredit)) {
            accountSubAbleReqRPCDTO.setAccountSubId(acctIndividualCredit.getAccountId());
            resultCredit = uAcctIndividualCreditService.enableAccountById(accountSubAbleReqRPCDTO.getAccountSubId());
        }
        return resultCredit;
    }

    private boolean businessDebitResult(AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO) {
        boolean resultDebit = true;
        AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(accountSubAbleReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubAbleReqRPCDTO.getCompanyId());
        if (Objects.nonNull(acctBusinessDebit)) {
            accountSubAbleReqRPCDTO.setAccountSubId(acctBusinessDebit.getAccountId());
            resultDebit = uAcctBusinessDebitService.enableAccountById(accountSubAbleReqRPCDTO.getAccountSubId());
        }
        return resultDebit;
    }

    private boolean businessCreditResult(AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO) {
        boolean resultCredit = true;
        AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(accountSubAbleReqRPCDTO.getCompanyId(), BankNameEnum.FBT.getCode(), accountSubAbleReqRPCDTO.getCompanyId());
        if (Objects.nonNull(acctBusinessCredit)) {
            accountSubAbleReqRPCDTO.setAccountSubId(acctBusinessCredit.getAccountId());
            resultCredit = uAcctBusinessCreditService.enableAccountById(accountSubAbleReqRPCDTO.getAccountSubId());
        }
        return resultCredit;
    }


    /**
     * 预切换-检查是否有虚拟卡未退还额度
     *
     * @param accountSubAbleReqRPCDTO
     * @return
     */
    @Deprecated
    @Override
    public boolean preActivityAccountSub(AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO) {
        try {
            ValidateUtils.validate(accountSubAbleReqRPCDTO);
            List<String> balanceEmpIds = searchCardManager.findBalanceEmployeeByCompanyId(accountSubAbleReqRPCDTO.getCompanyId());
            if (ObjUtils.isNotEmpty(balanceEmpIds)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_ACT_BANK_ERROR);
            }
            return true;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】preActivityAccountSub 参数：{}", accountSubAbleReqRPCDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】preActivityAccountSub 参数：{}", accountSubAbleReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】preActivityAccountSub 参数：{}", accountSubAbleReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 切换
     * fixme renfj 网关
     *
     * @param accountSubAbleReqRPCDTO
     * @return
     * @throws FinAccountNoEnoughException
     */
    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public boolean activityAccountSub(AccountSubAbleReqRPCDTO accountSubAbleReqRPCDTO) throws FinAccountNoEnoughException {
        try {
            //改生效帐户
            uAcctCommonService.changeactAivityAccountSub(accountSubAbleReqRPCDTO);
            return true;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】activityAccountSub 参数：{}", accountSubAbleReqRPCDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】activityAccountSub 参数：{}", accountSubAbleReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】activityAccountSub 参数：{}", accountSubAbleReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean showAccountSub(AccountSubShowReqRPCDTO reqRPCDTO) {
        try {
            ValidateUtils.validate(reqRPCDTO);
            String companyId = reqRPCDTO.getCompanyId();
            boolean update = true;
            reqRPCDTO.setBankAccountNo(companyId);
            reqRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            if (FundAccountSubType.isBusinessAccount(reqRPCDTO.getAccountSubType())
                    && FundAccountModelType.isRecharge(reqRPCDTO.getAccountModel())) {
                AcctBusinessDebit acct = uAcctBusinessDebitService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                reqRPCDTO.setAccountId(acct.getAccountId());
                update = uAcctBusinessDebitService.showAccount(reqRPCDTO);
            } else if (FundAccountSubType.isBusinessAccount(reqRPCDTO.getAccountSubType())
                    && FundAccountModelType.isCredit(reqRPCDTO.getAccountModel())) {
                AcctBusinessCredit acct = uAcctBusinessCreditService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                reqRPCDTO.setAccountId(acct.getAccountId());
                update = uAcctBusinessCreditService.showAccount(reqRPCDTO);
            } else if (FundAccountSubType.isIndividualAccount(reqRPCDTO.getAccountSubType())
                    && FundAccountModelType.isRecharge(reqRPCDTO.getAccountModel())) {
                AcctIndividualDebit acct = uAcctIndividualDebitService.findCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                reqRPCDTO.setAccountId(acct.getAccountId());
                update = uAcctIndividualDebitService.showAccount(reqRPCDTO);
            } else if (FundAccountSubType.isIndividualAccount(reqRPCDTO.getAccountSubType())
                    && FundAccountModelType.isCredit(reqRPCDTO.getAccountModel())) {
                AcctIndividualCredit acct = uAcctIndividualCreditService.findByCompanyIdAndBank(companyId,BankNameEnum.FBT.getCode(),companyId);
                reqRPCDTO.setAccountId(acct.getAccountId());
                update = uAcctIndividualCreditService.showAccount(reqRPCDTO);
            } else {
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType());
            }
//            boolean update = uAccountSubService.showAccount(reqRPCDTO);
            return update;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】showAccountSub 参数：{}", reqRPCDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】showAccountSub 参数：{}", reqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】showAccountSub 参数：{}", reqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<FundFreezenRespRPCDTO> queryCompanyVirtualCardAcct(String companyId) {
        List<FundFreezenRespRPCDTO> fundFreezenRespRPCDTOS = new ArrayList<>();
        List<FundFreezen> fundFreezens = uFundFreezenService.queryByCompanyIdAndUseType(companyId, FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey(),null,null);
        if (CollectionUtils.isNotEmpty(fundFreezens)){
            for (FundFreezen f: fundFreezens) {
                FundFreezenRespRPCDTO fundFreezenRespRPCDTO = new FundFreezenRespRPCDTO();
                fundFreezenRespRPCDTO.setAccountId(f.getAccountId());
                fundFreezenRespRPCDTO.setFreezeBalance(f.getFreezeBalance());
                fundFreezenRespRPCDTO.setCompanyId(f.getCompanyId());
                fundFreezenRespRPCDTOS.add(fundFreezenRespRPCDTO);
            }
            return fundFreezenRespRPCDTOS;
        }
        return fundFreezenRespRPCDTOS;
    }

    /**
     * 参数校验，操作金额，操作类型校验
     * @author: zhaoxu
     * @date: 2022-06-06 15:39:33
     */
    private void adjustToSubCheck(AccountSubAdjustToSubReqRPCDTO reqDTO, AcctBusinessCredit businessCredit, AcctIndividualCredit individualCredit) {
        ValidateUtils.validate(reqDTO);
        if (reqDTO.getAccountSubType().equals(reqDTO.getAccountSubTypeTo())) {
            FinhubLogger.error("adjustTempFromSubCheck accountSubType 校验失败,reqDto:{}", JSONObject.toJSONString(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_ADJUST_ACCOUNT_SAME);
        }
        // 商务
        if (ObjUtils.isNull(businessCredit)) {
            FinhubLogger.error("adjustTempFromSubCheck businessCredit 校验失败,reqDto:{}", JSONObject.toJSONString(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
        }
        //个人
        if (ObjUtils.isNull(individualCredit)) {
            FinhubLogger.error("adjustTempFromSubCheck individualCredit 校验失败,reqDto:{}", JSONObject.toJSONString(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
        }
        // 操作金额，操作类型校验
        BigDecimal operationAmount = reqDTO.getOperationAmount();
        if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType())) {
            if (businessCredit.getBalance().compareTo(operationAmount) < 0 || businessCredit.getInitCredit().add(businessCredit.getTempAmount()).compareTo(operationAmount) < 0) {
                FinhubLogger.error("adjustTempFromSubCheck buss 操作金额，操作类型校验失败,reqDto:{},businessCredit:{}", JSONObject.toJSONString(reqDTO), JSONObject.toJSONString(businessCredit));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_ADJUST_TO_SUB_NOT_ENOUGH);
            }
        } else if(FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType())) {
            if (individualCredit.getBalance().compareTo(operationAmount) < 0 || individualCredit.getInitCredit().add(individualCredit.getTempAmount()).compareTo(operationAmount) < 0) {
                FinhubLogger.error("adjustTempFromSubCheck individual 操作金额，操作类型校验失败,reqDto:{},individualCredit:{}", JSONObject.toJSONString(reqDTO), JSONObject.toJSONString(individualCredit));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_ADJUST_TO_SUB_NOT_ENOUGH);
            }
        } else {
            FinhubLogger.error("adjustTempFromSubCheck subType 校验失败,reqDto:{}", JSONObject.toJSONString(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_OPT_CHECK_ERROR);
        }
    }
}
