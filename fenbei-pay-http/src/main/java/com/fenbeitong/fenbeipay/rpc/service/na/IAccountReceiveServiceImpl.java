package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountReceiveListReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountReceiveListRespDTO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountReceiveService;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/17
 * @Description
 */
@Service("iAccountReceiveService")
public class IAccountReceiveServiceImpl implements IAccountReceiveService {

    @Autowired
    private AccountReceiveInfoService accountReceiveInfoService;

    @Override
    public List<AccountReceiveListRespDTO> queryAccountReceiveListByEmployees(AccountReceiveListReqDTO reqDTO) {

        return accountReceiveInfoService.queryAccountReceiveListByEmployees(reqDTO);
    }
}
