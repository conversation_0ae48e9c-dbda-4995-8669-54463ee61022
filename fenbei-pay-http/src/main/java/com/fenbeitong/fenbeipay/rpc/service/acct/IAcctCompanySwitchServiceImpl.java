package com.fenbeitong.fenbeipay.rpc.service.acct;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UCompanySwitchService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCompanySwitchAddReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCompanySwitchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCompanySwitchRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctCompanySwitchService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: IAcctCompanySwitchServiceImpl
 * @ProjectName fenbei-pay
 * @author: wh
 * @date 2021/2/26 13:41
 */
@Service("iAcctCompanySwitchService")
public class IAcctCompanySwitchServiceImpl implements IAcctCompanySwitchService {

    @Autowired
    private UCompanySwitchService uCompanySwitchService;

    @Override
    public void createAcctCompanySwitch(AcctCompanySwitchAddReqDTO reqDTO) {
        try {
            ValidateUtils.validate(reqDTO);
            uCompanySwitchService.createAcctCompanySwitch(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【添加切换的企业账户】createAcctCompanySwitch 参数：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【添加切换的企业账户】createAcctCompanySwitch 参数：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【添加切换的企业账户】createAcctCompanySwitch 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public Boolean isAcctCompanySwitch(AcctCompanySwitchReqDTO reqDTO) {
        try {
            ValidateUtils.validate(reqDTO);
            return uCompanySwitchService.isCompanyAcctModelSwitch(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【是否切换新的账户企业某账户】isCompanyAcctModelSwitch参数：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【是否切换新的账户企业某账户】isCompanyAcctModelSwitch{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【是否切换新的账户企业某账户】isCompanyAcctModelSwitch{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public AcctCompanySwitchRespDTO queryCompanySwitch(AcctCompanySwitchReqDTO reqDTO) {
        try {
            ValidateUtils.validate(reqDTO);
            return uCompanySwitchService.queryCompanySwitch(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【查询切换新的账户企业某账户】queryCompanySwitch 参数：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【查询切换新的账户企业某账户】queryCompanySwitch 参数：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【查询切换新的账户企业某账户】queryCompanySwitch 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public Boolean isCompanySwitch(String companyId) {
        try {
            if (ObjUtils.isBlank(companyId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            return uCompanySwitchService.isCompanySwitch(companyId);
        } catch (FinPayException e) {
            FinhubLogger.error("【是否切换新的账户某企业】isCompanySwitch 参数：{}", companyId);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【是否切换新的账户某企业】isCompanySwitch参数：{}", companyId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【是否切换新的账户某企业】isCompanySwitch 参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public List<AcctCompanySwitchRespDTO> findCompanySwitch(String companyId) {
        try {
            if (ObjUtils.isBlank(companyId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            return uCompanySwitchService.findCompanySwitch(companyId);
        } catch (FinPayException e) {
            FinhubLogger.error("【查询切换新的账户企业某账户】findCompanySwitch 参数：{}", companyId);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【查询切换新的账户企业某账户】findCompanySwitch：{}", companyId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【查询切换新的账户企业某账户】findCompanySwitch 参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public void delAcctCompanySwitch(String companyId) {
        try {
            if (ObjUtils.isBlank(companyId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
             uCompanySwitchService.delAcctCompanySwitch(companyId);
        } catch (FinPayException e) {
            FinhubLogger.error("【删除切换新的账户企业某账户】delAcctCompanySwitch 参数：{}", companyId);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【删除切换新的账户企业某账户】delAcctCompanySwitch：{}", companyId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【删除切换新的账户企业某账户】delAcctCompanySwitch 参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }
}
