package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessCreditService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessDebitService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctIndividualCreditService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctIndividualDebitService;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.*;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.cashier.manager.KafkaCashierPayManager;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 收银台退款
 */
@Service("iCashierOrderRefundSettlementService")
public class ICashierOrderRefundSettlementServiceImpl implements ICashierOrderRefundSettlementService {

    /**
     * 强制解锁时间设置
     */
    private long LOCK_TIME_REFUND = 10000L;

    /**
     * 等待时间
     **/
    private long WAIT_TIME_REFUND = 600L;


    @Autowired
    private CashierOrderRefundSettlementService cashierOrderRefundSettlementService;

    @Autowired
    private KafkaCashierPayManager kafkaCashierPayManager;

    @Autowired
    protected RedissonService redissonService;

    @Autowired
    protected UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    protected UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    protected UAcctIndividualDebitService uAcctIndividualDebitService;

    @Autowired
    protected UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    private DingDingMsgService dingDingMsgService;
    /**
     * 发起退款-单笔退款
     * 【普通流程：因公+因私+预算管控+退款回掉】
     *
     * @param cashierRefundTradeRPCVo
     * @return
     */
    @Override
    public CashierRefundTradeRPCDTO refundTrade(CashierRefundTradeRPCVo cashierRefundTradeRPCVo) {
        FinhubLogger.info("【收银台】【RPC收银台退款】参数：{}", JSON.toJSONString(cashierRefundTradeRPCVo));
        final String lockKey = MessageFormat.format(RedisKeyConstant.CASHIER_REFUND_ORDERID_KEY, cashierRefundTradeRPCVo.getFbOrderId());
        try {
            cashierRefundTradeRPCVo = ValidateUtils.validate(cashierRefundTradeRPCVo);

            CashierRefundTradeReqVo cashierRefundTradeReqVo = new CashierRefundTradeReqVo();
            BeanUtils.copyProperties(cashierRefundTradeRPCVo, cashierRefundTradeReqVo);
            CashierRefundTradeRespVo cashierRefundTradeRespVo = null;
            try {
                //加锁
                boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
                FinhubLogger.info("加锁结果:" + lock + lockKey);
                if (!lock) {
                    throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
                }
                cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTradeAndSaas(cashierRefundTradeReqVo);
                FinhubLogger.info("【收银台】【RPC收银台退款】退款成功：{}", JSON.toJSONString(cashierRefundTradeRespVo));
            } catch (Exception e) {
                String msgError = "【收银台】【RPC收银台退款】异常:场景订单号:" + cashierRefundTradeRPCVo.getFbOrderId() + e.getMessage();
                if (e instanceof FinPayException && Objects.equals(GlobalResponseCode.CASHIER_REFUND_TOTAL_AMOUNT_HAD_REFUND.getCode(), ((FinPayException)e).getCode())) {
                    FinhubLogger.warn(msgError, e);
                } else {
                    dingDingMsgService.sendMsg(msgError);
                    FinhubLogger.error(msgError, e);
                }
                throw e;
            }
            //场景通知
            CashierRefundTradeRespVo finalCashierRefundTradeRespVo = cashierRefundTradeRespVo;
            CompletableFuture.runAsync(() -> {
                cashierOrderRefundSettlementService.checkRefundStatusAndCallBiz(finalCashierRefundTradeRespVo.getRefundTxnId(), cashierRefundTradeReqVo.getEmployeeId());
            });
            CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
            BeanUtils.copyProperties(cashierRefundTradeRespVo, cashierRefundTradeRPCDTO);
            return cashierRefundTradeRPCDTO;
        } catch (InterruptedException e) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
        } catch (Exception e) {
            throw e;
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("释放锁失败：{}", lockKey, e);
            }
        }
    }

    /**
     * 指定各种支付能力最大可退金额
     * @param cashierRefundTradeMaxWayRPCVo
     * @return
     */
    @Override
    public CashierRefundTradeRPCDTO refundMaxTrade(CashierRefundTradeMaxWayRPCVo cashierRefundTradeMaxWayRPCVo) {
        FinhubLogger.info("【收银台】【RPC收银台退款：指定每种支付能力最大可退金额，进行退款】参数：{}", JSON.toJSONString(cashierRefundTradeMaxWayRPCVo));
        ValidateUtils.validate(cashierRefundTradeMaxWayRPCVo);
        CashierRefundTradeMaxWayReqVo cashierRefundTradeRPCVo = new CashierRefundTradeMaxWayReqVo();
        BeanUtils.copyProperties(cashierRefundTradeMaxWayRPCVo, cashierRefundTradeRPCVo);
        cashierRefundTradeRPCVo.setCompanyRefundMaxAmount(cashierRefundTradeMaxWayRPCVo.getCompanyRefundMaxPrice());
        cashierRefundTradeRPCVo.setRedcouponRefundMaxAmount(cashierRefundTradeMaxWayRPCVo.getRedcouponRefundMaxPrice());
        CashierRefundTradeRespVo cashierRefundTradeRespVo = null;
        try {
            cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTradeMaxWayAndSaas(cashierRefundTradeRPCVo);
        } catch (Exception e) {
            String msgError = "【收银台】【RPC收银台退款:指定每种支付能力最大可退金额，进行退款】异常:场景订单号:" + cashierRefundTradeRPCVo.getFbOrderId() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError, e);
            throw e;
        }
        //场景通知
        CashierRefundTradeRespVo finalCashierRefundTradeRespVo = cashierRefundTradeRespVo;
        CompletableFuture.runAsync(() -> {
            cashierOrderRefundSettlementService.checkRefundStatusAndCallBiz(finalCashierRefundTradeRespVo.getRefundTxnId(), cashierRefundTradeMaxWayRPCVo.getEmployeeId());
        });
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(cashierRefundTradeRespVo, cashierRefundTradeRPCDTO);
        return cashierRefundTradeRPCDTO;
    }

    /**
     * @Description: 改签退款
     * @Param: [cashierBatchRefundTradeRPCVo]
     * @return: java.util.List<com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierRefundTradeRPCDTO>
     * @Author: wh
     * @Date: 2019/5/14 7:01 PM
     */
    @Override
    public List<CashierRefundTradeRPCDTO> batchRefundTrade(CashierBatchRefundTradeRPCVo cashierBatchRefundTradeRPCVo) {
        ValidateUtils.validate(cashierBatchRefundTradeRPCVo);
        CashierBatchRefundTradeReqVo cashierBatchRefundTradezReqVo = new CashierBatchRefundTradeReqVo();
        BeanUtils.copyProperties(cashierBatchRefundTradeRPCVo, cashierBatchRefundTradezReqVo);
        cashierBatchRefundTradezReqVo.addRefundList(cashierBatchRefundTradeRPCVo.getRefundList());
        try {
            List<CashierRefundTradeRespVo> batchRefundTradeAndCallBiz = cashierOrderRefundSettlementService.createBatchRefundTradeAndSaas(cashierBatchRefundTradezReqVo);

            List<CashierRefundTradeRPCDTO> cashierRefundTradeRPCDTOList = batchRefundTradeAndCallBiz.stream().map(cashierRefundTradeRespVo1 -> {
                CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
                BeanUtils.copyProperties(cashierRefundTradeRespVo1, cashierRefundTradeRPCDTO);
                return cashierRefundTradeRPCDTO;
            }).collect(Collectors.toList());


            CompletableFuture.runAsync(() -> {
                FinhubLogger.info("退款通知{}", cashierBatchRefundTradeRPCVo.getBatchRefundOrderId());
                cashierOrderRefundSettlementService.checkRefundStatusByRefundOrderIdAndCallBiz(cashierBatchRefundTradeRPCVo.getBatchRefundOrderId());
            });
            return cashierRefundTradeRPCDTOList;
        } catch (Exception e) {
            String msgError = "【收银台】【改签批量退款】异常:场景订单号:" + JsonUtils.toJson(cashierBatchRefundTradeRPCVo) + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError, e);
            throw e;
        }
    }


    /**
     * 发起退款-单笔退款(成都工会)
     * 【特殊流程：因公实时返回退款结果，无员工无因私，有预算管控，有退款回掉】
     *
     * @param cashierRefundTradeNoEmployeeRPCVo
     * @return
     */
    @Override
    public CashierRefundTradeRPCDTO refundTrade4NoEmployee(CashierRefundTradeNoEmployeeRPCVo cashierRefundTradeNoEmployeeRPCVo) {
        CashierRefundTradeNoEmployeeReqVo cashierRefundTradeReqVo = new CashierRefundTradeNoEmployeeReqVo();
        BeanUtils.copyProperties(cashierRefundTradeNoEmployeeRPCVo, cashierRefundTradeReqVo);
        cashierRefundTradeReqVo = ValidateUtils.validate(cashierRefundTradeReqVo);
        CashierRefundTradeRespVo cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTrade4SaasNoEmployee(cashierRefundTradeReqVo);
        //通知
        CompletableFuture.runAsync(() -> {
            cashierOrderRefundSettlementService.checkRefundStatusByRefundTxnIdAndCallBiz(cashierRefundTradeRespVo.getRefundTxnId());
        });
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(cashierRefundTradeRespVo, cashierRefundTradeRPCDTO);
        return cashierRefundTradeRPCDTO;
    }


    /**
     * 发起退款-单笔退款(回填单)
     * 【特殊流程：因公实时返回退款结果，有员工无因私，无预算管控，无退款回掉】
     *
     * @param cashierRefundTradeRPCVo
     * @return
     */
    @Override
    public CashierRefundTradeRPCDTO refundTrade4NoCallBiz(CashierRefundTradeRPCVo cashierRefundTradeRPCVo) {
        FinhubLogger.info("【收银台】【RPC回填单收银台退款】参数：{}", JSON.toJSONString(cashierRefundTradeRPCVo));
        cashierRefundTradeRPCVo = ValidateUtils.validate(cashierRefundTradeRPCVo);
        CashierRefundTradeReqVo cashierRefundTradeReqVo = new CashierRefundTradeReqVo();
        BeanUtils.copyProperties(cashierRefundTradeRPCVo, cashierRefundTradeReqVo);
        CashierRefundTradeRespVo cashierRefundTradeRespVo = null;
        boolean saasBuget = false;
        try {
            cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTradeOrSaasNoPersonal(cashierRefundTradeReqVo, saasBuget);
        } catch (Exception e) {
            String msgError = "【RPC回填单收银台退款】异常:场景订单号:" + cashierRefundTradeRPCVo.getFbOrderId() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError, e);
            throw e;
        }
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(cashierRefundTradeRespVo, cashierRefundTradeRPCDTO);
        return cashierRefundTradeRPCDTO;
    }


    /**
     * 发起退款-单笔退款(回填单)
     * 【特殊流程：因公实时返回退款结果，有员工无因私，有预算管控，无退款回掉】
     *
     * @param cashierRefundTradeRPCVo
     * @return
     * @since V3.2.1
     */
    @Override
    public CashierRefundTradeRPCDTO refundTrade4SassNoCallBiz(CashierRefundTradeRPCVo cashierRefundTradeRPCVo) {
        FinhubLogger.info("【收银台】【RPC回填单收银台退款+预算】参数：{}", JSON.toJSONString(cashierRefundTradeRPCVo));
        cashierRefundTradeRPCVo = ValidateUtils.validate(cashierRefundTradeRPCVo);
        CashierRefundTradeReqVo cashierRefundTradeReqVo = new CashierRefundTradeReqVo();
        BeanUtils.copyProperties(cashierRefundTradeRPCVo, cashierRefundTradeReqVo);
        CashierRefundTradeRespVo cashierRefundTradeRespVo = null;
        boolean saasBuget = true;
        try {
            cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTradeOrSaasNoPersonal(cashierRefundTradeReqVo, saasBuget);
        } catch (Exception e) {
            String msgError = "【收银台】【RPC回填单收银台退款+预算】异常:场景订单号:" + cashierRefundTradeRPCVo.getFbOrderId() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError, e);
            throw e;
        }
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(cashierRefundTradeRespVo, cashierRefundTradeRPCDTO);
        return cashierRefundTradeRPCDTO;
    }

    /**
     * 作废退款交易-单笔(回填单)
     * 【流程：只有因公可以作废】
     *
     * @param cashierXeRefundTradeRPCVo
     * @return
     * @since V3.2.1
     */
    @Override
    public CashierXeRefundTradeRPCDTO xeRefundTrade(CashierXeRefundTradeRPCVo cashierXeRefundTradeRPCVo) {
        FinhubLogger.info("【收银台】【RPC回填单退款交易作废】参数：{}", JSON.toJSONString(cashierXeRefundTradeRPCVo));
        cashierXeRefundTradeRPCVo = ValidateUtils.validate(cashierXeRefundTradeRPCVo);
        CashierXeRefundTradeReqVo cashierXeRefundTradeReqVo = new CashierXeRefundTradeReqVo();
        BeanUtils.copyProperties(cashierXeRefundTradeRPCVo, cashierXeRefundTradeReqVo);
        CashierXeRefundTradeRespVo cashierXeRefundTradeRespVo = null;
        try {
            cashierXeRefundTradeRespVo = cashierOrderRefundSettlementService.xeRefundTradeOrSass(cashierXeRefundTradeReqVo);
        } catch (Exception e) {
            String msgError = "【收银台】【RPC回填单退款交易作废】异常:退款交易号:" + cashierXeRefundTradeRPCVo.getRefundTxnId() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError, e);
            throw e;
        }
        CashierXeRefundTradeRPCDTO cashierXeRefundTradeRPCDTO = new CashierXeRefundTradeRPCDTO();
        BeanUtils.copyProperties(cashierXeRefundTradeRespVo, cashierXeRefundTradeRPCDTO);
        return cashierXeRefundTradeRPCDTO;

    }

    /**
     * 内部系统组-账单查询
     * @param cashierRefundQueryReqRPCVo
     * @return
     */
    @Override
    public List<CashierRefundQueryRPCDTO> getRefundDetail(CashierRefundQueryReqRPCVo cashierRefundQueryReqRPCVo) {
        try{
            CashierRefundQueryReqVo cashierRefundQueryReqVo = new CashierRefundQueryReqVo();
            BeanUtils.copyProperties(cashierRefundQueryReqRPCVo, cashierRefundQueryReqVo);
            List<CashierRefundQueryRespVo> refundSettlements = cashierOrderRefundSettlementService.getRefundDetail(cashierRefundQueryReqVo);
            List<CashierRefundQueryRPCDTO> cashierRefundQueryRPCDTOS = new ArrayList<>();
            if (CollectionUtils.isEmpty(refundSettlements)) {
                return cashierRefundQueryRPCDTOS;
            }
            CashierRefundQueryRPCDTO cashierRefundQueryRPCDTO;
            CashierCostAttributionRPCDTO cashierCostAttributionRPCDTO;
            for (CashierRefundQueryRespVo cashierRefundQueryRespVo : refundSettlements) {
                cashierRefundQueryRPCDTO = new CashierRefundQueryRPCDTO();
                CashierOrderCostAttribution costAttribution = cashierRefundQueryRespVo.getCostAttribution();
                cashierCostAttributionRPCDTO = new CashierCostAttributionRPCDTO();
                BeanUtils.copyProperties(cashierRefundQueryRespVo, cashierRefundQueryRPCDTO);
                if (ObjUtils.isNotEmpty(costAttribution)) {
                    BeanUtils.copyProperties(costAttribution, cashierCostAttributionRPCDTO);
                    cashierRefundQueryRPCDTO.setCashierCostAttributionRPCDTO(cashierCostAttributionRPCDTO);
                }
                if (CollectionUtils.isNotEmpty(cashierRefundQueryRespVo.getAttributions())) {
                    List<CashierCostAttributionRPCDTO> list = Lists.newArrayList();
                    List<CashierOrderCostAttribution> c = cashierRefundQueryRespVo.getAttributions();
                    for (CashierOrderCostAttribution orderCostAttribution : c) {
                        CashierCostAttributionRPCDTO dto = new CashierCostAttributionRPCDTO();
                        BeanUtils.copyProperties(orderCostAttribution, dto);
                        list.add(dto);
                    }
                    cashierRefundQueryRPCDTO.setAttributions(list);
                }
                cashierRefundQueryRPCDTOS.add(cashierRefundQueryRPCDTO);
            }
            return cashierRefundQueryRPCDTOS;
        }catch (FinPayException e){
            throw new FinhubException(GlobalResponseCode.CASHIER_REFUND_SETTLE_NOT_EXIST.getCode(),GlobalResponseCode.CASHIER_REFUND_SETTLE_NOT_EXIST.getType(),GlobalResponseCode.CASHIER_REFUND_SETTLE_NOT_EXIST.getMsg());
        }
    }


    @Override
    public List<CashierRefundBatchQueryRPCDTO> batchQueryByRefundOrderIds(CashierRefundBatchQueryReqRPCVo cashierRefundBatchQueryReqRPCVo) {
        CashierBatchRefundQueryReqVo cashierBatchQueryReqVo = new CashierBatchRefundQueryReqVo();
        cashierBatchQueryReqVo.setRefundOrderIds(cashierRefundBatchQueryReqRPCVo.getRefundOrderIds());
        List<CashierBatchRefundQueryRespVo> refundQueryRespVos = cashierOrderRefundSettlementService.batchQueryByRefundOrderIds(cashierBatchQueryReqVo);
        List<CashierRefundBatchQueryRPCDTO> cashierRefundBatchQueryRPCDTOS = new ArrayList<>();
        CashierRefundBatchQueryRPCDTO cashierRefundBatchQueryRPCDTO;
        for (CashierBatchRefundQueryRespVo batchRefundQueryRespVo : refundQueryRespVos) {
            cashierRefundBatchQueryRPCDTO = new CashierRefundBatchQueryRPCDTO();
            BeanUtils.copyProperties(batchRefundQueryRespVo, cashierRefundBatchQueryRPCDTO);
            cashierRefundBatchQueryRPCDTOS.add(cashierRefundBatchQueryRPCDTO);
        }
        return cashierRefundBatchQueryRPCDTOS;
    }

    @Override
    public List<CashierRefundBatchQueryRPCDTO> batchQueryByFbOrderIds(CashierBatchQueryReqRPCVo cashierBatchQueryReqRPCVo) {
        CashierBatchQueryReqVo cashierBatchQueryReqVo = new CashierBatchQueryReqVo();
        cashierBatchQueryReqVo.setFbOrderIds(cashierBatchQueryReqRPCVo.getFbOrderIds());
        List<CashierBatchRefundQueryRespVo> refundQueryRespVos = cashierOrderRefundSettlementService.batchQuery(cashierBatchQueryReqVo);
        List<CashierRefundBatchQueryRPCDTO> cashierRefundBatchQueryRPCDTOS = new ArrayList<>();
        CashierRefundBatchQueryRPCDTO cashierRefundBatchQueryRPCDTO;
        for (CashierBatchRefundQueryRespVo batchRefundQueryRespVo : refundQueryRespVos) {
            cashierRefundBatchQueryRPCDTO = new CashierRefundBatchQueryRPCDTO();
            BeanUtils.copyProperties(batchRefundQueryRespVo, cashierRefundBatchQueryRPCDTO);
            cashierRefundBatchQueryRPCDTOS.add(cashierRefundBatchQueryRPCDTO);
        }
        return cashierRefundBatchQueryRPCDTOS;
    }

    @Override
    public CashierRefundTradeRPCDTO refundAcctPublicTrade(CashierAcctPublicRefundTradeRPCVo cashierRefundTradeRPCVo) {
        CashierAcctPublicRefundTradeReqVo cashierRefundTradeReqVo = new CashierAcctPublicRefundTradeReqVo();
        BeanUtils.copyProperties(cashierRefundTradeRPCVo, cashierRefundTradeReqVo);
        CashierRefundTradeRespVo refundBankTradeOrSass = cashierOrderRefundSettlementService.refundAcctPublicTrade(cashierRefundTradeReqVo);
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(refundBankTradeOrSass, cashierRefundTradeRPCDTO);
        CompletableFuture.runAsync(() -> {
            CashierOrderRefundSettlement cashierOrderRefundSettlement = cashierOrderRefundSettlementService.queryCashierRefundSettlementByRefundOrderId(refundBankTradeOrSass.getRefundOrderId());
            kafkaCashierPayManager.pushCashierStatusUpdate(cashierOrderRefundSettlement);
        });
        return cashierRefundTradeRPCDTO;
    }

    @Override
    public CashierRefundTradeRPCDTO dishonoured(CashierAcctPublicRefundTradeRPCVo cashierRefundTradeRPCVo) {
        CashierAcctPublicRefundTradeReqVo cashierRefundTradeReqVo = new CashierAcctPublicRefundTradeReqVo();
        BeanUtils.copyProperties(cashierRefundTradeRPCVo, cashierRefundTradeReqVo);
        CashierRefundTradeRespVo refundBankTradeOrSass = cashierOrderRefundSettlementService.dishonoured(cashierRefundTradeReqVo);
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(refundBankTradeOrSass, cashierRefundTradeRPCDTO);
        CompletableFuture.runAsync(() -> {
            CashierOrderRefundSettlement cashierOrderRefundSettlement = cashierOrderRefundSettlementService.queryCashierRefundSettlementByRefundOrderId(refundBankTradeOrSass.getRefundOrderId());
            kafkaCashierPayManager.pushCashierStatusUpdate(cashierOrderRefundSettlement);
        });
        return cashierRefundTradeRPCDTO;
    }

    @Override
    public CashierRefundTradeRPCDTO refund4Reimbursement(CashierRefundTrade4ReimbursementRPCVO cashierRefundTrade4ReimbursementRPCVO) {
        CashierRefundTrade4ReimbursementReqVo cashierRefundTrade4ReimbursementReqVo = new CashierRefundTrade4ReimbursementReqVo();
        BeanUtils.copyProperties(cashierRefundTrade4ReimbursementRPCVO, cashierRefundTrade4ReimbursementReqVo);
        CashierRefundTradeRespVo refundBankTradeOrSass = cashierOrderRefundSettlementService.refundTrade4Reimbursement(cashierRefundTrade4ReimbursementReqVo);
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(refundBankTradeOrSass, cashierRefundTradeRPCDTO);
        CompletableFuture.runAsync(() -> {
            CashierOrderRefundSettlement cashierOrderRefundSettlement = cashierOrderRefundSettlementService.queryCashierRefundSettlementByRefundOrderId(refundBankTradeOrSass.getRefundOrderId());
            kafkaCashierPayManager.pushCashierStatusUpdate(cashierOrderRefundSettlement);
        });
        return cashierRefundTradeRPCDTO;
    }

    @Override
    public CashierRefundTradeRPCDTO dishonoured4Reimbursement(CashierRefundTrade4ReimbursementRPCVO cashierRefundTrade4ReimbursementRPCVO) {
        CashierRefundTrade4ReimbursementReqVo cashierRefundTrade4ReimbursementReqVo = new CashierRefundTrade4ReimbursementReqVo();
        BeanUtils.copyProperties(cashierRefundTrade4ReimbursementRPCVO, cashierRefundTrade4ReimbursementReqVo);
        CashierRefundTradeRespVo refundBankTradeOrSass = cashierOrderRefundSettlementService.dishonoured4Reimbursement(cashierRefundTrade4ReimbursementReqVo);
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(refundBankTradeOrSass, cashierRefundTradeRPCDTO);
        CompletableFuture.runAsync(() -> {
            CashierOrderRefundSettlement cashierOrderRefundSettlement = cashierOrderRefundSettlementService.queryCashierRefundSettlementByRefundOrderId(refundBankTradeOrSass.getRefundOrderId());
            kafkaCashierPayManager.pushCashierStatusUpdate(cashierOrderRefundSettlement);
        });
        return cashierRefundTradeRPCDTO;
    }


    @Override
    public List<CashierRefundBatchQueryRPCDTO> batchQueryByRefundTxnIds(CashierBatchQueryRefundTxnIdReqRPCVo cashierBatchQueryRefundTxnIdReqRPCVo) {
        CashierBatchQueryReqVo cashierBatchQueryReqVo = new CashierBatchQueryReqVo();
        cashierBatchQueryReqVo.setFbOrderIds(cashierBatchQueryReqVo.getFbOrderIds());
        List<CashierBatchRefundQueryRespVo> refundQueryRespVos = cashierOrderRefundSettlementService.batchQueryByRefundTxnIds(cashierBatchQueryRefundTxnIdReqRPCVo);
        List<CashierRefundBatchQueryRPCDTO> cashierRefundBatchQueryRPCDTOS = new ArrayList<>();
        CashierRefundBatchQueryRPCDTO cashierRefundBatchQueryRPCDTO;
        for (CashierBatchRefundQueryRespVo batchRefundQueryRespVo : refundQueryRespVos) {
            cashierRefundBatchQueryRPCDTO = new CashierRefundBatchQueryRPCDTO();
            BeanUtils.copyProperties(batchRefundQueryRespVo, cashierRefundBatchQueryRPCDTO);
            cashierRefundBatchQueryRPCDTOS.add(cashierRefundBatchQueryRPCDTO);
        }
        return cashierRefundBatchQueryRPCDTOS;
    }

    @Override
    public CashierRefundTradeRPCDTO createRefundBankTradeOrSass(CashierRefundTradeRPCVo cashierRefundTradeRPCVo, boolean saasBuget) {
        CashierRefundTradeReqVo cashierRefundTradeReqVo = new CashierRefundTradeReqVo();
        BeanUtils.copyProperties(cashierRefundTradeRPCVo, cashierRefundTradeReqVo);
        CashierRefundTradeRespVo refundBankTradeOrSass = cashierOrderRefundSettlementService.createRefundBankTradeOrSass(cashierRefundTradeReqVo, false);
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(refundBankTradeOrSass, cashierRefundTradeRPCDTO);
        CompletableFuture.runAsync(() -> {
            CashierOrderRefundSettlement cashierOrderRefundSettlement = cashierOrderRefundSettlementService.queryCashierRefundSettlementByRefundOrderId(refundBankTradeOrSass.getRefundOrderId());
            kafkaCashierPayManager.pushCashierStatusUpdate(cashierOrderRefundSettlement);
        });
        //todo 虚拟卡业务上处理，退款到个人帐户还是到企业帐户，
        // 退还预算和可核销金额的逻辑(2022-3-17)-->返回给NOC侧，然后根据refundToAccount 判断是退回到个人帐户还是企业帐户
        Boolean refundToAccount=refundBankTradeOrSass.getRefundToAccount();
        cashierRefundTradeRPCDTO.setRefundOrderFlag(refundToAccount);
        return cashierRefundTradeRPCDTO;
    }

    @Override
    public CashierRefundTradeRPCDTO refundTrade4ReliefOrSass(CashierTradeReliefRPCVo cashierTradeReliefRPCVo, boolean sassBudget) {
        FinhubLogger.info("【收银台】【RPC创建减免单】参数：{}", JSON.toJSONString(cashierTradeReliefRPCVo));
        ValidateUtils.validate(cashierTradeReliefRPCVo);
        CashierRefundTradeRespVo cashierRefundTradeRespVo = null;
        try {
            cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTrade4ReliefOrSaas(cashierTradeReliefRPCVo, sassBudget);
            FinhubLogger.info("【收银台】【RPC创建减免单】成功：{}", JSON.toJSONString(cashierRefundTradeRespVo));
        } catch (FinPayException e) {
            String msgError = "【收银台】【RPC创建减免单】异常:场景订单号:" + cashierTradeReliefRPCVo.getFbOrderId() + e.getMessage();
            if (Objects.equals(GlobalResponseCode.CASHIER_REFUND_TOTAL_AMOUNT_HAD_REFUND.getCode(), e.getCode()) ||
                    Objects.equals(GlobalResponseCode.CASHIER_REFUND_COMPANY_AMOUNT_IS_OVER.getCode(), e.getCode())) {
                FinhubLogger.warn(msgError, e);
            } else {
                dingDingMsgService.sendMsg(msgError);
                FinhubLogger.error(msgError, e);
            }
            throw e;
        } catch (Exception e) {
            String msgError = "【收银台】【RPC创建减免单】异常:场景订单号:" + cashierTradeReliefRPCVo.getFbOrderId() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError, e);
            throw e;
        }
        //场景通知
        String refundTxnId = cashierRefundTradeRespVo.getRefundTxnId();
        CompletableFuture.runAsync(() -> cashierOrderRefundSettlementService.checkRefundStatusAndCallBiz(refundTxnId, cashierTradeReliefRPCVo.getEmployeeId()));
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(cashierRefundTradeRespVo, cashierRefundTradeRPCDTO);
        return cashierRefundTradeRPCDTO;
    }


    /**
     * 发起退款-客诉减免单
     * 【只给总金额，先退个人，再退公司】
     * @param cashierTradeReliefRPCVo
     * @param sassBudget
     * @return
     */
    @Override
    public CashierRefundTradeRPCDTO refundTrade4ReliefOrSassByDefaultWay(CashierTradeReliefRPCVo cashierTradeReliefRPCVo, boolean sassBudget) {
        FinhubLogger.info("【收银台】【RPC创建减免单默认退款】参数：{}", JSON.toJSONString(cashierTradeReliefRPCVo));
        ValidateUtils.validate(cashierTradeReliefRPCVo);
        CashierRefundTradeRespVo cashierRefundTradeRespVo = null;
        if(BigDecimalUtils.hasPrice(cashierTradeReliefRPCVo.getPublicRefundAmount())||BigDecimalUtils.hasPrice(cashierTradeReliefRPCVo.getPersonalRefundAmount())||BigDecimalUtils.hasPrice(cashierTradeReliefRPCVo.getRedcouponRefundAmount())){
            FinhubLogger.error("【收银台】【RPC创建减免单默认退款】参数错误：{}", JSON.toJSONString(cashierRefundTradeRespVo));
            throw new FinPayException(GlobalResponseCode.CASHIER_REFUND_TOTAL_AMOUNT_IS_OVER);
        }
        try {
            cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTrade4ReliefOrSaasByDefaultWay(cashierTradeReliefRPCVo, sassBudget);
            FinhubLogger.info("【收银台】【RPC创建减免单默认退款】成功：{}", JSON.toJSONString(cashierRefundTradeRespVo));
        } catch (FinPayException e) {
            String msgError = "【收银台】【RPC创建减免单默认退款】异常:场景订单号:" + cashierTradeReliefRPCVo.getFbOrderId() + e.getMessage();
            if (Objects.equals(GlobalResponseCode.CASHIER_REFUND_TOTAL_AMOUNT_HAD_REFUND.getCode(), e.getCode()) ||
                    Objects.equals(GlobalResponseCode.CASHIER_REFUND_COMPANY_AMOUNT_IS_OVER.getCode(), e.getCode())) {
                FinhubLogger.warn(msgError, e);
            } else {
                dingDingMsgService.sendMsg(msgError);
                FinhubLogger.error(msgError, e);
            }
            throw e;
        } catch (Exception e) {
            String msgError = "【收银台】【RPC创建减免单默认退款】异常:场景订单号:" + cashierTradeReliefRPCVo.getFbOrderId() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError, e);
            throw e;
        }
        //场景通知
        String refundTxnId = cashierRefundTradeRespVo.getRefundTxnId();
        CompletableFuture.runAsync(() -> cashierOrderRefundSettlementService.checkRefundStatusAndCallBiz(refundTxnId, cashierTradeReliefRPCVo.getEmployeeId()));
        CashierRefundTradeRPCDTO cashierRefundTradeRPCDTO = new CashierRefundTradeRPCDTO();
        BeanUtils.copyProperties(cashierRefundTradeRespVo, cashierRefundTradeRPCDTO);
        cashierRefundTradeRPCDTO.setTotalRefundAmount(cashierTradeReliefRPCVo.getTotalRefundAmount());
        return cashierRefundTradeRPCDTO;
    }

}
