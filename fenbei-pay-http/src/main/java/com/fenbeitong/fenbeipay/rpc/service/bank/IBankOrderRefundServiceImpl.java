package com.fenbeitong.fenbeipay.rpc.service.bank;


import com.fenbeitong.fenbeipay.api.constant.enums.bank.*;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankRefundCreditReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankRefundTradeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankRefundTradeRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankRefundTrapRespDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankOrderRefundService;
import com.fenbeitong.fenbeipay.bank.base.conver.BankCardCreditAppConver;
import com.fenbeitong.fenbeipay.bank.base.conver.BankCardDistributeConver;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardApplyFlowMapper;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankPettyExtMapper;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankSubPettyFlowMapper;
import com.fenbeitong.fenbeipay.bank.base.dto.CreateRefundOrderDto;
import com.fenbeitong.fenbeipay.bank.base.dto.RefundOrderTypeDto;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.*;
import com.fenbeitong.fenbeipay.bank.company.order.manager.TradeCardManager;
import com.fenbeitong.fenbeipay.bank.company.service.virtualcard.IVirtualCardAccountSwitchService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.virtualcard.VirtualCardTradeFundType;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.bank.*;
import com.fenbeitong.fenbeipay.rpc.service.base.IBaseServiceImpl;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;

@Service("iBankOrderRefundService")
@Slf4j
public class IBankOrderRefundServiceImpl extends IBaseServiceImpl implements IBankOrderRefundService {

    @Autowired
    private TradeCardManager tradeCardManager;
    @Autowired
    private SearchCardManager searchCardManager;
    @Autowired
    private ApplyCardManager applyCardManager;
    @Autowired
    public CreditApplyManager creditApplyManager;
    @Autowired
    private BankPettyManager bankPettyManager;
    @Autowired
    private CreditDistributeManager creditDistributeManager;
    @Autowired
    private BankCardApplyFlowManger bankCardApplyFlowManger;
    @Autowired
    public BankCardApplyFlowMapper bankCardApplyFlowMapper;
    @Autowired
    public BankPettyExtMapper bankPettyExtMapper;
    @Autowired
    public BankSubPettyFlowMapper bankSubPettyFlowMapper;
    @Autowired
    public IBankCardCreditApplyServiceImpl bankCardCreditApplyService;
    @Autowired
    IVirtualCardAccountSwitchService iVirtualCardAccountSwitchService;
    @Autowired
    IBankAccountServiceImpl iBankAccountService ;

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BankRefundTradeRespDTO refundTrade(BankRefundTradeReqDTO reqDTO) {
        FinhubLogger.info("分贝通虚拟卡退款参数=={}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            //根据用户查询：担心员工离职
            BankCard bankCard = searchCardManager.queryByBankNameAndAccountNo(reqDTO.getBankName(), reqDTO.getBankAccountNo());

            BankCardTradeFlow cardTradeFlow = tradeCardManager.checkRefund(reqDTO);

            //确认退款类型
            RefundOrderTypeDto refundOrderTypeDto = queryRefundType(reqDTO, bankCard, cardTradeFlow);
            Integer tradeFundType=cardTradeFlow.getTradeFundType();
            //如果原始消费单的tradeFundType是来源于个人资金帐户，备用金不进行操作(广发银行错花还款需求增加逻辑)
            log.info("卡的交易流水tradeFundType="+cardTradeFlow.getTradeFundType());
            if(VirtualCardTradeFundType.COMPANY.getType() == tradeFundType){
                //备用金额退款
                bankPettyManager.updateBankPerryByRefundTrade(reqDTO,refundOrderTypeDto.getDitCompany());
            }
            //操作退款
            tradeCardManager.refundEmployeeTrade(reqDTO, bankCard, refundOrderTypeDto,cardTradeFlow);

            //判断是否存在错花还款
            BankCardApplyFlow applyFlow = bankCardApplyFlowManger.queryBankCardFlowByBizNoAndOperationType(reqDTO.getFbOrderId(), BankApplyType.CARD_REPAYMENT.getKey());
            if(!ObjUtils.isNull(applyFlow)){
                return BankRefundTradeRespDTO.of(bankCard.getCardBalance().add(reqDTO.getOperationAmount()));
            }else {
                //员工状态正常,直接返回 或 个人资金退款
                if (!refundOrderTypeDto.applyRefund() || VirtualCardTradeFundType.PERSONAL.getType() == tradeFundType) {
                    BankRefundTradeRespDTO refundTradeRespDTO=new BankRefundTradeRespDTO();
                    //true 退款原路返回(到个人帐户);
                    refundTradeRespDTO.setRefundToAccount(Boolean.TRUE);
                    refundTradeRespDTO.setBalance(bankCard.getCardBalance().add(reqDTO.getOperationAmount()));
                    return refundTradeRespDTO;
                }else {
                    //员工状态正常,退还到企业
                    //1不是相同企业。2.解绑状态 和 3.分贝通虚拟卡已经禁用是低版本套餐
                    BankRefundTradeRespDTO refundTradeRespDTO = BankRefundTradeRespDTO.of(executeRefundCredit(reqDTO, bankCard, refundOrderTypeDto));
                    FinhubLogger.info("虚拟卡退款====refundToAccount:{}",Boolean.FALSE);
                    //false:退款到企业帐户(退款原路返回失败情况)
                    refundTradeRespDTO.setRefundToAccount(Boolean.FALSE);
                    return refundTradeRespDTO;
                }
            }
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝通虚拟卡退款异常】refundTrade 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝通虚拟卡退款验证异常】refundTrade 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝通虚拟卡退款系统异常】refundTrade 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public BankRefundTrapRespDTO queryTrapRefund(String refundOrderId,String fbOrderId) {
        BankCardCreditApply bankCardCreditApply=creditApplyManager.queryBankCardByBizNoAndTrapStatus(refundOrderId, BankTrapStatus.BANKTRAPING);
        if (ObjUtils.isEmpty(bankCardCreditApply)){
            if(StringUtils.isNotBlank(fbOrderId)){
                BankCardApplyFlow applyFlow = bankCardApplyFlowManger.queryBankCardFlowByBizNoAndOperationType(fbOrderId, BankApplyType.CARD_REPAYMENT.getKey());
                if(!ObjUtils.isNull(applyFlow)){
                    return BankRefundTrapRespDTO.builder()
                            .bankApplyType(BankTrapApplyType.getTradeTypeEnum(applyFlow.getOperationType()).getKey())
                            .refundOrderId(refundOrderId)
                            .build();
                }
            }
            return null;
        }
        try {
            BankCardTradeFlow bankCardTradeFlow=tradeCardManager.queryByRefundOrderId(refundOrderId);
            BankCardApplyFlow bankCardApplyFlow= bankCardApplyFlowManger.queryByBankTransNo(bankCardTradeFlow.getBankTransNo());
            return BankRefundTrapRespDTO.builder()
                    .bankApplyType(BankTrapApplyType.getTradeTypeEnum(bankCardApplyFlow.getOperationType()).getKey())
                    .refundOrderId(refundOrderId)
                    .build();
        } catch (Exception e) {
            FinhubLogger.error("【分贝通虚拟卡退款退还查询失败】queryTrapRefund 参数：{}==={}", refundOrderId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public BankRefundTrapRespDTO queryBankNameByRefundOrderId(String refundOrderId) {
        BankRefundTrapRespDTO bankRefundTrapRespDTO = new BankRefundTrapRespDTO();
        BankCardCreditApply bankCardCreditApply = creditApplyManager.queryBankNameByRefundOrderId(refundOrderId);
        if (ObjUtils.isEmpty(bankCardCreditApply)) {
            return bankRefundTrapRespDTO;
        }
        BeanUtils.copyProperties(bankCardCreditApply,bankRefundTrapRespDTO);
        return bankRefundTrapRespDTO;
    }

    @Override
    public void recordReturnAmountFlow(BankRefundCreditReqDTO reqDTO) {
        applyCardManager.recordReturnAmountFlow(reqDTO);
    }


    ///————————————————————————————————private————————————————————————————————————————————


    /**
     * 退冻结池额度退企业额度
     * 记录退额订单
     *
     * @Param: [reqDTO, bankCard]
     * @return: java.math.BigDecimal
     */
    private BigDecimal executeRefundCredit(BankRefundTradeReqDTO reqDTO, BankCard bankCard, RefundOrderTypeDto refundOrderTypeDto) {
        FinhubLogger.info("交易触发退还操作:{},{}", refundOrderTypeDto.toString(), reqDTO.toString());

        //保存申请单
        CreateRefundOrderDto createRefundOrderDto = BankCardCreditAppConver.addRefundCreditOrder(reqDTO);
        BankCardCreditApply bankCardCreditApply = creditApplyManager.saveRefundCreditOrder(createRefundOrderDto);
        BankCardCreditDistribute bankCardCreditDistribute = BankCardDistributeConver.toBankCardCreditApplyDistribute(reqDTO, bankCard,VirtualOpenTypeEnum.RETURN.getKey(), bankCardCreditApply);
        creditDistributeManager.save(bankCardCreditDistribute);
        //备用金退还修改
        if (refundOrderTypeDto.getRetPetty()) {
            bankPettyManager.updateBankPerryByReturnCredit(reqDTO.getPettyId(), reqDTO.getOperationAmount());
            bankPettyManager.updateBankPerryReturnApplyTransNo(reqDTO.getPettyId(), bankCardCreditApply.getApplyTransNo());
        } else {
            //备用金增加退还额度
            bankPettyExtMapper.onlyAddReturnSum(reqDTO.getOperationAmount(), reqDTO.getPettyId());
        }
        //直接操作退企业
        if (refundOrderTypeDto.noCompanyBankCard()) {
            BankRefundCreditReqDTO bankRefundCreditReqDTO = BankCardCreditAppConver.refundTradeToBankRefundCreditReqDTO(reqDTO, bankCard, bankCardCreditDistribute);
            return applyCardManager.refundCreditAccount(bankRefundCreditReqDTO);
        }
        //扣卡退企业
        BankRefundCreditReqDTO bankRefundCreditReqDTO = BankCardCreditAppConver.toBankCardRefundCreditReqDTO(reqDTO, bankCard, bankCardCreditDistribute);

        CompletableFuture.runAsync(() -> {
            //退款直接退回企业  发送消息信息
            iBankAccountService.sendNoticeMsg(BankApplyCreditType.SYSTEM_REFUND_CREDIT.getBankApplyType().getKey(), bankCard, reqDTO.getOperationAmount(), reqDTO.getRefundOrderId());
        });

        return applyCardManager.refundCredit(bankRefundCreditReqDTO);

    }


}
