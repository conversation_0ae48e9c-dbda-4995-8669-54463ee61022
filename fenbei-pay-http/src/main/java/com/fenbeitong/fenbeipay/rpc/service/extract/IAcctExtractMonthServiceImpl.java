package com.fenbeitong.fenbeipay.rpc.service.extract;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctExtractMonthSearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctExtractMonthSearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractMonthService;
import com.fenbeitong.fenbeipay.extract.service.UAcctExtractMonthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName IAcctExtractMonthServiceImpl
 * @Description: 对账月切RPC接口实现
 * <AUTHOR>
 * @Date 2021/3/17
 **/
@Service("iAcctExtractMonthService")
public class IAcctExtractMonthServiceImpl implements IAcctExtractMonthService {

    @Autowired
    private UAcctExtractMonthService uAcctExtractMonthService;

    @Override
    public ResponsePage<AcctExtractMonthSearchRespRPCDTO> queryByPage(AcctExtractMonthSearchReqRPCDTO queryReq) {
        return uAcctExtractMonthService.queryByPage(queryReq);
    }

    @Override
    public int updateExtractMonth(String key, String value, Integer accountType) {
        return uAcctExtractMonthService.updateExtractMonth(key, value, accountType);
    }
}
