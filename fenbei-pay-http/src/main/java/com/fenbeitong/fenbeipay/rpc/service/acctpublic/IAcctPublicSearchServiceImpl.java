package com.fenbeitong.fenbeipay.rpc.service.acctpublic;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctpublic.base.conver.AcctPublicFlowConvert;
import com.fenbeitong.fenbeipay.acctpublic.manager.AcctPublicManager;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicSearchService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.accountpublic.AccountPublicStatus;
import com.fenbeitong.fenbeipay.api.model.ResultDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctFlowRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.*;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchService;
import com.fenbeitong.fenbeipay.api.util.BankNameShowConfig;
import com.fenbeitong.fenbeipay.core.enums.auth.AuthObjMenuCodeEnum;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.acctpublic.AcctPublicFindReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.acctpublic.AcctPublicFindRespVo;
import com.fenbeitong.fenbeipay.core.service.auth.DataAuthService;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublicFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAcctShowStatusEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.usercenter.api.model.dto.privilege.AuthObjResDTO;
import com.fenbeitong.usercenter.api.model.dto.privilege.EmployeePaymentAuthDTO;
import com.fenbeitong.usercenter.api.model.dto.privilege.EmployeePaymentAuthReqDTO;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 对公支付查询
 * 详情
 * 流水查询
 *
 * @since 4.0.0
 */
@Service("iAcctPublicSearchService")
public class IAcctPublicSearchServiceImpl implements IAcctPublicSearchService {
    @Autowired
    private AcctPublicSearchService acctPublicSearchService;


    @Autowired
    protected UAcctCompanyMainService uAcctCompanyMainService;

    @Autowired
    protected IPrivilegeService iPrivilegeService;

    @Autowired
    private DataAuthService dataAuthService;

    @Autowired
    private AcctPublicManager acctPublicManager;

    @Autowired
    private UAcctCommonService uAcctCommonService;


    //    //@TODO 老的
//    @Override
//    public AcctPublicDetailRespRPCDTO queryByCompanyIdAndBankAccountName(String companyId,  BankNameEnum bankAccountName) {
//        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(bankAccountName)) {
//            FinhubLogger.error("【对公账户查询参数异常】queryByCompanyIdAndBankAccountName 参数：{}={}", companyId, bankAccountName);
//            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
//        }
//        return accountPublicSearchService.queryByCompanyIdAndBankAccountName(companyId, companyId,bankAccountName);
//
//    }
    //@TODO 老的
    @Override
    public AcctPublicInfoRespRPCDTO queryByCompanyAndBankNo(String companyId,  BankNameEnum bankAccountName, String bankAccountNo) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(bankAccountName) || ObjUtils.isBlank(bankAccountNo)) {
            FinhubLogger.error("【对公账户查询参数异常】queryByCompanyAndBankNo 参数：{}={}", companyId, bankAccountName);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }

        return acctPublicSearchService.queryByCompanyIdAndBankAccountNo(companyId, bankAccountName,bankAccountNo);
    }



    @Override
    public AcctPublicDetailRespRPCDTO queryByCompanyIdAndBankAccountName(String companyId, String companyAccountId, BankNameEnum bankAccountName) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(bankAccountName)) {
            FinhubLogger.error("【对公账户查询参数异常】queryByCompanyIdAndBankAccountName 参数：{}={}={}", companyId,companyAccountId, bankAccountName);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicSearchService.queryByCompanyIdAndBankAccountName(companyId, companyAccountId,bankAccountName);

    }

    @Override
    public AcctPublicDetailRespRPCDTO queryByCompanyAcctIdAndBankAcctName(String companyAccountId, BankNameEnum bankNameEnum) {
        if (ObjUtils.isBlank(companyAccountId) || ObjUtils.isBlank(bankNameEnum)) {
            FinhubLogger.error("【对公账户查询参数异常】queryByCompanyAcctIdAndBankAcctName 参数：{}={}", companyAccountId, bankNameEnum);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicSearchService.queryByCompanyAcctIdAndBankAcctName(companyAccountId,bankNameEnum);

    }

    @Override
    public AcctPublicDetailRespRPCDTO queryByCompanyAcctId(String companyAccountId) {
        if (ObjUtils.isBlank(companyAccountId) ) {
            FinhubLogger.error("【对公账户查询参数异常】queryByCompanyAcctIdAndBankAcctName 参数：{}", companyAccountId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicSearchService.queryByCompanyAcctId(companyAccountId);

    }

    @Override
    public AcctPublicInfoRespRPCDTO queryByCompanyAndBankNo(String companyId, String companyAccountId, BankNameEnum bankAccountName, String bankAccountNo) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(bankAccountName) || ObjUtils.isBlank(bankAccountNo)) {
            FinhubLogger.error("【对公账户查询参数异常】queryByCompanyAndBankNo 参数：{}={}", companyId, bankAccountName);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        AcctPublicInfoRespRPCDTO acctPublicInfo = acctPublicSearchService.queryByCompanyIdAndBankAccountName(companyId, companyAccountId, bankAccountName, bankAccountNo);

        if (Objects.nonNull(acctPublicInfo)){
            replaceBankAccountAcctName(acctPublicInfo);
        }

        return acctPublicInfo;
    }

    private void replaceBankAccountAcctName(AcctPublicInfoRespRPCDTO acctPublicInfo) {
        AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.findAcctCompanyMain(acctPublicInfo.getCompanyId(), acctPublicInfo.getCompanyMainId(), acctPublicInfo.getBankAccountName().getCode());
        String businessName = acctPublicInfo.getBankAccountAcctName();
        if(Objects.nonNull(acctCompanyMain)){
            /**
             * ZHIFU-5070 中信账户名称优化
             * 1.上线之后展示主体名称
             * 2.白名单内展示主体名称
             */

            String showMainName = uAcctCommonService.getShowMainName(acctPublicInfo.getBankAccountName().getCode(), acctCompanyMain.getBusinessName(), acctPublicInfo.getBankAccountNo(), acctCompanyMain);
            businessName = showMainName;
        }
        acctPublicInfo.setBankAccountAcctName(businessName);
    }

    @Override
    public ResponsePage<AcctPublicFlowFindRespRPCDTO>  searchAcctPublicFlowPage(AcctPublicFlowFindRepRPCDTO requestDTO) {
        requestDTO.checkReq();
        return acctPublicSearchService.searchAcctPublicFlowPage(requestDTO);

    }

    @Override
    public AcctPublicFlowDetailRespRPCDTO searchAcctPublicFlowDetail(String accountFlowId) {
        if (ObjUtils.isBlank(accountFlowId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicSearchService.searchAcctPublicFlowDetail(accountFlowId);
    }


    @Override
    public List<AcctPublicSimpleInfoRespRPCDTO> findAccountAcctNameByCompanyId(String companyId) {
        if (ObjUtils.isBlank(companyId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        List<AcctPublicSimpleInfoRespRPCDTO> acctPublicNameByCompanyId = acctPublicSearchService.findAcctPublicNameByCompanyId(companyId);
        if(CollectionUtils.isNotEmpty(acctPublicNameByCompanyId)){
            List<String> companyMainIds = acctPublicNameByCompanyId.stream().filter(e -> !StringUtils.isBlank(e.getCompanyMainId())).map(AcctPublicSimpleInfoRespRPCDTO::getCompanyMainId).collect(Collectors.toList());
            List<AcctCompanyMain> byMainIds = uAcctCompanyMainService.findByMainIds(companyMainIds);
            Map<String, AcctCompanyMain> mapByMainId = byMainIds.stream().collect(Collectors.toMap(AcctCompanyMain::getCompanyMainId, Function.identity(), (key1, key2) -> key2));
            acctPublicNameByCompanyId.stream().forEach(e->{
                String bankAccountName = StringUtils.isBlank(e.getBankAccountName()) ? "" : e.getBankAccountName();
                String bankAccountNo = StringUtils.isBlank(e.getBankAccountNo()) ? "" : e.getBankAccountNo();
                //String businessName = Objects.isNull(mapByMainId.get(e.getCompanyMainId())) ? "" : mapByMainId.get(e.getCompanyMainId()).getBusinessName();
                AcctCompanyMain acctCompanyMain = mapByMainId.get(e.getCompanyMainId());
                String businessName = e.getBankAccountAcctName();
                if(Objects.nonNull(acctCompanyMain)){
                    /**
                     * ZHIFU-5070 中信账户名称优化
                     * 1.上线之后展示主体名称
                     * 2.白名单内展示主体名称
                     */
                    String showMainName = uAcctCommonService.getShowMainName(bankAccountName, acctCompanyMain.getBusinessName(), bankAccountNo, acctCompanyMain);
                    businessName = showMainName;
                }
                e.setShowAccountName(BankNameShowConfig.makeBankPublicShowName(bankAccountName, bankAccountNo, businessName));
                e.setBankAccountAcctName(businessName);

                BankNameEnum bankEnum = BankNameEnum.getBankEnum(bankAccountName);
                e.setShowBankAccountName(bankEnum.getName());
                e.setBankIconUrl(bankEnum.getBankIconUrl());
                e.setBankBackgroundUrl(bankEnum.getBankBackground());
            });
        }
        return acctPublicNameByCompanyId;
    }

    @Override
    public List<AcctPublicSimpleInfoRespRPCDTO> findAllAccountAcctNameByCompanyId(String companyId) {
        if (ObjUtils.isBlank(companyId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicSearchService.findAllAccountAcctNameByCompanyId(companyId);
    }

    @Override
    public AcctPublicFindRespRPCDTO queryAcctPublicList(AcctPublicFindReqRPCDTO acctPublicFindReqRPCDTO) {
        AcctPublicFindReqVo acctPublicFindReqVo = new AcctPublicFindReqVo();
        BeanUtils.copyProperties(acctPublicFindReqRPCDTO, acctPublicFindReqVo);
        acctPublicFindReqVo.checkReq();
        AcctPublicFindRespVo acctPublicList = acctPublicSearchService.queryAcctPublicList(acctPublicFindReqVo);
        return AcctPublicFindRespRPCDTO.builder()
                .simpleInfoRespRPCDTOS(acctPublicList.getSimpleInfoRespRPCDTOS())
                .build();

    }

    //@TODO 老的
    @Override
    public GetAcctPubliDetailRespRPCDTO queryByCompanyAndAcctName(String companyId, BankNameEnum bankAccountName) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(bankAccountName)) {
            FinhubLogger.error("【对公账户参数异常】queryByCompanyAndAcctName 参数：{}={}", companyId, bankAccountName);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicSearchService.queryByCompanyAndAcctName(companyId,companyId, bankAccountName);
    }
    @Override
    public GetAcctPubliDetailRespRPCDTO queryByCompanyAndAcctName(String companyId, String companyAccountId,BankNameEnum bankAccountName) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(bankAccountName)) {
            FinhubLogger.error("【对公账户参数异常】queryByCompanyAndAcctName 参数：{}={}", companyId, bankAccountName);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicSearchService.queryByCompanyAndAcctName(companyId,companyAccountId, bankAccountName);
    }

    @Override
    public ResponsePage<AcctPublicListRespRPCDTO> searchAcctPublicPage(AcctPublicPageReqRPCDTO acctPublicPageReqRPCDTO) {
        return acctPublicSearchService.searchAcctPublicPage(acctPublicPageReqRPCDTO);
    }

    @Override
    public List<String> getCompanyName() {
        return acctPublicSearchService.findCompanyName();
    }

    @Override
    public List<AcctPublicSimpleInfoRespRPCDTO> findBankAccountAcctName() {
        return acctPublicSearchService.findBankAccountAcctName();
    }

    @Override
    public List<AcctPublicSimpleInfoRespRPCDTO> findNormalBankAccountAcctName() {
        return acctPublicSearchService.findNormalBankAccountAcctName();
    }


    @Override
    public ResponsePage<AcctPublicFlowListRespRPCDTO> queryAcctPublicFlowPage(AcctPublicFlowReqRPCDTO acctPublicFlowReqRPCDTO) {
        ValidateUtils.validate(acctPublicFlowReqRPCDTO);
        return acctPublicSearchService.searchAcctPublicFlowList(acctPublicFlowReqRPCDTO);
    }

    @Override
    public ResponsePage<ExportAcctPublicFlowRespRPCDTO> exportAcctPublicFlowPage(AcctPublicFlowQueryReqRPCDTO acctPublicFlowQueryReqRPCDTO) {
        return acctPublicSearchService.exportAcctPublicFlowPage(acctPublicFlowQueryReqRPCDTO);
    }

    @Override
    public ResponsePage<QueryAcctPublicFlowAmountRespRPCDTO> queryAcctPublicFlowAmount(AcctPublicAmountPageReqRPCDTO amountPageReqRPCDTO) {
        return acctPublicSearchService.queryAcctPublicFlowAmount(amountPageReqRPCDTO);
    }

    @Override
    public ResponsePage<QueryAcctPublicFlowAmountRespRPCDTO> queryAcctPublicFlowAmountV2(AcctPublicAmountPageReqRPCDTO amountPageReqRPCDTO) {
        return acctPublicSearchService.queryAcctPublicFlowAmountV2(amountPageReqRPCDTO);
    }

    @Override
    public  List<AcctFlowRespDTO> queryFlowByNoCost(){
        List<AccountPublicFlow> accountPublicFlows = acctPublicSearchService.queryFlowByNoCost();
        List<AcctFlowRespDTO> acctFlowRespDTOS = AcctPublicFlowConvert.AcctPublicFlowWebRespRPCDTO2(accountPublicFlows);
        return acctFlowRespDTOS;

    }

    /**
     * 账户总金额
     * @author: zhaoxu
     * @date: 2022-04-07 16:10:27
     */
    @Override
    public ResultDTO<BigDecimal> queryAcctPublicTotalBalance(String companyId, String userId) {
        FinhubLogger.info("账户总金额查询 queryAcctPublicTotalBalance companyId:{},userId:{}", companyId, userId);
        ResultDTO<BigDecimal> res = new ResultDTO<>();
        res.setModel(BigDecimal.ZERO);

        try {
            // todo 新权限 done
            // List<EmployeePaymentAuthReqDTO.AccountInfo> changeAccountList = iPrivilegeService.getChangeAccountList(companyId, userId);
            AuthObjResDTO authObjResDTO = dataAuthService.getDataAuth(AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
            if(authObjResDTO == null){
                res.setSuccess(true);
                FinhubLogger.error("账户总金额查询 queryAcctPublicTotalBalance , 未查询到权限数据, menuCode:{},companyId:{},userId:{},res:{}", AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId, JSONObject.toJSONString(res));
                return res;
            }
            if (!authObjResDTO.getAccount().getAllData() && CollectionUtils.isEmpty(authObjResDTO.getAccount().getPartDataIdList())) {
                FinhubLogger.warn("账户总金额查询 queryAcctPublicTotalBalance , 未查询到有权限的账户");
                res.setSuccess(true);
                return res;
            }

            // 根据权限查询用户下的对公账户
            List<String> companyAccountIds = CollectionUtils.isEmpty(authObjResDTO.getAccount().getPartDataIdList()) ? null : authObjResDTO.getAccount().getPartDataIdList();
            // List<String> companyAccountIds = changeAccountList.stream().map(EmployeePaymentAuthReqDTO.AccountInfo::getId).collect(Collectors.toList());
            // 查询启用、禁用的账户状态
            List<Integer> status = Arrays.asList(AccountPublicStatus.NORMAL.getKey(), AccountPublicStatus.DISABLE.getKey());
            BigDecimal totalBalance = acctPublicSearchService.queryAcctPublicTotalBalance(companyId, status, companyAccountIds);
            res.setSuccess(true);
            res.setModel(totalBalance);
        } catch (Exception e) {
            res.setSuccess(false);
            res.setErrMsg(e.getMessage());
            FinhubLogger.error("账户总金额查询 queryAcctPublicTotalBalance exception companyId:{},userId:{}", companyId, userId, e);
        } catch (Throwable e) {
            res.setSuccess(false);
            res.setErrMsg(e.getMessage());
            FinhubLogger.error("账户总金额查询 queryAcctPublicTotalBalance error companyId:{},userId:{}", companyId, userId, e);
        }
        FinhubLogger.info("账户总金额查询 queryAcctPublicTotalBalance companyId:{},userId:{},res:{}", companyId, userId, JSONObject.toJSONString(res));
        return res;
    }

    @Override
    public List<AcctPublicInfoAndAuthRespRPCDTO> findAcctAndAuth(String companyId, String userId) throws FinhubException {
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(companyId,userId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }

        //获取启用的对公付款账户
        List<AcctPublicSimpleInfoRespRPCDTO> accountPublics = findAccountAcctNameByCompanyId(companyId);
        List<AcctPublicInfoAndAuthRespRPCDTO> acctAndAuthList = new ArrayList<>();
        if (CollectionUtils.isEmpty(accountPublics)){
            return acctAndAuthList;
        }

        //排除不展示的
        List<AcctPublicSimpleInfoRespRPCDTO> accountAcctNameList = accountPublics.stream()
                .filter(e -> Objects.nonNull(e.getAccountPublicStatus()) && AccountPublicStatus.isNormal(e.getAccountPublicStatus())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(accountAcctNameList)){
            return acctAndAuthList;
        }

        accountAcctNameList.forEach(p->{
            AcctPublicInfoAndAuthRespRPCDTO acctAndAuthInfo = new AcctPublicInfoAndAuthRespRPCDTO();
            BeanUtils.copyProperties(p,acctAndAuthInfo);
            BankNameEnum bankEnum = BankNameEnum.getBankEnum(p.getBankAccountName());
            acctAndAuthInfo.setBankCode(bankEnum.getCode());
            acctAndAuthInfo.setBankName(bankEnum.getName());
            acctAndAuthList.add(acctAndAuthInfo);
        });

        //获取用户的账户操作付款的权限
        EmployeePaymentAuthDTO employeePaymentAuthDTO = queryEmployeePaymentAuthInfo(companyId, userId);
        if (ObjUtils.isBlank(employeePaymentAuthDTO)){
            return acctAndAuthList;
        }

        //创建付款单的权限
        EmployeePaymentAuthReqDTO.PaymentCommonInfo paymentOrderAuth = employeePaymentAuthDTO.getPaymentOrderAuth();
        if (Objects.isNull(paymentOrderAuth)) {
            return acctAndAuthList;
        }

        List<EmployeePaymentAuthReqDTO.AccountInfo> accountList = paymentOrderAuth.getAccountList();
        if (CollectionUtils.isEmpty(accountList)){
            return acctAndAuthList;
        }

        Map<String, EmployeePaymentAuthReqDTO.AccountInfo> map = accountList.stream().collect(Collectors.toMap(p -> p.getId(), p -> p));
        acctAndAuthList.forEach(p->{
            EmployeePaymentAuthReqDTO.AccountInfo accountInfo = map.get(p.getCompanyAccountId());
            if(Objects.nonNull(accountInfo)){
                p.setPaymentOrderAuth(true);
            }
        });

        return acctAndAuthList;
    }

    @Override
    public List<String> queryAllAuthAccountByCompanyId(String companyId) {
        List<AccountPublic> acctPublicByCompanyIdAndStatus = acctPublicManager
                .findAcctPublicByCompanyIdAndStatus(companyId, Arrays.asList(AccountPublicStatus.NORMAL.getKey(), AccountPublicStatus.DISABLE.getKey(), AccountPublicStatus.UNBIND.getKey()),
                        Arrays.asList(FundAcctShowStatusEnum.SHOW.getStatus()));
        if (CollectionUtils.isEmpty(acctPublicByCompanyIdAndStatus)) {
            return Collections.EMPTY_LIST;
        }
        return acctPublicByCompanyIdAndStatus.stream().map(AccountPublic::getCompanyAccountId).collect(Collectors.toList());
    }

    /**
     * 获取用户的账户操作付款的权限
     * @param companyId
     * @param userId
     * @return
     */
    private EmployeePaymentAuthDTO queryEmployeePaymentAuthInfo(String companyId, String userId){
        try {
            FinhubLogger.info("获取用户付款权限,companyId={},userId={}",companyId, userId);
            EmployeePaymentAuthDTO employeePaymentAuthDTO = iPrivilegeService.queryEmployeePaymentAuthInfo(companyId, userId);
            FinhubLogger.info("获取用户付款权限,companyId={},userId={},resInfo={}",companyId, userId, JsonUtils.toJson(employeePaymentAuthDTO));
            return employeePaymentAuthDTO;
        }catch (FinhubException e){
            FinhubLogger.error("获取用户付款权限异常,companyId={},userId={}",companyId, userId, e);
            throw e;
        }catch (Exception e){
            FinhubLogger.error("获取用户付款权限异常,companyId={},userId={}",companyId, userId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode());
        }
    }
}
