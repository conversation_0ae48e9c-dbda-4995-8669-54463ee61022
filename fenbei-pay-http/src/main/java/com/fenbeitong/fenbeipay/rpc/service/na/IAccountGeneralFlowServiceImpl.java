package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountGeneralBankRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountGeneralFlowStereoFindReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountGeneralFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountGeneralFlowService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service("iAccountGeneralFlowService")
public class IAccountGeneralFlowServiceImpl implements IAccountGeneralFlowService {

    @Autowired
    private AccountGeneralFlowService accountGeneralFlowService;

    @Autowired
    private AccountGeneralService accountGeneralService;


    @Override
    public ResponsePage<AccountGeneralFlowRespRPCDTO> queryCompanyAccountFlowsByStereo(AccountGeneralFlowStereoFindReqRPCDTO reqRpcDTO) {
        AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
        accountGeneralBankRPCDTO.setBankNo(reqRpcDTO.getCompanyId());
        accountGeneralBankRPCDTO.setCompanyId(reqRpcDTO.getCompanyId());
        accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
        AccountGeneral accountGeneral = accountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
        if(Objects.isNull(accountGeneral)){
            throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
        }
        reqRpcDTO.setAccountId(accountGeneral.getAccountGeneralId());
        return accountGeneralFlowService.queryPageByStereo(reqRpcDTO);
    }
}
