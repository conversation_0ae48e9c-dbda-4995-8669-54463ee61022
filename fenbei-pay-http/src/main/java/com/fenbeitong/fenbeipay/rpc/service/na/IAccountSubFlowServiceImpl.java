package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessCreditFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessDebitFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctIndividualCreditFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctIndividualDebitFlow;
import com.fenbeitong.fenbeipay.acctdech.service.*;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountSubOperationType;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubFlowStereoFindReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubFlowService;
import com.fenbeitong.fenbeipay.na.service.AccountSubFlowService;
import com.fenbeitong.finhub.common.constant.FundAccountModelType;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

@Service("iAccountSubFlowService")
public class IAccountSubFlowServiceImpl implements IAccountSubFlowService {

    @Autowired
    private AccountSubFlowService accountSubFlowService;

    @Autowired
    private AcctIndividualCreditFlowService acctIndividualCreditFlowService;

    @Autowired
    private AcctIndividualDebitFlowService acctIndividualDebitFlowService;

    @Autowired
    private AcctBusinessDebitFlowService acctBusinessDebitFlowService;

    @Autowired
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Override
    public ResponsePage<AccountSubFlowRespRPCDTO> queryAccountSubFlowsByStereo(AccountSubFlowStereoFindReqRPCDTO reqRPCDTO) {
        //fixme 跟刘厚兵确认，旧版页面一定要改（一个账户一个模式，不支持多账户）
        ResponsePage<AccountSubFlowRespRPCDTO> responsePage = new ResponsePage<>();
        //如果模式为空或者（类型为空或者类型为多个）返回空对象
        if (Objects.isNull(reqRPCDTO.getAccountModel())) {
            return responsePage;
        }
        if (CollectionUtils.isEmpty(reqRPCDTO.getAccountSubTypes()) || reqRPCDTO.getAccountSubTypes().size() > 1) {
            return responsePage;
        }
        Integer acctSubType = reqRPCDTO.getAccountSubTypes().get(0);
        ResponsePage<AccountSubFlowRespRPCDTO> flows = null;
        if (FundAccountSubType.isBusinessAccount(acctSubType) &&
                FundAccountModelType.isRecharge(reqRPCDTO.getAccountModel())) {
            flows = acctBusinessDebitFlowService.queryPageByStereo(reqRPCDTO);
        } else if (FundAccountSubType.isBusinessAccount(acctSubType) &&
                FundAccountModelType.isCredit(reqRPCDTO.getAccountModel())) {
            flows = acctBusinessDebitFlowService.queryPageByStereo(reqRPCDTO);
        } else if (FundAccountSubType.isIndividualAccount(acctSubType) &&
                FundAccountModelType.isRecharge(reqRPCDTO.getAccountModel())) {
            flows = acctBusinessDebitFlowService.queryPageByStereo(reqRPCDTO);
        } else if (FundAccountSubType.isIndividualAccount(acctSubType) &&
                FundAccountModelType.isCredit(reqRPCDTO.getAccountModel())) {
            flows = acctBusinessDebitFlowService.queryPageByStereo(reqRPCDTO);
        }
//        ResponsePage<AccountSubFlowRespRPCDTO> flows = accountSubFlowService.queryPageByStereo(reqRPCDTO);
        return flows;
    }

    /**
     * @param bizNo
     * @param accountSubType
     * @param accountSubOperationType
     * @return
     */
    @Override
    public BigDecimal queryAccountOperationAmountByBizNo(String bizNo, int accountSubType, int accountSubOperationType) {
        BigDecimal totalOperationAmount = BigDecimal.ZERO;
        if (FundAccountSubType.isBusinessAccount(accountSubType)) {
            BigDecimal totalOperationAmountDebit = acctBusinessDebitFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubOperationType);
            BigDecimal totalOperationAmountCredit = acctBusinessCreditFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubOperationType);
            totalOperationAmountDebit = Objects.isNull(totalOperationAmountDebit) ? BigDecimal.ZERO : totalOperationAmountDebit;
            totalOperationAmountCredit = Objects.isNull(totalOperationAmountCredit) ? BigDecimal.ZERO : totalOperationAmountCredit;
            totalOperationAmount = totalOperationAmountDebit.add(totalOperationAmountCredit);
        } else if (FundAccountSubType.isIndividualAccount(accountSubType)) {
            BigDecimal totalOperationAmountDebit = acctIndividualDebitFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubOperationType);
            BigDecimal totalOperationAmountCredit = acctIndividualCreditFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubOperationType);
            totalOperationAmountDebit = Objects.isNull(totalOperationAmountDebit) ? BigDecimal.ZERO : totalOperationAmountDebit;
            totalOperationAmountCredit = Objects.isNull(totalOperationAmountCredit) ? BigDecimal.ZERO : totalOperationAmountCredit;
            totalOperationAmount = totalOperationAmountDebit.add(totalOperationAmountCredit);
        }
//        BigDecimal totalOperationAmount = accountSubFlowService.queryAccountOperationAmountByBizNo(bizNo, accountSubType, accountSubOperationType);
        return totalOperationAmount;
    }

    /**
     * @param voucherGrantTaskId
     * @param accountSubTyp
     * @return
     */
    @Override
    public AccountSubFlowRespRPCDTO queryAccountSubFlowByGrantTaskId(String voucherGrantTaskId, int accountSubTyp) {
        AccountSubFlowRespRPCDTO accountSubType = new AccountSubFlowRespRPCDTO();
        if (FundAccountSubType.isBusinessAccount(accountSubTyp)) {
            AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryByTaskId(voucherGrantTaskId, AccountSubOperationType.FROZEN_VOUCHER_GRANT.getKey());
            if (Objects.nonNull(acctBusinessDebitFlow)) {
                BeanUtils.copyProperties(acctBusinessDebitFlow, accountSubType);
                accountSubType.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                accountSubType.setAccountModel(FundAccountModelType.RECHARGE.getKey());
                return accountSubType;
            }
            AcctBusinessCreditFlow acctBusinessCreditFlow = acctBusinessCreditFlowService.queryByTaskId(voucherGrantTaskId, AccountSubOperationType.FROZEN_VOUCHER_GRANT.getKey());
            if (Objects.nonNull(acctBusinessCreditFlow)) {
                BeanUtils.copyProperties(acctBusinessCreditFlow, accountSubType);
                accountSubType.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                accountSubType.setAccountModel(FundAccountModelType.CREDIT.getKey());
                return accountSubType;
            }
        } else if (FundAccountSubType.isIndividualAccount(accountSubTyp)) {
            AcctIndividualDebitFlow acctIndividualDebitFlow = acctIndividualDebitFlowService.queryByTaskId(voucherGrantTaskId, AccountSubOperationType.FROZEN_VOUCHER_GRANT.getKey());
            if (Objects.nonNull(acctIndividualDebitFlow)) {
                BeanUtils.copyProperties(acctIndividualDebitFlow, accountSubType);
                accountSubType.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                accountSubType.setAccountModel(FundAccountModelType.RECHARGE.getKey());
                return accountSubType;
            }
            AcctIndividualCreditFlow individualCreditFlow = acctIndividualCreditFlowService.queryByTaskId(voucherGrantTaskId, AccountSubOperationType.FROZEN_VOUCHER_GRANT.getKey());
            if (Objects.nonNull(individualCreditFlow)) {
                BeanUtils.copyProperties(individualCreditFlow, accountSubType);
                accountSubType.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                accountSubType.setAccountModel(FundAccountModelType.CREDIT.getKey());
                return accountSubType;
            }
        }
        return null;
//        return accountSubFlowService.queryAccountSubFlowByGrantTaskId(voucherGrantTaskId, accountSubTyp);
    }

    /**
     * @param accountSubFlowId
     * @return
     */
    @Override
    public AccountSubFlowRespRPCDTO queryAccountSubFlowByFlowId(String accountSubFlowId) {
        AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
        AcctIndividualDebitFlow acctIndividualDebitFlow = acctIndividualDebitFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
        if (Objects.nonNull(acctIndividualDebitFlow)) {
            BeanUtils.copyProperties(acctIndividualDebitFlow, accountSubFlowRespRPCDTO);
            accountSubFlowRespRPCDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            accountSubFlowRespRPCDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
            return accountSubFlowRespRPCDTO;
        }
        AcctIndividualCreditFlow individualCreditFlow = acctIndividualCreditFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
        if (Objects.nonNull(individualCreditFlow)) {
            BeanUtils.copyProperties(individualCreditFlow, accountSubFlowRespRPCDTO);
            accountSubFlowRespRPCDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            accountSubFlowRespRPCDTO.setAccountModel(FundAccountModelType.CREDIT.getKey());
            return accountSubFlowRespRPCDTO;
        }
        AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
        if (Objects.nonNull(acctBusinessDebitFlow)) {
            BeanUtils.copyProperties(acctBusinessDebitFlow, accountSubFlowRespRPCDTO);
            accountSubFlowRespRPCDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            accountSubFlowRespRPCDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
            return accountSubFlowRespRPCDTO;
        }
        AcctBusinessCreditFlow acctBusinessCreditFlow = acctBusinessCreditFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
        if (Objects.nonNull(acctBusinessCreditFlow)) {
            BeanUtils.copyProperties(acctBusinessCreditFlow, accountSubFlowRespRPCDTO);
            accountSubFlowRespRPCDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            accountSubFlowRespRPCDTO.setAccountModel(FundAccountModelType.CREDIT.getKey());
            return accountSubFlowRespRPCDTO;
        }
        return null;
//        return accountSubFlowService.queryAccountSubFlowByFlowId(accountSubFlowId);
    }
}
