package com.fenbeitong.fenbeipay.rpc.service.redcoupon;

import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.RedcouponGrantStereoResp;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.RedcouponRecallStereoResp;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponService;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTaskService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponFlow;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponGrantRecord;
import com.fenbeitong.fenbeipay.redcoupon.manager.AccountRedCouponFlowManager;
import com.fenbeitong.fenbeipay.redcoupon.manager.AccountRedcouponGrantRecordManager;
import com.fenbeitong.fenbeipay.redcoupon.service.AccountRedcouponService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * @Description: 红包券rpc操作实现类
 * @ClassName: IAccountRedcouponServiceImpl
 * @Author: zhangga
 * @CreateDate: 2019/12/9 3:25 PM
 * @UpdateUser:
 * @UpdateDate: 2019/12/9 3:25 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iAccountRedcouponService")
public class IAccountRedcouponServiceImpl implements IAccountRedcouponService {

    @Autowired
    private AccountRedcouponService accountRedcouponService;

    @Autowired
    private AccountRedcouponGrantRecordManager accountRedcouponGrantRecordManager;

    @Autowired
    private AccountRedCouponFlowManager accountRedCouponFlowManager;

    @Autowired
    private IVouchersTaskService iVouchersTaskService;

    @Autowired
    protected DingDingMsgService dingDingMsgService;

    @Override
    public void grantRedcoupon(RedcouponGrantReqDTO operationReqDTO) {
        try {
            operationReqDTO.checkReq();
            accountRedcouponService.grantRedcoupon(operationReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【Stereo发放红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }catch (Exception e) {
            FinhubLogger.error("【红包券】【Stereo发放红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw e;
        }
    }

    @Override
    public RedcouponGrantStereoResp grantRedcouponStereo(RedcouponStereoGrantReqDTO operationReqDTO) {
        FinhubLogger.info("【红包券】【Stereo发放红包券】,param{}", JsonUtils.toJson(operationReqDTO));
        RedcouponGrantStereoResp redcouponGrantStereoResp = new RedcouponGrantStereoResp();
        try {
            operationReqDTO.checkReq();
            Boolean isGranted = checkIsGranted(operationReqDTO.getBizNo(),redcouponGrantStereoResp);
            if(isGranted){
                return redcouponGrantStereoResp;
            }
            redcouponGrantStereoResp = accountRedcouponService.grantRedcouponStereo(operationReqDTO);
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【Stereo发放红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            redcouponGrantStereoResp.setStatus(2);
            redcouponGrantStereoResp.setFailReason(e.getMessage());
        }
        return redcouponGrantStereoResp;
    }

    /**
     * 校验是否已发放
     * @param bizNo
     * @param redcouponGrantStereoResp
     * @return
     */
    private Boolean checkIsGranted(String bizNo, RedcouponGrantStereoResp redcouponGrantStereoResp) {
        AccountRedcouponGrantRecord accountRedcouponGrantRecord = accountRedcouponGrantRecordManager.queryRecordByBizNo(bizNo);
        if(Objects.isNull(accountRedcouponGrantRecord)){
            return false;
        }
        redcouponGrantStereoResp.setGrantFlowId(accountRedcouponGrantRecord.getGrantRecordId());
        redcouponGrantStereoResp.setStatus(1);
        redcouponGrantStereoResp.setGrantAmt(accountRedcouponGrantRecord.getGrantAmt());
        return true;
    }

    @Override
    public void addRedcoupon(RedcouponOperationReqDTO operationReqDTO) {
        try {
            ValidateUtils.validate(operationReqDTO);
            accountRedcouponService.addRedcoupon(operationReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【Stereo增加红包券额度异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【Stereo增加红包券额度异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw e;
        }
    }

    @Override
    public void callRedcoupon(RedcouponOperationReqDTO operationReqDTO) {
        try {
            ValidateUtils.validate(operationReqDTO);
            accountRedcouponService.callRedcoupon(operationReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【Stereo撤回红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【Stereo撤回红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw e;
        }
    }

    @Override
    public RedcouponRecallStereoResp callRedcouponStereo(RedcouponRecallReqDTO redcouponRecallReqDTO) {
        FinhubLogger.info("【红包券】【Stereo撤回红包券】,param{}", JsonUtils.toJson(redcouponRecallReqDTO));
        RedcouponRecallStereoResp redcouponRecallStereoResp = new RedcouponRecallStereoResp();
        try {
            ValidateUtils.validate(redcouponRecallReqDTO);
            Boolean isRecalled = checkIsRecall(redcouponRecallReqDTO.getBizNo(),redcouponRecallStereoResp);
            if(isRecalled){
                return redcouponRecallStereoResp;
            }
            checkGrantIsExist(redcouponRecallReqDTO.getGrantFlowId());
            redcouponRecallStereoResp = accountRedcouponService.callRedcoupon(redcouponRecallReqDTO);
            if(redcouponRecallReqDTO.getIsOverdue()){
                try {
                    iVouchersTaskService.recoveryExpiryRedCouponVouchersTask(redcouponRecallReqDTO.getCompanyId(),redcouponRecallReqDTO.getGrantFlowId());
                }catch (Exception e){
                    FinhubLogger.error("红包券过期回收分贝券失败 companyId:{},err=",redcouponRecallReqDTO.getCompanyId(),e);
                    dingDingMsgService.sendMsg("【【红包券过期回收分贝券失败】" + redcouponRecallReqDTO.getCompanyId());
                }
            }
        }  catch (Exception e) {
            FinhubLogger.error("【红包券】【Stereo撤回红包券异常】{},参数：{}",e.getMessage(), redcouponRecallReqDTO, e);
            redcouponRecallStereoResp.setStatus(2);
            redcouponRecallStereoResp.setFailReason(e.getMessage());
        }
        return redcouponRecallStereoResp;
    }

    private AccountRedcouponGrantRecord checkGrantIsExist(String grantFlowId) {
        if(StringUtils.isNotEmpty(grantFlowId)) {
            AccountRedcouponGrantRecord accountRedcouponGrantRecord = accountRedcouponGrantRecordManager.queryRecordByRecordId(grantFlowId);
            if (Objects.isNull(accountRedcouponGrantRecord)) {
                  throw new FinPayException(GlobalResponseCode.ACCOUNT_REDCOUPON_GRANT_RECORD_NOT_EXIST);
            }
            return accountRedcouponGrantRecord;
        }
        return null;
    }

    /**
     * 是否已撤回
     * @param bizNo
     */
    private Boolean checkIsRecall(String bizNo,RedcouponRecallStereoResp redcouponRecallStereoResp) {
        AccountRedcouponFlow accountRedcouponFlow = accountRedCouponFlowManager.queryFlowByBizNo(bizNo);
        if(Objects.nonNull(accountRedcouponFlow)){
            redcouponRecallStereoResp.setStatus(1);
            redcouponRecallStereoResp.setRecallAmt(accountRedcouponFlow.getOperationAmount());
            redcouponRecallStereoResp.setRecallFlowId(accountRedcouponFlow.getRedcouponFlowId());
            return true;
        }
        return false;
    }

    @Override
    @Deprecated
    public void updateEffectiveRedcoupon(RedcouponUpdateOtherReqDTO operationReqDTO) {
        try {
            ValidateUtils.validate(operationReqDTO);
            if(DateUtils.compareByDateTime(operationReqDTO.getRedcouponExpiryTime(),new Date())<=0){
                throw new FinhubException(GlobalResponseCode.ACCOUNT_REDCOUPON_ACCOUNT_EXPIRYTIME_IS_OVER.getCode(), GlobalResponseCode.ACCOUNT_REDCOUPON_ACCOUNT_EXPIRYTIME_IS_OVER.getType(), GlobalResponseCode.ACCOUNT_REDCOUPON_ACCOUNT_EXPIRYTIME_IS_OVER.getMsg());
            }
            accountRedcouponService.updateEffectiveRedcoupon(operationReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【Stereo修改有效期异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【Stereo修改有效期异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw e;
        }
    }

    @Override
    public void cashierPay(RedcouponOperationReqDTO operationReqDTO) {
        try {
            ValidateUtils.validate(operationReqDTO);
            accountRedcouponService.cashierPay(operationReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【收银台消费红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【收银台消费红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw e;
        }
    }

    @Override
    public void cashierRefund(RedcouponOperationReqDTO operationReqDTO) {
        try {
            ValidateUtils.validate(operationReqDTO);
            accountRedcouponService.cashierRefund(operationReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【收银台退款红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【收银台退款红包券异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw e;
        }
    }

    @Override
    @Deprecated
    public void bankCardApply(RedcouponOperationReqDTO operationReqDTO) {
        try {
            ValidateUtils.validate(operationReqDTO);
            accountRedcouponService.bankCardApply(operationReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【虚拟卡申请额度异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【虚拟卡申请额度异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw e;
        }
    }

    @Override
    @Deprecated
    public void bankCardRefund(RedcouponOperationReqDTO operationReqDTO) {
        try {
            ValidateUtils.validate(operationReqDTO);
            accountRedcouponService.bankCardRefund(operationReqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【虚拟卡退还额度异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【虚拟卡退还额度异常】{},参数：{}",e.getMessage(), operationReqDTO, e);
            throw e;
        }
    }

    @Override
    @Deprecated
    public void updateUseScope(RedcouponUpdateUseScopeReqDTO reqDTO) {

        try {
            ValidateUtils.validate(reqDTO);
            accountRedcouponService.updateUseScope(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【红包券】【Stereo修改红包券使用范围异常】{},参数：{}",e.getMessage(), reqDTO, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【红包券】【Stereo修改红包券使用范围异常】{},参数：{}",e.getMessage(), reqDTO, e);
            throw e;
        }
    }


    //=======================================Private Method====================================


    @Override
    public Integer updateGrantRecord(String grantRecordId, String bizNo) {
        if(StringUtils.isBlank(grantRecordId)||StringUtils.isBlank(bizNo)){
            return 0;
        }
        AccountRedcouponGrantRecord accountRedcouponGrantRecord = new AccountRedcouponGrantRecord();
        accountRedcouponGrantRecord.setGrantRecordId(grantRecordId);
        accountRedcouponGrantRecord.setBizNo(bizNo);
        return accountRedcouponGrantRecordManager.updateGrantRecordByRecordId(accountRedcouponGrantRecord);
    }
}
