package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctComUpdateService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UCompanySwitchService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctComUpdate4ByComIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctComUpdate4UpdateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCompanySwitchAddReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCompanySwitchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctComUpdate4RespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCompanySwitchRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctComUpdateService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 */
@Service("iAcctComUpdateService")
public class IAcctComUpdateServiceImpl implements IAcctComUpdateService {

    @Autowired
    private UAcctComUpdateService uAcctComUpdateService;

    @Override
    public void updateAcctCompanyUpdate4(AcctComUpdate4UpdateReqDTO reqDTO) {
        try {
            ValidateUtils.validate(reqDTO);
            uAcctComUpdateService.updateAcctCompanyUpdate4(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【资金账户4.0】updateAcctCompanyUpdate4 参数：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【资金账户4.0】updateAcctCompanyUpdate4  参数：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【资金账户4.0】updateAcctCompanyUpdate4  参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public AcctComUpdate4RespDTO findActUpdate4ByComId(AcctComUpdate4ByComIdReqDTO reqDTO) {
        try {
            ValidateUtils.validate(reqDTO);
            return uAcctComUpdateService.findActUpdate4ByComId(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【资金账户4.0】findActUpdate4ByComId：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【资金账户4.0】findActUpdate4ByComId：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【资金账户4.0】findActUpdate4ByComId：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

}
