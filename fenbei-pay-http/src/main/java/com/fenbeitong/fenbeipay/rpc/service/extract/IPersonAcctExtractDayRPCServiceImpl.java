package com.fenbeitong.fenbeipay.rpc.service.extract;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.PersonAcctEnum;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.PersonAcctExtractDaySearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.PersonAcctExtractDaySearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.extract.IPersonAcctExtractDayRPCService;
import com.fenbeitong.fenbeipay.dto.extract.AcctPersonExtractDay;
import com.fenbeitong.fenbeipay.extract.db.mapper.AcctPersonExtractDayMapper;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: guogx
 * @Date: 2022/11/3 下午9:46
 */
@Service("iPersonAcctExtractDayRPCService")
public class IPersonAcctExtractDayRPCServiceImpl implements IPersonAcctExtractDayRPCService {

    @Resource
    private AcctPersonExtractDayMapper acctPersonExtractDayMapper;

    @Override
    public ResponsePage<PersonAcctExtractDaySearchRespRPCDTO> queryPage(PersonAcctExtractDaySearchReqRPCDTO searchReqRPCDTO) {
        FinhubLogger.info("IPersonAcctExtractDayRPCServiceImpl#queryPage#req:{}", JSON.toJSON(searchReqRPCDTO));
        ResponsePage<PersonAcctExtractDaySearchRespRPCDTO> respPage = new ResponsePage<>();
        Example example = getExample(searchReqRPCDTO);
        int count = acctPersonExtractDayMapper.selectCountByExample(example);
        respPage.setTotalCount(count);
        FinhubLogger.info("IPersonAcctExtractDayRPCServiceImpl#queryPage#resp.totalCount:{}", count);
        if(count > 0){
            PageHelper.startPage(searchReqRPCDTO.getPageNo(), searchReqRPCDTO.getPageSize());
            List<AcctPersonExtractDay> acctPersonExtractDayList = acctPersonExtractDayMapper.selectByExample(example);
            List<PersonAcctExtractDaySearchRespRPCDTO> dtoList = acctPersonExtractDayList.stream().map(item -> convertPo2Dto(item)).collect(Collectors.toList());
            respPage.setDataList(dtoList);
        }
        return respPage;
    }

    private Example getExample(PersonAcctExtractDaySearchReqRPCDTO searchReqRPCDTO) {
        Example example = new Example(AcctPersonExtractDay.class);
        Example.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotBlank(searchReqRPCDTO.getEmployeeName())){
            criteria.andEqualTo("employeeName", searchReqRPCDTO.getEmployeeName());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getEmployeePhone())){
            criteria.andEqualTo("employeePhone", searchReqRPCDTO.getEmployeePhone());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getCompanyName())){
            criteria.andEqualTo("companyName", searchReqRPCDTO.getCompanyName());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getBankName())){
            criteria.andEqualTo("bankName", searchReqRPCDTO.getBankName());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getCompanyBankAcctId())){
            criteria.andEqualTo("companyBankAcctId", searchReqRPCDTO.getCompanyBankAcctId());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getCompanyBankAccountNo())){
            criteria.andEqualTo("companyBankAccountNo", searchReqRPCDTO.getCompanyBankAccountNo());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getBankAccountNo())){
            criteria.andEqualTo("bankAccountNo", searchReqRPCDTO.getBankAccountNo());
        }
        String billTimeFrom = formatBillTime(searchReqRPCDTO.getBillTimeFrom());
        if(StringUtils.isNotBlank(billTimeFrom)){
            criteria.andGreaterThanOrEqualTo("billTime", billTimeFrom);
        }
        String billTimeTo = formatBillTime(searchReqRPCDTO.getBillTimeTo());
        if(StringUtils.isNotBlank(billTimeTo)){
            criteria.andLessThanOrEqualTo("billTime", billTimeTo);
        }
        if(searchReqRPCDTO.getAccountType() != null){
            criteria.andEqualTo("accountType", searchReqRPCDTO.getAccountType());
        }
        if(searchReqRPCDTO.getExtractStatus() != null){
            criteria.andEqualTo("extractStatus", searchReqRPCDTO.getExtractStatus());
        }
        example.orderBy("id").desc();
        return example;
    }

    private PersonAcctExtractDaySearchRespRPCDTO convertPo2Dto(AcctPersonExtractDay acctPersonExtractDay){
        PersonAcctExtractDaySearchRespRPCDTO personAcctExtractDayDTO = new PersonAcctExtractDaySearchRespRPCDTO();
        BeanUtils.copyProperties(acctPersonExtractDay, personAcctExtractDayDTO);
        personAcctExtractDayDTO.setAccountTypeShow(PersonAcctEnum.AccountType.getEnum(acctPersonExtractDay.getAccountType()).msg());
        personAcctExtractDayDTO.setBankNameShow(BankNameEnum.getBankEnum(acctPersonExtractDay.getBankName()).getName());
        personAcctExtractDayDTO.setExtractStatusShow(PersonAcctEnum.ExtractStatus.getEnum(acctPersonExtractDay.getExtractStatus()).msg());
        return personAcctExtractDayDTO;
    }


    private String formatBillTime(String billTime){
        try{
            if(StringUtils.isNotBlank(billTime)){
                //2022-11-08 00:00:00
                billTime = billTime.substring(0,10).replaceAll("-","");
            }
        }catch (Exception e){
            FinhubLogger.error("IPersonAcctExtractDayRPCServiceImpl#formatBillTime#error#req:{}", billTime, e);
        }
        return billTime;
    }

}
