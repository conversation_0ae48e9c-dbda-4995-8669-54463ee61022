package com.fenbeitong.fenbeipay.rpc.service.bank;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyCreditFlowType;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyCreditIsNew;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.ApplyCreditDeductionReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankSearchApplyCreditReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankUnRefundCreditReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.RefundOrderReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankCardCreditApplyResqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.UnRefundApplyRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.VirtualCardCreditApplyResqDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardCreditApplyService;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.CreditApplyOperationManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.SearchCardManager;
import com.fenbeitong.fenbeipay.bank.softwareSaas.DTO.RefundOrderParamDTO;
import com.fenbeitong.fenbeipay.bank.softwareSaas.RefundOrderForBudgetManager;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyBusinessConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCreditApplyFlow;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;

import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Service("iBankCardCreditApplyService")
public class IBankCardCreditApplyServiceImpl implements IBankCardCreditApplyService {
    /**强制解锁时间设置*/
    private final long lockTime = 6000L;

    /**等待时间**/
    private final long waitTime = 6100L;

    @Autowired
    private CreditApplyOperationManager creditApplyOperationManager;

    @Autowired
    private RedissonService redissonService;

    @Autowired
    private SearchCardManager searchCardManager;

    @Autowired
    private RefundOrderForBudgetManager refundOrderForBudgetManager;

    @Override
    public List<BankCardCreditApplyResqDTO> getBankCardCreditApplyByParam(BankSearchApplyCreditReqDTO reqDTO) {
        FinhubLogger.info("IBankCardCreditApplyServiceImpl->getBankCardCreditApplyByParam->pay:reqDTO={}",JSONObject.toJSON(reqDTO));
        FinhubLogger.info("getBankCardCreditApplyByParam init param :{}",JSONObject.toJSON(reqDTO));
        return creditApplyOperationManager.getBankCardCreditApplyByParam(reqDTO);
    }

    @Override
    public List<BankCardCreditApplyResqDTO> getBankCardCreditApplyByBizNoList(List<String> bizNoList) {
        return creditApplyOperationManager.getBankCardCreditApplyByBizNoList(bizNoList);
    }

    @Override
    public ResponsePage<BankCardCreditApplyResqDTO> getBankCardCreditApplyForPage(BankSearchApplyCreditReqDTO reqDTO) {
        FinhubLogger.info("IBankCardCreditApplyServiceImpl->getBankCardCreditApplyForPage->pay:reqDTO={}",JSONObject.toJSON(reqDTO));
        ResponsePage<BankCardCreditApplyResqDTO> responsePage=new ResponsePage();
        List<BankCardCreditApplyResqDTO> list=getBankCardCreditApplyByParam(reqDTO);
        int count=creditApplyOperationManager.getBankCardCreditApplyForCount(reqDTO);
        responsePage.setDataList(list);
        responsePage.setTotalCount(count);
        return responsePage;
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void deductionBalanceForCreditApply(List<ApplyCreditDeductionReqDTO> applyList) {
        FinhubLogger.info("扣减额度申请单    init param {}", JSONObject.toJSON(applyList));
        if (CollectionUtils.isEmpty(applyList)) {
            return;
        }
        String companyId = applyList.get(0).getCompanyId();
        String employeeId = applyList.get(0).getEmployeeId();
        //saas 费用单ID
        String costId=applyList.get(0).getCostId();
        List<String> list = Lists.newArrayList();
        //参数中金额单位：元 后续处理转换成分
        Map<String, BigDecimal> eductMoneyMap = dealWithData(applyList, list);
        //1:参数合法性检查
        if (StringUtils.isEmpty(companyId) || StringUtils.isEmpty(employeeId) || CollectionUtils.isEmpty(list) || StringUtils.isEmpty(costId) || eductMoneyMap.size()==0 ) {
            FinhubLogger.info("扣减费用单参数为空param is empty {},eductMoneyMap size={}", JSONObject.toJSON(applyList),eductMoneyMap.size());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "扣减费用申请单参数为空!");
        }
        //2：批量扣减数量限制
        if (list.size() > 100) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "扣减费用单超过数量限制100!");
        }
        //3：批量扣减操作（进行锁操作）
        String key = companyId + ":" + employeeId;
        final String lockKey = MessageFormat.format(RedisKeyBusinessConstant.BANK_CARD_CREDITAPPYLY_USEBALANCE_OPERATION, key);
        try {
            boolean lock=redissonService.tryLock(lockTime, waitTime, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.info("费用单加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            creditApplyOperationManager.eductCreditApplyForUseBalance(companyId, employeeId,costId, list, eductMoneyMap);
        } catch (InterruptedException e) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
        } catch (Exception e) {
            throw e;
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("费用单释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("费用单释放锁失败：{}", lockKey, e);
            }
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void epositBalanceForCreditApply(String consId) {
        FinhubLogger.info("恢复费用单(费用删除时候) init param consId={}", consId);
        if (StringUtils.isEmpty(consId)) {
            return;
        }
        //获取费用单关联的申请单扣减记录
        List<BankCardCreditApplyFlow> flowList= creditApplyOperationManager.getApplyFlowList(consId, BankApplyCreditFlowType.APPLY_HEXIAO_TYPE.getKey());
        if(CollectionUtils.isEmpty(flowList)){
            FinhubLogger.info("恢复费用单流水为空(费用删除时候)param is empty consId {}", consId);
            return;
        }
        String companyId=flowList.get(0).getCompanyId();
        String employeeId=flowList.get(0).getEmployeeId();
        List<String> list= Lists.newArrayList();
        Map<String,BigDecimal> decimalMap=dealWithData2(flowList,list);
        FinhubLogger.info("删除费用需要恢复的扣减金额 decimalMap:{}",JSONObject.toJSON(decimalMap));
        //1:参数合法性检查
        if (StringUtils.isEmpty(companyId) || StringUtils.isEmpty(employeeId) || CollectionUtils.isEmpty(list)) {
            FinhubLogger.info("恢复费用单超过数量限制或者为空(费用删除时候)param is empty {}", JSONObject.toJSON(decimalMap));
            return;
        }
        //2：批量恢复数量限制
        if(list.size()>100){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),"恢复费用单单超过数量限制100!");
        }
        //3：批量夏恢复操作
        String key = companyId + ":" + employeeId;
        final String lockKey = MessageFormat.format(RedisKeyBusinessConstant.BANK_CARD_CREDITAPPYLY_USEBALANCE_OPERATION, key);
        try {
            boolean lock=redissonService.tryLock(lockTime, waitTime, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.info("申请单恢复加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            creditApplyOperationManager.epositBalanceForCreditApply(companyId,employeeId,consId,list,decimalMap);
        } catch (InterruptedException e) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
        } catch (Exception e) {
            throw e;
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("申请单恢复释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("申请单恢复释放锁失败：{}", lockKey, e);
            }
        }
    }

    @Override
    public long getMaxCreditApplyCountsByUser(String companyId, String employeeId, String bankCardAccount) {
        FinhubLogger.info("IBankCardCreditApplyServiceImpl->getMaxCreditApplyCountsByUser->pay:companyId={},employeeId={},bankCardAccount={}",companyId,employeeId,bankCardAccount);
        if(StringUtils.isEmpty(companyId) || StringUtils.isEmpty(employeeId) || StringUtils.isEmpty(bankCardAccount)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),"参数为空!");
        }
        return creditApplyOperationManager.getMaxCreditApplyCountsByUser(companyId,employeeId,bankCardAccount);
    }

    /**
     *
     * @param pettyId 备用金ID为空为普通模式
     * @param applyTransNoRefund 操作单ID
     * @param applyAmount 扣减金额
     * @param companyId
     * @param employeeId
     * @param bankName
     */
    @Override
    public void autoDeductionBalanceForCreditApply(String pettyId,String applyTransNoRefund,BigDecimal applyAmount,String companyId, String employeeId, String bankName) {
        FinhubLogger.info("退款申请单扣减参数为=== pettyId={},applyTransNoRefund={},applyAmount={},companyId={},employeeId={},bankName={}",
                pettyId,applyTransNoRefund,applyAmount,companyId,employeeId,bankName);
        //1:检查参数
        if (StringUtils.isEmpty(companyId) || StringUtils.isEmpty(employeeId) || StringUtils.isEmpty(applyAmount) || StringUtils.isEmpty(bankName)) {
            FinhubLogger.info("扣减退款申请单参数为空param is empty");
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "扣减退款申请单参数为空!");
        }
        //2：加锁
        String key = companyId + ":" + employeeId;
        final String lockKey = MessageFormat.format(RedisKeyBusinessConstant.BANK_CARD_CREDITAPPYLY_USEBALANCE_OPERATION, key);
        try {
            boolean lock=redissonService.tryLock(lockTime, waitTime, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.info("申请单恢复加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            creditApplyOperationManager.autoDeductionBalanceForCreditApply(pettyId,applyTransNoRefund,applyAmount,companyId,employeeId,bankName);

        } catch (InterruptedException e) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
        } catch (Exception e) {
            throw e;
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("申请单恢复释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("申请单恢复释放锁失败：{}", lockKey, e);
            }
        }
    }
    @Override
    public Date getCreditApplyForCreateTime(String companyId, String employeeId,Integer cardModel) {
        FinhubLogger.info("IBankCardCreditApplyServiceImpl->getCreditApplyForCreateTime->pay:companyId={},employeeId={},cardModel={}",companyId,employeeId,cardModel);
        if(StringUtils.isEmpty(companyId) || StringUtils.isEmpty(employeeId)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),"参数为空!");
        }

        BankSearchApplyCreditReqDTO reqDTO=BankSearchApplyCreditReqDTO.builder()
                .companyId(companyId)
                .employeeId(employeeId)
                .cardModel(cardModel)
                .isNew(BankApplyCreditIsNew.apply_new.getKey())
                .useBalance(BigDecimal.ZERO)
                .pageNo(1)
                .pageSize(1)
                .build();
        List<BankCardCreditApplyResqDTO> list=getBankCardCreditApplyByParam(reqDTO);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0).getCreateTime();
    }

    /**
     *  处理数据(单位转换：元转成分)
     * @param applyList
     * @param applyTranNos
     * @return
     */
    private Map<String, BigDecimal> dealWithData(List<ApplyCreditDeductionReqDTO> applyList,List<String> applyTranNos) {
        Map<String, BigDecimal> map = new HashMap<>();
        for (ApplyCreditDeductionReqDTO applyReq : applyList) {
            applyTranNos.add(applyReq.getApplyTransNo());
            BigDecimal operationAmount = applyReq.getOperationAmount();
            String applyId = applyReq.getApplyTransNo();
            if (null == applyId || null == operationAmount) {
                FinhubLogger.info("费用关联额度申请单参数:applyTransNo={},operationAmount={}",applyId,null ==operationAmount ? null : operationAmount.doubleValue() );
                continue;
            }
            //单位转换：元转换成分
            operationAmount=BigDecimalUtils.yuan2fen(operationAmount);
            map.put(applyId, operationAmount);
        }
        return map;
    }
    private Map<String, BigDecimal> dealWithData2(List<BankCardCreditApplyFlow> applyList,List<String> applyTranNos) {
        Map<String, BigDecimal> map = new HashMap<>();
        for (BankCardCreditApplyFlow applyReq : applyList) {
            applyTranNos.add(applyReq.getApplyTransNo());
            BigDecimal operationAmount = applyReq.getDeductionAmount();
            String transNo = applyReq.getApplyTransNo();
            if (null == transNo || null == operationAmount) {
                continue;
            }
            map.put(transNo, operationAmount);
        }
        return map;
    }

    @Override
    public int getCardBalanceByCompanyId(String companyId) {
        FinhubLogger.info("IBankCardCreditApplyServiceImpl->getCardBalanceByCompanyId->pay:companyId={}",companyId);
        if (StringUtils.isEmpty(companyId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.getCardBalanceByCompanyId(companyId);
    }

    @Override
    public void refundOrderForBudget(RefundOrderReqDTO refundOrderReqDTO) {
        //虚拟卡业务上处理，退款到个人帐户还是到企业帐户，退还预算和可核销金额的逻辑(2022-3-17)
        RefundOrderParamDTO refundOrderParamDTO = new RefundOrderParamDTO();
        BeanUtils.copyProperties(refundOrderReqDTO, refundOrderParamDTO);
        FinhubLogger.info("虚拟卡退款init param:{},转化后 refundOrderParamDTO param：{}",
                JSONObject.toJSONString(refundOrderReqDTO), JSONObject.toJSONString(refundOrderParamDTO));
        String pettyId = refundOrderReqDTO.getPettyId();
        if (StringUtils.isEmpty(pettyId)) {
            refundOrderForBudgetManager.refundOrderForNomal(refundOrderParamDTO);
        } else {
            refundOrderForBudgetManager.refundOrderForPetty(refundOrderParamDTO);
        }
    }

    @Override
    public List<VirtualCardCreditApplyResqDTO> getVirtualCardCreditApply(String companyId, List<String> costIds) {
        FinhubLogger.info("IBankCardCreditApplyServiceImpl->getCardBalanceByCompanyId->pay:companyId={}",companyId);
        FinhubLogger.info("查询费用关联的申请单数据param companyId:{},costIds:{}", companyId, costIds);
        if (CollectionUtils.isEmpty(costIds)) {
            return Lists.newArrayList();
        }
        if (costIds.size() > 200) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "最多查询单超过数量限制200!");
        }
        List<BankCardCreditApplyFlow> list = creditApplyOperationManager.getVirtualCreditApplyFlow(companyId, costIds);
        return tranfer(list);
    }

    @Override
    public ResponsePage<UnRefundApplyRespDTO> queryCreditApplyForPage(BankUnRefundCreditReqDTO reqDTO) {
        FinhubLogger.info("reqDTO={}",JSONObject.toJSON(reqDTO));
        ValidateUtils.validate(reqDTO);
        ResponsePage<UnRefundApplyRespDTO> responsePage=new ResponsePage();
        List<UnRefundApplyRespDTO> list=creditApplyOperationManager.queryCreditDistributeByParam(reqDTO);
        int count=creditApplyOperationManager.queryCreditDistributeForCount(reqDTO);
        responsePage.setDataList(list);
        responsePage.setTotalCount(count);
        return responsePage;
    }

    @Override
    public void dataCleanCreditDisTributeInfo(String id,String applyTransNo) {
        //從tb_bank_card_credit_apply  写入 tb_bank_card_credit_distribute
        creditApplyOperationManager.dataCleanCreditDisTributeInfo(id,applyTransNo);
    }

    @Override
    public void updateCreditDisTributeInfo(String id,String applyTransNo) {
        CompletableFuture.runAsync(()->{
            creditApplyOperationManager.updateCreditDisTributeInfo(id,applyTransNo);
        });
    }

    /**
     * BankCardCreditApplyFlow链表转为 VirtualCardCreditApplyResqDTO链表
     **/
    private List<VirtualCardCreditApplyResqDTO> tranfer(List<BankCardCreditApplyFlow> list) {
        List<VirtualCardCreditApplyResqDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        list.stream().forEach(bankCardCreditApplyFlow -> result.add(buildVirtualCardCreditApplyResqDTO(bankCardCreditApplyFlow)));
        return result;
    }

    private VirtualCardCreditApplyResqDTO buildVirtualCardCreditApplyResqDTO(BankCardCreditApplyFlow bankCardCreditApplyFlow) {
        VirtualCardCreditApplyResqDTO virtualCardCreditApplyResqDTO = new VirtualCardCreditApplyResqDTO();
        virtualCardCreditApplyResqDTO.setApplyId(bankCardCreditApplyFlow.getBizNo());
        virtualCardCreditApplyResqDTO.setApplyAmount(bankCardCreditApplyFlow.getApplyAmount());
        virtualCardCreditApplyResqDTO.setCostId(bankCardCreditApplyFlow.getOperaId());
        return virtualCardCreditApplyResqDTO;
    }

}
