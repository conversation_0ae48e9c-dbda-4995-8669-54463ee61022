package com.fenbeitong.fenbeipay.rpc.service.bank;

import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyCreditType;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.v2.BankCardSearchApplyCreditV2ReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.v2.BaseV2PageRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.v2.BankCardCreditApplyV2RespDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardCreditApplyV2Service;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardCreditApplyMapper;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCreditApply;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Service("iBankCardCreditApplyV2Service")
@Slf4j
public class BankCardCreditApplyV2ServiceImpl implements IBankCardCreditApplyV2Service {
    @Autowired
    BankCardCreditApplyMapper bankCardCreditApplyMapper;
    @Override
    public BaseV2PageRespDTO<BankCardCreditApplyV2RespDTO> searchApplyCredit(BankCardSearchApplyCreditV2ReqDTO bankCardSearchApplyCreditV2ReqDTO) {
        Example cardSearchExample = getApplyCardSearchExample(bankCardSearchApplyCreditV2ReqDTO.getCompanyId(), bankCardSearchApplyCreditV2ReqDTO.getEmployeeId(),
                bankCardSearchApplyCreditV2ReqDTO.getBankName(), null, bankCardSearchApplyCreditV2ReqDTO.getBankAccountNos());
        RowBounds rowBounds = new RowBounds(bankCardSearchApplyCreditV2ReqDTO.getOffset(), bankCardSearchApplyCreditV2ReqDTO.getPageSize());
        List<BankCardCreditApply> bankCardCreditApplyList =bankCardCreditApplyMapper.selectByExampleAndRowBounds(cardSearchExample,rowBounds);
        BaseV2PageRespDTO<BankCardCreditApplyV2RespDTO> responsePage = new BaseV2PageRespDTO<>();
        if (ObjUtils.isEmpty(bankCardCreditApplyList)){
            responsePage.setTotalCount(0);
            responsePage.setDataList(new ArrayList<>());
            return responsePage;
        }
        List<BankCardCreditApplyV2RespDTO> bankCardCreditApplyV2RespDTOS = new ArrayList<>();
        bankCardCreditApplyList.forEach(data -> {
            BankCardCreditApplyV2RespDTO bankCardCreditApplyV2RespDTO = new BankCardCreditApplyV2RespDTO();
            BeanUtils.copyProperties(data,bankCardCreditApplyV2RespDTO);
            bankCardCreditApplyV2RespDTOS.add(bankCardCreditApplyV2RespDTO);
        });
        responsePage.setTotalCount(bankCardCreditApplyList.size());
        responsePage.setDataList(bankCardCreditApplyV2RespDTOS);
        return responsePage;
    }

    private Example getApplyCardSearchExample(String companyId, String employeeId,String bankName,Integer cardModel,List<String> bankAccountNos) {
        Example example = new Example(BankCardCreditApply.class);
        Example.Criteria criteria = example.createCriteria();
        //查询条件
        if (ObjUtils.isNotBlank(companyId)) {
            criteria.andEqualTo("companyId", companyId);
        }
        if (ObjUtils.isNotBlank(employeeId)) {
            criteria.andEqualTo("employeeId", employeeId);
        }
        if (ObjUtils.isNotBlank(bankName)) {
            criteria.andEqualTo("bankName", bankName);
        }
        if (ObjUtils.isNotBlank(cardModel)) {
            criteria.andEqualTo("cardModel", cardModel);
        }
        if (CollectionUtils.isNotEmpty(bankAccountNos)) {
            criteria.andIn("bankAccountNo", bankAccountNos);
        }
        //只展示 额度退还
        criteria.andIn("applyType", Lists.newArrayList(
                BankApplyCreditType.REFUND_CREDIT.getKey(), BankApplyCreditType.COMPANY_REFUND_CREDIT.getKey(),BankApplyCreditType.SYSTEM_REFUND_CREDIT.getKey()));
        example.orderBy("createTime").desc();
        return example;
    }
}
