package com.fenbeitong.fenbeipay.rpc.service.voucher.base;

import com.alibaba.druid.sql.ast.statement.SQLIfStatement;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.*;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.*;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.req.VouchersExternalRpcReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersExternalDetailsRpcResDTO;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherCostStatusEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherModelEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTaskType;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTermTypeEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.WriteInvoiceType;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskDetailsCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskDetailsRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTempletRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskDetailsRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskResponseRPCDTO;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersTaskCreateService;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.constant.personpay.EmployeeStatus;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherCommonUtils;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskCreateDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskDetailsDTO;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTempletService;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTypeService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.sass.budget.service.api.dubbo.BudgetCompanyBusinessConfigService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleTypeDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeOrgUnitDTO;
import com.fenbeitong.usercenter.api.model.enums.company.CompanyRuleType;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 分贝券操作RPC服务
 * @ClassName: IVouchersOperationServiceImpl
 * @Author: zhangga
 * @CreateDate: 2020/11/10 3:07 下午
 * @UpdateUser:
 * @UpdateDate: 2020/11/10 3:07 下午
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Slf4j
@Service("iVouchersTaskCreateService")
public class IVouchersTaskCreateServiceImpl implements IVouchersTaskCreateService {
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private VouchersTypeService vouchersTypeService;
    @Autowired
    private VouchersTempletService vouchersTempletService;
    @Autowired
    private ICompanyService iCompanyService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private VoucherCommonUtils voucherCommonUtils;
    private static final int MAX_DETAILS_SIZE = 200;

    @Override
    public VouchersTaskResponseRPCDTO createVouchersGrantTask(VouchersTaskRPCDTO vouchersTaskRPCDTO, Integer voucherTaskType, Integer voucherSourceType) throws FinhubException {
        //发放任务参数校验
        ValidateUtils.validate(vouchersTaskRPCDTO);
        String companyId = vouchersTaskRPCDTO.getCompanyId();
        //封装主任务参数
        VouchersTaskCreateDTO vouchersTaskCreateDTO = new VouchersTaskCreateDTO();
        vouchersTaskCreateDTO.buildDTO(vouchersTaskRPCDTO, voucherTaskType);
        //校验当前企业券是否可先开票
        Integer writeInvoiceType = vouchersTaskCreateDTO.getWriteInvoiceType();
        checkInvoiceAdvancePrivilege(companyId, writeInvoiceType);
        ValidateUtils.validate(vouchersTaskCreateDTO);
        //校验当前企业是否开启分贝券占用预算
        VoucherCostStatusEnum voucherCostStatusEnum = checkCostConfig(vouchersTaskRPCDTO, voucherTaskType);
        vouchersTaskCreateDTO.setCostStatus(voucherCostStatusEnum.getStatus());
        //里程补贴发券不支持先票发券（4.9.2 取消此规则）
        //封装明细
        List<VouchersTaskDetailsRPCDTO> taskDetailsRPCDTOList = vouchersTaskRPCDTO.getVouchersTaskDetailsRPCDTOList();
        if (ObjUtils.isNotEmpty(taskDetailsRPCDTOList)) {
            List<VouchersTaskDetailsDTO> taskDetailsDTOList = getVouchersTaskDetailsDTOS(vouchersTaskCreateDTO, voucherSourceType, taskDetailsRPCDTOList);
            vouchersTaskCreateDTO.setVouchersTaskDetailsDTOList(taskDetailsDTOList);
        }
        String voucherTaskId = vouchersTaskCreateDTO.getVouchersTaskId();
        VouchersTaskResponseRPCDTO taskResponseRPCDTO = new VouchersTaskResponseRPCDTO(vouchersTaskRPCDTO.getBizNo(), voucherTaskId);
        createGrantTask(vouchersTaskCreateDTO, vouchersTaskRPCDTO.getIsStart(), ObjUtils.isNotEmpty(taskDetailsRPCDTOList) ? taskDetailsRPCDTOList.size() : 0);
        return taskResponseRPCDTO;
    }

    /**
     * 校验当前企业预算配置及参数
     * @param tasksDTO
     * @param voucherTaskType
     * @return
     */
    private VoucherCostStatusEnum checkCostConfig(VouchersTaskRPCDTO tasksDTO, Integer voucherTaskType) {
        if (VoucherTaskType.EXAMINE_APPROVE_GRANT.getValue() == voucherTaskType) {
            if (StringUtils.isNotBlank(tasksDTO.getCostId())) {
                //审批发放，预算已经占用成功
                return VoucherCostStatusEnum.SUCCESS_OCCUPY;
            }
            return VoucherCostStatusEnum.NOT_OCCUPY;
        }

        if (VoucherTaskType.getCostAttributionType().contains(voucherTaskType)){
            boolean voucherOccupyBudget = voucherCommonUtils.queryCostConfig(tasksDTO.getCompanyId());
            if (voucherOccupyBudget) {
                if (StringUtils.isEmpty(tasksDTO.getCostInfo())) {
                    throw new FinPayException(GlobalResponseCode.VOUCHER_PAYMENT_PARAM_ERROR_COST);
                }
                if (StringUtils.isEmpty(tasksDTO.getDateOfExpense())) {
                    throw new FinPayException(GlobalResponseCode.VOUCHER_PAYMENT_DATE_ERROR_COST);
                }
                return VoucherCostStatusEnum.NEED_OCCUPY;
            } else {
                return VoucherCostStatusEnum.NOT_OCCUPY;
            }
        }
        return VoucherCostStatusEnum.NOT_OCCUPY;
    }

    @Override
    public VouchersTaskResponseRPCDTO addVouchersGrantTaskDetails(VouchersTaskDetailsRPCDTO vouchersTaskDetailsRPCDTO, String voucherTaskId, String companyId, Boolean isStart, Integer voucherSourceType) throws FinhubException {
        if (ObjUtils.isEmpty(vouchersTaskDetailsRPCDTO)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        ValidateUtils.validate(vouchersTaskDetailsRPCDTO);
        VouchersTask vouchersTask = vouchersTaskHandleService.getVouchersTaskById(voucherTaskId, companyId);
        if (null == vouchersTask) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "券发放任务不存在");
        }
        try {
            Map<String, EmployeeOrgUnitDTO> employeeInfoMap = employeeService.getEmployeeInfoMap(vouchersTask.getCompanyId(), Lists.newArrayList(vouchersTaskDetailsRPCDTO.getEmployeeId()));
            Company company = iCompanyService.queryCompanyById(companyId);
            if(ObjUtils.isNull(company)){
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "参数错误：companyId未查到公司信息");
            }
            String companyName = company.getName();
            VouchersTaskDetailsDTO taskDetailsDTO = new VouchersTaskDetailsDTO();
            taskDetailsDTO.setCompanyId(companyId);
            taskDetailsDTO.setCompanyName(companyName);
            taskDetailsDTO.buildDTO(vouchersTaskDetailsRPCDTO, vouchersTask, voucherSourceType, employeeInfoMap);
            ValidateUtils.validate(taskDetailsDTO);
            String nonExistentEmployeeId = vouchersTaskHandleService.addGrantTaskDetails(taskDetailsDTO);
            if (isStart) {
                //启动发放任务
                vouchersTaskHandleService.startGrantTask(voucherTaskId, companyId);
            }
            List<String> nonExistentEmployees = new ArrayList<>();
            if (nonExistentEmployeeId != null) {
                nonExistentEmployees.addAll(nonExistentEmployees);
            }
            return new VouchersTaskResponseRPCDTO(vouchersTaskDetailsRPCDTO.getBizNo(), taskDetailsDTO.getVouchersTaskDetailsId(), nonExistentEmployees);
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public VouchersTaskDetailsRespRPCDTO addVouchersGrantTaskDetails(List<VouchersTaskDetailsRPCDTO> vouchersTaskDetailsRPCDTOList, String voucherTaskId, String companyId, Boolean isStart, Integer voucherSourceType) throws FinhubException {
        VouchersTask vouchersTask = vouchersTaskHandleService.getVouchersTaskById(voucherTaskId, companyId);
        if (null == vouchersTask) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "券发放任务不存在");
        }
        List<VouchersTaskResponseRPCDTO> result = new ArrayList<>();
        List<String> nonExistentEmployees = new ArrayList<>();
        //封装List
        List<VouchersTaskDetailsDTO> vouchersTaskDetailsDTOS = getVoucherTaskDetailList(vouchersTaskDetailsRPCDTOList, voucherSourceType, vouchersTask, result, nonExistentEmployees);
        vouchersTaskHandleService.batchAddGrantTaskDetails(vouchersTaskDetailsDTOS);
        if (isStart) {
            //启动发放任务
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    // 事务提交完毕时，触发
                    vouchersTaskHandleService.startGrantTask(voucherTaskId, companyId);
                }
            });
        }
        return new VouchersTaskDetailsRespRPCDTO(result, nonExistentEmployees);
    }

    @Override
    public boolean startTask(String voucherTaskId, String companyId) {
        return vouchersTaskHandleService.startGrantTask(voucherTaskId, companyId);
    }

    @Override
    public boolean cancelTask(String voucherTaskId, String companyId) {
        return vouchersTaskHandleService.cancelTask(voucherTaskId, companyId);
    }

    @Override
    public VouchersTaskResponseRPCDTO createGrantTaskByVouchersTemplet(VouchersTaskCreateRPCDTO taskCreateRPCDTO) throws FinhubException {
        ValidateUtils.validate(taskCreateRPCDTO);
        String companyId = taskCreateRPCDTO.getCompanyId();
        String voucherTempletId = taskCreateRPCDTO.getVoucherTempletId();
        Integer voucherSourceType = taskCreateRPCDTO.getVoucherSourceType();
        VouchersTemplet vouchersTemplet = vouchersTempletService.getVouchersTempletById(voucherTempletId);
        if (vouchersTemplet == null) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        //里程补贴发券不支持先票发券（4.9.2 取消此规则）
        Integer writeInvoiceType = vouchersTemplet.getWriteInvoiceType();
        //当前不支持单次券先开票
        if (WriteInvoiceType.isAdvance(writeInvoiceType) && VoucherModelEnum.isConsumptionOne(vouchersTemplet.getVoucherModel())) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_TEMPLET_ADVANCE_INVOICE_ONE_ERROR);
        }
        if (!vouchersTemplet.getCompanyId().equals(companyId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        //分贝券类型是否需要费控
        VoucherCostStatusEnum voucherCostStatusEnum = null;
        if (VoucherTaskType.getCostAttributionType().contains(taskCreateRPCDTO.getVoucherTaskType())) {
            if (voucherCommonUtils.queryCostConfig(taskCreateRPCDTO.getCompanyId())) {
                //公司配置了管控
                if (StringUtils.isBlank(taskCreateRPCDTO.getCostInfo())) {
                    throw new FinPayException(GlobalResponseCode.VOUCHER_PAYMENT_PARAM_ERROR_COST);
                }
                if (StringUtils.isBlank(taskCreateRPCDTO.getDateOfExpense())) {
                    throw new FinPayException(GlobalResponseCode.VOUCHER_PAYMENT_DATE_ERROR_COST);
                }
                voucherCostStatusEnum = VoucherCostStatusEnum.NEED_OCCUPY;
            } else {
                voucherCostStatusEnum = VoucherCostStatusEnum.NOT_OCCUPY;
            }
        } else {
            voucherCostStatusEnum = VoucherCostStatusEnum.NOT_OCCUPY;
        }

        //校验当前企业券是否可先开票
        checkInvoiceAdvancePrivilege(companyId, writeInvoiceType);
        //封装主任务
        VouchersTaskCreateDTO taskCreateDTO = new VouchersTaskCreateDTO();
        taskCreateDTO.buildDTO(taskCreateRPCDTO, vouchersTemplet);
        taskCreateDTO.setCostStatus(voucherCostStatusEnum.getStatus());
        //封装任务详情
        /*if (vouchersTemplet.getVoucherTermType() != 1) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }*/
        List<VouchersTaskDetailsCreateRPCDTO> taskDetailsCreateRPCDTOS = taskCreateRPCDTO.getTaskDetailsCreateRPCDTOS();
        Set<BigDecimal> voucherDenominationSet = new HashSet<>();
        //查询当前企业分贝券是否离职回收
        int voucherRecoveryType = iCompanyService.queryFbqRecycleType(companyId);
        if (ObjUtils.isNotEmpty(taskDetailsCreateRPCDTOS)) {
            List<VouchersTaskDetailsDTO> taskDetailsDTOS = new ArrayList<>();
            List<String> employeeIds = taskDetailsCreateRPCDTOS.stream().map(VouchersTaskDetailsCreateRPCDTO::getEmployeeId).collect(Collectors.toList());
            Map<String, EmployeeOrgUnitDTO> employeeInfoMap = employeeService.getEmployeeInfoMap(companyId, employeeIds);
            Company company = iCompanyService.queryCompanyById(companyId);
            if(ObjUtils.isNull(company)){
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "参数错误：companyId未查到公司信息");
            }
            String companyName = company.getName();
            taskDetailsCreateRPCDTOS.forEach(taskDetailsRPCDTO -> {
                ValidateUtils.validate(taskDetailsRPCDTO);
                VouchersTaskDetailsDTO taskDetailsDTO = new VouchersTaskDetailsDTO();
                taskDetailsDTO.setCompanyId(companyId);
                taskDetailsDTO.setCompanyName(companyName);
                taskDetailsDTO.buildDtoByTemplet(taskDetailsRPCDTO, taskCreateDTO, vouchersTemplet, voucherSourceType, employeeInfoMap, voucherRecoveryType);
                //发放明细参数校验
                try {
                    ValidateUtils.validate(taskDetailsDTO);
                    taskDetailsDTOS.add(taskDetailsDTO);
                    voucherDenominationSet.add(taskDetailsRPCDTO.getVoucherDenomination());
                } catch (Exception e) {
                    FinhubLogger.error("【创建发券任务】发券明细信息不全,taskDetailsDTO:{}", JSONObject.toJSONString(taskDetailsDTO), e);
                }
            });
            taskCreateDTO.setVouchersTaskDetailsDTOList(taskDetailsDTOS);
        }
        VouchersTempletRPCDTO vouchersInfo = getVouchersInfo(vouchersTemplet, voucherDenominationSet);
        taskCreateDTO.setVouchersInfo(vouchersInfo);
        ValidateUtils.validate(taskCreateDTO);
        String vouchersTaskId = taskCreateDTO.getVouchersTaskId();
        VouchersTaskResponseRPCDTO taskResponseRPCDTO = new VouchersTaskResponseRPCDTO(taskCreateRPCDTO.getBizNo(), vouchersTaskId);
        //判断是否启动任务，如果任务明细大于200条你默认不启动，等待唤醒
        createGrantTask(taskCreateDTO, taskCreateRPCDTO.getIsStart(), ObjUtils.isNotEmpty(taskCreateDTO.getVouchersTaskDetailsDTOList()) ? taskCreateDTO.getVouchersTaskDetailsDTOList().size() : 0);
        return taskResponseRPCDTO;
    }

    @Override
    public VouchersTaskDetailsRespRPCDTO addGrantTaskDetailsByVouchersTemplet(List<VouchersTaskDetailsCreateRPCDTO> taskDetailsCreateRPCDTOS, String voucherTaskId, String companyId, Boolean isStart, Integer voucherSourceType) throws FinhubException {
        VouchersTask vouchersTask = vouchersTaskHandleService.getVouchersTaskById(voucherTaskId, companyId);
        if (null == vouchersTask) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "券发放任务不存在");
        }
        //封装任务详情
        List<String> collect = taskDetailsCreateRPCDTOS.stream().map(VouchersTaskDetailsCreateRPCDTO::getEmployeeId).collect(Collectors.toList());
        Map<String, EmployeeOrgUnitDTO> employeeInfoMap = employeeService.getEmployeeInfoMap(vouchersTask.getCompanyId(), collect);
        Company company = iCompanyService.queryCompanyById(companyId);
        if(ObjUtils.isNull(company)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "参数错误：companyId未查到公司信息");
        }
        String companyName = company.getName();
        List<VouchersTaskDetailsDTO> taskDetailsDTOS = new ArrayList<>();
        List<VouchersTaskResponseRPCDTO> result = new ArrayList<>();
        List<String> nonExistentEmployees = new ArrayList<>();
        taskDetailsCreateRPCDTOS.forEach(taskDetailsRPCDTO -> {
            ValidateUtils.validate(taskDetailsRPCDTO);
            VouchersTaskDetailsDTO taskDetailsDTO = new VouchersTaskDetailsDTO();
            taskDetailsDTO.setCompanyId(companyId);
            taskDetailsDTO.setCompanyName(companyName);
            taskDetailsDTO.buildDtoByTemplet(taskDetailsRPCDTO, vouchersTask, voucherSourceType, employeeInfoMap);
            //发放明细参数校验
            try {
                ValidateUtils.validate(taskDetailsDTO);
                taskDetailsDTOS.add(taskDetailsDTO);
                result.add(new VouchersTaskResponseRPCDTO(taskDetailsRPCDTO.getBizNo(), taskDetailsDTO.getVouchersTaskDetailsId()));
            } catch (Exception e) {
                nonExistentEmployees.add(taskDetailsRPCDTO.getEmployeeId());
                FinhubLogger.error("【创建发券任务】发券明细信息不全,taskDetailsDTO:{}", JSONObject.toJSONString(taskDetailsDTO), e);
            }
        });
        //封装List
        vouchersTaskHandleService.batchAddGrantTaskDetails(taskDetailsDTOS);
        if (isStart) {
            //启动发放任务
            vouchersTaskHandleService.startGrantTask(voucherTaskId, companyId);
        }
        return new VouchersTaskDetailsRespRPCDTO(result, nonExistentEmployees);
    }

    @Override
    public VouchersExternalDetailsRpcResDTO addTaskDetailsWithCheck(VouchersExternalRpcReqDTO reqDTO) {
        VouchersTask vouchersTask = vouchersTaskHandleService.getVouchersTaskById(reqDTO.getVoucherTaskId(), reqDTO.getCompanyId());
        if (null == vouchersTask) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "券发放任务不存在");
        }
        Company company = iCompanyService.queryCompanyById(reqDTO.getCompanyId());
        if(ObjUtils.isNull(company)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "参数错误：companyId未查到公司信息");
        }
        //封装任务详情
        List<String> employeeIds = reqDTO.getTaskDetails().stream().map(detail-> detail.getEmployeeId()).collect(Collectors.toList());
        Map<String, EmployeeOrgUnitDTO> employeeInfoMap = employeeService.getEmployeeInfoMap(vouchersTask.getCompanyId(), employeeIds);List<VouchersTaskDetailsDTO> taskDetailsDTOS = new ArrayList<>();
        List<VouchersTaskResponseRPCDTO> result = new ArrayList<>();
        List<String> nonExistentEmployees = new ArrayList<>();
        for(VouchersExternalRpcReqDTO.TaskDetail taskDetail:reqDTO.getTaskDetails()){
            String employeeId = taskDetail.getEmployeeId();
            EmployeeOrgUnitDTO employeeOrgUnit = employeeInfoMap.get(employeeId);
            //离职员工
            if(null == employeeOrgUnit){
                FinhubLogger.info("【发券任务明细】员工离职employeeId:{}", employeeId);
                nonExistentEmployees.add(taskDetail.getEmployeeId());
                continue;
            }
            //禁用员工
            if (employeeOrgUnit.getStatus() == EmployeeStatus.INACTIVE.getKey()) {
                FinhubLogger.info("【发券任务明细】员工禁用employeeId:{}", employeeId);
                nonExistentEmployees.add(taskDetail.getEmployeeId());
                continue;
            }
            try {
                VouchersTaskDetailsDTO taskDetailsDTO = new VouchersTaskDetailsDTO();
                taskDetailsDTO.setCompanyId(company.getId());
                taskDetailsDTO.setCompanyName(company.getName());
                VouchersTaskDetailsCreateRPCDTO taskDetailDTO = new VouchersTaskDetailsCreateRPCDTO();
                BeanUtil.copyProperties(taskDetail, taskDetailDTO, CopyOptions.create().setIgnoreError(true));
                taskDetailsDTO.buildDtoByTemplet(taskDetailDTO, vouchersTask, reqDTO.getVoucherSourceType(), employeeInfoMap);
                ValidateUtils.validate(taskDetailsDTO);
                taskDetailsDTOS.add(taskDetailsDTO);
                result.add(new VouchersTaskResponseRPCDTO(taskDetail.getBizNo(), taskDetailsDTO.getVouchersTaskDetailsId()));
            } catch (Exception e) {
                nonExistentEmployees.add(taskDetail.getEmployeeId());
                FinhubLogger.error("【发券任务明细】发券明细信息不全,taskDetail:{}", JSONObject.toJSONString(taskDetail), e);
            }
        }
        //封装List
        vouchersTaskHandleService.batchAddGrantTaskDetails(taskDetailsDTOS);
        return VouchersExternalDetailsRpcResDTO.builder().vouchersTaskResponseRPCDTOList(result).nonExistentEmployees(nonExistentEmployees).build();
    }

    //TODO =====================================================private===========================================================
    private void checkInvoiceAdvancePrivilege(String companyId, Integer writeInvoiceType) {
        List<CompanyRuleTypeDTO> ruleTypeDTOS = iCompanyService.queryCompanyRuleByType(companyId, Lists.newArrayList(CompanyRuleType.CompanyBeforehandInvoiceType));
        if (ObjUtils.isEmpty(ruleTypeDTOS)) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_ADVANCE_INVOICE_AUTO);
        }
        CompanyRuleTypeDTO typeDTO = ruleTypeDTOS.get(0);
        if (typeDTO.getState() == CoreConstant.NO && writeInvoiceType != null && WriteInvoiceType.isAdvance(writeInvoiceType)) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_ADVANCE_INVOICE_AUTO);
        }
    }


    private List<VouchersTaskDetailsDTO> getVoucherTaskDetailList(List<VouchersTaskDetailsRPCDTO> vouchersTaskDetailsRPCDTOList, Integer voucherSourceType, VouchersTask vouchersTask, List<VouchersTaskResponseRPCDTO> result, List<String> nonExistentEmployees) {
        List<VouchersTaskDetailsDTO> vouchersTaskDetailsDTOS = new ArrayList<>();
        List<String> collect = vouchersTaskDetailsRPCDTOList.stream().map(VouchersTaskDetailsRPCDTO::getEmployeeId).collect(Collectors.toList());
        String companyId = vouchersTask.getCompanyId();
        Map<String, EmployeeOrgUnitDTO> employeeInfoMap = employeeService.getEmployeeInfoMap(companyId, collect);
        Company company = iCompanyService.queryCompanyById(companyId);
        if(ObjUtils.isNull(company)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "参数错误：companyId未查到公司信息");
        }
        String companyName = company.getName();
        for (VouchersTaskDetailsRPCDTO taskDetailsRPCDTO : vouchersTaskDetailsRPCDTOList) {
            ValidateUtils.validate(taskDetailsRPCDTO);
            //当前不支持单次券先开票
            if (WriteInvoiceType.isAdvance(taskDetailsRPCDTO.getWriteInvoiceType()) && VoucherModelEnum.isConsumptionOne(taskDetailsRPCDTO.getVoucherModel())) {
                throw new FinPayException(GlobalResponseCode.VOUCHER_TEMPLET_ADVANCE_INVOICE_ONE_ERROR);
            }
            VouchersTaskDetailsDTO taskDetailsDTO = new VouchersTaskDetailsDTO();
            taskDetailsDTO.setCompanyId(companyId);
            taskDetailsDTO.setCompanyName(companyName);
            taskDetailsDTO.buildDTO(taskDetailsRPCDTO, vouchersTask, voucherSourceType, employeeInfoMap);
            try {
                ValidateUtils.validate(taskDetailsDTO);
                vouchersTaskDetailsDTOS.add(taskDetailsDTO);
                result.add(new VouchersTaskResponseRPCDTO(taskDetailsRPCDTO.getBizNo(), taskDetailsDTO.getVouchersTaskDetailsId()));
            } catch (Exception e) {
                nonExistentEmployees.add(taskDetailsRPCDTO.getEmployeeId());
                FinhubLogger.error("【创建发券任务】发券明细信息不全,taskDetailsDTO:{}", JSONObject.toJSONString(taskDetailsDTO), e);
            }
        }
        return vouchersTaskDetailsDTOS;
    }

    private List<VouchersTaskDetailsDTO> getVouchersTaskDetailsDTOS(VouchersTaskCreateDTO vouchersTaskCreateDTO, Integer voucherSourceType, List<VouchersTaskDetailsRPCDTO> taskDetailsRPCDTOList) {
        List<VouchersTaskDetailsDTO> taskDetailsDTOList = new ArrayList<>();
        String companyId = vouchersTaskCreateDTO.getCompanyId();
        List<String> employeeIds = taskDetailsRPCDTOList.stream().map(VouchersTaskDetailsRPCDTO::getEmployeeId).collect(Collectors.toList());
        Map<String, EmployeeOrgUnitDTO> employeeInfoMap = employeeService.getEmployeeInfoMap(companyId, employeeIds);
        //查询当前企业分贝券是否离职回收
        int voucherRecoveryType = iCompanyService.queryFbqRecycleType(companyId);
        Company company = iCompanyService.queryCompanyById(companyId);
        if(ObjUtils.isNull(company)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "参数错误：companyId未查到公司信息");
        }
        String companyName = company.getName();
        taskDetailsRPCDTOList.forEach(taskDetailsRPCDTO -> {
            //当前不支持单次券先开票
            if (WriteInvoiceType.isAdvance(taskDetailsRPCDTO.getWriteInvoiceType()) && VoucherModelEnum.isConsumptionOne(taskDetailsRPCDTO.getVoucherModel())) {
                throw new FinPayException(GlobalResponseCode.VOUCHER_TEMPLET_ADVANCE_INVOICE_ONE_ERROR);
            }
            VouchersTaskDetailsDTO taskDetailsDTO = new VouchersTaskDetailsDTO();
            taskDetailsDTO.setCompanyName(companyName);
            taskDetailsDTO.setCompanyId(companyId);
            taskDetailsDTO.buildDTO(taskDetailsRPCDTO, vouchersTaskCreateDTO, voucherSourceType, employeeInfoMap, voucherRecoveryType);
            //发放明细参数校验
            try {
                ValidateUtils.validate(taskDetailsDTO);
                taskDetailsDTOList.add(taskDetailsDTO);
            } catch (Exception e) {
                FinhubLogger.error("【创建发券任务】发券明细信息不全,taskDetailsDTO:{}", JSONObject.toJSONString(taskDetailsDTO), e);
            }
        });
        return taskDetailsDTOList;
    }


    private VouchersTempletRPCDTO getVouchersInfo(VouchersTemplet vouchersTemplet, Set<BigDecimal> voucherDenominationSet) {
        VouchersTempletRPCDTO vouchersInfo = new VouchersTempletRPCDTO();
        BeanUtils.copyProperties(vouchersTemplet, vouchersInfo);
        String termValidity = vouchersTemplet.getVoucherTermValidity();
        if (voucherDenominationSet != null && voucherDenominationSet.size() == 1) {
            vouchersInfo.setVoucherDenomination(voucherDenominationSet.iterator().next());
        }
        vouchersInfo.setVoucherEffectiveDays(VoucherTermTypeEnum.getVoucherEffectiveDays(vouchersTemplet.getVoucherTermType(), vouchersTemplet.getVoucherTermValidity()));
        if (vouchersTemplet.getVoucherTermType() == VoucherTermTypeEnum.TERM_OF_DAY_HOUR_MINUTE.getValue()) {
            JSONObject jsb = JSONObject.parseObject(vouchersTemplet.getVoucherTermValidity());
            vouchersInfo.setVoucherEffectiveHours(jsb.getInteger("hour"));
            vouchersInfo.setVoucherEffectiveMinutes(jsb.getInteger("minute"));
        }
        vouchersInfo.setVoucherDenominationSet(voucherDenominationSet);
        vouchersInfo.setVoucherEffectiveTime(VoucherTermTypeEnum.getVoucherEffectiveTime(vouchersTemplet.getVoucherTermType(), termValidity));
        vouchersInfo.setVoucherExpiryTime(VoucherTermTypeEnum.getVoucherExpiryTime(vouchersTemplet.getVoucherTermType(), termValidity));
        if (VoucherTermTypeEnum.TERM_OF_DAYS.getValue() == vouchersTemplet.getVoucherTermType()) {
            vouchersInfo.setVoucherEffectiveDays(Integer.valueOf(termValidity == null ? "0" : termValidity));
        }
        List<String> voucherTypeList = JSONArray.parseArray(vouchersTemplet.getVoucherTypeList(), String.class);
        vouchersInfo.setVoucherTypeList(voucherTypeList);
        if (ObjUtils.isNotEmpty(voucherTypeList)) {
            List<String> voucherTypeNames = vouchersTypeService.getVouchersTypeNames(voucherTypeList);
            vouchersInfo.setVoucherTypeNames(voucherTypeNames);
        }
        return vouchersInfo;
    }

    private void createGrantTask(VouchersTaskCreateDTO vouchersTaskCreateDTO, Boolean isStart, int size) {
        vouchersTaskHandleService.createVouchersTask(vouchersTaskCreateDTO);
        //判断是否启动任务，如果任务明细大于200条你默认不启动，等待唤醒
        if (isStart && size <= MAX_DETAILS_SIZE) {
            //启动发放任务
            vouchersTaskHandleService.startGrantTask(vouchersTaskCreateDTO.getVouchersTaskId(), vouchersTaskCreateDTO.getCompanyId());
        }
    }
}
