package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayStatus;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.PublicPayResult;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponOperationType;
import com.fenbeitong.fenbeipay.api.model.ResultDTO;
import com.fenbeitong.fenbeipay.api.model.dto.SaturnCashierOrRefundRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.*;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierSearchOrderService;
import com.fenbeitong.fenbeipay.awplus.service.personpayservice.PersonPayRecordService;
import com.fenbeitong.fenbeipay.awplus.service.personrefundservice.PersonRefundService;
import com.fenbeitong.fenbeipay.cashier.manager.CashierOrderSettlementPayManager;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.paycenter.PayStatus;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPay;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponFlow;
import com.fenbeitong.fenbeipay.redcoupon.service.AccountRedcouponService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;

import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;


@Service("iCashierSearchOrderService")
public class ICashierSearchOrderServiceImpl implements ICashierSearchOrderService {

    @Autowired
    private CashierSearchOrderService cashierSearchOrderService;
    @Autowired
    private AccountRedcouponService accountRedcouponService;

    @Autowired
    private PersonPayRecordService personPayRecordService;
    @Autowired
    private PersonRefundService personRefundService;

    @Autowired
    private CashierOrderSettlementPayManager cashierOrderSettlementPayManager;

   private  static final  List<Integer> paySuccess = Lists.newArrayList(CashierPayStatus.PAY_FBQ_SUCCESS.getKey(),
            CashierPayStatus.PAY_FBB_SUCCESS.getKey(), CashierPayStatus.PAY_THIRDPART_SUCCESS.getKey(),
            CashierPayStatus.PAY_COMPANY_SUCCESS.getKey(),CashierPayStatus.PAY_REDCOUPON_SUCCESS.getKey(),
            CashierPayStatus.PAY_BANK_SUCCESS.getKey(),CashierPayStatus.PAY_ACCT_PUBLIC_SUCCESS.getKey(),CashierPayStatus.PAY_BANK_CARD_SUCCESS.getKey());

    /**
     * 查询一单多次交易汇总明细
     * @param cashierMultipleQueryReqRPCVo
     * @return
     */
    @Override
    public CashierMultipleTradeListRPCDTO queryCashierMultipleSettlementList(CashierMultipleQueryReqRPCVo cashierMultipleQueryReqRPCVo) {
        ValidateUtils.validate(cashierMultipleQueryReqRPCVo);
        return cashierSearchOrderService.queryCashierMultipleSettlementList(cashierMultipleQueryReqRPCVo);
    }

    /**
     * 查询一单多次支付各项汇总金额
     * @param cashierMultipleQueryReqRPCVo
     * @return
     */
    @Override
    public CashierSearchMultipleTradeRPCDTO queryCashierMultipleDistinctFbOrderIdDetail(CashierMultipleQueryReqRPCVo cashierMultipleQueryReqRPCVo) {
        ValidateUtils.validate(cashierMultipleQueryReqRPCVo);
        return cashierSearchOrderService.queryCashierMultipleDistinctFbOrderIdDetail(cashierMultipleQueryReqRPCVo);
    }

    /**
     * 根据场景分页查询支付列表
     * @param queryReqRPCVo
     * @return
     */
    @Override
    public ResponsePage<CashierSearchMultipleTradeRPCDTO> batchQueryByPage(CashierSearchBatchPageReqRPCVo queryReqRPCVo) {
        ValidateUtils.validate(queryReqRPCVo);
        return cashierSearchOrderService.searchMultipleBatchByPage(queryReqRPCVo);
    }

    @Override
    public ResultDTO<CashierSearchPayStatusRPCDTO> queryPublicPayOrderResult(CashierPayDetailRPCVo req) {
        try {
            PublicPayResult payResult = cashierSearchOrderService.queryPublicPayOrderResult(req.getFbOrderId());

            CashierSearchPayStatusRPCDTO res = new CashierSearchPayStatusRPCDTO();
            res.setFbOrderId(req.getFbOrderId());
            res.setCashierPayStatus(payResult.getCode());

            ResultDTO<CashierSearchPayStatusRPCDTO> resultDTO = new ResultDTO<>();
            resultDTO.setSuccess(true);
            resultDTO.setModel(res);
            FinhubLogger.info("queryPublicPayOrderResult result, {}", resultDTO);
            return resultDTO;
        } catch (Exception e) {
            FinhubLogger.error("error while queryPublicPayOrderResult, {}", e.getMessage(), e);
            ResultDTO<CashierSearchPayStatusRPCDTO> resultDTO = new ResultDTO<>();
            resultDTO.setSuccess(false);
            resultDTO.setErrMsg(e.getMessage());
            resultDTO.setErrCode("" + ((e instanceof FinhubException) ? ((FinhubException) e).getCode() : GlobalResponseCode.EXCEPTION.getCode()));
            return resultDTO;
        }
    }

    @Override
    public CashierPayDetail4MainOrderRPCDTO searchPayDetail4MainOrder(CashierPayDetail4MainOrderRPCVO cashierPayDetailRPCVo) {
        return cashierSearchOrderService.searchCashierPayDetail4MainOrder(cashierPayDetailRPCVo);
    }


    /**
     * 一单多交易详情接口
     * @param cashierSearchDetailReqRPCVo
     * @return CashierSearchMultipleTradeDetailRPCDTO
     */
    @Override
    public CashierSearchMultipleTradeDetailRPCDTO searchMultiplePayDetail(CashierSearchDetailReqRPCVo cashierSearchDetailReqRPCVo) {
        ValidateUtils.validate(cashierSearchDetailReqRPCVo);
        try {
            return cashierSearchOrderService.searchMultiplePayDetailGroupByFbOrderId(cashierSearchDetailReqRPCVo);
        }catch (FinPayException e){
            FinhubLogger.warn("【一单多交易详情接口查询】异常，参数：{}", JsonUtils.toJson(cashierSearchDetailReqRPCVo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }catch (Exception e){
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    /**
     * 一单多交易，按照订单Id聚合交易信息，列表查询
     * @param searchBatchOrderIdReqRPCVo
     * @return
     */
    @Override
    public List<CashierSearchMultipleTradeRPCDTO> searchBatchMultiplePayList(CashierSearchBatchOrderIdReqRPCVo searchBatchOrderIdReqRPCVo) {
        if(searchBatchOrderIdReqRPCVo.getFbOrderIds().length<1){
          return null;
        }
        return cashierSearchOrderService.searchBatchMultiplePayListGroupByFbOrderId(searchBatchOrderIdReqRPCVo);
    }

    @Override
    public List<CashierQueryRPCDTO> searchPayDetail(CashierQueryReqRPCVo cashierQueryReqRPCVo) {
        ValidateUtils.validate(cashierQueryReqRPCVo);
        return cashierSearchOrderService.searchPayDetail(cashierQueryReqRPCVo);

    }

    @Override
    public List<CashierBatchQueryRPCDTO> searchBatchPayList(CashierBatchQueryReqRPCVo cashierBatchQueryReqRpcVo) {
        if(cashierBatchQueryReqRpcVo.getFbOrderIds().length<1){
            return null;
        }
        return cashierSearchOrderService.searchBatchPayList(cashierBatchQueryReqRpcVo);
    }

    @Override
    public CashierPayDetailRPCDTO searchPayDetail(CashierPayDetailRPCVo cashierPayDetailRPCVo) {
        ValidateUtils.validate(cashierPayDetailRPCVo);
        return cashierSearchOrderService.searchCashierPayDetail(cashierPayDetailRPCVo);
    }

    @Override
    public CashierPayDetailRPCDTO searchPayFlowDetail(CashierPayDetailRPCVo cashierPayDetailRPCVo) {
        ValidateUtils.validate(cashierPayDetailRPCVo);
        CashierPayDetailRPCDTO accountFlow = cashierSearchOrderService.searchCashierPayFlowDetail(cashierPayDetailRPCVo.getFbOrderId());
        redCoupon(cashierPayDetailRPCVo.getFbOrderId(),accountFlow);
        return accountFlow;
    }

    @Override
    public CashierSearchPayStatusRPCDTO searchPayStatus(CashierQueryReqRPCVo cashierQueryReqRPCVo) {
        ValidateUtils.validate(cashierQueryReqRPCVo);
        return cashierSearchOrderService.searchPayStatus(cashierQueryReqRPCVo);
    }

    @Override
    public CashierSearchPayAmountRpcDTO searchPayAmount(CashierQueryReqRPCVo cashierQueryReqRPCVo) {
        ValidateUtils.validate(cashierQueryReqRPCVo);
        return cashierSearchOrderService.searchPayAmount(cashierQueryReqRPCVo);
    }

    @Override
    public CashierPayDetailRPCDTO searchCashierPayDetail(CashierPayDetailRPCVo cashierPayDetailRPCVo) {
        if (ObjUtils.isNull(cashierPayDetailRPCVo) || ObjUtils.isBlank(cashierPayDetailRPCVo.getFbOrderId()) || ObjUtils.isNull(cashierPayDetailRPCVo.getOrderRootType())) {
            return null;
        }
        return cashierSearchOrderService.searchCashierPayDetailByOrderId(cashierPayDetailRPCVo.getFbOrderId(), cashierPayDetailRPCVo.getOrderRootType());
    }

    @Override
    public CashierPayDetailRPCDTO searchAccountPayFlowDetail(CashierPayDetailRPCVo cashierPayDetailRPCVo) {
        return cashierSearchOrderService.searchAccountPayFlowDetail(cashierPayDetailRPCVo);
    }

    @Override
    public List<ThirdPayFlowRespDTO> queryThirdFlowByBizNo(String bizNo, Integer operationType) {
        List<PersonPayRecord> personPayRecords = personPayRecordService.queryPayRecordByFbOrderId(bizNo);
        List<ThirdPayFlowRespDTO> respDTOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(personPayRecords)){
            for (PersonPayRecord personPayRecord: personPayRecords){
                if (PayStatus.isSuccess(personPayRecord.getStatus())){
                    ThirdPayFlowRespDTO thirdPayFlowRespDTO = new ThirdPayFlowRespDTO();
                    BeanUtils.copyProperties(personPayRecord,thirdPayFlowRespDTO);
                    thirdPayFlowRespDTO.setAmount(personPayRecord.getAmount());
                    thirdPayFlowRespDTO.setFbOrderId(personPayRecord.getOrderId());
                    respDTOS.add(thirdPayFlowRespDTO);
                }
            }
        }
        return respDTOS;
    }

    @Override
    public List<ThirdPayFlowRespDTO> queryThirdFlowByReBizNo(String bizNo, String reBizNo, Integer operationType) {
        if (StringUtils.isEmpty(bizNo)){
            return null;
        }
        List<ThirdPayFlowRespDTO> thirdPayFlowRespDTOS = Lists.newArrayList();
        List<PersonRefundRecord> refundRecords = personRefundService.queryRefundRecordByOrderId(bizNo);
        if (CollectionUtils.isNotEmpty(refundRecords)){
            for (PersonRefundRecord personRefundRecord: refundRecords){
                ThirdPayFlowRespDTO thirdPayFlowRespDTO = new ThirdPayFlowRespDTO();
                BeanUtils.copyProperties(personRefundRecord,thirdPayFlowRespDTO);
                thirdPayFlowRespDTO.setAmount((long)personRefundRecord.getRefundAmount());
                thirdPayFlowRespDTO.setFbOrderId(personRefundRecord.getOrderId());
                thirdPayFlowRespDTOS.add(thirdPayFlowRespDTO);
            }
        }
        return thirdPayFlowRespDTOS;
    }


    //======================================Private Method==========================================

    public void redCoupon(String orderId,CashierPayDetailRPCDTO accountFlow){

        List<AccountRedcouponFlow> accountRedcouponFlowList = accountRedcouponService.queryRedcouponFlowByOrderId(orderId);
        BigDecimal amountRedcoupon = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(accountRedcouponFlowList)){
            BigDecimal amountRedcouponConsume = BigDecimal.ZERO;
            BigDecimal amountRedcouponRefund = BigDecimal.ZERO;
            for(AccountRedcouponFlow accountRedcouponFlow : accountRedcouponFlowList){
                if(RedcouponOperationType.PUBLIC_CONSUME.getKey() == accountRedcouponFlow.getOperationType()){
                    amountRedcouponConsume = amountRedcouponConsume.add(accountRedcouponFlow.getOperationAmount());
                }
                if(RedcouponOperationType.PUBLIC_CONSUME_REFUND.getKey() == accountRedcouponFlow.getOperationType()){
                    amountRedcouponRefund = amountRedcouponRefund.add(accountRedcouponFlow.getOperationAmount());
                }
            }
            amountRedcoupon = amountRedcouponConsume.subtract(amountRedcouponRefund);
            accountFlow.setAmountRedcoupon(amountRedcoupon);
        }
    }

    @Override
    public List<CostAttributionRPCDTO> searchCostAttributionByOrderId(String orderId) {
        if(ObjUtils.isBlank(orderId)) {
            return null;
        }
        return cashierSearchOrderService.searchCostAttributionByOrderId(orderId);
    }

    @Override
    public SaturnCashierOrRefundRespRPCDTO searchCashierOrRefundInfo(SaturnCashierOrRefundReqRPCVO saturnCashierOrRefundReqRPCVO){
        return cashierSearchOrderService.searchCashierOrRefundInfo(saturnCashierOrRefundReqRPCVO);
    }

    @Override
    public Boolean checkHasPay(String fbOrderId) {
        List<CashierOrderSettlementPay> cashierPayListByFbOrderId = cashierOrderSettlementPayManager.getCashierPayListByFbOrderId(fbOrderId);
        cashierPayListByFbOrderId = cashierPayListByFbOrderId.stream().filter(cashierOrderSettlementPay -> paySuccess.contains(cashierOrderSettlementPay.getPayStatus())).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(cashierPayListByFbOrderId);
    }
}
