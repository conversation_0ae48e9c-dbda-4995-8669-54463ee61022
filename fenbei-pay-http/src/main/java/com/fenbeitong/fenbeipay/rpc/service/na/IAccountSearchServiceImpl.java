package com.fenbeitong.fenbeipay.rpc.service.na;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.bank.ent.api.account.BankEntAccountInfoService;
import com.fenbeitong.bank.ent.api.account.dto.BankEntAccountInfoDTO;
import com.fenbeitong.bank.ent.api.account.dto.BankEntAccountInfoReqDTO;
import com.fenbeitong.bank.ent.api.account.dto.BankEntAccountInfoRespDTO;
import com.fenbeitong.fenbeipay.acctdech.unit.service.AcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.api.constant.enums.group.MenuAcctEnum;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctOverviewReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctBaseMainRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByComIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctComGwRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountOneStatisticsAmountDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountCompanyBaseRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountStatisticalCreditDTO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSearchService;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.mask.utils.DataMaskUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.fxpay.api.enums.FxCompanyAccountSubType;
import com.fenbeitong.fxpay.api.interfaces.ICompanyAcctService;
import com.fenbeitong.fxpay.api.vo.CompanyAcctRes;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.fxpay.api.vo.acct.CompanyAcctInfoReq;
import com.fenbeitong.pay.search.service.AccountStatisticalService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyNewDto;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service("iAccountSearchService")
public class IAccountSearchServiceImpl implements IAccountSearchService {

    @Autowired
    private AccountStatisticalService accountStatisticalService;

    @Autowired
    protected AcctCompanyGatewayService acctCompanyGatewayService;

    @Autowired
    private UAcctCommonService uAcctCommonService;

    @Autowired
    protected ICompanyService iCompanyService;

    @Autowired
    private BankEntAccountInfoService bankEntAccountInfoService;

    @Autowired
    protected ICompanyAcctService iCompanyAcctService;

    @Override
    public List<AccountStatisticalCreditDTO> accountOneStatisticalCredit(AccountOneStatisticsAmountDTO reqDto) {

        ValidateUtils.validate(reqDto);
        AcctComGwByComIdReqDTO acctComGwByComIdReqDTO = new AcctComGwByComIdReqDTO();
        acctComGwByComIdReqDTO.setCompanyId(reqDto.getCompanyId());
        AcctComGwRespDTO acctComGwRespDTO = acctCompanyGatewayService.findActGwsByComId(acctComGwByComIdReqDTO);
        return  accountStatisticalService.acctStatisticalCredit(reqDto,acctComGwRespDTO);
    }

    @Override
    public List<AccountCompanyBaseRespDTO> queryAllAcctBaseInfo(String companyId, String menuCode) {
        if (StringUtils.isBlank(companyId) || StringUtils.isBlank(menuCode)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "企业ID和菜单code都不能为空");
        }
        MenuAcctEnum menuAcctEnum = MenuAcctEnum.getMenuAcctEnum(menuCode);
        if (menuAcctEnum == null) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "不支持该菜单code");
        }

        List<AccountCompanyBaseRespDTO> respDTOList = new ArrayList<>();
        if (menuAcctEnum.isFinanceAccount()) {
            AcctOverviewReqDTO acctOverviewReqDTO = new AcctOverviewReqDTO();
            acctOverviewReqDTO.setCompanyId(companyId);
            List<AcctBaseMainRespDTO> acctBaseMainRespDTOS = uAcctCommonService.queryMainAcctView(acctOverviewReqDTO);
            acctBaseMainRespDTOS.forEach(acctBaseMainRespDTOItem -> {
                AccountCompanyBaseRespDTO acctCompanyBaseRespDTO = new AccountCompanyBaseRespDTO();
                BeanUtils.copyProperties(acctBaseMainRespDTOItem, acctCompanyBaseRespDTO);
                String companyMainName = acctBaseMainRespDTOItem.getCompanyMainName();
                if (StringUtils.isBlank(companyMainName)) {
                    CompanyNewDto companyNewDto = iCompanyService.queryCompanyNewByCompanyId(acctBaseMainRespDTOItem.getCompanyId());
                    acctCompanyBaseRespDTO.setAccountShowName(companyNewDto.getCompanyName() + " " + acctBaseMainRespDTOItem.getBankMainShowName());
                } else {
                    acctCompanyBaseRespDTO.setAccountShowName(companyMainName + " " + acctBaseMainRespDTOItem.getBankMainShowName());
                }
                respDTOList.add(acctCompanyBaseRespDTO);
            });
        }

        if (menuAcctEnum.isBankEntAcct()) {
            BankEntAccountInfoReqDTO bankEntAccountInfoReqDTO = new BankEntAccountInfoReqDTO();
            bankEntAccountInfoReqDTO.setCompanyId(companyId);
            BankEntAccountInfoRespDTO bankEntAccountInfoRespDTO = bankEntAccountInfoService.queryAccountInfo4Overview(bankEntAccountInfoReqDTO);
            List<BankEntAccountInfoDTO> bankEntAccountInfoDTOS = bankEntAccountInfoRespDTO.getBankEntAccountInfoDTOS();
            if (CollectionUtils.isNotEmpty(bankEntAccountInfoDTOS)) {
                bankEntAccountInfoDTOS.forEach(bankEntAcct -> {
                    AccountCompanyBaseRespDTO acctCompanyBaseRespDTO = new AccountCompanyBaseRespDTO();
                    acctCompanyBaseRespDTO.setCompanyId(bankEntAcct.getCompanyId());
                    acctCompanyBaseRespDTO.setAccountShowName(bankEntAcct.getBankAccountName() + " " + bankEntAcct.getBankName() + "(" + bankEntAcct.getBankAccountNo().substring(bankEntAcct.getBankAccountNo().length() - 4) + ")");
                    acctCompanyBaseRespDTO.setAccountId(bankEntAcct.getAcctInfoId());
                    respDTOList.add(acctCompanyBaseRespDTO);
                });
            }
        }

        // 海外卡账户信息
        if (menuAcctEnum.isFxAcct()) {
            CompanyAcctInfoReq req = new CompanyAcctInfoReq();
            req.setCompanyId(companyId);
            req.setAccountSubType(FxCompanyAccountSubType.PETTY);
            req.setCurrency(CurrencyEnum.USD);
            req.setChannel(FxAcctChannelEnum.AIRWALLEX);
            ResponseVo<List<CompanyAcctRes>> responseVo = iCompanyAcctService.queryCompanyAcctInfo(req);
            FinhubLogger.info("queryAllAcctBaseInfo查询海外卡账户信息req:{},res:{}", JSONObject.toJSONString(req), JSONObject.toJSONString(responseVo));
            if (responseVo.isSuccess()) {
                if (CollectionUtils.isNotEmpty(responseVo.getData())) {
                    CompanyAcctRes companyAcctRes = responseVo.getData().get(0);
                    AccountCompanyBaseRespDTO acctCompanyBaseRespDTO = new AccountCompanyBaseRespDTO();
                    acctCompanyBaseRespDTO.setCompanyId(companyAcctRes.getCompanyId());
                    acctCompanyBaseRespDTO.setAccountShowName(FxAcctChannelEnum.AIRWALLEX.getChannelName() + "(" + DataMaskUtils.bankCard(companyAcctRes.getAccountId()) + ")");
                    acctCompanyBaseRespDTO.setAccountId(companyAcctRes.getAccountId());
                    acctCompanyBaseRespDTO.setAccountModel(CoreConstant.FX_UC_ACCOUNT_MODEL);
                    respDTOList.add(acctCompanyBaseRespDTO);
                }
            }
        }
        return respDTOList;
    }
}
