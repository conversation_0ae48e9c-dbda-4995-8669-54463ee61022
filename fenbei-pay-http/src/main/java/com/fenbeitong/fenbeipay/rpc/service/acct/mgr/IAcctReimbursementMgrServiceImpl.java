package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctGeneralService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctReimbursementService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCreateReimbursementReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.ReimbursementManualCreateAccountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.ReimbursementQueryReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctReimbursementInfo;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.ReimbursementQueryRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctReimbursementMgrService;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctReimbursement;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAcctActStatusEnum;
import com.fenbeitong.finhub.common.constant.FundAcctShowStatusEnum;
import com.fenbeitong.finhub.common.constant.FundAcctStatusEnum;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 员工报销账户管理
 * <AUTHOR>
 * @date 2022/6/6
 */
@Service("iAcctReimbursementMgrService")
public class IAcctReimbursementMgrServiceImpl implements IAcctReimbursementMgrService {
    @Autowired
    UAcctReimbursementService uAcctReimbursementService;
    @Autowired
    UAcctGeneralService uAcctGeneralService;
    @Autowired
    AcctCompanyMainService acctCompanyMainService;
    @Autowired
    private UAcctCommonService uAcctCommonService;
    @Override
    public ReimbursementQueryRespDTO queryReimbursementAccount(ReimbursementQueryReqDTO reimbursementQueryReqDTO) {
        List<AcctReimbursement> acctReimbursements =  uAcctReimbursementService.findEffectiveByCompanyId(reimbursementQueryReqDTO.getCompanyId());
        if (CollectionUtils.isEmpty(acctReimbursements)){
            return new ReimbursementQueryRespDTO();
        }else {
            ReimbursementQueryRespDTO reimbursementQueryRespDTO = new ReimbursementQueryRespDTO();
            reimbursementQueryRespDTO.setCompanyId(reimbursementQueryReqDTO.getCompanyId());
            List<AcctReimbursementInfo> acctReimbursementInfos = new ArrayList<>();
            List<String> companyMainIds = acctReimbursements.stream().filter(e -> !StringUtils.isBlank(e.getCompanyMainId())).map(AcctReimbursement::getCompanyMainId).collect(Collectors.toList());
            List<AcctCompanyMain> byMainIds = acctCompanyMainService.findByMainIds(companyMainIds);
            Map<String, AcctCompanyMain> companyMainMap = byMainIds.stream().collect(Collectors.toMap(AcctCompanyMain::getCompanyMainId, Function.identity(), (key1, key2) -> key2));
            acctReimbursements.forEach(
                    acctReimbursement -> {
                        AcctCompanyMain acctCompanyMain = companyMainMap.get(acctReimbursement.getCompanyMainId());
                        AcctReimbursementInfo acctReimbursementInfo = new AcctReimbursementInfo();
                        acctReimbursementInfo.setAccountId(acctReimbursement.getAccountId());
                        acctReimbursementInfo.setAccountStatus(acctReimbursement.getAccountStatus());
                        acctReimbursementInfo.setBankAccountNo(acctReimbursement.getBankAccountNo());
                        if (BankNameEnum.isCitic(acctReimbursement.getBankName())){
                            acctReimbursementInfo.setAccountRemark("中信银行报销打款限制：\n" +
                                    "打款时间：工作日9:00——17:00；\n" +
                                    "打款限额：单笔限额5w（小于等于）（若收款账户为二类户则附加二类户收款限额）；\n" +
                                    "其他限制：每个个人账户一、二类户每月至多打款2次；");
                        }
                        if (ObjUtils.isBlank(acctCompanyMain)) {
                            acctReimbursementInfo.setCompanyName(acctReimbursement.getCompanyName());
                        } else {
                            /**
                             * ZHIFU-5070 中信账户名称优化
                             * 1.上线之后展示主体名称
                             * 2.白名单内展示主体名称
                             */
                            String showMainName = uAcctCommonService.getShowMainName(acctReimbursement.getBankName(), acctCompanyMain.getBusinessName(), acctReimbursement.getBankAccountNo(), acctCompanyMain);
                            acctReimbursementInfo.setCompanyName(showMainName);
                        }
                        acctReimbursementInfo.setBankName(acctReimbursement.getBankName());
                        acctReimbursementInfos.add(acctReimbursementInfo);
                    }
            );
            reimbursementQueryRespDTO.setReimbursementAccounts(acctReimbursementInfos);
            return reimbursementQueryRespDTO;
        }
    }

    @Override
    public boolean isReimbursementAccountOpened(ReimbursementQueryReqDTO reimbursementQueryReqDTO) {
        List<AcctReimbursement> acctReimbursements =  uAcctReimbursementService.findEffectiveByCompanyId(reimbursementQueryReqDTO.getCompanyId());
        return !CollectionUtils.isEmpty(acctReimbursements);
    }

    @Override
    public List<String> createAccount(ReimbursementManualCreateAccountReqDTO reimbursementManualCreateAccountReqDTO) {

        /*
            平台方	中信银行
            账户类型	充值账户
            开户主体	企业主体
        */
        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByBankNameAndCompanyMainType(reimbursementManualCreateAccountReqDTO.getBankName(),reimbursementManualCreateAccountReqDTO.getCompanyMainType());
        if (CollectionUtils.isEmpty(accountGenerals)){
            return null;
        }
        if (!reimbursementManualCreateAccountReqDTO.getUpgradeAllFlag()){
            accountGenerals = accountGenerals.stream().filter(accountGeneral ->
                    reimbursementManualCreateAccountReqDTO.getCompanyIds().contains(accountGeneral.getCompanyId())
            ).collect(Collectors.toList());
        }
        List<String> companyIds = Lists.newArrayList();
        for (AccountGeneral accountGeneral:accountGenerals){
            String companyId = createSingleAccount(accountGeneral);
            companyIds.add(companyId);
        }

        return companyIds;
    }

    /**
     * 单一创建报销账户请求
     * @param accountGeneral 余额账户
     */
    private String createSingleAccount(AccountGeneral accountGeneral){
        AcctReimbursement acctReimbursement = uAcctReimbursementService.findCompanyIdAndBank(accountGeneral.getCompanyId(),accountGeneral.getBankName(),accountGeneral.getBankAccountNo());
        if (acctReimbursement!=null){
            return acctReimbursement.getCompanyId();
        }
        AcctCreateReimbursementReqDTO acctCreateReimbursementReqDTO = new AcctCreateReimbursementReqDTO();
        acctCreateReimbursementReqDTO.setBankName(accountGeneral.getBankName());
        acctCreateReimbursementReqDTO.setOperationChannel(OperationChannelType.WEB.getKey());
        acctCreateReimbursementReqDTO.setBizNo(accountGeneral.getBankAccountNo());
        acctCreateReimbursementReqDTO.setAccountModel(accountGeneral.getAccountModel());
        acctCreateReimbursementReqDTO.setBankAccountNo(accountGeneral.getBankAccountNo());
        acctCreateReimbursementReqDTO.setBankName(accountGeneral.getBankName());
        acctCreateReimbursementReqDTO.setBankAcctId(accountGeneral.getBankAcctId());
        acctCreateReimbursementReqDTO.setCompanyMainType(accountGeneral.getCompanyMainType());
        acctCreateReimbursementReqDTO.setAccountGeneralId(accountGeneral.getAccountGeneralId());
        acctCreateReimbursementReqDTO.setCompanyId(accountGeneral.getCompanyId());
        acctCreateReimbursementReqDTO.setCompanyMainId(accountGeneral.getCompanyMainId());
        acctCreateReimbursementReqDTO.setCompanyName(accountGeneral.getCompanyName());
        acctCreateReimbursementReqDTO.setCompanyModel(accountGeneral.getCompanyModel());
        acctCreateReimbursementReqDTO.setAccountStatus(FundAcctStatusEnum.UN_ABLE.getStatus());
        acctCreateReimbursementReqDTO.setActiveStatus(FundAcctActStatusEnum.ACTIVATE.getStatus());
        acctCreateReimbursementReqDTO.setShowStatus(FundAcctShowStatusEnum.UN_SHOW.getStatus());
        acctCreateReimbursementReqDTO.setOperationUserId("QA");
        acctCreateReimbursementReqDTO.setOperationUserName("hl");
        acctCreateReimbursementReqDTO.setOperationDescription("存量客户默认创建");
        acctCreateReimbursementReqDTO.setOperationChannel(OperationChannelType.SYSTEM.getKey());
        AcctReimbursement reimbursement =  uAcctReimbursementService.establishAccount(acctCreateReimbursementReqDTO);
        return reimbursement.getCompanyId();
    }
}
