package com.fenbeitong.fenbeipay.rpc.service.acct.flow;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbei.settlement.base.dto.BasePageDTO;
import com.fenbeitong.fenbei.settlement.external.api.dto.BillSummaryDetailDTO;
import com.fenbeitong.fenbei.settlement.external.api.query.BillSummaryDetailQuery;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.AcctBusinessDebitFlowExtMapper;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.AcctCompanyCardFlowExtMapper;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.AcctIndividualDebitFlowMapper;
import com.fenbeitong.fenbeipay.acctdech.dto.AcctOptFlowPageDTO;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.ApplyTypeEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.SubAcctQueryAddressEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyCreditType;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.CompanyCardOperationType;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponOperationType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankCardCreditApplyResqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByAcctTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctComGwAcctRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.flow.IAcctFlowService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.FinhubExceptionUtil;
import com.fenbeitong.fenbeipay.core.utils.ModelConverterUtils;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublicFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.bank.*;
import com.fenbeitong.fenbeipay.dto.flow.AccountAllFlow;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponFlow;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.rpc.service.acct.IAcctAbstractService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.common.saas.entity.CostAttributionGroup;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.pay.search.dto.AccountAllFlowReqDTO;
import com.fenbeitong.saas.card.common.enums.CostAttributionCategory;
import com.fenbeitong.saasplus.api.model.dto.apply.ApplyOrderVoucherResDTO;
import com.fenbeitong.saasplus.api.model.dto.finance.CostInfoResult;
import com.fenbeitong.saasplus.api.service.apply.IApplyOrderService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * IAcctFlowServiceImpl
 * fenbei-pay
 */
@Service("iAcctFlowService")
public class IAcctFlowServiceImpl extends IAcctAbstractService implements IAcctFlowService {

    @Value("${export.query.host}")
    private String exportQueryHost;

    /**
     * 账单关联流水，是否走大数据库标识
     * QX 2022-03-05
     */
    @Value("${db.fenbeitong1.onOff}")
    private boolean flowBillOn;

    @Autowired
    AcctAllFlowServiceImpl acctAllFlowServiceImpl;

    @Autowired
    IApplyOrderService iApplyOrderService;

    @Autowired
    private AccountGeneralFlowService accountGeneralFlowService;

    @Autowired
    private AcctBusinessDebitFlowExtMapper acctBusinessDebitFlowExtMapper;

    @Autowired
    private AcctIndividualDebitFlowMapper acctIndividualDebitFlowMapper;

    @Autowired
    private AcctCompanyCardFlowExtMapper acctCompanyCardFlowExtMapper;



    /**
     * @Description: 业务账户类型-流水查询（规范）
     * @Param: [reqDTO]
     * @return: com.fenbeitong.fenbeipay.api.base.ResponsePage<com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctFlowStereoPageServiceResqDTO>
     */
    @Override
    public ResponsePage<AcctFlowStereoPageServiceResqDTO> acctFlowSearchStereoPage(AcctFlowStereoPageServiceReqDTO reqDTO) {
        FinhubLogger.info("【stereo流水查询】 acctFlowSearchStereoPage 入参 {}", JsonUtils.toJson(reqDTO));
        try {
            ResponsePage<AcctFlowStereoPageServiceResqDTO> responsePage = new ResponsePage<>();
            if (reqDTO.checkReq()) {
                //不加getTradeTypeName 会报错
                responsePage.setDataList(Collections.emptyList());
                return responsePage;
            }
            ResponsePage<AcctFlowStereoPageServiceResqDTO> flows = null;
            FinhubLogger.info("【stereo流水查询】 flowBillOn is {}", flowBillOn);
            if(flowBillOn) {
                // 开启走大数据
                boolean currentFlag = false;
                if(reqDTO.getStartTime() != null && reqDTO.getEndTime() != null){
                    String startDate = DateUtils.formatDate(reqDTO.getStartTime());
                    String endDate = DateUtils.formatDate(reqDTO.getEndTime());
                    String today = DateUtils.formatDate(new Date());
                    if(startDate.equals(endDate) && startDate.equals(today)){
                        // 如果当天，走业务库（不查询账单信息）
                        flows = currentFlows(reqDTO);
                        currentFlag = true;
                    }
                }
                FinhubLogger.info("【stereo流水查询】 currentFlag is {}", currentFlag);
                if(!currentFlag){
                    // 如果历史，走大数据库
                    flows = acctAllFlowServiceImpl.queryPageAcctAllFlow(reqDTO);
                }
            } else {
                // 未开启 走业务库。
                flows = currentFlows(reqDTO);
            }
            return flows;
        } catch (FinPayException e) {
            FinhubLogger.error("【stereo流水查询】异常：{}==异常{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【stereo流水查询】验证异常：{}==异常{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【stereo流水查询】系统异常：{}==异常{} ", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    private ResponsePage<AcctFlowStereoPageServiceResqDTO> currentFlows(AcctFlowStereoPageServiceReqDTO reqDTO){
        ResponsePage<AcctFlowStereoPageServiceResqDTO> flows = new ResponsePage<>();
        if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType()) &&
                FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            flows = uAcctBusinessDebitFlowService.queryPageAcctDebitFlow(reqDTO);
        } else if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType()) &&
                FundAccountModelType.isCredit(reqDTO.getAccountModel())) {
            flows = uAcctBusinessCreditFlowService.queryPageAcctDebitFlow(reqDTO);
        } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType()) &&
                FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            flows = uAcctIndividualDebitFlowService.queryPageAcctDebitFlow(reqDTO);
        } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType()) &&
                FundAccountModelType.isCredit(reqDTO.getAccountModel())) {
            flows = uAcctIndividualCreditFlowService.queryPageAcctDebitFlow(reqDTO);
        } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.GENERAL_ACCOUNT.getKey())) {
            flows = uAcctGeneralFlowService.queryPageAcctGeneralFlowFlow(reqDTO);
        } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey())) {
            flows = uAcctCompanyCardFlowService.queryPageAcctDebitFlow(reqDTO);
        } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey())&&
                FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            flows = acctPublicSearchService.searchAcctPublicPage(reqDTO);
        } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.REDCOUPON_ACCOUNT.getKey())) {
            flows = accountRedcouponSearchService.searchAcctPublicPage(reqDTO);
        } else if (FundAccountSubType.isSettlementAccount(reqDTO.getAccountSubType()) &&
                FundAccountModelType.isRecharge(reqDTO.getAccountModel())){
            flows = uAcctSettlementService.queryPageAcctDebitFlow(reqDTO);
        } else if (FundAccountSubType.isReimbursement(reqDTO.getAccountSubType())) {
            AcctOptFlowReqDTO req = new AcctOptFlowReqDTO();
            BeanUtils.copyProperties(reqDTO, req);
            req.setBankName(reqDTO.getFundPlatform());
            ResponsePage<AcctFlowRespDTO> flows1 = uAcctReimbursementService.queryReimbursementFlowPage(req);
            if (CollectionUtils.isNotEmpty(flows1.getDataList())) {
                List<AcctFlowStereoPageServiceResqDTO> collect = flows1.getDataList().stream().map(e -> {
                    AcctFlowStereoPageServiceResqDTO resp = new AcctFlowStereoPageServiceResqDTO();
                    BeanUtils.copyProperties(e, resp);
                    resp.setFundPlatform(e.getBankName());
                    return resp;
                }).collect(Collectors.toList());
                if (reqDTO.getIsSummaryAmount() && flows1.getTotalAmountData() != null) {
                    AcctFlowRespDTO flow = flows1.getTotalAmountData();
                    AcctFlowStereoPageServiceResqDTO acctFlowStereoPageRespDTO = new AcctFlowStereoPageServiceResqDTO();
                    BeanUtils.copyProperties(flow, acctFlowStereoPageRespDTO);
                    flows.setTotalAmountData(acctFlowStereoPageRespDTO);
                }
                flows.setTotalCount(flows1.getTotalCount());
                flows.setDataList(collect);
            }
        } else if (FundAccountSubType.isOverseaAcct(reqDTO.getAccountSubType())){
            flows = acctOverseaService.queryAccountFlow(reqDTO);
        }
        return flows;
    }


    @Override
    public BigDecimal queryAccountOperationAmountByBizNo(String bizNo, int accountSubType, int accountSubOperationType) {
        return uAcctCommonService.queryAccountOperationAmountByBizNo(bizNo, accountSubType, accountSubOperationType);
    }

    @Override
    public AccountSubFlowRespRPCDTO queryAccountSubFlowByGrantTaskId(String voucherGrantTaskId, int accountSubTyp) {
        return uAcctCommonService.queryAccountSubFlowByGrantTaskId(voucherGrantTaskId, accountSubTyp);
    }

    /**
     * @param accountSubFlowId
     * @return
     */
    @Override
    public AccountSubFlowRespRPCDTO queryAccountSubFlowByFlowId(String accountSubFlowId) {
        return uAcctCommonService.queryAccountSubFlowByFlowId(accountSubFlowId);
    }

    @Override
    public List<AccountSubFlowRespRPCDTO> queryAccountSubFlowByBizNo(String bizNo, Integer accountSubType, Integer accountModel, Integer operationType) {
        List<AccountSubFlowRespRPCDTO> dataLis = new ArrayList<>();
        if (FundAccountSubType.isBusinessAccount(accountSubType) && FundAccountModelType.isRecharge(accountModel)) {
            List<AcctBusinessDebitFlow> dataList = uAcctBusinessDebitFlowService.queryAccountSubFlowByBizNo(bizNo, operationType);
            if (ObjUtils.isEmpty(dataList)) {
                return dataLis;
            }
            dataList.forEach(data -> {
                AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                if (!ObjUtils.isNull(data)) {
                    BeanUtils.copyProperties(data, dto);
                }
                dataLis.add(dto);
            });
        } else if (FundAccountSubType.isBusinessAccount(accountSubType) && FundAccountModelType.isCredit(accountModel)) {
            List<AcctBusinessCreditFlow> dataList = uAcctBusinessCreditFlowService.queryAccountSubFlowByBizNo(bizNo, operationType);
            if (ObjUtils.isEmpty(dataList)) {
                return dataLis;
            }
            dataList.forEach(data -> {
                AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                if (!ObjUtils.isNull(data)) {
                    BeanUtils.copyProperties(data, dto);
                }
                dataLis.add(dto);
            });
        } else if (FundAccountSubType.isIndividualAccount(accountSubType) && FundAccountModelType.isRecharge(accountModel)) {
            List<AcctIndividualDebitFlow> dataList = uAcctIndividualDebitFlowService.queryAccountSubFlowByBizNo(bizNo, operationType);
            if (ObjUtils.isEmpty(dataList)) {
                return dataLis;
            }
            dataList.forEach(data -> {
                AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                if (!ObjUtils.isNull(data)) {
                    BeanUtils.copyProperties(data, dto);
                }
                dataLis.add(dto);
            });
        } else if (FundAccountSubType.isIndividualAccount(accountSubType) && FundAccountModelType.isCredit(accountModel)) {
            List<AcctIndividualCreditFlow> dataList = uAcctIndividualCreditFlowService.queryAccountSubFlowByBizNo(bizNo, operationType);
            if (ObjUtils.isEmpty(dataList)) {
                return dataLis;
            }
            dataList.forEach(data -> {
                AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                if (!ObjUtils.isNull(data)) {
                    BeanUtils.copyProperties(data, dto);
                }
                dataLis.add(dto);
            });
        }
        return dataLis;
    }

    @Override
    public List<AccountSubFlowRespRPCDTO> queryAccountSubFlowByReBizNo(String reBizNo, Integer accountSubType, Integer accountModel, Integer operationType) {
        List<AccountSubFlowRespRPCDTO> dataLis = new ArrayList<>();
        if (FundAccountSubType.isBusinessAccount(accountSubType) && FundAccountModelType.isRecharge(accountModel)) {
            List<AcctBusinessDebitFlow> dataList = uAcctBusinessDebitFlowService.queryAccountSubFlowByReBizNo(reBizNo, operationType);
            if (ObjUtils.isEmpty(dataList)) {
                return dataLis;
            }
            dataList.forEach(data -> {
                AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                if (!ObjUtils.isNull(data)) {
                    BeanUtils.copyProperties(data, dto);
                }
                dataLis.add(dto);
            });
        } else if (FundAccountSubType.isBusinessAccount(accountSubType) && FundAccountModelType.isCredit(accountModel)) {
            List<AcctBusinessCreditFlow> dataList = uAcctBusinessCreditFlowService.queryAccountSubFlowByReBizNo(reBizNo, operationType);
            if (ObjUtils.isEmpty(dataList)) {
                return dataLis;
            }
            dataList.forEach(data -> {
                AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                if (!ObjUtils.isNull(data)) {
                    BeanUtils.copyProperties(data, dto);
                }
                dataLis.add(dto);
            });
        } else if (FundAccountSubType.isIndividualAccount(accountSubType) && FundAccountModelType.isRecharge(accountModel)) {
            List<AcctIndividualDebitFlow> dataList = uAcctIndividualDebitFlowService.queryAccountSubFlowByReBizNo(reBizNo, operationType);
            if (ObjUtils.isEmpty(dataList)) {
                return dataLis;
            }
            dataList.forEach(data -> {
                AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                if (!ObjUtils.isNull(data)) {
                    BeanUtils.copyProperties(data, dto);
                }
                dataLis.add(dto);
            });
        } else if (FundAccountSubType.isIndividualAccount(accountSubType) && FundAccountModelType.isCredit(accountModel)) {
            List<AcctIndividualCreditFlow> dataList = uAcctIndividualCreditFlowService.queryAccountSubFlowByReBizNo(reBizNo, operationType);
            if (ObjUtils.isEmpty(dataList)) {
                return dataLis;
            }
            dataList.forEach(data -> {
                AccountSubFlowRespRPCDTO dto = new AccountSubFlowRespRPCDTO();
                if (!ObjUtils.isNull(data)) {
                    BeanUtils.copyProperties(data, dto);
                }
                dataLis.add(dto);
            });
        }
        return dataLis;
    }

    @Override
    public List<AccountSubFlowRespRPCDTO> queryAccountSubFlowByBizNoAndReBizNo(String bizNo, String reBizNo, Integer accountSubType, Integer accountModel, Integer operationType) {
        List<AccountSubFlowRespRPCDTO> accountSubFlowRespRPCDTOS = Lists.newArrayList();
        if (FundAccountSubType.isBusinessAccount(accountSubType) ) {
            if (FundAccountModelType.isRecharge(accountModel)){
                if (FundAccountSubOptType.PUBLIC_CONSUME.getKey() == operationType){
                    List<AcctBusinessDebitFlow> acctBusinessDebitFlowsConsume = uAcctBusinessDebitFlowService.queryAccountSubFlowByBizNo(bizNo,operationType);
                    if (CollectionUtils.isNotEmpty(acctBusinessDebitFlowsConsume)){
                        for (AcctBusinessDebitFlow acctBusinessDebitFlow:acctBusinessDebitFlowsConsume){
                            AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                            BeanUtils.copyProperties(acctBusinessDebitFlow,accountSubFlowRespRPCDTO);
                            accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                        }
                    }
                }
                if (FundAccountSubOptType.PUBLIC_CONSUME_REFUND.getKey() == operationType){
                    List<AcctBusinessDebitFlow> acctBusinessDebitFlowsRefund = uAcctBusinessDebitFlowService.queryAccountSubFlowByBizNoAndReBizNo(bizNo,reBizNo,operationType);
                    if (CollectionUtils.isNotEmpty(acctBusinessDebitFlowsRefund)){
                        for (AcctBusinessDebitFlow acctBusinessDebitFlow:acctBusinessDebitFlowsRefund){
                            AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                            BeanUtils.copyProperties(acctBusinessDebitFlow,accountSubFlowRespRPCDTO);
                            accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                        }
                    }
                }
            }
            if(FundAccountModelType.isCredit(accountModel)) {
                if (FundAccountSubOptType.PUBLIC_CONSUME.getKey() == operationType){
                    List<AcctBusinessCreditFlow> acctBusinessCreditFlowsConsume = uAcctBusinessCreditFlowService.queryAccountSubFlowByBizNo(bizNo,operationType);
                    if (CollectionUtils.isNotEmpty(acctBusinessCreditFlowsConsume)){
                        for (AcctBusinessCreditFlow acctBusinessDebitFlow:acctBusinessCreditFlowsConsume){
                            AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                            BeanUtils.copyProperties(acctBusinessDebitFlow,accountSubFlowRespRPCDTO);
                            accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                        }
                    }
                }
                if (FundAccountSubOptType.PUBLIC_CONSUME_REFUND.getKey() == operationType){
                    List<AcctBusinessCreditFlow> acctBusinessCreditFlowsRefund = uAcctBusinessCreditFlowService.queryAccountSubFlowByBizNoAndReBizNo(bizNo,reBizNo,operationType);
                    if (CollectionUtils.isNotEmpty(acctBusinessCreditFlowsRefund)){
                        for (AcctBusinessCreditFlow acctBusinessCreditFlow:acctBusinessCreditFlowsRefund){
                            AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                            BeanUtils.copyProperties(acctBusinessCreditFlow,accountSubFlowRespRPCDTO);
                            accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                        }
                    }
                }
            }
        } else if (FundAccountSubType.isIndividualAccount(accountSubType)){
            if (FundAccountModelType.isRecharge(accountModel)) {
                if (FundAccountSubOptType.FROZEN_VOUCHER_GRANT.getKey() == operationType){
                    List<AcctIndividualDebitFlow> acctIndividualDebitFlows = uAcctIndividualDebitFlowService.queryAccountSubFlowByBizNo(bizNo,operationType);
                    if (CollectionUtils.isNotEmpty(acctIndividualDebitFlows)){
                        for (AcctIndividualDebitFlow acctIndividualDebitFlow:acctIndividualDebitFlows){
                            AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                            BeanUtils.copyProperties(acctIndividualDebitFlow,accountSubFlowRespRPCDTO);
                            accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                        }
                    }
                }
                if (FundAccountSubOptType.FROZEN_VOUCHER_RECALL.getKey() == operationType){
                    List<AcctIndividualDebitFlow> acctIndividualDebitFlowsRefund = uAcctIndividualDebitFlowService.queryAccountSubFlowByBizNoAndReBizNo(bizNo,reBizNo,operationType);
                    if (CollectionUtils.isNotEmpty(acctIndividualDebitFlowsRefund)){
                        for (AcctIndividualDebitFlow acctIndividualDebitFlow:acctIndividualDebitFlowsRefund){
                            AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                            BeanUtils.copyProperties(acctIndividualDebitFlow,accountSubFlowRespRPCDTO);
                            accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                        }
                    }
                }
            }
            if (FundAccountModelType.isCredit(accountModel)) {
                if (FundAccountSubOptType.FROZEN_VOUCHER_GRANT.getKey() == operationType){
                    List<AcctIndividualCreditFlow> acctIndividualCreditFlowsConsume = uAcctIndividualCreditFlowService.queryAccountSubFlowByBizNo(bizNo,operationType);
                    if (CollectionUtils.isNotEmpty(acctIndividualCreditFlowsConsume)){
                        for (AcctIndividualCreditFlow acctIndividualCreditFlow:acctIndividualCreditFlowsConsume){
                            AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                            BeanUtils.copyProperties(acctIndividualCreditFlow,accountSubFlowRespRPCDTO);
                            accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                        }
                    }
                }
                if (FundAccountSubOptType.FROZEN_VOUCHER_RECALL.getKey() == operationType){
                    List<AcctIndividualCreditFlow> acctIndividualCreditFlowsRefund = uAcctIndividualCreditFlowService.queryAccountSubFlowByBizNoAndReBizNo(bizNo,reBizNo,operationType);
                    if (CollectionUtils.isNotEmpty(acctIndividualCreditFlowsRefund)){
                        for (AcctIndividualCreditFlow acctIndividualCreditFlow:acctIndividualCreditFlowsRefund){
                            AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                            BeanUtils.copyProperties(acctIndividualCreditFlow,accountSubFlowRespRPCDTO);
                            accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                        }
                    }
                }
            }
        }  else if (accountSubType.equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey())) {
            if (FundAccountSubOptType.PUBLIC_CONSUME.getKey() == operationType){
                AcctCompanyCardFlow acctCompanyCardFlowConsume = uAcctCompanyCardFlowService.queryAcctCompanyCardFlowByBizNoAndOperationType(bizNo,operationType);
                if (acctCompanyCardFlowConsume!=null){
                        AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                        BeanUtils.copyProperties(acctCompanyCardFlowConsume,accountSubFlowRespRPCDTO);
                        accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                }
            }
            if (FundAccountSubOptType.PUBLIC_CONSUME_REFUND.getKey() == operationType){
                AcctCompanyCardFlow acctCompanyCardFlowRefund = uAcctCompanyCardFlowService.queryAcctCompanyCardFlowByBizNoAndReBizNoAndOperationType(bizNo,reBizNo,operationType);
                if (acctCompanyCardFlowRefund!=null){
                    AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                    BeanUtils.copyProperties(acctCompanyCardFlowRefund,accountSubFlowRespRPCDTO);
                    accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                }
            }
        } else if (accountSubType.equals(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey()) && FundAccountModelType.isRecharge(accountModel)) {
            if (FundAccountSubOptType.PUBLIC_CONSUME.getKey() == operationType){
                AccountPublicFlow accountPublicFlowConsume = acctPublicSearchService.queryAccountPublicFlowByBizNoAndOperationType(bizNo,operationType);
                if (accountPublicFlowConsume!=null){
                    AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                    BeanUtils.copyProperties(accountPublicFlowConsume,accountSubFlowRespRPCDTO);
                    accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                }
            }
            if (FundAccountSubOptType.PUBLIC_CONSUME_REFUND.getKey() == operationType){
                AccountPublicFlow accountPublicFlowRefund = acctPublicSearchService.queryAccountPublicFlowByBizNoAndReBizNoAndOperationType(bizNo,reBizNo,operationType);
                if (accountPublicFlowRefund!=null){
                    AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                    BeanUtils.copyProperties(accountPublicFlowRefund,accountSubFlowRespRPCDTO);
                    accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                }
            }
        } else if (accountSubType.equals(FundAccountSubType.REDCOUPON_ACCOUNT.getKey())) {
            if (RedcouponOperationType.PUBLIC_CONSUME.getKey() == operationType || RedcouponOperationType.FROZEN_VOUCHER_GRANT.getKey() == operationType){
                List<AccountRedcouponFlow> flows = accountRedcouponSearchService.queryByBizNoAndOperationType(bizNo, operationType);
                if (CollectionUtils.isNotEmpty(flows)){
                    for (AccountRedcouponFlow flowsConsume : flows) {
                        AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                        BeanUtils.copyProperties(flowsConsume,accountSubFlowRespRPCDTO);
                        accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                    }
                }
            }
            if (RedcouponOperationType.PUBLIC_CONSUME_REFUND.getKey() == operationType || RedcouponOperationType.FROZEN_VOUCHER_RECALL.getKey() == operationType){
                List<AccountRedcouponFlow> flows = accountRedcouponSearchService.queryByBizNoAndReBizNoAndOperationType(bizNo, reBizNo, operationType);
                if(CollectionUtils.isNotEmpty(flows)) {
                    for (AccountRedcouponFlow flow : flows) {
                        AccountSubFlowRespRPCDTO accountSubFlowRespRPCDTO = new AccountSubFlowRespRPCDTO();
                        BeanUtils.copyProperties(flow, accountSubFlowRespRPCDTO);
                        accountSubFlowRespRPCDTOS.add(accountSubFlowRespRPCDTO);
                    }
                }
            }
        }
        return accountSubFlowRespRPCDTOS;
    }

    @Override
    public AccountSubFlowRespRPCDTO queryAcctIndividualDebitFlowByAcctFlowIdOrBankTransNo(String flowId) {
        AccountSubFlowRespRPCDTO respRPCDTO = new AccountSubFlowRespRPCDTO();
        AcctIndividualDebitFlow acctIndividualDebitFlow = uAcctIndividualDebitFlowService.queryAcctIndividualDebitFlowByAcctFlowIdOrBankTransNo(flowId, null);
        if(ObjUtils.isNull(acctIndividualDebitFlow)){
            return null;
        }
        BeanUtils.copyProperties(acctIndividualDebitFlow,respRPCDTO);
        return respRPCDTO;
    }


    @Override
    public AcctGeneralFlowDTO findByBizno(String bizNo) {
        AcctGeneralFlowDTO flowDTO = new AcctGeneralFlowDTO();
        if (StringUtils.isBlank(bizNo)) {
            return null;
        }
        AccountGeneralFlow byBizno = uAcctGeneralFlowService.findByBizno(bizNo);
        if (Objects.nonNull(byBizno)) {
            flowDTO.setAccountId(byBizno.getAccountGeneralId());
            flowDTO.setCompanyId(byBizno.getCompanyId());
            flowDTO.setAccountSubType(FundAccountSubType.GENERAL_ACCOUNT.getKey());
            flowDTO.setCompanyModel(byBizno.getCompanyModel());
            return flowDTO;
        } else {
            AccountPublicFlow accountPublicFlow = acctPublicSearchService.queryByBizNo(bizNo);
            if (Objects.isNull(accountPublicFlow)) {
                return null;
            }
            flowDTO.setAccountId(accountPublicFlow.getCompanyAccountId());
            flowDTO.setCompanyId(accountPublicFlow.getCompanyId());
            flowDTO.setAccountSubType(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey());
            flowDTO.setCompanyModel(FundAccountModelType.RECHARGE.getKey());
            return flowDTO;
        }
    }

    @Override
    public boolean updateCostImageStatus(AcctCostImageReqDTO reqDTO) {
        boolean isSuccess = false;
        if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType()) &&
                FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            isSuccess = uAcctBusinessDebitFlowService.updateCostImageStatus(reqDTO);
            //众邦-若存在更新众邦银行平台账户电子回单
            if (BankNameEnum.isZBBank(reqDTO.getBankName())) {
                uBankAcctFlowService.updateZbBankCostImageStatus(reqDTO);
            }else if (BankNameEnum.isSpd(reqDTO.getBankName())){
                uBankAcctFlowService.updateSpdBankCostImageStatus(reqDTO);
            }
        } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType()) &&
                FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            //个人充值账户只有发放和退还流水，消费和退款在冻结池流水表,只能更新发放和退还回单
            isSuccess = uAcctIndividualDebitFlowService.updateCostImageStatus(reqDTO);
            //众邦-若存在更新众邦银行平台账户电子回单
            if (BankNameEnum.isZBBank(reqDTO.getBankName())) {
                uBankAcctFlowService.updateZbBankCostImageStatus(reqDTO);
            }else if (BankNameEnum.isSpd(reqDTO.getBankName())){
                uBankAcctFlowService.updateSpdBankCostImageStatus(reqDTO);
            }
        } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.GENERAL_ACCOUNT.getKey())) {
            // 如果是余额账户，中信可能是手动退汇
            AccountPublicFlow accountPublicFlow = acctPublicSearchService.queryByBankTransNo(reqDTO.getBankTransNo());
            if(!ObjUtils.isNull(accountPublicFlow)){
                AccountPublicFlow update = new AccountPublicFlow();
                update.setId(accountPublicFlow.getId());
                update.setCostImageStatus(reqDTO.getCostImageStatus());
                update.setCostImageTime(reqDTO.getCostImageTime());
                update.setCostImageUrl(reqDTO.getCostImageUrl());
                isSuccess = acctPublicDechService.updateByFlowId(update);
            }else {
                isSuccess =  uAcctGeneralFlowService.updateCostImageStatus(reqDTO);
            }
        } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey())) {
            isSuccess =  uAcctCompanyCardFlowService.updateCostImageStatus(reqDTO);
        } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey())) {
            AccountPublicFlow accountPublicFlow = acctPublicSearchService.query4UpdateCostImageStatus(reqDTO.getFlowId(),reqDTO.getBankTransNo());
            if (ObjUtils.isNotEmpty(accountPublicFlow)) {
                AccountPublicFlow update = new AccountPublicFlow();
                update.setId(accountPublicFlow.getId());
                update.setCostImageStatus(reqDTO.getCostImageStatus());
                update.setCostImageTime(reqDTO.getCostImageTime());
                update.setCostImageUrl(reqDTO.getCostImageUrl());
                isSuccess =  acctPublicDechService.updateByFlowId(update);
            }
        } else if (reqDTO.getAccountSubType().equals(FundAccountSubType.REIMBURSEMENT_ACCOUNT.getKey())){
            isSuccess =  uAcctReimbursementService.updateCostImageStatus(reqDTO);
        } else if (reqDTO.getAccountSubType().equals(0)){
            // 平台账户-收款账户提现
            isSuccess =  uBankAcctFlowService.updateCostImageStatusByBizNo(reqDTO);
            //若存在更新中信银行平台账户电子回单,若存在众邦更新众邦
            if(BankNameEnum.isZBBank(reqDTO.getBankName())){
                isSuccess = uBankAcctFlowService.updateZbBankCostImageStatus(reqDTO);
            }else if(BankNameEnum.isCitic(reqDTO.getBankName())){
                isSuccess = uBankAcctFlowService.updateZxBankCostImageStatus(reqDTO);
            }else if(BankNameEnum.isSpd(reqDTO.getBankName())){
                isSuccess = uBankAcctFlowService.updateSpdBankCostImageStatus(reqDTO);
            }
        } else {
            FinhubLogger.warn("账户不存在：{}",JsonUtils.toJson(reqDTO));
        }
        if (!isSuccess){
            FinhubLogger.warn("【电子回单】电子回单更新失败" + JsonUtils.toJson(reqDTO));
        }
        return isSuccess;
    }

    @Override
    public boolean updatePubCostImageStatus(AcctPubCostImageReqDTO reqDTO) {
        AccountPublicFlow accountPublicFlow;
        if (!StringUtils.isBlank(reqDTO.getReBizNo())){
             accountPublicFlow = acctPublicSearchService.queryByBizNoAndReBizNo(reqDTO.getBizNo(),reqDTO.getReBizNo());
        } else {
            accountPublicFlow = acctPublicSearchService.queryByBizNo(reqDTO.getBizNo());
        }

        if(ObjUtils.isNull(accountPublicFlow)){
            FinhubLogger.error("【对公账户】流水不存在,参数：{}",JsonUtils.toJson(reqDTO));
            return false;
        }
        if(Objects.nonNull(accountPublicFlow.getCostImageStatus()) && FundAcctCostImageStatus.isCostImageSuce(accountPublicFlow.getCostImageStatus())){
            return true;
        }
        accountPublicFlow.setCostImageStatus(reqDTO.getCostImageStatus());
        accountPublicFlow.setCostImageTime(reqDTO.getCostImageTime());
        accountPublicFlow.setCostImageUrl(reqDTO.getCostImageUrl());
        return acctPublicDechService.updateByFlowId(accountPublicFlow);
    }

    @Override
    public boolean directAcctByHupo(AcctHupoTradeReqDTO acctHupoTradeReqDTO) {
        if (FundAccountSubType.isBusinessAccount(acctHupoTradeReqDTO.getAccountSubType()) &&
                FundAccountModelType.isRecharge(acctHupoTradeReqDTO.getAccountModel())) {
            return false;
        } else if (FundAccountSubType.isBusinessAccount(acctHupoTradeReqDTO.getAccountSubType()) &&
                FundAccountModelType.isCredit(acctHupoTradeReqDTO.getAccountModel())) {
            return false;
        } else if (FundAccountSubType.isIndividualAccount(acctHupoTradeReqDTO.getAccountSubType()) &&
                FundAccountModelType.isRecharge(acctHupoTradeReqDTO.getAccountModel())) {
            return false;
        } else if (FundAccountSubType.isIndividualAccount(acctHupoTradeReqDTO.getAccountSubType()) &&
                FundAccountModelType.isCredit(acctHupoTradeReqDTO.getAccountModel())) {
            return false;
        } else if (acctHupoTradeReqDTO.getAccountSubType().equals(FundAccountSubType.GENERAL_ACCOUNT.getKey())) {
            AccountGeneralFlow byFlowId = uAcctGeneralFlowService.findByFlowId(acctHupoTradeReqDTO.getFlowId());
            if(ObjUtils.isNull(byFlowId)){
                FinhubLogger.error("【余额账户】流水不存在,参数：{}",JsonUtils.toJson(acctHupoTradeReqDTO));
                return false;
            }
            AccountGeneral accountGeneral = uAcctGeneralService.findByAccountId(byFlowId.getAccountGeneralId());
            if(ObjUtils.isNull(accountGeneral)){
                FinhubLogger.error("【余额账户】余额账户不存在,参数：{}",JsonUtils.toJson(acctHupoTradeReqDTO));
                return false;
            }
            AccountPublic accountPublic = acctPublicSearchService.findByComIdAndMIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getCompanyMainId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
            //查询主体开户信息
            return Objects.nonNull(accountPublic) && FundAcctDirectAcctTypeEnum.isHupo(accountPublic.getDirectAcctType());
        } else if (acctHupoTradeReqDTO.getAccountSubType().equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey())) {
            return false;
        } else if (acctHupoTradeReqDTO.getAccountSubType().equals(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey())) {
            AccountPublicFlow accountPublicFlow = acctPublicSearchService.queryByFlowId(acctHupoTradeReqDTO.getFlowId());
            if(ObjUtils.isNull(accountPublicFlow)){
                FinhubLogger.error("【对公账户】流水不存在,参数：{}",JsonUtils.toJson(acctHupoTradeReqDTO));
                return false;
            }
            AccountPublic accountPublic = acctPublicSearchService.queryByAccountId(accountPublicFlow.getAccountPublicId());
            if(ObjUtils.isNull(accountPublic)){
                FinhubLogger.error("【对公账户】账户不存在,参数：{}",JsonUtils.toJson(acctHupoTradeReqDTO));
                return false;
            }
            return FundAcctDirectAcctTypeEnum.isHupo(accountPublic.getDirectAcctType());
        }else {
            FinhubLogger.error("账户不存在：{}",JsonUtils.toJson(acctHupoTradeReqDTO));
            return false;
        }
    }

    @Override
    public ExportAcctFlowTaskDataRespDTO exportAcctFlowTaskData(ExportAcctFlowTaskDataReqDTO reqDTO) {
        ExportAcctFlowTaskDataRespDTO respDTO = new ExportAcctFlowTaskDataRespDTO();
        List<ExportAcctFlowTaskData> excelDataList = new ArrayList<>();
        if(ObjUtils.isNotEmpty(reqDTO.getFundPlatforms())){
            // excel
            for (String fundPlatform : reqDTO.getFundPlatforms()) {
                for (Integer accountModel : reqDTO.getAccountModels()) {
                    ExportAcctFlowTaskData excelData = new ExportAcctFlowTaskData();
                    // sheet
                    List<ExportAcctFlowTaskDataSheet> dataSheetList = new ArrayList<>();
                    for (Integer accountSubType : reqDTO.getAccountSubTypes()) {

                        ExportAcctFlowTaskDataSheet dataSheet = new ExportAcctFlowTaskDataSheet();
                        dataSheet.setQueryPath(exportQueryHost + SubAcctQueryAddressEnum.getEnum(accountSubType).getAddress());
                        dataSheet.setSheetName(FundAccountSubType.getEnum(accountSubType).getValue());
                        // 查询条件
                        ExportAcctFlowTaskDataQuery dataQuery = new ExportAcctFlowTaskDataQuery();
                        BeanUtils.copyProperties(reqDTO,dataQuery);
                        dataQuery.setAccountModel(accountModel);
                        dataQuery.setAccountSubType(accountSubType);
                        dataQuery.setFundPlatform(fundPlatform);
                        dataSheet.setQuery(dataQuery);
                        dataSheet.setColumns(reqDTO.getColumns());
                        dataSheetList.add(dataSheet);
                    }
                    // excelName
                    if (Objects.equals(fundPlatform,BankCoreConstant.ZBBANKH_CODE)) {
                        excelData.setExcelName(BankCoreConstant.ZBBANKH_NAME + "-" + FundAccountModelType.getEnum(accountModel).getMsg4Client());
                    }else {
                        excelData.setExcelName(BankNameEnum.getBankEnum(fundPlatform).getName() + "-" + FundAccountModelType.getEnum(accountModel).getMsg4Client());
                    }
                    excelData.setSheetNames(dataSheetList);
                    excelDataList.add(excelData);
                }
                respDTO.setExportTaskList(excelDataList);
            }
        }
        // 组装excelData
        return respDTO;
    }

    @Override
    public ResponsePage<AcctFlow4StereoBillRespDTO> acctFlowSearchByStereoBill(AcctFlow4StereoBillReqDTO acctFlow4StereoBillReqDTO) {
        ResponsePage<AcctFlow4StereoBillRespDTO> responsePage = new ResponsePage<>();

        BillSummaryDetailQuery billSummaryDetailQuery = new BillSummaryDetailQuery();
        billSummaryDetailQuery.setBillNo(acctFlow4StereoBillReqDTO.getBillNo());
        billSummaryDetailQuery.setCompanyId(acctFlow4StereoBillReqDTO.getCompanyId());
        billSummaryDetailQuery.setPageIndex(acctFlow4StereoBillReqDTO.getPageNo());
        billSummaryDetailQuery.setPageSize(acctFlow4StereoBillReqDTO.getPageSize());
        FinhubLogger.info("queryBillSummaryDetailDataListByBillNo req:{}", JSON.toJSONString(billSummaryDetailQuery));
        BasePageDTO<BillSummaryDetailDTO> list = iBillOpenApi.queryBillSummaryDetailDataListByBillNo(billSummaryDetailQuery);
        FinhubLogger.info("queryBillSummaryDetailDataListByBillNo resp:{}", JSON.toJSONString(list));
        if (ObjUtils.isNotEmpty(list)){
            List<AcctFlow4StereoBillRespDTO> respDTOS = Lists.newArrayList();
            long start = System.currentTimeMillis();
            for (BillSummaryDetailDTO billSummaryDetailDTO: list.getDtoList()){
                String fbOrderId = billSummaryDetailDTO.getDataId()==null?null:billSummaryDetailDTO.getDataId();
                if (fbOrderId == null){
                    continue;
                }
                AcctFlow4StereoBillRespDTO acctFlow4StereoBillRespDTO = new AcctFlow4StereoBillRespDTO();
                List<AcctBusinessDebitFlow> publicConsumeList = uAcctBusinessDebitFlowService.queryAccountSubFlowByBizNo(fbOrderId,FundAccountSubOptType.PUBLIC_CONSUME.getKey());
                if (ObjUtils.isEmpty(publicConsumeList)){
                    publicConsumeList  = uAcctBusinessDebitFlowService.queryAccountSubFlowByReBizNo(fbOrderId,FundAccountSubOptType.PUBLIC_CONSUME_REFUND.getKey());
                }

                if (ObjUtils.isNotEmpty(publicConsumeList)){
                    acctFlow4StereoBillRespDTO.setAccountFlowId(publicConsumeList.get(0).getAccountFlowId());
                    acctFlow4StereoBillRespDTO.setAccountModel(publicConsumeList.get(0).getAccountModel());
                    acctFlow4StereoBillRespDTO.setAccountSubType(publicConsumeList.get(0).getAccountModel());
                    acctFlow4StereoBillRespDTO.setBillNo(billSummaryDetailDTO.getSummaryInfoBean().getBillNo());
                    acctFlow4StereoBillRespDTO.setBillDate(billSummaryDetailDTO.getSummaryInfoBean().getBillDate());
                    acctFlow4StereoBillRespDTO.setBalance(publicConsumeList.get(0).getBalance());
                    acctFlow4StereoBillRespDTO.setBillBeginDate(billSummaryDetailDTO.getSummaryInfoBean().getBillBeginDate());
                    acctFlow4StereoBillRespDTO.setBankAccountNo(publicConsumeList.get(0).getBankAccountNo());
                    acctFlow4StereoBillRespDTO.setBankName(publicConsumeList.get(0).getBankName());
                    acctFlow4StereoBillRespDTO.setBizNo(publicConsumeList.get(0).getBizNo());
                    acctFlow4StereoBillRespDTO.setBankTransNo(publicConsumeList.get(0).getBankTransNo());
                    acctFlow4StereoBillRespDTO.setBillEndDate(billSummaryDetailDTO.getSummaryInfoBean().getBillEndDate());
                    acctFlow4StereoBillRespDTO.setSyncBankTransNo(publicConsumeList.get(0).getSyncBankTransNo());
                    acctFlow4StereoBillRespDTO.setCompanyId(publicConsumeList.get(0).getCompanyId());
                    acctFlow4StereoBillRespDTO.setCreateTime(publicConsumeList.get(0).getCreateTime());
                    acctFlow4StereoBillRespDTO.setFundPlatform(publicConsumeList.get(0).getBankName());
                    acctFlow4StereoBillRespDTO.setOperationAmount(publicConsumeList.get(0).getOperationAmount());
                    acctFlow4StereoBillRespDTO.setOperationType(publicConsumeList.get(0).getOperationType());
                    acctFlow4StereoBillRespDTO.setOperationTypeDesc(publicConsumeList.get(0).getOperationTypeDesc());
                    acctFlow4StereoBillRespDTO.setOperationUserName(publicConsumeList.get(0).getOperationUserName());
                    acctFlow4StereoBillRespDTO.setOrderId(billSummaryDetailDTO.getOrderId());
                    acctFlow4StereoBillRespDTO.setOrderType(publicConsumeList.get(0).getOrderType());
                    acctFlow4StereoBillRespDTO.setTotalAmount(billSummaryDetailDTO.getTotalAmount());
                    acctFlow4StereoBillRespDTO.setTradeType(publicConsumeList.get(0).getTradeType());
                    KeyValue<Integer, String> keyValue = new KeyValue<>();
                    keyValue.setKey(billSummaryDetailDTO.getSummaryInfoBean().getUserVisibleState().getKey());
                    keyValue.setValue(billSummaryDetailDTO.getSummaryInfoBean().getUserVisibleState().getValue());
                    acctFlow4StereoBillRespDTO.setUserVisibleState(keyValue);
                    FundAcctTradeType tradeTypeEnum = FundAcctTradeType.getEnum(publicConsumeList.get(0).getTradeType());
                    if (tradeTypeEnum !=null) {
                        acctFlow4StereoBillRespDTO.setTradeTypeName(tradeTypeEnum.getDesc());
                    }
                    respDTOS.add(acctFlow4StereoBillRespDTO);
                    continue;
                }
                List<AcctBusinessCreditFlow> publicConsumeList1 = uAcctBusinessCreditFlowService.queryAccountSubFlowByBizNo(fbOrderId,FundAccountSubOptType.PUBLIC_CONSUME.getKey());
                if (ObjUtils.isEmpty(publicConsumeList1)){
                    publicConsumeList1  = uAcctBusinessCreditFlowService.queryAccountSubFlowByReBizNo(fbOrderId,FundAccountSubOptType.PUBLIC_CONSUME_REFUND.getKey());
                }
                if (ObjUtils.isNotEmpty(publicConsumeList1)){
                    acctFlow4StereoBillRespDTO.setAccountFlowId(publicConsumeList1.get(0).getAccountFlowId());
                    acctFlow4StereoBillRespDTO.setAccountModel(publicConsumeList1.get(0).getAccountModel());
                    acctFlow4StereoBillRespDTO.setAccountSubType(publicConsumeList1.get(0).getAccountModel());
                    acctFlow4StereoBillRespDTO.setBillNo(billSummaryDetailDTO.getSummaryInfoBean().getBillNo());
                    acctFlow4StereoBillRespDTO.setBillDate(billSummaryDetailDTO.getSummaryInfoBean().getBillDate());
                    acctFlow4StereoBillRespDTO.setBalance(publicConsumeList1.get(0).getBalance());
                    acctFlow4StereoBillRespDTO.setBillBeginDate(billSummaryDetailDTO.getSummaryInfoBean().getBillBeginDate());
                    acctFlow4StereoBillRespDTO.setBankAccountNo(publicConsumeList1.get(0).getBankAccountNo());
                    acctFlow4StereoBillRespDTO.setBankName(publicConsumeList1.get(0).getBankName());
                    acctFlow4StereoBillRespDTO.setBizNo(publicConsumeList1.get(0).getBizNo());
                    acctFlow4StereoBillRespDTO.setBankTransNo(publicConsumeList1.get(0).getBankTransNo());
                    acctFlow4StereoBillRespDTO.setBillEndDate(billSummaryDetailDTO.getSummaryInfoBean().getBillEndDate());
                    acctFlow4StereoBillRespDTO.setSyncBankTransNo(publicConsumeList1.get(0).getSyncBankTransNo());
                    acctFlow4StereoBillRespDTO.setCompanyId(publicConsumeList1.get(0).getCompanyId());
                    acctFlow4StereoBillRespDTO.setCreateTime(publicConsumeList1.get(0).getCreateTime());
                    acctFlow4StereoBillRespDTO.setFundPlatform(publicConsumeList1.get(0).getBankName());
                    acctFlow4StereoBillRespDTO.setOperationAmount(publicConsumeList1.get(0).getOperationAmount());
                    acctFlow4StereoBillRespDTO.setOperationType(publicConsumeList1.get(0).getOperationType());
                    acctFlow4StereoBillRespDTO.setOperationTypeDesc(publicConsumeList1.get(0).getOperationTypeDesc());
                    acctFlow4StereoBillRespDTO.setOperationUserName(publicConsumeList1.get(0).getOperationUserName());
                    acctFlow4StereoBillRespDTO.setOrderId(billSummaryDetailDTO.getOrderId());
                    acctFlow4StereoBillRespDTO.setOrderType(publicConsumeList1.get(0).getOrderType());
                    acctFlow4StereoBillRespDTO.setTotalAmount(billSummaryDetailDTO.getTotalAmount());
                    acctFlow4StereoBillRespDTO.setTradeType(publicConsumeList1.get(0).getTradeType());
                    KeyValue<Integer, String> keyValue = new KeyValue<>();
                    keyValue.setKey(billSummaryDetailDTO.getSummaryInfoBean().getUserVisibleState().getKey());
                    keyValue.setValue(billSummaryDetailDTO.getSummaryInfoBean().getUserVisibleState().getValue());
                    acctFlow4StereoBillRespDTO.setUserVisibleState(keyValue);
                    FundAcctTradeType tradeTypeEnum = FundAcctTradeType.getEnum(publicConsumeList1.get(0).getTradeType());
                    if (tradeTypeEnum !=null) {
                        acctFlow4StereoBillRespDTO.setTradeTypeName(tradeTypeEnum.getDesc());
                    }
                    respDTOS.add(acctFlow4StereoBillRespDTO);
                }
            }
            long end = System.currentTimeMillis();
            FinhubLogger.info("queryBillSummaryDetailDataListByBillNo from database spend:{}", (end-start)/1000);
            responsePage.setDataList(respDTOS);
            responsePage.setTotalCount(list.getCount());
            return responsePage;
        }
        responsePage.setTotalCount(0);
        responsePage.setDataList(new ArrayList<>());
        return responsePage;
    }

    @Override
    public AcctGeneralFlowDTO findByBankTransNo(String bankTransNo) {
        AcctGeneralFlowDTO respRPCDTO = new AcctGeneralFlowDTO();
        AccountGeneralFlow accountGeneralFlow = uAcctGeneralFlowService.queryByBankTransNo(bankTransNo);
        if(ObjUtils.isNull(accountGeneralFlow)){
            return null;
        }
        BeanUtils.copyProperties(accountGeneralFlow,respRPCDTO);
        return respRPCDTO;
    }

    @Override
    public AcctGeneralFlowDTO findRechargeByBankNameAndBankNoAndTransNo(String bankName, String bankAccountNo, String bankTransNo) {
        AcctGeneralFlowDTO respRPCDTO = new AcctGeneralFlowDTO();
        AccountGeneralFlow accountGeneralFlow = uAcctGeneralFlowService.findRechargeByBankNameAndBankNoAndTransNo(bankName, bankAccountNo, bankTransNo);
        if(ObjUtils.isNull(accountGeneralFlow)){
            return null;
        }
        BeanUtils.copyProperties(accountGeneralFlow,respRPCDTO);
        return respRPCDTO;
    }

    @Override
    public AcctAddTheAccountRespDTO findInfo4AddTheAccount(AcctAddTheAccountReqDTO acctAddTheAccountReqDTO) {
        try {
            AcctAddTheAccountRespDTO acctAddTheAccountRespDTO = null;
            if (FundAccountSubType.isBusinessAccount(acctAddTheAccountReqDTO.getAccountSubType())) {
                AcctComGwByAcctTypeReqDTO reqDTO = new AcctComGwByAcctTypeReqDTO();
                reqDTO.setCompanyId(acctAddTheAccountReqDTO.getCompanyId());
                reqDTO.setFundAccountType(acctAddTheAccountReqDTO.getAccountSubType());
                AcctComGwAcctRespDTO acctRespDTO = acctCompanyGatewayService.findActGwByAcctType(reqDTO);
                if (acctRespDTO == null){
                   return null;
                }
                if (FundAccountModelType.isRecharge(acctRespDTO.getAccountModel())){
                    acctAddTheAccountRespDTO = uAcctBusinessDebitFlowService.findInfo4AddTheAccount(acctAddTheAccountReqDTO.getAccountFlowId());
                }
                if (FundAccountModelType.isCredit(acctRespDTO.getAccountModel())) {
                    acctAddTheAccountRespDTO = uAcctBusinessCreditFlowService.findInfo4AddTheAccount(acctAddTheAccountReqDTO.getAccountFlowId());
                }
            } else if (FundAccountSubType.isIndividualAccount(acctAddTheAccountReqDTO.getAccountSubType()) ) {
                acctAddTheAccountRespDTO = uFundFreezenService.findInfo4AddTheAccount(acctAddTheAccountReqDTO.getAccountFlowId());
            }
            return acctAddTheAccountRespDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【stereo补账查询】异常：{}==异常{}", JsonUtils.toJson(acctAddTheAccountReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【stereo补账查询】验证异常：{}==异常{}", JsonUtils.toJson(acctAddTheAccountReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【stereo补账查询】系统异常：{}==异常{} ", JsonUtils.toJson(acctAddTheAccountReqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AccountSubFlowRespRPCDTO findPublicFlowByBankTransNo(String bankTransNo) {
        AccountSubFlowRespRPCDTO respRPCDTO = new AccountSubFlowRespRPCDTO();
        AccountPublicFlow accountPublicFlow = acctPublicSearchService.queryByBankTransNo(bankTransNo);
        if(ObjUtils.isNull(accountPublicFlow)){
            return null;
        }
        BeanUtils.copyProperties(accountPublicFlow,respRPCDTO);
        return respRPCDTO;
    }

    @Override
    public String zxReturnRemittanceUpdateCostImage(String bankTransNo, String orderId) {
        AccountGeneralFlow accountGeneralFlow = uAcctGeneralFlowService.queryByBankTransNo(bankTransNo);
        if(ObjUtils.isNull(accountGeneralFlow)){
            return null;
        }
        String receiptUrl = accountGeneralFlow.getCostImageUrl();
        AccountGeneralFlow update = new AccountGeneralFlow();
        update.setId(accountGeneralFlow.getId());
        update.setRemark("退汇：" + orderId);
        update.setCostImageStatus(0);
        update.setCostImageTime(null);
        update.setCostImageUrl(null);
        boolean result = uAcctGeneralFlowService.updateById4ReturnRemittance(update);
        if(!result){
            FinhubLogger.error("中信手动退汇-更新充值流水信息失败,bankTransNo={},orderId={}", bankTransNo, orderId);
        }
        return receiptUrl;
    }
    @Override
    public ResponsePage<AccountVirtualFlowRespDTO> queryAccountBigDataPageFlow(AccountVirtualFlowReqRPCDTO reqDto) {
        try{
            FinhubLogger.info("queryAccountBigDataPageFlow={}", JSON.toJSONString(reqDto));
            ResponsePage<AccountVirtualFlowRespDTO> res = new ResponsePage<>();
            AccountAllFlowReqDTO req = buildQueryData(reqDto);
            //根据条件查询大数据虚拟卡账户流水
            ResponsePage<AccountAllFlow> accountFlow= accountAllFlowService.queryPage(req);
            List<AccountAllFlow> accountFlowDataList = accountFlow.getDataList();
            if (CollectionUtils.isEmpty(accountFlowDataList)) {
                return res;
            }
            String companyId = reqDto.getCompanyId();
            List<AccountVirtualFlowRespDTO> resList = new ArrayList<>();
            List<AccountVirtualFlowRespDTO> finalResList = resList;
            accountFlowDataList.forEach(item ->{
                //operationType=80 额度申请，金额是负数，需要转成正数？，普通申请单在tb_bank_card_credit_apply表operation_channel=6是循环备用金子单
                //1 额度申请
                if (CompanyCardOperationType.FROZEN_BANK_RECALL.getCode().equals(item.getOperationType())) {
                    operaApply(finalResList, item);
                }
                //2 额度退回
                if (CompanyCardOperationType.FROZEN_BANK_REFUND.getCode().equals(item.getOperationType())) {
                    operaRefundFlow(finalResList, item);
                }
            });
            getMeaningNo(finalResList);
            res.setDataList(finalResList);
            res.setTotalCount(accountFlow.getTotalCount());
            FinhubLogger.info("queryAccountBigDataPageFlow={},totalCount={},finalResListSize={}", JSON.toJSONString(reqDto), accountFlow.getTotalCount(),finalResList.size());

            return res;

        } catch (FinhubException e){
            FinhubLogger.warn("queryAccountPageFlow warn", e);
            throw e;
        } catch (Exception e){
            FinhubLogger.error("queryAccountPageFlow eror", e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.EXCEPTION);
        }
    }

    private void operaRefundFlow(List<AccountVirtualFlowRespDTO> finalResList, AccountAllFlow item) {
        item.setOperationAmount(BigDecimalUtils.fenToYuan(item.getOperationAmount()));
        AccountVirtualFlowRespDTO accountVirtual = ModelConverterUtils.convert(item, AccountVirtualFlowRespDTO.class);
        //填充账户模式
        AccountModelType accountModel = AccountModelType.getEnum(item.getAccountModel());
        accountVirtual.setAccountModelName(accountModel.getValue());
        accountVirtual.setOperationTypeName("额度回收");
        accountVirtual.setBizNo(item.getBizId());
        //设置虚拟卡流水中银行卡卡号
        accountVirtual.setAccountId(item.getBankAccountNo());
        String bizNo = item.getBizId();
        String applyTransNo = "";
        BankCardCreditDistribute distribute = creditDistributeManager.getDistributeByDistributeOrderNo(item.getBizId());
        if (Objects.nonNull(distribute)){
            FinhubLogger.info("额度退回流水的入口：distributeNo={},bizNo={}", item.getBizId(), bizNo);
            bizNo = Optional.ofNullable(distribute.getBizNo()).orElse(distribute.getApplyTransNo());
            accountVirtual.setBizNo(bizNo);
            applyTransNo = distribute.getApplyTransNo();
        }else{
            FinhubLogger.info("额度退回流水的入口：bizNo={}", bizNo);
        }
        List<BankCardCreditApplyFlow> creditApplyFlowBizNoList = creditApplyOperationManager.queryFlowByBizNo(bizNo);
        List<AcctCountApplyRespDTO> applyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(creditApplyFlowBizNoList)) {
            FinhubLogger.info("额度退回流水的入口：bizNo={},creditApplyFlowBizNoList={}", bizNo,creditApplyFlowBizNoList.size());
            creditApplyFlowBizNoList.forEach(apply ->{
                AcctCountApplyRespDTO acctCountApply = new AcctCountApplyRespDTO();
                acctCountApply.setBizNo(apply.getBizNo());
                acctCountApply.setApplyAmount(BigDecimalUtils.fenToYuan(apply.getApplyAmount()));
                acctCountApply.setDeductionAmount(BigDecimalUtils.fenToYuan(apply.getDeductionAmount()));
                acctCountApply.setEmployeeId(apply.getEmployeeId());
                EmployeeContract employeeContract = getEmployeeContract(apply.getEmployeeId(), apply.getCompanyId());
                acctCountApply.setOperationUserName(ObjUtils.isEmpty(employeeContract) ? "" : employeeContract.getName());
                acctCountApply.setCreateTime(apply.getCreateTime());
                acctCountApply.setType(ApplyTypeEnum.VIRTUAL_APPLY.getCode());
                acctCountApply.setRemark(apply.getApplyReason());
                CostInfoResult costInfo = iOrderCostService.queryCostInfoByApplyOrderId(apply.getBizNo());
                com.fenbeitong.finhub.common.saas.entity.CostInfoResult costInfoResult = ModelConverterUtils.convert(costInfo, com.fenbeitong.finhub.common.saas.entity.CostInfoResult.class);
                acctCountApply.setCostInfo(costInfoResult);
                applyList.add(acctCountApply);
            });
            accountVirtual.setApplyList(applyList);
        }else {
            //备用金去另外一张表tb_bank_card_apply_flow查询
            FinhubLogger.info("额度退回流水的入口refundPettyModel：bizNo={},applyTransNo={}", bizNo,applyTransNo);
            refundPettyModel(accountVirtual,applyList, applyTransNo);
        }
        finalResList.add(accountVirtual);
    }

    private void operaApply(List<AccountVirtualFlowRespDTO> finalResList, AccountAllFlow item) {
        item.setOperationAmount(BigDecimalUtils.fenToYuan(item.getOperationAmount()));
        AccountVirtualFlowRespDTO accountVirtual = ModelConverterUtils.convert(item, AccountVirtualFlowRespDTO.class);
        //填充账户模式
        AccountModelType accountModel = AccountModelType.getEnum(item.getAccountModel());
        accountVirtual.setAccountModelName(accountModel.getValue());
        accountVirtual.setOperationTypeName("额度发放");
        accountVirtual.setBizNo(item.getBizId());
        //设置虚拟卡流水中银行卡卡号
        accountVirtual.setAccountId(item.getBankAccountNo());
        ArrayList<String> list = Lists.newArrayList();
        BankCardCreditDistribute distribute = creditDistributeManager.getDistributeByDistributeOrderNo(item.getBizId());
        if (Objects.nonNull(distribute)){
            list.add(distribute.getBizNo());
            accountVirtual.setBizNo(distribute.getBizNo());
            FinhubLogger.info("获取下发单信息，distributeNo={},bizNo={}", item.getBizId(), distribute.getBizNo());
        } else{
            list.add(item.getBizId());
            FinhubLogger.info("获取下发单信息不存在，bizNo={}", item.getBizId());
        }
        List<BankCardCreditApplyResqDTO> creditApplyBizNoList = creditApplyOperationManager.getBankCardCreditApplyByBizNoList(list);
        if (CollectionUtils.isNotEmpty(creditApplyBizNoList)) {
            FinhubLogger.info("获取到的申请单信息数量，size={}", creditApplyBizNoList.size());
            List<AcctCountApplyRespDTO> applyList = new ArrayList<>();
            creditApplyBizNoList.forEach(apply ->{
                if (OperationChannelType.ROUNDBYJ.getKey() == apply.getOperationChannel()) {
                    //循环备用金子单数据,根据bizNo查询到主单数据tb_bank_sub_petty.bizno,再根据pettyID找到主单的申请单
                    BankSubPetty bankSubPetty = bankPettyManager.queryBankSubPettyByBizNo(apply.getBizNo());
                    FinhubLogger.info("循环备用金的情况，bizNo={},bankSubPetty={}", apply.getBizNo(),Objects.isNull(bankSubPetty));
                    if (ObjUtils.isNotEmpty(bankSubPetty)) {
                        AcctCountApplyRespDTO acctCountApply = new AcctCountApplyRespDTO();
                        String rootPettyId = bankSubPetty.getRootPettyId();
                        BankPetty bankPetty = bankPettyManager.queryBankPettyPettyId(rootPettyId);
                        String bizNo = bankPetty.getBizNo();//主单申请单

                        acctCountApply.setBizNo(bizNo);
                        acctCountApply.setCreateTime(bankPetty.getCreateTime());
                        acctCountApply.setType(ApplyTypeEnum.ROUND_PETTY_APPLY.getCode());
                        acctCountApply.setRemark(apply.getRemark());
                        acctCountApply.setApplyAmount(BigDecimalUtils.fenToYuan(bankPetty.getApplySum()));
                        acctCountApply.setDeductionAmount(BigDecimalUtils.fenToYuan(bankSubPetty.getRoundBalance()));
                        acctCountApply.setEmployeeId(bankPetty.getEmployeeId());
                        acctCountApply.setOperationUserName(bankPetty.getEmployeeName());

                        //费用归属信息
                        CostInfoResult costInfo = iOrderCostService.queryCostInfoByApplyOrderId(bizNo);
                        com.fenbeitong.finhub.common.saas.entity.CostInfoResult costInfoResult = ModelConverterUtils.convert(costInfo, com.fenbeitong.finhub.common.saas.entity.CostInfoResult.class);
                        acctCountApply.setCostInfo(costInfoResult);
                        applyList.add(acctCountApply);
                    }
                }else {
                    AcctCountApplyRespDTO acctCountApply = new AcctCountApplyRespDTO();
                    acctCountApply.setBizNo(apply.getBizNo());
                    acctCountApply.setCreateTime(apply.getCreateTime());
                    Integer cardModel = apply.getCardModel();
                    if (2 == cardModel) {
                        BankPetty bankPetty = bankPettyManager.queryBankPettyByBizNo(apply.getBizNo());
                        if (ObjUtils.isNotEmpty(bankPetty)) {
                            Integer pettyType = bankPetty.getPettyType();
                            switch (pettyType) {
                                case 1:
                                    acctCountApply.setType(ApplyTypeEnum.PETTY_APPLY.getCode());
                                    break;
                                case 2:
                                    acctCountApply.setType(ApplyTypeEnum.ROUND_PETTY_APPLY.getCode());
                                    break;
                                case 3:
                                    acctCountApply.setType(ApplyTypeEnum.ONE_PETTY_APPLY.getCode());
                                    break;
                            }
                        }
                    }else {
                        acctCountApply.setType(ApplyTypeEnum.VIRTUAL_APPLY.getCode());
                    }
                    acctCountApply.setRemark(apply.getRemark());
                    acctCountApply.setApplyAmount(BigDecimalUtils.fenToYuan(apply.getApplyAmount()));
                    acctCountApply.setDeductionAmount(BigDecimalUtils.fenToYuan(apply.getApplyAmount()));
                    acctCountApply.setEmployeeId(apply.getEmployeeId());
                    EmployeeContract employeeContract = getEmployeeContract(apply.getEmployeeId(), apply.getCompanyId());
                    acctCountApply.setOperationUserName(ObjUtils.isEmpty(employeeContract) ? "" : employeeContract.getName());
                    //上线改成批量获取(rpc接口待提供)
                    CostInfoResult costInfo = iOrderCostService.queryCostInfoByApplyOrderId(apply.getBizNo());
                    com.fenbeitong.finhub.common.saas.entity.CostInfoResult costInfoResult = ModelConverterUtils.convert(costInfo, com.fenbeitong.finhub.common.saas.entity.CostInfoResult.class);
                    acctCountApply.setCostInfo(costInfoResult);
                    FinhubLogger.info("其它的情况，bizNo={},costInfo={}", apply.getBizNo(),Objects.isNull(costInfo));
                    applyList.add(acctCountApply);
                }
            });
            accountVirtual.setApplyList(applyList);
        }
        finalResList.add(accountVirtual);
    }

    private EmployeeContract getEmployeeContract(String employeeId, String companyId) {
        try {
            return iBaseEmployeeExtService.queryEmployeeInfo(employeeId, companyId);
        } catch (Exception e){
            FinhubLogger.warn("获取用户信息失败,employeeId={},companyId={}",employeeId, companyId, e);
            return null;
        }

    }

    @Override
    public AccountVirtualFlowRespDTO queryAccountBigDataDetailFlow(String accountFlowId) {
        FinhubLogger.info("queryAccountPageFlow入参accountFlowId={}", accountFlowId);
        AccountVirtualFlowRespDTO res = new AccountVirtualFlowRespDTO();
        List<AccountVirtualFlowRespDTO> resList = new ArrayList<>();
        //根据条件查询大数据虚拟卡账户流水
        AccountAllFlowReqDTO req = new AccountAllFlowReqDTO();
        req.setAccountFlowId(accountFlowId);
        ResponsePage<AccountAllFlow> accountFlow= accountAllFlowService.queryPage(req);
        List<AccountAllFlow> accountFlowDataList = accountFlow.getDataList();
        if (CollectionUtils.isEmpty(accountFlowDataList)) {
            return res;
        }
        accountFlowDataList.forEach(item -> {
            //operationType=80 额度申请，金额是负数，需要转成正数？，普通申请单在tb_bank_card_credit_apply表operation_channel=6是循环备用金子单
            //1 额度申请
            if (CompanyCardOperationType.FROZEN_BANK_RECALL.getCode().equals(item.getOperationType())) {
                operaApply(resList, item);
            }
            //2 额度退回
            if (CompanyCardOperationType.FROZEN_BANK_REFUND.getCode().equals(item.getOperationType())) {
                operaRefundFlow(resList, item);
            }
        });
        getMeaningNo(resList);
        return CollectionUtils.isEmpty(resList) ? res : resList.get(0);
    }
    @Override
    public AccountVirtualFlowRespDTO queryAccountDetailFlow(String accountFlowId) {
        FinhubLogger.info("queryAccountPageFlow入参accountFlowId={}", accountFlowId);
//        ResponsePage<AccountVirtualFlowRespDTO> res = new ResponsePage<>();
        AccountVirtualFlowRespDTO res = new AccountVirtualFlowRespDTO();
        List<AccountVirtualFlowRespDTO> resList = new ArrayList<>();

        String companyId = "";
        AcctCompanyCardFlow acctCompanyCardFlow = acctCompanyCardFlowService.queryByFlowId(accountFlowId);
        AcctBusinessDebitFlow acctBusinessDebitFlow = acctBusinessDebitFlowService.queryAccountSubFlowByFlowId(accountFlowId);
        AcctBusinessCreditFlow acctBusinessCreditFlow = acctBusinessCreditFlowService.queryAccountSubFlowByFlowId(accountFlowId);

        if (ObjUtils.isNotEmpty(acctCompanyCardFlow)) {
            companyId = acctCompanyCardFlow.getCompanyId();
            AccountVirtualFlowRespDTO acctCompanyCardFlowDto = ModelConverterUtils.convert(acctCompanyCardFlow, AccountVirtualFlowRespDTO.class);
            resList.add(acctCompanyCardFlowDto);
        }
        if (ObjUtils.isNotEmpty(acctBusinessDebitFlow)) {
            companyId = acctBusinessDebitFlow.getCompanyId();
            AccountVirtualFlowRespDTO acctBusinessDebitFlowDto = ModelConverterUtils.convert(acctBusinessDebitFlow, AccountVirtualFlowRespDTO.class);
            resList.add(acctBusinessDebitFlowDto);
        }
        if (ObjUtils.isNotEmpty(acctBusinessCreditFlow)) {
            companyId = acctBusinessCreditFlow.getCompanyId();
            AccountVirtualFlowRespDTO acctBusinessCreditFlowDto = ModelConverterUtils.convert(acctBusinessCreditFlow, AccountVirtualFlowRespDTO.class);
            resList.add(acctBusinessCreditFlowDto);
        }
        List<AccountAllFlow> accountFlowDataList = ModelConverterUtils.convert(resList, AccountAllFlow.class);
        Map<String, AccountVirtualFlowRespDTO> resMap = resList.stream().collect(Collectors.toMap(AccountVirtualFlowRespDTO::getAccountFlowId, Function.identity(),(o,a) ->a));
        List<AccountVirtualFlowRespDTO> resList2 = new ArrayList<>();
        accountFlowDataList.forEach(item -> {
            item.setBizId(resMap.get(item.getAccountFlowId()).getBizNo());
            //operationType=80 额度申请，金额是负数，需要转成正数？，普通申请单在tb_bank_card_credit_apply表operation_channel=6是循环备用金子单
            //1 额度申请
            if (CompanyCardOperationType.FROZEN_BANK_RECALL.getCode().equals(item.getOperationType())) {
                operaApply(resList2, item);
            }
            //2 额度退回
            if (CompanyCardOperationType.FROZEN_BANK_REFUND.getCode().equals(item.getOperationType())) {
                operaRefundFlow(resList2, item);
            }
        });
        //先过滤掉匹配不到申请单的数据
        List<AccountVirtualFlowRespDTO> collect1 = resList2.stream().filter(item -> {
            if (CollectionUtils.isNotEmpty(item.getApplyList())) {
                List<AcctCountApplyRespDTO> applyList = item.getApplyList();
                List<AcctCountApplyRespDTO> collect = applyList.stream().filter(apply -> ObjUtils.isNotEmpty(apply.getCostInfo())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect1)) {
            res = collect1.get(0);
        }
        List<AccountVirtualFlowRespDTO> finalResList = new ArrayList<>();
        finalResList.add(res);
        getMeaningNo(finalResList);
        return CollectionUtils.isEmpty(finalResList) ? res : finalResList.get(0);
    }

    @Override
    public ResponsePage<AccountVirtualFlowRespDTO> queryAccountPageFlow(AccountVirtualFlowReqRPCDTO reqDto) {
        try {
            FinhubLogger.info("queryAccountPageFlow入参reqDto={}", JSON.toJSONString(reqDto));
            ResponsePage<AccountVirtualFlowRespDTO> res = new ResponsePage<>();
            List<AccountVirtualFlowRespDTO> resList = new ArrayList<>();
            String companyId = reqDto.getCompanyId();
            Date startTime = reqDto.getStartTime();
            Date endTime = reqDto.getEndTime();
            Integer pageNo = reqDto.getPageNo();
            Integer pageSize = reqDto.getPageSize();


            AcctOptFlowPageDTO req = new AcctOptFlowPageDTO();
            req.setCompanyId(companyId);
            req.setPageSize(pageSize);
            req.setPageNo(pageNo);
            req.setIfPageQuery(true);
            req.setStartTime(startTime);
            req.setEndTime(endTime);
            //暂定额度发放，额度退回
            List<Integer> operationTypes = new ArrayList<>();
            operationTypes.add(FundAcctCreditOptType.FROZEN_BANK_RECALL.getKey());
            operationTypes.add(FundAcctCreditOptType.FROZEN_BANK_REFUND.getKey());
            req.setOperationTypes(operationTypes);

            ResponsePage<AcctCompanyCardFlow> acctCompanyCardPage = acctCompanyCardFlowService.queryPage(req);
            ResponsePage<AcctBusinessDebitFlow> acctDebitPage = acctBusinessDebitFlowService.queryPage(req);
            ResponsePage<AcctBusinessCreditFlow> acctCreditPage = acctBusinessCreditFlowService.queryPage(req);
            List<AcctCompanyCardFlow> acctCompanyCardList = acctCompanyCardPage.getDataList();
            List<AcctBusinessDebitFlow> acctDebitList = acctDebitPage.getDataList();
            List<AcctBusinessCreditFlow> acctCreditList = acctCreditPage.getDataList();

            Integer totalCount = 0;
            if (CollectionUtils.isNotEmpty(acctCompanyCardList)) {
                List<AccountVirtualFlowRespDTO> acctCompanyCardResList = ModelConverterUtils.convert(acctCompanyCardList, AccountVirtualFlowRespDTO.class);
                resList.addAll(acctCompanyCardResList);
                totalCount = totalCount+acctCompanyCardPage.getTotalCount();
            }
            if (CollectionUtils.isNotEmpty(acctDebitList)) {
                List<AccountVirtualFlowRespDTO> acctDebitResList = ModelConverterUtils.convert(acctDebitList, AccountVirtualFlowRespDTO.class);
                resList.addAll(acctDebitResList);
                totalCount = totalCount+acctDebitPage.getTotalCount();
            }
            if (CollectionUtils.isNotEmpty(acctCreditList)) {
                List<AccountVirtualFlowRespDTO> acctCreditResList = ModelConverterUtils.convert(acctCreditList, AccountVirtualFlowRespDTO.class);
                resList.addAll(acctCreditResList);
                totalCount = totalCount+acctCreditPage.getTotalCount();
            }
            List<AccountAllFlow> accountFlowDataList = ModelConverterUtils.convert(resList, AccountAllFlow.class);
            Map<String, AccountVirtualFlowRespDTO> resMap = resList.stream().collect(Collectors.toMap(AccountVirtualFlowRespDTO::getAccountFlowId, Function.identity(),(o,a) ->a));
            List<AccountVirtualFlowRespDTO> resList2 = new ArrayList<>();
            accountFlowDataList.forEach(item -> {
                item.setBizId(resMap.get(item.getAccountFlowId()).getBizNo());
                //operationType=80 额度申请，金额是负数，需要转成正数？，普通申请单在tb_bank_card_credit_apply表operation_channel=6是循环备用金子单
                //1 额度申请
                if (CompanyCardOperationType.FROZEN_BANK_RECALL.getCode().equals(item.getOperationType())) {
                    operaApply(resList2, item);
                }
                //2 额度退回
                if (CompanyCardOperationType.FROZEN_BANK_REFUND.getCode().equals(item.getOperationType())) {
                    operaRefundFlow(resList2, item);
                }
            });
            //先过滤掉匹配不到申请单的数据
            List<AccountVirtualFlowRespDTO> collect1 = resList2.stream().filter(item -> {
                if (CollectionUtils.isNotEmpty(item.getApplyList())) {
                    List<AcctCountApplyRespDTO> applyList = item.getApplyList();
                    List<AcctCountApplyRespDTO> collect = applyList.stream().filter(apply -> ObjUtils.isNotEmpty(apply.getCostInfo())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }).collect(Collectors.toList());
            getMeaningNo(collect1);
            res.setDataList(collect1);
            res.setTotalCount(totalCount);
            FinhubLogger.info("queryAccountPageFlow入参reqDto={},totalCount={},collect1Size={},resList2Size={}", JSON.toJSONString(reqDto), totalCount,collect1.size(),resList2.size());
            return res;
        } catch (FinhubException e){
            FinhubLogger.warn("queryAccountPageFlow warn", e);
            throw e;
        } catch (Exception e){
            FinhubLogger.error("queryAccountPageFlow eror", e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.EXCEPTION);
        }
    }
    private void getMeaningNo(List<AccountVirtualFlowRespDTO> finalResList) {
        Set<String> set = getBizNoByRes(finalResList);
        if (set.size()>0) {
            //调用saasrpc接口查询meaningNo
            List<String> applyIds = Lists.newArrayList(set);
            List<ApplyOrderVoucherResDTO> meaningList = iApplyOrderService.getMeaningNoById(applyIds);
            if (CollectionUtils.isNotEmpty(meaningList)) {
                Map<String, ApplyOrderVoucherResDTO> applyMeaningMap = meaningList.stream().collect(Collectors.toMap(ApplyOrderVoucherResDTO::getId, Function.identity()));
                applyMeaningMap = applyMeaningMap == null ? new HashMap<>() : applyMeaningMap;
                Map<String, ApplyOrderVoucherResDTO> finalApplyMeaningMap = applyMeaningMap;
                finalResList.forEach(item ->{
                    List<AcctCountApplyRespDTO> applyList = item.getApplyList();
                    if (CollectionUtils.isNotEmpty(applyList)) {
                        applyList.forEach(apply ->{
                            ApplyOrderVoucherResDTO applyOrderVoucherResDTO = finalApplyMeaningMap.get(apply.getBizNo());
                            if (ObjUtils.isNotEmpty(applyOrderVoucherResDTO)) {
                                apply.setMeaningNo(applyOrderVoucherResDTO.getMeaningNo());
                                apply.setApplyOrderType(applyOrderVoucherResDTO.getApplyOrderType());
                                apply.setOrgId(applyOrderVoucherResDTO.getOrgId());
                                if (ObjUtils.isBlank(apply.getReason())) {
                                    apply.setReason(applyOrderVoucherResDTO.getApplyReason());
                                }
                                com.fenbeitong.finhub.common.saas.entity.CostInfoResult costInfo = apply.getCostInfo();
                                if (ObjUtils.isEmpty(costInfo)) {
                                    com.fenbeitong.finhub.common.saas.entity.CostInfoResult costInfo2 = new com.fenbeitong.finhub.common.saas.entity.CostInfoResult();
                                    String cateGoryId = String.valueOf(applyOrderVoucherResDTO.getCostAttributionCategory());
                                    costInfo2.setCostCategoryId(cateGoryId);
                                    CostAttributionCategory category = CostAttributionCategory.getByKey(applyOrderVoucherResDTO.getCostAttributionCategory());
                                    String categoryName = category == null ? "" : category.getValue();
                                    costInfo2.setCostCategory(categoryName);
                                    List<CostAttributionGroup> costAttributionGroupList = new ArrayList<>();
                                    CostAttributionGroup costAttributionGroup = new CostAttributionGroup();
                                    costAttributionGroup.setCategory(applyOrderVoucherResDTO.getCostAttributionCategory());
                                    costAttributionGroup.setCategoryName(categoryName);

                                    List<CostAttribution> costAttributionList = new ArrayList<>();
                                    CostAttribution costAttribution = new CostAttribution();
                                    costAttribution.setId(applyOrderVoucherResDTO.getCostAttributionId());
                                    costAttribution.setName(applyOrderVoucherResDTO.getCostAttributionName());
                                    costAttributionList.add(costAttribution);
                                    costAttributionGroup.setCostAttributionList(costAttributionList);
                                    costAttributionGroupList.add(costAttributionGroup);
                                    costInfo2.setCostAttributionGroupList(costAttributionGroupList);
                                    apply.setCostInfo(costInfo2);
                                }
                            }
                        });
                    }
                });
            }
        }
    }

    private Set<String> getBizNoByRes(List<AccountVirtualFlowRespDTO> finalResList) {
        Set<String> set = new HashSet<>();
        if (CollectionUtils.isNotEmpty(finalResList)) {
            finalResList.forEach(item ->{
                List<AcctCountApplyRespDTO> applyList = item.getApplyList();
                if (CollectionUtils.isNotEmpty(applyList)) {
                    applyList.forEach(apply ->{
                        String bizNo = apply.getBizNo();
                        if (ObjUtils.isNotEmpty(bizNo)) {
                            set.add(bizNo);
                        }
                    });
                }
            });
        }
        return set;
    }
    private void refundPettyModel(AccountVirtualFlowRespDTO item,List<AcctCountApplyRespDTO> applyList, String applyTransNo) {
        List<BankCardApplyFlow> bankCardApplyFlowList = bankCardApplyFlowManger.queryBankCardFlowByBizNo(item.getBizNo());
        if (CollectionUtils.isEmpty(bankCardApplyFlowList)){
            if (!StringUtils.isBlank(applyTransNo)){
                //兼容查询退款的时候，卡流水里面bizNo是applyTransNo
                bankCardApplyFlowList = bankCardApplyFlowManger.queryBankCardFlowByBizNo(applyTransNo);
            }
        }
        FinhubLogger.info("refundPettyModel 查询流水结果，bizNo={},applyTransNo={},resultEmpty={}", item.getBizNo(),applyTransNo,CollectionUtils.isEmpty(bankCardApplyFlowList));
        if (ObjUtils.isNotEmpty(bankCardApplyFlowList)) {
            BankCardApplyFlow bankCardApplyFlow = bankCardApplyFlowList.get(0);
            String pettyId = bankCardApplyFlow.getPettyId();
            if (ObjUtils.isNotEmpty(pettyId)) {
                //备用金
                BankPetty bankPetty = bankPettyManager.queryBankPettyPettyId(pettyId);
                if (ObjUtils.isNotEmpty(bankPetty)) {
                    AcctCountApplyRespDTO acctCountApply = new AcctCountApplyRespDTO();
                    acctCountApply.setBizNo(bankPetty.getBizNo());
                    acctCountApply.setApplyAmount(BigDecimalUtils.fenToYuan(bankPetty.getApplySum()));
                    acctCountApply.setDeductionAmount(item.getOperationAmount());
                    acctCountApply.setEmployeeId(bankPetty.getEmployeeId());
                    acctCountApply.setOperationUserName(bankPetty.getEmployeeName());
                    acctCountApply.setCreateTime(bankPetty.getCreateTime());
                    Integer pettyType = bankPetty.getPettyType();
                    switch (pettyType) {
                        case 1:
                            acctCountApply.setType(ApplyTypeEnum.PETTY_APPLY.getCode());
                            break;
                        case 2:
                            acctCountApply.setType(ApplyTypeEnum.ROUND_PETTY_APPLY.getCode());
                            break;
                        case 3:
                            acctCountApply.setType(ApplyTypeEnum.ONE_PETTY_APPLY.getCode());
                            break;
                    }
                    acctCountApply.setRemark(bankCardApplyFlow.getApplyReason());
                    CostInfoResult costInfo = iOrderCostService.queryCostInfoByApplyOrderId(bankPetty.getBizNo());
                    com.fenbeitong.finhub.common.saas.entity.CostInfoResult costInfoResult = ModelConverterUtils.convert(costInfo, com.fenbeitong.finhub.common.saas.entity.CostInfoResult.class);
                    acctCountApply.setCostInfo(costInfoResult);
                    applyList.add(acctCountApply);
                    item.setApplyList(applyList);
                }
            }else {
               //退款  找不到申请单的普通模式
                String companyId = bankCardApplyFlow.getCompanyId();
                String employeeId = bankCardApplyFlow.getEmployeeId();
                List<BankCardCreditApply> bankCardCreditApplyList = creditApplyOperationManager.queryByCompanyIdAndEmployeeId(companyId, employeeId, BankApplyCreditType.APPLY_CREDIT.getKey(),item.getCreateTime(),bankCardApplyFlow.getOperationAmount());
                if (CollectionUtils.isNotEmpty(bankCardCreditApplyList)) {

                    BankCardCreditApply bankCardCreditApply = bankCardCreditApplyList.get(0);
                    AcctCountApplyRespDTO acctCountApply = new AcctCountApplyRespDTO();
                    acctCountApply.setBizNo(bankCardCreditApply.getBizNo());
                    acctCountApply.setApplyAmount(BigDecimalUtils.fenToYuan(bankCardCreditApply.getApplyAmount()));
                    acctCountApply.setDeductionAmount(item.getOperationAmount());
                    acctCountApply.setEmployeeId(employeeId);
                    acctCountApply.setOperationUserName(bankCardApplyFlow.getOperationUserName());
                    acctCountApply.setCreateTime(bankCardCreditApply.getCreateTime());
                    acctCountApply.setType(ApplyTypeEnum.VIRTUAL_APPLY.getCode());
                    acctCountApply.setRemark(bankCardCreditApply.getRemark());
                    CostInfoResult costInfo = iOrderCostService.queryCostInfoByApplyOrderId(bankCardCreditApply.getBizNo());
                    if (ObjUtils.isNotEmpty(costInfo)) {
                        com.fenbeitong.finhub.common.saas.entity.CostInfoResult costInfoResult = ModelConverterUtils.convert(costInfo, com.fenbeitong.finhub.common.saas.entity.CostInfoResult.class);
                        acctCountApply.setCostInfo(costInfoResult);
                    }
                    applyList.add(acctCountApply);
                    item.setApplyList(applyList);
                }
            }
        }
    }

    private AccountAllFlowReqDTO buildQueryData(AccountVirtualFlowReqRPCDTO reqDto) {
        AccountAllFlowReqDTO req = new AccountAllFlowReqDTO();
        //只查询虚拟卡的账户流水数据
        req.setFlowFlag(6);
        req.setCompanyId(reqDto.getCompanyId());
        req.setStartTime(reqDto.getStartTime());
        req.setEndTime(reqDto.getEndTime());
        req.setPageNo(reqDto.getPageNo());
        req.setPageSize(reqDto.getPageSize());
        //暂定额度发放，额度退回
        List<Integer> operationTypes = new ArrayList<>();
        operationTypes.add(FundAcctCreditOptType.FROZEN_BANK_RECALL.getKey());
        operationTypes.add(FundAcctCreditOptType.FROZEN_BANK_REFUND.getKey());
        req.setOperationTypes(operationTypes);
        return req;
    }

    @Override
    public List<AcctGeneralFlowDTO> queryByFlowIds(List<String> accountFlowIds) {
        List<AccountGeneralFlow> accountGeneralFlows = accountGeneralFlowService.queryByFlowIds(accountFlowIds);
        if(CollectionUtils.isEmpty(accountGeneralFlows)){
            return Lists.newArrayList();
        }
        return accountGeneralFlows.stream().map(item -> {
            AcctGeneralFlowDTO respRPCDTO = new AcctGeneralFlowDTO();
            BeanUtils.copyProperties(item, respRPCDTO);
            return respRPCDTO;
        }).collect(Collectors.toList());
    }
    
    @Override
    public AcctGeneralFlowDTO queryByFlowId(String accountFlowId) {
        AccountGeneralFlow flow = accountGeneralFlowService.queryByFlowId(accountFlowId);
        AcctGeneralFlowDTO respRPCDTO = new AcctGeneralFlowDTO();
        BeanUtils.copyProperties(flow, respRPCDTO);
        respRPCDTO.setAccountId(flow.getAccountGeneralId());
        respRPCDTO.setAccountSubType(FundAccountSubType.GENERAL_ACCOUNT.getKey());
        return respRPCDTO;
    }

    @Override
    public List<AcctGeneralFlowDTO> queryOtherAcctFlowByFlowIds(List<OtherAcctFlowQueryReqDTO> flowQueryReqDTOs) {
        List<AcctGeneralFlowDTO> resultList = Lists.newArrayList();

        Map<Integer, List<OtherAcctFlowQueryReqDTO>> flowCollect = flowQueryReqDTOs.stream().collect(Collectors.groupingBy(OtherAcctFlowQueryReqDTO::getAccountSubType));
        //暂时只处理商务消费账户，补助福利账户，备用金账户流水
        List<OtherAcctFlowQueryReqDTO> businessAcctFlowQueryReq = flowCollect.get(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        if(CollectionUtils.isNotEmpty(businessAcctFlowQueryReq)){
            List<String> businessAcctFlowIds = businessAcctFlowQueryReq.stream().map(OtherAcctFlowQueryReqDTO::getAccountFlowId).collect(Collectors.toList());
            Example example = new Example(AcctBusinessDebitFlow.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("accountFlowId", businessAcctFlowIds);
            List<AcctBusinessDebitFlow> acctBusinessDebitFlows = acctBusinessDebitFlowExtMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(acctBusinessDebitFlows)){
                List<AcctGeneralFlowDTO> businessList = acctBusinessDebitFlows.stream().map(item -> {
                    AcctGeneralFlowDTO respRPCDTO = new AcctGeneralFlowDTO();
                    BeanUtils.copyProperties(item, respRPCDTO);
                    return respRPCDTO;
                }).collect(Collectors.toList());
                resultList.addAll(businessList);
            }
        }
        List<OtherAcctFlowQueryReqDTO> individualAcctFlowQueryReq = flowCollect.get(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
        if(CollectionUtils.isNotEmpty(individualAcctFlowQueryReq)){
            List<String> individualAcctFlowIds = individualAcctFlowQueryReq.stream().map(OtherAcctFlowQueryReqDTO::getAccountFlowId).collect(Collectors.toList());
            Example example = new Example(AcctIndividualDebitFlow.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("accountFlowId", individualAcctFlowIds);
            List<AcctIndividualDebitFlow> acctIndividualDebitFlows = acctIndividualDebitFlowMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(acctIndividualDebitFlows)){
                List<AcctGeneralFlowDTO> individualList = acctIndividualDebitFlows.stream().map(item -> {
                    AcctGeneralFlowDTO respRPCDTO = new AcctGeneralFlowDTO();
                    BeanUtils.copyProperties(item, respRPCDTO);
                    return respRPCDTO;
                }).collect(Collectors.toList());
                resultList.addAll(individualList);
            }
        }

        List<OtherAcctFlowQueryReqDTO> virtualAcctFlowQueryReq = flowCollect.get(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
        if(CollectionUtils.isNotEmpty(virtualAcctFlowQueryReq)){
            List<String> vivirtualAcctFlowIds = virtualAcctFlowQueryReq.stream().map(OtherAcctFlowQueryReqDTO::getAccountFlowId).collect(Collectors.toList());
            Example example = new Example(AcctCompanyCardFlow.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("accountFlowId", vivirtualAcctFlowIds);
            List<AcctCompanyCardFlow> acctCompanyCardFlows = acctCompanyCardFlowExtMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(acctCompanyCardFlows)){
                List<AcctGeneralFlowDTO> individualList = acctCompanyCardFlows.stream().map(item -> {
                    AcctGeneralFlowDTO respRPCDTO = new AcctGeneralFlowDTO();
                    BeanUtils.copyProperties(item, respRPCDTO);
                    return respRPCDTO;
                }).collect(Collectors.toList());
                resultList.addAll(individualList);
            }
        }

        return resultList;
    }

}
