package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.*;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementV2Service;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementV2Service;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 收银接口V2
 * <AUTHOR>
 */
@Service("iCashierOrderSettlementV2Service")
public class ICashierOrderSettlementV2ServiceImpl implements ICashierOrderSettlementV2Service {
    @Autowired
    CashierOrderSettlementV2Service cashierOrderSettlementV2Service;
    
    @Override
    public CashierCreateRespVO create(CashierCreateReqVO cashierCreateRPCVO) {
        return null;
    }

    @Override
    public CashierConfirmPayRespVO pay(CashierConfirmPayReqVO cashierPayReqVO) {
        return null;
    }

    /**
     * 创建并支付请求
     * @param cashierCreateAndPayReqVO 创建并支付
     * @return CashierCreateAndPayRespVO
     */
    @Override
    public CashierCreateAndPayRespVO createAndPay(CashierCreateAndPayReqVO cashierCreateAndPayReqVO) {
        try{
            return cashierOrderSettlementV2Service.createAndPay(cashierCreateAndPayReqVO);
        }catch (FinPayException e){
            throw new FinhubException(e.getCode());
        }
    }

    /**
     * 批量创建并支付请求
     * @param cashierCreateAndPayBatchReqVO
     * @return
     */
    @Override
    public CashierCreateAndPayBatchRespVO createAndPayBatch(CashierCreateAndPayBatchReqVO cashierCreateAndPayBatchReqVO) {
        try{
            return cashierOrderSettlementV2Service.createAndPayBatch(cashierCreateAndPayBatchReqVO);
        }catch (FinPayException e){
            throw new FinhubException(e.getCode());
        }
    }

    @Override
    public CashierConfirmPayRespVO confirmPay(CashierConfirmPayReqVO cashierPayReqVO) {
        return cashierOrderSettlementV2Service.confirmPay(cashierPayReqVO);
    }

    @Override
    public void cancel() {

    }
    
    @Override
    public CashierQueryRespVO query(CashierQueryReqVO cashierQueryReqVO) {
        return cashierOrderSettlementV2Service.query(cashierQueryReqVO);
    }

    @Override
    public CashierDishonouredRespVO dishonoured(CashierDishonouredReqVO cashierDishonouredReqVO) {
        return cashierOrderSettlementV2Service.dishonoured(cashierDishonouredReqVO);
    }

    @Override
    public CashierSalaryDishonouredRespVO salaryDishonoured(CashierSalaryDishonouredReqVO cashierSalaryDishonouredReqVO) {
        return cashierOrderSettlementV2Service.salaryDishonoured(cashierSalaryDishonouredReqVO);
    }

    @Override
    public CashierNotifyRespVO notify(CashierNotifyReqVO cashierNotifyReqVO) {
        return cashierOrderSettlementV2Service.notify(cashierNotifyReqVO);
    }

    @Override
    public CashierBatchNotifyRespVO batchNotify(CashierBatchNotifyReqVO cashierBatchNotifyReqVO) {
        return cashierOrderSettlementV2Service.batchNotify(cashierBatchNotifyReqVO);
    }

    @Override
    public void payByAsync(String batchNo) {
        cashierOrderSettlementV2Service.payByAsync(batchNo);
    }

    @Override
    public void asyncPayByCron() {
        cashierOrderSettlementV2Service.asyncPayByCron();
    }

    @Override
    public void asyncNotifyByCron() {
        cashierOrderSettlementV2Service.asyncNotifyByCron();
    }

    @Override
    public void queryByAsync(String batchNo) {
        cashierOrderSettlementV2Service.queryByAsync(batchNo);
    }

    @Override
    public void queryByCron() {
        cashierOrderSettlementV2Service.queryByCron();
    }
}
