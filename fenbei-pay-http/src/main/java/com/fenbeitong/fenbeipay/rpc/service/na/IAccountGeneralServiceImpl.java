package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicDechService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctChangeNameReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCreateSubDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctGeneralDefTranReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferCommonReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCreditDetailRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCreditMainRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByComIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwBySubAndBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.AccountRedcouponRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountGeneralVO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountGeneralService;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcoupon;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountGeneralService;
import com.fenbeitong.fenbeipay.redcoupon.service.AccountRedcouponService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import static com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType.CREDIT;
import static com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType.RECHARGE;
import static com.fenbeitong.finhub.common.constant.FundAcctActStatusEnum.ACTIVATE;
import static com.fenbeitong.finhub.common.constant.FundAcctActStatusEnum.UN_ACTIVATE;
import static com.fenbeitong.finhub.common.constant.FundAcctShowStatusEnum.SHOW;
import static com.fenbeitong.finhub.common.constant.FundAcctShowStatusEnum.UN_SHOW;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: java类作用描述
 * @ClassName: IAccountGeneralServiceImpl
 * @Author: zhangga
 * @CreateDate: 2019/3/18 6:26 PM
 * @UpdateUser:
 * @UpdateDate: 2019/3/18 6:26 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iAccountGeneralService")
public class IAccountGeneralServiceImpl implements IAccountGeneralService {

    @Autowired
    private UAccountGeneralService uAccountGeneralService;
    @Autowired
    private UAcctGeneralService uAcctGeneralService;

    @Autowired
    private AccountGeneralFlowService accountGeneralFlowService;
    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    private AccountRedcouponService accountRedcouponService;

    @Autowired
    private IVouchersPersonService iVouchersPersonService;

    @Autowired
    private UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    private UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    private UAcctCompanyCardService uAcctCompanyCardService;

    @Autowired
    private UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    private UAcctIndividualDebitService uAcctIndividualDebitService;

    @Autowired
    private UCompanySwitchService uCompanySwitchService;

    @Autowired
    private AcctCompanyGatewayService acctCompanyGatewayService;

    @Autowired
    private AcctPublicDechService acctPublicDechService;

    @Autowired
    private DingDingMsgService dingDingMsgService;
    @Autowired
    private UAcctCommonService uAcctCommonService;

    @Autowired
    protected AcctCompanyMainService acctCompanyMainService;

    /**
     * @Description: 创建账户
     * @methodName: createAccount
     * @Param: [createDTO]
     * @return: boolean
     * @Author: zhangga
     * @Date: 2019/3/18 7:33 PM
     **/
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public boolean createAccount(AccountGeneralCreateRPCDTO createDTO) throws FinhubException {
        FinhubLogger.info("【新账户系统，创建账户】createAccount 参数：{}", createDTO);
        try {
            //校验字段
            ValidateUtils.validate(createDTO);
            String accountGeneralId = uAccountGeneralService.createAccount(createDTO);
            AccountSubEstablishReqRPCDTO accountSubEstablishDTO = new AccountSubEstablishReqRPCDTO();
            BeanUtils.copyProperties(createDTO, accountSubEstablishDTO);
            accountSubEstablishDTO.setAccountGeneralId(accountGeneralId);
            //新账户体系 商务账户 个人账户 都创建充值 授信
            List<AccountSubModelDTO> subList = createDTO.getSubList();
            if (CollectionUtils.isNotEmpty(subList)) {
                for (AccountSubModelDTO subModelDTO : subList) {
                    accountSubEstablishDTO.setAccountModel(subModelDTO.getAccountModel());
                    accountSubEstablishDTO.setAccountSubType(subModelDTO.getAccountSubType());
                    accountSubEstablishDTO.setActiveStatus(subModelDTO.getActiveStatus());
                    accountSubEstablishDTO.setShowStatus(FundAcctActStatusEnum.isAct(subModelDTO.getActiveStatus()) ? SHOW.getStatus() : UN_SHOW.getStatus());
                    accountSubEstablishDTO.setCompanyModel(createDTO.getCompanyModel());
                    accountSubEstablishDTO.setCompanyMainId(accountSubEstablishDTO.getCompanyId());
                    accountSubEstablishDTO.setCompanyMainType(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey());
                    accountSubEstablishDTO.setBankAccountNo(accountSubEstablishDTO.getCompanyId());
                    accountSubEstablishDTO.setBankName(BankNameEnum.FBT.getCode());
                    saveAccountSub(accountSubEstablishDTO);
                }
            } else {
                //440兼容老版本
                //创建商务账户
                accountSubEstablishDTO.setAccountModel(createDTO.getAccountModel());
                accountSubEstablishDTO.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                accountSubEstablishDTO.setShowStatus(SHOW.getStatus());
                AcctCreateSubDTO acctCreateSubDTO = new AcctCreateSubDTO();
                BeanUtils.copyProperties(accountSubEstablishDTO, acctCreateSubDTO);
                acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.UN_ABLE.getStatus());
                acctCreateSubDTO.setCompanyMainId(accountSubEstablishDTO.getCompanyId());
                acctCreateSubDTO.setCompanyMainType(CompanyMainTypeEnum.COMPANY_MAIN_SELF.getKey());
                acctCreateSubDTO.setBankAccountNo(accountSubEstablishDTO.getCompanyId());
                acctCreateSubDTO.setBankName(BankNameEnum.FBT.getCode());
                if (FundAccountModelType.isCredit(accountSubEstablishDTO.getAccountModel())) {
                    uAcctBusinessCreditService.establishAccount(acctCreateSubDTO);
                } else if (FundAccountModelType.isRecharge(accountSubEstablishDTO.getAccountModel())) {
                    uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
                }
                //创建个人账户
                accountSubEstablishDTO.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                accountSubEstablishDTO.setAccountModel(FundAccountModelType.RECHARGE.getKey());
                accountSubEstablishDTO.setShowStatus(SHOW.getStatus());
                uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);
            }
            return true;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】createAccount 参数：{}账户余额不足", createDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户系统异常】createAccount 参数：{}", createDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】createAccount 参数：{}", createDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】createAccount 参数：{}", createDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 一个企业，一个银行，一个银行卡号，创建一个可用余额账户
     *
     * @param createDTO
     * @return
     * @throws FinhubException
     */
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public boolean createGeneralAccount(AccountGeneralCreateRPCDTO createDTO) throws FinhubException {
        FinhubLogger.info("【新账户系统，创建账户】createAccount 参数：{}", createDTO);
        try {
            //校验字段
            ValidateUtils.validate(createDTO);
            String accountGeneralId = uAccountGeneralService.createAccount(createDTO);
            return true;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】createAccount 参数：{}账户余额不足", createDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户系统异常】createAccount 参数：{}", createDTO.toString());
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】createAccount 参数：{}", createDTO.toString());
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】createAccount 参数：{}", createDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public SimpleAccountGeneralInfoResp querySimpleAccountGeneralInfo(String companyId, String bankName) {
        try {
            if (StringUtils.isEmpty(companyId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            // ******** 众邦改版后，调账业务只能包含FBT和ZBBANK账户（改版前只能至FBT）
            if (StringUtils.isEmpty(bankName) ) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            // 局部重构，复用
            AccountGeneral accountGeneral = this.uAcctGeneralService.queryAccountGeneral(companyId, bankName);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST_ADJUST);
            }

            SimpleAccountGeneralInfoResp resp = new SimpleAccountGeneralInfoResp();
            resp.setAccountId(accountGeneral.getAccountGeneralId());
            resp.setCompanyId(companyId);
            resp.setCompanyName(accountGeneral.getCompanyName());
            resp.setBalance(accountGeneral.getBalance());
            resp.setAccountStatus(accountGeneral.getAccountStatus());
            return resp;
        } catch (Exception e) {
            FinhubLogger.error("error while invoking querySimpleAccountGeneralInfo(), companyId:{}, bankName:{}, errormsg:{}", companyId, bankName, e.getMessage(), e);
            if (e instanceof FinPayException) {
                throw new FinhubException(((FinPayException) e).getCode(), ((FinPayException) e).getType(), e.getMessage());
            } else {
                throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
            }
        }
    }

    /**
     * @Description: 查询账户详情
     * @methodName: queryAccountGeneralInfo
     * @Param: [companyId]
     * @return: com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountGeneralRespRPCDTO
     * @Author: zhangga
     * @Date: 2019/3/18 7:34 PM
     **/
    @Override
    public AccountGeneralRespRPCDTO queryAccountGeneralInfo(String companyId) throws FinAccountNoEnoughException {
        try {
            if (StringUtils.isEmpty(companyId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }

            //总账户
            AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
            accountGeneralBankRPCDTO.setBankNo(companyId);
            accountGeneralBankRPCDTO.setCompanyId(companyId);
            accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            PageBean pageBean = new PageBean();
            //总流水
            List<AccountGeneralFlowRespRPCDTO> flowRespRPCDTOS = accountGeneralFlowService.getByCompanyIdAndBankNamePageBeanDTO(accountGeneral.getAccountGeneralId(), pageBean);

            AcctComGwByComIdReqDTO acctComGwByComIdReqDTO = new AcctComGwByComIdReqDTO();
            acctComGwByComIdReqDTO.setCompanyId(companyId);
            List<AcctCommonBaseDTO> accountSubList = acctCompanyGatewayService.findCommonByCompanyId(acctComGwByComIdReqDTO);
            List<AccountSubRespDTO> accountSubVoList = new ArrayList<>();
            accountSubList.forEach(accountSub -> {
                AccountSubRespDTO vo = getAccountBaseRespDTO(accountSub, companyId);
                accountSubVoList.add(vo);
            });
            AccountGeneralRespRPCDTO dto = new AccountGeneralRespRPCDTO();
            BeanUtils.copyProperties(accountGeneral, dto);
            dto.setAccountRedcouponRespRPCDTO(getAccountRedcouponRespRPCDTO(companyId));
            dto.setAccountGeneralFlowList(flowRespRPCDTOS);
            dto.setAccountSubList(accountSubVoList);
            BigDecimal availableVoucherBalance = iVouchersPersonService.queryAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(companyId, null, null);
            dto.setAvailableVoucherAmount(availableVoucherBalance);
            // 临时额度参数构建
            dto = buildTempAmount(dto, accountSubList);
            return dto;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】queryAccountGeneralInfo 参数：{}账户余额不足", companyId, accountEx);
            throw accountEx;
        } catch (FinPayException e) {
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】queryAccountGeneralInfo 参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @Description: 查询账户详情
     * @methodName: queryCompanyAccountInfo
     * @Param: [companyIds]
     * @return: java.util.List<com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountGeneralRespRPCDTO>
     * @Author: zhangga
     * @Date: 2019/3/21 4:25 PM
     **/
    @Override
    public List<AccountGeneralRespRPCDTO> queryCompanyAccountInfo(List<String> companyIds) throws FinAccountNoEnoughException {
        try {
            List<AccountGeneral> accountGenerals = uAccountGeneralService.findByCompanyIds(companyIds);
            if (CollectionUtils.isEmpty(accountGenerals)) {
                return null;
            }
            accountGenerals = accountGenerals.stream().filter(accountGeneral -> BankNameEnum.isFbt(accountGeneral.getBankName())).collect(Collectors.toList());
            List<AccountGeneralRespRPCDTO> dataList = new ArrayList<>();
            accountGenerals.forEach(accountGeneral -> {
                String companyId = accountGeneral.getCompanyId();
                AcctComGwByComIdReqDTO acctComGwByBankReqDTO = new AcctComGwByComIdReqDTO();
                acctComGwByBankReqDTO.setCompanyId(accountGeneral.getCompanyId());
                List<AcctCommonBaseDTO> accountSubList = acctCompanyGatewayService.findActCommonByComId(acctComGwByBankReqDTO);
                if (CollectionUtils.isEmpty(accountSubList)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                AccountGeneralRespRPCDTO dto = new AccountGeneralRespRPCDTO();

                BeanUtils.copyProperties(accountGeneral, dto);
                List<AccountSubRespDTO> accountSubVoList = new ArrayList<>();
                accountSubList.forEach(accountSub -> {
                    AccountSubRespDTO accountSubRespDTO = getAccountBaseRespDTO(accountSub, companyId);
                    accountSubVoList.add(accountSubRespDTO);
                });
                dto.setAccountSubList(accountSubVoList);
                dto.setAccountRedcouponRespRPCDTO(getAccountRedcouponRespRPCDTO(companyId));
                BigDecimal availableVoucherBalance = iVouchersPersonService.queryAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(companyId, null, null);
                dto.setAvailableVoucherAmount(availableVoucherBalance);
                boolean companySwitch = uCompanySwitchService.isCompanySwitch(accountGeneral.getCompanyId());
                dto.setSwitchNewAcct(companySwitch);
                dto = buildTempAmount(dto, accountSubList);
                dataList.add(dto);
            });
            return dataList;
        } catch (FinPayException e) {
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】queryCompanyAccountInfo 参数：{}", companyIds.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @Description: 主账户充值，如果accountSubType不为空，并转账到对应得子账户
     * @methodName: accountRecharge
     * @Param: [accountRechargeDTO]
     * @return: boolean
     * @Author: zhangga
     * @Date: 2019/3/18 7:34 PM
     **/
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public AccountGeneralVO accountRecharge(AccountGeneralOperationRPCDTO accountRechargeDTO) throws FinAccountNoEnoughException {
        FinhubLogger.info("【新账户系统，主账户充值】accountRechargeDTO 参数：{}", accountRechargeDTO);
        try {
            //校验字段
            ValidateUtils.validate(accountRechargeDTO);

            AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
            accountGeneralBankRPCDTO.setBankNo(accountRechargeDTO.getCompanyId());
            accountGeneralBankRPCDTO.setCompanyId(accountRechargeDTO.getCompanyId());
            accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            accountRechargeDTO.setBankNo(accountGeneral.getBankAccountNo());
            accountRechargeDTO.setBankName(accountGeneral.getBankName());
            AccountGeneralFlow accountGeneralFlow = uAccountGeneralService.accountRecharge(accountRechargeDTO);
            Integer accountSubType = accountRechargeDTO.getAccountSubType();
            if (Objects.nonNull(accountSubType) && FundAccountSubType.isSubAccount(accountSubType)) {
                AccountGeneralTransferRPCDTO accountTransferDTO = new AccountGeneralTransferRPCDTO();
                BeanUtils.copyProperties(accountRechargeDTO, accountTransferDTO);
                accountTransferDTO.setAccountSubType(accountSubType);
                accountTransferDTO.setBizNo(accountGeneralFlow.getAccountGeneralId());
                accountTransferDTO.setOperationUserName(CoreConstant.SYSTEM);
                transferOut(accountTransferDTO);
            }
            AccountGeneralVO vo = new AccountGeneralVO();
            BeanUtils.copyProperties(accountGeneralFlow, vo);
            return vo;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】accountRecharge 参数：{}账户余额不足", accountRechargeDTO.toString(), accountEx);
            dingDingMsgService.sendMsg("【新账户系统，主账户充值】充值异常:" + accountEx.getMessage() + "  accountRechargeDTO 参数：" + JsonUtils.toJson(accountRechargeDTO));
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户系统异常】accountRecharge 参数：{}", accountRechargeDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户充值】充值异常:" + e.getMessage() + "  accountRechargeDTO 参数：" + JsonUtils.toJson(accountRechargeDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】accountRecharge 参数：{}", accountRechargeDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户充值】充值异常:" + e.getMessage() + "  accountRechargeDTO 参数：" + JsonUtils.toJson(accountRechargeDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】accountRecharge 参数：{}", accountRechargeDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户充值】充值异常:" + e.getMessage() + "  accountRechargeDTO 参数：" + JsonUtils.toJson(accountRechargeDTO));
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @Description: 主账户转账到子账户
     * @methodName: transferOut
     * @Param: [accountTransferDTO]
     * @return: boolean
     * @Author: zhangga
     * @Date: 2019/3/18 7:35 PM
     **/
    @Deprecated
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public boolean transferOut(AccountGeneralTransferRPCDTO accountTransferDTO) throws FinAccountNoEnoughException {
        FinhubLogger.info("【新账户系统，主账户转账到子账户】accountTransferDTO 参数：{}", accountTransferDTO);
        try {
            //校验字段
            ValidateUtils.validate(accountTransferDTO);
            Integer accountSubType = accountTransferDTO.getAccountSubType();
            if (!FundAccountSubType.isSubAccount(accountSubType)) {
                return false;
            }
            accountTransferDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
            accountGeneralBankRPCDTO.setBankNo(accountTransferDTO.getCompanyId());
            accountGeneralBankRPCDTO.setCompanyId(accountTransferDTO.getCompanyId());
            accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            accountTransferDTO.setBankNo(accountGeneral.getBankAccountNo());
            accountTransferDTO.setBankName(accountGeneral.getBankName());
            String accountGeneralId = uAccountGeneralService.transferOut(accountTransferDTO);
            AccountSubOperationReqRPCDTO accountSubOperationRPCDTO = new AccountSubOperationReqRPCDTO();
            BeanUtils.copyProperties(accountTransferDTO, accountSubOperationRPCDTO);
            accountSubOperationRPCDTO.setAccountSubType(accountSubType);
            accountSubOperationRPCDTO.setBizNo(accountGeneralId);
            accountSubOperationRPCDTO.setAccountModel(accountTransferDTO.getAccountModelSub());
            accountSubOperationRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            accountSubOperationRPCDTO.setBankNo(accountGeneral.getBankAccountNo());
            //商务授信  商务充值   个人授信     个人充值
            AcctComGwBySubAndBankReqDTO andBankReqDTO = new AcctComGwBySubAndBankReqDTO();
            andBankReqDTO.setFundAccountType(accountSubOperationRPCDTO.getAccountSubType());
            andBankReqDTO.setBankAccountNo(accountTransferDTO.getCompanyId());
            andBankReqDTO.setCompanyId(accountTransferDTO.getCompanyId());
            andBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
            Integer accountModel = accountTransferDTO.getAccountModelSub();
            if (Objects.isNull(accountModel)) {
                accountTransferDTO.setAccountModelSub(AccountModelType.RECHARGE.getKey());
            }
            if (FundAccountSubType.isBusinessAccount(accountTransferDTO.getAccountSubType())
                    && FundAccountModelType.isRecharge(accountTransferDTO.getAccountModelSub())) {
                AcctTransferCommonReqDTO acctTransferCommonReqDTO = new AcctTransferCommonReqDTO();
                BeanUtils.copyProperties(accountSubOperationRPCDTO, acctTransferCommonReqDTO);
                acctTransferCommonReqDTO.setAccountId(accountSubOperationRPCDTO.getAccountSubId());
                acctTransferCommonReqDTO.setBankAccountNo(accountTransferDTO.getCompanyId());
                acctTransferCommonReqDTO.setBankName(BankNameEnum.FBT.getCode());
                acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
                acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
                uAcctBusinessDebitService.transferInto(acctTransferCommonReqDTO);
            } else if (FundAccountSubType.isIndividualAccount(accountTransferDTO.getAccountSubType())
                    && FundAccountModelType.isRecharge(accountTransferDTO.getAccountModelSub())) {
                AcctTransferCommonReqDTO acctTransferCommonReqDTO = new AcctTransferCommonReqDTO();
                BeanUtils.copyProperties(accountSubOperationRPCDTO, acctTransferCommonReqDTO);
                acctTransferCommonReqDTO.setAccountId(accountSubOperationRPCDTO.getAccountSubId());
                acctTransferCommonReqDTO.setBankAccountNo(accountTransferDTO.getCompanyId());
                acctTransferCommonReqDTO.setBankName(BankNameEnum.FBT.getCode());
                acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
                acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
                uAcctIndividualDebitService.transferInto(acctTransferCommonReqDTO);
            } else if (FundAccountSubType.isComCardAccount(accountTransferDTO.getAccountSubType())
                && FundAccountModelType.isRecharge(accountTransferDTO.getAccountModelSub())) {
            AcctTransferCommonReqDTO acctTransferCommonReqDTO = new AcctTransferCommonReqDTO();
            BeanUtils.copyProperties(accountSubOperationRPCDTO, acctTransferCommonReqDTO);
            acctTransferCommonReqDTO.setAccountId(accountSubOperationRPCDTO.getAccountSubId());
            acctTransferCommonReqDTO.setBankAccountNo(accountTransferDTO.getCompanyId());
            acctTransferCommonReqDTO.setBankName(BankNameEnum.FBT.getCode());
            acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
            acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
            uAcctCompanyCardService.transferInto(acctTransferCommonReqDTO);
        }
            return true;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】transferOut 参数：{}账户余额不足", accountTransferDTO.toString(), accountEx);
            dingDingMsgService.sendMsg("【新账户系统，主账户转账到子账户】转账异常:" + accountEx.getMessage() + "  accountTransferDTO 参数：" + JsonUtils.toJson(accountTransferDTO));
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户系统异常】transferOut 参数：{}", accountTransferDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户转账到子账户】转账异常:" + e.getMessage() + "  accountTransferDTO 参数：" + JsonUtils.toJson(accountTransferDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】transferOut 参数：{}", accountTransferDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户转账到子账户】转账异常:" + e.getMessage() + "   accountTransferDTO 参数：" + JsonUtils.toJson(accountTransferDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】transferOut 参数：{}", accountTransferDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户转账到子账户】转账异常:" + e.getMessage() + "  accountTransferDTO 参数：" + JsonUtils.toJson(accountTransferDTO));
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public boolean changeCompanyName(AccountChangeNameReqDTO changeNameReqDTO) throws FinAccountNoEnoughException {
        FinhubLogger.info("【新账户系统修改企业核心信息】changeNameReqDTO 参数：{}", changeNameReqDTO);
        try {
            //校验字段
//            ValidateUtils.validate(changeNameReqDTO);
            // 根据企业id查询账户信息
            List<AccountGeneral> generalList = uAccountGeneralService.findByCompanyId(changeNameReqDTO.getCompanyId());
            if (CollectionUtils.isEmpty(generalList)) {
                FinhubLogger.error("【新账户系统修改企业核心信息】未查询到账户信息  changeNameReqDTO 参数：{}", changeNameReqDTO);
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            // 先修改企业名称
            AcctChangeNameReqDTO acctChangeNameReqDTO = new AcctChangeNameReqDTO();
            acctChangeNameReqDTO.setCompanyName(changeNameReqDTO.getCompanyName());
            acctChangeNameReqDTO.setCompanyId(changeNameReqDTO.getCompanyId());
            uAcctCommonService.changeCompanyName(acctChangeNameReqDTO);
            // 主体类型是否修改判断
            // 过滤账户1+1本主体账户，如果有本主体信息，修改开户记录为其他主体，开放主体开户通道
            List<AccountGeneral> selfGeneralList = generalList.stream().filter(s -> !BankNameEnum.isFbt(s.getBankName()) && CompanyMainTypeEnum.isMainSelf(s.getCompanyMainType())).collect(Collectors.toList());
            FinhubLogger.info("【新账户系统修改企业核心信息】本主体列表：{}", JsonUtils.toJson(selfGeneralList));
            if (CollectionUtils.isNotEmpty(selfGeneralList)) {
                for (AccountGeneral selfGeneral : selfGeneralList) {
                    // 修改开户记录主体类型，开放主体开户通道
                    // 判断主体信息营业执照是否变更
                    // 查询主体信息
                    AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(selfGeneral.getCompanyId(), selfGeneral.getCompanyMainId(), selfGeneral.getBankName());
                    if (Objects.isNull(acctCompanyMain)) {
                        FinhubLogger.error("【新账户系统修改企业核心信息】本主体未查询到主体信息  selfGeneral 参数：{}", JsonUtils.toJson(selfGeneral));
                        continue;
                    }
                    if (!acctCompanyMain.getBusinessLicenseCode().equals(changeNameReqDTO.getBusinessLicenseCode())) {
                        FinhubLogger.info("【新账户系统修改企业核心信息】本主体修改开户记录为其他主体，开放主体开户通道  selfGeneral 参数：{}", JsonUtils.toJson(selfGeneral));
                        // 开户成功后，账户1+1体系存在多个本主体账户，stereo增加切换账户1+1体系主体按钮，点击切换企业主体为其他主体
                        uAcctCommonService.changeOpenAccountInfoMainType(selfGeneral, changeNameReqDTO, CompanyMainTypeEnum.COMPANY_MAIN_OTHER);
                    } else {
                        // 切换回原有本主体时，变更开户记录
                        FinhubLogger.info("【新账户系统修改企业核心信息】切换回原有本主体时，变更开户记录  selfGeneral 参数：{}", JsonUtils.toJson(selfGeneral));
                        uAcctCommonService.changeOpenAccountInfoMainType(selfGeneral, changeNameReqDTO, CompanyMainTypeEnum.COMPANY_MAIN_SELF);
                    }
                }
            }
            // 过滤账户1+1其他主体账户，如果有其他主体，且营业执照为修改后营业执照，将其他主体变为本主体
            List<AccountGeneral> otherGeneralList = generalList.stream().filter(s -> !BankNameEnum.isFbt(s.getBankName()) && CompanyMainTypeEnum.isMainOther(s.getCompanyMainType())).collect(Collectors.toList());
            FinhubLogger.info("【新账户系统修改企业核心信息】其他主体列表：{}", JsonUtils.toJson(selfGeneralList));
            if (CollectionUtils.isNotEmpty(otherGeneralList)) {
                for (AccountGeneral otherGeneral : otherGeneralList) {
                    // 查询主体信息
                    AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(otherGeneral.getCompanyId(), otherGeneral.getCompanyMainId(), otherGeneral.getBankName());
                    if (Objects.isNull(acctCompanyMain)) {
                        FinhubLogger.error("【新账户系统修改企业核心信息】其他主体未查询到主体信息  selfGeneral 参数：{}", JsonUtils.toJson(otherGeneral));
                        continue;
                    }
                    // 判断主体信息营业执照是否为变更后营业执照信息
                    if (acctCompanyMain.getBusinessLicenseCode().equals(changeNameReqDTO.getBusinessLicenseCode())) {
                        FinhubLogger.info("【新账户系统修改企业核心信息】其他主体切换为本主体  otherGeneral 参数：{}", JsonUtils.toJson(otherGeneral));
                        // 如果是其他主体创建商务、个人、虚拟卡账户
                        uAcctCommonService.otherMainCreateBusIndCardAccount(otherGeneral);
                        // 将其他主体变为本主体
                        uAcctCommonService.changeAccountMainType(otherGeneral, CompanyMainTypeEnum.COMPANY_MAIN_SELF);
                        uAcctCommonService.changeOpenAccountInfoMainType(otherGeneral, changeNameReqDTO, CompanyMainTypeEnum.COMPANY_MAIN_SELF);
                    }
                }
            }
            return true;
        } catch (FinPayException e) {
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】changeCompanyName 参数：{}", changeNameReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean defaultTransferAccount(AccountDefaultTransferRPCDTO defaultTransferAccountCDTO) throws FinhubException {
        FinhubLogger.info("【新账户系统，修改默认转账子账户】defaultTransferAccountCDTO 参数：{}", defaultTransferAccountCDTO);
        try {
            //校验字段
            ValidateUtils.validate(defaultTransferAccountCDTO);
            String companyId = defaultTransferAccountCDTO.getCompanyId();
            Integer accountSubType = defaultTransferAccountCDTO.getAccountSubType();
            AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
            accountGeneralBankRPCDTO.setBankNo(defaultTransferAccountCDTO.getCompanyId());
            accountGeneralBankRPCDTO.setCompanyId(defaultTransferAccountCDTO.getCompanyId());
            accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
            if (Objects.isNull(accountGeneral)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            AcctGeneralDefTranReqDTO accountGeneralDefTranRPCDTO = new AcctGeneralDefTranReqDTO();
            accountGeneralDefTranRPCDTO.setCompanyId(companyId);
            accountGeneralDefTranRPCDTO.setAccountSubType(accountSubType);
            accountGeneralDefTranRPCDTO.setBankAccountNo(accountGeneral.getBankAccountNo());
            accountGeneralDefTranRPCDTO.setBankName(accountGeneral.getBankName());
            uAccountGeneralService.defaultTransferAccount(accountGeneralDefTranRPCDTO);
            return true;
        } catch (FinPayException e) {
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】defaultTransferAccountCDTO 参数：{}", defaultTransferAccountCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean changeCompanyAccountModel(AccountChangeModelReqRPCDTO reqRPCDTO) {
        return false;
    }


    @Override
    public boolean changeCompanyModel(AccountChangeCompanyModelReqDTO reqRPCDTO) {
        FinhubLogger.info("【新账户系统，公司合作模式】changeCompanyModel 参数：{}", reqRPCDTO);
        try {
            //校验字段
            ValidateUtils.validate(reqRPCDTO);
            boolean resultGen = uAccountGeneralService.changeCompanyModel(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            boolean businessCredit = uAcctBusinessCreditService.updateModelByCompanyId(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            boolean businessDebit = uAcctBusinessDebitService.updateModelByCompanyId(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            boolean individualCredit = uAcctIndividualCreditService.updateModelByCompanyId(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            boolean individualDebit = uAcctIndividualDebitService.updateModelByCompanyId(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            boolean resultRedcoupon = accountRedcouponService.changeCompanyModel(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            boolean resultCompanyCard =uAcctCompanyCardService.changeCompanyModel(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            boolean resultCompanyGateway = acctCompanyGatewayService.changeCompanyModel(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            boolean resultAcctPublic =acctPublicDechService.changeAcctPublicCompanyModel(reqRPCDTO.getCompanyId(), reqRPCDTO.getCompanyModel());
            return businessDebit && businessCredit && individualCredit && individualDebit && resultGen && resultRedcoupon &&resultCompanyCard &&resultCompanyGateway
                    && resultAcctPublic;
        } catch (FinPayException e) {
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】changeCompanyModel 参数：{}", reqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public DefaultTransferInfoRespRPCDTO findDefaultTransferSubAcctInfo(DefaultTransferInfoReqRPCDTO defaultTransferInfoReqRPCDTO) {
        AccountGeneralBankRPCDTO accountGeneralBankRPCDTO = new AccountGeneralBankRPCDTO();
        accountGeneralBankRPCDTO.setBankNo(defaultTransferInfoReqRPCDTO.getCompanyId());
        accountGeneralBankRPCDTO.setCompanyId(defaultTransferInfoReqRPCDTO.getCompanyId());
        accountGeneralBankRPCDTO.setBankName(BankNameEnum.FBT.getCode());
        AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(accountGeneralBankRPCDTO);
        if (Objects.isNull(accountGeneral)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
        }
        DefaultTransferInfoRespRPCDTO defaultTransferInfoRespRPCDTO = new DefaultTransferInfoRespRPCDTO();
        int acctSubType = Objects.isNull(accountGeneral.getDefaultTransferAccountType()) ? FundAccountSubType.GENERAL_ACCOUNT.getKey() : accountGeneral.getDefaultTransferAccountType();
        defaultTransferInfoRespRPCDTO.setAcctSubType(acctSubType);
        return defaultTransferInfoRespRPCDTO;
    }

    @Override
    public List<AccountGeneralRespDTO> queryAllAcctByCompanyId(String companyId) {
        List<AccountGeneral> acctGenerals = uAccountGeneralService.findByCompanyId(companyId);
        List<AccountGeneralRespDTO> result = new ArrayList<>();
        acctGenerals.forEach(acctGeneral -> {
            AccountGeneralRespDTO accountGeneralRespDTO = new AccountGeneralRespDTO();
            BeanUtils.copyProperties(acctGeneral, accountGeneralRespDTO);
            result.add(accountGeneralRespDTO);
        });
        return result;
    }

    private AccountSubRespDTO getAccountBaseRespDTO(AcctCommonBaseDTO accountSub, String companyId) {
        AccountSubRespDTO subRespDTO = new AccountSubRespDTO();
        BeanUtils.copyProperties(accountSub, subRespDTO);
        if (FundAccountSubType.isIndividualAccount(accountSub.getAccountSubType())) {
            BigDecimal availableVoucherBalance = iVouchersPersonService.queryAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(companyId, FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountSub.getAccountModel());
            subRespDTO.setAvailableVoucherAmount(availableVoucherBalance);
        }
        return subRespDTO;
    }


    private AccountRedcouponRespRPCDTO getAccountRedcouponRespRPCDTO(String companyId) {
        //查询红包券
        AccountRedcouponRespRPCDTO accountRedcouponRespRPCDTO = new AccountRedcouponRespRPCDTO();
        AccountRedcoupon accountRedcoupon = accountRedcouponSearchService.queryAccountCouponInfo(companyId);
        if (accountRedcoupon != null) {
            BeanUtils.copyProperties(accountRedcoupon, accountRedcouponRespRPCDTO);
            BigDecimal availableVoucherBalance = iVouchersPersonService.queryAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(companyId, FundAccountSubType.REDCOUPON_ACCOUNT.getKey(), FundAccountModelType.RECHARGE.getKey());
            accountRedcouponRespRPCDTO.setAvailableVoucherAmount(availableVoucherBalance);
        }
        return accountRedcouponRespRPCDTO;
    }

    private void saveAccountSub(AccountSubEstablishReqRPCDTO establishReqRPCDTO) {
        AcctCreateSubDTO acctCreateSubDTO = new AcctCreateSubDTO();
        BeanUtils.copyProperties(establishReqRPCDTO, acctCreateSubDTO);
        acctCreateSubDTO.setAccountStatus(FundAcctStatusEnum.UN_ABLE.getStatus());
        acctCreateSubDTO.setCompanyMainId(establishReqRPCDTO.getCompanyId());
        if (FundAccountSubType.isBusinessAccount(establishReqRPCDTO.getAccountSubType())) {
            //创建商务账户
            if (FundAccountModelType.isRecharge(establishReqRPCDTO.getAccountModel())) {
                //创建激活的账户
                //创建对手账户
                uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
                Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
                Integer otherActiveStatus = FundAcctActStatusEnum.isAct(acctCreateSubDTO.getActiveStatus()) ? UN_ACTIVATE.getStatus() : ACTIVATE.getStatus();
                Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
                acctCreateSubDTO.setAccountModel(otherAccountModel);
                acctCreateSubDTO.setActiveStatus(otherActiveStatus);
                acctCreateSubDTO.setShowStatus(otherShowStatus);
                uAcctBusinessCreditService.establishAccount(acctCreateSubDTO);
            } else if (FundAccountModelType.isCredit(establishReqRPCDTO.getAccountModel())) {
                uAcctBusinessCreditService.establishAccount(acctCreateSubDTO);
                Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
                Integer otherActiveStatus = FundAcctActStatusEnum.isAct(acctCreateSubDTO.getActiveStatus()) ? UN_ACTIVATE.getStatus() : ACTIVATE.getStatus();
                Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
                acctCreateSubDTO.setAccountModel(otherAccountModel);
                acctCreateSubDTO.setActiveStatus(otherActiveStatus);
                acctCreateSubDTO.setShowStatus(otherShowStatus);
                uAcctBusinessDebitService.establishAccount(acctCreateSubDTO);
            }
        } else {
            //创建个人账户
            if (FundAccountModelType.isRecharge(establishReqRPCDTO.getAccountModel())) {
                uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);
                Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
                Integer otherActiveStatus = FundAcctActStatusEnum.isAct(acctCreateSubDTO.getActiveStatus()) ? UN_ACTIVATE.getStatus() : ACTIVATE.getStatus();
                Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
                acctCreateSubDTO.setAccountModel(otherAccountModel);
                acctCreateSubDTO.setActiveStatus(otherActiveStatus);
                acctCreateSubDTO.setShowStatus(otherShowStatus);
                uAcctIndividualCreditService.establishAccount(acctCreateSubDTO);
            } else if (FundAccountModelType.isCredit(establishReqRPCDTO.getAccountModel())) {
                uAcctIndividualCreditService.establishAccount(acctCreateSubDTO);
                Integer otherAccountModel = FundAccountModelType.isRecharge(acctCreateSubDTO.getAccountModel()) ? CREDIT.getKey() : RECHARGE.getKey();
                Integer otherActiveStatus = FundAcctActStatusEnum.isAct(acctCreateSubDTO.getActiveStatus()) ? UN_ACTIVATE.getStatus() : ACTIVATE.getStatus();
                Integer otherShowStatus = FundAcctShowStatusEnum.isShow(acctCreateSubDTO.getShowStatus()) ? UN_SHOW.getStatus() : SHOW.getStatus();
                acctCreateSubDTO.setAccountModel(otherAccountModel);
                acctCreateSubDTO.setActiveStatus(otherActiveStatus);
                acctCreateSubDTO.setShowStatus(otherShowStatus);
                uAcctIndividualDebitService.establishAccount(acctCreateSubDTO);
            }
        }
    }

    /**
     * 构建临时额度返回参数
     * @author: zhaoxu
     * @date: 2022-06-03 19:57:33
     */
    private AccountGeneralRespRPCDTO buildTempAmount(AccountGeneralRespRPCDTO dto, List<AcctCommonBaseDTO> accountSubList) {
        AccountGeneralRespRPCDTO resDto = new AccountGeneralRespRPCDTO();
        BeanUtils.copyProperties(dto, resDto);
        Map<Integer, AcctCommonBaseDTO> acctMap = accountSubList.stream().filter(v -> Integer.valueOf(FundAccountModelType.CREDIT.getKey()).equals(v.getAccountModel())).collect(Collectors.toMap(AcctCommonBaseDTO::getAccountSubType, Function.identity(), (v1, v2) -> v1));
        AcctCommonBaseDTO acctBusiness = acctMap.get(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        AcctCommonBaseDTO acctIndividual = acctMap.get(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());

        AcctCreditMainRespDTO acctCreditMainRespDTO = new AcctCreditMainRespDTO();
        AcctCreditDetailRespDTO acctBusinessRes = new AcctCreditDetailRespDTO();
        AcctCreditDetailRespDTO acctIndividualRes = new AcctCreditDetailRespDTO();

        BigDecimal initCreditBalance = BigDecimal.ZERO;
        BigDecimal totalCredit = BigDecimal.ZERO;
        BigDecimal balance = BigDecimal.ZERO;
        BigDecimal totalTempAmount = BigDecimal.ZERO;

        if (acctBusiness != null) {
            BeanUtils.copyProperties(acctBusiness, acctBusinessRes);
            acctBusinessRes.setTotalCredit(acctBusiness.getInitCredit().add(acctBusiness.getTempAmount()));
            acctCreditMainRespDTO.setRecoverDate(acctBusiness.getRecoverDate());
            acctCreditMainRespDTO.setBussCreditDetailRespDTO(acctBusinessRes);

            acctCreditMainRespDTO.setAccountId(acctBusiness.getAccountId());
            acctCreditMainRespDTO.setCompanyMainId(acctBusiness.getCompanyMainId());
            acctCreditMainRespDTO.setBankName(acctBusiness.getBankName());
            acctCreditMainRespDTO.setBankAccountNo(acctBusiness.getBankAccountNo());

            initCreditBalance = initCreditBalance.add(acctBusiness.getInitCredit());
            totalCredit = totalCredit.add(acctBusiness.getTempAmount()).add(acctBusiness.getInitCredit());
            balance = balance.add(acctBusiness.getBalance());
            totalTempAmount = totalTempAmount.add(acctBusiness.getTempAmount());
        }

        if (acctIndividual != null) {
            BeanUtils.copyProperties(acctIndividual, acctIndividualRes);
            acctIndividualRes.setTotalCredit(acctIndividualRes.getInitCredit().add(acctIndividualRes.getTempAmount()));
            acctCreditMainRespDTO.setIndlCreditDetailRespDTO(acctIndividualRes);

            initCreditBalance = initCreditBalance.add(acctIndividual.getInitCredit());
            totalCredit = totalCredit.add(acctIndividual.getTempAmount()).add(acctIndividual.getInitCredit());
            balance = balance.add(acctIndividual.getBalance());
            totalTempAmount = totalTempAmount.add(acctIndividual.getTempAmount());
        }

        acctCreditMainRespDTO.setInitCreditBalance(initCreditBalance);
        acctCreditMainRespDTO.setTotalCredit(totalCredit);
        acctCreditMainRespDTO.setBalance(balance);
        acctCreditMainRespDTO.setTotalTempAmount(totalTempAmount);
        resDto.setAcctCreditMain(acctCreditMainRespDTO);
        return resDto;
    }
}
