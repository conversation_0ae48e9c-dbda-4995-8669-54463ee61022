package com.fenbeitong.fenbeipay.rpc.service.bank;


import com.fenbeitong.fenbeipay.acctdech.manager.IBankCardTrapManager;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardTrapRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("iBankCardTrapRpcService")
@Slf4j
public class IBankCardTrapRpcServiceImpl implements IBankCardTrapRpcService {

    @Autowired
    private IBankCardTrapManager bankCardTrapManager;


    @Override
    public boolean updateTrapStatus(String id, String trapStatus) {
        return bankCardTrapManager.updateTrapStatus(id, trapStatus);
    }

    @Override
    public boolean updateTrapStatusByOriTxnId(String oriTxnId, String trapStatus) {
        return bankCardTrapManager.updateTrapStatusByOriTxnId(oriTxnId, trapStatus);
    }
}
