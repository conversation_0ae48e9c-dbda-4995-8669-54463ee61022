package com.fenbeitong.fenbeipay.rpc.service.base;

import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankCardStatus;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.ApplyCardManager;
import com.fenbeitong.fenbeipay.core.constant.personpay.EmployeeStatus;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.event.AccountEventHandlerService;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoService;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherConstant;
import com.fenbeitong.fenbeipay.vouchers.unit.service.UVoucherRecoveryTaskService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.constant.FundAccountModelType;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.consumer.KafkaConsumerUtils;
import com.fenbeitong.finhub.kafka.msg.dech.KafkaDechSpaPersonAcctMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaCompanyCardAcctChangeMsg;
import com.fenbeitong.finhub.kafka.msg.uc.KafkaEmployeeUpdateMsg;
import com.fenbeitong.finhub.kafka.msg.uc.KafkaOrgUnitUpdateMsg;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.fxpay.api.enums.FxCompanyAccountSubType;
import com.fenbeitong.fxpay.api.interfaces.ICompanyAcctService;
import com.fenbeitong.fxpay.api.vo.CompanyAcctRes;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.fxpay.api.vo.acct.CompanyAcctInfoReq;
import com.fenbeitong.usercenter.api.model.enums.employee.OperateType;
import com.google.common.base.Objects;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * kafka 消息处理
 */
@Service
public class IBaseConsumerKafkaServiceImpl {
    @Autowired
    public ApplyCardManager applyCardManager;
    @Autowired
    private UVoucherRecoveryTaskService uVoucherRecoveryTaskService;
    @Autowired
    private DingDingMsgService dingDingMsgService;
    @Autowired
    private AccountReceiveInfoService accountReceiveInfoService;
    @Autowired
    private AccountEventHandlerService accountEventHandlerService;
    @Autowired
    private ICompanyAcctService iCompanyAcctService;

    @KafkaListener(topics = {"usercenter_employee_change"})
    public void ucUpdateEmployeeListener(ConsumerRecord<?, ?> record) {
        FinhubLogger.info("【kafka消息 ===员工信息操作：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), record.value());
        KafkaEmployeeUpdateMsg iMessage = KafkaConsumerUtils.invokeIMessage(record.value().toString(),
                KafkaEmployeeUpdateMsg.class);
        if (ObjUtils.isEmpty(iMessage) || ObjUtils.isEmpty(iMessage.getCompanyId()) || ObjUtils.isEmpty(
                iMessage.getEmployeeId())) {
            FinhubLogger.info("【kafka消费：员工信息变更:参数上有误】消费 data:" + iMessage.toString());
            return;
        }
        Integer operateStatus = iMessage.getOperateStatus();
        //员工执行了更新操作的业务
        if (OperateType.UPDATE_OP.getKey() == operateStatus) {
            //分贝通虚拟卡信息修改：员工状态的用户名称或手机或组织架构变更
            try {
                applyCardManager.updateEmployeeListener(iMessage);
                FinhubLogger.info("【分贝通虚拟卡处理kafka】同步员工信息变更消息完成 data:" + iMessage.toString());
            } catch (Exception e) {
                FinhubLogger.error("【kafka员工信息更新，分贝通虚拟卡修改信息失败】data：{}", iMessage.getEmployeeId(), e);
                dingDingMsgService.sendMsg("【员工信息更新，系统操作分贝通虚拟卡修改信息失败】员工Id：" + iMessage.getEmployeeId());
            }
            //分贝通虚拟卡状态修改：员工状态更新（启用和禁用）
            updateBankStatusEmployee(iMessage);
        }
        //员工离职，回收分贝券
        try {
            if (OperateType.DELETE_OP.getKey() != iMessage.getOperateStatus()) {
                return;
            }
            String companyId = iMessage.getCompanyId();
            String employeeId = iMessage.getEmployeeId();
            uVoucherRecoveryTaskService.createWithdrawalTaskByEmployeeId(companyId, employeeId, VoucherConstant.EMPLOYEE_DELETE_OPERATION, VoucherConstant.EMPLOYEE_DELETE_OPERATION);
            FinhubLogger.info("【kafka消费】员工离职回收分贝券消费完成 data:{}", iMessage.toString());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券，撤回员工可用分贝券异常】：{}", iMessage.toString(), e);
        }
    }


    /**
     *  消息处理部门信息的业务
     */
    @KafkaListener(topics = {"usercenter_org_unit_change"})
    public void kafkaUcUpdateEmployeeOrgUnitListener(ConsumerRecord<?, ?> record) {
        FinhubLogger.info("【kafka消息 来自员工部门信息变更：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), record.value());
        KafkaOrgUnitUpdateMsg iMessage = KafkaConsumerUtils.invokeIMessage(record.value().toString(), KafkaOrgUnitUpdateMsg.class);

        if (ObjUtils.isEmpty(iMessage) || OperateType.UPDATE_OP.getKey() != iMessage.getOperateStatus()
                || ObjUtils.isEmpty(iMessage.getCompanyId()) || ObjUtils.isEmpty(iMessage.getOrgUnitId())
                || ObjUtils.isEmpty(iMessage.getOrgUnitName())) {
            FinhubLogger.info("【kafka消费 分贝通虚拟卡信息：数据不满足部门名称变更】消费 data:" + iMessage.toString());
            return;
        }
        //更新员工信息。部门名称
        applyCardManager.updateEmployeeOrgUnit(iMessage);
        FinhubLogger.info("【分贝通虚拟卡处理kafka】同步员工部门信息变更消息完成 data:" + iMessage.toString());
    }

    /**
     * 员工状态变为禁用或启用:更新分贝通虚拟卡信息
     */
    private void updateBankStatusEmployee(KafkaEmployeeUpdateMsg iMessage) {
        try {
            if (!(ObjUtils.isEmpty(iMessage.getTargetStatus()) || ObjUtils.isEmpty(iMessage.getCurrentStatus())
                    || Objects.equal(iMessage.getTargetStatus(), iMessage.getCurrentStatus()))) {
                if (EmployeeStatus.INACTIVE.getKey() == iMessage.getTargetStatus()
                        && EmployeeStatus.ACTIVE.getKey() == iMessage.getCurrentStatus()) {
                    applyCardManager.updateBankStatusEmployee(iMessage, BankCardStatus.DISABLE);
                    FinhubLogger.info("【分贝通虚拟卡处理kafka：员工状态变为禁用】消费处理=完成 data:" + iMessage.toString());
                }
            }
        } catch (Exception e) {
            FinhubLogger.error("【分贝通虚拟卡处理kafka：员工状态变为禁用或启用失败】data：{}", iMessage.toString(), e);
            dingDingMsgService.sendMsg("【分贝通虚拟卡处理kafka，系统操作分贝通虚拟卡修改状态失败】员工Id：" + iMessage.getEmployeeId());
        }
    }


    @KafkaListener(topics = {"topic_dech_spa_person_acct"})
    public void updateEmployeeByBankListener(ConsumerRecord<?, ?> record) {
        KafkaDechSpaPersonAcctMsg personAcctMsg = KafkaConsumerUtils.invokeIMessage(record.value().toString(),
                KafkaDechSpaPersonAcctMsg.class);
        FinhubLogger.info("平安银行员工信息：{}",personAcctMsg);
        if (ObjUtils.isNull(personAcctMsg)) {
            FinhubLogger.info("【kafka消费：平安银行员工信息 message为空");
            return;
        }
        //保存员工信息
        accountReceiveInfoService.syncEmployeeInfoByDechSpa(personAcctMsg);
    }

    @KafkaListener(topics = {"fenbei_company_card_acct_change_msg"})
    public void acctChange(ConsumerRecord<?, ?> record) {
        KafkaCompanyCardAcctChangeMsg kafkaCompanyCardAcctChangeMsg = KafkaConsumerUtils.invokeIMessage(record.value().toString(),
                KafkaCompanyCardAcctChangeMsg.class);
        FinhubLogger.info("海外的动账消息：{}",kafkaCompanyCardAcctChangeMsg);
        if (FxAcctChannelEnum.isAirWallex(kafkaCompanyCardAcctChangeMsg.getBankName())) {
            CompanyAcctInfoReq companyAcctInfoReq = new CompanyAcctInfoReq();
            companyAcctInfoReq.setCompanyId(kafkaCompanyCardAcctChangeMsg.getCompanyId());
            companyAcctInfoReq.setAccountSubType(FxCompanyAccountSubType.PETTY);
            companyAcctInfoReq.setCurrency(CurrencyEnum.USD);
            ResponseVo<List<CompanyAcctRes>> ac =  iCompanyAcctService.queryCompanyAcctInfo(companyAcctInfoReq);
            sendAccountChangeEvent0(ac.getData().get(0),kafkaCompanyCardAcctChangeMsg.getOperationAmount(),kafkaCompanyCardAcctChangeMsg.getKey());
        }

    }
    public void sendAccountChangeEvent0(CompanyAcctRes companyAcct, BigDecimal operationAmount, int eventType) {
        try {
            BigDecimal balance = BigDecimalUtils.fen2yuan(companyAcct.getAvailableBalance());
            accountEventHandlerService.sendAccountChangeEventPlus(true,
                    FundAccountSubType.OVERSEA_ACCOUNT.getKey(), companyAcct.getCompanyId(), eventType,
                    balance, null, operationAmount, FundAccountModelType.RECHARGE.getKey(),
                    companyAcct.getChannel(), companyAcct.getAccountId());
        } catch (Exception e) {
            FinhubLogger.error("账户资金余额变动消息发送失败 companyId:{},账户类型为,账户类型为{}", companyAcct.getCompanyName(), FundAccountSubType.OVERSEA_ACCOUNT.getKey(), e);
        }
    }
}
