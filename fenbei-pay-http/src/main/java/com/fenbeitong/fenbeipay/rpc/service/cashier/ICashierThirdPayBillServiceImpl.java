package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayChannel;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierThirdBillRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierThirdBillReqRPCVO;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierThirdPayBillService;
import com.fenbeitong.fenbeipay.awplus.model.dto.ThirdPayBillRequestDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.ThirdPayBillResponseDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.ThirdPayBillTaskRequestDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.ThirdPayBillTaskResponseDTO;
import com.fenbeitong.fenbeipay.awplus.service.bill.ThirdBillService;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.TbThirdPayBillTaskMapper;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTask;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTaskExample;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.finance.api.bf.IAccountSubjectService;
import com.fenbeitong.finance.api.bf.dto.AccountSubjectDTO;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询三方支付账单实现
 * <AUTHOR>
 */
@Service("iCashierThirdPayBillService")
public class ICashierThirdPayBillServiceImpl implements ICashierThirdPayBillService {
    @Autowired
    ThirdBillService thirdBillService;
    @Autowired
    IAccountSubjectService iAccountSubjectService;
    @Autowired
    DingDingMsgService dingDingMsgService;
    @Autowired
    TbThirdPayBillTaskMapper tbThirdPayBillTaskMapper;
    @Override
    public CashierThirdBillRespRPCDTO queryBill(CashierThirdBillReqRPCVO cashierThirdBillReqRPCVO) {
        ThirdPayBillRequestDTO thirdPayBillRequestDTO = new ThirdPayBillRequestDTO();
        BeanUtils.copyProperties(cashierThirdBillReqRPCVO,thirdPayBillRequestDTO);
        ThirdPayBillResponseDTO billResponseDTO = thirdBillService.queryBill(thirdPayBillRequestDTO);
        CashierThirdBillRespRPCDTO cashierThirdBillRespRPCDTO = new CashierThirdBillRespRPCDTO();
        cashierThirdBillRespRPCDTO.setBillUrl(billResponseDTO.getBillUrl());
        cashierThirdBillRespRPCDTO.setBillStatus(billResponseDTO.getBillStatus());
        return cashierThirdBillRespRPCDTO;
    }

    @Override
    public int initTask(String billDate) {
        int accountCount = 0;
        int taskCount = 0;
        //从内部系统查询需要执行的账户
        List<AccountSubjectDTO> weChatList = iAccountSubjectService.queryAppIdList("WeChat");
        FinhubLogger.info("微信账号:{}", JsonUtils.toJson(weChatList));
        if (CollectionUtils.isNotEmpty(weChatList)){
            int weChatCount = initTask0(weChatList,billDate, CashierPayChannel.WECHAT.name());
            taskCount = taskCount + weChatCount;
            accountCount = accountCount + weChatList.size();
        }
        List<AccountSubjectDTO> alipayList = iAccountSubjectService.queryAppIdList("Alipay");
        FinhubLogger.info("支付宝账号:{}", JsonUtils.toJson(alipayList));
        if (CollectionUtils.isNotEmpty(alipayList)){
            int alipayCount =  initTask0(alipayList,billDate, CashierPayChannel.ALIPAY.name());
            taskCount = taskCount + alipayCount;
            accountCount = accountCount + alipayList.size();
        }
        if (taskCount != accountCount){
            dingDingMsgService.sendMsg("【三方支付交易账单查询】任务初始化失败,账户数:"+accountCount + ",创建任务数:" + taskCount);
        }
        return taskCount;
    }

    private int initTask0(List<AccountSubjectDTO> accountList,String billDate,String channel){
        int count = 0;
        for (AccountSubjectDTO account:accountList){
            ThirdPayBillTaskRequestDTO thirdPayBillTaskRequestDTO = new ThirdPayBillTaskRequestDTO();
            thirdPayBillTaskRequestDTO.setBillDate(billDate);
            thirdPayBillTaskRequestDTO.setAccount(account.getCode());
            thirdPayBillTaskRequestDTO.setChannel(channel);
            thirdPayBillTaskRequestDTO.setVersion(account.getVersion());
            ThirdPayBillTaskResponseDTO thirdPayBillTaskResponseDTO =  thirdBillService.initTask(thirdPayBillTaskRequestDTO);
            if (thirdPayBillTaskResponseDTO!=null && thirdPayBillTaskResponseDTO.getTaskCount()!= null) {
                count = count + thirdPayBillTaskResponseDTO.getTaskCount();
            }
        }
        return count;
    }

    @Override
    public void runTask() {
        thirdBillService.runTask();
    }
}
