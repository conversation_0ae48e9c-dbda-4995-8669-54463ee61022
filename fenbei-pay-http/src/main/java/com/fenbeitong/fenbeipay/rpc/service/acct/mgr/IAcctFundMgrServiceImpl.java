package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.api.model.dto.BankAcctBookRespDto;
import com.fenbeitong.dech.api.service.IBankSearchService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyMainService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.sas.SmsTemplateEnum;
import com.fenbeitong.fenbeipay.api.model.ResultDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctGeneralTransConvert;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.AcctPublicInfoRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.api.util.BankNameShowConfig;
import com.fenbeitong.fenbeipay.api.util.CaptchaUtil;
import com.fenbeitong.fenbeipay.api.util.ExceptionUtils;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.BankConfigUtil;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.dto.oversea.AcctOversea;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.rpc.service.acct.IAcctAbstractService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaRechargeInformTaskMsg;
import com.fenbeitong.finhub.kafka.producer.IKafkaProducerPublisher;
import com.fenbeitong.finhub.kafka.producer.ISaturnKafkaProducerPublisher;
import com.fenbeitong.harmony.captcha.api.dto.CaptchaSendResult;
import com.fenbeitong.harmony.captcha.api.service.ICaptchaService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.fenbeitong.finhub.common.constant.FundAccountSubType.GENERAL_ACCOUNT;

/**
 * <AUTHOR>
 * @Date 2021/1/12
 * @Description 资金账户-资金账户管理
 */
@Service("iAcctFundMgrService")
public class IAcctFundMgrServiceImpl extends IAcctAbstractService implements IAcctFundMgrService {
    @Autowired
    private IKafkaProducerPublisher iKafkaProducerPublisher;

    @Autowired
    protected ISaturnKafkaProducerPublisher iSaturnKafkaProducerPublisher;

    @Autowired
    protected AccountGeneralFlowService accountGeneralFlowService;

    @Autowired
    protected RedissonService redissonService;
    
    @Autowired
    private ICaptchaService captchaService;
    
    @Autowired
    private RedisDao redisDao;
    /**
     * 强制解锁时间设置
     */
    private final long lockTimeRefund = 10000L;
    /**
     * 等待时间
     **/
    private final long waitTimeRefund  = 400L;

    @Autowired
    protected DingDingMsgService dingDingMsgService;
    
    private static final String SMS_RATE_LIMIT_KEY = "PAY:GROUP:TRANSFER:";

    @Autowired
    AcctCompanyMainService acctCompanyMainService;

    @Autowired
    IBankSearchService iBankSearchService;

    @Override
    public AcctGeneralOptRespDTO rechargeByBank(AcctBankRechargeOptReqDTO bankOptReqDTO) throws FinAccountNoEnoughException {
        String lockKey = null;
        try {
            ValidateUtils.validate(bankOptReqDTO);
            lockKey = MessageFormat.format(RedisKeyConstant.ACCT_RECHARGE_BANK_KEY, bankOptReqDTO.getBankTransNo());
            //加锁
            boolean lock = redissonService.tryLock(waitTimeRefund, lockTimeRefund, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.warn("rechargeByBank加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            bankOptReqDTO.setOperationUserName(CoreConstant.SYSTEM);

            AccountGeneral accountGeneral = uAcctGeneralService.findByBankAccountNoOrBankAcctId(bankOptReqDTO.getCompanyId(),bankOptReqDTO.getBankName(),bankOptReqDTO.getBankAccountNo(), bankOptReqDTO.getBankAcctId());
            //做幂等验证
            AccountGeneralFlow accountGeneralFlow = uAcctGeneralFlowService.queryByBankTransNo(bankOptReqDTO.getBankTransNo());
            if (Objects.nonNull(accountGeneralFlow)){
               FinhubLogger.error("【资金账户4.0】充值时账户重复操作清确认：{}", JsonUtils.toJson(accountGeneralFlow));
               throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_RECHARGE_REPEAT_FAIL);
           }
            AccountPublic accountPublic = acctPublicSearchService.findByComIdAndMIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getCompanyMainId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
            if(Objects.nonNull(accountPublic)) {
                bankOptReqDTO.setDirectAcctType(accountPublic.getDirectAcctType());
            }
            AccountGeneralFlow flow =
                uAcctGeneralService.accountRechargeByBankUpdateCost(bankOptReqDTO, accountGeneral);
            //发送消息通知stereo发送短信
            sendSms(bankOptReqDTO, flow,accountGeneral);
            //默认划转账户类型不为空，不为可以支配余额账户时，需要自动划转
            if (Objects.nonNull(accountGeneral) && !(Objects.isNull(accountGeneral.getDefaultTransferAccountType()) || FundAccountSubType.isGeneralAccount(accountGeneral.getDefaultTransferAccountType()))) {
                autoTransfer(bankOptReqDTO, accountGeneral);
            }
            AcctGeneralOptRespDTO generalOptRespDTO = new AcctGeneralOptRespDTO();
            BeanUtils.copyProperties(flow, generalOptRespDTO);
            return generalOptRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】recharge 参数：{},账户余额不足", bankOptReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", bankOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", bankOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", bankOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        } finally {
            //放锁
            try {
                if (StringUtils.isNotEmpty(lockKey)) {
                    redissonService.unLock(lockKey);
                    FinhubLogger.info("释放锁成功：{}", lockKey);
                }
            } catch (Exception e) {
                FinhubLogger.error("释放锁失败：{}", lockKey, e);
            }
        }
    }

    @Override
    public AcctGeneralOptRespDTO transferOut(AcctGeneralTransOutReqDTO bankOptReqDTO)
        throws FinAccountNoEnoughException {
        bankOptReqDTO.validate();
        AcctTransferDebitReqDTO acctTransferDebitReqDTO = AcctGeneralTransConvert.parseToReq(bankOptReqDTO);
        AcctTransferBaseInfoDTO acctTransferBaseInfoDTO = AcctGeneralTransConvert.parseToBaseInfo(bankOptReqDTO);
        AccountGeneralFlow accountGeneralFlow =
            uAcctGeneralService.transferOut(acctTransferDebitReqDTO, acctTransferBaseInfoDTO);
        if (Objects.isNull(accountGeneralFlow)) {
            return null;
        }
        // 发送对账消息
        KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
        try {
            BeanUtils.copyProperties(accountGeneralFlow, kafkaAutoAcctCheckingMsg);
            kafkaAutoAcctCheckingMsg.setAccountSubType(GENERAL_ACCOUNT.getKey());
            autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
        }catch (Exception exception){
            FinhubLogger.error("余额账户充值撤销，自动对账消息发送失败:{}", JSON.toJSONString(kafkaAutoAcctCheckingMsg));
            FinhubLogger.error("挂账 transferOut,对账消息发送失败", exception);
        }
        return parseFlow2Resp(accountGeneralFlow);
    }

    private AcctGeneralOptRespDTO parseFlow2Resp(AccountGeneralFlow accountGeneralFlow) {
        AcctGeneralOptRespDTO acctGeneralOptRespDTO = new AcctGeneralOptRespDTO();
        acctGeneralOptRespDTO.setBankAccountNo(accountGeneralFlow.getBankAccountNo());
        acctGeneralOptRespDTO.setBankName(accountGeneralFlow.getBankName());
        acctGeneralOptRespDTO.setBizNo(accountGeneralFlow.getBizNo());
        acctGeneralOptRespDTO.setCompanyId(accountGeneralFlow.getCompanyId());
        acctGeneralOptRespDTO.setCompanyMainId(accountGeneralFlow.getCompanyMainId());
        acctGeneralOptRespDTO.setAccountFlowId(accountGeneralFlow.getAccountFlowId());
        return acctGeneralOptRespDTO;
    }

    /**
     * 充值两种：
     * 入金通知Kafka，
     * 企业自主充值
     *
     * @param bankOptReqDTO
     * @return
     * @throws FinAccountNoEnoughException
     */
    @Override
    public AcctGeneralOptRespDTO recharge(AcctGeneralBankOptReqDTO bankOptReqDTO) throws FinAccountNoEnoughException {
        AcctGeneralOptRespDTO generalOptRespDTO = new AcctGeneralOptRespDTO();
        try {
            ValidateUtils.validate(bankOptReqDTO);
            AccountGeneralFlow flow = uAcctGeneralService.accountRecharge(bankOptReqDTO);
            BeanUtils.copyProperties(flow, generalOptRespDTO);
            return generalOptRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】recharge 参数：{},账户余额不足", bankOptReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", bankOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", bankOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", bankOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 结算账户充值
     * @param bankOptReqDTO
     * @return
     * @throws FinAccountNoEnoughException
     */
    @Override
    public AcctGeneralOptRespDTO rechargeD0ByBank(AcctBankRechargeOptReqDTO bankOptReqDTO) throws FinAccountNoEnoughException {
        String lockKey = null;
        try {
            ValidateUtils.validate(bankOptReqDTO);
            lockKey = MessageFormat.format(RedisKeyConstant.ACCT_SETTLEMENT_KEY, bankOptReqDTO.getBizNo());
            //加锁
            boolean lock = redissonService.tryLock(waitTimeRefund, lockTimeRefund, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.warn("rechargeByBank加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            bankOptReqDTO.setOperationUserName(CoreConstant.SYSTEM);
            AccountGeneral accountGeneral = uAcctGeneralService.findByCompanyIdAndBank(bankOptReqDTO.getCompanyId(),bankOptReqDTO.getBankName(),bankOptReqDTO.getBankAccountNo());
            if (Objects.isNull(accountGeneral)) {
                FinhubLogger.error("【资金账户4.0】充值时账户错误,余额账户不存在：{}",bankOptReqDTO.toString());
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            AccountGeneralFlow exist = uAcctGeneralFlowService.findByBizno(bankOptReqDTO.getBizNo());
            if (exist != null) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            AccountGeneralFlow flow =
                uAcctGeneralService.accountRechargeByBankUpdateCost(bankOptReqDTO, accountGeneral);
            //发送消息通知stereo发送短信
            sendSms(bankOptReqDTO, flow,accountGeneral);
            //默认划转账户类型不为空，不为可以支配余额账户时，需要自动划转
            if (Objects.nonNull(accountGeneral) && !(Objects.isNull(accountGeneral.getDefaultTransferAccountType()) || FundAccountSubType.isGeneralAccount(accountGeneral.getDefaultTransferAccountType()))) {
                autoTransfer(bankOptReqDTO, accountGeneral);
            }
            AcctGeneralOptRespDTO generalOptRespDTO = new AcctGeneralOptRespDTO();
            BeanUtils.copyProperties(bankOptReqDTO, generalOptRespDTO);
            return generalOptRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】rechargeBySettlement 参数：{},账户余额不足", bankOptReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】rechargeBySettlement 参数：{}", bankOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", bankOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", bankOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        } finally {
            //放锁
            try {
                if (StringUtils.isNotEmpty(lockKey)) {
                    redissonService.unLock(lockKey);
                    FinhubLogger.info("释放锁成功：{}", lockKey);
                }
            } catch (Exception e) {
                FinhubLogger.error("释放锁失败：{}", lockKey, e);
            }
        }
    }

    /**
     * 结算账户充值到余额账户并默认转账到子账户
     * @param acctSettlementTransOutOptReqDTO
     * @return
     * @throws FinAccountNoEnoughException
     */
    @Override
    @Transactional(transactionManager = "fenbeipayTransactionManager", readOnly = false, rollbackFor = Exception.class)
    public AcctSettlementTransOutOptRespDTO rechargeBySettlement(AcctSettlementTransOutOptReqDTO acctSettlementTransOutOptReqDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(acctSettlementTransOutOptReqDTO);
            uAcctCommonDebitService.rechargeBySettlement(acctSettlementTransOutOptReqDTO);
            AccountGeneral accountGeneral = uAcctGeneralService.findByAccountId(acctSettlementTransOutOptReqDTO.getAccountId());
            AcctBankRechargeOptReqDTO bankOptReqDTO = new AcctBankRechargeOptReqDTO();
            BeanUtils.copyProperties(acctSettlementTransOutOptReqDTO,bankOptReqDTO);
            bankOptReqDTO.setOperationAmount(acctSettlementTransOutOptReqDTO.getOperationAmount());
            AccountGeneralFlow flow = uAcctGeneralService.rechargeBySettlement(bankOptReqDTO,accountGeneral);

            //发送消息通知stereo发送短信
            sendSms(bankOptReqDTO, flow,accountGeneral);
            if (!(Objects.isNull(acctSettlementTransOutOptReqDTO.getDefaultTransferAccountType()) || FundAccountSubType.isGeneralAccount(acctSettlementTransOutOptReqDTO.getDefaultTransferAccountType()))){
                accountGeneral.setDefaultTransferAccountType(acctSettlementTransOutOptReqDTO.getDefaultTransferAccountType());
                autoTransfer(bankOptReqDTO, accountGeneral);
            }else if (Objects.nonNull(accountGeneral) && !(Objects.isNull(accountGeneral.getDefaultTransferAccountType()) || FundAccountSubType.isGeneralAccount(accountGeneral.getDefaultTransferAccountType()))) {
                //默认划转账户类型不为空，不为可以支配余额账户时，需要自动划转
                autoTransfer(bankOptReqDTO, accountGeneral);
            }
            AcctSettlementTransOutOptRespDTO acctSettlementTransOutOptRespDTO = new AcctSettlementTransOutOptRespDTO();
            acctSettlementTransOutOptRespDTO.setAccountGeneralId(accountGeneral.getAccountGeneralId());
            acctSettlementTransOutOptRespDTO.setRemark("充值成功");
            return acctSettlementTransOutOptRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】rechargeBySettlement 参数：{},账户余额不足", acctSettlementTransOutOptReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】rechargeBySettlement 参数：{}", acctSettlementTransOutOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】rechargeBySettlement 参数：{}", acctSettlementTransOutOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】rechargeBySettlement 参数：{}", acctSettlementTransOutOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipayTransactionManager", readOnly = false, rollbackFor = Exception.class)
    public AcctGeneralOptRespDTO cashWithdrawal(AcctGeneralOptReqDTO generalOptReqDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(generalOptReqDTO);
            String verifyCode = generalOptReqDTO.getVerifyCode();
            String verifyCodePhoneNum = generalOptReqDTO.getVerifyCodePhoneNum();
                        
            if(!Sets.newHashSet(FundPlatformEnum.CGB.getCode(), FundPlatformEnum.LIANLIAN.getCode()).contains(generalOptReqDTO.getBankName())) {
            	if (StringUtils.isBlank(verifyCode) || StringUtils.isBlank(verifyCodePhoneNum)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_CASHOUT_VERIFYCODE_NULL);
                }
            	iCaptchaService.verifyCaptcha(verifyCodePhoneNum, verifyCode);
            }
            
            if (BankNameEnum.isLianlian(generalOptReqDTO.getBankName()) && 
            		StringUtils.isNotBlank(verifyCode) && 
            		StringUtils.isAnyBlank(generalOptReqDTO.getBizNo(), generalOptReqDTO.getBankTransNo())) {
            	throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            
            AcctGeneralOptRespDTO generalOptResp = uAcctGeneralService.cashWithdrawal(generalOptReqDTO);
//            uAcctGeneralService.cashWithdrawalBank(generalOptReqDTO);
            return generalOptResp;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】cashWithdrawal 参数：{},账户余额不足", generalOptReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", generalOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinhubException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), payEx);
            throw payEx;
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }
    
    /**
     * 
     * @param phoneNo
     * @param captcha
     * @return
     */
    private boolean isRateLimitExceeded(String phoneNo, String captcha) {
        String key = SMS_RATE_LIMIT_KEY + phoneNo;
        long now = System.currentTimeMillis();
        try {
            redisDao.getZSetOperations().add(key, captcha, now);
            redisDao.getRedisTemplate().expire(key, 1, TimeUnit.HOURS);
            long count = redisDao.getZSetOperations().count(key, now - 60 * 1000, now);
            if (count > 2) {
                return true;
            }
        } catch (Exception e) {
            FinhubLogger.error("when isRateLimitExceeded error->{}", ExceptionUtils.logStackTraceInfor(e));
        }
        return false;
    }
    
    @Override
    public String sendSmsCaptcha4GroupTransfer(AcctTransferRechargeReqDTO request) {
        FinhubLogger.info("【集团资金调拨】sendSmsCaptcha4GroupTransfer入参->{}", JSON.toJSONString(request));
        try {
            if (StringUtils.isBlank(request.getVerifyCodePhoneNum())) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            // 增加防刷策略
            String captcha = CaptchaUtil.createCaptcha(4);

            boolean exceeded = isRateLimitExceeded(request.getVerifyCodePhoneNum(), captcha);
            if (exceeded) {
                throw new FinhubException(401, "短信验证码发送过于频繁，请稍后再试");
            }

            Map<String, String> smsParams = Maps.newHashMap();
            smsParams.put("var1", captcha);
            CaptchaSendResult sendResult =
                    captchaService.sendCaptcha(request.getVerifyCodePhoneNum(),
                            SmsTemplateEnum.GROUP_TRANSFER_SMS_TEMPLATE.getId(), smsParams, captcha, 1, TimeUnit.MINUTES);
            
            FinhubLogger.info("【集团资金调拨】sendSmsCaptcha4GroupTransfer 验证码->{} 结果->{}", captcha, JSON.toJSONString(sendResult));
            if (Objects.isNull(sendResult) || StringUtils.isBlank(sendResult.getSequenceNo())) {
                throw new FinPayException(GlobalResponseCode.SEND_SMS_FAILURE);
            }
            
            return sendResult.getSequenceNo();
        }  catch (FinPayException e) {
            FinhubLogger.error("【集团资金调拨】sendSmsCaptcha4GroupTransfer异常 参数：{} cause:{}", JSON.toJSONString(request), ExceptionUtils.logStackTraceInfor(e));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【集团资金调拨】sendSmsCaptcha4GroupTransfer异常 参数：{},cause:{}", JSON.toJSONString(request), ExceptionUtils.logStackTraceInfor(e));
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }
    
    @Override
    public AcctCommonOptRespDTO transferGeneralBetweenGroupMemberRechargeAcct(AcctTransferRechargeReqDTO transferRequest, Set<String> generalIds) {
        FinhubLogger.info("【集团资金调拨】transferGeneralBetweenGroupMemberRechargeAcct入参->{}", JSON.toJSONString(transferRequest));

        try {
            ValidateUtils.validate(transferRequest);
            if (transferRequest.getOperationAmount().compareTo(BigDecimal.ZERO) <= 0 ||
                    Objects.equals(transferRequest.getAccountGeneralId(), transferRequest.getTargetAccountGeneralId())) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            if (CollectionUtils.isEmpty(generalIds) ||
                    !generalIds.contains(transferRequest.getAccountGeneralId()) ||
                    !generalIds.contains(transferRequest.getTargetAccountGeneralId())) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_OPT_CHECK_ERROR);
            }

            Boolean ok = captchaService.verifyCaptcha(transferRequest.getSequenceNo(), transferRequest.getVerifyCode());
            if (Objects.isNull(ok) || !ok) {
                FinhubLogger.error("【集团资金调拨】短信验证码{}不正确", transferRequest.getVerifyCode());
                throw new FinPayException(GlobalResponseCode.SMS_CAPTCHA);
            }

            AcctCommonOptRespDTO resp = uAcctCommonDebitService.transferGeneralBetweenGroupMemberRechargeAcct(transferRequest);
            return resp;
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn("【集团资金调拨】transferGeneralBetweenGroupMemberRechargeAcct账户余额不足 参数：{},cause:{}", JSON.toJSONString(transferRequest), ExceptionUtils.logStackTraceInfor(e));
            throw e;
        } catch (FinPayException e) {
            FinhubLogger.error("【集团资金调拨】transferGeneralBetweenGroupMemberRechargeAcct账户余额不足 参数：{} {} cause:{}", JSON.toJSONString(transferRequest), JSON.toJSONString(generalIds), ExceptionUtils.logStackTraceInfor(e));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【集团资金调拨】transferGeneralBetweenGroupMemberRechargeAcct异常 参数：{},cause:{}", JSON.toJSONString(transferRequest), ExceptionUtils.logStackTraceInfor(e));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【集团资金调拨】transferGeneralBetweenGroupMemberRechargeAcct异常 参数：{},cause:{}", JSON.toJSONString(transferRequest), ExceptionUtils.logStackTraceInfor(e));
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctCommonOptRespDTO transferOut2Others(AcctTransferDebitReqDTO reqDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(reqDTO);
            reqDTO.getTransferList().forEach(item->ValidateUtils.validate(item));
            AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonDebitService.transferOut2Others(reqDTO);
            Boolean effectiveStatus = iCompanyService.queryAccountEffectiveStatus(reqDTO.getCompanyId());
            if(Objects.nonNull(effectiveStatus) && effectiveStatus) {
                List<AcctNeedReminderRespDTO> acctNeedReminderRespDTOS = uAcctCommonService.transferOut2OthersActivInfo(reqDTO);
                optCommonRespDTO.setAcctNeedReminderRespDTO(acctNeedReminderRespDTOS);
            }
            return optCommonRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】transferOut2Others 参数：{},账户余额不足", reqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】transferOut2Others 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (FinhubException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】transferOut2Others 参数：{}", reqDTO.toString(), payEx);
            throw payEx;
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】transferOut2Others 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 转回总账户
     *
     * @param reqDTO
     * @return
     * @throws FinAccountNoEnoughException
     */
    @Override
    public AcctCommonOptRespDTO transferOut2General(AcctTransferCommonReqDTO reqDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(reqDTO);
            return uAcctCommonDebitService.transferOut2General(reqDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】transferOut2General 参数：{},账户余额不足", reqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】transferOut2General 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】transferOut2General 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 调额
     *
     * @param reqDTO
     * @return
     */
    @Override
    @Transactional(transactionManager = "fenbeipayTransactionManager", readOnly = false, rollbackFor = Exception.class)
    public AcctCommonOptRespDTO adjust(AcctAdjustCreditReqDTO reqDTO) throws FinhubException {
        try {
            ValidateUtils.validate(reqDTO);
            AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonCreditService.adjust(reqDTO);
            return optCommonRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】adjust 参数：{},账户余额不足", reqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 转移额度到指定账户（先调减，后调增）
     *
     * @param reqDTO
     * @return
     * @throws FinhubException
     */
    @Override
    public AcctCommonOptRespDTO adjustFromSub(AcctTransferCreditToEachReqDTO reqDTO) throws FinAccountNoEnoughException {
        try {
            FinhubLogger.info("转移额度adjustFromSub reqDto:{}", JSON.toJSONString(reqDTO));
            AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(reqDTO.getCompanyId(), reqDTO.getBankName(),reqDTO.getBankAccountNo());
            AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(reqDTO.getCompanyId(), reqDTO.getBankName(),reqDTO.getBankAccountNo());
            // 参数校验，操作金额，操作类型校验
            adjustTempFromSubCheck(reqDTO, acctBusinessCredit ,acctIndividualCredit);
            // 构建操作参数
            BigDecimal operationAmount = reqDTO.getOperationAmount();
            if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType())) {
                operationAmount = operationAmount.negate();
            }
            AcctBusinessCreditRecoverTask businessRecoverTask = acctBusinessCreditRecoverTaskService.queryByAccountId(acctBusinessCredit.getAccountId());
            uAcctCommonCreditService.adjustTempFromSub(reqDTO, acctBusinessCredit, acctIndividualCredit, businessRecoverTask, operationAmount);
            return new AcctCommonOptRespDTO();
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】adjustFromSub 参数：{},账户余额不足", reqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】adjustFromSub 参数：{}", reqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】adjustFromSub 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipayTransactionManager",readOnly = false, rollbackFor = Exception.class)
    public AcctCommonOptRespDTO repayment(AcctRepaymentCreditReqDTO repaymentCreditReqDTO) throws FinhubException {
        try {
            ValidateUtils.validate(repaymentCreditReqDTO);
            AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonCreditService.repayment(repaymentCreditReqDTO);
            return optCommonRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】repayment 参数：{},账户余额不足", repaymentCreditReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】repayment 参数：{}", repaymentCreditReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】repayment 参数：{}", repaymentCreditReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】repayment 参数：{}", repaymentCreditReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 还款退回
     * @param repaymentRebackCreditReqDTO
     * @return
     * @throws FinhubException
     */
    @Override
    @Transactional(transactionManager = "fenbeipayTransactionManager",readOnly = false, rollbackFor = Exception.class)
    public AcctCommonOptRespDTO repaymentReback(AcctRepaymentCreditReqDTO repaymentRebackCreditReqDTO) throws FinhubException {
        try {
            ValidateUtils.validate(repaymentRebackCreditReqDTO);
            repaymentRebackCreditReqDTO.setTargetBankName(repaymentRebackCreditReqDTO.getBankName());
            repaymentRebackCreditReqDTO.setTargetBankAccountNo(repaymentRebackCreditReqDTO.getBankAccountNo());
            repaymentRebackCreditReqDTO.setTargetBankAcctId(repaymentRebackCreditReqDTO.getBankAccountNo());
            AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonCreditService.repaymentReback(repaymentRebackCreditReqDTO);
            return optCommonRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】repaymentReback 参数：{},账户余额不足", repaymentRebackCreditReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】repaymentReback 参数：{}", repaymentRebackCreditReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】repaymentReback 参数：{}", repaymentRebackCreditReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】repaymentReback 参数：{}", repaymentRebackCreditReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public Boolean cashWithdrawalCallBack(String bizNo) {
        AccountGeneralFlow accountGeneralFlow = uAcctGeneralService.cashWithdrawalCallBack(bizNo);
        return Objects.nonNull(accountGeneralFlow);
    }

    @Override
    @Transactional(transactionManager = "fenbeipayTransactionManager", readOnly = false, rollbackFor = Exception.class)
    public AcctGeneralOptRespDTO bankAcctWithdrawal4Stereo(AcctGeneralOptReqDTO generalOptReqDTO) {
        AcctGeneralOptRespDTO generalOptRespDTO = new AcctGeneralOptRespDTO();
        try {
            ValidateUtils.validate(generalOptReqDTO);
            String verifyCode = generalOptReqDTO.getVerifyCode();
            String verifyCodePhoneNum = generalOptReqDTO.getVerifyCodePhoneNum();
            if (StringUtils.isBlank(verifyCode) || StringUtils.isBlank(verifyCodePhoneNum)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_CASHOUT_VERIFYCODE_NULL);
            }
            if(!BankNameEnum.isCgb(generalOptReqDTO.getBankName()) && !BankNameEnum.isLianlian(generalOptReqDTO.getBankName())){
                iCaptchaService.verifyCaptcha(generalOptReqDTO.getVerifyCodePhoneNum(), generalOptReqDTO.getVerifyCode());
            }
            BankAcctFlow flow = uBankAcctService.bankAcctWithdrawal4Stereo(generalOptReqDTO);
            BeanUtils.copyProperties(flow, generalOptRespDTO);
            return generalOptRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】cashWithdrawal 参数：{},账户余额不足", generalOptReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinhubException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), payEx);
            throw payEx;
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctSettlementRechargeOptRespDTO settlement(AcctSettlementRechargeOptReqDTO acctSettlementRechargeOptReqDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(acctSettlementRechargeOptReqDTO);
            return uAcctSettlementService.settlement(acctSettlementRechargeOptReqDTO);
        }  catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】settlement 参数：{}", acctSettlementRechargeOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", acctSettlementRechargeOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinhubException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】settlement 参数：{}", acctSettlementRechargeOptReqDTO.toString(), payEx);
            throw payEx;
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】settlement 参数：{}", acctSettlementRechargeOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctSettlementRechargeOptRespDTO settlementReverse(AcctSettlementRechargeOptReqDTO acctSettlementRechargeOptReqDTO) throws FinAccountNoEnoughException {
        try {
            FinhubLogger.info("IAcctFundMgrServiceImpl#settlementReverse#req:{}", JSON.toJSONString(acctSettlementRechargeOptReqDTO));
            ValidateUtils.validate(acctSettlementRechargeOptReqDTO);
            AcctTransferCommonReqDTO commonReqDTO = new AcctTransferCommonReqDTO();
            BeanUtils.copyProperties(acctSettlementRechargeOptReqDTO,commonReqDTO);
            FinhubLogger.info("IAcctFundMgrServiceImpl#settlementReverse#transferOut#req:{}", JSON.toJSONString(commonReqDTO));
            AcctCommonOptRespDTO acctCommonOptRespDTO = uAcctSettlementService.transferOut(commonReqDTO);
            FinhubLogger.info("IAcctFundMgrServiceImpl#settlementReverse#transferOut#resp:{}", JSON.toJSONString(acctCommonOptRespDTO));
            AcctSettlementRechargeOptRespDTO acctSettlementRechargeOptRespDTO = new AcctSettlementRechargeOptRespDTO();
            BeanUtils.copyProperties(acctCommonOptRespDTO,acctSettlementRechargeOptRespDTO);
            FinhubLogger.info("IAcctFundMgrServiceImpl#settlementReverse#resp:{}", JSON.toJSONString(acctSettlementRechargeOptRespDTO));
            return acctSettlementRechargeOptRespDTO;
        }  catch (FinPayException payEx) {
            FinhubLogger.error("IAcctFundMgrServiceImpl#settlementReverse#FinPayException#req:{}", acctSettlementRechargeOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("IAcctFundMgrServiceImpl#settlementReverse#ValidateException#req:{}", acctSettlementRechargeOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinhubException payEx) {
            FinhubLogger.error("IAcctFundMgrServiceImpl#settlementReverse#FinhubException#req:{}", acctSettlementRechargeOptReqDTO.toString(), payEx);
            throw payEx;
        } catch (Exception e) {
            FinhubLogger.error("IAcctFundMgrServiceImpl#settlementReverse#Exception#req:{}", acctSettlementRechargeOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    private void sendSms(AcctBankRechargeOptReqDTO bankOptReqDTO, AccountGeneralFlow flow,AccountGeneral accountGeneral) {
        try {
            if (accountGeneral == null) {
                return;
            }
            KafkaRechargeInformTaskMsg kafkaRechargeInformTaskMsg = new KafkaRechargeInformTaskMsg();
            kafkaRechargeInformTaskMsg.setCompanyId(flow.getCompanyId());
            kafkaRechargeInformTaskMsg.setBalance(BigDecimalUtils.fenToYuan(bankOptReqDTO.getOperationAmount()));
            kafkaRechargeInformTaskMsg.setCompanyName(accountGeneral.getCompanyName());
            String plateDesc = BankNameShowConfig.makeBankMainShowName(accountGeneral.getBankName(), accountGeneral.getBankAccountNo(), FundAccountModelType.RECHARGE.getKey());
            Integer defaultTransferAccountType = accountGeneral.getDefaultTransferAccountType();
            StringBuilder stringBuilder = new StringBuilder(plateDesc);
            stringBuilder.append("-");
            if (Objects.isNull(defaultTransferAccountType)) {
                stringBuilder.append(FundAccountSubType.GENERAL_ACCOUNT.getAcctName());
            } else {
                stringBuilder.append(FundAccountSubType.getEnum(defaultTransferAccountType).getAcctName());
            }
            kafkaRechargeInformTaskMsg.setPlateDesc(stringBuilder.toString());
            //低
            iKafkaProducerPublisher.publish(kafkaRechargeInformTaskMsg);
        } catch (Exception ex) {
            FinhubLogger.error("【新账户V4.0系统异常】充值发送短信通知失败：{}", bankOptReqDTO.toString(), ex);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    private void autoTransfer(AcctBankRechargeOptReqDTO bankOptReqDTO, AccountGeneral accountGeneral) {
        try {
            AcctTransferDebitReqDTO acctTransferDebitReqDTO = new AcctTransferDebitReqDTO();
            BeanUtils.copyProperties(accountGeneral, acctTransferDebitReqDTO);
            List<AcctTransferBaseInfoDTO> subAcct = new ArrayList<>();
            AcctTransferBaseInfoDTO transferBaseInfoDTO = new AcctTransferBaseInfoDTO();
            transferBaseInfoDTO.setOperationAmount(bankOptReqDTO.getOperationAmount());
            transferBaseInfoDTO.setRemark("自动划转");
            transferBaseInfoDTO.setAccountSubType(accountGeneral.getDefaultTransferAccountType());
            if(FundAccountSubType.isBusinessAccount(accountGeneral.getDefaultTransferAccountType())) {
                AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                if(Objects.nonNull(acctBusinessDebit)) {
                    transferBaseInfoDTO.setAccountId(acctBusinessDebit.getAccountId());
                }else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
            }
            if(FundAccountSubType.isIndividualAccount(accountGeneral.getDefaultTransferAccountType())) {
                AcctIndividualDebit individualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                if(Objects.nonNull(individualDebit)) {
                    transferBaseInfoDTO.setAccountId(individualDebit.getAccountId());
                }else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
            }
            if(FundAccountSubType.isComCardAccount(accountGeneral.getDefaultTransferAccountType())) {
                AcctCompanyCard acctCompanyCard = uAcctCompanyCardService.findByCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                if(Objects.nonNull(acctCompanyCard)) {
                    transferBaseInfoDTO.setAccountId(acctCompanyCard.getAccountId());
                }else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
            }
            if(FundAccountSubType.isComPublicCardAccount(accountGeneral.getDefaultTransferAccountType())) {
                AcctPublicInfoRespRPCDTO publicInfoRespRPCDTO = acctPublicSearchService.queryByCompanyIdAndBankAccountNo(accountGeneral.getCompanyId(), BankNameEnum.getBankEnum(accountGeneral.getBankName()), accountGeneral.getBankAccountNo());
                if(Objects.nonNull(publicInfoRespRPCDTO)) {
                    transferBaseInfoDTO.setAccountId(publicInfoRespRPCDTO.getAccountPublicId());
                }else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
            }

            if(FundAccountSubType.isReimbursement(accountGeneral.getDefaultTransferAccountType())) {
                AcctReimbursement acctReimbursement = uAcctReimbursementService.findCompanyIdAndBank(accountGeneral.getCompanyId(), accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                if(Objects.nonNull(acctReimbursement)) {
                    transferBaseInfoDTO.setAccountId(acctReimbursement.getAccountId());
                }else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
            }
            
            if(FundAccountSubType.isOverseaAcct(accountGeneral.getDefaultTransferAccountType())) {
            	AcctOversea acctOversea = acctOverseaService.queryByCompanyIdAndMainIdAndBank(accountGeneral.getCompanyId(), null, accountGeneral.getBankName(), accountGeneral.getBankAccountNo());
                if(Objects.nonNull(acctOversea)) {
                    transferBaseInfoDTO.setAccountId(acctOversea.getAccountId());
                } else {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
            }
            
            subAcct.add(transferBaseInfoDTO);
            acctTransferDebitReqDTO.setTransferList(subAcct);
            acctTransferDebitReqDTO.setOperationChannelType(OperationChannelType.NORMAL.getKey());
            acctTransferDebitReqDTO.setOperationUserName(bankOptReqDTO.getOperationUserName());
            this.transferOut2Others(acctTransferDebitReqDTO);
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】自动划转失败，账户不存在：{}", bankOptReqDTO.toString(), payEx);
            String errorMsg = "【新账户V4.0系统异常】自动划转失败，账户不存在,可支配余额账户id为："+accountGeneral.getAccountGeneralId();
            dingDingMsgService.sendMsg(errorMsg);
        }catch (Exception ex) {
            FinhubLogger.error("【新账户V4.0系统异常】自动划转失败：{}", bankOptReqDTO.toString(), ex);
            String errorMsg = "【新账户V4.0系统异常】自动划转失败,可支配余额账户id为："+accountGeneral.getAccountGeneralId();
            dingDingMsgService.sendMsg(errorMsg);
        }
    }

    /**
     * 调整临时额度
     * @author: zhaoxu
     * @date: 2022-05-30 11:14:08
     */
    @Override
    public AcctCommonOptRespDTO adjustTempAmount(AcctAdjustCreditTempAmountReqDTO reqDTO) {
        boolean lock = false;
        String lockKey = CoreConstant.ACCT_ADJUST_CREDIT_PREFIX + reqDTO.getCompanyId();
        try {
            FinhubLogger.info("调整临时额度 adjustTempAmount,reqDto:{}", JSON.toJSONString(reqDTO));
            lock = redissonService.tryLock(CoreConstant.ACCT_ADJUST_CREDIT_WAIT_TIME, CoreConstant.ACCT_ADJUST_CREDIT_LOCK_TIME, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                FinhubLogger.error("调整临时额度 adjustTempAmount lock error reqDTO:{}", JSON.toJSONString(reqDTO));
                throw new FinhubException(GlobalResponseCode.REDIS_GET_LOCK_ERROR.getCode(), GlobalResponseCode.REDIS_GET_LOCK_ERROR.getType(), GlobalResponseCode.REDIS_GET_LOCK_ERROR.getMsg());
            }
            paramsValidate(reqDTO);
            return uAcctCommonCreditService.adjustTempAmount(reqDTO);
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】adjustTempAmount 参数：{}", reqDTO.toString(), payEx);
            String errorMsg = "【新账户V4.0系统异常】调整临时额度失败,companyId为："+ reqDTO.getCompanyId();
            dingDingMsgService.sendMsg(errorMsg);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjustTempAmount 参数：{}", reqDTO.toString(), e);
            dingDingMsgService.sendMsg(String.format("新账户V4.0系统异常】调整临时额度失败,companyId为：%s,异常信息：%s", reqDTO.getCompanyId(), e.getMessage()));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】adjustTempAmount 参数：{}", reqDTO.toString(), e);
            dingDingMsgService.sendMsg(String.format("新账户V4.0系统异常】调整临时额度失败,companyId为：%s,异常信息：%s", reqDTO.getCompanyId(), e.getMessage()));
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        } finally {
            if (lock) {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    FinhubLogger.error("adjustTempAmount释放锁失败,lockKey：{}", lockKey, e);
                }
            }
        }
    }

    /**
     * 参数校验
     * @author: zhaoxu
     * @date: 2022-05-30 14:36:03
     */
    private void paramsValidate(AcctAdjustCreditTempAmountReqDTO reqDTO) {
        ValidateUtils.validate(reqDTO);
        if (reqDTO.getOperationAmount().compareTo(reqDTO.getCreditAmount()) < 0) {
            FinhubLogger.error("paramsValidate 校验失败，totalCredit必须大于creditAmount,参数：{}", reqDTO.toString());
            throw new ValidateException(String.format("临时额度:[%s]必须大于固定额度:[%s]", reqDTO.getOperationAmount(), reqDTO.getCreditAmount()));
        }
    }

    /**
     * 临时额度恢复
     * @author: zhaoxu
     * @date: 2022-05-30 11:14:08
     */
    @Override
    public ResultDTO<Void> recoverTempAmount(AcctAdjustCreditTempAmountReqDTO reqDTO) {
        boolean lock = false;
        String lockKey = CoreConstant.ACCT_ADJUST_CREDIT_PREFIX + reqDTO.getCompanyId();
        try {
            FinhubLogger.info("临时额度恢复recoverTempAmount reqDTO:{}", JSON.toJSONString(reqDTO));
            lock = redissonService.tryLock(CoreConstant.ACCT_ADJUST_CREDIT_WAIT_TIME, CoreConstant.ACCT_ADJUST_CREDIT_LOCK_TIME, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                FinhubLogger.error("临时额度恢复recoverTempAmount lock error adjustToSub reqDto:{}", JSON.toJSONString(reqDTO));
                return new ResultDTO<>(false, String.valueOf(GlobalResponseCode.REDIS_GET_LOCK_ERROR.getCode()), GlobalResponseCode.REDIS_GET_LOCK_ERROR.getMsg(), GlobalResponseCode.REDIS_GET_LOCK_ERROR.getMsg());
            }
            ValidateUtils.validate(reqDTO);
            return uAcctCommonCreditService.recoverTempAmount(reqDTO);
        }  catch (ValidateException e) {
            FinhubLogger.error("临时额度恢复recoverTempAmount 异常 reqDTO:{}", JSON.toJSONString(reqDTO), e);
            String errorMsg = "【新账户V4.0系统异常】临时额度恢复失败,companyId为："+ reqDTO.getCompanyId();
            dingDingMsgService.sendMsg(errorMsg);
            return new ResultDTO<>(false, String.valueOf(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode()), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        } catch (Exception e) {
            FinhubLogger.error("临时额度恢复recoverTempAmount 异常 reqDTO:{}", JSON.toJSONString(reqDTO), e);
            String errorMsg = "【新账户V4.0系统异常】临时额度恢复失败,companyId为："+ reqDTO.getCompanyId();
            dingDingMsgService.sendMsg(errorMsg);
            return new ResultDTO<>(false, String.valueOf(GlobalResponseCode.EXCEPTION.getCode()), e.getMessage(), GlobalResponseCode.EXCEPTION.getMsg());
        } catch (Throwable e) {
            FinhubLogger.error("临时额度恢复recoverTempAmount error reqDTO:{}", JSON.toJSONString(reqDTO), e);
            String errorMsg = "【新账户V4.0系统异常】临时额度恢复失败,companyId为："+ reqDTO.getCompanyId();
            dingDingMsgService.sendMsg(errorMsg);
            return new ResultDTO<>(false, String.valueOf(GlobalResponseCode.EXCEPTION.getCode()), e.getMessage(), GlobalResponseCode.EXCEPTION.getMsg());
        } finally {
            if (lock) {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    FinhubLogger.error("recoverTempAmount释放锁失败,lockKey：{}", lockKey, e);
                }
            }
        }
    }

    /**
     * 参数校验，操作金额，操作类型校验
     * @author: zhaoxu
     * @date: 2022-06-06 15:39:33
     */
    private void adjustTempFromSubCheck(AcctTransferCreditToEachReqDTO reqDTO, AcctBusinessCredit businessCredit, AcctIndividualCredit individualCredit) {
        ValidateUtils.validate(reqDTO);
        if (reqDTO.getAccountSubType().equals(reqDTO.getAccountSubTypeTo())) {
            FinhubLogger.error("adjustTempFromSubCheck accountSubType 校验失败,reqDto:{}", JSON.toJSONString(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_ADJUST_ACCOUNT_SAME);
        }
        // 商务
        if (Objects.isNull(businessCredit)) {
            FinhubLogger.error("adjustTempFromSubCheck businessCredit 校验失败,reqDto:{}", JSON.toJSONString(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
        }
        //个人
        if (Objects.isNull(individualCredit)) {
            FinhubLogger.error("adjustTempFromSubCheck individualCredit 校验失败,reqDto:{}", JSON.toJSONString(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
        }
        // 操作金额，操作类型校验
        BigDecimal operationAmount = reqDTO.getOperationAmount();
        if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountSubType())) {
            if (businessCredit.getBalance().compareTo(operationAmount) < 0 || businessCredit.getInitCredit().add(businessCredit.getTempAmount()).compareTo(operationAmount) < 0) {
                FinhubLogger.error("adjustTempFromSubCheck buss 操作金额，操作类型校验失败,reqDto:{},businessCredit:{}", JSON.toJSONString(reqDTO), JSON.toJSONString(businessCredit));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_ADJUST_TO_SUB_NOT_ENOUGH);
            }
        } else if(FundAccountSubType.isIndividualAccount(reqDTO.getAccountSubType())) {
            if (individualCredit.getBalance().compareTo(operationAmount) < 0 || individualCredit.getInitCredit().add(individualCredit.getTempAmount()).compareTo(operationAmount) < 0) {
                FinhubLogger.error("adjustTempFromSubCheck individual 操作金额，操作类型校验失败,reqDto:{},individualCredit:{}", JSON.toJSONString(reqDTO), JSON.toJSONString(individualCredit));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_ADJUST_TO_SUB_NOT_ENOUGH);
            }
        } else {
            FinhubLogger.error("adjustTempFromSubCheck subType 校验失败,reqDto:{}", JSON.toJSONString(reqDTO));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_OPT_CHECK_ERROR);
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipayTransactionManager", readOnly = false, rollbackFor = Exception.class)
    public Integer updateGeneralFLowByFlowId(String flowId, AcctGeneralFLowUpdateReqDTO acctGeneralFLowUpdateReqDTO) {
        if(StringUtils.isBlank(flowId)){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        FinhubLogger.info("更新 AccountGeneralFlow 银行流水号 req={}", JsonUtils.toJson(acctGeneralFLowUpdateReqDTO));
        AccountGeneralFlow accountGeneralFlow = new AccountGeneralFlow();
        accountGeneralFlow.setAccountFlowId(flowId);
        accountGeneralFlow.setBankTransNo(acctGeneralFLowUpdateReqDTO.getBankTransNo());
        accountGeneralFlow.setSyncBankTransNo(acctGeneralFLowUpdateReqDTO.getSyncBankTransNo());
        int i = uAcctGeneralService.updateGeneralFLowByFlowId(accountGeneralFlow);
        if (i > 0){
            AccountGeneralFlow flow = uAcctGeneralService.queryGeneralFlowByFlowId(flowId);
            if (Objects.nonNull(flow)) {
                // 充值撤销 充值撤销失败参与对账
                if ((flow.getOperationType() == FundAcctGeneralOptType.RECHARG_SUBSCRIP_CANCEL.getKey()
                        && Objects.equals(flow.getTradeType(), FundAcctTradeType.TRANSFER_OUT.getCode())) ||
                        (flow.getOperationType() == FundAcctGeneralOptType.RECHARG_SUBSCRIP_CANCEL_FAIL.getKey()
                                && Objects.equals(flow.getTradeType(), FundAcctTradeType.TRANSFER_IN.getCode()))){

                    // 发送对账消息
                    if (BankConfigUtil.needCallBank(flow.getBankName(), flow.getCompanyId())) {
                        sendAutoAcctCheckingMsg(flow);
                    }
                }
            }
        }
        return i;
    }

    @Override
    @Transactional(transactionManager = "fenbeipayTransactionManager", readOnly = false, rollbackFor = Exception.class)
    public void zbUpgradeTransfer(String companyId, BigDecimal rechargeBalance) {
        //设置默认最近同步时间为2020-01-01
        LocalDateTime localDateTime = LocalDateTime.of(2023, 11, 10, 10, 10);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());
        Date oldStopTime = Date.from(zonedDateTime.toInstant());
        List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyIdAndBankName(companyId,BankNameEnum.ZBBANK.getCode());
        if (CollectionUtils.isEmpty(accountGenerals)) {
            return;
        }
        List<AcctBusinessDebit> businessDebits = uAcctBusinessDebitService.findByCompanyId(companyId);
        List<AcctIndividualDebit> individualDebits = uAcctIndividualDebitService.findByCompanyId(companyId);
        List<AcctCompanyCard> acctCompanyCards = uAcctCompanyCardService.findByCompanyId(companyId);
        List<AcctPublicDetailRespDTO> publicRespDtos = acctPublicSearchService.findByCompanyId(companyId);

        //计算总额
        for (AccountGeneral accountGeneral: accountGenerals) {

            BigDecimal rechargeAmount = accountGeneralFlowService.queryZBBankV2RechargeAmount(accountGeneral.getAccountGeneralId(),oldStopTime);
            Integer defaultTransferAccountType = accountGeneral.getDefaultTransferAccountType();
            /**
             * {
             *     "fctId": "0356000003922447924862988",
             *     "drawableBalance": "********",
             *     "onOrderBalance": "0",
             *     "unUseBalance": "0",
             *     "frozenStatus": false,
             *     "remark": "null"
             * }
             */
            Optional<AcctBusinessDebit> acctBusinessDebitOptional = businessDebits.stream().filter(debit -> debit.getAccountGeneralId().equals(accountGeneral.getAccountGeneralId())).findFirst();
            if (acctBusinessDebitOptional.isPresent()){
                AcctBusinessDebit acctBusinessDebit = acctBusinessDebitOptional.get();
                boolean isBusDefault = Objects.equals(acctBusinessDebit.getAccountSubType(), defaultTransferAccountType);
                if (BigDecimalUtils.hasPrice(acctBusinessDebit.getBalance())) {
                    /**
                     * {
                     *     "accountId": "ABA202302021533002706326",
                     *     "accountSubType": 5,
                     *     "bankAccountNo": "0356000007602488943390775",
                     *     "bankName": "ZBBANK",
                     *     "companyId": "639d2f16eabc2e699dc8594b",
                     *     "companyMainId": "CMA202302021533002029126",
                     *     "operationAmount": 1000000,
                     *     "operationChannelType": 5,
                     *     "operationUserId": "63d86d4a01b895421a9cb1c4",
                     *     "operationUserName": "吴鸣",
                     *     "remark": "",
                     *     "targetBankAccountNo": "0356000007602488943390775",
                     *     "targetBankName": "ZBBANK"
                     * }
                     */
                    AcctTransferCommonReqDTO acctTransferCommonReqDTO = new AcctTransferCommonReqDTO();
                    acctTransferCommonReqDTO.setAccountId(acctBusinessDebit.getAccountId());
                    acctTransferCommonReqDTO.setAccountSubType(acctBusinessDebit.getAccountSubType());
                    acctTransferCommonReqDTO.setBankAccountNo(acctBusinessDebit.getBankAccountNo());
                    acctTransferCommonReqDTO.setBankName(acctBusinessDebit.getBankName());
                    acctTransferCommonReqDTO.setCompanyId(acctBusinessDebit.getCompanyId());
                    acctTransferCommonReqDTO.setCompanyMainId(acctBusinessDebit.getCompanyMainId());
                    //商务账户余额大于新充值金额,提差额
                    BigDecimal balanceOfSub  =  acctBusinessDebit.getBalance().subtract(rechargeAmount);
                    if (BigDecimalUtils.hasPrice(rechargeAmount)) {
                        if (BigDecimalUtils.hasPrice(balanceOfSub)) {
                            acctTransferCommonReqDTO.setOperationAmount(balanceOfSub);
                            acctTransferCommonReqDTO.setOperationChannelType(OperationChannelType.SYSTEM.getKey());
                            acctTransferCommonReqDTO.setOperationUserId(OperationChannelType.SYSTEM.getValue());
                            acctTransferCommonReqDTO.setOperationUserName(OperationChannelType.SYSTEM.getValue());
                            acctTransferCommonReqDTO.setRemark("系统回收");
                            acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
                            acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
                            AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonDebitService.transferOut2General(acctTransferCommonReqDTO);
                            }
                    }else {
                        acctTransferCommonReqDTO.setOperationAmount(balanceOfSub);
                        acctTransferCommonReqDTO.setOperationChannelType(OperationChannelType.SYSTEM.getKey());
                        acctTransferCommonReqDTO.setOperationUserId(OperationChannelType.SYSTEM.getValue());
                        acctTransferCommonReqDTO.setOperationUserName(OperationChannelType.SYSTEM.getValue());
                        acctTransferCommonReqDTO.setRemark("系统回收");
                        acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
                        acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
                        AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonDebitService.transferOut2General(acctTransferCommonReqDTO);
                    }
                }
            }
            Optional<AcctIndividualDebit> acctIndividualDebitOptional = individualDebits.stream().filter(debit -> debit.getAccountGeneralId().equals(accountGeneral.getAccountGeneralId())).findFirst();
            if (acctIndividualDebitOptional.isPresent()){
                AcctIndividualDebit  acctIndividualDebit = acctIndividualDebitOptional.get();
                if (BigDecimalUtils.hasPrice(acctIndividualDebit.getBalance())) {
                    AcctTransferCommonReqDTO acctTransferCommonReqDTO = new AcctTransferCommonReqDTO();
                    acctTransferCommonReqDTO.setAccountId(acctIndividualDebit.getAccountId());
                    acctTransferCommonReqDTO.setAccountSubType(acctIndividualDebit.getAccountSubType());
                    acctTransferCommonReqDTO.setBankAccountNo(acctIndividualDebit.getBankAccountNo());
                    acctTransferCommonReqDTO.setBankName(acctIndividualDebit.getBankName());
                    acctTransferCommonReqDTO.setCompanyId(acctIndividualDebit.getCompanyId());
                    acctTransferCommonReqDTO.setCompanyMainId(acctIndividualDebit.getCompanyMainId());
                    acctTransferCommonReqDTO.setOperationAmount(acctIndividualDebit.getBalance());
                    acctTransferCommonReqDTO.setOperationChannelType(OperationChannelType.SYSTEM.getKey());
                    acctTransferCommonReqDTO.setOperationUserId(OperationChannelType.SYSTEM.getValue());
                    acctTransferCommonReqDTO.setOperationUserName(OperationChannelType.SYSTEM.getValue());
                    acctTransferCommonReqDTO.setRemark("系统回收");
                    acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
                    acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
                    AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonDebitService.transferOut2General(acctTransferCommonReqDTO);
                }
            }
            Optional<AcctCompanyCard> acctCompanyCardOptional = acctCompanyCards.stream().filter(companyCard -> companyCard.getAccountGeneralId().equals(accountGeneral.getAccountGeneralId())).findFirst();
            if (acctCompanyCardOptional.isPresent()){
                AcctCompanyCard acctCompanyCard = acctCompanyCardOptional.get();
                if (BigDecimalUtils.hasPrice(acctCompanyCard.getBalance())) {
                    AcctTransferCommonReqDTO acctTransferCommonReqDTO = new AcctTransferCommonReqDTO();
                    acctTransferCommonReqDTO.setAccountId(acctCompanyCard.getAccountId());
                    acctTransferCommonReqDTO.setAccountSubType(acctCompanyCard.getAccountSubType());
                    acctTransferCommonReqDTO.setBankAccountNo(acctCompanyCard.getBankAccountNo());
                    acctTransferCommonReqDTO.setBankName(acctCompanyCard.getBankName());
                    acctTransferCommonReqDTO.setCompanyId(acctCompanyCard.getCompanyId());
                    acctTransferCommonReqDTO.setCompanyMainId(acctCompanyCard.getCompanyMainId());
                    acctTransferCommonReqDTO.setOperationAmount(acctCompanyCard.getBalance());
                    acctTransferCommonReqDTO.setOperationChannelType(OperationChannelType.SYSTEM.getKey());
                    acctTransferCommonReqDTO.setOperationUserId(OperationChannelType.SYSTEM.getValue());
                    acctTransferCommonReqDTO.setOperationUserName(OperationChannelType.SYSTEM.getValue());
                    acctTransferCommonReqDTO.setRemark("系统回收");
                    acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
                    acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
                    AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonDebitService.transferOut2General(acctTransferCommonReqDTO);
                }
            }
            Optional<AcctPublicDetailRespDTO> acctPublicDetailRespDTOOptional = publicRespDtos.stream().filter(publicAcct -> publicAcct.getAccountGeneralId().equals(accountGeneral.getAccountGeneralId())).findFirst();
            if (acctPublicDetailRespDTOOptional.isPresent()){
                AcctPublicDetailRespDTO acctPublicDetailRespDTO = acctPublicDetailRespDTOOptional.get();
                if (BigDecimalUtils.hasPrice(acctPublicDetailRespDTO.getBalance())) {
                    AcctTransferCommonReqDTO acctTransferCommonReqDTO = new AcctTransferCommonReqDTO();
                    acctTransferCommonReqDTO.setAccountId(acctPublicDetailRespDTO.getAccountId());
                    acctTransferCommonReqDTO.setAccountSubType(FundAccountSubType.BANK_PUBLIC_ACCOUNT.getKey());
                    acctTransferCommonReqDTO.setBankAccountNo(acctPublicDetailRespDTO.getBankAccountNo());
                    acctTransferCommonReqDTO.setBankName(acctPublicDetailRespDTO.getBankName());
                    acctTransferCommonReqDTO.setCompanyId(acctPublicDetailRespDTO.getCompanyId());
                    acctTransferCommonReqDTO.setCompanyMainId(acctPublicDetailRespDTO.getCompanyMainId());
                    acctTransferCommonReqDTO.setOperationAmount(acctPublicDetailRespDTO.getBalance());
                    acctTransferCommonReqDTO.setOperationChannelType(OperationChannelType.SYSTEM.getKey());
                    acctTransferCommonReqDTO.setOperationUserId(OperationChannelType.SYSTEM.getValue());
                    acctTransferCommonReqDTO.setOperationUserName(OperationChannelType.SYSTEM.getValue());
                    acctTransferCommonReqDTO.setRemark("系统回收");
                    acctTransferCommonReqDTO.setTargetBankAccountNo(accountGeneral.getBankAccountNo());
                    acctTransferCommonReqDTO.setTargetBankName(accountGeneral.getBankName());
                    AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonDebitService.transferOut2General(acctTransferCommonReqDTO);
                }
            }
            //查询账簿余额
            BankAcctBookRespDto bankAcctBookRespDto = iBankSearchService.queryAcctBook(accountGeneral.getBankAcctId(),BankNameEnum.ZBBANK.getCode(), null);
            FinhubLogger.info("bankAcctBookRespDto: {}" + JsonUtils.toJson(bankAcctBookRespDto));
            if (bankAcctBookRespDto != null && bankAcctBookRespDto.getDrawableBalance() != null) {
                BigDecimal virtualBalance = new BigDecimal(bankAcctBookRespDto.getDrawableBalance());
                AccountGeneral accountGeneralNew = uAcctGeneralService.findByAccountId(accountGeneral.getAccountGeneralId());
                BigDecimal balanceOfGeneral = accountGeneralNew.getBalance().subtract(virtualBalance);
                boolean isBigger = balanceOfGeneral.compareTo(BigDecimal.ZERO) >=0;
                if (accountGeneral.getAccountGeneralId().equals(accountGeneralNew.getAccountGeneralId())
                        && BigDecimalUtils.hasPrice(accountGeneralNew.getBalance())
                        && isBigger
                ) {
                    AcctGeneralOptReqDTO acctGeneralOptReqDTO = new AcctGeneralOptReqDTO();
                    acctGeneralOptReqDTO.setCompanyId(accountGeneralNew.getCompanyId());
                    acctGeneralOptReqDTO.setCompanyMainId(accountGeneralNew.getCompanyMainId());
                    acctGeneralOptReqDTO.setBankName(accountGeneralNew.getBankName());
                    acctGeneralOptReqDTO.setBankAccountNo(accountGeneralNew.getBankAccountNo());

                    //虚户金额多
                    FinhubLogger.info("按账户流水提,需要人工确认,虚户金额多:{}", accountGeneralNew.getAccountGeneralId());
                    acctGeneralOptReqDTO.setOperationAmount(virtualBalance);
                    acctGeneralOptReqDTO.setOperationUserId("SYSTEM");
                    acctGeneralOptReqDTO.setOperationUserName("SYSTEM");
                    AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(accountGeneralNew.getCompanyId(), accountGeneralNew.getCompanyMainId(), accountGeneralNew.getBankName());
                    if (Objects.isNull(acctCompanyMain)) {
                        throw new FinPayException(GlobalResponseCode.MAIN_COMPANY_NO_EXIST);
                    }
                    acctGeneralOptReqDTO.setTargetBankName(acctCompanyMain.getBankCardName());
                    acctGeneralOptReqDTO.setTargetBankAccountNo(acctCompanyMain.getBankCardNo());
                    acctGeneralOptReqDTO.setRemark("众邦升级");
                    acctGeneralOptReqDTO.setOperationChannelType(OperationChannelType.SYSTEM.getKey());
                    acctGeneralOptReqDTO.setCustomerServiceId("FBT");
                    acctGeneralOptReqDTO.setCustomerServiceName("FBT");
//            acctGeneralOptReqDTO.setBizNo(accountGeneral.getBizNo());
                    AccountGeneralFlow flow = uAcctGeneralService.cashWithdrawal4Upgrade(acctGeneralOptReqDTO);
                    BeanUtils.copyProperties(flow, acctGeneralOptReqDTO);
                    FinhubLogger.info(">>>平台账户-提现调用结束,返回值:{}", JsonUtils.toJson(acctGeneralOptReqDTO));
                }
            }
            }
    }

    @Override
    public AcctGeneralOptRespDTO bankAcctTransfer4Stereo(AcctGeneralOptReqDTO generalOptReqDTO) {
        AcctGeneralOptRespDTO generalOptRespDTO = new AcctGeneralOptRespDTO();
        try {
            ValidateUtils.validate(generalOptReqDTO);
            String verifyCode = generalOptReqDTO.getVerifyCode();
            String verifyCodePhoneNum = generalOptReqDTO.getVerifyCodePhoneNum();
            if (StringUtils.isBlank(verifyCode) || StringUtils.isBlank(verifyCodePhoneNum)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_CASHOUT_VERIFYCODE_NULL);
            }
            if(!BankNameEnum.isCgb(generalOptReqDTO.getBankName()) && !BankNameEnum.isLianlian(generalOptReqDTO.getBankName())){
                iCaptchaService.verifyCaptcha(generalOptReqDTO.getVerifyCodePhoneNum(), generalOptReqDTO.getVerifyCode());
            }
            BankAcctFlow flow = uBankAcctService.bankAcctTransfer4Stereo(generalOptReqDTO);
            BeanUtils.copyProperties(flow, generalOptRespDTO);
            return generalOptRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户V4.0系统异常】cashWithdrawal 参数：{},账户余额不足", generalOptReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinhubException payEx) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), payEx);
            throw payEx;
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】cashWithdrawal 参数：{}", generalOptReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    private void sendAutoAcctCheckingMsg(AccountGeneralFlow generalFlow){
        KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
        BeanUtils.copyProperties(generalFlow, kafkaAutoAcctCheckingMsg);
        kafkaAutoAcctCheckingMsg.setAccountSubType(GENERAL_ACCOUNT.getKey());
        autoAcctCheckingEventUtil.sendAccountChangeEvent(kafkaAutoAcctCheckingMsg);
    }
}
