package com.fenbeitong.fenbeipay.rpc.service.acctdech;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.AccountRechargeWhiteListExtMapper;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.AccountRechargeWhiteListMapper;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.AcctRechargeWhiteListDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.AcctRechargeWhiteReqDTO;
import com.fenbeitong.fenbeipay.api.service.acctdech.IAcctRechargeWhiteListService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.AccountRechargeWhiteList;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: liyi
 * @Date: 2022/3/11 下午3:42
 */
@Service("iAcctRechargeWhiteListService")
public class IAcctRechargeWhiteListServiceImpl implements IAcctRechargeWhiteListService {

    @Autowired
    private AccountRechargeWhiteListMapper acctRechargeWhiteListMapper;

    @Autowired
    private AccountRechargeWhiteListExtMapper acctRechargeWhiteListExtMapper;

    @Override
    public List<AcctRechargeWhiteListDTO> queryWhiteListByPage(AcctRechargeWhiteReqDTO acctRechargeWhiteReqDTO) {
        FinhubLogger.info("【查询白名单】请求参数：{}", acctRechargeWhiteReqDTO);
        Example example = new Example(AccountRechargeWhiteList.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleteFlag", 0);
        if (StringUtils.isNotBlank(acctRechargeWhiteReqDTO.getCompanyId())) {
            criteria.andEqualTo("companyId", acctRechargeWhiteReqDTO.getCompanyId());
        }
        if (StringUtils.isNotBlank(acctRechargeWhiteReqDTO.getRechargeCompanyName())) {
            criteria.andLike("rechargeCompanyName", acctRechargeWhiteReqDTO.getRechargeCompanyName());
        }
        if (StringUtils.isNotBlank(acctRechargeWhiteReqDTO.getBankName())) {
            criteria.andEqualTo("bankName", acctRechargeWhiteReqDTO.getBankName());
        }
        
        if (StringUtils.isNotBlank(acctRechargeWhiteReqDTO.getRechargeBankAccountNo())) {
            criteria.andEqualTo("rechargeBankAccountNo", acctRechargeWhiteReqDTO.getRechargeBankAccountNo());
        }
        RowBounds rowBounds = new RowBounds(acctRechargeWhiteReqDTO.getOffset(), acctRechargeWhiteReqDTO.getPageSize());

        List<AccountRechargeWhiteList> acctRechargeWhiteLists = acctRechargeWhiteListMapper.selectByExampleAndRowBounds(example, rowBounds);
        FinhubLogger.info("【查询白名单】从DB查询出的数据：{}", JSON.toJSONString(acctRechargeWhiteLists));

        List<AcctRechargeWhiteListDTO> result = new ArrayList<>();
        for (AccountRechargeWhiteList acctRechargeWhiteList : acctRechargeWhiteLists) {
            AcctRechargeWhiteListDTO acctRechargeWhiteListDTO = new AcctRechargeWhiteListDTO();
            BeanUtils.copyProperties(acctRechargeWhiteList, acctRechargeWhiteListDTO);
            FxAcctChannelEnum channelEnum = FxAcctChannelEnum.matchChannel(acctRechargeWhiteList.getBankName());
            if (ObjectUtils.isNotEmpty(channelEnum)) {
                acctRechargeWhiteListDTO.setBankNameStr(channelEnum.getChannelName());
            } else {
                acctRechargeWhiteListDTO.setBankNameStr(BankNameEnum.getBankEnum(acctRechargeWhiteList.getBankName()).getName());
            }
            result.add(acctRechargeWhiteListDTO);
        }
        return result;
    }

    @Override
    public Integer queryWhiteListCount(AcctRechargeWhiteReqDTO acctRechargeWhiteReqDTO) {
        FinhubLogger.info("【查询白名单总数】请求参数：{}", acctRechargeWhiteReqDTO);
        Example example = new Example(AccountRechargeWhiteList.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleteFlag", 0);
        if (StringUtils.isNotBlank(acctRechargeWhiteReqDTO.getCompanyId())) {
            criteria.andEqualTo("companyId", acctRechargeWhiteReqDTO.getCompanyId());
        }
        if (StringUtils.isNotBlank(acctRechargeWhiteReqDTO.getRechargeCompanyName())) {
            criteria.andLike("rechargeCompanyName", acctRechargeWhiteReqDTO.getRechargeCompanyName());
        }
        if (StringUtils.isNotBlank(acctRechargeWhiteReqDTO.getBankName())) {
            criteria.andEqualTo("bankName", acctRechargeWhiteReqDTO.getBankName());
        }

        return acctRechargeWhiteListMapper.selectCountByExample(example);
    }

    @Override
    public AcctRechargeWhiteListDTO getCompanyByRecharge(String rechargeCompanyName, String bankCode) {
        Example example = new Example(AccountRechargeWhiteList.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleteFlag", 0);
        criteria.andEqualTo("rechargeCompanyName", rechargeCompanyName);
        criteria.andEqualTo("bankName", bankCode);

        AccountRechargeWhiteList acctRechargeWhiteList = acctRechargeWhiteListMapper.selectOneByExample(example);
        if (acctRechargeWhiteList != null) {
            AcctRechargeWhiteListDTO acctRechargeWhiteListDTO = new AcctRechargeWhiteListDTO();
            BeanUtils.copyProperties(acctRechargeWhiteList, acctRechargeWhiteListDTO);
            return acctRechargeWhiteListDTO;
        } else {
            return null;
        }
    }

    @Override
    public List<String> queryRechargeCompanyName(String keyword) {
        return acctRechargeWhiteListExtMapper.queryRechargeCompanyName(keyword);
    }


    @Override
    public void createBankWhite(AcctRechargeWhiteListDTO whiteDTO) {
        FinhubLogger.info("【添加白名单】请求参数：{}", whiteDTO);
        ValidateUtils.validate(whiteDTO);

        AcctRechargeWhiteListDTO existedWhite = getCompanyByRecharge(whiteDTO.getRechargeCompanyName(), whiteDTO.getBankName());
        if (existedWhite != null) {
            GlobalResponseCode responseCode = GlobalResponseCode.ACCOUNT_RECHARGE_WHITE_EXISTED;
            throw new FinhubException(responseCode.getCode(), responseCode.getType(), BankNameEnum.getBankEnum(whiteDTO.getBankName()).getName() + responseCode.getMsg());
        }

        AccountRechargeWhiteList acctRechargeWhiteList = new AccountRechargeWhiteList();
        BeanUtils.copyProperties(whiteDTO, acctRechargeWhiteList);
        acctRechargeWhiteList.setDeleteFlag(0);
        acctRechargeWhiteListMapper.insertSelective(acctRechargeWhiteList);
    }

    @Override
    public void deleteBankWhite(Integer id) {
        if (null == id) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }

        AccountRechargeWhiteList acctRechargeWhiteList = new AccountRechargeWhiteList();
        acctRechargeWhiteList.setId(id);
        acctRechargeWhiteList.setDeleteFlag(1);
        acctRechargeWhiteListMapper.updateByPrimaryKeySelective(acctRechargeWhiteList);
    }
}
