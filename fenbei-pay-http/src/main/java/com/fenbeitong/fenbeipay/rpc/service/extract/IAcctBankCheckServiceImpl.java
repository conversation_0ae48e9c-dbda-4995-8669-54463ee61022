package com.fenbeitong.fenbeipay.rpc.service.extract;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.AcctBankCheckBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctBankCheckProcessReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctBankCheckSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctBankCheckSearchRespDTO;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctBankCheckService;
import com.fenbeitong.fenbeipay.extract.service.UAcctBankCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName IAcctBankCheckServiceImpl
 * @Description: 企业账户余额对账差错
 * <AUTHOR>
 * @Date 2021/3/18
 **/
@Service("iAcctBankCheckService")
public class IAcctBankCheckServiceImpl implements IAcctBankCheckService {

    @Autowired
    private UAcctBankCheckService uAcctBankCheckService;

    @Override
    public ResponsePage<AcctBankCheckSearchRespDTO> queryByPage(AcctBankCheckSearchReqDTO queryReq) {
        return uAcctBankCheckService.queryByPage(queryReq);
    }

    @Override
    public void extractProcess(AcctBankCheckProcessReqDTO acctBankCheckProcessReqDTO) {
        uAcctBankCheckService.extractProcess(acctBankCheckProcessReqDTO);
    }

    @Override
    public AcctBankCheckBaseDTO insertSelective(AcctBankCheckBaseDTO acctBankCheckBaseDTO) {
        return uAcctBankCheckService.insertSelective(acctBankCheckBaseDTO);
    }
}
