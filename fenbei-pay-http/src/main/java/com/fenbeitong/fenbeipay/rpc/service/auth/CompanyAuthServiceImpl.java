package com.fenbeitong.fenbeipay.rpc.service.auth;

import java.util.Objects;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AirwallexAcctDetailDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthResultDTO;
import com.fenbeitong.dech.api.service.airwallex.AirwallexAuthPayService;
import com.fenbeitong.fenbeipay.api.model.dto.AirwallexAcctDetailRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.AuthRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.QueryAuthRequestDTO;
import com.fenbeitong.fenbeipay.api.service.auth.CompanyAuthService;

@Service("companyAuthService")
public class CompanyAuthServiceImpl implements CompanyAuthService {

	@Autowired
    private AirwallexAuthPayService airwallexAuthPayService;
	
	@Override
	public AuthRespDTO queryAuthResultByAirwallex(QueryAuthRequestDTO request) {
		AuthResultDTO authResult = airwallexAuthPayService.queryAuthResult(AuthRequestDTO.builder().companyId(request.getCompanyId()).build());
		if (Objects.isNull(authResult)) {
			return AuthRespDTO.builder().status(0).companyId(request.getCompanyId()).authorized(Boolean.FALSE).build();
		}
		
		return AuthRespDTO.builder().status(authResult.getStatus()).companyId(request.getCompanyId()).authorized(authResult.isAuthorized()).build();
	}

	/* (non-Javadoc)
	 * @see com.fenbeitong.fenbeipay.api.service.auth.CompanyAuthService#queryAirwallexAcctDetail(com.fenbeitong.fenbeipay.api.model.dto.QueryAuthRequestDTO)
	 */
	@Override
	public AirwallexAcctDetailRespDTO queryAirwallexAcctDetail(QueryAuthRequestDTO request) {
		AirwallexAcctDetailDTO detail = airwallexAuthPayService.queryAWAccountDetail(request.getCompanyId());
		if (Objects.isNull(detail)) {
			return null;
		}
		AirwallexAcctDetailRespDTO result = new AirwallexAcctDetailRespDTO();
		BeanUtils.copyProperties(detail, result);
		 return result;
	}

}
