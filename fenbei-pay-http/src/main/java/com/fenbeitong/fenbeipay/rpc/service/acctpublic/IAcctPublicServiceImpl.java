package com.fenbeitong.fenbeipay.rpc.service.acctpublic;


import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicService;
import com.fenbeitong.fenbeipay.api.constant.enums.accountpublic.AccountPublicBankAuth;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.*;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountGeneralService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 对公支付管理与注册等
 * 开卡
 * 卡解绑、绑定公司
 *
 * @since 4.0.0
 */
@Service("iAcctPublicService")
public class IAcctPublicServiceImpl implements IAcctPublicService {
    @Autowired
    private AcctPublicService acctPublicService;

    @Autowired
    protected UAccountGeneralService uAccountGeneralService;

    @Override
    public AddAcctPublicRespRPCDTO addAccountPublic(AddAcctPublicReqRPCDTO reqDTO) {
        FinhubLogger.info("【对公支付账户资料审核中】：参数=={}", JsonUtils.toJson(reqDTO));
        try {
            reqDTO.checkReq(reqDTO);
            AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(reqDTO.getCompanyId(),
                    BankNameEnum.FBT.getCode(), reqDTO.getCompanyId());
            if (ObjUtils.isEmpty(accountGeneral)){
                throw new FinPayException(GlobalResponseCode.ACCOUNT_PUBLIC_BUSINESS_NO_EXIST);
            }
            return acctPublicService.addAccountPublic(reqDTO,accountGeneral);
        } catch (FinPayException e) {
            FinhubLogger.error("【对公支付账户资料审核中】addAccountPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【对公支付账户资料审核中】addAccountPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【对公支付账户资料审核中】addAccountPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void updateOpenAcctPublic(UpdateOpenAcctPublicReqRPCDTO reqDTO) {
        FinhubLogger.info("【失败修改为开户】：参数=={}", JsonUtils.toJson(reqDTO));
        try {
            reqDTO.checkReq(reqDTO);
            acctPublicService.updateOpenAcctPublic(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【失败修改为开户】updateOpenAcctPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【失败修改为开户】updateOpenAcctPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【失败修改为开户】updateOpenAcctPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void synExamineFailBankAcct(SynAcctPublicReqRPCDTO reqDTO) {
        FinhubLogger.info("【对公支付账户资料审核失败】：参数=={}", JsonUtils.toJson(reqDTO));
        try {
            reqDTO.checkReq(reqDTO);
            acctPublicService.synExamineFailBankAcct(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【对公支付账户资料审核失败】synExamineFailBankAcct 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【对公支付账户资料审核失败】synExamineFailBankAcct 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【对公支付账户资料审核失败】synExamineFailBankAcct 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void waitAuthAccountPublic(WaitAuthPublicReqRPCDTO reqDTO) {
        FinhubLogger.info("【对公支付账户待打款认证】：参数=={}", JsonUtils.toJson(reqDTO));
        try {
            reqDTO.checkReq(reqDTO);
            acctPublicService.waitAuthAccountPublic(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【对公支付账户待打款认证】waitAuthAccountPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【对公支付账户待打款认证】waitAuthAccountPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【对公支付账户待打款认证】waitAuthAccountPublic 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void synAuthIngBankAccount(SynAcctPublicReqRPCDTO reqDTO) {
        FinhubLogger.info("【同步银行卡认证中->对公支付账户】：参数=={}", JsonUtils.toJson(reqDTO));
        try {
            reqDTO.checkReq(reqDTO);
            acctPublicService.synAuthBankAccountNo(reqDTO, AccountPublicBankAuth.AUTH_ING);
        } catch (FinPayException e) {
            FinhubLogger.error("【同步银行卡认证中失败->对公支付账户异常】synAuthIngBankAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【同步银行卡认证中失败->对公支付账户验证异常】synAuthIngBankAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【同步银行卡认证中失败->对公支付账户系统异常】synAuthIngBankAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void synAuthBankAccountNo(SynAcctPublicReqRPCDTO reqDTO) {
        FinhubLogger.info("【同步银行卡认证成功->对公支付账户】：参数=={}", JsonUtils.toJson(reqDTO));
        try {
            reqDTO.checkReq(reqDTO);
            acctPublicService.synAuthBankAccountNo(reqDTO, AccountPublicBankAuth.AUTH_SUCCESS);
        } catch (FinPayException e) {
            FinhubLogger.error("【同步银行卡认证成功->对公支付账户异常】synAuthBankAccountNo 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【同步银行卡认证成功->对公支付账户验证异常】synAuthBankAccountNo 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【同步银行卡认证成功->对公支付账户系统异常】synAuthBankAccountNo 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void synFailAuthBankAccount(SynAcctPublicReqRPCDTO reqDTO) {
        FinhubLogger.info("【同步银行卡认证失败->对公支付账户】：参数=={}", JsonUtils.toJson(reqDTO));
        try {
            reqDTO.checkReq(reqDTO);
            acctPublicService.synAuthBankAccountNo(reqDTO, AccountPublicBankAuth.AUTH_Fail);
        } catch (FinPayException e) {
            FinhubLogger.error("【同步银行卡认证失败->对公支付账户异常】synFailAuthBankAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【同步银行卡认证失败->对公支付账户验证异常】synFailAuthBankAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【同步银行卡认证失败->对公支付账户系统异常】synFailAuthBankAccount 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BindAcctPublicRespRPCDTO bindAccountPublic(BindAcctPublicReqRPCDTO reqDTO) {
        FinhubLogger.info("【银行虚户卡绑定->对公支付账户bindAccountPublic 】{}", JsonUtils.toJson(reqDTO));
        try {
            reqDTO.checkReq(reqDTO);
            return acctPublicService.bindAccountPublic(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【银行虚户卡绑定->对公支付账户异常】bindAccountPublic=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【银行虚户卡绑定->对公支付账户验证异常】bindAccountPublic=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【银行虚户卡绑定->对公支付账户系统异常】bindAccountPublic=参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void disableAcctPublic(UpdateAcctPublicReqRPCDTO updateAcctPublicReqRPCDTO) {
        FinhubLogger.info("【禁用对公账户->disableAcctPublic】{}", JsonUtils.toJson(updateAcctPublicReqRPCDTO));
        try {
            ValidateUtils.validate(updateAcctPublicReqRPCDTO);
            acctPublicService.disableAcctPublic(updateAcctPublicReqRPCDTO);
        } catch (FinPayException payEx) {
            FinhubLogger.error("【对公账户禁用异常】disableAcctPublic 参数：{}==错误{}", JsonUtils.toJson(updateAcctPublicReqRPCDTO), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【对公账户禁用参数错误】disableAcctPublic 参数：{}==错误{}", JsonUtils.toJson(updateAcctPublicReqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【对公账户禁用系统异常】disableAcctPublic 参数：{}==错误{}", JsonUtils.toJson(updateAcctPublicReqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void enableAcctPublic(UpdateAcctPublicReqRPCDTO updateAcctPublicReqRPCDTO) {
        FinhubLogger.info("【启用对公账户->enableAcctPublic】{}", JsonUtils.toJson(updateAcctPublicReqRPCDTO));
        try {
            ValidateUtils.validate(updateAcctPublicReqRPCDTO);
            acctPublicService.enableAcctPublic(updateAcctPublicReqRPCDTO);
        } catch (FinPayException payEx) {
            FinhubLogger.error("【对公账户启用异常】enableAcctPublic 参数：{}==错误{}", JsonUtils.toJson(updateAcctPublicReqRPCDTO), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【对公账户启用参数异常】enableAcctPublic 参数：{}==错误{}", JsonUtils.toJson(updateAcctPublicReqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【对公账户启用系统异常】enableAcctPublic 参数：{}==错误{}", JsonUtils.toJson(updateAcctPublicReqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    @Override
    public void synPicAcctPublic(SynPicAcctPublicReqRPCDTO reqRPCDTO) {
        FinhubLogger.info("【同步图片urlsynPicAcctPublic】{}",JsonUtils.toJson(reqRPCDTO));
        try {
            reqRPCDTO.checkReq(reqRPCDTO);
            acctPublicService.synPicAcctPublic(reqRPCDTO);
        } catch (FinPayException payEx) {
            FinhubLogger.error("【同步图片urlsynPicAcctPublic】 参数：{}==错误{}",JsonUtils.toJson(reqRPCDTO), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【同步图片urlsynPicAcctPublic】 参数：{}==错误{}",JsonUtils.toJson(reqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【【同步图片urlsynPicAcctPublic】 参数：{}==错误{}", JsonUtils.toJson(reqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public int synCostImageDownloadBatchByBizNos(List<String> bizNos){
        FinhubLogger.info("【同步下载状态为已下载】参数 bizNos={}",JsonUtils.toJson(bizNos));
        if(CollectionUtils.isEmpty(bizNos)){
            throw new FinhubException(GlobalResponseCode.ACCOUNT_PUBLIC_SYNC_DOWNLOAD_BIZNO_IS_NULL.getCode(), GlobalResponseCode.ACCOUNT_PUBLIC_SYNC_DOWNLOAD_BIZNO_IS_NULL.getType(), GlobalResponseCode.ACCOUNT_PUBLIC_SYNC_DOWNLOAD_BIZNO_IS_NULL.getMsg());
        }
        try {
            return acctPublicService.synCostImageDownloadBatchByBizNos(bizNos);
        }catch (Exception e) {
            throw new FinhubException(GlobalResponseCode.ACCOUNT_PUBLIC_SYNC_DOWNLOAD_ERROR.getCode(), GlobalResponseCode.ACCOUNT_PUBLIC_SYNC_DOWNLOAD_ERROR.getType(), GlobalResponseCode.ACCOUNT_PUBLIC_SYNC_DOWNLOAD_ERROR.getMsg());
        }
    }
}
