package com.fenbeitong.fenbeipay.rpc.service.acctdech;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonDebitService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctDebitMainRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.AccountGeneralReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.AcctGeneralReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.AcctGeneralRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByMIdBankReqDTO;
import com.fenbeitong.fenbeipay.api.service.acctdech.IAcctGeneralService;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.query.AccountGeneralQuery;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountModelType;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: guoguangxiao
 * @date: 2022-05-17 19:08:48
 * @description:
 */
@Service("iAcctGeneralService")
public class AcctGeneralServiceImpl implements IAcctGeneralService {


    @Resource
    private AccountGeneralService accountGeneralService;

    @Resource
    private UAcctCommonDebitService uAcctCommonDebitService;

    @Override
    public List<AcctGeneralRespDTO> queryRechargeAcctList(AcctGeneralReqDTO acctGeneralReq) {

        FinhubLogger.info("AcctGeneralServiceImpl#queryRechargeAcctList#req:{}", JSON.toJSONString(acctGeneralReq));
        List<AcctGeneralRespDTO> respList = Lists.newArrayList();
        try{
            AccountGeneralQuery query = new AccountGeneralQuery();
            query.setCompanyId(acctGeneralReq.getCompanyId());
            query.setAccountModel(FundAccountModelType.RECHARGE.getKey());
            query.setBankName(acctGeneralReq.getBankName());
            List<AccountGeneral> accountGeneralList = accountGeneralService.queryList(query);
            if(CollectionUtils.isNotEmpty(accountGeneralList)){
                for(AccountGeneral accountGeneral : accountGeneralList){
                    respList.add(convertPoToVo(accountGeneral));
                }
            }

        }catch (Exception e){
            FinhubLogger.error("AcctGeneralServiceImpl#queryRechargeAcctList#error#req:{}", JSON.toJSONString(acctGeneralReq), e);
        }

        return respList;
    }


    @Override
    public AcctGeneralRespDTO getRechargeAcct(AcctGeneralReqDTO acctGeneralReq){

        FinhubLogger.info("AcctGeneralServiceImpl#getRechargeAcct#req:{}", JSON.toJSONString(acctGeneralReq));
        AcctGeneralRespDTO respDTO = null;
        try{
            AccountGeneralQuery query = new AccountGeneralQuery();
            query.setAccountGeneralId(acctGeneralReq.getAccountGeneralId());
            AccountGeneral accountGeneral = accountGeneralService.getOne(query);
            respDTO = convertPoToVo(accountGeneral);
        }catch (Exception e){
            FinhubLogger.error("AcctGeneralServiceImpl#getRechargeAcct#error#req:{}", JSON.toJSONString(acctGeneralReq), e);
        }

        return respDTO;

    }

    @Override
    public AcctGeneralRespDTO getAccountGeneral(AccountGeneralReqDTO acctGeneralReq) {
        FinhubLogger.info("AcctGeneralServiceImpl#getAccountGeneral#req:{}", JSON.toJSONString(acctGeneralReq));
        AcctGeneralRespDTO respDTO = null;
        try{
            AccountGeneralQuery query = new AccountGeneralQuery();
            BeanUtils.copyProperties(acctGeneralReq, query);
            AccountGeneral accountGeneral = accountGeneralService.getOne(query);
            respDTO = convertPoToVo(accountGeneral);
        }catch (Exception e){
            FinhubLogger.error("AcctGeneralServiceImpl#getAccountGeneral#error#req:{}", JSON.toJSONString(acctGeneralReq), e);
        }

        return respDTO;
    }

    @Override
    public AcctDebitMainRespDTO findDebitMain(AcctComGwByMIdBankReqDTO reqDTO) {
        FinhubLogger.info("AcctGeneralServiceImpl#findDebitMain#req:{}", JSON.toJSONString(reqDTO));
        AcctDebitMainRespDTO debitMain = uAcctCommonDebitService.findDebitMain(reqDTO);
        FinhubLogger.info("AcctGeneralServiceImpl#getAccountGeneral#resp:{}", JSON.toJSONString(debitMain));
        return debitMain;
    }

    private AcctGeneralRespDTO convertPoToVo(AccountGeneral accountGeneral) {
        AcctGeneralRespDTO respDTO = new AcctGeneralRespDTO();
        if(accountGeneral!=null){
            respDTO.setAccountGeneralId(accountGeneral.getAccountGeneralId());
            respDTO.setCompanyId(accountGeneral.getCompanyId());
            respDTO.setCompanyName(accountGeneral.getCompanyName());
            respDTO.setCompanyMainId(accountGeneral.getCompanyMainId());
            respDTO.setCompanyMainName(accountGeneral.getCompanyMainName());
            respDTO.setBankName(accountGeneral.getBankName());
            respDTO.setBankAccountNo(accountGeneral.getBankAccountNo());
            respDTO.setBankShowName(BankNameEnum.getBankEnum(respDTO.getBankName()).getName());
            respDTO.setBalance(accountGeneral.getBalance());
            respDTO.setFullBankAccountNo(accountGeneral.getBankAccountNo());
            respDTO.setBankAcctId(accountGeneral.getBankAcctId());
            String companyMainName = accountGeneral.getCompanyMainName();
            String bankShowName = respDTO.getBankShowName();
            String bankAccountNo = accountGeneral.getBankAccountNo();
            if(StringUtils.isNotBlank(bankAccountNo) && bankAccountNo.length() > 3 ){
                bankAccountNo = bankAccountNo.substring(bankAccountNo.length()-4);
            }
            String bankMainShowName = companyMainName + " " + bankShowName + " (" + bankAccountNo +")";
            respDTO.setBankMainShowName(bankMainShowName);
        }
        return respDTO;
    }



}
