package com.fenbeitong.fenbeipay.rpc.service.cashier;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.constant.enums.PayEnvEnum;
import com.fenbeitong.fenbeipay.api.model.ResultDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.AliTransferRequestDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.PayingAgentRequestDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.PayingAgentRespDTO;
import com.fenbeitong.fenbeipay.api.service.cashier.PaymentAgentService;
import com.fenbeitong.fenbeipay.awplus.config.PayCommonConfigBiz;
import com.fenbeitong.fenbeipay.awplus.service.gateway.ali.AlipayTransferService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.config.PayCommonConfig;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 
 * <AUTHOR> 
 * @Description: 支付宝代付服务
 * 
 * @date 2023-03-13 06:01:06 
*/
@Service("paymentAgentService")
public class PaymentAgentServiceImpl implements PaymentAgentService, InitializingBean, ApplicationContextAware {

	@Autowired
	private PayCommonConfigBiz payCommonConfigBiz;
	
	private Map<String, AlipayTransferService> transferServiceMap;
	
	private ApplicationContext context;
	
	@Value("${pay.env:dev}")
	private String env;
	
	private static final String ALIPAY_ACCT_NO_LIST_KEY = "ALIPAY_ACCT_NO_LIST";
	
	private static final String TICKET_TYPE_LIST_KEY = "TICKET_TYPE_LIST";
	
	private static final String ALIPAY_CHANNEL_TYPE_KEY = "ALIPAY_CHANNEL_TYPE";
	
	private static final String AT = "@";
	
	private static final String SERVICE_SUFFIX = "AlipayTransferService";
	
	private static final String PAY_CHANNEL = "TRANSFER";
	
	@Override
	public Boolean checkAuthPay(PayingAgentRequestDTO request) {
		if (request.isSkipAuth()) {
			FinhubLogger.error("【代付预订】跳过鉴权支付请求，参数：{}", JSON.toJSONString(request));
			return Boolean.TRUE;
		}
		PaymentAndDepartment pad = getAndDepartment();
		AlipayAuthInfor authInfor = (StringUtils.isBlank(request.getTicketType()) ? Arrays.asList(pad.getDakeAccts(), pad.getZhicaiAccts()) : 
			(getDepartmentByTicketType(request.getTicketType()) == DepartmentEnum.ZHICAI ? 
					Arrays.asList(pad.getZhicaiAccts()) : Arrays.asList(pad.getDakeAccts())))
				.stream().flatMap(List :: stream)
				.filter(auth -> Objects.equals(auth.getAlipayAcctNo(), request.getAlipayAcctNo()))
				.findFirst()
				.orElse(null);
		if (Objects.isNull(authInfor)) {
			FinhubLogger.error("【代付预订】待校验授权账户不存在，参数：{}", JSON.toJSONString(request));
			return Boolean.FALSE;
		}
		String sign = StringUtils.isNotBlank(authInfor.getPayAuthCode()) ? authInfor.getPayAuthCode() : StringUtils.EMPTY;
		if (!StringUtils.equalsIgnoreCase(sign, request.getPayAuthCode())) {
			FinhubLogger.warn("【代付预订】输入支付授权码不正确，输入授权码：{}", request.getPayAuthCode());
			return Boolean.FALSE;
		}
		
		return Boolean.TRUE;
	}

	@Override
	public PayingAgentRespDTO alipayAcctList(PayingAgentRequestDTO request) {
		try {
			checkPayChannel(request);
			DepartmentEnum department = getDepartmentByTicketType(request.getTicketType());
			PaymentAndDepartment pad = getAndDepartment();
			if (DepartmentEnum.ZHICAI == department) {
				List<String> accts = pad.getZhicaiAccts().stream().map(AlipayAuthInfor :: getAlipayAcctNo).collect(Collectors.toList());
				return PayingAgentRespDTO.builder().alipayAcctNos(accts).build();
			}
			List<String> accts = pad.getDakeAccts().stream().map(AlipayAuthInfor :: getAlipayAcctNo).collect(Collectors.toList());
			return PayingAgentRespDTO.builder().alipayAcctNos(accts).build();
		} catch (Exception e) {
			FinhubLogger.error("【代付预订】获取付款账户时异常，参数：{}, cause:{}", JSON.toJSONString(request), e.getMessage(), e);
			return PayingAgentRespDTO.builder().alipayAcctNos(Collections.emptyList()).build();
		}
	}
	
	private PaymentAndDepartment getAndDepartment() {
		PayCommonConfig config = payCommonConfigBiz.queryConfig(ALIPAY_ACCT_NO_LIST_KEY);
		return JSON.parseObject(config.getConfigValue(), PaymentAndDepartment.class);
	}
	
	private DepartmentEnum getDepartmentByTicketType(String ticketTypeCode) {
		PayCommonConfig ticketTypeConfig = payCommonConfigBiz.queryConfig(TICKET_TYPE_LIST_KEY);
		if (Objects.isNull(ticketTypeConfig)) {
			FinhubLogger.warn("工单类型：{}还没有对应的配置", ticketTypeCode);
			return null;
		}
		TicketType ticketType = JSON.parseObject(ticketTypeConfig.getConfigValue(), TicketType.class);
		if (ticketType.getZhicaiTicketTypes().contains(ticketTypeCode)) {
			return DepartmentEnum.ZHICAI;
		}
		
		return DepartmentEnum.DAKE;
	}
	
	private String buildServiceName(String alipayAcctNo) {
		return alipayAcctNo.substring(0, alipayAcctNo.indexOf(AT)).concat(SERVICE_SUFFIX);
	}
	
	private AlipayTransferService selecTransferService(AliTransferRequestDTO param) {
		if (Objects.isNull(param) || StringUtils.isBlank(param.getPayerAccountNo())) {
			FinhubLogger.error("【代付预订】付款账号为空，参数->{}", JSON.toJSONString(param));
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		String serviceName = buildServiceName(param.getPayerAccountNo());
		if (!transferServiceMap.containsKey(serviceName)) {
			FinhubLogger.error("【代付预订】没有与账号对应的服务，参数->{}", JSON.toJSONString(param));
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		return transferServiceMap.get(serviceName);
	}

	@Override
	public ResultDTO<String> aliTransfer(AliTransferRequestDTO param) {
    	try {
//    		if (param.isSkipAuth() && PayEnvEnum.isNotProd(env)) {
//    			FinhubLogger.error("【代付预订】正试图使用非线上环境跳过鉴权付款，参数：{}", JSON.toJSONString(param));
//    			throw new FinhubException(401, "【代付预订】正试图使用非线上环境跳过鉴权付款");
//    		}
    		checkPayChannel(param);
    		
    		FinhubLogger.info("【代付预订】支付宝转账请求参数->{}", JSON.toJSONString(param));
    		AlipayTransferService service = selecTransferService(param);
			String result = service.transfer(param);
			return new ResultDTO<>(result);
		} catch (Exception e) {
			return new ResultDTO<>("-1", e.getMessage());
		}
	}
	
	private void checkPayChannel(Object param) {
		PayingAgentRespDTO channel = alipayChannel(PayingAgentRequestDTO.builder().build());
		if (Objects.isNull(channel) || StringUtils.isBlank(channel.getPayChannelType()) || !StringUtils.equals(PAY_CHANNEL, channel.getPayChannelType())) {
			FinhubLogger.error("【代付预订】并没有启用新渠道，参数：{}", JSON.toJSONString(param));
			throw new FinhubException(401, "【代付预订】并没有启用新渠道");
		}		
	}

	@Override
	public ResultDTO<String> queryAliTransfer(AliTransferRequestDTO param) {
		try {
			AlipayTransferService service = selecTransferService(param);
			String result = service.queryTransRecord(param);
			return new ResultDTO<>(result);
		} catch (Exception e) {
			return new ResultDTO<>("-1", e.getMessage());
		}
	}

	@Override
	public ResultDTO<String> queryAliAccount(AliTransferRequestDTO param) {
		try {
			AlipayTransferService service = selecTransferService(param);
			String result = service.queryAccountInfor(param.getPayerAccountNo());
			return new ResultDTO<>(result);
		} catch (Exception e) {
			return new ResultDTO<>("-1", e.getMessage());
		}
	}
	
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class TicketType {
		
		private Set<String> zhicaiTicketTypes;
		
		private Set<String> dakeTicketTypes;
	}
	
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class PaymentAndDepartment {
		
		private List<AlipayAuthInfor> zhicaiAccts;
		
		private List<AlipayAuthInfor> dakeAccts;
	}
	
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class AlipayAuthInfor {
		
		private String alipayAcctNo;
		
		private String payAuthCode;
	}
	
	public enum DepartmentEnum {
		ZHICAI, DAKE;
	}

	@Override
	public PayingAgentRespDTO alipayChannel(PayingAgentRequestDTO request) {
		PayCommonConfig channelConfig = payCommonConfigBiz.queryConfig(ALIPAY_CHANNEL_TYPE_KEY);
		String channel = Optional.ofNullable(channelConfig).map(PayCommonConfig :: getConfigValue).orElse(StringUtils.EMPTY);
		return PayingAgentRespDTO.builder().payChannelType(channel).build();
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		transferServiceMap = context.getBeansOfType(AlipayTransferService.class);
		FinhubLogger.info("【代付预订】完成支付宝客户端初始化，内容->{}", JSON.toJSONString(transferServiceMap));
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.context = applicationContext;
	}
}
