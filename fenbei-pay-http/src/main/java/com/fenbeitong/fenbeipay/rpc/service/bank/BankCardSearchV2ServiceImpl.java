package com.fenbeitong.fenbeipay.rpc.service.bank;


import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankCardStatus;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.v2.BankCardRpcV2RespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.v2.BankCardPageSearchV2ReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.v2.BankCardSearchV2ReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.v2.BaseV2PageRespDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchV2Service;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardExtMapper;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardMapper;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service("iBankCardSearchV2Service")
@Slf4j
public class BankCardSearchV2ServiceImpl implements IBankCardSearchV2Service {
    @Autowired
    BankCardMapper bankCardMapper;
    @Autowired
    BankCardExtMapper bankCardExtMapper;

    public BankCardRpcV2RespDTO convertToDTO(BankCard bankCard){
        if (ObjUtils.isEmpty(bankCard)) {
            return null;
        }
        BankCardRpcV2RespDTO bankCardRpcV2RespDTO = new BankCardRpcV2RespDTO();
        BeanUtils.copyProperties(bankCard, bankCardRpcV2RespDTO);
        return bankCardRpcV2RespDTO;
    }
    @Override
    public BankCardRpcV2RespDTO queryByBankAccountNo(String bankAccountNo) {
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bankAccountNo", bankAccountNo);
        BankCard bankCard = bankCardMapper.selectOneByExample(example);
        return convertToDTO(bankCard);
    }

    @Override
    public BankCardRpcV2RespDTO searchBankCardDetail(BankCardSearchV2ReqDTO bankCardSearchV2ReqDTO) {
        Example cardSearchExample = getCardSearchExample(bankCardSearchV2ReqDTO.getCompanyId(), bankCardSearchV2ReqDTO.getEmployeeId(),
                null, bankCardSearchV2ReqDTO.getBankAccountNo(),bankCardSearchV2ReqDTO.getFbCardNo(), bankCardSearchV2ReqDTO.getBankName(), null,
                null, null,null,false, null,null,null);
        BankCard bankCard = bankCardMapper.selectOneByExample(cardSearchExample);
        return convertToDTO(bankCard);
    }

    @Override
    public BaseV2PageRespDTO<BankCardRpcV2RespDTO> searchBankCardPage(BankCardPageSearchV2ReqDTO bankCardPageSearchV2ReqDTO) {
        Example cardSearchExample =
                getCardSearchExample(bankCardPageSearchV2ReqDTO.getCompanyId(), bankCardPageSearchV2ReqDTO.getEmployeeId(),
                        bankCardPageSearchV2ReqDTO.getEmployeeName(), bankCardPageSearchV2ReqDTO.getBankAccountNo(),null,
                        bankCardPageSearchV2ReqDTO.getBankName(), bankCardPageSearchV2ReqDTO.getOrgUnitId(),
                        bankCardPageSearchV2ReqDTO.getEmployeePhone(), bankCardPageSearchV2ReqDTO.cardStatus(),
                        bankCardPageSearchV2ReqDTO.getStairOrgUnitId(), bankCardPageSearchV2ReqDTO.getIsBalance(),
                        bankCardPageSearchV2ReqDTO.getOrgUnitIds(),null,null);

        int count = bankCardMapper.selectCountByExample(cardSearchExample);
        if (count == 0) {
            return BaseV2PageRespDTO.of(0, Lists.newArrayList());
        }
        RowBounds rowBounds = new RowBounds(bankCardPageSearchV2ReqDTO.getOffset(), bankCardPageSearchV2ReqDTO.getPageSize());
        List<BankCard> bankCards = bankCardMapper.selectByExampleAndRowBounds(cardSearchExample, rowBounds);
        if (CollectionUtils.isEmpty(bankCards)){
            return BaseV2PageRespDTO.of(count, Lists.newArrayList());
        }
        List<BankCardRpcV2RespDTO> bankCardRpcV2RespDTOS = new ArrayList<>();
        bankCards.forEach(bankCard -> {
            bankCardRpcV2RespDTOS.add(convertToDTO(bankCard));
        });
        return BaseV2PageRespDTO.of(count, bankCardRpcV2RespDTOS);
    }

    @Override
    public List<BankCardRpcV2RespDTO> queryUnbindByPhone(String phone) {
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("employeePhone", phone);
        criteria.andEqualTo("cardStatus", BankCardStatus.UNBIND.getKey());
        List<BankCard> bankCards = bankCardMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(bankCards)) {
            return null;
        }
        List<BankCardRpcV2RespDTO> bankCardRpcV2RespDTOS = new ArrayList<>();
        bankCards.forEach(bankCard -> {
            bankCardRpcV2RespDTOS.add(convertToDTO(bankCard));
        });
        return bankCardRpcV2RespDTOS;
    }

    @Override
    public List<BankCardRpcV2RespDTO> queryBankCardUsableAmount(BankCardPageSearchV2ReqDTO bankCardPageSearchV2ReqDTO) {
        List<BankCardRpcV2RespDTO> resultList = new ArrayList<>();
        //不查询出解绑的用户
        if (ObjUtils.isNotBlank(BankCardStatus.getEnum(bankCardPageSearchV2ReqDTO.getCardStatus())) &&
                BankCardStatus.isNotWebQuery(bankCardPageSearchV2ReqDTO.getCardStatus())) {
            return resultList;
        }
        Example cardSearchExample = getCardSearchExample(bankCardPageSearchV2ReqDTO.getCompanyId(), bankCardPageSearchV2ReqDTO.getEmployeeId(),
                bankCardPageSearchV2ReqDTO.getEmployeeName(), bankCardPageSearchV2ReqDTO.getBankAccountNo(),null, bankCardPageSearchV2ReqDTO.getBankName(), bankCardPageSearchV2ReqDTO.getOrgUnitId(),
                bankCardPageSearchV2ReqDTO.getEmployeePhone(), bankCardPageSearchV2ReqDTO.cardStatus(), bankCardPageSearchV2ReqDTO.getStairOrgUnitId(), bankCardPageSearchV2ReqDTO.getIsBalance(), bankCardPageSearchV2ReqDTO.getOrgUnitIds(),
                null,null);
        int count = bankCardMapper.selectCountByExample(cardSearchExample);
        if (count == 0) {
            return resultList;
        }
        List<BankCard> bankCards = bankCardMapper.selectByExample(cardSearchExample);
        if (CollectionUtils.isEmpty(bankCards)){
            return resultList;
        }
        bankCards.forEach(bankCard ->{
            BankCardRpcV2RespDTO bankCardRpcV2RespDTO = new BankCardRpcV2RespDTO();
            BeanUtils.copyProperties(bankCard,bankCardRpcV2RespDTO);
            resultList.add(bankCardRpcV2RespDTO);
        });
        return resultList;
    }

    @Override
    public List<BankCardRpcV2RespDTO> query4ExportBankCardList(BankCardPageSearchV2ReqDTO bankCardPageSearchV2ReqDTO) {

        Example cardSearchExample = getCardSearchExample(bankCardPageSearchV2ReqDTO.getCompanyId(), null,
                bankCardPageSearchV2ReqDTO.getEmployeeName(),
                bankCardPageSearchV2ReqDTO.getBankAccountNo(), null,
                bankCardPageSearchV2ReqDTO.getBankName(),
                bankCardPageSearchV2ReqDTO.getOrgUnitId(),
                bankCardPageSearchV2ReqDTO.getEmployeePhone(),
                bankCardPageSearchV2ReqDTO.cardStatus(),
                bankCardPageSearchV2ReqDTO.getStairOrgUnitId(),
                bankCardPageSearchV2ReqDTO.getIsBalance(),
                bankCardPageSearchV2ReqDTO.getOrgUnitIds(),
                null,
                bankCardPageSearchV2ReqDTO.getEndDate());
        cardSearchExample.orderBy("createTime").asc();
        List<BankCard> bankCards = bankCardMapper.selectByExample(cardSearchExample);
        List<BankCardRpcV2RespDTO> bankCardRpcV2RespDTOS = new ArrayList<>();
        bankCards.forEach(bankCard -> {
            bankCardRpcV2RespDTOS.add(convertToDTO(bankCard));
        });
        return bankCardRpcV2RespDTOS;
    }

    @Override
    public List<String> queryFbCardNo(BankCardPageSearchV2ReqDTO bankCardPageSearchV2ReqDTO) {
        Example cardSearchExample = getCardSearchExample(bankCardPageSearchV2ReqDTO.getCompanyId(), null,
                bankCardPageSearchV2ReqDTO.getEmployeeName(), bankCardPageSearchV2ReqDTO.getBankAccountNo(),
                null, bankCardPageSearchV2ReqDTO.getBankName(), bankCardPageSearchV2ReqDTO.getOrgUnitId(),
                bankCardPageSearchV2ReqDTO.getEmployeePhone(), bankCardPageSearchV2ReqDTO.cardStatus(),
                bankCardPageSearchV2ReqDTO.getStairOrgUnitId(),bankCardPageSearchV2ReqDTO.getIsBalance(),
                bankCardPageSearchV2ReqDTO.getOrgUnitIds(),null,null);
        cardSearchExample.selectProperties("fbCardNo");
        cardSearchExample.orderBy("createTime").asc();
        List<BankCard> bankCards = bankCardMapper.selectByExample(cardSearchExample);
        if (CollectionUtils.isEmpty(bankCards)) {
            return Lists.newArrayList();
        }
        return bankCards.stream().map(BankCard::getFbCardNo).collect(Collectors.toList());
    }

    @Override
    public int countOfHavingCardCompanyBalanceByCompanyId(String companyId) {
        if(StringUtils.isBlank(companyId)){
            return 0;
        }
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("companyId", companyId);
        criteria.andNotEqualTo("cardCompanyBalance",0);
        return bankCardMapper.selectCountByExample(example);
    }

    @Override
    public List<BankCardRpcV2RespDTO> queryByParams(BankCardPageSearchV2ReqDTO bankCardPageSearchV2ReqDTO) {
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getCompanyId())) {
            criteria.andEqualTo("companyId", bankCardPageSearchV2ReqDTO.getCompanyId());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getEmployeeId())) {
            criteria.andEqualTo("employeeId", bankCardPageSearchV2ReqDTO.getEmployeeId());
        }else {
            if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getEmployeeIds())) {
                criteria.andIn("employeeId", bankCardPageSearchV2ReqDTO.getEmployeeIds());
            }
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getBankName())) {
            criteria.andEqualTo("bankName", bankCardPageSearchV2ReqDTO.getBankName());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getBankAccountNo())) {
            criteria.andEqualTo("bankAccountNo", bankCardPageSearchV2ReqDTO.getBankAccountNo());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getCardStatus())) {
            criteria.andEqualTo("cardStatus", bankCardPageSearchV2ReqDTO.getCardStatus());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getFbCardNo())) {
            criteria.andEqualTo("fbCardNo", bankCardPageSearchV2ReqDTO.getFbCardNo());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getBankAcctId())) {
            criteria.andEqualTo("bankAcctId", bankCardPageSearchV2ReqDTO.getBankAcctId());
        }


        List<BankCard> bankCards = bankCardMapper.selectByExample(example);
        List<BankCardRpcV2RespDTO> bankCardRpcV2RespDTOS = new ArrayList<>();
        bankCards.forEach(bankCard -> {
            bankCardRpcV2RespDTOS.add(convertToDTO(bankCard));
        });
        return bankCardRpcV2RespDTOS;
    }

    @Override
    public BaseV2PageRespDTO<BankCardRpcV2RespDTO> stereoQuery(BankCardPageSearchV2ReqDTO bankCardPageSearchV2ReqDTO) {
        Example example = getStereoQueryExample(bankCardPageSearchV2ReqDTO);
        //分页数据
        example.setOrderByClause("create_time desc limit "+ bankCardPageSearchV2ReqDTO.getOffset()+","+bankCardPageSearchV2ReqDTO.getPageSize());
        List<BankCard> bankCards = bankCardMapper.selectByExample(example);
        int count = bankCardMapper.selectCountByExample(example);
        if (CollectionUtils.isEmpty(bankCards)){
            return BaseV2PageRespDTO.of(count, Lists.newArrayList());
        }
        List<BankCardRpcV2RespDTO> bankCardRpcV2RespDTOS = new ArrayList<>();
        bankCards.forEach(bankCard -> {
            bankCardRpcV2RespDTOS.add(convertToDTO(bankCard));
        });
        return BaseV2PageRespDTO.of(count, bankCardRpcV2RespDTOS);
    }

    @Override
    public Boolean updatePettyCardModelType(Long id, Integer cardMode) {
        return bankCardExtMapper.updatePettyCardModelType(id,cardMode) > 0;
    }

    @Override
    public List<String> getVirtualCardCompanyIds() {
        return bankCardExtMapper.getVirtualCardCompanyIds();
    }

    @Override
    public List<String> getCardUsersByCompanyId(String companyId) {
        return bankCardExtMapper.getCardUsersByCompanyId(companyId);
    }

    @Override
    public int findBankEmployee(String companyId, String employeeId) {
        return bankCardExtMapper.findBankEmployee(companyId,employeeId);
    }

    @Override
    public int countCompanyEmployeeBankCredit(String companyId, String bankName) {
        return bankCardExtMapper.countCompanyEmployeeBankCredit(companyId,bankName);
    }

    @Override
    public List<String> findBalanceEmployeeByCompanyId(String companyId) {
        return bankCardExtMapper.findBalanceEmployeeByCompanyId(companyId);
    }

    private Example getStereoQueryExample(BankCardPageSearchV2ReqDTO bankCardPageSearchV2ReqDTO) {
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getEmployeeId())) {
            criteria.andEqualTo("employeeId", bankCardPageSearchV2ReqDTO.getEmployeeId());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getEmployeeName())) {
            criteria.andEqualTo("employeeName", bankCardPageSearchV2ReqDTO.getEmployeeName());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getEmployeePhone())) {
            criteria.andEqualTo("employeePhone", bankCardPageSearchV2ReqDTO.getEmployeePhone());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getBankAccountNo())) {
            criteria.andEqualTo("bankAccountNo", bankCardPageSearchV2ReqDTO.getBankAccountNo());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getBankName())) {
            criteria.andEqualTo("bankName", bankCardPageSearchV2ReqDTO.getBankName());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getCompanyId())) {
            criteria.andEqualTo("companyId", bankCardPageSearchV2ReqDTO.getCompanyId());
        }
        if (ObjUtils.isNotBlank(bankCardPageSearchV2ReqDTO.getCompanyName())) {
            criteria.andEqualTo("companyName", bankCardPageSearchV2ReqDTO.getCompanyName());
        }
        if (null != bankCardPageSearchV2ReqDTO.getCardStatus()) {
            criteria.andEqualTo("cardStatus", bankCardPageSearchV2ReqDTO.getCardStatus());
        }
        if (null != bankCardPageSearchV2ReqDTO.getStartTime() && null != bankCardPageSearchV2ReqDTO.getEndTime()) {
            criteria.andBetween("createTime", bankCardPageSearchV2ReqDTO.getStartTime(), bankCardPageSearchV2ReqDTO.getEndTime());
        }
        return example;
    }

    private Example getCardSearchExample(String companyId, String employeeId, String employeeName,
                                         String bankAccountNo, String fbCardNo, String bankName, String orgUnitId, String employeePhone,
                                         List<Integer> cardStatus, String stairOrgUnitId, Boolean isBalance, List<String> orgUnitIds, String startDate, String endDate) {
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        //查询条件
        if (ObjUtils.isNotBlank(companyId)) {
            criteria.andEqualTo("companyId", companyId);
        }
        if (ObjUtils.isNotBlank(employeeId)) {
            criteria.andEqualTo("employeeId", employeeId);
        }
        if (ObjUtils.isNotBlank(employeeName)) {
            criteria.andLike("employeeName","%"+employeeName+"%");
        }
        if (ObjUtils.isNotBlank(bankName)) {
            criteria.andEqualTo("bankName", bankName);
        }
        if (ObjUtils.isNotBlank(bankAccountNo)) {
            criteria.andEqualTo("bankAccountNo", bankAccountNo);
        }
        if (ObjUtils.isNotBlank(fbCardNo)) {
            criteria.andEqualTo("fbCardNo", fbCardNo);
        }
        if (ObjUtils.isNotBlank(orgUnitId)) {
            criteria.andEqualTo("orgUnitId", orgUnitId);
        }
        if (ObjUtils.isNotBlank(employeePhone)) {
            criteria.andEqualTo("employeePhone", employeePhone);
        }
        if (ObjUtils.isNotBlank(cardStatus)) {
            criteria.andIn("cardStatus", cardStatus);
        }
        if (ObjUtils.isNotBlank(stairOrgUnitId)) {
            criteria.andEqualTo("stairOrgUnitId", stairOrgUnitId);
        }
        if (ObjUtils.isNotEmpty(isBalance) && isBalance){
            criteria.andGreaterThan("cardCompanyBalance", BigDecimal.ZERO);
        }
        if (CollectionUtils.isNotEmpty(orgUnitIds)) {
            criteria.andIn("orgUnitId", orgUnitIds);
        }
        if (ObjUtils.isNotEmpty(startDate)) {
            Timestamp startTime = Timestamp.valueOf(startDate + " 00:00:00");
            criteria.andGreaterThanOrEqualTo("createTime",startTime);
        }
        if (ObjUtils.isNotEmpty(endDate)) {
            Timestamp endTime = Timestamp.valueOf(endDate + " 23:59:59");
            criteria.andLessThanOrEqualTo("createTime",endTime);
        }
        example.orderBy("createTime").desc();
        return example;
    }

}
