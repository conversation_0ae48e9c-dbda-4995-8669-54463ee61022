package com.fenbeitong.fenbeipay.rpc.service.acctpublic;

import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicDechService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicSearchService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.dech.resp.AcctPublicQueryDetailRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.*;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchDechService;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.acctpublic.AcctPublicFindReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.acctpublic.AcctPublicFindRespVo;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 对公支付查询
 * 详情
 * 流水查询
 *
 * @since 4.0.0
 */
@Service("iAcctPublicSearchDechService")
public class IAcctPublicSearchDechServiceImpl implements IAcctPublicSearchDechService {

    @Autowired
    private AcctPublicDechService acctPublicDechService;

    @Override
    public AcctPublicQueryDetailRespDTO queryByCompanyAcctId(String companyId,String companyAccountId) {
        if (ObjUtils.isBlank(companyAccountId) ) {
            FinhubLogger.error("【对公账户查询参数异常】queryByCompanyAcctId 参数：{}", companyAccountId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicDechService.queryAcctPublicByCompanyAcctId(companyId,companyAccountId);
    }

    @Override
    public Boolean authAcctPublicByStatusAcctId(String accountPublicId, List<Integer> accountPublicStatus) {
        if (ObjUtils.isBlank(accountPublicId) || CollectionUtils.isEmpty(accountPublicStatus)) {
            FinhubLogger.error("【对公账户权限参数异常】accountPublicId 参数：{}", accountPublicId, JsonUtils.toJson(accountPublicStatus));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        return acctPublicDechService.authAcctPublicByStatusAcctId(accountPublicId,accountPublicStatus);
    }
}
