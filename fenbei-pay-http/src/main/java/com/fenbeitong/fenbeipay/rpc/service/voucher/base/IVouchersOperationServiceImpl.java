package com.fenbeitong.fenbeipay.rpc.service.voucher.base;

import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponType;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersWithdrawRPCDTO;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersOperationService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcoupon;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponGrantRecord;
import com.fenbeitong.fenbeipay.redcoupon.manager.AccountRedcouponGrantRecordManager;
import com.fenbeitong.fenbeipay.redcoupon.service.AccountRedcouponService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTempletService;
import com.fenbeitong.fenbeipay.vouchers.unit.service.UVoucherRecoveryTaskService;
import com.fenbeitong.finhub.common.constant.ToBillStatusEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.luastar.swift.base.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Description: 分贝券操作RPC服务
 * @ClassName: IVouchersOperationServiceImpl
 * @Author: zhangga
 * @CreateDate: 2020/11/10 3:07 下午
 * @UpdateUser:
 * @UpdateDate: 2020/11/10 3:07 下午
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iVouchersOperationService")
public class IVouchersOperationServiceImpl implements IVouchersOperationService {
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private VouchersTempletService vouchersTempletService;
    @Autowired
    private UVoucherRecoveryTaskService uVoucherRecoveryTaskService;

    @Autowired
    private AccountRedcouponGrantRecordManager accountRedcouponGrantRecordManager;

    @Autowired
    private AccountRedcouponService accountRedcouponService;

    @Override
    public boolean updateVouchersTaskInvoiceStatus(String companyId, List<String> voucherTaskIdList, Integer writeInvoiceStatus, String operationUserId, String operationUserName) throws FinhubException {
        return vouchersTaskHandleService.updateVouchersTaskInvoiceStatus(companyId, voucherTaskIdList, writeInvoiceStatus);
    }

    @Override
    public boolean updateVouchersTaskBillStatus(List<String> voucherTaskIdList, Integer taskBillStatus, String operationUserId, String operationUserName) throws FinhubException {
        if (ToBillStatusEnum.UN_IN_BILL.getKey() == taskBillStatus || ToBillStatusEnum.BEING_IN_BILL.getKey() == taskBillStatus || ToBillStatusEnum.IS_IN_BILL.getKey() == taskBillStatus) {
            return vouchersTaskHandleService.updateVouchersTaskBillStatus(voucherTaskIdList, taskBillStatus);
        }
        return false;
    }

    @Override
    public boolean withdrawalVouchersByEmployeeIds(VouchersWithdrawRPCDTO vouchersWithdrawRPCDTO) throws FinhubException {
        String companyId = vouchersWithdrawRPCDTO.getCompanyId();
        String operationUserId = vouchersWithdrawRPCDTO.getOperationUserId();
        String operationUserName = vouchersWithdrawRPCDTO.getOperationUserName();
        vouchersWithdrawRPCDTO.getEmployeeIds().forEach(employeeId -> {
            List<String> recoveryTaskIds = uVoucherRecoveryTaskService.createWithdrawalTaskByEmployeeId(companyId, employeeId, operationUserId, operationUserName);
        });
        return true;
    }

    @Override
    public void recoveryExpiryRedCouponVouchersTask(String companyId, String grantRecordId) {
        AccountRedcoupon accountRedcoupon = accountRedcouponService.selectByCompanyIdAndType(companyId, RedcouponType.REDCOUPON.getKey());
        if(Objects.isNull(accountRedcoupon)){
            throw new FinPayException(GlobalResponseCode.ACCOUNT_REDCOUPON_NOT_EXIST);
        }
        AccountRedcouponGrantRecord accountRedcouponGrantRecord = null;
        if(Objects.nonNull(grantRecordId)) {
            List<AccountRedcouponGrantRecord> accountRedcouponGrantRecords = accountRedcouponGrantRecordManager.queryFirstRecordByAcctRedcouponIdNoCondition(accountRedcoupon.getAccountRedcouponId());
            if (CollectionUtils.isEmpty(accountRedcouponGrantRecords)) {
                return;
            }
            AccountRedcouponGrantRecord accountRedcouponGrantRecord1 = accountRedcouponGrantRecords.get(0);
            if(accountRedcouponGrantRecord1.getGrantRecordId().equals(grantRecordId)){
                accountRedcouponGrantRecord=accountRedcouponGrantRecord1;
            }
        }
        if(Objects.nonNull(accountRedcouponGrantRecord)) {
            uVoucherRecoveryTaskService.createRedCouponExpiryRecoveryTask(companyId, accountRedcouponGrantRecord);
        }
    }

    @Override
    public void useVouchersTemplet(String vouchersTempletId, String bizNo, String bizName, String operationUserId) {
        vouchersTempletService.useVouchersTemplet(vouchersTempletId, bizNo, bizName, operationUserId);
    }

    @Override
    public void removeVouchersTempletUse(String vouchersTempletId, String bizNo, String operationUserId) {
        vouchersTempletService.removeVouchersTempletUse(vouchersTempletId, bizNo, operationUserId);
    }

}
