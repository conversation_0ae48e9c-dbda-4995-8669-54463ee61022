package com.fenbeitong.fenbeipay.rpc.service.personaccount;

import com.fenbeitong.fenbeipay.account.convert.PersonAccountFlowConvert;
import com.fenbeitong.fenbeipay.account.currency.GrantTaskDetailService;
import com.fenbeitong.fenbeipay.account.currency.RecallTaskDetailService;
import com.fenbeitong.fenbeipay.account.currency.TaskService;
import com.fenbeitong.fenbeipay.account.dto.FbbGrantTasksDetailExportDto;
import com.fenbeitong.fenbeipay.account.person.GrantActivityConfigService;
import com.fenbeitong.fenbeipay.account.person.PersonAccountFowService;
import com.fenbeitong.fenbeipay.account.person.PersonAccountGrantRecordService;
import com.fenbeitong.fenbeipay.account.person.PersonAccountService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.PersonFbbFlowDto;
import com.fenbeitong.fenbeipay.api.model.dto.personaccount.*;
import com.fenbeitong.fenbeipay.api.model.dto.personaccount.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.personaccount.resp.*;
import com.fenbeitong.fenbeipay.api.model.po.personaccount.PersonAccount;
import com.fenbeitong.fenbeipay.api.service.personaccount.IPersonAccountService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.paycenter.PersonPayBusinessType;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountGrantRecord;
import com.fenbeitong.fenbeipay.core.model.vo.account.*;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.constant.FundAcctFbbOptType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by xjf on 2018/8/16.
 * 原来的Service下沉到fenbei-pay-core的PersonAccountService modify renfengjie
 */
@Service("iPersonAccountService")
public class PersonAccountServiceImpl implements IPersonAccountService {

    @Autowired
    private PersonAccountService personAccountService;

    @Autowired
    private PersonAccountFowService personAccountFowService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private GrantTaskDetailService grantTaskDetailService;

    @Autowired
    private RecallTaskDetailService recallTaskDetailService;

    @Autowired
    private GrantActivityConfigService grantActivityConfigService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private PersonAccountGrantRecordService personAccountGrantRecordService;

    @Override
    public PersonAccountDto getByEmployeeId(String employeeId) {
        try {
            return personAccountService.getByEmployeeId(employeeId);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币账户查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币账户查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币账户查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    /**
     * 根据员工id获取账户信息
     *
     * @param employeeId
     * @return
     */
    @Override
    public PersonAccount getAccountByEmployeeId(String employeeId) {
        try {
            com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount personAccount = personAccountService.getAccountByEmployeeId(employeeId);
            PersonAccount personAccountRPC = new PersonAccount();
            BeanUtils.copyProperties(personAccount, personAccountRPC);
            personAccountRPC.setLock(personAccount.getLockAccount());
            return personAccountRPC;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币账户查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币账户查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币账户查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    /**
     * 获取分贝币流水
     *
     * @param employeeId
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @Override
    public List<PersonFbbFlowDto> queryPersonFbbFlowList(String employeeId, Integer type, Integer pageIndex, Integer pageSize) {
        try {
            return personAccountService.queryPersonFbbFlowList(employeeId, type, pageIndex, pageSize);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币账户流水查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币账户流水查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币账户流水查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 获取分贝币详情
     *
     * @param employeeId
     * @param id
     * @return
     */
    @Override
    public PersonFbbFlowDto queryPersonFbbFLowInfo(String employeeId, String id) {
        try {
            return personAccountService.queryPersonFbbFLowInfo(employeeId, id);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币账户流水详情查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币账户流水详情查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币账户流水详情查询】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public int savePersonAccount(String employeeId) {
        try {
            return personAccountService.savePersonAccount(employeeId);
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝币账户】异常，参数：{}", employeeId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝币账户】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝币账户】异常，参数：{}", employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", rollbackFor = Exception.class)
    public boolean grantFbbToPerson(FbbGrantDTO fbbGrantDTO) {
        FinhubLogger.info("【接收到发放分贝币参数】{}", JsonUtils.toJson(fbbGrantDTO));
        try {
            BigDecimal grantAmount = fbbGrantDTO.getGrantAmount();
            if (grantAmount.compareTo(BigDecimal.ZERO) <= 0) {
                FinhubLogger.warn("【分贝币发放异常】返币数量：{}，员工Id：{}", grantAmount, fbbGrantDTO.getEmployeeId());
                return false;
            }
            return personAccountService.grantFbb(fbbGrantDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币发放异常】异常，参数：{}", JsonUtils.toJson(fbbGrantDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币发放异常】异常，参数：{}", JsonUtils.toJson(fbbGrantDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币发放异常】异常，参数：{}", JsonUtils.toJson(fbbGrantDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public FbbGrantRespRPCDTO grantActivityFbbToPerson(FbbActivityGrantDTO fbbGrantDTO) throws FinhubException {
        FinhubLogger.info("【接收到发放分贝币参数】{}", JsonUtils.toJson(fbbGrantDTO));
        try {
            fbbGrantDTO.validate();
            grantActivityConfigService.checkActivityLimit(fbbGrantDTO.getActivityNo(), fbbGrantDTO.getGrantAmount());
            return personAccountService.grantActivityFbb(fbbGrantDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币发放异常】异常，参数：{}", JsonUtils.toJson(fbbGrantDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币发放异常】异常，参数：{}", JsonUtils.toJson(fbbGrantDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币发放异常】异常，参数：{}", JsonUtils.toJson(fbbGrantDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public RefundGrantFbbDTO refundActGrantFbb(RefundGrantFbbReqDTO refundGrantFbbReqDTO) {
        //校验字段
        FinhubLogger.info("退款撤回分贝币{}", JsonUtils.toJson(refundGrantFbbReqDTO));
        try {
            ValidateUtils.validate(refundGrantFbbReqDTO);
            PersonAccountFlow actGrantFbb = personAccountFowService.getActGrantFbb(refundGrantFbbReqDTO.getOrderId(), refundGrantFbbReqDTO.getGrantAmount(), PersonPayBusinessType.ActRecharge, OperationChannelType.NORMAL);
            if (ObjUtils.isEmpty(actGrantFbb)) {
                throw new FinPayException(GlobalResponseCode.FBB_PERSON_GRANT_NO_EXSIT);
            }
            com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount personAccount = personAccountService.getPersonAccount(refundGrantFbbReqDTO.getEmployeeId());
            if (personAccount.getBalance().compareTo(BigDecimal.ZERO) <= 0) {
                return buildRefundGreantFbbDTO(personAccount.getEmployeeId(), refundGrantFbbReqDTO.getOrderId(), BigDecimal.ZERO);
            }
            //计算扣款金额
            BigDecimal recallAmount = personAccount.getBalance().compareTo(actGrantFbb.getAmount()) < 0 ? personAccount.getBalance() : actGrantFbb.getAmount();
            BigDecimal personAccountBalance = personAccount.getBalance().subtract(recallAmount);

            if (personAccountService.updateRecallAmount(personAccount.getId(), recallAmount) != 1) {
                throw new FinPayException(GlobalResponseCode.FBB_PERSON_REFUND_RECALL_ERROR);
            }
            personAccountService.addRefundActFbbAccountFlow(refundGrantFbbReqDTO, actGrantFbb, personAccountBalance, recallAmount);

            return buildRefundGreantFbbDTO(personAccount.getEmployeeId(), refundGrantFbbReqDTO.getOrderId(), recallAmount);
        } catch (FinPayException e) {
            FinhubLogger.error("【退款撤回分贝币】异常，参数：{}", JsonUtils.toJson(refundGrantFbbReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【退款撤回分贝币】异常，参数：{}", JsonUtils.toJson(refundGrantFbbReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【退款撤回分贝币】异常，参数：{}", JsonUtils.toJson(refundGrantFbbReqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    /**
     * @Description: 根据用户id和业务类型订单id
     * @Param: [employeeId, businessType, orderId]
     * @return: com.fenbeitong.fenbeipay.api.model.dto.paycenter.PersonFbbFlowDto
     * @Author: wh
     * @Date: 2019/4/25 3:15 PM
     */
    @Override
    public PersonFbbFlowDto queryByEmployeeIdTypeOrderId(PersonAccountFlowDTO personAccountFlowDTO) {
        try {
            PersonAccount personAccount = this.getAccountByEmployeeId(personAccountFlowDTO.getEmployeeId());
            if (ObjUtils.isEmpty(personAccount)) {
                throw new FinPayException(GlobalResponseCode.PAY_ACCOUNT_NOT_EXSIT);
            }
            List<PersonAccountFlow> personAccountFlows = personAccountFowService.queryByEmployeeIdTypeOrderId(personAccount.getId(), personAccountFlowDTO.getAmount(),
                    personAccountFlowDTO.getBusinessType(), personAccountFlowDTO.getOrderId());
            if (CollectionUtils.isEmpty(personAccountFlows) || personAccountFlows.size() > 1) {
                FinhubLogger.error("获取分贝币活动发放流水失败==employeeId{} balance{} businessType{} orderId{}", JsonUtils.toJson(personAccountFlowDTO));
                throw new FinPayException(GlobalResponseCode.FBB_PERSON_ACCOUNT_FLOW_ERROR);
            }
            return PersonAccountFlowConvert.toPersonFbbFlowDto(personAccountFlows.get(0));
        } catch (FinPayException e) {
            FinhubLogger.error("【获取分贝币活动发放流水】异常，参数：{}", JsonUtils.toJson(personAccountFlowDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【获取分贝币活动发放流水】异常，参数：{}", JsonUtils.toJson(personAccountFlowDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【获取分贝币活动发放流水】异常，参数：{}", JsonUtils.toJson(personAccountFlowDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public FbbGrantTasksRespRPCDTO createFbbGrantTask(FbbGrantTasksCreateReqRPCDTO grantTasksCreateDTO) {
        try {
            ValidateUtils.validate(grantTasksCreateDTO);
            FbbGrantRecallTasksVo tasksDTO = new FbbGrantRecallTasksVo();
            BeanUtils.copyProperties(grantTasksCreateDTO, tasksDTO);
            FbbGrantRecallTasksRespVo grantTask = taskService.createGrantTask(tasksDTO);
            if (grantTask == null) {
                return null;
            }
            FbbGrantTasksRespRPCDTO respRPCDTO = new FbbGrantTasksRespRPCDTO();
            BeanUtils.copyProperties(grantTask, respRPCDTO);
            return respRPCDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(grantTasksCreateDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(grantTasksCreateDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(grantTasksCreateDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public FbbTasksBaseRespRPCDTO executeFbbGrantTask(FbbTasksExecuteReqRPCDTO tasksExecuteDTO) {
        try {
            ValidateUtils.validate(tasksExecuteDTO);
            FbbExecuteGrantTasksVo grantTasksVo = new FbbExecuteGrantTasksVo();
            BeanUtils.copyProperties(tasksExecuteDTO, grantTasksVo);
            FbbTasksBaseRespVo grantTask = taskService.executeGrantTask(grantTasksVo);
            taskService.startGrantTask(grantTask.getTasksId());
            if (grantTask == null) {
                return null;
            }
            FbbTasksBaseRespRPCDTO respRPCDTO = new FbbTasksAddUserRespRPCDTO();
            BeanUtils.copyProperties(grantTask, respRPCDTO);
            return respRPCDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(tasksExecuteDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(tasksExecuteDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(tasksExecuteDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @return ResponsePage<FbbTasksListRespRPCDTO>
     * <AUTHOR>
     * @Date 2020/12/15 1:46 下午
     * @Description 根据条件查询分贝币发放任务列表
     * @Param tasksListReqDTO
     **/
    @Override
    public ResponsePage<FbbTasksListRespRPCDTO> queryFbbTaskList(FbbTasksListReqRPCDTO tasksListReqDTO) {
        try {
            ValidateUtils.validate(tasksListReqDTO);
            ResponsePage<FbbTasksListRespRPCDTO> rpcPage = new ResponsePage<>();
            FbbTasksListVo fbbTasksListVo = new FbbTasksListVo();
            BeanUtils.copyProperties(tasksListReqDTO, fbbTasksListVo);
            fbbTasksListVo.setUpdateStartTime(tasksListReqDTO.getStartTime());
            fbbTasksListVo.setUpdateEndTime(tasksListReqDTO.getEndTime());
            ResponsePage<FbbTasksListRespVo> taskPage = taskService.taskList(fbbTasksListVo);
            if (ObjUtils.isEmpty(taskPage)) {
                return new ResponsePage<>();
            }
            List<FbbTasksListRespVo> dataList = taskPage.getDataList();
            if (ObjUtils.isEmpty(dataList)) {
                return new ResponsePage<>();
            }
            List<FbbTasksListRespRPCDTO> rpcList = new ArrayList<>();
            dataList.forEach(data -> {
                FbbTasksListRespRPCDTO rpcdto = new FbbTasksListRespRPCDTO();
                BeanUtils.copyProperties(data, rpcdto);
                rpcList.add(rpcdto);
            });
            rpcPage.setDataList(rpcList);
            rpcPage.setTotalCount(taskPage.getTotalCount());
            rpcPage.setCondition(taskPage.getCondition());
            return rpcPage;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(tasksListReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(tasksListReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(tasksListReqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @return FbbTasksDetailRespRPCDTO
     * <AUTHOR>
     * @Date 2020/12/15 2:12 下午
     * @Description 根据taskId查询任务详情
     * @Param tasksId
     **/
    @Override
    public FbbTasksDetailRespRPCDTO queryFbbTaskInfo(String tasksId) {
        try {
            if (ObjUtils.isBlank(tasksId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            FbbTasksBaseVo fbbTasksListVo = new FbbRecallTasksVo();
            fbbTasksListVo.setTasksId(tasksId);
            FbbTasksDetailRespVo fbbTasksDetailRespVo = taskService.taskDetail(fbbTasksListVo);
            if (ObjUtils.isEmpty(fbbTasksDetailRespVo)) {
                return null;
            }
            FbbTasksDetailRespRPCDTO respRPCDTO = new FbbTasksDetailRespRPCDTO();
            BeanUtils.copyProperties(fbbTasksDetailRespVo, respRPCDTO);
            return respRPCDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", tasksId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", tasksId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", tasksId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public FbbTasksAddUserRespRPCDTO batchAddFbbGrantTask(FbbGrantTasksBatchAddUserReqRPCDTO batchAddUserDTO) {
        try {
            ValidateUtils.validate(batchAddUserDTO);
            FbbGrantUserTasksVo fbGrantRecallTasksVo = new FbbGrantUserTasksVo();
            fbGrantRecallTasksVo.build(batchAddUserDTO);
            FbbGrantUserTasksRespVo fbGrantRecallTasksRespVo = grantTaskDetailService.batchAdd(fbGrantRecallTasksVo);
            if (ObjUtils.isEmpty(fbGrantRecallTasksRespVo)) {
                return null;
            }
            FbbTasksAddUserRespRPCDTO respRPCDTO = new FbbTasksAddUserRespRPCDTO();
            BeanUtils.copyProperties(fbGrantRecallTasksRespVo, respRPCDTO);
            return respRPCDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(batchAddUserDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(batchAddUserDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币发放任务】异常，参数：{}", JsonUtils.toJson(batchAddUserDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @return com.fenbeitong.fenbeipay.api.base.ResponsePage<com.fenbeitong.fenbeipay.api.model.dto.personaccount.resp.FbbTaskInfoRespRPCDTO>
     * <AUTHOR>
     * @Date 2020/12/15 5:35 下午
     * @Description 分页查询操作的用户
     * @Param [taskInfoQueryDTO]
     **/
    @Override
    public ResponsePage<FbbTaskInfoRespRPCDTO> queryFbbTaskDetails(FbbTaskInfoQueryReqRPCDTO taskInfoQueryDTO) {
        try {
            ValidateUtils.validate(taskInfoQueryDTO);
            FbbTaskEmployeesListVo fbbTaskEmployeesListVo = new FbbTaskEmployeesListVo();
            BeanUtils.copyProperties(taskInfoQueryDTO, fbbTaskEmployeesListVo);
            ResponsePage<FbbTaskEmployeesListResqVo> fbbTaskEmployeesListRespVo = taskService.employeesList(fbbTaskEmployeesListVo);
            if (ObjUtils.isEmpty(fbbTaskEmployeesListRespVo)) {
                return new ResponsePage<>();
            }
            List<FbbTaskEmployeesListResqVo> dataList = fbbTaskEmployeesListRespVo.getDataList();
            if (ObjUtils.isEmpty(dataList)) {
                return new ResponsePage<>();
            }
            ResponsePage<FbbTaskInfoRespRPCDTO> rpcPage = new ResponsePage<>();
            List<FbbTaskInfoRespRPCDTO> rpcList = new ArrayList<>();
            dataList.forEach(data -> {
                FbbTaskInfoRespRPCDTO rpcdto = new FbbTaskInfoRespRPCDTO();
                BeanUtils.copyProperties(data, rpcdto);
                rpcdto.setRecallStatus(data.getRecallStatus() == null ? 0 : data.getRecallStatus());
                rpcList.add(rpcdto);
            });
            rpcPage.setDataList(rpcList);
            rpcPage.setTotalCount(fbbTaskEmployeesListRespVo.getTotalCount());
            rpcPage.setCondition(fbbTaskEmployeesListRespVo.getCondition());
            return rpcPage;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(taskInfoQueryDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(taskInfoQueryDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(taskInfoQueryDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<FbbGrantTasksExportRespRPCDTO> grantTaskExport(FbbTasksListReqRPCDTO exportRPCDTO) {
        try {
            ValidateUtils.validate(exportRPCDTO);
            FbbGrantTasksDetailExportDto tasksDetailExportDto = new FbbGrantTasksDetailExportDto();
            BeanUtils.copyProperties(exportRPCDTO, tasksDetailExportDto);
            FinhubLogger.info("导出分贝币任务列表参数{}",JsonUtils.toJson(tasksDetailExportDto));
            List<FbbTasksGrantExportVo> fbbTasksGrantExportVos = taskService.exportTaskList(tasksDetailExportDto);
            FinhubLogger.info("导出分贝币任务列表返回结果{}",JsonUtils.toJson(fbbTasksGrantExportVos));
            if (ObjUtils.isEmpty(fbbTasksGrantExportVos)) {
                return null;
            }
            List<FbbGrantTasksExportRespRPCDTO> exportRespRPCDTOS = new ArrayList<>();
            fbbTasksGrantExportVos.forEach(export -> {
                FbbGrantTasksExportRespRPCDTO rpcdto = new FbbGrantTasksExportRespRPCDTO();
                BeanUtils.copyProperties(export, rpcdto);
                exportRespRPCDTOS.add(rpcdto);
            });
            return exportRespRPCDTOS;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(exportRPCDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(exportRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", JsonUtils.toJson(exportRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public FbbTasksBaseRespRPCDTO createFbbRecallTask(FbbRecallTasksCreateReqRPCDTO recallTasksCreateDTO) {
        try {
            ValidateUtils.validate(recallTasksCreateDTO);
            FbbRecallTasksVo tasksDTO = new FbbRecallTasksVo();
            BeanUtils.copyProperties(recallTasksCreateDTO, tasksDTO);
            FbbTasksBaseRespVo grantTask = taskService.createRecallTask(tasksDTO);
            if (grantTask == null) {
                return null;
            }
            FbbTasksBaseRespRPCDTO respRPCDTO = new FbbTasksAddUserRespRPCDTO();
            BeanUtils.copyProperties(grantTask, respRPCDTO);
            return respRPCDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(recallTasksCreateDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(recallTasksCreateDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(recallTasksCreateDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public FbbTasksAddUserRespRPCDTO batchAddFbbRecallTask(FbbRecallTaskBatchAddUserReqRPCDTO recallBatchAddUserDTO) {
        try {
            ValidateUtils.validate(recallBatchAddUserDTO);
            FbbRecallTEmployeesLisVo tasksDTO = new FbbRecallTEmployeesLisVo();
            BeanUtils.copyProperties(recallBatchAddUserDTO, tasksDTO);
            FbbGrantUserTasksRespVo grantTask = recallTaskDetailService.batchAddRecallTask(tasksDTO);
            if (grantTask == null) {
                return null;
            }
            FbbTasksAddUserRespRPCDTO respRPCDTO = new FbbTasksAddUserRespRPCDTO();
            BeanUtils.copyProperties(grantTask, respRPCDTO);
            return respRPCDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(recallBatchAddUserDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(recallBatchAddUserDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(recallBatchAddUserDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public FbbTasksBaseRespRPCDTO executeFbbRecallTask(FbbTasksExecuteReqRPCDTO tasksExecuteDTO) {
        try {
            ValidateUtils.validate(tasksExecuteDTO);
            FbbExecuteGrantTasksVo tasksDTO = new FbbExecuteGrantTasksVo();
            BeanUtils.copyProperties(tasksExecuteDTO, tasksDTO);
            FbbTasksBaseRespVo grantTask = taskService.executeRecallTask(tasksDTO);
            taskService.startTask(grantTask.getTasksId());
            if (grantTask == null) {
                return null;
            }
            FbbTasksBaseRespRPCDTO respRPCDTO = new FbbTasksAddUserRespRPCDTO();
            BeanUtils.copyProperties(grantTask, respRPCDTO);
            return respRPCDTO;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(tasksExecuteDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(tasksExecuteDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币任务】异常，参数：{}", JsonUtils.toJson(tasksExecuteDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<FbbTaskInfoByOrderIdRespRPCDTO> queryFbbGrantRecallByOrderId(String fbOrderId) {
        try {
            if (ObjUtils.isBlank(fbOrderId)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            FbbOrderBaseVo fbbOrderBaseVo = new FbbOrderBaseVo();
            fbbOrderBaseVo.setFbOrderId(fbOrderId);
            List<FbbOrderListRespVo> grantTask = grantTaskDetailService.queryGrantRecallByOrderId(fbbOrderBaseVo);
            if (ObjUtils.isEmpty(grantTask)) {
                return new ArrayList<>();
            }
            List<FbbTaskInfoByOrderIdRespRPCDTO> dataList = new ArrayList<>();
            grantTask.forEach(task -> {
                FbbTaskInfoByOrderIdRespRPCDTO respRPCDTO = new FbbTaskInfoByOrderIdRespRPCDTO();
                BeanUtils.copyProperties(task, respRPCDTO);
                dataList.add(respRPCDTO);
            });
            return dataList;
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", fbOrderId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", fbOrderId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币任务查询】异常，参数：{}", fbOrderId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public int delPersonAccount(String accountId) throws FinhubException {
        try {
            if (ObjUtils.isEmpty(accountId)){
                throw new ValidateException("参数对象不能为空");
            }
            return personAccountService.delPersonAccountById(accountId);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝币手动账户处理】异常，参数：{}", accountId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝币手动账户处理】异常，参数：{}", accountId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝币手动账户处理】异常，参数：{}", accountId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public List<FbbFlowRespDTO> queryFbbFlowByBizNo(String bizNo, Integer operationType) {
        List<PersonAccountFlow> list =  personAccountFowService.queryPersonAccountFlowByOrderIdAndType(bizNo,operationType);
        if (CollectionUtils.isNotEmpty(list)){
            List<FbbFlowRespDTO> fbbFlowRespDTOS = Lists.newArrayList();
            for (PersonAccountFlow personAccountFlow:list){
                FbbFlowRespDTO fbbFlowRespDTO = new FbbFlowRespDTO();
                BeanUtils.copyProperties(personAccountFlow,fbbFlowRespDTO);
                fbbFlowRespDTO.setOperationAmount(personAccountFlow.getAmount());
                fbbFlowRespDTOS.add(fbbFlowRespDTO);
            }
            return fbbFlowRespDTOS;
        }
        return null;
    }

    @Override
    public List<FbbFlowRespDTO> queryFbbFlowByReBizNo(String bizNo, String reBizNo, Integer operationType) {
        List<PersonAccountFlow> list = null;
        if (reBizNo == null){
            list =  personAccountFowService.queryPersonAccountFlowByOrderIdAndType(bizNo,operationType);
        }else{
            list =  personAccountFowService.queryPersonAccountFlowByOrderIdAndType(reBizNo,operationType);
        }
        if (CollectionUtils.isNotEmpty(list)){
            List<FbbFlowRespDTO> fbbFlowRespDTOS = Lists.newArrayList();
            for (PersonAccountFlow personAccountFlow:list){
                FbbFlowRespDTO fbbFlowRespDTO = new FbbFlowRespDTO();
                BeanUtils.copyProperties(personAccountFlow,fbbFlowRespDTO);
                fbbFlowRespDTO.setOperationAmount(personAccountFlow.getAmount());
                fbbFlowRespDTOS.add(fbbFlowRespDTO);
            }
            return fbbFlowRespDTOS;
        }
        return null;
    }

    private RefundGrantFbbDTO buildRefundGreantFbbDTO(String employeeId, String orderId, BigDecimal balance) {
    	return RefundGrantFbbDTO.builder().fbbAmount(balance)
                .employeeId(employeeId)
                .orderId(orderId)
                .build();
    }

    @Override
    public FbbGrantStereoResp grantFbbStereo(FbbGrantStereoReq fbbGrantRequest) {
        FinhubLogger.info("【接收到stereo发放分贝币参数】{}", JsonUtils.toJson(fbbGrantRequest));
        FbbGrantStereoResp fbbGrantStereoResp = new FbbGrantStereoResp();
        try {
            fbbGrantRequest.validate();
            PersonAccountGrantRecord personAccountGrantRecord = personAccountGrantRecordService.queryRecordByBizNo(fbbGrantRequest.getBizNo());
            if(Objects.nonNull(personAccountGrantRecord)){
                return buildSuccessGrantResp(personAccountGrantRecord);
            }
            fbbGrantStereoResp = personAccountService.grantStereoFbb(fbbGrantRequest);
        } catch (Exception e) {
            FinhubLogger.error("【stereo分贝币发放异常】异常，参数：{}", JsonUtils.toJson(fbbGrantRequest), e);
            fbbGrantStereoResp.setStatus(2);
            fbbGrantStereoResp.setFailReason(e.getMessage());
        }
        return fbbGrantStereoResp;
    }

    private FbbGrantStereoResp buildSuccessGrantResp(PersonAccountGrantRecord personAccountGrantRecord) {
        FbbGrantStereoResp fbbGrantStereoResp = new FbbGrantStereoResp();
        fbbGrantStereoResp.setStatus(1);
        fbbGrantStereoResp.setGrantAmt(personAccountGrantRecord.getGrantAmt());
        fbbGrantStereoResp.setGrantFlowId(personAccountGrantRecord.getGrantRecordId());
        return fbbGrantStereoResp;
    }

    @Override
    public FbbRecallStereoResp recallFbbStereo(FbbRecallStereoReq fbbRecallStereoReq) {
        FinhubLogger.info("【接收到stereo撤回分贝币参数】{}", JsonUtils.toJson(fbbRecallStereoReq));
        FbbRecallStereoResp fbbRecallStereoResp = new FbbRecallStereoResp();
        try {
            fbbRecallStereoReq.validate();
            List<PersonAccountFlow> personAccountFlows = personAccountFowService.queryPersonAccountFlowByOrderIdAndType(fbbRecallStereoReq.getBizNo(), FundAcctFbbOptType.ActExchange.getKey());
            if(CollectionUtils.isNotEmpty(personAccountFlows)){
               return buildSuccessRecallResp(personAccountFlows.get(0));
            }
            fbbRecallStereoResp =  personAccountService.recallStereoFbb(fbbRecallStereoReq);
        } catch (Exception e) {
            FinhubLogger.error("【stereo分贝币撤回异常】异常，参数：{}", JsonUtils.toJson(fbbRecallStereoReq), e);
            fbbRecallStereoResp.setStatus(2);
            fbbRecallStereoResp.setFailReason(e.getMessage());
        }
        return fbbRecallStereoResp;
    }

    private FbbRecallStereoResp buildSuccessRecallResp(PersonAccountFlow personAccountFlow) {
        FbbRecallStereoResp fbbRecallStereoResp = new FbbRecallStereoResp();
        fbbRecallStereoResp.setStatus(1);
        fbbRecallStereoResp.setRecallAmt(personAccountFlow.getAmount().negate());
        fbbRecallStereoResp.setRecallFlowId(personAccountFlow.getId());
        return fbbRecallStereoResp;
    }
    
    @Override
    public ResponsePage<FbbGrantRecordDTO> queryFbbGrantRecordToExpire(QueryFbbGrantRecordRequestDTO queryRequest) {
    	ValidateUtils.validate(queryRequest);
    	ResponsePage<FbbGrantRecordDTO> resp = new ResponsePage<>();
    	Optional.ofNullable(queryRequest)
    		.filter(r -> Objects.isNull(r.getExpireTimeStart()))
    		.ifPresent(r -> r.setExpireTimeStart(new Date()));
    	
    	List<PersonAccountGrantRecord> fbbList = personAccountGrantRecordService.queryListToExpire(queryRequest);
    	PageInfo<PersonAccountGrantRecord> pageInfo = new PageInfo<>(fbbList);
    	resp.setTotalCount((int) pageInfo.getTotal());
    	if (CollectionUtils.isNotEmpty(fbbList)) {
    		List<FbbGrantRecordDTO> result = fbbList.stream().map(personAccountGrantRecordService :: buildDTObyModel).collect(Collectors.toList());
    		resp.setDataList(result);
    	}
    	
    	return resp;
    }

    @Override
    public ResponsePage<FbbGrantRecordDTO> queryFbbGrantRecord(FbbGrantRecordPageReq fbbGrantRecordPageReq) {
        ValidateUtils.validate(fbbGrantRecordPageReq);
        ResponsePage<FbbGrantRecordDTO> fbbGrantRecordDTOResponsePage = new ResponsePage<>();
        int count = personAccountGrantRecordService.selectCount(fbbGrantRecordPageReq);
        fbbGrantRecordDTOResponsePage.setTotalCount(count);
        if(count>0){
            List<FbbGrantRecordDTO> fbbGrantRecordDTOS = personAccountGrantRecordService.selectList(fbbGrantRecordPageReq);
            fbbGrantRecordDTOResponsePage.setDataList(fbbGrantRecordDTOS);
        }
        return fbbGrantRecordDTOResponsePage;
    }

    @Override
    public ResponsePage<FbbAccountFlowDTO> queryFbbFlowPage(FbbFlowQueryPageReq fbbFlowQueryPageReq) {
        ValidateUtils.validate(fbbFlowQueryPageReq);
        ResponsePage<FbbAccountFlowDTO> fbbAccountFlowDTOResponsePage = new ResponsePage<>();
        if(StringUtils.isNotBlank(fbbFlowQueryPageReq.getEmployeeId())) {
            com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount accountByEmployeeId = personAccountService.getAccountByEmployeeId(fbbFlowQueryPageReq.getEmployeeId());
            if (Objects.isNull(accountByEmployeeId)) {
                fbbAccountFlowDTOResponsePage.setDataList(Lists.newArrayList());
                fbbAccountFlowDTOResponsePage.setTotalCount(0);
                return fbbAccountFlowDTOResponsePage;
            } else {
                fbbFlowQueryPageReq.setAccountId(accountByEmployeeId.getId());
            }
        }
        int count = personAccountFowService.selectCount(fbbFlowQueryPageReq);
        fbbAccountFlowDTOResponsePage.setTotalCount(count);
        if(count>0){
            List<FbbAccountFlowDTO> fbbAccountFlowDTOS = personAccountFowService.selectList(fbbFlowQueryPageReq);
            fbbAccountFlowDTOResponsePage.setDataList(fbbAccountFlowDTOS);
        }
        return fbbAccountFlowDTOResponsePage;
    }

    @Override
    public FbbGrantRecordDTO queryFbbGrantRecordDetail(String grantRecordId) {
        return personAccountGrantRecordService.queryFbbGrantRecordDetail(grantRecordId);
    }

    @Override
    public List<FbbAccountFlowDTO> queryFbbAcctFlowByGrantRecordId(String grantRecordId) {
        return personAccountFowService.queryFbbAcctFlowByGrantRecordId(grantRecordId);
    }

    @Override
    public FbbAcctFlowDetailDTO queryFbbAcctFlowDetail(String flowId) {
        FbbAcctFlowDetailDTO fbbAcctFlowDetailDTO = new FbbAcctFlowDetailDTO();
        FbbAccountFlowDTO fbbAccountFlowDTO = personAccountFowService.queryFbbAccountFlowById(flowId);
        fbbAcctFlowDetailDTO.setFbbFlowRespDTO(fbbAccountFlowDTO);
        if(StringUtils.isNotBlank(fbbAccountFlowDTO.getGrantFlowId())) {
            FbbGrantRecordDTO fbbGrantRecordDTO = personAccountGrantRecordService.queryFbbGrantRecordDetail(fbbAccountFlowDTO.getGrantFlowId());
            fbbAcctFlowDetailDTO.setFbbGrantRecordDTO(fbbGrantRecordDTO);
        }
        return fbbAcctFlowDetailDTO;
    }

    @Override
    public Integer updateGrantRecord(String grantRecordId, String bizNo) {
        if(StringUtils.isBlank(grantRecordId)||StringUtils.isBlank(bizNo)){
            return 0;
        }
        PersonAccountGrantRecord personAccountGrantRecord = new PersonAccountGrantRecord();
        personAccountGrantRecord.setGrantRecordId(grantRecordId);
        personAccountGrantRecord.setBizNo(bizNo);
        return personAccountGrantRecordService.updateGrantRecordByRecordId(personAccountGrantRecord);
    }
}