package com.fenbeitong.fenbeipay.rpc.service.redcoupon;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctFreezenConsumeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctFreezenRefundReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOperationRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AccountRedEnvelopeFlowReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AccountRedEnvelopeFreezeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AccountRedEnvelopeUnFreezeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.AccountRedEnvelopeFlowRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.AccountRedEnvelopeFreezeRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.AccountRedEnvelopeUnFreezeRespDTO;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedEnvelopeService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.redcoupon.service.UAccountRedEnvelopeService;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.luastar.swift.base.exception.ValidateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/7/3 11:55 上午
 * @Description
 */
@Service
public class IAccountRedEnvelopeServiceImpl implements IAccountRedEnvelopeService {

    @Autowired
    private UAccountRedEnvelopeService uAccountRedEnvelopeService;

    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;
    @Override
    public AcctOperationRespDTO freeze(AccountRedEnvelopeFreezeReqDTO reqDTO) {
        try {
            return uAccountRedEnvelopeService.freeze(reqDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
        throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        }  catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】freeze 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctOperationRespDTO unFreeze(AccountRedEnvelopeUnFreezeReqDTO reqDTO) {

        try {
            return uAccountRedEnvelopeService.unFreeze(reqDTO);
        }catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        }  catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】unFreeze 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public AccountRedEnvelopeFreezeRespDTO consumeFreeze(AcctFreezenConsumeReqDTO reqDTO) {

        try {
            return uAccountRedEnvelopeService.consumeFreeze(reqDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】consumeFreeze 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AccountRedEnvelopeUnFreezeRespDTO refundFreeze(AcctFreezenRefundReqDTO reqDTO) {

        try {
            return uAccountRedEnvelopeService.refundFreeze(reqDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】consumeFreeze 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public ResponsePage<AccountRedEnvelopeFlowRespDTO> queryAccountSubFlowList(AccountRedEnvelopeFlowReqDTO reqDTO) {
        return accountRedcouponSearchService.queryAccountSubFlowList(reqDTO);
    }

    @Override
    public AccountRedEnvelopeFlowRespDTO queryAccountFlowByGrantTaskId(String voucherGrantTaskId) {
        return uAccountRedEnvelopeService.queryAccountFlowByGrantTaskId(voucherGrantTaskId);
    }

    @Override
    public AccountRedEnvelopeFlowRespDTO queryAccountFlowByFlowId(String redcouponFlowId) {
        return uAccountRedEnvelopeService.queryAccountFlowByFlowId(redcouponFlowId);
    }
}
