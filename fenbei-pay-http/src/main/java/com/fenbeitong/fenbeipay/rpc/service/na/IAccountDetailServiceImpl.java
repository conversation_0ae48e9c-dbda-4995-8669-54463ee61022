package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.api.model.dto.na.req.EmployeeAccountListReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.EmployeeAccountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.EmployeeAccountRespDTO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountDetailService;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service("iAccountDetailService")
public class IAccountDetailServiceImpl implements IAccountDetailService {

    @Autowired
    private AccountReceiveInfoDetailService accountReceiveInfoDetailService;

    @Override
    public List<EmployeeAccountRespDTO> getEmployeeAccountListByCondition(EmployeeAccountReqDTO reqDTO) {
        return accountReceiveInfoDetailService.getEmployeeAccountListByCondition(reqDTO);
    }

    @Override
    public List<EmployeeAccountRespDTO> getEmployeeAccountListByType(EmployeeAccountListReqDTO accountListReqDTO) {
        return accountReceiveInfoDetailService.getEmployeeAccountListByType(accountListReqDTO);
    }
}
