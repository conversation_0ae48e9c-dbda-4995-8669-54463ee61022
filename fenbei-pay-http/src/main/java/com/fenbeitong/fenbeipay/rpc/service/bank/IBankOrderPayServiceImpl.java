package com.fenbeitong.fenbeipay.rpc.service.bank;

import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankPayTradeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankReimbursedReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankPayTradeRespDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankOrderPayService;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.BankPettyManager;
import com.fenbeitong.fenbeipay.bank.company.order.manager.TradeCardManager;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service("iBankOrderPayService")
public class IBankOrderPayServiceImpl implements IBankOrderPayService {

    @Autowired
    private TradeCardManager tradeCardManager;
    @Autowired
    private BankPettyManager bankPettyManager;


    @Override
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public BankPayTradeRespDTO payTrade(BankPayTradeReqDTO reqDTO) {

        FinhubLogger.info("银行卡支付参数=={}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            return tradeCardManager.payTrade(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.warn("【银行卡支付异常】payTrade 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.warn("【银行卡支付验证异常】payTrade 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【银行卡支付系统异常】payTrade 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    public BigDecimal synOrderReimbursed(BankReimbursedReqDTO reqDTO) {
        FinhubLogger.info("银行卡核销金额参数=={}", JsonUtils.toJson(reqDTO));
        try {
            ValidateUtils.validate(reqDTO);
            return bankPettyManager.synOrderReimbursedSum(reqDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【银行卡核销金额异常】synOrderReimbursed 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【银行卡核销金额验证异常】synOrderReimbursed 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【银行卡核销金额系统异常】synOrderReimbursed 参数：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }

    }
}
