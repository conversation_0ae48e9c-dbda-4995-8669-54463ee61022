package com.fenbeitong.fenbeipay.rpc.service.voucher.base;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherFlowType;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.WriteInvoiceType;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.PersonTransferStereoExportRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersFlowReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.*;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersSearchService;
import com.fenbeitong.fenbeipay.cashier.transfer.PersonTransferService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlow;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersPerson;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersFlowRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersOperationFlowService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTypeService;
import com.fenbeitong.fenbeipay.vouchers.vo.VouchersFlowVO;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 分贝券查询RPC服务
 * @ClassName: IVouchersSearchServiceImpl
 * @Author: zhangga
 * @CreateDate: 2020/11/10 3:07 下午
 * @UpdateUser:
 * @UpdateDate: 2020/11/10 3:07 下午
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iVouchersSearchService")
public class IVouchersSearchServiceImpl implements IVouchersSearchService {
    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private VouchersOperationFlowService vouchersOperationFlowService;
    @Autowired
    private PersonTransferService personTransferService;
    @Autowired
    private VouchersTypeService vouchersTypeService;
    private static final String VOUCHER_TRANSFER_FLAG = "transfer_";

    @Override
    public boolean queryIsHaveUsableVouchers(String companyId, String employeeId) throws FinhubException {
        List<VouchersPerson> vouchersPersonList = vouchersPersonService.selectAvailableVouchersOfLeaveRecovery(companyId, employeeId);
        return ObjUtils.isEmpty(vouchersPersonList) ? false : true;
    }

    @Override
    public ResponsePage<VouchersRecoveryRespRPCDTO> queryAdvanceInvoiceRecoveryVoucher(VouchersFlowReqRPCDTO vouchersFlowReqRPCDTO) throws FinhubException {
        if (ObjUtils.isBlank(vouchersFlowReqRPCDTO.getCompanyId())) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "companyId 不能为空");
        }
        VouchersFlowRequestDTO requestDTO = new VouchersFlowRequestDTO();
        BeanUtils.copyProperties(vouchersFlowReqRPCDTO, requestDTO);
        requestDTO.setWriteInvoiceType(WriteInvoiceType.INVOICE_ADVANCE.getValue());
        requestDTO.setTypeList(Lists.newArrayList(VoucherFlowType.RECOVERY_INVOICED.getValue(), VoucherFlowType.WITHDRAWAL_INVOICED.getValue()));
        ResponsePage<VouchersFlowVO> responsePage = vouchersOperationFlowService.selectConsumeFlowByPage(requestDTO, true);
        ResponsePage<VouchersRecoveryRespRPCDTO> data = new ResponsePage<>();
        data.setTotalCount(responsePage.getTotalCount());
        List<VouchersRecoveryRespRPCDTO> dataList = new ArrayList<>();
        responsePage.getDataList().forEach(vo -> {
            dataList.add(new VouchersRecoveryRespRPCDTO(vo.getCreateTime(), vo.getCompanyId(), vo.getAmount()));
        });
        data.setDataList(dataList);
        data.setCondition(responsePage.getCondition());
        return data;
    }

    @Override
    public ResponsePage<VouchersOperationFlowRespRPCDTO> queryVoucherFlow(VouchersFlowReqRPCDTO vouchersFlowReqRPCDTO) throws FinhubException {
        FinhubLogger.info("分贝券流水查询参数：{}", JSONObject.toJSONString(vouchersFlowReqRPCDTO));
        ResponsePage<VouchersOperationFlow> VouchersOperationFlows = vouchersOperationFlowService.selectVouchersFlowByPage(vouchersFlowReqRPCDTO);
        Integer totalCount = VouchersOperationFlows.getTotalCount();
        if (totalCount <= 0) {
            return new ResponsePage<>();
        }
        List<VouchersOperationFlow> dataList = VouchersOperationFlows.getDataList();
        if (ObjUtils.isEmpty(dataList)) {
            return new ResponsePage<>();
        }
        //根据劵id查询VouchersTaskDetails
        Set<String> voucherIdSet = new HashSet<>();
        dataList.forEach(vouchersOperationFlow -> {
            voucherIdSet.add(vouchersOperationFlow.getVoucherId());
            voucherIdSet.add(vouchersOperationFlow.getOriginalVoucherId());
        });
        List<VouchersPerson> voucherDetails = vouchersPersonService.selectVouchersByIds(new ArrayList<>(voucherIdSet));
//        List<String> voucherIds = dataList.stream().map(vouchersOperationFlow -> vouchersOperationFlow.getVoucherId())
//                .distinct().collect(Collectors.toList());
//        List<VouchersPerson> voucherDetails = vouchersPersonService.selectVouchersByIds(voucherIds);
        //转换结果
        Map<String, VouchersPerson> detailsMap = voucherDetails.stream().collect(Collectors.toMap(
                VouchersPerson::getVoucherId, Function.identity()));
        Map<String, List<KeyValueRPCDTO>> voucherTypesMap = new HashMap<>();
        voucherDetails.forEach(vouchersPerson -> {
            voucherTypesMap.put(vouchersPerson.getVoucherId(), vouchersTypeService.getVouchersTypeKV(vouchersPerson.getVoucherTypeList()));
        });
        List<VouchersOperationFlowRespRPCDTO> dataRPCDTOList = new ArrayList<>();
        dataList.forEach(data -> {
            VouchersOperationFlowRespRPCDTO dto = new VouchersOperationFlowRespRPCDTO();
            BeanUtils.copyProperties(data, dto);
            VouchersPerson voucher = detailsMap.get(data.getVoucherId());
            if (voucher != null) {
                Integer type = data.getType();
                if (type == VoucherFlowType.CONSUMPTION.getValue() || type == VoucherFlowType.REFUND.getValue()) {
                    dto.setBizNo(data.getFbOrderId());
                } else {
                    String vouchersTaskId = voucher.getVouchersTaskId();
                    dto.setBizNo(vouchersTaskId == null ? null : vouchersTaskId.replaceAll(VOUCHER_TRANSFER_FLAG, ""));
                }
                dto.setVoucherTypeList(voucherTypesMap.get(data.getVoucherId()));
                dto.setVoucherExpiryTime(voucher.getVoucherExpiryTime());
                dto.setVoucherEffectiveTime(voucher.getVoucherEffectiveTime());
                dto.setExtContent(voucher.getExtContent());
            }
            VouchersPerson originalVoucher = detailsMap.get(data.getOriginalVoucherId());
            if (originalVoucher != null) {
                dto.setOriginalVoucherId(originalVoucher.getVoucherId());
                dto.setOriginalVoucherName(originalVoucher.getVoucherName());
                dto.setOriginalVoucherEmployeeId(originalVoucher.getEmployeeId());
                dto.setOriginalVoucherEmployeeName(originalVoucher.getEmployeeName());
                dto.setOriginalVoucherEmployeeDepartmentId(originalVoucher.getEmployeeDepartmentId());
                dto.setOriginalVoucherEmployeeDepartment(originalVoucher.getEmployeeDepartment());
                dto.setOriginalVoucherEmployeeDepartmentFull(originalVoucher.getEmployeeDepartmentFull());
            }
            dataRPCDTOList.add(dto);
        });

        ResponsePage<VouchersOperationFlowRespRPCDTO> responsePage = new ResponsePage<>();
        responsePage.setDataList(dataRPCDTOList);
        responsePage.setTotalCount(totalCount);
        return responsePage;
    }

    @Override
    public VouchersFlow4ExportRespRPCDTO queryVoucherFlow4Export(VouchersFlowReqRPCDTO vouchersFlowReqRPCDTO) throws FinhubException {
        FinhubLogger.info("stereo分贝券流水导出参数：{}", JSONObject.toJSONString(vouchersFlowReqRPCDTO));
        ResponsePage<VouchersOperationFlow> vouchersOperationFlows = vouchersOperationFlowService.selectVouchersFlowByPage(vouchersFlowReqRPCDTO);
        Integer totalCount = vouchersOperationFlows.getTotalCount();
        List<VouchersOperationFlow> vouchersOperationFlowsDataList = vouchersOperationFlows.getDataList();
        VouchersFlow4ExportRespRPCDTO respData = new VouchersFlow4ExportRespRPCDTO();
        if (totalCount <= 0 || CollectionUtils.isEmpty(vouchersOperationFlowsDataList)) {
            return respData;
        }
        //根据劵VouchersTaskDetails查询voucherIds
        List<String> voucherIds = vouchersOperationFlowsDataList.stream().map(vouchersOperationFlow ->
                vouchersOperationFlow.getVoucherId()).distinct().collect(Collectors.toList());
        List<VouchersPerson> voucherDetailList = vouchersPersonService.selectVouchersByIds(voucherIds);
        Map<String, VouchersPerson> detailsMap = new HashMap<>();
        Map<String, List<KeyValueRPCDTO>> voucherTypesMap = new HashMap<>();
        voucherDetailList.forEach(vouchersPerson -> {
            detailsMap.put(vouchersPerson.getVoucherId(), vouchersPerson);
            voucherTypesMap.put(vouchersPerson.getVoucherId(), vouchersTypeService.getVouchersTypeKV(vouchersPerson.getVoucherTypeList()));
        });
        //根据劵VouchersTask 查询vouchersTaskIds
        List<String> vouchersTaskIds = voucherDetailList.stream().map(vouchersTaskDetails -> vouchersTaskDetails.getVouchersTaskId() == null ? null : vouchersTaskDetails.getVouchersTaskId().replaceAll(VOUCHER_TRANSFER_FLAG, ""))
                .distinct().collect(Collectors.toList());
        List<VouchersTask> vouchersByTaskList = vouchersTaskHandleService.findVouchersByTaskIds(vouchersTaskIds);
        Map<String, VouchersTask> vouchersTaskMap = vouchersByTaskList.stream().collect(Collectors.toMap(
                VouchersTask::getVouchersTaskId, Function.identity()));

        /*
          所有流水
         */
        List<VouchersOperationExportRespRPCDTO> allDataList = new ArrayList<>();
        /*
          分贝券消费流水
         */
        List<VouchersOperationExportRespRPCDTO> consumeDataList = new ArrayList<>();
        /*
          分贝券发放流水
         */
        List<VouchersOperationExportRespRPCDTO> grantDataList = new ArrayList<>();
        /*
          分贝券撤回流水
         */
        List<VouchersOperationExportRespRPCDTO> withdrawalDataList = new ArrayList<>();
        /*
          分贝券转让记录
         */
        List<VouchersOperationExportRespRPCDTO> transferDataList = new ArrayList<>();
        for (VouchersOperationFlow data : vouchersOperationFlowsDataList) {
            VouchersPerson voucherDetails = detailsMap.get(data.getVoucherId());
            VouchersOperationExportRespRPCDTO respRPCDTO = new VouchersOperationExportRespRPCDTO();
            BeanUtils.copyProperties(data, respRPCDTO);
            Integer type = data.getType();
            if (null != voucherDetails) {
                if (type == VoucherFlowType.CONSUMPTION.getValue() || type == VoucherFlowType.REFUND.getValue()) {
                    respRPCDTO.setBizNo(data.getFbOrderId());
                }
                if (type == VoucherFlowType.GRANT.getValue() || type == VoucherFlowType.WITHDRAWAL.getValue() || type == VoucherFlowType.WITHDRAWAL_INVOICED.getValue()) {
                    respRPCDTO.setBizNo(voucherDetails.getVouchersTaskId());
                }
                respRPCDTO.setVoucherDenomination(voucherDetails.getVoucherDenomination());
                respRPCDTO.setVoucherExpiryTime(voucherDetails.getVoucherExpiryTime());
                respRPCDTO.setVoucherEffectiveTime(voucherDetails.getVoucherEffectiveTime());
                respRPCDTO.setVoucherTypeList(voucherTypesMap.get(data.getVoucherId()));
                VouchersTask vouchersTask = vouchersTaskMap.get(voucherDetails.getVouchersTaskId());
                if (null != vouchersTask) {
                    respRPCDTO.setTaskDesc(vouchersTask.getTaskDesc());
                }
                VouchersPerson originalVoucher = detailsMap.get(data.getOriginalVoucherId());
                if (originalVoucher != null) {
                    respRPCDTO.setOriginalVoucherId(originalVoucher.getVoucherId());
                    respRPCDTO.setOriginalVoucherName(originalVoucher.getVoucherName());
                    respRPCDTO.setOriginalVoucherEmployeeId(originalVoucher.getEmployeeId());
                    respRPCDTO.setOriginalVoucherEmployeeName(originalVoucher.getEmployeeName());
                    respRPCDTO.setOriginalVoucherEmployeeDepartmentId(originalVoucher.getEmployeeDepartmentId());
                    respRPCDTO.setOriginalVoucherEmployeeDepartment(originalVoucher.getEmployeeDepartment());
                }
            }
            allDataList.add(respRPCDTO);
            VouchersOperationExportRespRPCDTO dto = new VouchersOperationExportRespRPCDTO();
            BeanUtils.copyProperties(respRPCDTO, dto);
            if (VoucherFlowType.isConsumptionOrRefund(type)) {
                consumeDataList.add(dto);
            }
            if (VoucherFlowType.isGrant(type)) {
                grantDataList.add(dto);
            }
            if (VoucherFlowType.isWithdrawal(type) || VoucherFlowType.isRecovery(type)) {
                withdrawalDataList.add(dto);
            }
            if (VoucherFlowType.isTransferInOrOut(type)) {
                transferDataList.add(dto);
            }
        }
        //转让信息
        buildTransferData(transferDataList, respData);

        respData.setTotalCount(totalCount);
        respData.setAllDataList(allDataList);
        respData.setConsumeDataList(consumeDataList);
        respData.setGrantDataList(grantDataList);
        respData.setWithdrawalDataList(withdrawalDataList);
        return respData;
    }

    @Override
    public BigDecimal queryAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(String companyId, Integer accountSubType, Integer accountModelType) {
        if (ObjUtils.isBlank(companyId)) {
            return BigDecimal.ZERO;
        }
        return vouchersPersonService.selectAvailableVoucherAmountByCompanyIdAndAccountSubTypeModel(companyId, accountSubType, accountModelType);
    }

    @Override
    public List<VouchersOperationFlowRespRPCDTO> queryVoucherFlowList(List<String> vouchersFlowIds) throws FinhubException {
        List<VouchersOperationFlow> flowList = vouchersOperationFlowService.selectVouchersFlowByIds(vouchersFlowIds);
        List<VouchersOperationFlowRespRPCDTO> dataList = new ArrayList<>();
        if (ObjUtils.isEmpty(flowList)) {
            return dataList;
        }
        //根据劵id查询VouchersTaskDetails
        Set<String> voucherIdSet = new HashSet<>();
        flowList.forEach(vouchersOperationFlow -> {
            voucherIdSet.add(vouchersOperationFlow.getVoucherId());
            voucherIdSet.add(vouchersOperationFlow.getOriginalVoucherId());
            voucherIdSet.add(vouchersOperationFlow.getParentVoucherId());
        });
        List<VouchersPerson> voucherDetails = vouchersPersonService.selectVouchersByIds(new ArrayList<>(voucherIdSet));
        //转换结果
        Map<String, VouchersPerson> detailsMap = voucherDetails.stream().collect(Collectors.toMap(
                VouchersPerson::getVoucherId, Function.identity()));
        flowList.forEach(vouchersFlow -> {
            VouchersOperationFlowRespRPCDTO flowRespRPCDTO = new VouchersOperationExportRespRPCDTO();
            BeanUtils.copyProperties(vouchersFlow, flowRespRPCDTO);
            VouchersPerson voucher = detailsMap.get(vouchersFlow.getVoucherId());
            if (voucher != null) {
                flowRespRPCDTO.setVoucherExpiryTime(voucher.getVoucherExpiryTime());
                flowRespRPCDTO.setVoucherEffectiveTime(voucher.getVoucherEffectiveTime());
                flowRespRPCDTO.setExtContent(voucher.getExtContent());
            }
            VouchersPerson originalVoucher = detailsMap.get(vouchersFlow.getOriginalVoucherId());
            if (originalVoucher != null) {
                flowRespRPCDTO.setOriginalVoucherName(originalVoucher.getVoucherName());
                flowRespRPCDTO.setOriginalVoucherEmployeeId(originalVoucher.getEmployeeId());
                flowRespRPCDTO.setOriginalVoucherEmployeeName(originalVoucher.getEmployeeName());
                flowRespRPCDTO.setOriginalVoucherEmployeeDepartmentId(originalVoucher.getEmployeeDepartmentId());
                flowRespRPCDTO.setOriginalVoucherEmployeeDepartment(originalVoucher.getEmployeeDepartment());
                flowRespRPCDTO.setOriginalVoucherEmployeeDepartmentFull(originalVoucher.getEmployeeDepartmentFull());
                flowRespRPCDTO.setVouchersTaskDetailsId(originalVoucher.getId());
            }
            VouchersPerson parentVoucher = detailsMap.get(vouchersFlow.getOriginalVoucherId());
            if (parentVoucher != null) {
                flowRespRPCDTO.setParentVoucherName(parentVoucher.getVoucherName());
                flowRespRPCDTO.setParentVoucherEmployeeId(parentVoucher.getEmployeeId());
                flowRespRPCDTO.setParentVoucherEmployeeName(parentVoucher.getEmployeeName());
                flowRespRPCDTO.setParentVoucherEmployeeDepartmentId(parentVoucher.getEmployeeDepartmentId());
                flowRespRPCDTO.setParentVoucherEmployeeDepartment(parentVoucher.getEmployeeDepartment());
                flowRespRPCDTO.setParentVoucherEmployeeDepartmentFull(parentVoucher.getEmployeeDepartmentFull());
            }
            if (ObjUtils.isNotBlank(vouchersFlow.getBankName()) && BankNameEnum.isFbt(vouchersFlow.getBankName()) && ObjUtils.isBlank(flowRespRPCDTO.getBankAccountNo())) {
                flowRespRPCDTO.setBankAccountNo(vouchersFlow.getCompanyId());
            }
            dataList.add(flowRespRPCDTO);
        });
        return dataList;
    }

    @Override
    public List<VouchersOperationFlowRespRPCDTO> queryVoucherConsumeFlowByFbOrderIds(List<String> fbOrderIds) {
        List<VouchersOperationFlow> operationFlows = vouchersOperationFlowService.queryVouchersFlowByOrderIds(fbOrderIds, VoucherFlowType.CONSUMPTION.getValue());
        if (ObjUtils.isEmpty(operationFlows)) {
            return new ArrayList<>();
        }
        List<VouchersOperationFlowRespRPCDTO> respRPCDTOS = new ArrayList<>();
        operationFlows.forEach(flow -> {
            VouchersOperationFlowRespRPCDTO dto = new VouchersOperationExportRespRPCDTO();
            BeanUtils.copyProperties(flow, dto);
            respRPCDTOS.add(dto);
        });
        return respRPCDTOS;
    }

    private void buildTransferData(List<VouchersOperationExportRespRPCDTO> transferDataList, VouchersFlow4ExportRespRPCDTO respData) {
        if (CollectionUtils.isEmpty(transferDataList)) {
            return;
        }
        //兼容老版本去掉bizNo为空
        List<VouchersOperationExportRespRPCDTO> vouchersOperationFlowList = transferDataList.stream().filter(v ->
                ObjUtils.isNotBlank(v.getBizNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vouchersOperationFlowList)) {
            return;
        }
        Map<String, List<VouchersOperationExportRespRPCDTO>> vouchersOperationFlowMap = vouchersOperationFlowList.stream()
                .collect(Collectors.groupingBy(VouchersOperationExportRespRPCDTO::getBizNo));

        //根据劵VouchersTaskDetails查询voucherIds
        List<String> bizNos = new ArrayList<>(vouchersOperationFlowMap.keySet());
        List<PersonTransferFlow> personTransferFlowList = personTransferService.findPersonTransferByIds(bizNos);

        List<PersonTransferStereoExportRPCDTO> transferStereoExportRPCDTOS = personTransferFlowList.stream().map(personTransferFlow -> {
            List<VouchersOperationExportRespRPCDTO> operationFlows = vouchersOperationFlowMap.get(personTransferFlow
                    .getId());
            return buildTransferStereoExportRPCDTO(personTransferFlow, operationFlows);
        }).collect(Collectors.toList());
        respData.setTransferDataList(transferStereoExportRPCDTOS);
    }


    private PersonTransferStereoExportRPCDTO buildTransferStereoExportRPCDTO(PersonTransferFlow personTransferFlow, List<VouchersOperationExportRespRPCDTO> vouchersOperationFlows) {
        PersonTransferStereoExportRPCDTO respRPCDTO = new PersonTransferStereoExportRPCDTO();
        for (VouchersOperationExportRespRPCDTO operationFlow : vouchersOperationFlows) {
            if (operationFlow.getType().equals(VoucherFlowType.TRANSFER_OUT.getValue())) {
                respRPCDTO.setOperationFlowSourceId(operationFlow.getId());
                respRPCDTO.setSourceEmployeeDepartment(operationFlow.getEmployeeDepartment());
                respRPCDTO.setSourceEmployeePhone(operationFlow.getEmployeePhone());
                respRPCDTO.setSourceVoucherName(operationFlow.getVoucherName());
            }
            if (operationFlow.getType().equals(VoucherFlowType.TRANSFER_IN.getValue())) {
                respRPCDTO.setOperationFlowTargetId(operationFlow.getId());
                respRPCDTO.setTargetEmployeeDepartment(operationFlow.getEmployeeDepartment());
                respRPCDTO.setTargetEmployeePhone(operationFlow.getEmployeePhone());
                respRPCDTO.setTargetVoucherName(operationFlow.getVoucherName());
            }
            respRPCDTO.setDeductionAccountType(operationFlow.getDeductionAccountType());
        }
        respRPCDTO.setCreateTime(personTransferFlow.getCreateTime());
        respRPCDTO.setAmount(personTransferFlow.getTransferAmount());
        respRPCDTO.setSourceVoucherId(personTransferFlow.getSourceId());
        respRPCDTO.setSourceName(personTransferFlow.getFromEmployeeName());
        respRPCDTO.setTargetVoucherId(personTransferFlow.getTargetId());
        respRPCDTO.setTargetName(personTransferFlow.getToEmployeeName());
        return respRPCDTO;
    }
}
