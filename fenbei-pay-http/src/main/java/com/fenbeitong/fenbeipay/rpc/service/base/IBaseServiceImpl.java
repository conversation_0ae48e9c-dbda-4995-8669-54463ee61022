
package com.fenbeitong.fenbeipay.rpc.service.base;


import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyCardService;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankCardStatus;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.CardModelType;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.PettyType;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.RoundPettyStatus;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankRefundTradeReqDTO;
import com.fenbeitong.fenbeipay.api.service.gateway.IAcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.bank.base.dto.RefundOrderTypeDto;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.BankPettyManager;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.fenbeipay.dto.bank.BankCardTradeFlow;
import com.fenbeitong.fenbeipay.dto.bank.BankPetty;
import com.fenbeitong.saasplus.api.model.enums.vitualCard.CheckStatus;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeSimpleInfoContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.google.common.base.Objects;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Map;

public class IBaseServiceImpl {

    @Autowired
    public IBaseEmployeeExtService iBaseEmployeeExtService;
    @Autowired
    protected IPrivilegeService iPrivilegeService;
    @Autowired
    protected IAcctCompanyGatewayService iAcctCompanyGatewayService;
    @Autowired
    protected DingDingMsgService dingDingMsgService;
    @Autowired
    protected UAcctCompanyCardService uAcctCompanyCardService;
    @Autowired
    private BankPettyManager bankPettyManager;


    /**
      * @Description: 验证企业是否可以用分贝通虚拟卡
      * @Param: [companyId]
      * @return: java.lang.Boolean
      */
    public Boolean checkCompanyPackage(String companyId){
        Boolean isResult=false;
        try {
            String code = "m_bank_individual";
            Map<String, Boolean> detailResult = iPrivilegeService.queryFunctionMoudle(companyId,code);
            if(ObjUtils.isEmpty(detailResult)){
                //为空，没有套餐信息
                throw new FinPayException(GlobalResponseCode.BANK_CARD_REFUND_COMPANY_PACKAGE_NULL);
            }
            if (!detailResult.get(code)) {
                //基础版本
                isResult= true;
            }
            return isResult;
        } catch (Exception e) {
            dingDingMsgService.sendMsg("【分贝通虚拟卡，退款查询套餐异常,】公司Id：" + companyId);
        }
        return isResult;
    }


    /**
      * @Description: 不同企业true 相同false
      * @Param: [reqDTO, cardTradeFlow]
      * @return: java.lang.Boolean
      */
    public Boolean checkNotSameCompany(String employeeId, String companyId) {
        EmployeeSimpleInfoContract employeeSimpleInfo = iBaseEmployeeExtService.getEmployeeInfoByEmloyeeId(employeeId);
        if (ObjUtils.isNotEmpty(employeeSimpleInfo)) {
            if (ObjUtils.isBlank(employeeSimpleInfo.getCompany_id())) {
                return true;
            } else if (!Objects.equal(employeeSimpleInfo.getCompany_id(), companyId)) {
                return true;
            }
        }
        return false;
    }

    /**
      * @Description: 不同的账户id就返回ture
      * @Param: [reqDTO]
      * @return: java.lang.Boolean
      */
    public Boolean checkIsCutAcctModel(BankRefundTradeReqDTO reqDTO){
        AcctCommonBaseDTO acctCommonBaseDTO = uAcctCompanyCardService.getAcctCompanyCard(reqDTO.getCompanyId(),reqDTO.getBankName());
        if (ObjUtils.isEmpty(acctCommonBaseDTO)) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_AUTH_ACCOUNT_ERROR);
        }
        if (!Objects.equal(acctCommonBaseDTO.getAccountId(), reqDTO.getAccountSubId())) {
            return true;
        }
        return false;
    }

    /**
     * @Description: 查询退款是否需要退还保证里面只有一个为ture
     * @Param: [reqDTO, bankCard, cardTradeFlow]
     * @return: com.fenbeitong.fenbeipay.bank.hupo.base.dto.RefundOrderTypeDto
     */
    public RefundOrderTypeDto queryRefundType(BankRefundTradeReqDTO reqDTO, BankCard bankCard, BankCardTradeFlow cardTradeFlow) {
        RefundOrderTypeDto typeDto = RefundOrderTypeDto.of();
        //1步查询企业不同，不同企业true 相同false
        typeDto.setDitCompany(checkNotSameCompany(reqDTO.getEmployeeId(), cardTradeFlow.getCompanyId()));
        //2步如果企业相同：才需要验证解绑状态
        if (!typeDto.getDitCompany()) {
            typeDto.setUnbindBankCard(BankCardStatus.isUnbind(bankCard.getCardStatus()));
        }
        //3步只有企业相同||解绑状态：才查询用分贝通虚拟卡开关
        if (!(typeDto.getDitCompany() || typeDto.getUnbindBankCard())) {
            typeDto.setOffBankCard(checkCompanyPackage(reqDTO.getCompanyId()));
        }
         //4步如果消费的账户
        if (!(typeDto.getDitCompany() || typeDto.getUnbindBankCard())|| !typeDto.getOffBankCard()) {
            typeDto.setIsCutAcctModel(checkIsCutAcctModel(reqDTO));
        }

        //单独判断：退还备用金和前3种权限其中有可能并存：备用金转了普通模式
        if (ObjUtils.isNotBlank(reqDTO.getPettyId()) && (ObjUtils.isEmpty(bankCard.getPettyId()) ||!Objects.equal(reqDTO.getPettyId(),bankCard.getPettyId()))){
            typeDto.setRetPetty(true);
        }
        //上一笔备用金已经过期限，备用金已经过期,则需要退还企业
        if (ObjUtils.isNotBlank(reqDTO.getPettyId())){
            BankPetty bankPetty = bankPettyManager.queryBankPettyPettyId(reqDTO.getPettyId());
            Date date = new Date();
            if (bankPetty.getExpireDate() != null && date.after(bankPetty.getExpireDate())){
                typeDto.setRetPetty(true);
            }
        }

        //单独判断：退还备用金和前3种权限其中有可能并存:普通砖备用金退企业
        if (ObjUtils.isBlank(reqDTO.getPettyId()) && (ObjUtils.isNotEmpty(bankCard.getPettyId())||CardModelType.isPetty(bankCard.getCardModel()))){
            typeDto.setRetNormal(true);
        }
        if (typeDto.getIsCutAcctModel() && ObjUtils.isNotEmpty(reqDTO.getPettyId())){
            typeDto.setRetPetty(true);
        }
        if(BankCardStatus.isStopUse(bankCard.getCardStatus())){
            typeDto.setCardStopUse(true);
        }
        //循环备用金，循环状态启用时，正向单已经核销完毕
        if (ObjUtils.isNotBlank(reqDTO.getPettyId())
                && reqDTO.getPettyType() != null
                && reqDTO.getPettyType()== PettyType.ROUND.getKey()
                && reqDTO.getRoundStatus() != null
                && reqDTO.getRoundStatus() != RoundPettyStatus.DISCARD_RETURN_MONEY.getKey()
                && reqDTO.getRoundStatus() != RoundPettyStatus.DISCARD_NO_PETTY.getKey()
                && reqDTO.getCheckStatus() == CheckStatus.SUCCEED.getKey()){
            typeDto.setRoundPettyWriteOff(true);
            typeDto.setRetPetty(true);
        }
        //循环备用金，循环状态已经终止
        if (ObjUtils.isNotBlank(reqDTO.getPettyId())
                && reqDTO.getPettyType() != null
                && reqDTO.getPettyType()== PettyType.ROUND.getKey()
                && reqDTO.getRoundStatus() != null
                && (reqDTO.getRoundStatus() == RoundPettyStatus.DISCARD_RETURN_MONEY.getKey() || reqDTO.getRoundStatus() == RoundPettyStatus.DISCARD_NO_PETTY.getKey())){
            typeDto.setRoundPettyStatus(true);
            typeDto.setRetPetty(true);
        }
        //一次性备用金，直接退企业
        if(reqDTO.getPettyId() != null
                && reqDTO.getPettyType() != null
                && reqDTO.getPettyType() == PettyType.SINGLE.getKey()){
            typeDto.setSinglePetty(true);
            typeDto.setRetPetty(true);
        }
        return typeDto;
    }
}

