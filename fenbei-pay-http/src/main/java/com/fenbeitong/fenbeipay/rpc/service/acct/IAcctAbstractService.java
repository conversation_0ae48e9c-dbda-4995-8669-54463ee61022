package com.fenbeitong.fenbeipay.rpc.service.acct;

import com.fenbeitong.fenbei.settlement.external.api.api.bill.IBillOpenApi;
import com.fenbeitong.fenbeipay.acctdech.conver.CompanyAccountInfoConverter;
import com.fenbeitong.fenbeipay.acctdech.service.*;
import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicDechService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicSearchService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicService;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.*;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.AutoAcctCheckingEventUtil;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.fenbeipay.nf.unit.service.UFundFreezenService;
import com.fenbeitong.fenbeipay.redcoupon.service.AccountRedcouponService;
import com.fenbeitong.fenbeipay.redcoupon.service.UAccountRedEnvelopeService;
import com.fenbeitong.pay.search.service.AccountAllFlowService;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.fenbeitong.pay.search.service.impl.AccountStatisticalServiceImpl;
import com.fenbeitong.saasplus.api.service.finance.IOrderCostService;
import com.fenbeitong.usercenter.api.service.captcha.ICaptchaService;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.fenbeitong.usercenter.api.service.company.IRCompanyService;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

public abstract class IAcctAbstractService {

    @Value("${exception.remind.profile}")
    protected String currentEnvironment;
    @Value("${ding.ding.cashier.token}")
    protected String dingDingRobotToken;
    @Value("${ding.ding.cashier.project.manager}")
    protected String projectManager;

    /**
     * U层充值&授信账户复合Service
     */
    @Autowired
    protected UAcctCommonService uAcctCommonService;

    /**
     * U层充值账户复合Service
     */
    @Autowired
    protected UAcctCommonDebitService uAcctCommonDebitService;
    /**
     * U层授信账户复合Service
     */
    @Autowired
    protected UAcctCommonCreditService  uAcctCommonCreditService;

    @Autowired
    protected AccountStatisticalServiceImpl accountStatisticalService;

    @Autowired
    protected UFundFreezenService uFundFreezenService;

    @Autowired
    protected AccountRedcouponService accountRedcouponService;

    @Autowired
    protected SearchCardManager searchCardManager;

    @Autowired
    protected UAcctGeneralService uAcctGeneralService;

    @Autowired
    protected IVouchersPersonService iVouchersPersonService;

    @Autowired
    protected UAcctBusinessCreditFlowService uAcctBusinessCreditFlowService;

    @Autowired
    protected UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    protected UAcctBusinessDebitFlowService uAcctBusinessDebitFlowService;

    @Autowired
    protected UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    protected UAcctCompanyCardFlowService uAcctCompanyCardFlowService;

    @Autowired
    protected UAcctCompanyCardService uAcctCompanyCardService;

    @Autowired
    protected UAcctIndividualCreditFlowService uAcctIndividualCreditFlowService;

    @Autowired
    protected UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    protected UAcctIndividualDebitFlowService uAcctIndividualDebitFlowService;

    @Autowired
    protected UAcctIndividualDebitService uAcctIndividualDebitService;

    @Autowired
    protected  UAcctCompanyMainService uAcctCompanyMainService;
    @Autowired
    protected  AcctCompanyBindCardService acctCompanyBindCardService;
    @Autowired
    protected UCompanySwitchService uCompanySwitchService;

    @Autowired
    protected UAcctGeneralFlowService uAcctGeneralFlowService;

    @Autowired
    protected AcctPublicSearchService acctPublicSearchService;

    @Autowired
    protected UAccountRedEnvelopeService uAccountRedEnvelopeService;

    @Autowired
    protected AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    protected AcctCompanyGatewayService acctCompanyGatewayService;

    @Autowired
    protected AcctPublicService acctPublicService;

    @Autowired
    protected AcctPublicDechService acctPublicDechService;

    @Autowired
    protected  UAcctSettlementService uAcctSettlementService;


    //UC
    @Autowired
    protected ICompanyService iCompanyService;
    
    @Autowired
    protected IRCompanyService iRCompanyService;

    @Autowired
    protected ICaptchaService iCaptchaService;

    @Autowired
    protected BankAcctService bankAcctService;
    @Autowired
    protected UBankAcctService uBankAcctService;

    @Autowired
    protected RedissonService redissonService;

    @Autowired
    protected AutoAcctCheckingEventUtil autoAcctCheckingEventUtil;

    @Autowired
    protected AccountGeneralService accountGeneralService;

    @Autowired
    protected IBillOpenApi iBillOpenApi;

    @Autowired
    protected UBankAcctFlowService uBankAcctFlowService;

    @Autowired
    protected AcctBusinessCreditRecoverTaskService acctBusinessCreditRecoverTaskService;

    @Autowired
    protected AcctIndividualCreditRecoverTaskService acctIndividualCreditRecoverTaskService;
    @Autowired
    protected UAcctReimbursementService uAcctReimbursementService;

    @Autowired
    protected AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Autowired
    protected AcctBusinessDebitFlowService acctBusinessDebitFlowService;

    @Autowired
    protected AcctCompanyCardFlowService acctCompanyCardFlowService;

    @Autowired
    protected CreditApplyOperationManager creditApplyOperationManager;

    @Autowired
    protected CreditDistributeManager creditDistributeManager;

    @Autowired
    protected IOrderCostService iOrderCostService;

    @Autowired
    protected AccountAllFlowService accountAllFlowService;

    @Autowired
    protected BankCardApplyFlowManger bankCardApplyFlowManger;

    @Autowired
    protected BankPettyManager bankPettyManager;

    @Autowired
    protected IBaseEmployeeExtService iBaseEmployeeExtService;

    @Autowired
    protected CompanyAccountInfoConverter companyAccountInfoConverter;
    
    @Autowired
    protected AcctOverseaService acctOverseaService;
}
