package com.fenbeitong.fenbeipay.rpc.service.voucher;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherSourceType;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTaskType;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskDetailsCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskDetailsRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTasksReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskDetailsRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskDetailsRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskResponseRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.voucher.VouchersInvoiceTaskStatisticsVO;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTaskService;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersOperationService;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersTaskCreateService;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersTaskSearchService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherConstant;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.consumer.KafkaConsumerUtils;
import com.fenbeitong.finhub.kafka.msg.stereo.KafkaGrantVoucherIntoBillMsg;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: java类作用描述
 * @ClassName: IVouchersTaskServiceImpl
 * @Author: zhangga
 * @CreateDate: 2019/5/21 2:51 PM
 * @UpdateUser:
 * @UpdateDate: 2019/5/21 2:51 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("iVouchersTaskService")
public class IVouchersTaskServiceImpl implements IVouchersTaskService {
    @Autowired
    private IVouchersOperationService iVouchersOperationService;
    @Autowired
    private IVouchersTaskCreateService iVouchersTaskCreateService;
    @Autowired
    private IVouchersTaskSearchService iVouchersTaskSearchService;

    @Override
    public VouchersTaskResponseRPCDTO createVouchersGrantTask(VouchersTaskRPCDTO vouchersTaskRPCDTO, Integer voucherTaskType, Integer voucherSourceType) throws FinhubException {
        FinhubLogger.info("【创建分贝券发放任务】，任务类型：{}，券来源：{}，任务详情：{}", voucherTaskType, voucherSourceType, vouchersTaskRPCDTO.toString());
        if (ObjUtils.isEmpty(vouchersTaskRPCDTO) || null == voucherTaskType || null == voucherSourceType) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersTaskCreateService.createVouchersGrantTask(vouchersTaskRPCDTO, voucherTaskType, voucherSourceType);
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务类型：{}，券来源：{}，任务详情：{}", voucherTaskType, voucherSourceType, vouchersTaskRPCDTO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务类型：{}，券来源：{}，任务详情：{}", voucherTaskType, voucherSourceType, vouchersTaskRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务类型：{}，券来源：{}，任务详情：{}", voucherTaskType, voucherSourceType, vouchersTaskRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    @Override
    public VouchersTaskDetailsRespRPCDTO addVouchersGrantTaskDetails(List<VouchersTaskDetailsRPCDTO> vouchersTaskDetailsRPCDTOList, String voucherTaskId, String companyId, Boolean isStart, Integer voucherSourceType) throws FinhubException {
        if (ObjUtils.isEmpty(vouchersTaskDetailsRPCDTOList) || ObjUtils.isBlank(voucherTaskId) || ObjUtils.isBlank(companyId) || null == isStart || null == voucherSourceType) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersTaskCreateService.addVouchersGrantTaskDetails(vouchersTaskDetailsRPCDTOList, voucherTaskId, companyId, isStart, voucherSourceType);
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTOList.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTOList.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTOList.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    @Override
    public boolean startTask(String voucherTaskId, String companyId) {
        try {
            return iVouchersTaskCreateService.startTask(voucherTaskId, companyId);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝券任务启动】异常，任务Id为：{}", voucherTaskId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝券任务启动】异常，任务Id为：{}", voucherTaskId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券任务启动】异常，任务Id为：{}", voucherTaskId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean cancelTask(String voucherTaskId, String companyId) {
        try {
            return iVouchersTaskCreateService.cancelTask(voucherTaskId, companyId);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝券任务取消】异常，任务Id为：{}", voucherTaskId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝券任务取消】异常，任务Id为：{}", voucherTaskId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券任务取消】异常，任务Id为：{}", voucherTaskId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public VouchersTaskResponseRPCDTO createBountyGrantTask(VouchersTaskRPCDTO vouchersTaskRPCDTO) throws FinhubException {
        int voucherTaskType = VoucherTaskType.BOUNTY_EXCHANGE.getValue();
        int voucherSourceType = VoucherSourceType.COMPANY_BOUNTY.getValue();
        FinhubLogger.info("【创建分贝券发放任务】，任务类型：{}，券来源：{}，任务详情：{}", voucherTaskType, voucherSourceType, vouchersTaskRPCDTO.toString());
        if (ObjUtils.isEmpty(vouchersTaskRPCDTO)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersTaskCreateService.createVouchersGrantTask(vouchersTaskRPCDTO, voucherTaskType, voucherSourceType);
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务详情：{}", vouchersTaskRPCDTO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务详情：{}", vouchersTaskRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务详情：{}", vouchersTaskRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public VouchersTaskResponseRPCDTO addBountyGrantTaskDetails(VouchersTaskDetailsRPCDTO vouchersTaskDetailsRPCDTO, String voucherTaskId, String companyId, Boolean isStart) throws FinhubException {
        FinhubLogger.info("【创建分贝券发放任务】，任务详情：{}", JSONObject.toJSONString(vouchersTaskDetailsRPCDTO));
        if (ObjUtils.isNull(vouchersTaskDetailsRPCDTO)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersTaskCreateService.addVouchersGrantTaskDetails(vouchersTaskDetailsRPCDTO, voucherTaskId, companyId, isStart, VoucherSourceType.COMPANY_BOUNTY.getValue());
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务详情：{}", JSONObject.toJSONString(vouchersTaskDetailsRPCDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务详情：{}", JSONObject.toJSONString(vouchersTaskDetailsRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务详情：{}", JSONObject.toJSONString(vouchersTaskDetailsRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public VouchersTaskDetailsRespRPCDTO addBountyGrantTaskDetails(List<VouchersTaskDetailsRPCDTO> vouchersTaskDetailsRPCDTOList, String voucherTaskId, String companyId, Boolean isStart) throws FinhubException {
        if (ObjUtils.isEmpty(vouchersTaskDetailsRPCDTOList) || ObjUtils.isBlank(voucherTaskId) || ObjUtils.isBlank(companyId) || null == isStart) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        int voucherSourceType = VoucherSourceType.COMPANY_BOUNTY.getValue();
        try {
            return iVouchersTaskCreateService.addVouchersGrantTaskDetails(vouchersTaskDetailsRPCDTOList, voucherTaskId, companyId, isStart, VoucherSourceType.COMPANY_BOUNTY.getValue());
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTOList.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTOList.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, vouchersTaskDetailsRPCDTOList.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public VouchersTaskResponseRPCDTO createGrantTaskByVouchersTemplet(VouchersTaskCreateRPCDTO taskCreateRPCDTO) throws FinhubException {
        FinhubLogger.info("【创建分贝券发放任务】，任务详情：{}", JsonUtils.toJson(taskCreateRPCDTO));
        try {
            return iVouchersTaskCreateService.createGrantTaskByVouchersTemplet(taskCreateRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务参数：{}", taskCreateRPCDTO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务参数：{}", taskCreateRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝券发放任务】异常，任务参数：{}", taskCreateRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    @Override
    public VouchersTaskDetailsRespRPCDTO addGrantTaskDetailsByVouchersTemplet(List<VouchersTaskDetailsCreateRPCDTO> taskDetailsCreateRPCDTOS, String voucherTaskId, String companyId, Boolean isStart, Integer voucherSourceType) throws FinhubException {
        FinhubLogger.info("【创建分贝券发放任务】增加详情，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, JSON.toJSONString(taskDetailsCreateRPCDTOS));
        if (ObjUtils.isEmpty(taskDetailsCreateRPCDTOS) || ObjUtils.isBlank(voucherTaskId) || ObjUtils.isBlank(companyId) || null == isStart || null == voucherSourceType) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersTaskCreateService.addGrantTaskDetailsByVouchersTemplet(taskDetailsCreateRPCDTOS, voucherTaskId, companyId, isStart, voucherSourceType);
        } catch (FinPayException e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, taskDetailsCreateRPCDTOS.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, taskDetailsCreateRPCDTOS.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【创建分贝券发放任务】增加详情异常，任务id：{}，券来源：{}，任务详情：{}", voucherTaskId, voucherSourceType, taskDetailsCreateRPCDTOS.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean updateVouchersTaskInvoiceStatus(String companyId, List<String> voucherTaskIdList, Integer writeInvoiceStatus, String operationUserId, String operationUserName) throws FinhubException {
        FinhubLogger.info("【更新分贝券发放任务开票状态】，公司ID：{}，发券任务ID：{}，状态：{}，操作人ID：{}，操作人：{}", companyId, voucherTaskIdList.toString(), writeInvoiceStatus, operationUserId, operationUserName);
        if (ObjUtils.isBlank(companyId) || ObjUtils.isEmpty(voucherTaskIdList) || writeInvoiceStatus == null) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersOperationService.updateVouchersTaskInvoiceStatus(companyId, voucherTaskIdList, writeInvoiceStatus, operationUserId, operationUserName);
        } catch (FinPayException e) {
            FinhubLogger.error("【更新分贝券发放任务开票状态】异常，公司ID：{}，发券任务ID：{}，状态：{}", companyId, voucherTaskIdList.toString(), writeInvoiceStatus);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【更新分贝券发放任务开票状态】异常，公司ID：{}，发券任务ID：{}，状态：{}", companyId, voucherTaskIdList.toString(), writeInvoiceStatus);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【更新分贝券发放任务开票状态】异常，公司ID：{}，发券任务ID：{}，状态：{}", companyId, voucherTaskIdList.toString(), writeInvoiceStatus);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean updateVouchersTaskBillStatus(List<String> voucherTaskIdList, Integer taskBillStatus, String operationUserId, String operationUserName) throws FinhubException {
        FinhubLogger.info("【更新分贝券发放任务入账状态】，发券任务ID：{}，状态：{}，操作人ID：{}，操作人：{}", voucherTaskIdList.toString(), taskBillStatus, operationUserId, operationUserName);
        if (ObjUtils.isEmpty(voucherTaskIdList) || taskBillStatus == null) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            return iVouchersOperationService.updateVouchersTaskBillStatus(voucherTaskIdList, taskBillStatus, operationUserId, operationUserName);
        } catch (FinPayException e) {
            FinhubLogger.error("【更新分贝券发放任务入账状态】异常，发券任务ID：{}，状态：{}", voucherTaskIdList.toString(), taskBillStatus);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【更新分贝券发放任务入账状态】异常，发券任务ID：{}，状态：{}", voucherTaskIdList.toString(), taskBillStatus);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【更新分贝券发放任务入账状态】异常，发券任务ID：{}，状态：{}", voucherTaskIdList.toString(), taskBillStatus);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<VouchersTaskRespDTO> queryVouchersTask(String companyId, List<String> voucherTaskIdList, Integer pageNo, Integer pageSize) throws FinhubException {
        try {
            if (ObjUtils.isBlank(companyId) || ObjUtils.isEmpty(voucherTaskIdList)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            return iVouchersTaskSearchService.queryVouchersTask(companyId, voucherTaskIdList, pageNo, pageSize);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public List<VouchersTaskRespDTO> queryVouchersTask(VouchersTasksReqRPCDTO tasksReqRPCDTO) throws FinhubException {
        try {
            if (ObjUtils.isNull(tasksReqRPCDTO)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            return iVouchersTaskSearchService.queryVouchersTask(tasksReqRPCDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{}，状态：{}", JSONObject.toJSONString(tasksReqRPCDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{}，状态：{}", JSONObject.toJSONString(tasksReqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{}，状态：{}", JSONObject.toJSONString(tasksReqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public ResponsePage<VouchersTaskDetailsRespDTO> queryVouchersTaskDetails(String companyId, List<String> voucherTaskIdList, Integer pageNo, Integer pageSize) throws FinhubException {
        try {
            if (ObjUtils.isBlank(companyId) || ObjUtils.isEmpty(voucherTaskIdList)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            return iVouchersTaskSearchService.queryVouchersTaskDetails(companyId, voucherTaskIdList, pageNo, pageSize);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public ResponsePage<VouchersTaskDetailsRespDTO> queryVouchersGrantTaskDetails(String companyId, List<String> voucherTaskIdList, Integer pageNo, Integer pageSize) throws FinhubException {
        try {
            if (ObjUtils.isBlank(companyId) || ObjUtils.isEmpty(voucherTaskIdList)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            return iVouchersTaskSearchService.queryVouchersGrantTaskDetails(companyId, voucherTaskIdList, pageNo, pageSize);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},{},{}", companyId, JSONObject.toJSONString(voucherTaskIdList), pageNo, pageSize, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public VouchersInvoiceTaskStatisticsVO queryVouchersInvoiceTasksStatistics(String companyId, List<String> voucherTaskIdList) throws FinhubException {
        try {
            return iVouchersTaskSearchService.queryVouchersInvoiceTasksStatistics(companyId, voucherTaskIdList);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},", companyId, JSONObject.toJSONString(voucherTaskIdList), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},", companyId, JSONObject.toJSONString(voucherTaskIdList), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券RPC查询】异常，参数：{},{},", companyId, JSONObject.toJSONString(voucherTaskIdList), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void recoveryExpiryRedCouponVouchersTask(String companyId,String grantRecordId) {
        try {
            iVouchersOperationService.recoveryExpiryRedCouponVouchersTask(companyId,grantRecordId);
        } catch (FinPayException e) {
            FinhubLogger.error("【分贝券RPC回收】异常，参数：{}", companyId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【分贝券RPC回收】异常，参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【分贝券RPC回收】异常，参数：{}", companyId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * @Description: 消息处理账单状态变更
     * @Param: [record]
     */
    @KafkaListener(topics = {"stereo_grant_voucher_info_bill"})
    public void voucherTaskBillStatusUpdateListener(ConsumerRecord<?, ?> record) {
        FinhubLogger.info("【kafka消息 来自账单的分贝券任务出账状态变更：{}】,分区：{}消息内容如下：{}", record.topic(), record.partition(), record.value());
        KafkaGrantVoucherIntoBillMsg iMessage = KafkaConsumerUtils.invokeIMessage(record.value().toString(), KafkaGrantVoucherIntoBillMsg.class);

        if (ObjUtils.isEmpty(iMessage) || ObjUtils.isEmpty(iMessage.getVoucherTaskId()) || ObjUtils.isNull(iMessage.getBeforeToBillStatus()) || ObjUtils.isNull(iMessage.getAfterToBillStatus())) {
            FinhubLogger.error("【kafka消费 来自账单的分贝券任务出账状态变更：{}】参数异常 data:{}", record.topic(), JSONObject.toJSONString(iMessage));
            return;
        }
        updateVouchersTaskBillStatus(Lists.newArrayList(iMessage.getVoucherTaskId()), iMessage.getAfterToBillStatus(), VoucherConstant.SYSTEM_OPERATION_STEREO, VoucherConstant.SYSTEM_OPERATION_STEREO);
        FinhubLogger.info("【kafka消费 来自账单的分贝券任务出账状态变更：{}】消费完成 data:{}", record.topic(), JSONObject.toJSONString(iMessage));
    }
}
