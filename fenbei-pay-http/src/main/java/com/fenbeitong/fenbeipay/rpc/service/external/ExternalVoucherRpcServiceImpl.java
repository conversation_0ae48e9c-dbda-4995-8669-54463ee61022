package com.fenbeitong.fenbeipay.rpc.service.external;

import cn.hutool.core.util.ObjectUtil;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.ResponseCodeEnum;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersBusinessFlowRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersIssuerRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskDetailsCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTasksReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTempletRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.req.VouchersExternalRpcReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.req.VouchersTaskGrantRpcReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.*;
import com.fenbeitong.fenbeipay.api.service.external.IExternalVoucherRpcService;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersTaskCreateService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.voucher.VoucherTaskStatus;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersPerson;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherCommonUtils;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherConstant;
import com.fenbeitong.fenbeipay.vouchers.dto.OperationUserInfoDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersFlowRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTasksRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersOperationFlowService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskService;
import com.fenbeitong.fenbeipay.vouchers.vo.VoucherForStatisticsVO;
import com.fenbeitong.fenbeipay.vouchers.vo.VouchersFlowVO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对接发放分贝券使用类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/1/17
 */
@Service("iExternalVoucherRpcService")
public class ExternalVoucherRpcServiceImpl implements IExternalVoucherRpcService {

    @Autowired
    private IVouchersTaskCreateService iVouchersTaskCreateService;
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private VouchersOperationFlowService vouchersOperationFlowService;
    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    private VouchersTaskService vouchersTaskService;

    @Override
    public VouchersTaskResponseRPCDTO createGrantTask(VouchersTaskCreateRPCDTO taskCreateRPCDTO) {
        FinhubLogger.info("【对接分贝券创建任务】请求参数={}",taskCreateRPCDTO);
        return iVouchersTaskCreateService.createGrantTaskByVouchersTemplet(taskCreateRPCDTO);
    }


    @Override
    public VouchersExternalDetailsRpcResDTO addGrantTaskDetails(VouchersExternalRpcReqDTO taskRpcReqDTO) {
        ValidateUtils.validate(taskRpcReqDTO);
        FinhubLogger.info("【对接分贝券追加明细】任务名称参数={},任务条数={}",taskRpcReqDTO.getVoucherTaskId(),taskRpcReqDTO.getTaskDetails().size());
        List<VouchersExternalRpcReqDTO.TaskDetail> taskDetails = taskRpcReqDTO.getTaskDetails();
        List<String> employeePhones = taskDetails.stream().filter(taskDetail -> StringUtils.isNotBlank(taskDetail.getEmployeePhone())).map(VouchersExternalRpcReqDTO.TaskDetail::getEmployeePhone).collect(Collectors.toList());
        Map<String, EmployeeContract> employeeInfoMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(employeePhones)) {
            employeeInfoMap = employeeService.getEmployeeMapByPhone(taskRpcReqDTO.getCompanyId(), employeePhones);
        }
        List<String> nonExistentEmployeePhones = new ArrayList<>();
        List<VouchersTaskDetailsCreateRPCDTO> vouchersTaskDetailsDTOList = new ArrayList<>();
        for (VouchersExternalRpcReqDTO.TaskDetail detail : taskDetails) {
            ValidateUtils.validate(detail);
            if (StringUtils.isBlank(detail.getEmployeeId()) && StringUtils.isBlank(detail.getEmployeePhone())) {
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "员工ID和员工手机号不能同时为空");
            }
            VouchersTaskDetailsCreateRPCDTO createRPCDTO = new VouchersTaskDetailsCreateRPCDTO();
            createRPCDTO.setVoucherDenomination(detail.getVoucherDenomination());
            createRPCDTO.setVoucherDesc(detail.getVoucherDesc());
            createRPCDTO.setVoucherEffectiveTime(detail.getVoucherEffectiveTime());
            createRPCDTO.setVoucherExpiryTime(detail.getVoucherExpiryTime());
            createRPCDTO.setBizNo(detail.getBizNo());
            String employeeId = detail.getEmployeeId();
            if (StringUtils.isNotBlank(employeeId)) {
                createRPCDTO.setEmployeeId(employeeId);
                vouchersTaskDetailsDTOList.add(createRPCDTO);
                continue;
            }
            EmployeeContract employeeContract = employeeInfoMap.get(detail.getEmployeePhone());
            if (ObjectUtil.isNotNull(employeeContract) && StringUtils.isNotBlank(employeeContract.getEmployee_id())) {
                createRPCDTO.setEmployeeId(employeeContract.getEmployee_id());
                FinhubLogger.info("【对接发放分贝券】发放使用手机号[{}]获取员工ID[{}]", detail.getEmployeePhone(), employeeContract.getEmployee_id());
                vouchersTaskDetailsDTOList.add(createRPCDTO);
            } else {
                FinhubLogger.info("【对接发放分贝券】发放使用手机号[{}]获取员工ID为空", detail.getEmployeePhone());
                nonExistentEmployeePhones.add(detail.getEmployeePhone());
            }
        }
        //组装返回对象
        VouchersTaskDetailsRespRPCDTO respRPCDTO = iVouchersTaskCreateService.addGrantTaskDetailsByVouchersTemplet(vouchersTaskDetailsDTOList, taskRpcReqDTO.getVoucherTaskId(), taskRpcReqDTO.getCompanyId(), taskRpcReqDTO.getIsStart(), taskRpcReqDTO.getVoucherSourceType());
        return VouchersExternalDetailsRpcResDTO.builder().vouchersTaskResponseRPCDTOList(respRPCDTO.getVouchersTaskResponseRPCDTOList()).nonExistentEmployees(respRPCDTO.getNonExistentEmployees()).nonExistentEmployeePhones(nonExistentEmployeePhones).build();
    }

    @Override
    public VouchersExternalDetailsRpcResDTO addTaskDetailsWithCheck(VouchersExternalRpcReqDTO taskRpcReqDTO) {
        ValidateUtils.validate(taskRpcReqDTO);
        FinhubLogger.info("【对接分贝券追加检查发放明细】任务名称参数={},任务条数={}",taskRpcReqDTO.getVoucherTaskId(),taskRpcReqDTO.getTaskDetails().size());
        return  iVouchersTaskCreateService.addTaskDetailsWithCheck(taskRpcReqDTO);
    }

    @Override
    public VouchersTaskGrantRpcRespDTO startGrantTask(VouchersTaskGrantRpcReqDTO taskRpcReqDTO) {
        FinhubLogger.info("【对接执行发放任务】请求参数={}",taskRpcReqDTO);
        //-1、失败 0、成功 1、进行中
        Integer code = ResponseCodeEnum.FAIL.getCode();
        String errorMsg = ResponseCodeEnum.FAIL.getValue();
        try{
            taskRpcReqDTO.validate();
            boolean isSuccess =  vouchersTaskHandleService.startGrantTask(taskRpcReqDTO.getVoucherTaskId(), taskRpcReqDTO.getCompanyId());
            if(isSuccess) {
                code = ResponseCodeEnum.SUCCESS.getCode();
                errorMsg = ResponseCodeEnum.SUCCESS.getValue();
            }else{
                VouchersTask vouchersTask = vouchersTaskHandleService.getVouchersTaskById(taskRpcReqDTO.getVoucherTaskId(), taskRpcReqDTO.getCompanyId());
                if(ObjectUtil.isNotNull(vouchersTask)){
                    errorMsg = vouchersTask.getTaskFailureReasons();
                }
            }
        }catch (Exception e){
            FinhubLogger.error("执行分贝券发放异常，分贝券任务ID:{},异常信息:{}",taskRpcReqDTO.getVoucherTaskId(),e.getMessage(),e);
            errorMsg = StringUtils.isNotBlank(e.getMessage())?e.getMessage():ResponseCodeEnum.FAIL.getValue();
        }
        VouchersTaskGrantRpcRespDTO rpcResp = new VouchersTaskGrantRpcRespDTO();
        rpcResp.setCode(code);
        rpcResp.setErrorMsg(errorMsg);
        rpcResp.setVoucherTaskId(taskRpcReqDTO.getVoucherTaskId());
        rpcResp.setCompanyId(taskRpcReqDTO.getCompanyId());
        FinhubLogger.info("执行分贝券发放返回分贝券任务ID={}，rpcResp={}",taskRpcReqDTO.getVoucherTaskId(),rpcResp);
        return rpcResp;
    }

    @Override
    public VouchersTaskGrantRpcRespDTO retryStartTask(VouchersTaskGrantRpcReqDTO taskRpcReqDTO) {
        FinhubLogger.info("【对接执行重新发放任务】请求参数={}",taskRpcReqDTO);
        //-1、失败 0、成功 1、进行中
        Integer code = ResponseCodeEnum.FAIL.getCode();
        String errorMsg = ResponseCodeEnum.FAIL.getValue();
        try{
            taskRpcReqDTO.validate();
            OperationUserInfoDTO operationUserInfoDTO = new OperationUserInfoDTO();
            BeanUtils.copyProperties(taskRpcReqDTO, operationUserInfoDTO);
            boolean isSuccess = vouchersTaskHandleService.retryStartTask(taskRpcReqDTO.getVoucherTaskId(), operationUserInfoDTO);
            if(isSuccess) {
                code = ResponseCodeEnum.SUCCESS.getCode();
                errorMsg = ResponseCodeEnum.SUCCESS.getValue();
            }
        }catch (Exception e){
            FinhubLogger.error("重新发放分贝券异常，分贝券任务ID:{},异常信息:{}",taskRpcReqDTO.getVoucherTaskId(),e.getMessage(),e);
            errorMsg = StringUtils.isNotBlank(e.getMessage()) ? e.getMessage():ResponseCodeEnum.FAIL.getValue();
        }
        VouchersTaskGrantRpcRespDTO rpcResp = new VouchersTaskGrantRpcRespDTO();
        rpcResp.setCode(code);
        rpcResp.setErrorMsg(errorMsg);
        rpcResp.setVoucherTaskId(taskRpcReqDTO.getVoucherTaskId());
        rpcResp.setCompanyId(taskRpcReqDTO.getCompanyId());
        FinhubLogger.info("重新发放分贝券返回分贝券任务ID={}，rpcResp={}",taskRpcReqDTO.getVoucherTaskId(),rpcResp);
        return rpcResp;
    }

    @Override
    public boolean cancelTask(String voucherTaskId, String companyId) {
        FinhubLogger.info("【对接执行取消发放任务】任务ID={}",voucherTaskId);
        return vouchersTaskHandleService.cancelTask(voucherTaskId, companyId);
    }

    @Override
    public List<VouchersExternalRpcResDTO> queryVouchersTask(VouchersTasksReqRPCDTO tasksReqRPCDTO) {
        VouchersTasksRequestDTO tasksRequestDTO = new VouchersTasksRequestDTO();
        BeanUtils.copyProperties(tasksReqRPCDTO, tasksRequestDTO);
        ResponsePage<VouchersTask> responsePage = vouchersTaskHandleService.queryVoucherTaskByPage(tasksRequestDTO);
        List<VouchersExternalRpcResDTO> result = new ArrayList<>();
        List<VouchersTask> vouchersTaskList = responsePage.getDataList();
        if (CollectionUtils.isNotEmpty(vouchersTaskList)) {
            vouchersTaskList.stream().forEach(vouchersTask -> {
                VouchersExternalRpcResDTO dto = new VouchersExternalRpcResDTO();
                BeanUtils.copyProperties(vouchersTask, dto);
                dto.setStatusName(VoucherTaskStatus.getMsgFromValue(dto.getStatus()));
                dto.setVouchersOperationAmount(vouchersTask.getVouchersOperationAmount().subtract(vouchersTask.getTotalRecoveryAmount()));
                result.add(dto);
            });
        }
        return result;
    }

    /**
     *  查询分贝券交易流水
     * @param vouchersBusinessFlowRPCDTO
     * @return
     */
    @Override
    public ResponsePage<VouchersFlowRPCDTO> selectConsumeFlowByPage(VouchersBusinessFlowRPCDTO vouchersBusinessFlowRPCDTO) {
        //检查查询时间范围
        VoucherCommonUtils.checkDateMore31Day(vouchersBusinessFlowRPCDTO.getStartTime(), vouchersBusinessFlowRPCDTO.getEndTime());
        try {
            FinhubLogger.info("【查询分贝券交易流水】，selectConsumeFlowByPage,param：{}", JsonUtils.toJson(vouchersBusinessFlowRPCDTO));
            VouchersFlowRequestDTO requestDTO = new VouchersFlowRequestDTO();
            BeanUtils.copyProperties(vouchersBusinessFlowRPCDTO, requestDTO);
            ResponsePage<VouchersFlowVO> responsePage = vouchersOperationFlowService.selectConsumeFlowByPage(requestDTO, requestDTO.getHidePhoneNo());
            List<VouchersFlowRPCDTO> vouchersFlowRPCDTOS = convertToVouchersFlowRPCDTO(responsePage.getDataList());
            ResponsePage<VouchersFlowRPCDTO> responseInfoPage = new ResponsePage();
            responseInfoPage.setDataList(vouchersFlowRPCDTOS);
            responseInfoPage.setTotalCount(responsePage.getTotalCount());
            responseInfoPage.setCondition(responsePage.getCondition());
            return responseInfoPage;
        } catch (FinPayException e) {
            FinhubLogger.error("【查询分贝券交易流水】异常，参数：{}", JsonUtils.toJson(vouchersBusinessFlowRPCDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【查询分贝券交易流水】异常，参数：{}", JsonUtils.toJson(vouchersBusinessFlowRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【查询分贝券交易流水】异常，参数：{}", JsonUtils.toJson(vouchersBusinessFlowRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    public List<VouchersFlowRPCDTO>  convertToVouchersFlowRPCDTO(List<VouchersFlowVO> list){
        if (com.luastar.swift.base.utils.CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        //查询分贝券模版id  映射关系 voucherId->voucherTaskId->vouchersTemplete
        Map<String, VouchersTempletRPCDTO> voucherIdMapping = buildVoucherIdToTempleteMapping(list);
        List<VouchersFlowRPCDTO> result = Lists.newArrayList();
        for (VouchersFlowVO vouchersFlowVO : list) {
            VouchersFlowRPCDTO vouchersFlowRPCDTO = new VouchersFlowRPCDTO();
            BeanUtils.copyProperties(vouchersFlowVO, vouchersFlowRPCDTO);
            //填充模版id
            VouchersTempletRPCDTO vouchersTempletRPCDTO = voucherIdMapping.get(vouchersFlowVO.getVoucherId());
            if (ObjectUtil.isNotNull(vouchersTempletRPCDTO)){
                vouchersFlowRPCDTO.setVoucherTempletId(vouchersTempletRPCDTO.getVoucherTempletId());
            }
            VouchersIssuerRPCDTO issuerRPCDTO = new VouchersIssuerRPCDTO();
            issuerRPCDTO.setEmployeeId(vouchersFlowVO.getEmployeeId());
            issuerRPCDTO.setEmployeeName(vouchersFlowVO.getEmployeeName());
            issuerRPCDTO.setEmployeePhone(vouchersFlowVO.getEmployeePhone());
            issuerRPCDTO.setEmployeeDepartmentId(vouchersFlowVO.getEmployeeDepartmentId());
            if (!VoucherConstant.STR_SPLIT.equalsIgnoreCase(vouchersFlowVO.getEmployeeDepartment())){
                issuerRPCDTO.setEmployeeDepartment(vouchersFlowVO.getEmployeeDepartment());
            }
            if (!VoucherConstant.STR_SPLIT.equalsIgnoreCase(vouchersFlowVO.getEmployeeDepartmentFull())){
                issuerRPCDTO.setEmployeeDepartmentFull(vouchersFlowVO.getEmployeeDepartmentFull());
            }
            vouchersFlowRPCDTO.setIssuerRPCDTO(issuerRPCDTO);
            result.add(vouchersFlowRPCDTO);
        }
        return result;
    }

    @NotNull
    private Map<String, VouchersTempletRPCDTO> buildVoucherIdToTempleteMapping(List<VouchersFlowVO> list) {
        Map<String, String> voucherTaskIdMapping = Maps.newHashMap();
        Map<String, VouchersTempletRPCDTO> voucherIdMapping = Maps.newHashMap();
        //获取分贝券ID
        List<String> voucherIds = list.stream().map(VouchersFlowVO::getVoucherId).collect(Collectors.toList());
        //获取分贝券详情
        List<VouchersPerson> vouchers = vouchersPersonService.selectVouchersByIds(voucherIds);
        if (CollectionUtils.isNotEmpty(vouchers)){
            List<String> vouchersTaskIds = vouchers.stream().map(VouchersPerson::getVouchersTaskId).map(e -> e.replaceAll(VoucherConstant.VOUCHER_TRANSFER_FLAG, "")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(vouchersTaskIds)){
                //查询原始任务快照
                List<VouchersTask> vouchersTasks = vouchersTaskService.getVouchersTaskByTaskId(vouchersTaskIds);
                if (CollectionUtils.isNotEmpty(vouchersTasks)){
                    for (VouchersTask vouchersTask : vouchersTasks) {
                        voucherTaskIdMapping.put(vouchersTask.getVouchersTaskId(), vouchersTask.getVouchersInfoJson());
                    }
                }
            }
            for (VouchersPerson voucher : vouchers) {
                String s = voucherTaskIdMapping.get(voucher.getVouchersTaskId());
                if (StringUtils.isNotBlank(s)){
                    VouchersTempletRPCDTO templetRPCDTO = JsonUtils.toObj(s, VouchersTempletRPCDTO.class);
                    voucherIdMapping.put(voucher.getVoucherId(), templetRPCDTO);
                }
            }
        }
        return voucherIdMapping;
    }

    @Override
    public ResponsePage<VoucherForStatisticsRPCDTO> grantRecoveryStatistics(VouchersReqRPCDTO vouchersReqRPCDTO) {
        FinhubLogger.info("【已发券统计】，grantRecoveryStatistics,param：{}", JsonUtils.toJson(vouchersReqRPCDTO));
        VoucherCommonUtils.checkDateMore31Day(vouchersReqRPCDTO.getStartTime(), vouchersReqRPCDTO.getEndTime());
        try {
            return vouchersPersonService.grantRecoveryStatistics(vouchersReqRPCDTO);
        }catch (FinPayException e) {
            FinhubLogger.error("【已发券统计】异常，参数：{}", JsonUtils.toJson(vouchersReqRPCDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【已发券统计】异常，参数：{}", JsonUtils.toJson(vouchersReqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【已发券统计】异常，参数：{}", JsonUtils.toJson(vouchersReqRPCDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public ResponsePage<VoucherForStatisticsRPCDTO> voucherRecoveryStatistics(VouchersReqRPCDTO vouchersReqRPCDTO) {
        FinhubLogger.info("【回收券统计】，voucherRecoveryStatistics,param：{}", JsonUtils.toJson(vouchersReqRPCDTO));
        if (Objects.isNull(vouchersReqRPCDTO) || StringUtils.isBlank(vouchersReqRPCDTO.getCompanyId())) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "公司id不能为空");
        }
        VouchersRequestDTO requestDTO = new VouchersRequestDTO();
        BeanUtils.copyProperties(vouchersReqRPCDTO, requestDTO);
        requestDTO.setCompanyId(vouchersReqRPCDTO.getCompanyId());
        Date startTime = requestDTO.getStartTime();
        Date endTime = requestDTO.getEndTime();
        //时间范围限制1年
        VoucherCommonUtils.checkDateMore366Day(startTime, endTime);
        requestDTO.setStartRecoveryTime(startTime);
        requestDTO.setEndRecoveryTime(endTime);
        requestDTO.setStartTime(null);
        requestDTO.setEndTime(null);
        ResponsePage<VoucherForStatisticsVO> responsePage = vouchersPersonService.selectRecoveryVouchersByPage(requestDTO);
        ResponsePage<VoucherForStatisticsRPCDTO> responsePageRPC = new ResponsePage<>();
        if (Objects.isNull(responsePage)) {
            return responsePageRPC;
        }
        responsePageRPC.setTotalCount(responsePage.getTotalCount());
        responsePageRPC.setCondition(responsePage.getCondition());
        List<VoucherForStatisticsVO> dataList = responsePage.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            responsePageRPC.setDataList(dataList.stream().filter(Objects::nonNull).map(e -> {
                VoucherForStatisticsRPCDTO statisticsRPCDTO = new VoucherForStatisticsRPCDTO();
                BeanUtils.copyProperties(e, statisticsRPCDTO);
                return statisticsRPCDTO;
            }).collect(Collectors.toList()));
        }
        return responsePageRPC;
    }
}
