package com.fenbeitong.fenbeipay.rpc.service.bank;

import com.fenbeitong.fenbeipay.acctdech.manager.BankAcctLimitManager;
import com.fenbeitong.fenbeipay.api.model.dto.bank.BankAcctLimitDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankAcctLimitService;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.BankAcctLimitMapper;
import com.fenbeitong.fenbeipay.dto.bank.BankAcctLimit;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: liyi
 * @Date: 2022/10/8 8:11 PM
 */
@Service("iBankAcctLimitService")
public class BankAcctLimitServiceImpl implements IBankAcctLimitService {
    @Autowired
    private BankAcctLimitManager bankAcctLimitManager;

    @Override
    public List<BankAcctLimitDTO> getBankAcctLimits(String bankAccountNo) {
        return bankAcctLimitManager.getBankAcctLimits(bankAccountNo);
    }
}
