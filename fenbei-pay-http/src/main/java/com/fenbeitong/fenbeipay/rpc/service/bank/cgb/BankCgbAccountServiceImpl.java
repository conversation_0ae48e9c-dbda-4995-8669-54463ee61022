package com.fenbeitong.fenbeipay.rpc.service.bank.cgb;

import com.fenbeitong.bank.api.utils.CopyUtils;
import com.fenbeitong.dech.api.model.dto.cgb.CgbSetAccountEntrySignHWReqDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbSetAcountEntrySignHWRespDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbSloveTrapReqDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbSloveTrapRespDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbTrapQueryReqDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbTrapQueryRespDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbTrapReqDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbTrapRespDTO;
import com.fenbeitong.dech.api.service.ICgbVirtualCardService;
import com.fenbeitong.fenbeipay.acctdech.conver.BankCardTrapConverter;
import com.fenbeitong.fenbeipay.acctdech.conver.BankCardTrapFlowConverter;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.BankCardTrapFlowMapper;
import com.fenbeitong.fenbeipay.acctdech.db.mapper.BankCardTrapMapper;
import com.fenbeitong.fenbeipay.acctdech.enums.TrapTypeEnum;
import com.fenbeitong.fenbeipay.acctdech.manager.IBankCardTrapFlowManager;
import com.fenbeitong.fenbeipay.acctdech.manager.IBankCardTrapManager;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyCardFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.BankAcctFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.BankAcctService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.AcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UBankAcctService;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyType;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankCardTrapStatusEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankTrapStatus;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardAcctWithdrawalCreditReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.RechargeCardAcctCreditReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.CgbDirectVirtualCardSolveTrapReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.CgbDirectVirtualCardTrapReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.SolveTrapUpgradeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.TrapQueryRpcReqDto;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.cgb.*;
import com.fenbeitong.fenbeipay.api.service.bank.cgb.IBankCgbAccountService;
import com.fenbeitong.fenbeipay.bank.base.manager.BankCardBaseManagerImpl;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.ApplyCardManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.CreditApplyManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.CreditDistributeManager;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.virtualcard.CgbTrapPayAccountEnum;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.AutoAcctCheckingEventUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.FinhubExceptionUtil;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyCardFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcct;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCreditDistribute;
import com.fenbeitong.fenbeipay.dto.bank.BankCardTrap;
import com.fenbeitong.fenbeipay.dto.bank.BankCardTrapFlow;
import com.fenbeitong.finhub.common.constant.BankAcctTypeEnum;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundAcctCompanyCardOptType;
import com.fenbeitong.finhub.common.constant.FundAcctCreditOptType;
import com.fenbeitong.finhub.common.constant.FundAcctSyncBankStatus;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.BANK_CARD_ACCOUNT_RECHARGE_ERROR1;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.BANK_CARD_FIRST_ENTRY_ERROR;
import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.EXCEPTION;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/3/10
 */
@Service("iBankCgbAccountService")
public class BankCgbAccountServiceImpl extends BankCardBaseManagerImpl implements IBankCgbAccountService {
    @Autowired
    ICgbVirtualCardService iCgbVirtualCardService;
    @Autowired
    BankAcctService bankAcctService;
    @Autowired
    BankAcctFlowService bankAcctFlowService;
    @Autowired
    UBankAcctService uBankAcctService;
    @Autowired
    BankCardTrapFlowMapper bankCardTrapFlowMapper;
    @Autowired
    BankCardTrapMapper bankCardTrapMapper;
    @Autowired
    AcctCompanyGatewayService acctCompanyGatewayService;
    @Value("${cgb.trap.pay.account}")
    String cgbTrapPayAccount;
    @Value("${cgb.trap.pay.account.name}")
    String cgbTrapPayAccountName;
    @Value("${exception.remind.profile}")
    protected String profile;
    @Autowired
    DingDingMsgService dingDingMsgService;
    @Autowired
    ApplyCardManager applyCardManager;
    @Autowired
    IBankCardTrapManager bankCardTrapManager;
    @Autowired
    IBankCardTrapFlowManager iBankCardTrapFlowManager;
    @Autowired
    AcctCompanyCardFlowService acctCompanyCardFlowService;
    @Autowired
    protected AutoAcctCheckingEventUtil autoAcctCheckingEventUtil;
    @Autowired
    public CreditApplyManager creditApplyManager;
    @Autowired
    private CreditDistributeManager creditDistributeManager;
    @Autowired
    private RedissonService redissonService;

    /** 强制解锁时间设置 */
    private static final long LOCK_TIME = 10000L;

    /** 等待时间 **/
    private static final long WAIT_TIME = 12000L;

    /**
     * 一分钱圈存
     * @param virtualCardTrapReqDTO
     * @return
     */
    @Override
    public CgbDirectVirtualCardTrapRespDTO firstTrap(CgbDirectVirtualCardTrapReqDTO virtualCardTrapReqDTO) {
        FinhubLogger.info("圈存1分钱=={}", JsonUtils.toJson(virtualCardTrapReqDTO));
        try {
            ValidateUtils.validate(virtualCardTrapReqDTO);
            BankCardTrap bankCardTrapExisted = bankCardTrapManager.queryFirstTrap(virtualCardTrapReqDTO.getBankAccountNo(),"90");
            if (bankCardTrapExisted != null){
                CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
                cgbDirectVirtualCardTrapRespDTO.setTrapStatus(bankCardTrapExisted.getTrapStatus());
                return cgbDirectVirtualCardTrapRespDTO;
            }
            //防休眠数据初始化
            applyCardManager.updatePreventSleepData(virtualCardTrapReqDTO.getBankAccountNo(),virtualCardTrapReqDTO.getTBankAccountNo());
            BankCard bankCard = queryCardByBankAccountNo(virtualCardTrapReqDTO.getBankAccountNo());
            //非绑定入金设置,不在设置为需要充值
            String incomeFlag = "2";
            setAccountEntrySignHW(virtualCardTrapReqDTO.getTBankAccountNo(),incomeFlag);
            //查询1分钱圈存账户
            BankAcct trapAccount = bankAcctService.findByBankNameAndType(BankNameEnum.CGB.getCode(),BankAcctTypeEnum.TRAP_ACCOUNT.getCode());
            //调用广发发起首笔圈存
            CgbTrapRespDTO cgbTrapRespDTO = callBankInFirstTrap(virtualCardTrapReqDTO,bankCard.getEmployeeName(),trapAccount.getBankAccountNo());
            if (cgbTrapRespDTO.isSuccess()){
                //首次圈存记录转换
                BankCardTrap bankCardTrap = BankCardTrapConverter.covertToBankCardTrap4FirstTrap(virtualCardTrapReqDTO,cgbTrapRespDTO,bankCard);
                boolean saveBankCardTrapSuccess = bankCardTrapManager.saveFirstTrap(bankCardTrap);
                if (saveBankCardTrapSuccess){
                    BankCardTrapFlow bankCardTrapFlow = BankCardTrapFlowConverter.convert4FirstTrap(bankCard,virtualCardTrapReqDTO,cgbTrapRespDTO.getHoldNo(),trapAccount.getBankAccountNo());
                    bankCardTrapFlow.setPayBankAccountNo(cgbTrapPayAccount);
                    if ("test".equals(profile) || "dev".equals(profile) ){
                        bankCardTrapFlow.setPayBankAccountNo(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
                    }
                    bankCardTrapFlow.setPayBankAccountName(CgbTrapPayAccountEnum.getPayAccountNameByEnv(profile));
                    bankCardTrapFlow.setReceiveBankAccountNo(virtualCardTrapReqDTO.getBankAccountNo());
                    bankCardTrapFlow.setReceiveBankAccountName(bankCard.getEmployeeName());
                    iBankCardTrapFlowManager.saveFirstTrapFlow(bankCardTrapFlow);
                    CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
                    cgbDirectVirtualCardTrapRespDTO.setTrapStatus(cgbTrapRespDTO.getStatus());
                    return cgbDirectVirtualCardTrapRespDTO;
                }else {
                    throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_BANK_TRAP_SAVE_ERROR);
                }
            }else {
                CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
                cgbDirectVirtualCardTrapRespDTO.setTrapStatus(cgbTrapRespDTO.getStatus());
                return cgbDirectVirtualCardTrapRespDTO;
            }
        } catch (FinhubException e) {
            dingDingMsgService.sendMsg(virtualCardTrapReqDTO.getBankAccountNo()+":开户一分钱圈存异常");
            FinhubLogger.warn("【分贝通虚拟卡开户一分钱圈存异常】createAccount 参数：{}", JsonUtils.toJson(virtualCardTrapReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            dingDingMsgService.sendMsg(virtualCardTrapReqDTO.getBankAccountNo()+":开户一分钱圈存异常");
            FinhubLogger.warn("【分贝通虚拟卡开户圈一分钱验证异常】createAccount 参数：{}", JsonUtils.toJson(virtualCardTrapReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            dingDingMsgService.sendMsg(virtualCardTrapReqDTO.getBankAccountNo()+":开户一分钱圈存异常");
            FinhubLogger.error("【分贝通虚拟卡开户圈一分钱系统异常】createAccount 参数：{}", JsonUtils.toJson(virtualCardTrapReqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(),
                    EXCEPTION.getMsg());
        }
    }


    /**
     * 调用银行做首笔圈存
     * @param customAcctNo 收款账户
     * @param recAccountName 收款账户名称
     * @return
     */
    public CgbTrapRespDTO callBankInFirstTrap(CgbDirectVirtualCardTrapReqDTO virtualCardTrapReqDTO,String recAccountName,String customAcctNo) {
        CgbTrapRespDTO cgbTrapRespDTO;
        try {
            CgbTrapReqDTO trapReqDTO = new CgbTrapReqDTO();
            trapReqDTO.setPayAccount(cgbTrapPayAccount);
            if ("test".equals(profile) || "dev".equals(profile) ){
                trapReqDTO.setPayAccount(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
            }
            trapReqDTO.setPayAccountName(CgbTrapPayAccountEnum.getPayAccountNameByEnv(profile));
            trapReqDTO.setRecAccount(virtualCardTrapReqDTO.getBankAccountNo());
            trapReqDTO.setRecAccountName(recAccountName);
            trapReqDTO.setType("01");
            trapReqDTO.setBusiScene("10");
            trapReqDTO.setCustAccno(customAcctNo);
            trapReqDTO.setPayAmount(String.valueOf(virtualCardTrapReqDTO.getTrapAmount()));
            trapReqDTO.setTxnId(virtualCardTrapReqDTO.getTxnId());
            FinhubLogger.info("firstTrap request:{}",JsonUtils.toJson(trapReqDTO));
            cgbTrapRespDTO = iCgbVirtualCardService.trap(trapReqDTO);
            FinhubLogger.info("firstTrap request:{},response:{}",JsonUtils.toJson(trapReqDTO),JsonUtils.toJson(cgbTrapRespDTO));
        }catch (Exception e){
            dingDingMsgService.sendMsg(virtualCardTrapReqDTO.getBankAccountNo()+":firstTrap request error");
            cgbTrapRespDTO = new CgbTrapRespDTO();
            cgbTrapRespDTO.setStatus("99");
        }
        return cgbTrapRespDTO;
    }

    @Override
    public CgbDirectVirtualCardTrapRespDTO queryFirstTrap(String employeeId, String companyId) {
        BankCardTrap bankCardTrap =  bankCardTrapManager.queryFirstTrapByCompanyIdAndEmployeeId(companyId,employeeId);
        if (bankCardTrap == null) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_BANK_TRAP_QUERY_ERROR);
        }
        //首笔圈存中
        if (!"33".equals(bankCardTrap.getTrapStatus())){
            CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
            cgbDirectVirtualCardTrapRespDTO.setTrapStatus(bankCardTrap.getTrapStatus());
            return cgbDirectVirtualCardTrapRespDTO;
        }
        CgbTrapQueryReqDTO cgbTrapQueryReqDTO = new CgbTrapQueryReqDTO();
        cgbTrapQueryReqDTO.setOriFlowId(bankCardTrap.getOriTxnId());
        cgbTrapQueryReqDTO.setChannel("330");
        cgbTrapQueryReqDTO.setSChannel("330");
        cgbTrapQueryReqDTO.setTxnId(IDGen.genId(IDGen.BANK_CARD_TRAP));
        try {
            FinhubLogger.info("firstTrapQuery request:{}",JsonUtils.toJson(cgbTrapQueryReqDTO));
            CgbTrapQueryRespDTO cgbTrapQueryRespDTO = iCgbVirtualCardService.trapQuery(cgbTrapQueryReqDTO);
            FinhubLogger.info("firstTrapQuery request:{},response:{}",JsonUtils.toJson(cgbTrapQueryRespDTO),JsonUtils.toJson(cgbTrapQueryRespDTO));
            if (cgbTrapQueryRespDTO!= null && cgbTrapQueryRespDTO.isSuccess()){
                bankCardTrapManager.updateFirstTrap(bankCardTrap.getId(),cgbTrapQueryRespDTO.getHoldNo(),cgbTrapQueryRespDTO.getStatus());
                CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
                cgbDirectVirtualCardTrapRespDTO.setTrapStatus(cgbTrapQueryRespDTO.getStatus());
                return cgbDirectVirtualCardTrapRespDTO;
            }else {
                CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
                cgbDirectVirtualCardTrapRespDTO.setTrapStatus(bankCardTrap.getTrapStatus());
                return cgbDirectVirtualCardTrapRespDTO;
            }
        }catch (Exception e){
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_BANK_TRAP_QUERY_ERROR);
        }
    }

    @Override
    public String queryHoldNo(String bankAccountNo) {
        Example example = new Example(BankCardTrap.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bankAccountNo", bankAccountNo);
        criteria.andEqualTo("trapType", TrapTypeEnum.FIRST.getKey());
        BankCardTrap bankCardTrap =  bankCardTrapMapper.selectOneByExample(example);
        if (bankCardTrap != null){
            return bankCardTrap.getTrapNo();
        }
        return null;
    }

    @Override
    public CgbDirectVirtualCardTrapRespDTO addTrap(CgbDirectVirtualCardTrapReqDTO virtualCardTrapReqDTO) {
        FinhubLogger.info("追加圈存:{}", JsonUtils.toJson(virtualCardTrapReqDTO));
        CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
        validateCommon(virtualCardTrapReqDTO);
        BankCardTrap firstTrapRecord = bankCardTrapManager.queryFirstTrap(virtualCardTrapReqDTO.getBankAccountNo(), "90");
        if (firstTrapRecord == null || StringUtils.isEmpty(firstTrapRecord.getTrapNo())) {
            dingDingMsgService.sendMsg(virtualCardTrapReqDTO.getBankAccountNo() + ":未查到有效圈存记录");
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_FIRST_TRAP_NO_MISSING_ERROR);
        }
        AcctCommonBaseDTO accountSub4CompanyCard = queryCompanyAccountOfVirtualCard(firstTrapRecord.getCompanyId());
        String holdNo = firstTrapRecord.getTrapNo();
        BankCard bankCard = queryCardByBankAccountNo(virtualCardTrapReqDTO.getBankAccountNo());

        BankCardTrap bankCardTrap = bankCardTrapManager.queryTrapByOriTxnId(virtualCardTrapReqDTO.getTxnId(), BankNameEnum.CGB.getCode(), TrapTypeEnum.ADD.getKey());
        FinhubLogger.info("bankCardTrap圈存记录信息，{}", JsonUtils.toJson(bankCardTrap));
        if (Objects.isNull(bankCardTrap)) {
            bankCardTrap = BankCardTrapConverter.covertToCgbAddTrap(virtualCardTrapReqDTO);
            bankCardTrapManager.saveTrap(bankCardTrap);
        } else{
            if (BankCardTrapStatusEnum.isTraping(bankCardTrap.getTrapStatus())) {
                throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST);
            }
            if (BankCardTrapStatusEnum.isSuccess(bankCardTrap.getTrapStatus())) {
                cgbDirectVirtualCardTrapRespDTO.setHoldNo(holdNo);
                cgbDirectVirtualCardTrapRespDTO.setPayUserId(accountSub4CompanyCard.getBankAccountNo());
                cgbDirectVirtualCardTrapRespDTO.setTrapAmount(virtualCardTrapReqDTO.getTrapAmount());
                cgbDirectVirtualCardTrapRespDTO.setRecUserId(virtualCardTrapReqDTO.getBankAccountNo());
                cgbDirectVirtualCardTrapRespDTO.setTxnTrapId(virtualCardTrapReqDTO.getTxnId());
                return cgbDirectVirtualCardTrapRespDTO;
            }
            //更新记录为圈存中
            bankCardTrapManager.updateTrapStatus(bankCardTrap.getId(), BankCardTrapStatusEnum.TRAPING.getCode());
        }

        try{
            //分批圈存
            Long limitValue = 100000L;
            Long trapedAmount = 0L;
            Long trapAmount = bankCardTrap.getRemainAmount().longValue();

            BigDecimal dayTrapLimit = new BigDecimal(500000);

            String lockKey = MessageFormat.format(RedisKeyConstant.FBB_TRAP_COMPANY_KEY, bankCardTrap.getCompanyId());
            try {
                boolean lock = redissonService.tryLock(LOCK_TIME, WAIT_TIME, TimeUnit.MILLISECONDS, lockKey);
                if (!lock) {
                    throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
                }

                while (trapedAmount < bankCardTrap.getRemainAmount().longValue()){
                    if (bankCard.getCardBalance().compareTo(bankCard.getCardAcctBalance())<=0){
                        FinhubLogger.info("当前卡的实际余额已经大于等于卡余额，bankCard={}", JsonUtils.toJson(bankCard));
                        throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_BALANCE_ENOUGH_ERROR);
                    }

                    //计算当日已圈存
                    BigDecimal trapSum = new BigDecimal(0);
                    List<BankCardTrapFlow> bankCardTrapFlows = iBankCardTrapFlowManager.queryTrapFlowByEmployeeId(bankCard.getEmployeeId(), bankCard.getBankName(), DateUtils.formatDate(new Date()));
                    if (CollectionUtils.isNotEmpty(bankCardTrapFlows)){
                        trapSum = bankCardTrapFlows.stream().map(p -> p.getSubOperationAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (trapSum.compareTo(dayTrapLimit)>=0){
                            FinhubLogger.info("当日额度已经圈满，bankCard={}", JsonUtils.toJson(bankCard));
                            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_DAY_ENOUGH_ERROR);
                        }
                    }

                    //当日可圈存金额
                    BigDecimal dayCanTrapAmount = dayTrapLimit.subtract(trapSum);


                    //可圈存金额
                    BigDecimal canTrapAmount = bankCard.getCardBalance().subtract(bankCard.getCardAcctBalance());
                    //卡可圈存和当日可圈存比较，较小的为可圈存金额
                    canTrapAmount = canTrapAmount.compareTo(dayCanTrapAmount) > 0?dayCanTrapAmount:canTrapAmount;

                    //如果卡上可圈存少于单次圈存极值，此时要比较一下
                    if(BigDecimal.valueOf(limitValue).compareTo(canTrapAmount) > 0 && BigDecimal.valueOf(trapAmount).compareTo(canTrapAmount) > 0){
                        trapAmount = canTrapAmount.longValue();
                    }

                    if (trapAmount > limitValue){
                        addTrapByLessThen2000(bankCard,accountSub4CompanyCard,holdNo,String.valueOf(limitValue),virtualCardTrapReqDTO, bankCardTrap);
                        trapedAmount = trapedAmount + limitValue;
                        trapAmount = trapAmount - limitValue;
                    }else {
                        addTrapByLessThen2000(bankCard,accountSub4CompanyCard,holdNo,String.valueOf(trapAmount),virtualCardTrapReqDTO, bankCardTrap);
                        trapedAmount = trapedAmount + trapAmount;
                    }
                    bankCard = queryCardByBankAccountNo(virtualCardTrapReqDTO.getBankAccountNo());
                }
            } finally {
                redissonService.unLock(lockKey);
            }
            //更新虚拟卡企业流水
            saveAcctCompanyCardFlow4Trap(virtualCardTrapReqDTO.getApplyTransNo(),virtualCardTrapReqDTO.getTxnId(),virtualCardTrapReqDTO.getTrapAmount(),FundAcctCompanyCardOptType.FROZEN_BANK_RECALL);

            cgbDirectVirtualCardTrapRespDTO.setHoldNo(holdNo);
            cgbDirectVirtualCardTrapRespDTO.setPayUserId(accountSub4CompanyCard.getBankAccountNo());
            cgbDirectVirtualCardTrapRespDTO.setTrapAmount(virtualCardTrapReqDTO.getTrapAmount());
            cgbDirectVirtualCardTrapRespDTO.setRecUserId(virtualCardTrapReqDTO.getBankAccountNo());
            cgbDirectVirtualCardTrapRespDTO.setTxnTrapId(virtualCardTrapReqDTO.getTxnId());
            bankCardTrapManager.updateTrapStatus(bankCardTrap.getId(), BankCardTrapStatusEnum.SUCCESS.getCode());

            return cgbDirectVirtualCardTrapRespDTO;
        } catch (FinhubException e) {
            bankCardTrapManager.updateTrapStatus(bankCardTrap.getId(), BankCardTrapStatusEnum.FAIL.getCode(), e.getMessage());
            if (StringUtils.isNotBlank(e.getMessage()) && !e.getMessage().contains("该账户非柜面转账日限额超限")  &&  !e.getMessage().contains("当日额度已经圈满")){
                dingDingMsgService.sendMsg("分贝通虚拟卡追加圈存异常2:"+virtualCardTrapReqDTO.getBankAccountNo()+"--"+e.getMessage());
            }
            FinhubLogger.warn("【分贝通虚拟卡追加圈存异常2】addTrap 参数：{}", JsonUtils.toJson(virtualCardTrapReqDTO), e);
            throw new FinhubException(e.getCode(),e.getType(), e.getMessage());
        } catch (Exception e) {
            bankCardTrapManager.updateTrapStatus(bankCardTrap.getId(), BankCardTrapStatusEnum.FAIL.getCode(), EXCEPTION.getMsg());
            dingDingMsgService.sendMsg("分贝通虚拟卡追加圈存异常3"+virtualCardTrapReqDTO.getBankAccountNo());
            FinhubLogger.warn("【分贝通虚拟卡追加圈存异常3】addTrap 参数：{}", JsonUtils.toJson(virtualCardTrapReqDTO), e);
            throw new FinhubException(EXCEPTION.getCode());
        }
    }

    private void validateCommon(Object obj) {
        try{
            ValidateUtils.validate(obj);
        } catch (ValidateException e) {
            FinhubLogger.warn("参数校验不通过：{}", JsonUtils.toJson(obj), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        }
    }

    private void saveAcctCompanyCardFlow4Trap(String applyTransNo,String trapTxnId,BigDecimal trapAmount,FundAcctCompanyCardOptType fundAcctCompanyCardOptType){
        AcctCompanyCardFlow acctCompanyCardFlow = acctCompanyCardFlowService.queryByOptTypeAndBizNo(fundAcctCompanyCardOptType,applyTransNo);
        if(acctCompanyCardFlow!=null){
            AcctCompanyCardFlow companyCardFlow = new AcctCompanyCardFlow();
            companyCardFlow.setId(acctCompanyCardFlow.getId());
            companyCardFlow.setSyncBankStatus(FundAcctSyncBankStatus.SYNC.getCode());
            companyCardFlow.setSyncBankAmount(trapAmount);
            companyCardFlow.setBankTransNo(trapTxnId);
            companyCardFlow.setSyncBankTransNo(trapTxnId);
            companyCardFlow.setSyncBankTime(new Date());
            acctCompanyCardFlowService.updateByIdSelective(companyCardFlow);
        }else {
            FinhubLogger.warn(applyTransNo+ "：未查到企业虚拟卡流水");
        }
    }

    /**
     * @Description: 广发直连渠道虚拟卡对账，对圈存流水，不对企业虚拟卡流水.
     * @Author: guogx
     * @Date: 2022/9/25 上午11:37
     */
    private void sendVirtualCardFlowChecking(BankCardTrapFlow bankCardTrapFlow, BankCard bankCard) {
        try{
            FinhubLogger.info("BankCgbAccountServiceImpl#sendVirtualCardFlowChecking#虚拟卡对账圈存流水入对账信息req:{}", JsonUtils.toJson(bankCardTrapFlow));
            //广发直连渠道发送对账消息
            if(BankNameEnum.isCgb(bankCard.getBankName()) && bankCard.getDirectAcctType()!=null && bankCard.getDirectAcctType()==1){
                KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
                kafkaAutoAcctCheckingMsg.setAccountFlowId(bankCardTrapFlow.getId());
                FinhubLogger.info("BankCgbAccountServiceImpl#sendVirtualCardFlowChecking#bankCardTrapFlow.getId:{}", bankCardTrapFlow.getId());
                kafkaAutoAcctCheckingMsg.setAccountId(bankCard.getAccountId());
                kafkaAutoAcctCheckingMsg.setAccountModel(bankCard.getAccountModel());
                kafkaAutoAcctCheckingMsg.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
                kafkaAutoAcctCheckingMsg.setBankAccountNo(bankCard.getBankAccountNo());
                kafkaAutoAcctCheckingMsg.setBankName(bankCard.getBankName());
                //没有:银行虚户ID,公司主体Id, reBizNo refundTxnId cashierTxnId syncBankTransNo  targetBankName targetBankAcctId   targetAccount      targetAccountSubType
                kafkaAutoAcctCheckingMsg.setCompanyId(bankCard.getCompanyId());
                if("trap".equals(bankCardTrapFlow.getTrapOperationType())){
                    kafkaAutoAcctCheckingMsg.setOperationType(FundAcctCreditOptType.FROZEN_BANK_RECALL.getKey());
                    kafkaAutoAcctCheckingMsg.setOperationTypeDesc(FundAcctCreditOptType.FROZEN_BANK_RECALL.getValue());
                } else if("solveTrap".equals(bankCardTrapFlow.getTrapOperationType())){
                    kafkaAutoAcctCheckingMsg.setOperationType(FundAcctCompanyCardOptType.FROZEN_BANK_REFUND.getKey());
                    kafkaAutoAcctCheckingMsg.setOperationTypeDesc(FundAcctCreditOptType.FROZEN_BANK_REFUND.getValue());
                }
                kafkaAutoAcctCheckingMsg.setBizNo(bankCardTrapFlow.getTrapNo());
                kafkaAutoAcctCheckingMsg.setOperationAmount(bankCardTrapFlow.getSubOperationAmount());
                kafkaAutoAcctCheckingMsg.setSyncBankAmount(bankCardTrapFlow.getSubOperationAmount());
                kafkaAutoAcctCheckingMsg.setSyncBankTime(bankCardTrapFlow.getCreateTime());
                kafkaAutoAcctCheckingMsg.setCreateTime(bankCardTrapFlow.getCreateTime());
                kafkaAutoAcctCheckingMsg.setSyncBankTransNo(bankCardTrapFlow.getSubTxnId());
                autoAcctCheckingEventUtil.sendAccountChangeMsgEvent(kafkaAutoAcctCheckingMsg);
            }
        }catch (Exception e){
            FinhubLogger.error("BankCgbAccountServiceImpl#sendVirtualCardFlowChecking#虚拟卡对账圈存流水入对账信息req:{}", JsonUtils.toJson(bankCardTrapFlow), e);
        }
    }


    /**
     * 限额追加圈存2000
     * @return
     */
    public void addTrapByLessThen2000(BankCard bankCard,AcctCommonBaseDTO accountSub4CompanyCard, String holdNo,String addTrapAmountInOnce,CgbDirectVirtualCardTrapReqDTO virtualCardTrapReqDTO, BankCardTrap bankCardTrap) {
        String companyBankAcctId = accountSub4CompanyCard.getBankAcctId();
        //非绑定入金设置
        setAccountEntrySignHW(virtualCardTrapReqDTO.getTBankAccountNo(),"2");
        CgbTrapReqDTO trapReqDTO = new CgbTrapReqDTO();
        trapReqDTO.setPayAccount(cgbTrapPayAccount);
        if ("test".equals(profile) || "dev".equals(profile) ){
            trapReqDTO.setPayAccount(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
        }
        trapReqDTO.setPayAccountName(CgbTrapPayAccountEnum.getPayAccountNameByEnv(profile));
        trapReqDTO.setRecAccount(bankCard.getBankAcctId());
        //企业员工姓名需要查询实名
        trapReqDTO.setRecAccountName(bankCard.getEmployeeName());
        trapReqDTO.setType("02");
        trapReqDTO.setHoldNo(holdNo);
        trapReqDTO.setBusiScene("11");
        trapReqDTO.setCustAccno(companyBankAcctId);
        trapReqDTO.setPayAmount(addTrapAmountInOnce);
        trapReqDTO.setTxnId(IDGen.genId(IDGen.BANK_CARD_TRAP));

        FinhubLogger.info("addTrap req:{}",JsonUtils.toJson(trapReqDTO));
        CgbTrapRespDTO cgbTrapRespDTO = iCgbVirtualCardService.trap(trapReqDTO);
        FinhubLogger.info("addTrap req:{},resp:{}",JsonUtils.toJson(trapReqDTO),JsonUtils.toJson(cgbTrapRespDTO));
        if (cgbTrapRespDTO!= null && cgbTrapRespDTO.isSuccess() && cgbTrapRespDTO.getHoldNo() != null){
            BankCardTrapFlow bankCardTrapFlow = BankCardTrapFlowConverter.convert4AddTrap(trapReqDTO,bankCard,virtualCardTrapReqDTO.getTxnId(), virtualCardTrapReqDTO.getTrapAmount(),holdNo,companyBankAcctId);
            iBankCardTrapFlowManager.saveAddTrapFlow(bankCardTrapFlow);
            bankCardTrapManager.reduceRemainAmount(bankCardTrap.getId(), addTrapAmountInOnce, bankCardTrap.getTrapStatus());

            RechargeCardAcctCreditReqDTO rechargeReqDTO = RechargeCardAcctCreditReqDTO.builder()
                    .bankAccountNo(bankCard.getBankAccountNo())
                    .bankName(bankCard.getBankName())
                    .bizNo(bankCardTrap.getTrapNo())
                    .employeeId(bankCard.getEmployeeId())
                    .companyId(bankCard.getCompanyId())
                    .companyName(accountSub4CompanyCard.getCompanyName())
                    .operationUserId(bankCard.getEmployeeId())
                    .operationUserName(bankCard.getEmployeeName())
                    .bankApplyType(BankApplyType.CONSUME_RECHARGE_CREDIT.getKey())
                    .operationAmount(new BigDecimal(addTrapAmountInOnce)).build();
            applyCardManager.rechargeCardAcctBalance(rechargeReqDTO);
            //CGB虚拟卡入对账系统
            sendVirtualCardFlowChecking(bankCardTrapFlow, bankCard);
        }else {
            FinhubLogger.info("【分贝通虚拟卡追加圈存异常】addTrap req：{},resp:{}", JsonUtils.toJson(trapReqDTO),JsonUtils.toJson(cgbTrapRespDTO));
            throw new FinhubException(BANK_CARD_ACCOUNT_RECHARGE_ERROR1.getCode(), BANK_CARD_ACCOUNT_RECHARGE_ERROR1.getType(),(cgbTrapRespDTO.getErrorCode()+"-"+cgbTrapRespDTO.getErrorMsg()));
        }
    }

    /**
     * 更新发放单圈存状态
     * @param applyTransNo
     * @param trapStatus
     * @param trapErrorAmountSum
     */
    private void updateTrapStatusAndAmount(String applyTransNo, int trapStatus, String failReason, BigDecimal trapErrorAmountSum) {

        BankCardCreditDistribute bankCardCreditDistribute = creditDistributeManager.queryDistributeByTransNoAndBankName(applyTransNo, BankNameEnum.CGB.getCode());
        if(Objects.isNull(bankCardCreditDistribute)) return;
        BankCardCreditDistribute distributeOrder = new BankCardCreditDistribute();
        distributeOrder.setApplyTransNo(applyTransNo);
        distributeOrder.setBankName(BankNameEnum.CGB.getCode());
        distributeOrder.setTrapStatus(trapStatus);
        distributeOrder.setFailureReason(failReason);
        distributeOrder.setTrapErrorAmount(trapErrorAmountSum);
        //更新虚拟卡发放单状态
        creditDistributeManager.updateTrapStatusByTransNo(distributeOrder);
    }

    @Override
    public CgbDirectVirtualCardSolveTrapRespDTO solveTrap(CgbDirectVirtualCardSolveTrapReqDTO cgbDirectVirtualCardSolveTrapReqDTO) {
        FinhubLogger.info("解圈存:{}", JsonUtils.toJson(cgbDirectVirtualCardSolveTrapReqDTO));

        validateCommon(cgbDirectVirtualCardSolveTrapReqDTO);

        BankCard bankCard = queryCardByBankAccountNo(cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo());
        if (Objects.isNull(bankCard)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_ACCOUNT_NO_EXIST);
        }

        AcctCommonBaseDTO accountSub4CompanyCard = queryCompanyAccountOfVirtualCard(bankCard.getCompanyId());

        BankCardTrap firstTrapRecord = bankCardTrapManager.queryFirstTrap(cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo(),"90");
        if (firstTrapRecord == null || StringUtils.isEmpty(firstTrapRecord.getTrapNo())) {
            dingDingMsgService.sendMsg(cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo() + ":未查到有效圈存记录");
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_FIRST_TRAP_NO_MISSING_ERROR);
        }
        String holdNo = firstTrapRecord.getTrapNo();

        BankCardTrap bankCardTrap = bankCardTrapManager.queryTrapByOriTxnId(cgbDirectVirtualCardSolveTrapReqDTO.getTxnId(), BankNameEnum.CGB.getCode(), TrapTypeEnum.SOLVE.getKey());
        FinhubLogger.info("bankCardTrap解圈存记录信息，{}", JsonUtils.toJson(bankCardTrap));
        if (Objects.isNull(bankCardTrap)) {
            bankCardTrap = BankCardTrapConverter.covertToCgbSolveTrap(cgbDirectVirtualCardSolveTrapReqDTO);
            bankCardTrapManager.saveTrap(bankCardTrap);
        } else{
            if (BankCardTrapStatusEnum.isTraping(bankCardTrap.getTrapStatus())) {
                throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST);
            }
            if (BankCardTrapStatusEnum.isSuccess(bankCardTrap.getTrapStatus())) {
                CgbDirectVirtualCardSolveTrapRespDTO cgbDirectVirtualCardSolveTrapRespDTO = new CgbDirectVirtualCardSolveTrapRespDTO();
                cgbDirectVirtualCardSolveTrapRespDTO.setSolveTrapStatus(bankCardTrap.getTrapStatus());
                cgbDirectVirtualCardSolveTrapRespDTO.setPayUserId(bankCard.getBankAcctId());
                cgbDirectVirtualCardSolveTrapRespDTO.setRecUserId(cgbTrapPayAccount);
                if ("test".equals(profile) || "dev".equals(profile) ){
                    cgbDirectVirtualCardSolveTrapRespDTO.setRecUserId(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
                }
                cgbDirectVirtualCardSolveTrapRespDTO.setTrapAmount(cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount());
                cgbDirectVirtualCardSolveTrapRespDTO.setTxnTrapId(cgbDirectVirtualCardSolveTrapReqDTO.getTxnId());
                return cgbDirectVirtualCardSolveTrapRespDTO;
            }
            //更新记录为圈存中
            bankCardTrapManager.updateTrapStatus(bankCardTrap.getId(), BankCardTrapStatusEnum.TRAPING.getCode());
        }

        try{
            String errorMsg = "";
            BigDecimal trapErrorAmountSum = BigDecimal.ZERO;

            /*
             * 分批圈存
             */
            Long limitValue = 200000L;
            Long trapedAmount = 0L;
            Long trapAmount = bankCardTrap.getRemainAmount().longValue();

            String lockKey = MessageFormat.format(RedisKeyConstant.FBB_TRAP_COMPANY_KEY, bankCardTrap.getCompanyId());
            try {
                boolean lock = redissonService.tryLock(LOCK_TIME, WAIT_TIME, TimeUnit.MILLISECONDS, lockKey);
                if (!lock) {
                    throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
                }

                while (trapedAmount < bankCardTrap.getRemainAmount().longValue()){
                    if (trapAmount > limitValue){
                        CgbSolveTrapDto result =solveTrapByLessThen2000(bankCard,accountSub4CompanyCard,holdNo,String.valueOf(limitValue),cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount(),cgbDirectVirtualCardSolveTrapReqDTO.getTBankAccountNo(),cgbDirectVirtualCardSolveTrapReqDTO.getTxnId(),cgbDirectVirtualCardSolveTrapReqDTO.getCompanyAccountId(),cgbDirectVirtualCardSolveTrapReqDTO.getBankName(),bankCardTrap);
                        trapedAmount = trapedAmount + limitValue;
                        trapAmount = trapAmount - limitValue;
                        if(StringUtils.isNotBlank(result.getErrorMsg())){
                            errorMsg = result.getErrorMsg();
                        }
                        trapErrorAmountSum = trapErrorAmountSum.add(result.getTrapErrorAmount() == null ? BigDecimal.ZERO : result.getTrapErrorAmount());
                    }else {
                        CgbSolveTrapDto result = solveTrapByLessThen2000(bankCard,accountSub4CompanyCard,holdNo,String.valueOf(trapAmount),cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount(),cgbDirectVirtualCardSolveTrapReqDTO.getTBankAccountNo(),cgbDirectVirtualCardSolveTrapReqDTO.getTxnId(),cgbDirectVirtualCardSolveTrapReqDTO.getCompanyAccountId(),cgbDirectVirtualCardSolveTrapReqDTO.getBankName(),bankCardTrap);
                        trapedAmount = trapedAmount + limitValue;
                        if(StringUtils.isNotBlank(result.getErrorMsg())) {
                            errorMsg = result.getErrorMsg();
                        }
                        trapErrorAmountSum = trapErrorAmountSum.add(result.getTrapErrorAmount() == null ? BigDecimal.ZERO : result.getTrapErrorAmount());
                    }
                }
                FinhubLogger.info("解圈存失败信息errorMsg={},trapErrorAmountSum={}",errorMsg,trapErrorAmountSum);
            } finally {
                redissonService.unLock(lockKey);
            }

            if (StringUtils.isNotBlank(errorMsg) && trapErrorAmountSum.compareTo(BigDecimal.ZERO) > 0) {
                creditApplyManager.updateTrapStatusByApplyTransNo(cgbDirectVirtualCardSolveTrapReqDTO.getApplyTransNo(),errorMsg,BankTrapStatus.FAIL,trapErrorAmountSum);
                updateTrapStatusAndAmount(cgbDirectVirtualCardSolveTrapReqDTO.getApplyTransNo(), BankTrapStatus.FAIL.getKey(), errorMsg, trapErrorAmountSum);
            }else {
                creditApplyManager.updateTrapStatusByApplyTransNo(cgbDirectVirtualCardSolveTrapReqDTO.getApplyTransNo(),"",BankTrapStatus.SUCCESS,BigDecimal.ZERO);
                updateTrapStatusAndAmount(cgbDirectVirtualCardSolveTrapReqDTO.getApplyTransNo(), BankTrapStatus.SUCCESS.getKey(), null, BigDecimal.ZERO);
            }
            //更新虚拟卡企业流水
            saveAcctCompanyCardFlow4Trap(cgbDirectVirtualCardSolveTrapReqDTO.getApplyTransNo(),cgbDirectVirtualCardSolveTrapReqDTO.getTxnId(),cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount().subtract(trapErrorAmountSum),FundAcctCompanyCardOptType.FROZEN_BANK_REFUND);
            CgbDirectVirtualCardSolveTrapRespDTO cgbDirectVirtualCardSolveTrapRespDTO = new CgbDirectVirtualCardSolveTrapRespDTO();
            cgbDirectVirtualCardSolveTrapRespDTO.setSolveTrapStatus(trapErrorAmountSum.compareTo(BigDecimal.ZERO) >0 ? "99" : "90");
            cgbDirectVirtualCardSolveTrapRespDTO.setPayUserId(bankCard.getBankAcctId());
            cgbDirectVirtualCardSolveTrapRespDTO.setRecUserId(cgbTrapPayAccount);
            if ("test".equals(profile) || "dev".equals(profile) ){
                cgbDirectVirtualCardSolveTrapRespDTO.setRecUserId(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
            }
            cgbDirectVirtualCardSolveTrapRespDTO.setTrapAmount(cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount());
            cgbDirectVirtualCardSolveTrapRespDTO.setTxnTrapId(cgbDirectVirtualCardSolveTrapReqDTO.getTxnId());
            cgbDirectVirtualCardSolveTrapRespDTO.setErrorMsg(errorMsg.toString());
            bankCardTrapManager.updateTrapStatus(bankCardTrap.getId(), cgbDirectVirtualCardSolveTrapRespDTO.getSolveTrapStatus(), errorMsg.toString());
            return cgbDirectVirtualCardSolveTrapRespDTO;
        } catch (FinhubException e) {
            bankCardTrapManager.updateTrapStatus(bankCardTrap.getId(), BankCardTrapStatusEnum.FAIL.getCode(), e.getMessage());
            dingDingMsgService.sendMsg("分贝通虚拟卡追加圈存异常,请处理"+cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo());
            FinhubLogger.error("【分贝通虚拟卡追加圈存一分钱异常】addTrap 参数：{}", JsonUtils.toJson(cgbDirectVirtualCardSolveTrapReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            bankCardTrapManager.updateTrapStatus(bankCardTrap.getId(), BankCardTrapStatusEnum.FAIL.getCode(), EXCEPTION.getMsg());
            dingDingMsgService.sendMsg("分贝通虚拟卡追加圈存异常,请处理"+cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo());
            FinhubLogger.error("【分贝通虚拟卡追加圈存一分钱异常】addTrap 参数：{}", JsonUtils.toJson(cgbDirectVirtualCardSolveTrapReqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(), EXCEPTION.getMsg());
        }
    }

    @Override
    public CgbDirectVirtualCardSolveTrapRespDTO solveTrap4Upgrade(SolveTrapUpgradeReqDTO cgbDirectVirtualCardSolveTrapReqDTO) {
        FinhubLogger.info("解圈存:{}", JsonUtils.toJson(cgbDirectVirtualCardSolveTrapReqDTO));
        try {
            ValidateUtils.validate(cgbDirectVirtualCardSolveTrapReqDTO);
            BankCard bankCard = queryCardByBankAccountNo(cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo());
            AcctCommonBaseDTO accountSub4CompanyCard = queryCompanyAccountOfVirtualCard(bankCard.getCompanyId());
            String holdNo = cgbDirectVirtualCardSolveTrapReqDTO.getHupoHoldNo();
            StringBuilder errorMsg = new StringBuilder();
            BigDecimal trapErrorAmountSum = BigDecimal.ZERO;
            /*
              分批圈存
             */
            Long limitValue = 200000L;
            Long trapedAmount = 0L;
            Long trapAmount = cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount().longValue();
            while (trapedAmount < cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount().longValue()){
                if (trapAmount > limitValue){
                    CgbSolveTrapDto result =solveTrapByLessThen2000(bankCard,accountSub4CompanyCard,holdNo,String.valueOf(limitValue),cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount(),cgbDirectVirtualCardSolveTrapReqDTO.getTBankAccountNo(),cgbDirectVirtualCardSolveTrapReqDTO.getTxnId(),cgbDirectVirtualCardSolveTrapReqDTO.getCompanyAccountId(),cgbDirectVirtualCardSolveTrapReqDTO.getBankName(), null);
                    trapedAmount = trapedAmount + limitValue;
                    trapAmount = trapAmount - limitValue;
                    errorMsg.append(result.getErrorMsg()).append(";");
                    trapErrorAmountSum.add(result.getTrapErrorAmount() == null ? BigDecimal.ZERO : result.getTrapErrorAmount());
                }else {
                    CgbSolveTrapDto result =solveTrapByLessThen2000(bankCard,accountSub4CompanyCard,holdNo,String.valueOf(trapAmount),cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount(),cgbDirectVirtualCardSolveTrapReqDTO.getTBankAccountNo(),cgbDirectVirtualCardSolveTrapReqDTO.getTxnId(),cgbDirectVirtualCardSolveTrapReqDTO.getCompanyAccountId(),cgbDirectVirtualCardSolveTrapReqDTO.getBankName(), null);
                    trapedAmount = trapedAmount + limitValue;
                    errorMsg.append(result.getErrorMsg()).append(";");
                    trapErrorAmountSum.add(result.getTrapErrorAmount() == null ? BigDecimal.ZERO : result.getTrapErrorAmount());
                }
            }
            FinhubLogger.info("解圈存失败信息errorMsg={},trapErrorAmountSum={}",errorMsg,trapErrorAmountSum);
            if (StringUtils.isNotBlank(errorMsg.toString()) && trapErrorAmountSum.compareTo(BigDecimal.ZERO) > 0) {
                StringBuilder stringBuilder = errorMsg.deleteCharAt(errorMsg.length() - 1);
                creditApplyManager.updateTrapStatusByApplyTransNo(cgbDirectVirtualCardSolveTrapReqDTO.getApplyTransNo(),stringBuilder.toString(),BankTrapStatus.FAIL,trapErrorAmountSum);
            }else {
                creditApplyManager.updateTrapStatusByApplyTransNo(cgbDirectVirtualCardSolveTrapReqDTO.getApplyTransNo(),"",BankTrapStatus.SUCCESS,BigDecimal.ZERO);
            }
            //更新虚拟卡企业流水
            saveAcctCompanyCardFlow4Trap(cgbDirectVirtualCardSolveTrapReqDTO.getApplyTransNo(),cgbDirectVirtualCardSolveTrapReqDTO.getTxnId(),cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount(),FundAcctCompanyCardOptType.FROZEN_BANK_REFUND);
            CgbDirectVirtualCardSolveTrapRespDTO cgbDirectVirtualCardSolveTrapRespDTO = new CgbDirectVirtualCardSolveTrapRespDTO();
            cgbDirectVirtualCardSolveTrapRespDTO.setSolveTrapStatus(trapErrorAmountSum.compareTo(BigDecimal.ZERO) >0 ? "99" : "90");
            cgbDirectVirtualCardSolveTrapRespDTO.setPayUserId(bankCard.getBankAcctId());
            cgbDirectVirtualCardSolveTrapRespDTO.setRecUserId(cgbTrapPayAccount);
            if ("test".equals(profile) ){
                cgbDirectVirtualCardSolveTrapRespDTO.setRecUserId(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
            }
            cgbDirectVirtualCardSolveTrapRespDTO.setTrapAmount(cgbDirectVirtualCardSolveTrapReqDTO.getSolveTrapAmount());
            cgbDirectVirtualCardSolveTrapRespDTO.setTxnTrapId(cgbDirectVirtualCardSolveTrapReqDTO.getTxnId());
            return cgbDirectVirtualCardSolveTrapRespDTO;
        } catch (FinhubException e) {
            dingDingMsgService.sendMsg("分贝通虚拟卡追加圈存异常,请处理"+cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo());
            FinhubLogger.error("【分贝通虚拟卡追加圈存一分钱异常】addTrap 参数：{}", JsonUtils.toJson(cgbDirectVirtualCardSolveTrapReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            dingDingMsgService.sendMsg("分贝通虚拟卡追加圈存异常,请处理"+cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo());
            FinhubLogger.error("【分贝通虚拟卡追加圈存一分钱异常】addTrap 参数：{}", JsonUtils.toJson(cgbDirectVirtualCardSolveTrapReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            dingDingMsgService.sendMsg("分贝通虚拟卡追加圈存异常,请处理"+cgbDirectVirtualCardSolveTrapReqDTO.getBankAccountNo());
            FinhubLogger.error("【分贝通虚拟卡追加圈存一分钱异常】addTrap 参数：{}", JsonUtils.toJson(cgbDirectVirtualCardSolveTrapReqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(),
                    EXCEPTION.getMsg());
        }
    }

    @Override
    public CgbDirectVirtualCardTrapRespDTO firstTrap4Upgrade(CgbDirectVirtualCardTrapReqDTO virtualCardTrapReqDTO) {
        FinhubLogger.info("圈存1分钱=={}", JsonUtils.toJson(virtualCardTrapReqDTO));
        try {
            ValidateUtils.validate(virtualCardTrapReqDTO);
            BankCardTrap bankCardTrapExisted = bankCardTrapManager.queryFirstTrap(virtualCardTrapReqDTO.getBankAccountNo(),"90");
            if (bankCardTrapExisted != null){
                CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
                cgbDirectVirtualCardTrapRespDTO.setTrapStatus(bankCardTrapExisted.getTrapStatus());
                return cgbDirectVirtualCardTrapRespDTO;
            }
            BankCard bankCard = queryCardByBankAccountNo(virtualCardTrapReqDTO.getBankAccountNo());
            //非绑定入金设置
            setAccountEntrySignHW(virtualCardTrapReqDTO.getTBankAccountNo(),"2");
            //查询1分钱圈存账户
            BankAcct trapAccount = bankAcctService.findByBankNameAndType(BankNameEnum.CGB.getCode(),BankAcctTypeEnum.TRAP_ACCOUNT.getCode());
            //调用广发发起首笔圈存
            CgbTrapRespDTO cgbTrapRespDTO = callBankInFirstTrap(virtualCardTrapReqDTO,bankCard.getEmployeeName(),trapAccount.getBankAccountNo());
            if (cgbTrapRespDTO.isSuccess()){
                //首次圈存记录转换
                BankCardTrap bankCardTrap = BankCardTrapConverter.covertToBankCardTrap4FirstTrap(virtualCardTrapReqDTO,cgbTrapRespDTO,bankCard);
                boolean saveBankCardTrapSuccess = bankCardTrapManager.saveFirstTrap(bankCardTrap);
                if (saveBankCardTrapSuccess){
                    BankCardTrapFlow bankCardTrapFlow = BankCardTrapFlowConverter.convert4FirstTrap(bankCard,virtualCardTrapReqDTO,cgbTrapRespDTO.getHoldNo(),trapAccount.getBankAccountNo());
                    bankCardTrapFlow.setPayBankAccountNo(cgbTrapPayAccount);
                    if ("test".equals(profile) || "dev".equals(profile) ){
                        bankCardTrapFlow.setPayBankAccountNo(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
                    }
                    bankCardTrapFlow.setPayBankAccountName(CgbTrapPayAccountEnum.getPayAccountNameByEnv(profile));
                    bankCardTrapFlow.setReceiveBankAccountNo(virtualCardTrapReqDTO.getBankAccountNo());
                    bankCardTrapFlow.setReceiveBankAccountName(bankCard.getEmployeeName());
                    iBankCardTrapFlowManager.saveFirstTrapFlow(bankCardTrapFlow);
                    CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
                    cgbDirectVirtualCardTrapRespDTO.setTrapStatus(cgbTrapRespDTO.getStatus());
                    return cgbDirectVirtualCardTrapRespDTO;
                }else {
                    throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_BANK_TRAP_SAVE_ERROR);
                }
            }else {
                CgbDirectVirtualCardTrapRespDTO cgbDirectVirtualCardTrapRespDTO = new CgbDirectVirtualCardTrapRespDTO();
                cgbDirectVirtualCardTrapRespDTO.setTrapStatus(cgbTrapRespDTO.getStatus());
                return cgbDirectVirtualCardTrapRespDTO;
            }
        } catch (FinhubException e) {
            dingDingMsgService.sendMsg(virtualCardTrapReqDTO.getBankAccountNo()+":开户一分钱圈存异常");
            FinhubLogger.error("【分贝通虚拟卡开户一分钱圈存异常】createAccount 参数：{}", JsonUtils.toJson(virtualCardTrapReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            dingDingMsgService.sendMsg(virtualCardTrapReqDTO.getBankAccountNo()+":开户一分钱圈存异常");
            FinhubLogger.error("【分贝通虚拟卡开户圈一分钱验证异常】createAccount 参数：{}", JsonUtils.toJson(virtualCardTrapReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            dingDingMsgService.sendMsg(virtualCardTrapReqDTO.getBankAccountNo()+":开户一分钱圈存异常");
            FinhubLogger.error("【分贝通虚拟卡开户圈一分钱系统异常】createAccount 参数：{}", JsonUtils.toJson(virtualCardTrapReqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(),
                    EXCEPTION.getMsg());
        }
    }


    /**
     * 查找当前企业生效的账户
     */
    private AcctCommonBaseDTO queryCompanyAccountOfVirtualCard(String companyId){
        try{
            AcctCommonBaseDTO acctCommonBaseDTO = uAcctCompanyCardService.getAcctCompanyCard(companyId,BankNameEnum.CGB.getCode());
            if(Objects.isNull(acctCommonBaseDTO)){
                FinhubLogger.error("追加圈存，未找到激活的企业虚拟卡子账户companyId={}", companyId);
                dingDingMsgService.sendMsg(companyId + "：企业虚拟卡账户未开启,请确认");
                throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
            }
            return acctCommonBaseDTO;
        } catch (FinhubException e){
            FinhubLogger.warn("追加圈存，获取激活的企业虚拟卡子账户异常companyId={}", companyId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e){
            FinhubLogger.error("追加圈存，获取激活的企业虚拟卡子账户异常companyId={}", companyId, e);
            throw FinhubExceptionUtil.exceptionFrom(EXCEPTION);
        }

    }

    public CgbSolveTrapDto solveTrapByLessThen2000(BankCard bankCard, AcctCommonBaseDTO accountSub4CompanyCard , String holdNo, String solveTrapAmountInOnce, BigDecimal solveAmount, String tBankAccountNo, String txnId, String companyAccountId,String bankName, BankCardTrap bankCardTrap) {
        String companyBankAcctId = accountSub4CompanyCard.getBankAcctId();
        CgbSolveTrapDto result = new CgbSolveTrapDto();
        CgbSloveTrapReqDTO solveTrapReqDTO = new CgbSloveTrapReqDTO();
        solveTrapReqDTO.setPayAccount(bankCard.getBankAcctId());
        //企业员工姓名需要查询实名
        solveTrapReqDTO.setPayAccountName(bankCard.getEmployeeName());
        solveTrapReqDTO.setRecAccount(CgbTrapPayAccountEnum.getPayAccountNoByEnv(profile));
        solveTrapReqDTO.setRecAccountName(CgbTrapPayAccountEnum.getPayAccountNameByEnv(profile));
        solveTrapReqDTO.setHoldNo(holdNo);
        if(BankNameEnum.isFbt(bankName)){
            solveTrapReqDTO.setCustAccno(companyAccountId);
        }else{
            solveTrapReqDTO.setCustAccno(companyBankAcctId);
        }
        solveTrapReqDTO.setPayAmount(solveTrapAmountInOnce);
        solveTrapReqDTO.setTxnId(IDGen.genId(IDGen.BANK_CARD_TRAP));
        String errorMsg = "";
        try {
            //非绑定入金设置
            setAccountEntrySignHW(tBankAccountNo,"2");
            FinhubLogger.info("solveTrapByLessThen2000,req:{}",JsonUtils.toJson(solveTrapReqDTO));
            CgbSloveTrapRespDTO cgbSloveTrapRespDTO = iCgbVirtualCardService.sloveTrap(solveTrapReqDTO);
            FinhubLogger.info("solveTrapByLessThen2000,req:{},resp:{}",JsonUtils.toJson(solveTrapReqDTO),JsonUtils.toJson(cgbSloveTrapRespDTO));
            if (cgbSloveTrapRespDTO!=null && StringUtils.isNotBlank(cgbSloveTrapRespDTO.getStatus())){
                BankCardTrapFlow bankCardTrapFlow = BankCardTrapFlowConverter.covert4SolveTrap(bankCard,solveTrapReqDTO,holdNo,solveAmount,txnId);
                iBankCardTrapFlowManager.saveAddTrapFlow(bankCardTrapFlow);
                errorMsg =  cgbSloveTrapRespDTO.getStatus().equals("99") ? cgbSloveTrapRespDTO.getErrorMsg() : "";
                //CGB虚拟卡入对账系统
                sendVirtualCardFlowChecking(bankCardTrapFlow, bankCard);
                if (Objects.nonNull(bankCardTrap) && BankCardTrapStatusEnum.isSuccess(cgbSloveTrapRespDTO.getStatus())){
                    bankCardTrapManager.reduceRemainAmount(bankCardTrap.getId(), solveTrapAmountInOnce, bankCardTrap.getTrapStatus());

                    //账户接口减额(解圈存)
                    BankCardAcctWithdrawalCreditReqDTO accReqDTO  = BankCardAcctWithdrawalCreditReqDTO.builder()
                            .bankAccountNo(bankCard.getBankAcctId()).bankName(BankNameEnum.CGB.getCode()).operationAmount(new BigDecimal(solveTrapAmountInOnce)).bizNo(bankCardTrap.getTrapNo())
                            .employeeId(bankCard.getEmployeeId()).companyId(bankCard.getCompanyId()).companyName(accountSub4CompanyCard.getCompanyName())
                            .operationUserId(bankCard.getEmployeeId()).operationUserName(bankCard.getEmployeeName()).bankApplyType(BankApplyType.CARD_REPAYMENT_WITHDRAWAL_CREDIT.getKey())
                            .build();
                    applyCardManager.withdrawalCardAcctBalance(accReqDTO);
                }
            }else {
                throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.BANK_CARD_BANK_TRAP_ERROR);
            }
        } catch (FinhubException e) {
            errorMsg = e.getMessage();
            FinhubLogger.error("【分贝通虚拟卡解圈存异常】solveTrapByLessThen2000 参数：{}", JsonUtils.toJson(solveTrapReqDTO), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            errorMsg = e.getMessage();
            FinhubLogger.error("【分贝通虚拟卡解圈存异常】solveTrapByLessThen2000 参数：{}", JsonUtils.toJson(solveTrapReqDTO), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            errorMsg = EXCEPTION.getMsg();
            FinhubLogger.error("【分贝通虚拟卡解圈存异常】solveTrapByLessThen2000 参数：{}", JsonUtils.toJson(solveTrapReqDTO), e);
            throw new FinhubException(EXCEPTION.getCode(), EXCEPTION.getType(),EXCEPTION.getMsg());
        }finally {
            FinhubLogger.info("解圈存信息：errorMsg={}，errorAmount={}", errorMsg, new BigDecimal(solveTrapAmountInOnce));
            if (StringUtils.isNotBlank(errorMsg)){
                result.setErrorMsg(errorMsg);
                result.setTrapErrorAmount(new BigDecimal(solveTrapAmountInOnce));
            }
            return result;
        }
    }

    /**
     * 非绑定入金设置
     * 只有开卡的时候需要代收
     * @param bankAccountNo
     * @param incomeFlag  入账标识,代收标识：空-充值,2-不充值
     * @return void
     */
    private void setAccountEntrySignHW(String bankAccountNo,String incomeFlag){
        CgbSetAccountEntrySignHWReqDTO cgbSetAccountEntrySignHWReqDTO = new CgbSetAccountEntrySignHWReqDTO();
        cgbSetAccountEntrySignHWReqDTO.setEAccountNo(bankAccountNo);
        // 生成 请求流水号 CCA
        String txnId = IDGen.genId(IDGen.SET_ACCT_ENTRY_SIGN_HW);
        cgbSetAccountEntrySignHWReqDTO.setChannelSeq(txnId);
        cgbSetAccountEntrySignHWReqDTO.setTransTime(DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
        cgbSetAccountEntrySignHWReqDTO.setTxnId(txnId);
        if (incomeFlag != null) {
            cgbSetAccountEntrySignHWReqDTO.setIncomeFlag(incomeFlag);
        }
        FinhubLogger.info("setAccountEntrySignHW req={}", JsonUtils.toJson(cgbSetAccountEntrySignHWReqDTO));
        CgbSetAcountEntrySignHWRespDTO signHWRespDTO = iCgbVirtualCardService.setAcountEntrySignHW(cgbSetAccountEntrySignHWReqDTO);
        FinhubLogger.info("setAccountEntrySignHW req={},resp={}", JsonUtils.toJson(cgbSetAccountEntrySignHWReqDTO), JsonUtils.toJson(signHWRespDTO));
        if (!signHWRespDTO.isResultSuccess()){
            dingDingMsgService.sendMsg(bankAccountNo+":setAccountEntrySignHW 设置失败,请处理,errorCode:"+signHWRespDTO.getErrorCode()+",errorMsg:"+signHWRespDTO.getErrorMsg());
            //throw FinhubExceptionUtil.exceptionFrom(BANK_CARD_FIRST_ENTRY_ERROR);
            throw new FinhubException(BANK_CARD_FIRST_ENTRY_ERROR.getCode(), BANK_CARD_FIRST_ENTRY_ERROR.getType(),(signHWRespDTO.getErrorCode()+"-"+signHWRespDTO.getErrorMsg()));
        }
    }


    @Override
    public CgbDirectVirtualCardTrapRespDTO keepActiveTrap(String txnId, BigDecimal trapAmount, String platformAccountNo, BankCardDTO bankCard, String tBankAccountNo) {
        BankCardTrap firstTrapRecord = bankCardTrapManager.queryFirstTrap(bankCard.getBankAccountNo(), "90");
        if (firstTrapRecord == null || StringUtils.isEmpty(firstTrapRecord.getTrapNo())) {
            FinhubLogger.error(bankCard.getBankAccountNo() + ": keepActiveTrap 未查到有效圈存记录");
            throw new FinPayException(GlobalResponseCode.BANK_CARD_FIRST_TRAP_NO_MISSING_ERROR);
        }
        String holdNo = firstTrapRecord.getTrapNo();
        CgbDirectVirtualCardTrapRespDTO respDTO = new CgbDirectVirtualCardTrapRespDTO();

        try {
            CgbTrapReqDTO trapReqDTO = new CgbTrapReqDTO();
            CgbTrapRespDTO cgbTrapRespDTO;
            trapReqDTO.setPayAccount(cgbTrapPayAccount);
            if ("test".equals(profile) || "dev".equals(profile)) {
                trapReqDTO.setPayAccount(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
            }
            trapReqDTO.setPayAccountName(CgbTrapPayAccountEnum.getPayAccountNameByEnv(profile));
            trapReqDTO.setRecAccount(bankCard.getBankAccountNo());
            trapReqDTO.setRecAccountName(bankCard.getEmployeeName());
            trapReqDTO.setType("02"); // 01：新增 02：追加
            trapReqDTO.setBusiScene("10"); //10-平台圈存1分钱；11-企业圈存；
            trapReqDTO.setCustAccno(platformAccountNo); //busiScene=10，上送分贝通1分钱账簿ID；busiScene=11，上送电子账户关联的企业账簿ID；
            trapReqDTO.setHoldNo(holdNo);
            trapReqDTO.setPayAmount(String.valueOf(trapAmount));
            trapReqDTO.setTxnId(txnId);
            setAccountEntrySignHW(tBankAccountNo, "2");
            FinhubLogger.info("keepActiveTrap request:{}", JsonUtils.toJson(trapReqDTO));
            cgbTrapRespDTO = iCgbVirtualCardService.trap(trapReqDTO);
            FinhubLogger.info("keepActiveTrap request:{},response:{}", JsonUtils.toJson(trapReqDTO), JsonUtils.toJson(cgbTrapRespDTO));

            if (cgbTrapRespDTO == null) {
                throw new RuntimeException(bankCard.getFbCardNo() + " iCgbVirtualCardService.trap() return unexpected result, null");
            }
            if (cgbTrapRespDTO.isSuccess() && "90".equals(cgbTrapRespDTO.getStatus())) {
                BankCard bankCard1 = new BankCard();
                BeanUtils.copyProperties(bankCard, bankCard1);
                BankCardTrapFlow bankCardTrapFlow = BankCardTrapFlowConverter.convert4AddTrap(trapReqDTO, bankCard1, txnId, trapAmount, holdNo, platformAccountNo);
                iBankCardTrapFlowManager.saveAddTrapFlow(bankCardTrapFlow);
                respDTO.setSuccess();
            } else if (cgbTrapRespDTO.isSuccess() && "99".equals(cgbTrapRespDTO.getStatus())) {
                FinhubLogger.error("{} keepActiveTrap failed, request:{}, response: {}", bankCard.getFbCardNo(), JsonUtils.toJson(trapReqDTO), JsonUtils.toJson(cgbTrapRespDTO));
                respDTO.setFailed();
                respDTO.setErrorMsg(cgbTrapRespDTO.getErrorCode() + ":" + cgbTrapRespDTO.getErrorMsg());
            } else {
                FinhubLogger.error("{} keepActiveTrap return unexpected result, request:{}, response: {}", bankCard.getFbCardNo(), JsonUtils.toJson(trapReqDTO), JsonUtils.toJson(cgbTrapRespDTO));
                respDTO.setException();
                respDTO.setErrorMsg(cgbTrapRespDTO.getErrorCode() + ":" + cgbTrapRespDTO.getErrorMsg());
            }
            respDTO.setTrapStatus(cgbTrapRespDTO.getStatus());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("{} keepActiveTrap exception, msg:{}", bankCard.getFbCardNo(), e.getMessage(), e);
            respDTO.setException();
            respDTO.setTrapStatus("null");
            respDTO.setErrorMsg(e.getMessage());
            return respDTO;
        }
    }


    @Override
    public CgbDirectVirtualCardSolveTrapRespDTO keepActiveSolveTrap(String txnId, BigDecimal solveTrapAmount, String platformAccountNo, BankCardDTO bankCard, String tBankAccountNo) {
        BankCardTrap firstTrapRecord = bankCardTrapManager.queryFirstTrap(bankCard.getBankAccountNo(), "90");
        if (firstTrapRecord == null || StringUtils.isEmpty(firstTrapRecord.getTrapNo())) {
            FinhubLogger.error(bankCard.getBankAccountNo() + ": keepActiveTrap 未查到有效圈存记录");
            throw new FinPayException(GlobalResponseCode.BANK_CARD_FIRST_TRAP_NO_MISSING_ERROR);
        }
        String holdNo = firstTrapRecord.getTrapNo();

        CgbDirectVirtualCardSolveTrapRespDTO respDTO = new CgbDirectVirtualCardSolveTrapRespDTO();
        CgbSloveTrapReqDTO solveTrapReqDTO = new CgbSloveTrapReqDTO();
        CgbSloveTrapRespDTO cgbSloveTrapRespDTO;
        try {
            solveTrapReqDTO.setPayAccount(bankCard.getBankAcctId());
            solveTrapReqDTO.setPayAccountName(bankCard.getEmployeeName());
            solveTrapReqDTO.setRecAccount(cgbTrapPayAccount);
            if ("test".equals(profile) || "dev".equals(profile)) {
                solveTrapReqDTO.setRecAccount(CgbTrapPayAccountEnum.FAT_YD01.getPayAccountNo());
            }
            solveTrapReqDTO.setRecAccountName(CgbTrapPayAccountEnum.getPayAccountNameByEnv(profile));
            solveTrapReqDTO.setHoldNo(holdNo);
            solveTrapReqDTO.setCustAccno(platformAccountNo);
            solveTrapReqDTO.setPayAmount(solveTrapAmount.toString());
            solveTrapReqDTO.setTxnId(txnId);
            //非绑定入金设置
            setAccountEntrySignHW(tBankAccountNo, "2");
            FinhubLogger.info("keepActiveSolveTrap, req:{}", JsonUtils.toJson(solveTrapReqDTO));
            cgbSloveTrapRespDTO = iCgbVirtualCardService.sloveTrap(solveTrapReqDTO);
            FinhubLogger.info("keepActiveSolveTrap, req:{}, resp:{}", JsonUtils.toJson(solveTrapReqDTO), JsonUtils.toJson(cgbSloveTrapRespDTO));

            if (cgbSloveTrapRespDTO == null) {
                throw new RuntimeException(bankCard.getFbCardNo() + " iCgbVirtualCardService.trap() return unexpected result, null");
            }
            if ("90".equals(cgbSloveTrapRespDTO.getStatus())) {
                BankCard bankCard1 = new BankCard();
                BeanUtils.copyProperties(bankCard, bankCard1);
                BankCardTrapFlow bankCardTrapFlow = BankCardTrapFlowConverter.covert4SolveTrap(bankCard1, solveTrapReqDTO, holdNo, solveTrapAmount, txnId);
                iBankCardTrapFlowManager.saveAddTrapFlow(bankCardTrapFlow);
                respDTO.setSuccess();
            } else if ("99".equals(cgbSloveTrapRespDTO.getStatus())) {
                FinhubLogger.error("{} keepActiveSolveTrap failed, request:{}, response: {}", bankCard.getFbCardNo(), JsonUtils.toJson(solveTrapReqDTO), JsonUtils.toJson(cgbSloveTrapRespDTO));
                respDTO.setErrorMsg(cgbSloveTrapRespDTO.getErrorCode() + ":" + cgbSloveTrapRespDTO.getErrorMsg());
                respDTO.setFailed();
            } else {
                FinhubLogger.error("{} keepActiveSolveTrap return unexpected result, request:{}, response: {}", bankCard.getFbCardNo(), JsonUtils.toJson(solveTrapReqDTO), JsonUtils.toJson(cgbSloveTrapRespDTO));
                respDTO.setErrorMsg(cgbSloveTrapRespDTO.getErrorCode() + ":" + cgbSloveTrapRespDTO.getErrorMsg());
                respDTO.setError();
            }
            respDTO.setSolveTrapStatus(cgbSloveTrapRespDTO.getStatus());
            return respDTO;

        } catch (Exception e) {
            FinhubLogger.error("{} keepActiveTrap exception, msg:{}", bankCard.getFbCardNo(), e.getMessage(), e);
            respDTO.setError();
            respDTO.setSolveTrapStatus("null");
            respDTO.setErrorMsg(e.getMessage());
            return respDTO;
        }
    }

    @Override
    public CgbTrapRetryQueryDTO queryTrapCanRetry(String trapTxnId) {
        BankCardTrap bankCardTrap = bankCardTrapManager.queryTrapByOriTxnId(trapTxnId,BankNameEnum.CGB.getCode(),TrapTypeEnum.ADD.getKey());
        if (bankCardTrap != null){
            CgbTrapRetryQueryDTO cgbTrapRetryQueryDTO = new CgbTrapRetryQueryDTO();
            BeanUtils.copyProperties(bankCardTrap, cgbTrapRetryQueryDTO);
            return cgbTrapRetryQueryDTO;
        }
        return null;
    }

    @Override
    public List<CgbTrapRetryQueryDTO> queryCgbFailTrap(TrapQueryRpcReqDto reqDto){
        List<BankCardTrap> bankCardTraps = bankCardTrapManager.queryCgbFailTrap(reqDto);
        if(CollectionUtils.isEmpty(bankCardTraps)){
            return null;
        }
        return CopyUtils.copyList(bankCardTraps, CgbTrapRetryQueryDTO.class);
    }

    @Override
    public boolean syncThirdCardNo(String bankAccountNo, String thirdCardNo) {
        return bankCardTrapManager.syncThirdCardNo(bankAccountNo,thirdCardNo);
    }
}
