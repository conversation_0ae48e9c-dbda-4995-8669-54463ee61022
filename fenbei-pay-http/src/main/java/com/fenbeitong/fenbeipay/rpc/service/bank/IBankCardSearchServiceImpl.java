package com.fenbeitong.fenbeipay.rpc.service.bank;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.BankUserCardSearchReqDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.ReimbursePersonCardRespDto;
import com.fenbeitong.acctperson.api.service.search.IBankUserCardSearchService;
import com.fenbeitong.acctperson.api.service.search.IReimbursePersonCardSearchService;
import com.fenbeitong.dech.api.enums.SpaBindBankEnum;
import com.fenbeitong.fenbeipay.api.base.ResponseInfoPage;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.BankUserCardInfoRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardRelatedApplyOrderDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.upgrade.BankCardUpgradeSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.upgrade.BankCardUpgradeSearchRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.*;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.bank.base.conver.BankCardConver;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardApplyFlowExtMapper;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardCreditApplyFlowMapper;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardMapper;
import com.fenbeitong.fenbeipay.bank.base.manager.IBankCardManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.*;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.fenbeipay.dto.bank.BankCardApplyFlow;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCreditApplyFlow;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FinhubMessageType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.saasplus.api.model.dto.apply.ApplyOrderVoucherResDTO;
import com.fenbeitong.saasplus.api.service.apply.IApplyOrderService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeStairOrgUnitDTO;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.ILLEGAL_ARGUMENT;

/**
 * @Title: IBankCardSearchServiceImpl
 * @ProjectName fenbei-pay
 * @Description: 公共查询 在这里实现
 */
@Service("iBankCardSearchService")
@Slf4j
public class IBankCardSearchServiceImpl implements IBankCardSearchService {

    @Autowired
    private SearchCardManager searchCardManager;
    @Autowired
    private CheckCardManager checkCardManager;

    @Autowired
    private ApplyCardManager applyCardManager;
    @Autowired
    private BankCardApplyFlowManger bankCardApplyFlowManger;

    @Autowired
    private IBaseEmployeeExtService iBaseEmployeeExtService;
    @Resource
    private IBankCardManager bankCardManager;

    @Resource
    private BankCardMapper bankCardMapper;

    @Autowired
    private BankCardApplyFlowExtMapper bankCardApplyFlowMapper;

    @Autowired
    private CreditDistributeManager creditDistributeManager;

    @Autowired
    private IReimbursePersonCardSearchService iReimbursePersonCardSearchService;

    @Autowired
    private IBankUserCardSearchService iBankUserCardSearchService;
    @Resource
    private IApplyOrderService iApplyOrderService;
    @Autowired
    public BankCardCreditApplyFlowMapper bankCardCreditApplyFlowMapper;

    @Override
    public BankSearchCardDetailRespDTO queryByBankAccountNo(String bankAccountNo) {
        log.info("IBankCardSearchServiceImpl->queryByBankAccountNo:bankAccountNo={}",bankAccountNo);
        if (ObjUtils.isBlank(bankAccountNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.queryByBankAccountNo(bankAccountNo);
    }

    @Override
    public BankSearchCardDetailRespDTO searchBankCard(BankSearchCardReqDTO searchBankCardDTO) {
        log.info("IBankCardSearchServiceImpl->searchBankCard:searchBankCard={}", JSONObject.toJSONString(searchBankCardDTO));
        searchBankCardDTO.checkReq();
        return searchCardManager.searchBankCardDetail(searchBankCardDTO);
    }

    @Override
    public BankSearchCardDetailRespDTO queryBankCard(String companyId, String employeeId, String bankAccountNo) {
        log.info("IBankCardSearchServiceImpl->queryBankCard:companyId={},employeeId={},bankAccountNo={}",companyId,employeeId,bankAccountNo);
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(bankAccountNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.searchBankCardDetail(BankSearchCardReqDTO.builder()
                .companyId(companyId).employeeId(employeeId).bankAccountNo(bankAccountNo)
                .build());
    }

    @Override
    public BankSearchCardDetailRespDTO queryBankCardByFbCardNo(String companyId, String employeeId, String fbCardNo) {
        log.info("IBankCardSearchServiceImpl->queryBankCardByFbCardNo:companyId={},employeeId={},fbCardNo={}",companyId,employeeId,fbCardNo);
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(fbCardNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.searchBankCardDetail(BankSearchCardReqDTO.builder()
                .companyId(companyId).employeeId(employeeId).fbCardNo(fbCardNo)
                .build());
    }

    @Override
    public BankSearchCardDetailRespDTO queryBankCardByFbCardNo(String companyId, String fbCardNo) {
        log.info("IBankCardSearchServiceImpl->queryBankCardByFbCardNo:companyId={},fbCardNo={}",companyId,fbCardNo);

        if (ObjUtils.isBlank(companyId)  || ObjUtils.isBlank(fbCardNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.searchBankCardDetail(BankSearchCardReqDTO.builder()
                .companyId(companyId).fbCardNo(fbCardNo)
                .build());
    }

    @Override
    public BankSearchCardDetailRespDTO queryByCompanyInfo(String companyId, String employeeId, String bankName) {
        log.info("IBankCardSearchServiceImpl->queryByCompanyInfo:companyId={},employeeId={},bankName={}",companyId,employeeId,bankName);
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(bankName)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.searchBankCardDetail(BankSearchCardReqDTO.builder()
                .companyId(companyId).employeeId(employeeId).bankName(bankName)
                .build());
    }

    @Override
    public BankSearchCardDetailRespDTO queryByEmployeeInfo(String employeeId, String bankAccountNo, String bankName) {
        if (ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(bankAccountNo) || ObjUtils.isBlank(bankName)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.searchBankCardDetail(BankSearchCardReqDTO.builder()
                .employeeId(employeeId).bankAccountNo(bankAccountNo).bankName(bankName)
                .build());
    }

    @Override
    public BankSearchCardDetailRespDTO queryByEmployeeAndBankAccountNo(String employeeId, String bankAccountNo) {
        if (ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(bankAccountNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.searchBankCardDetail(BankSearchCardReqDTO.builder()
                .employeeId(employeeId).bankAccountNo(bankAccountNo)
                .build());
    }

    @Override
    public BankSearchCardDetailRespDTO queryByEmployeeAndBankName(String employeeId, String bankName) {
        if (ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(bankName)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.searchBankCardDetail(BankSearchCardReqDTO.builder()
                .employeeId(employeeId).bankName(bankName)
                .build());
    }

    @Override
    public BankSearchCardDetailRespDTO queryByNameAndAccountNo(String bankName, String bankAccountNo) {
        if (ObjUtils.isBlank(bankName) || ObjUtils.isBlank(bankAccountNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.queryByNameAndAccountNo(bankName, bankAccountNo);
    }

    @Override
    public List<BankSearchCardDetailRespDTO> queryUnbindByPhone(String phone) {
        if (ObjUtils.isBlank(phone)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.queryUnbindByPhone(phone);
    }

    @Override
    public List<BankSearchCardDetailRespDTO> findByCompanyIdAndEmployeeId(String companyId, String employeeId) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.findByCompanyIdAndEmployeeId(companyId, employeeId);
    }

    @Override
    public List<BankSearchCardDetailRespDTO> findByBankName(String bankName, Integer page, Integer pageSize) {
        if (ObjUtils.isBlank(bankName)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.findByBankName(bankName, page, pageSize);
    }

    @Override
    public BankCardCostAttributionRespDTO queryCostAttribution(String applyTransNo) {
        if (ObjUtils.isBlank(applyTransNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.queryCostAttribution(applyTransNo);
    }

    @Override
    public BankSeraechCostAttributionResqDTO searchCostAttribution(String companyId, String employeeId, String bankName) {
        try {
            if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(bankName)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            //查询银行卡
            return searchCardManager.searchCostAttribution(companyId, employeeId, bankName);
        } catch (FinPayException e) {
            FinhubLogger.error("【查询费用归属】searchCostAttribution 参数：{}=={}=={}=={}", companyId,employeeId,bankName,e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【查询费用归属异常】searchCostAttribution 参数：{}=={}=={}=={}" ,companyId,employeeId,bankName,e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【查询费用归属系统异常】searchCostAttribution 参数：{}=={}=={}=={}", companyId,employeeId,bankName,e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public LinkedHashMap<Object, List<BankSearchApplyCreditResqDTO>> searchApplyCredit(BankSearchApplyCreditReqDTO applyCreditReqDTO) {
        ValidateUtils.validate(applyCreditReqDTO);
        return searchCardManager.searchApplyCredit(applyCreditReqDTO);
    }

    @Override
    public BankRefundCreditDetailRespDTO queryByApplyTransNo(String companyId, String employeeId, String applyTransNo) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(applyTransNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.queryByApplyTransNo(companyId, employeeId, applyTransNo);
    }

    @Override
    public BankPettySubDetailRespDTO queryBySubId(String subId) {
        if (ObjUtils.isBlank(subId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        BankPettySubDetailRespDTO bankPettySubDetailRespDTO = searchCardManager.queryBySubId(subId);
        return bankPettySubDetailRespDTO;
    }

    @Override
    public ResponsePage<BankCardSearchResqDTO> searchBankCardPage(BankCardSearchReqDTO bankCardSearchReqDTO) {
        ValidateUtils.validate(bankCardSearchReqDTO);
        return searchCardManager.searchBankCardPage(bankCardSearchReqDTO);
    }

    @Override
    public ResponsePage<BankCardStatisticalResqDTO> exportStatisticalBankCard(BankCardSearchReqDTO bankCardSearchReqDTO) {
        ValidateUtils.validate(bankCardSearchReqDTO);
        return searchCardManager.exportBankCardList(bankCardSearchReqDTO);
    }

    @Override
    public ResponsePage<BankCardApplySearchResqDTO> searchBankCardApplyPage(BankCardApplySearchReqDTO bankCardApplySearchReqDTO) {
        ValidateUtils.validate(bankCardApplySearchReqDTO);
        return bankCardApplyFlowManger.searchWebBankCardPage(bankCardApplySearchReqDTO);
    }

    @Override
    public List<ExportBankCardFlowResqDTO> exportWebBankCardFlowPage(BankCardSearchReqDTO bankCardSearchReqDTO) {
        ValidateUtils.validate(bankCardSearchReqDTO);
        List<String> fbCardNos = searchCardManager.exportBankCardPage(bankCardSearchReqDTO);
        if (CollectionUtils.isEmpty(fbCardNos)){
            return Lists.newArrayList();
        }
        return bankCardApplyFlowManger.exportWebBankCardFlowPage(fbCardNos,bankCardSearchReqDTO.getCompanyId(),bankCardSearchReqDTO.getOffset(),bankCardSearchReqDTO.getPageSize(),null,null);
    }
    @Override
    public List<ExportBankCardFlowResqDTO> exportWebBankCardFlowDataAuth(BankCardSearchReqDTO bankCardSearchReqDTO, Boolean departFlag, List<String> partDataIdList) {
        ValidateUtils.validate(bankCardSearchReqDTO);
        List<String> fbCardNos = searchCardManager.exportBankCardPageDataAuth(bankCardSearchReqDTO, departFlag, partDataIdList);
        if (CollectionUtils.isEmpty(fbCardNos)){
            return Lists.newArrayList();
        }
        return bankCardApplyFlowManger.exportWebBankCardFlowPage(fbCardNos,bankCardSearchReqDTO.getCompanyId(),bankCardSearchReqDTO.getOffset(),bankCardSearchReqDTO.getPageSize(),bankCardSearchReqDTO.getStartDate(),bankCardSearchReqDTO.getEndDate());
    }

    @Override
    public Map<String, Boolean> isDeleteEmployeeByBank(String companyId, String employeeId) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(employeeId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return checkCardManager.isDeleteEmployeeByBank(companyId, employeeId);
    }

    @Override
    public Map<String, Boolean> companyEmployeeBankCredit(String companyId, BankNameEnum bankAccountName) {
        List<BankNameEnum> bankNameEnums = Arrays.asList(BankNameEnum.CGB, BankNameEnum.XWBANK);
        if (!bankNameEnums.contains(bankAccountName)){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return checkCardManager.companyEmployeeBankCredit(companyId,bankAccountName.getCode());
    }

    @Override
    public ResponsePage<BankCardForStereoRespDTO> stereoQueryBankCardList(BankCardForStereoReqDTO bankCardForStereoReqDTO) {
        ResponsePage<BankCardForStereoRespDTO> bankCards = searchCardManager.stereoQueryBankCardList(bankCardForStereoReqDTO);
        return bankCards;
    }

    @Override
    public List<BankExportFlowForStereoRespDTO> exportStereoBankCardFlowPage(BankCardForStereoReqDTO bankCardForStereoReqDTO) {
        return bankCardApplyFlowManger.exportStereoBankCardFlowPage(bankCardForStereoReqDTO,bankCardForStereoReqDTO.getOffset(),bankCardForStereoReqDTO.getPageSize());
    }

    @Override
    public ResponsePage<BankCardFlowForStereoRespDTO> stereoQueryBankCardFlow(BankCardOperationForStereoReqDTO operationForStereoReqDTO) {
        if (operationForStereoReqDTO == null || ObjUtils.isBlank(operationForStereoReqDTO.getFbCardNo())) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        ResponsePage<BankCardFlowForStereoRespDTO> bankCardFlows = bankCardApplyFlowManger.stereoQueryBankCardFlow(operationForStereoReqDTO);
        return bankCardFlows;
    }

    @Override
    public ResponsePage<BankCardStatisticsForStereoRespDTO> stereoStatisticsBankCard(BankCardStatisticsForStereoReqDTO stereoReqDTO) {
    	if (Objects.isNull(stereoReqDTO) || ObjUtils.isBlank(stereoReqDTO.getCompanyId())){
    	    throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
    	}
        return bankCardApplyFlowManger.stereoStatisticsBankCard(stereoReqDTO);
    }

    @Override
    public void bankCardStairUnitName(BankCardStatisticsForStereoReqDTO accountSubVo) {
       List<BankCard> bankCardList= searchCardManager.findCardBank(accountSubVo);
       if (ObjUtils.isEmpty(bankCardList)){
           return;
       }
        for (BankCard bankCard:bankCardList) {
            if (ObjUtils.isEmpty(bankCard.getCompanyId())||ObjUtils.isEmpty(bankCard.getEmployeeId())){
                continue;
            }
            try {
                EmployeeStairOrgUnitDTO employeeStairOrgUnitDTO = iBaseEmployeeExtService.queryCompanyOrgUnitList(bankCard.getCompanyId(),bankCard.getEmployeeId());
                if (ObjUtils.isEmpty(employeeStairOrgUnitDTO)){
                    continue;
                }
                applyCardManager.updateEmployeeStairOrgUnit(bankCard.getBankAccountNo(),employeeStairOrgUnitDTO.getStairOrgUnitId(),employeeStairOrgUnitDTO.getStairOrgUnitName());
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

    }

    @Override
    public List<BankSearchCardDetailRespDTO> findByEmployeeId(String employeeId) {
        if (ObjUtils.isBlank(employeeId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.findByEmployeeId(employeeId);
    }

    @Override
    public BankCardDetailRespDTO queryBankCardDetailByBankAcctId(String bankAcctId, String bankName) {
        return searchCardManager.queryBankCardDetailByBankAcctId(bankAcctId, bankName);
    }

    @Override
    public BankCardDetailRespDTO queryBankCardDetail(String companyId, String fbCardNo) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(fbCardNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.queryCardDetail(companyId, fbCardNo);
    }

    @Override
    public BankCardDetailRespDTO queryBankCardDetail(String fbCardNo) {
        if (ObjUtils.isBlank(fbCardNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.queryCardDetail(fbCardNo);
    }

    @Override
    public List<String> findBalanceEmployeeByCompanyId(String companyId) {
        if (ObjUtils.isBlank(companyId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        return searchCardManager.findBalanceEmployeeByCompanyId(companyId);
    }

    @Override
    public List<BankCardDTO> selectBankCardByCompanyIdAndEmployId(String companyId, String employeeId) {
        if (StringUtils.isBlank(companyId) || StringUtils.isBlank(employeeId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        List<BankCard> bankCardList = searchCardManager.selectBankCardByCompanyIdAndEmployId(companyId, employeeId);
        if (CollectionUtils.isEmpty(bankCardList)) {
            return null;
        }
        return bankCardList.stream().map(BankCardConver::copyBankCard2BankCardDTO).collect(Collectors.toList());
    }

    @Override
    public BankCardDTO selectBankCardByBankUid(String bankUid) {
        if (StringUtils.isBlank(bankUid)) {
            throw new FinhubException(ILLEGAL_ARGUMENT.getCode(), FinhubMessageType.TIP_TOAST, ILLEGAL_ARGUMENT.getMsg());
        }
        BankCard bankCard = bankCardManager.selectBankCardByBankUid(bankUid);
        return BankCardConver.copyBankCard2BankCardDTO(bankCard);
    }

    @Override
    public BankCardDTO selectBankCardByBankAcctId(String bankAcctId) {
        BankCard bankCard = bankCardManager.selectBankCardByBankAcctId(bankAcctId);
        return BankCardConver.copyBankCard2BankCardDTO(bankCard);
    }

    @Override
    public List<BankCardStatusRespDTO> selectBankCardByEmployeeIds(String companyId, List<String> employeeIds) {
        if (org.apache.commons.lang3.StringUtils.isBlank(companyId) || CollectionUtils.isEmpty(employeeIds)) {
            throw new FinhubException(ILLEGAL_ARGUMENT.getCode(), FinhubMessageType.TIP_TOAST,
                    ILLEGAL_ARGUMENT.getMsg());
        }
        List<BankCardStatusRespDTO> res = new ArrayList<>();
        List<BankCard> list = searchCardManager.selectBankCardByEmployeeIds(companyId, employeeIds);
        for (BankCard bankCard : list) {
            BankCardStatusRespDTO obj = new BankCardStatusRespDTO();
            obj.setEmployeeId(bankCard.getEmployeeId());
            obj.setCardStatus(bankCard.getCardStatus());
            obj.setBankAccountNo(bankCard.getBankAccountNo());
            obj.setCreateTime(bankCard.getCreateTime());
            res.add(obj);
        }
        return res;
    }

    @Override
    public List<String> getVirtualCardCompanyIds() {
        return searchCardManager.getVirtualCardCompanyIds();
    }

    @Override
    public List<String> getCardUsersByCompanyId(String companyId) {
        return searchCardManager.getCardUsersByCompanyId(companyId);
    }

    @Override
    public List<BankCardSearchResqDTO> queryBankCardUsableAmount(BankCardSearchReqDTO bankCardSearchReqDTO) {
        ValidateUtils.validate(bankCardSearchReqDTO);
        return searchCardManager.queryBankCardUsableAmount(bankCardSearchReqDTO);
    }

    @Override
    public List<BankCardDTO> batchSelectBankCardByBankAcctId(String companyId, List<String> bankAcctIds) {
        if (StringUtils.isBlank(companyId) || CollectionUtils.isEmpty(bankAcctIds)) {
            throw new FinhubException(ILLEGAL_ARGUMENT.getCode(), FinhubMessageType.TIP_TOAST, ILLEGAL_ARGUMENT.getMsg());
        }
        if(bankAcctIds.size()>500){
            throw new FinhubException(ILLEGAL_ARGUMENT.getCode(), FinhubMessageType.TIP_TOAST, "批量查询超过500条最大限制!");
        }
        List<BankCard> bankCards = bankCardManager.batchSelectBankCardByBankUid(companyId,bankAcctIds);
        List<BankCardDTO> newList=Lists.newArrayList();
        for(BankCard card : bankCards){
            BankCardDTO cardDTO=BankCardConver.copyBankCard2BankCardDTO(card);
            newList.add(cardDTO);
        }
        return newList;
    }

    @Override
    public BankCardUpgradeSearchRespDTO query4CgbUpgrade(BankCardUpgradeSearchReqDTO bankCardUpgradeSearchReqDTO) {
        List<BankCard> bankCards =  bankCardManager.select4CgbUpgrade(bankCardUpgradeSearchReqDTO);
        Long maxId = bankCardManager.selectMaxId();
        List<BankCardDTO> newList=Lists.newArrayList();
        for(BankCard card : bankCards){
            BankCardDTO cardDTO=BankCardConver.copyBankCard2BankCardDTO(card);
            newList.add(cardDTO);
        }
        BankCardUpgradeSearchRespDTO bankCardUpgradeSearchRespDTO = new BankCardUpgradeSearchRespDTO();
        bankCardUpgradeSearchRespDTO.setBankCardDTOList(newList);
        bankCardUpgradeSearchRespDTO.setMaxId(maxId);
        return bankCardUpgradeSearchRespDTO;
    }

    @Override
    public List<BankSearchCardDetailRespDTO> queryVirtualCardList(BankCardBalanceCheckDTO bankCardBalanceCheckDTO) {
        List<BankCard> bankCardList = bankCardMapper.selectByExample(getExample(bankCardBalanceCheckDTO));
        return bankCardList.stream().map(bankCard -> BankCardConver.searchBankCardDetail(bankCard)).collect(Collectors.toList());
    }

    @Override
    public ResponsePage<BankSearchCardDetailRespDTO> queryPageVirtualCardList(BankCardBalanceCheckDTO bankCardBalanceCheckDTO) {
        ResponsePage<BankSearchCardDetailRespDTO> respPage = new ResponsePage<>();
        int count = bankCardMapper.selectCountByExample(getExample(bankCardBalanceCheckDTO));
        respPage.setTotalCount(count);
        if(count > 0){
            PageHelper.startPage(bankCardBalanceCheckDTO.getPageNo(), bankCardBalanceCheckDTO.getPageSize());
            List<BankSearchCardDetailRespDTO> bankSearchCardDetailRespDTOList = queryVirtualCardList(bankCardBalanceCheckDTO);
            respPage.setDataList(bankSearchCardDetailRespDTOList);
        }
        return respPage;
    }

    @Override
    public BankCardFlowStatsRespDTO statsBankCardFlowRepaySumAmt(BankCardFlowStatsReqDTO statsReqDTO) {
        BankCardFlowStatsRespDTO statsRespDTO = new BankCardFlowStatsRespDTO();
        if(!StringUtils.isBlank(statsReqDTO.getEmployeeId())){
            Example example = new Example(BankCardApplyFlow.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("employeeId", statsReqDTO.getEmployeeId());
            if(!StringUtils.isBlank(statsReqDTO.getBankAccountNo())){
                criteria.andEqualTo("bankAccountNo", statsReqDTO.getBankAccountNo());
            }
            if(statsReqDTO.getCreateTimeFrom() != null){
                criteria.andGreaterThanOrEqualTo("createTime", statsReqDTO.getCreateTimeFrom());
            }
            if(statsReqDTO.getCreateTimeTo() != null){
                criteria.andLessThanOrEqualTo("createTime", statsReqDTO.getCreateTimeTo());
            }
            criteria.andIn("operationType", Arrays.asList(BankApplyType.CARD_REPAYMENT.getKey(), BankApplyType.CARD_REPAYMENT_REFUND.getKey()));
            List<BankCardApplyFlow> bankCardApplyFlows = bankCardApplyFlowMapper.selectByExample(example);
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(bankCardApplyFlows)){
                //统计还款总金额
                BigDecimal repaySum = bankCardApplyFlows.stream().filter(item -> BankApplyType.CARD_REPAYMENT.getKey() == item.getOperationType()).map(BankCardApplyFlow::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                //统计还款退回总金额
                BigDecimal repayRefundSum = bankCardApplyFlows.stream().filter(item -> BankApplyType.CARD_REPAYMENT_REFUND.getKey() == item.getOperationType()).map(BankCardApplyFlow::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                statsRespDTO.setRepaySum(repaySum);
                statsRespDTO.setRepayRefundSum(repayRefundSum);
            }
        }
        return statsRespDTO;
    }

    @Override
    public RepaymentPayAcctRespDTO getRepaymentPayAcct(String employeeId, String bankAccountNo) {
        if (ObjUtils.isBlank(employeeId) || ObjUtils.isBlank(bankAccountNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        BankSearchCardReqDTO bankSearchCardReqDTO = BankSearchCardReqDTO.builder()
                .employeeId(employeeId).bankAccountNo(bankAccountNo)
                .build();
        BankSearchCardDetailRespDTO bankSearchCardDetailRespDTO = searchCardManager.searchBankCardDetail(bankSearchCardReqDTO);
        RepaymentPayAcctRespDTO repaymentPayAcctRespDTO = new RepaymentPayAcctRespDTO();
        if (bankSearchCardDetailRespDTO == null) {
            return repaymentPayAcctRespDTO;
        }

        if (BankNameEnum.isZBBank(bankSearchCardDetailRespDTO.getBankName())) {
            BankUserCardSearchReqDTO userCardSearchReqDTO = new BankUserCardSearchReqDTO();
            userCardSearchReqDTO.setBankName(bankSearchCardDetailRespDTO.getBankName());
            userCardSearchReqDTO.setCompanyId(bankSearchCardDetailRespDTO.getCompanyId());
            userCardSearchReqDTO.setEmployeeId(employeeId);
            BankUserCardInfoRespDTO bankUserCardInfoRespDTO = new BankUserCardInfoRespDTO();
            BeanUtils.copyProperties(iBankUserCardSearchService.queryBankUserCardInfo(userCardSearchReqDTO), bankUserCardInfoRespDTO);
            repaymentPayAcctRespDTO.setBankUserCardInfo(bankUserCardInfoRespDTO);
        } else if (BankNameEnum.isSpa(bankSearchCardDetailRespDTO.getBankName())) {
            ReimbursePersonCardRespDto personCardRespDto = iReimbursePersonCardSearchService.queryPersonCardBySecondAcctCardNoAndType(bankSearchCardDetailRespDTO.getBankAccountNo(), 20);
            if (personCardRespDto == null) {
                throw new FinPayException(GlobalResponseCode.EXCEPTION);
            }
            BankAcctInfoRespDTO bankAcctInfoRespDTO = new BankAcctInfoRespDTO();
            String bankCardNo = personCardRespDto.getBankCardNo();
            bankAcctInfoRespDTO.setBankName(personCardRespDto.getBankCode());
            bankAcctInfoRespDTO.setBankDesc(personCardRespDto.getBankShowName() + "(" + bankCardNo.substring(bankCardNo.length() - 4) + ")");
            bankAcctInfoRespDTO.setBankAccountNo(bankCardNo);
            SpaBindBankEnum spaBindBank = SpaBindBankEnum.findByValue(personCardRespDto.getBankCode());
            if (spaBindBank != null) {
                bankAcctInfoRespDTO.setBankIcon(spaBindBank.getBankIconForApply());
            }
            repaymentPayAcctRespDTO.setBankAcctInfo(bankAcctInfoRespDTO);
        }
        return repaymentPayAcctRespDTO;
    }

    @Override
    public ResponseInfoPage<BankCardCreditDistributeRespDTO> queryCreditDistributePageByAccountNo(BankCardCreditDistributeReqDTO queryReq) {
        BankCardCreditDistributeReqDTO bankCardCreditDistributeReqDTO = new BankCardCreditDistributeReqDTO();
        BeanUtils.copyProperties(queryReq, bankCardCreditDistributeReqDTO);
        return creditDistributeManager.queryCreditDistributePageByAccountNo(bankCardCreditDistributeReqDTO);
    }

    @Override
    public ResponsePage<String> hasActiveCardUserPage(HasActiveCardUserReqDTO reqDTO) {
        FinhubLogger.info("获取有激活卡的用户，reqDTO={}", JsonUtils.toJson(reqDTO));
        if (StringUtils.isBlank(reqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }

        return searchCardManager.hasActiveCardUserPage(reqDTO);
    }

    private Example getExample(BankCardBalanceCheckDTO bankCardBalanceCheckDTO){
        Example example = new Example(BankCard.class);
        Example.Criteria criteria = example.createCriteria();
        //只获取广发银行数据.
        if(!StringUtils.isBlank(bankCardBalanceCheckDTO.getBankName())){
            criteria.andEqualTo("bankName", bankCardBalanceCheckDTO.getBankName());
        }
        if(bankCardBalanceCheckDTO.getCreateTimeFrom() != null){
            criteria.andGreaterThanOrEqualTo("createTime", bankCardBalanceCheckDTO.getCreateTimeFrom());
        }
        if(bankCardBalanceCheckDTO.getCreateTimeTo() != null){
            criteria.andLessThanOrEqualTo("createTime", bankCardBalanceCheckDTO.getCreateTimeTo());
        }
        if(bankCardBalanceCheckDTO.getUpdateTimeFrom() != null){
            criteria.andGreaterThanOrEqualTo("updateTime", bankCardBalanceCheckDTO.getUpdateTimeFrom());
        }
        if(bankCardBalanceCheckDTO.getUpdateTimeTo() != null){
            criteria.andLessThanOrEqualTo("updateTime", bankCardBalanceCheckDTO.getUpdateTimeTo());
        }
        if(bankCardBalanceCheckDTO.getDirectAcctType() != null){
            criteria.andEqualTo("directAcctType", bankCardBalanceCheckDTO.getDirectAcctType());
        }
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(bankCardBalanceCheckDTO.getExcludeFbCardNoList())){
            criteria.andNotIn("fbCardNo", bankCardBalanceCheckDTO.getExcludeFbCardNoList());
        }
        if(!StringUtils.isBlank(bankCardBalanceCheckDTO.getCondition())){
            criteria.andCondition(bankCardBalanceCheckDTO.getCondition());
        }
        return example;
    }

    @Override
    public BankCardHoldersRespDTO getCardUsersByNameAndEmployeeIds(String companyId, String name,
                                                                   List<String> employeeIds, Integer pageNum,
                                                                   Integer pageSize, List<String> excludeUsers) {
        if(org.apache.commons.lang3.StringUtils.isBlank(companyId) || pageNum == null || pageSize == null){
            throw new FinhubException(ILLEGAL_ARGUMENT.getCode(), FinhubMessageType.TIP_TOAST,
                    ILLEGAL_ARGUMENT.getMsg());
        }
        return searchCardManager.getCardUsersByNameAndEmployeeIds(companyId, name, employeeIds, pageNum, pageSize, excludeUsers);
    }

    @Override
    public BankCardDTO queryByFbCardNo(String fbCardNo) {
        if (ObjUtils.isBlank(fbCardNo)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        BankCard bankCard = searchCardManager.queryByfbCardNo(fbCardNo);
        return BankCardConver.copyBankCard2BankCardDTO(bankCard);

    }

    @Override
    public List<BankCardRelatedApplyOrderDTO> queryDetailList(String applyCreditFlowId) {
        BankCardApplyFlow currentApplyFlow = bankCardApplyFlowMapper.selectByPrimaryKey(applyCreditFlowId);
        if (currentApplyFlow == null || !BankApplyType.isRefundOperation(currentApplyFlow.getOperationType())) {
            return new ArrayList<>();
        }
        if (!StringUtils.isBlank(currentApplyFlow.getPettyId())) {
            return pettyRelatedApplyFlow(currentApplyFlow);
        } else {
            return vcAmountRelatedApplyFlow(currentApplyFlow);
        }
    }

    @Override
    public BankCardUpgradeSearchRespDTO select4ZBBankUpgrade(String companyId, String employeeId) {
        List<BankCardDTO> newList=Lists.newArrayList();
        List<BankCard> bankCards = bankCardManager.select4ZBBankUpgrade(companyId,employeeId);
        FinhubLogger.info("需要额度退回的卡:{}",JsonUtils.toJson(bankCards));
        for(BankCard card : bankCards){
            BankCardDTO cardDTO=BankCardConver.copyBankCard2BankCardDTO(card);
            newList.add(cardDTO);
        }
        BankCardUpgradeSearchRespDTO bankCardUpgradeSearchRespDTO = new BankCardUpgradeSearchRespDTO();
        bankCardUpgradeSearchRespDTO.setBankCardDTOList(newList);
        bankCardUpgradeSearchRespDTO.setMaxId(0L);
        return bankCardUpgradeSearchRespDTO;
    }


    /**
     * 退备用金关联的申请单
     *
     * @param currentApplyFlow
     * @return
     */
    private List<BankCardRelatedApplyOrderDTO> pettyRelatedApplyFlow(BankCardApplyFlow currentApplyFlow) {
        List<BankCardRelatedApplyOrderDTO> relatedApplyOrderDTOS = new ArrayList<>();
        // 查询退款对应的备用金申请流
        BankCardApplyFlow applyFlow = bankCardApplyFlowManger.getApplyFlowByRefundFlow(currentApplyFlow);
        if (applyFlow == null) {
            return relatedApplyOrderDTOS;
        }
        // 填充关联的备用金申请单信息
        BankCardRelatedApplyOrderDTO relatedApplyOrderDTO = buildRelatedApplyOrder(currentApplyFlow, applyFlow);

        List<String> bizIdList = Arrays.asList(applyFlow.getBizNo());
        List<ApplyOrderVoucherResDTO> applyList = iApplyOrderService.getMeaningNoById(bizIdList);
        if (applyList != null) {
            ApplyOrderVoucherResDTO applyOrderVoucherResDTO = applyList.get(0);
            relatedApplyOrderDTO.setMeaningNo(applyOrderVoucherResDTO.getMeaningNo());
            relatedApplyOrderDTO.setApplyReason(applyOrderVoucherResDTO.getApplyReason());
            if (applyOrderVoucherResDTO.getFormId() != null) {
                relatedApplyOrderDTO.setIsVirtualCustomForm(true);
            }
        }
        relatedApplyOrderDTOS.add(relatedApplyOrderDTO);
        return relatedApplyOrderDTOS;
    }


    /**
     * 虚拟卡额度退款关联的申请单
     *
     * @param currentApplyFlow
     * @return
     */
    private List<BankCardRelatedApplyOrderDTO> vcAmountRelatedApplyFlow(BankCardApplyFlow currentApplyFlow) {
        List<BankCardRelatedApplyOrderDTO> relatedApplyOrderDTOS = new ArrayList<>();
        List<BankCardCreditApplyFlow> bankCardCreditApplyFlowList = selectBankCardCreditApplyFlow(currentApplyFlow);
        if (CollectionUtils.isEmpty(bankCardCreditApplyFlowList)) {
            return relatedApplyOrderDTOS;
        }

        List<String> bizIdList = bankCardCreditApplyFlowList.stream().filter(res -> org.apache.commons.lang3.StringUtils.isNotBlank(res.getBizNo())).map(BankCardCreditApplyFlow::getBizNo).distinct().collect(Collectors.toList());
        List<ApplyOrderVoucherResDTO> applyList = iApplyOrderService.getMeaningNoById(bizIdList);
        Map<String, ApplyOrderVoucherResDTO> bizIdMap = applyList.stream().collect(Collectors.toMap(ApplyOrderVoucherResDTO::getId, Function.identity()));

        for (BankCardCreditApplyFlow bankCardCreditApplyFlow : bankCardCreditApplyFlowList) {
            BankCardApplyFlow bankCardApplyFlow = bankCardApplyFlowManger.getApplyFlowByBizNo(bankCardCreditApplyFlow.getBizNo());
            BankCardRelatedApplyOrderDTO relatedApplyOrderDTO = buildRelatedApplyOrder(bankCardCreditApplyFlow, bizIdMap.get(bankCardCreditApplyFlow.getBizNo()));
            if (bankCardApplyFlow != null) {
                relatedApplyOrderDTO.setCostAttributionName(bankCardApplyFlow.getCostAttributionName());
            }
            relatedApplyOrderDTOS.add(relatedApplyOrderDTO);
        }
        return relatedApplyOrderDTOS;
    }

    public static BankCardRelatedApplyOrderDTO buildRelatedApplyOrder(BankCardApplyFlow currentFlow, BankCardApplyFlow applyFlow) {
        // 填充关联的备用金申请单信息
        BankCardRelatedApplyOrderDTO relatedApplyOrder = new BankCardRelatedApplyOrderDTO();
        relatedApplyOrder.setApplyId(applyFlow.getBizNo());
        relatedApplyOrder.setCostAttributionName(applyFlow.getCostAttributionName());
        relatedApplyOrder.setApplyTime(getFormatDate(applyFlow.getCreateTime(), "yyyy/MM/dd HH:mm:ss"));

        relatedApplyOrder.setApplyAmount(applyFlow.getOperationAmount());
        relatedApplyOrder.setUseBalance(currentFlow.getOperationAmount());
        return relatedApplyOrder;
    }

    public static String getFormatDate(Date date, String format) {
        if(date == null){
            return "";
        }
        if(org.springframework.util.StringUtils.isEmpty(format)){
            format = "yyyy/MM/dd HH:mm:ss";
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(date);
    }

    /**
     * 查询虚拟卡额度退款对应的申请流
     *
     * @param currentApplyFlow
     * @return
     */
    private List<BankCardCreditApplyFlow> selectBankCardCreditApplyFlow(BankCardApplyFlow currentApplyFlow) {
        if (currentApplyFlow == null || org.apache.commons.lang3.StringUtils.isEmpty(currentApplyFlow.getBizNo())) {
            return null;
        }
        Example example = new Example(BankCardCreditApplyFlow.class);
        example.createCriteria().andEqualTo("operaId", currentApplyFlow.getBizNo());
        return bankCardCreditApplyFlowMapper.selectByExample(example);
    }



    /**
     *
     * @param applyFlow 业务侧申请flow
     * @param applyOrderVoucherResDTO 申请单
     * @return
     */
    public static BankCardRelatedApplyOrderDTO buildRelatedApplyOrder(BankCardCreditApplyFlow applyFlow, ApplyOrderVoucherResDTO applyOrderVoucherResDTO) {
        // 填充关联的备用金申请单信息
        BankCardRelatedApplyOrderDTO relatedApplyOrder = new BankCardRelatedApplyOrderDTO();
        relatedApplyOrder.setApplyId(applyFlow.getBizNo());
        relatedApplyOrder.setApplyTime(getFormatDate(applyFlow.getCreateTime(), "yyyy/MM/dd HH:mm:ss"));
        relatedApplyOrder.setApplyAmount(applyFlow.getApplyAmount());
        relatedApplyOrder.setUseBalance(applyFlow.getDeductionAmount());
        if (applyOrderVoucherResDTO != null) {
            relatedApplyOrder.setMeaningNo(applyOrderVoucherResDTO.getMeaningNo());
            relatedApplyOrder.setApplyReason(applyOrderVoucherResDTO.getApplyReason());
            if(!StringUtils.isBlank(applyOrderVoucherResDTO.getFormId())){
                relatedApplyOrder.setIsVirtualCustomForm(true);
            }
        }
        return relatedApplyOrder;
    }
}
