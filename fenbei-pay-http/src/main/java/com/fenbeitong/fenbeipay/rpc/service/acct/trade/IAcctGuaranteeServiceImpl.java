package com.fenbeitong.fenbeipay.rpc.service.acct.trade;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.api.enums.CustomerTypeEnum;
import com.fenbeitong.dech.api.model.dto.BankTradeReqDto;
import com.fenbeitong.dech.api.model.dto.BankTradeRespDto;
import com.fenbeitong.dech.api.model.dto.BankTransferReqDto;
import com.fenbeitong.dech.api.service.IBankTradeService;
import com.fenbeitong.fenbeipay.core.utils.BankConfigUtil;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenChangeType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctTargetBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctGuaranteeRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOperationRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByAcctTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersOperationFlowRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctGuaranteeService;
import com.fenbeitong.fenbeipay.api.service.voucher.base.IVouchersSearchService;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.dto.freezen.FreezenOperationReqDTO;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezen;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezenFlow;
import com.fenbeitong.fenbeipay.nf.service.FundFreezenFlowService;
import com.fenbeitong.fenbeipay.rpc.service.acct.IAcctAbstractService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.*;
import static java.math.BigDecimal.ROUND_HALF_DOWN;


/**
 * TODO 担保账户（个人账户）
 */
@Service("iAcctGuaranteeService")
public class IAcctGuaranteeServiceImpl extends IAcctAbstractService implements IAcctGuaranteeService {

    @Autowired
    private IBankTradeService iBankTradeService;
    @Autowired
    private IVouchersSearchService iVouchersSearchService;
    @Autowired
    private FundFreezenFlowService fundFreezenFlowService;
    @Autowired
    private DingDingMsgService dingDingMsgService;
    
    /**
     * 强制解锁时间设置
     */
    private static final long LOCK_TIME_REFUND = 10000L;

    /**
     * 等待时间
     **/
    private static final long WAIT_TIME_REFUND = 0L;

    private static final String BANK_TRANSFER_ERROR="【新账户系统异常】调用银行转账异常";

    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public AcctOperationRespDTO frozen4Voucher(AcctFreezeReqDTO acctFreezeReqDTO) throws FinAccountNoEnoughException {
        try {
            FinhubLogger.info("分贝券发放，冻结参数:{}", JsonUtils.toJson(acctFreezeReqDTO));
            String companyId = acctFreezeReqDTO.getCompanyId();
            Integer accountSubType = acctFreezeReqDTO.getAccountSubType();
            BigDecimal operationAmount = acctFreezeReqDTO.getOperationAmount();
            if (ObjUtils.isBlank(companyId) || ObjUtils.isNull(accountSubType) || ObjUtils.isNull(operationAmount)) {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            //查询当前生效账户
            AcctCommonBaseDTO currentAvailableAccount = acctCompanyGatewayService.findActCommonByAcctType(new AcctComGwByAcctTypeReqDTO(companyId, accountSubType));
            if (currentAvailableAccount == null) {
                dingDingMsgService.sendMsg("【分贝券发放个人账户查询失败】:参数:" + JsonUtils.toJson(acctFreezeReqDTO));
                FinhubLogger.error("【分贝券发放个人账户查询失败】:请求参数：{}", JSON.toJSONString(acctFreezeReqDTO));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_INDIVIDUAL_NOT_EXIST);
            }
            BigDecimal balance = currentAvailableAccount.getBalance();
            if (ObjUtils.isNull(balance) || balance.compareTo(operationAmount) < 0) {
                throw new FinAccountNoEnoughException(GlobalResponseCode.ACCOUNT_SUB_INDIVIDUAL_NOT_ENOUGH.getCode(), GlobalResponseCode.ACCOUNT_SUB_INDIVIDUAL_NOT_ENOUGH.getMsg(), GlobalResponseCode.ACCOUNT_SUB_INDIVIDUAL_NOT_ENOUGH.getType());
            }
            String bankName = currentAvailableAccount.getBankName();
            String bankAccountNo = currentAvailableAccount.getBankAccountNo();
            //查询 平台担保账户
            AcctGuaranteeRespDTO guaranteeRespDTO = queryGuaranteeBankAcct(companyId, bankName, bankAccountNo);
            if (ObjUtils.isNull(guaranteeRespDTO)) {
                dingDingMsgService.sendMsg("【分贝券发放担保户查询失败】:参数:" + JsonUtils.toJson(acctFreezeReqDTO));
                FinhubLogger.error("【分贝券发放担保户查询失败】:请求参数：{}", JSON.toJSONString(acctFreezeReqDTO));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GUARANTEE_BANK_ERROR);
            }
            //查询冻结账户，不存在创建新的冻结账户
            FundFreezen fundFreezen = queryFundFreezen(currentAvailableAccount, guaranteeRespDTO);
            //维护与银行对应关系，提前生成冻结池（担保账户）流水id
            String freezenFlowId = IDGen.genFreezenFlowId(companyId);
            acctFreezeReqDTO.setFreezenFlowId(freezenFlowId);
            //减少个人账户+记录流水
            AcctOperationRespDTO frozenAccountRespDTO = reduceAccount4VoucherGrant(acctFreezeReqDTO, currentAvailableAccount, guaranteeRespDTO, freezenFlowId);
            //增加担保（冻结）金额+记录流水  冻结池
            addFrozen4VoucherGrant(acctFreezeReqDTO, currentAvailableAccount, fundFreezen, freezenFlowId, guaranteeRespDTO);
            //增加担保账户金额+记录流水
            addGuaranteeAccount4VoucherGrant(acctFreezeReqDTO, currentAvailableAccount, guaranteeRespDTO.getAccountId());
            AcctOperationRespDTO operationRespDTO = new AcctOperationRespDTO(currentAvailableAccount.getAccountId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), currentAvailableAccount.getAccountModel(), frozenAccountRespDTO.getAccountFlowId());
            //银行转账
            //调用银行转账接口 从公司账户转入担保账户
            if (BankConfigUtil.needCallBank(bankName, companyId)) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        // 事务提交完毕时，触发
                        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryByFreezenFlowId(freezenFlowId);
                        callBankTransfer(fundFreezenFlow, currentAvailableAccount.getBankAcctId(), guaranteeRespDTO.getGuaranteeBankAcctId(), guaranteeRespDTO.getCompanyMainName(), "5");
                        /**
                         * 发送对账消息，从【callBankTransfer】迁出，与流水保持一致。事务提交后
                         * QX 2021-12-10 FBT-9264
                         */
                        sendAutoAcctCheckingMsg(freezenFlowId);
                    }
                });
                operationRespDTO.setSyncBankStatus(FundAcctSyncBankStatus.UN_SYNC.getCode());
            } else {
                operationRespDTO.setSyncBankStatus(FundAcctSyncBankStatus.NO_SYNC.getCode());
            }
            operationRespDTO.setBankName(bankName);
            operationRespDTO.setBankAccountNo(bankAccountNo);
            return operationRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】frozen 参数：{}", acctFreezeReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】frozen 参数：{}", acctFreezeReqDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】frozen 参数：{}", acctFreezeReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】frozen 参数：{}", acctFreezeReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public AcctOperationRespDTO freezenRelease4Voucher(AcctFreezenReleaseReqDTO freezenReleaseReqDTO) throws FinAccountNoEnoughException {
        try {
            ValidateUtils.validate(freezenReleaseReqDTO);
            String companyId = freezenReleaseReqDTO.getCompanyId();
            String accountId = freezenReleaseReqDTO.getAccountId();
            String voucherGrantTaskId = freezenReleaseReqDTO.getBizNo();
            FundFreezen fundFreezen = uFundFreezenService.queryByAccountIdAndUseType(accountId, FreezenUseType.INDIVIDUAL_VOUCHERS);
            if (ObjUtils.isNull(fundFreezen)) {
                throw new FinPayException(FREEZEN_BUDGET_NOT_EXIST);
            }
            //查询发放时冻结流水
            FundFreezenFlow fundFreezenFlow = uFundFreezenService.queryFreezenByVoucherGrantTaskId(accountId, voucherGrantTaskId, FreezenChangeType.FREEZING.getKey());
            if (ObjUtils.isNull(fundFreezenFlow)) {
                throw new FinPayException(FREEZEN_BUDGET_UNFREEZING_NOT_EXIST_FREEZING);
            }
            String freezenFlowId = fundFreezenFlow.getFreezenFlowId();
            String bankName = fundFreezen.getBankName();
            String bankAccountNo = fundFreezen.getBankAccountNo();
            Integer accountModel = fundFreezen.getAccountModel();
            //查询 担保账户
            AcctGuaranteeRespDTO guaranteeRespDTO = queryGuaranteeBankAcct(companyId, bankName, bankAccountNo);
            if (ObjUtils.isNull(guaranteeRespDTO)) {
                dingDingMsgService.sendMsg("【分贝券回收担保户查询失败】:参数:" + JsonUtils.toJson(freezenReleaseReqDTO));
                FinhubLogger.error("【分贝券回收】:请求参数：{}", JSON.toJSONString(freezenReleaseReqDTO));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GUARANTEE_BANK_ERROR);
            }
            //维护与银行对应关系，提前生成冻结池（担保账户）流水id
            String freezenReleaseFlowId = IDGen.genFreezenFlowId(companyId);
            String accountFlowId = null;
            String receiveAccountNo = null;
            String receiveAccountName = null;
            /*
              先票分贝券回收,资金是由担保户 -> 收款户
              路径只能是6: 营销子账簿→分贝通自营账簿
              因此先票回收，不应该从6 转换为9
             */
            String freezeFlagLocal = "6";
            if (freezenReleaseReqDTO.isReleaseToFbt()) {
                BankAcct bankAcctReceipt = uBankAcctService.findBankAcct(bankName, BankAcctTypeEnum.RECEIPT_BANK_ACCOUNT.getCode(), freezenReleaseReqDTO.getCompanyId(), freezenReleaseReqDTO.getOrderType(), 2);
                if (ObjUtils.isNull(bankAcctReceipt)) {
                    dingDingMsgService.sendMsg("【分贝券回收-解冻担保账户到收款户,收款户查询失败】:参数:" + JsonUtils.toJson(freezenReleaseReqDTO));
                    FinhubLogger.error("【分贝券回收-解冻担保账户到收款户,收款户查询失败】:请求参数：{}", JSON.toJSONString(freezenReleaseReqDTO));
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_RECEIPT_BANK_ERROR);
                }
                AcctTargetBaseDTO targetBaseDTO = new AcctTargetBaseDTO(bankAcctReceipt.getCompanyId(), bankAcctReceipt.getCompanyMainId(), bankAcctReceipt.getBankName(), bankAcctReceipt.getBankAcctId(), bankAcctReceipt.getBankAccountNo());
                targetBaseDTO.setTargetAccountId(bankAcctReceipt.getAccountId());
                //减扣冻结池
                reduceFrozen4VoucherRecovery(freezenReleaseReqDTO, fundFreezen, freezenReleaseFlowId, targetBaseDTO, guaranteeRespDTO, FreezenChangeType.UNFREEZING_2_FBT);
                //扣减担保账户
                reduceGuarantee4VoucherRecovery(freezenReleaseReqDTO, guaranteeRespDTO, targetBaseDTO, freezenReleaseFlowId, freezenFlowId);
                //增加平台收款账户的余额+记录收款账户流水
                AcctOperationRespDTO addReceiptRespDTO = addReceiptAccount4VoucherRecovery(freezenReleaseReqDTO, guaranteeRespDTO, bankAcctReceipt.getAccountId(), freezenReleaseFlowId, freezenFlowId);
                accountFlowId = addReceiptRespDTO.getAccountFlowId();
                receiveAccountNo = bankAcctReceipt.getBankAcctId();
                receiveAccountName = bankAcctReceipt.getCompanyMainName();
                if (BankNameEnum.isCgb(bankName)){
                    //对应 dech 的 CgbFreezeFlagEnum枚举
                    freezeFlagLocal = "7";
                }
            } else {
                AcctCommonBaseDTO acctCommonBaseDTO = acctCompanyGatewayService.findCommonByAccountId(accountId);
                if (ObjUtils.isNull(acctCommonBaseDTO)) {
                    dingDingMsgService.sendMsg("【分贝券回收-解冻担保账户到个人账户,个人账户查询失败】:参数:" + JsonUtils.toJson(freezenReleaseReqDTO));
                    FinhubLogger.error("【分贝券回收-解冻担保账户到个人账户,个人账户查询失败】:请求参数：{}", JSON.toJSONString(freezenReleaseReqDTO));
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_INDIVIDUAL_NOT_EXIST);
                }
                String individualBankAcctId = acctCommonBaseDTO.getBankAcctId();
                String companyMainId = acctCommonBaseDTO.getCompanyMainId();
                AcctTargetBaseDTO targetBaseDTO = new AcctTargetBaseDTO(acctCommonBaseDTO.getCompanyId(), companyMainId, acctCommonBaseDTO.getBankName(), individualBankAcctId, acctCommonBaseDTO.getBankAccountNo());
                targetBaseDTO.setTargetAccountSubType(acctCommonBaseDTO.getAccountSubType());
                targetBaseDTO.setTargetAccountModel(accountModel);
                targetBaseDTO.setTargetAccountId(acctCommonBaseDTO.getAccountId());
                //减扣冻结池+记录流水
                reduceFrozen4VoucherRecovery(freezenReleaseReqDTO, fundFreezen, freezenReleaseFlowId, targetBaseDTO, guaranteeRespDTO, FreezenChangeType.UNFREEZING);
                //扣减担保账户+记录流水
                reduceGuarantee4VoucherRecovery(freezenReleaseReqDTO, guaranteeRespDTO, targetBaseDTO, freezenReleaseFlowId, freezenFlowId);
                //增加个人账户余额+记录流水
                AcctOperationRespDTO addAccountRespDTO = addAccount4VoucherRecovery(freezenReleaseReqDTO, guaranteeRespDTO, accountModel);
                accountFlowId = addAccountRespDTO.getAccountFlowId();
                receiveAccountNo = individualBankAcctId;
                AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.findAcctCompanyMain(companyId, companyMainId, bankName);
                receiveAccountName = ObjUtils.isNull(acctCompanyMain) ? "未知" : ObjUtils.isBlank(acctCompanyMain.getBankBusinessName()) ? acctCompanyMain.getBusinessName() : acctCompanyMain.getBankBusinessName();
            }
            AcctOperationRespDTO operationRespDTO = new AcctOperationRespDTO(accountId, FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountModel, accountFlowId);
            operationRespDTO.setBankAccountNo(bankAccountNo);
            //银行转账
            //调用银行转账接口 从担保账户转入公司账户
            String finalReceiveAccountNo = receiveAccountNo;
            String finalReceiveAccountName = receiveAccountName;
            String freezeFlag = freezeFlagLocal;
            if (BankConfigUtil.needCallBank(bankName, companyId)) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        // 事务提交完毕时，触发
                        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryByFreezenFlowId(freezenReleaseFlowId);
                        callBankTransfer(fundFreezenFlow, guaranteeRespDTO.getGuaranteeBankAcctId(), finalReceiveAccountNo, finalReceiveAccountName, freezeFlag);
                        /*
                          发送对账消息，从【callBankTransfer】迁出，与流水保持一致。事务提交后
                          QX 2021-12-10 FBT-9264
                         */
                        //上账发送消息--自动对账
                        sendAutoAcctCheckingMsg(freezenReleaseFlowId);
                    }
                });
                operationRespDTO.setSyncBankStatus(FundAcctSyncBankStatus.UN_SYNC.getCode());
            } else {
                operationRespDTO.setSyncBankStatus(FundAcctSyncBankStatus.NO_SYNC.getCode());
            }
            return operationRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】unfreeze 参数：{}账户余额不足", freezenReleaseReqDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】unfreeze 参数：{}", freezenReleaseReqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public AcctOperationRespDTO freezenConsume4Voucher(AcctFreezenConsumeReqDTO consumeFreezeDTO) throws FinAccountNoEnoughException {
        try {
            String companyId = consumeFreezeDTO.getCompanyId();
            String bankName = consumeFreezeDTO.getBankName();
            // 资金发票流一致-查询发票主体对应到该主体的收款账户  --jiawei.li
            BankAcct receiptBankAcct = uBankAcctService.findBankAcct(bankName, BankAcctTypeEnum.RECEIPT_BANK_ACCOUNT.getCode(), consumeFreezeDTO.getCompanyId(), consumeFreezeDTO.getOrderType(), AccountTypeEnum.PERSONAL_TYPE.getCode());
            if (Objects.isNull(receiptBankAcct)) {
                dingDingMsgService.sendMsg("【分贝券消费-担保账户冻结金额转入收款账户,收款户查询失败】:参数:" + JsonUtils.toJson(consumeFreezeDTO));
                FinhubLogger.error("【分贝券消费-担保账户冻结金额转入收款账户,收款户查询失败】:请求参数：{}", JSON.toJSONString(consumeFreezeDTO));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GUARANTEE_BANK_ERROR);
            }
            AcctGuaranteeRespDTO guaranteeRespDTO = queryGuaranteeBankAcct(companyId, bankName, consumeFreezeDTO.getBankAccountNo());
            if (Objects.isNull(guaranteeRespDTO)) {
                dingDingMsgService.sendMsg("【分贝券消费-担保账户冻结金额转入收款账户,担保账户查询失败】:参数:" + JsonUtils.toJson(consumeFreezeDTO));
                FinhubLogger.error("【分贝券消费-担保账户冻结金额转入收款账户,担保账户查询失败】:请求参数：{}", JSON.toJSONString(consumeFreezeDTO));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GUARANTEE_BANK_ERROR);
            }
            //减少冻结池金额+记录流水
            AcctOperationRespDTO reduceFrozenRespDTO = reduceFrozenAccount4VoucherConsumer(consumeFreezeDTO, receiptBankAcct, guaranteeRespDTO);
            String frozenAccountFlowId = reduceFrozenRespDTO.getAccountFlowId();
            //减少担保账户+记录流水
            reduceGuaranteeAccount4VoucherConsumer(consumeFreezeDTO, guaranteeRespDTO, receiptBankAcct, frozenAccountFlowId);
            //增加平台收款账户的余额+记录收款账户流水
            addReceiptAccount4VoucherConsumer(consumeFreezeDTO, guaranteeRespDTO, receiptBankAcct, frozenAccountFlowId);
            // 消费退款异步处理上账逻辑
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    // 事务提交完毕时，触发
                    CompletableFuture.runAsync(() -> {
                        consumeSyncBank(frozenAccountFlowId, consumeFreezeDTO.getOrderSnapshot(), receiptBankAcct.getBankAcctId(), receiptBankAcct.getCompanyMainName());
                    });
                }
            });
            return reduceFrozenRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】consumeFreezen 参数：{}", consumeFreezeDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】consumeFreezen 参数：{}", consumeFreezeDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】consumeFreezen 参数：{}", consumeFreezeDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】consumeFreezen 参数：{}", consumeFreezeDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    @Override
    public AcctOperationRespDTO freezenRefund4Voucher(AcctFreezenRefundReqDTO refundFreezeDTO) throws FinAccountNoEnoughException {
        try {
            String bankName = refundFreezeDTO.getBankName();
            String companyId = refundFreezeDTO.getCompanyId();
            String accountId = refundFreezeDTO.getAccountId();
            //查询消费流水
            //2411-2506因为历史担保户未上账，退款先不检查消费流水，跑半年再恢复。
            FundFreezenFlow fundFreezenFlow = uFundFreezenService.queryFlowByVoucherIdAndBizNo(accountId, refundFreezeDTO.getVoucherId(), refundFreezeDTO.getBizNo(), FreezenChangeType.PAY.getKey());
            String consumerFlowId="unknown-push";
            if (ObjUtils.isNull(fundFreezenFlow)) {
                //历史数据发钉钉警告.
                dingDingMsgService.sendMsg("【分贝券退款-冻结池正向消费流水查询失败】:订单号:" + refundFreezeDTO.getBizNo());
                FinhubLogger.error("【分贝券退款-冻结池正向消费流水查询失败】:请求参数:{}", JSON.toJSONString(refundFreezeDTO));
                //throw new FinPayException(FREEZEN_BUDGET_REFUND_ERROR);
            }else{
                consumerFlowId = fundFreezenFlow.getFreezenFlowId();
            }

            // 资金发票流一致-查询发票主体对应到该主体的收款账户  --jiawei.li
            BankAcct receiptBankAcct = uBankAcctService.findBankAcct(bankName, BankAcctTypeEnum.RECEIPT_BANK_ACCOUNT.getCode(), refundFreezeDTO.getCompanyId(), refundFreezeDTO.getOrderType(), 2);
            if (Objects.isNull(receiptBankAcct)) {
                dingDingMsgService.sendMsg("【分贝券退款-收款账户金额转入担保账户冻结,收款户查询失败】:参数:" + JsonUtils.toJson(refundFreezeDTO));
                FinhubLogger.error("【分贝券退款-收款账户金额转入担保账户冻结,收款户查询失败】:请求参数：{}", JSON.toJSONString(refundFreezeDTO));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GUARANTEE_BANK_ERROR);
            }
            AcctGuaranteeRespDTO guaranteeRespDTO = queryGuaranteeBankAcct(companyId, bankName, refundFreezeDTO.getBankAccountNo());
            if (Objects.isNull(guaranteeRespDTO)) {
                dingDingMsgService.sendMsg("【分贝券退款-收款账户金额转入担保账户冻结,担保账户查询失败】:参数:" + JsonUtils.toJson(refundFreezeDTO));
                FinhubLogger.error("【分贝券退款-收款账户金额转入担保账户冻结,担保账户查询失败】:请求参数：{}", JSON.toJSONString(refundFreezeDTO));
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GUARANTEE_BANK_ERROR);
            }
            //增加冻结池金额+记录流水
            AcctOperationRespDTO operationRespDTO = addFrozenAccount4VoucherRefund(refundFreezeDTO, receiptBankAcct, guaranteeRespDTO);
            String frozenRefundFlowId = operationRespDTO.getAccountFlowId();
            //减少平台收款账户的余额+记录收款账户流水
            reduceReceiptAccount4VoucherRefund(refundFreezeDTO, guaranteeRespDTO, receiptBankAcct, frozenRefundFlowId, consumerFlowId);
            //增加担保账户+记录流水
            addGuaranteeAccount4VoucherRefund(refundFreezeDTO, guaranteeRespDTO, receiptBankAcct, frozenRefundFlowId, consumerFlowId);
            // 消费退款异步处理上账逻辑
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    // 事务提交完毕时，触发
                    CompletableFuture.runAsync(() -> {
                        refundSyncBank(frozenRefundFlowId, refundFreezeDTO.getOrderSnapshot(), receiptBankAcct.getBankAcctId(), guaranteeRespDTO.getCompanyMainName());
                    });
                }
            });
            return operationRespDTO;
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】refundFreezen 参数：{}", refundFreezeDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error("【新账户系统异常】refundFreezen 参数：{}", refundFreezeDTO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】refundFreezen 参数：{}", refundFreezeDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】refundFreezen 参数：{}", refundFreezeDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public AcctGuaranteeRespDTO queryGuaranteeBankAcct(String companyId, String bankCode, String bankAccountNo) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(bankCode)) {
            throw new FinPayException(ILLEGAL_ARGUMENT);
        }
        AcctGuaranteeRespDTO guaranteeRespDTO = new AcctGuaranteeRespDTO();
        //平台担保
        if (BankNameEnum.guaranteeByPlatform(bankCode)) {
            BankAcct bankAcctGuarantee = uBankAcctService.findBankAcct(bankCode, BankAcctTypeEnum.GUARANTEE_BANK_ACCOUNT.getCode(), companyId, null, 2);

            if (ObjUtils.isNull(bankAcctGuarantee)) {
                return null;
            }
            guaranteeRespDTO.setAccountId(bankAcctGuarantee.getAccountId());
            guaranteeRespDTO.setCompanyId(bankAcctGuarantee.getCompanyId());
            guaranteeRespDTO.setCompanyMainId(bankAcctGuarantee.getCompanyMainId());
            guaranteeRespDTO.setCompanyMainName(bankAcctGuarantee.getCompanyMainName());
            guaranteeRespDTO.setGuaranteeBankAccountNo(bankAcctGuarantee.getBankAccountNo());
            guaranteeRespDTO.setGuaranteeBankAcctId(bankAcctGuarantee.getBankAcctId());
            guaranteeRespDTO.setGuaranteeBankName(bankAcctGuarantee.getBankName());
            return guaranteeRespDTO;
        }
        //TODO 企业担保 当前只有分贝通体系
        if (BankNameEnum.guaranteeByCompany(bankCode) && BankNameEnum.isFbt(bankCode)) {
            guaranteeRespDTO.setAccountId(companyId);
            guaranteeRespDTO.setCompanyId(companyId);
            guaranteeRespDTO.setGuaranteeBankAccountNo(companyId);
            guaranteeRespDTO.setGuaranteeBankAcctId(companyId);
            guaranteeRespDTO.setGuaranteeBankName(bankCode);
            AccountGeneral accountGeneral = uAcctGeneralService.findByCompanyIdAndBank(companyId, bankCode, bankAccountNo);
            if (!ObjUtils.isNull(accountGeneral)) {
                guaranteeRespDTO.setCompanyMainId(accountGeneral.getCompanyMainId());
                guaranteeRespDTO.setCompanyMainName(accountGeneral.getCompanyMainName());
            }
        }
        return guaranteeRespDTO;
    }

    @Override
    public void asyncCallBank() {
        long startTimeMillis = System.currentTimeMillis();
        int totalCount = uFundFreezenService.queryNeedCallBankFlowCount();
        FinhubLogger.info("【Stopwatch】queryNeedCallBankFlowCount查询耗时->{}", (System.currentTimeMillis() - startTimeMillis));
        if (totalCount <= 0) {
            return;
        }
        Integer offset = 0;
        Integer pageSize = 200;
        int pageNo = totalCount / pageSize + 1;
        for (int i = 0; i < pageNo; i++) {
            startTimeMillis = System.currentTimeMillis();
            List<FundFreezenFlow> fundFreezenFlows = uFundFreezenService.queryNeedCallBankFlow(offset, pageSize);
            FinhubLogger.info("【Stopwatch】queryNeedCallBankFlow查询耗时->{}", (System.currentTimeMillis() - startTimeMillis));
            if (ObjUtils.isEmpty(fundFreezenFlows)) {
                return;
            }
            Set<String> accountCompanyMainIdSet = new HashSet<>();
            List<String> fbOrderIds = new ArrayList<>();
            fundFreezenFlows.forEach(flow -> {
                accountCompanyMainIdSet.add(flow.getCompanyMainId());
                accountCompanyMainIdSet.add(flow.getGuaranteeCompanyMainId());
                accountCompanyMainIdSet.add(flow.getTargetCompanyMainId());
                fbOrderIds.add(flow.getBizNo());
            });
            List<AcctCompanyMain> companyMains = uAcctCompanyMainService.findByMainIds(accountCompanyMainIdSet);
            Map<String, String> mainAcctMap = new HashMap<>();
            companyMains.forEach(acct -> {
                mainAcctMap.put(acct.getCompanyMainId(), ObjUtils.isBlank(acct.getBankBusinessName()) ? acct.getBusinessName() : acct.getBankBusinessName());
            });
            List<VouchersOperationFlowRespRPCDTO> voucherConsumeFlows = iVouchersSearchService.queryVoucherConsumeFlowByFbOrderIds(fbOrderIds);
            Map<String, String> voucherFlowMap = new HashMap<>();
            voucherConsumeFlows.forEach(flow -> {
                voucherFlowMap.put(flow.getFbOrderId(), flow.getOrderSnapshot());
            });
            for (FundFreezenFlow fundFreezenFlow : fundFreezenFlows) {
                try {
                    //银行上账
                    timeJobCallBank(fundFreezenFlow, voucherFlowMap, mainAcctMap);
                } catch (Exception e) {
                    FinhubLogger.error("【银行上账】上账异常，freezenFlowId:{}", fundFreezenFlow.getFreezenFlowId(), e);
                }
            }
        }
    }

    @Override
    public void updateCallBack(Long id,Integer callbackNum) {
        uFundFreezenService.updateCallBackById(id,callbackNum);

    }

    private void timeJobCallBank(FundFreezenFlow fundFreezenFlow, Map<String, String> voucherFlowMap, Map<String, String> mainAcctMap) {
        Integer operationType = fundFreezenFlow.getOperationType();
        String freezenFlowId = fundFreezenFlow.getFreezenFlowId();
        String bizNo = fundFreezenFlow.getBizNo();
        String orderSnapshot = voucherFlowMap.get(bizNo);
        if (FreezenChangeType.isPay(operationType)) {
            String acctCompanyMainName = mainAcctMap.get(fundFreezenFlow.getTargetCompanyMainId());
            if (ObjUtils.isBlank(acctCompanyMainName)) {
                FinhubLogger.error("【银行上账】收款账户主体为空，freezenFlowId:{}", freezenFlowId);
                return;
            }
            consumeSyncBank(freezenFlowId, orderSnapshot, fundFreezenFlow.getTargetBankAcctId(), acctCompanyMainName);
            return;
        }
        if (FreezenChangeType.isRefund(operationType)) {
            String acctCompanyMainName = mainAcctMap.get(fundFreezenFlow.getGuaranteeCompanyMainId());
            if (ObjUtils.isNull(acctCompanyMainName)) {
                FinhubLogger.error("【银行上账】收款账户主体为空，freezenFlowId:{}", freezenFlowId);
                return;
            }
            refundSyncBank(freezenFlowId, orderSnapshot, fundFreezenFlow.getTargetBankAcctId(), acctCompanyMainName);
            return;
        }
        if (FreezenChangeType.isFreezing(operationType)) {
            String acctCompanyMainName = mainAcctMap.get(fundFreezenFlow.getGuaranteeCompanyMainId());
            if (ObjUtils.isNull(acctCompanyMainName)) {
                FinhubLogger.error("【银行上账】收款账户主体为空，freezenFlowId:{}", freezenFlowId);
                return;
            }
            callBankTransfer(fundFreezenFlow, fundFreezenFlow.getBankAcctId(), fundFreezenFlow.getGuaranteeBankAcctId(), acctCompanyMainName, "5");
            return;
        }
        if (FreezenChangeType.isUnfreezing(operationType)) {
            String acctCompanyMainName = mainAcctMap.get(fundFreezenFlow.getTargetCompanyMainId());
            if (ObjUtils.isNull(acctCompanyMainName)) {
                FinhubLogger.error("【银行上账】收款账户主体为空，freezenFlowId:{}", freezenFlowId);
                return;
            }
            callBankTransfer(fundFreezenFlow, fundFreezenFlow.getGuaranteeBankAcctId(), fundFreezenFlow.getBankAcctId(), acctCompanyMainName, "6");
            return;
        }
        if (FreezenChangeType.isUnfreezing2Fbt(operationType)) {
            String acctCompanyMainName = mainAcctMap.get(fundFreezenFlow.getTargetCompanyMainId());
            if (ObjUtils.isNull(acctCompanyMainName)) {
                FinhubLogger.error("【银行上账】收款账户主体为空，freezenFlowId:{}", freezenFlowId);
                return;
            }
            String freezeFlagLocal = "6";
            if (BankNameEnum.isCgb(fundFreezenFlow.getBankName())){
                //对应 dech 的 CgbFreezeFlagEnum枚举
                freezeFlagLocal = "7";
            }
            callBankTransfer(fundFreezenFlow, fundFreezenFlow.getGuaranteeBankAcctId(), fundFreezenFlow.getTargetBankAcctId(), acctCompanyMainName, freezeFlagLocal);
        }
    }

    private void updateCallBankNum(FundFreezenFlow fundFreezenFlow) {
        uFundFreezenService.updateCallBackById(fundFreezenFlow.getId(),fundFreezenFlow.getCallbackNum());
        //发送钉钉提醒
        if (fundFreezenFlow.getCallbackNum() >= 10) {
            FinhubLogger.error("【fenbei-pay】冻结池资金流水上账超过10次，不再自动上账，需手动处理,资金流水id：{}", fundFreezenFlow.getFreezenFlowId());
            String msgError = "【fenbei-pay】冻结池资金流水上账超过10次，不再自动上账，需手动处理,资金流水id :" + fundFreezenFlow.getFreezenFlowId();
            dingDingMsgService.sendMsg(msgError);
        }
    }

    /**
     * Description: 分贝券消费，同步银行上账
     **/
    public void consumeSyncBank(String consumeFlowId, String orderSnapshot, String receiveAccountNo, String receiveAccountName) {
        FinhubLogger.info("【新账户系统】调用银行转账,流水id:{}", consumeFlowId);
        //需要增加分布式锁，获取到锁，继续执行，否则结束
        String lockKey = MessageFormat.format(RedisKeyConstant.ACCOUNT_FLOW_SYNC_BANK_KEY, consumeFlowId);
        try {
            boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                return;
            }
        } catch (InterruptedException e) {
            FinhubLogger.error("【新账户系统异常】调用银行转账，尝试加锁异常", e);
        }
        try {
            FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryByFreezenFlowId(consumeFlowId);
            if (ObjUtils.isNull(fundFreezenFlow)) {
                return;
            }
            //更新上账次数
            updateCallBankNum(fundFreezenFlow);
            String bankName = fundFreezenFlow.getBankName();
            if (!BankConfigUtil.needCallBank(bankName, fundFreezenFlow.getCompanyId())) {
                return;
            }

            BigDecimal operationAmount = fundFreezenFlow.getOperationAmount().setScale(0, ROUND_HALF_DOWN).abs();
            BankTradeReqDto bankTradeReqDto = new BankTradeReqDto();
            bankTradeReqDto.setBankName(bankName);
            bankTradeReqDto.setOperationAmount(operationAmount);
            bankTradeReqDto.setOperationUserId(fundFreezenFlow.getOperationUserId());
            bankTradeReqDto.setTxnId(consumeFlowId);
            bankTradeReqDto.setPayAccountNo(fundFreezenFlow.getGuaranteeBankAcctId());
            bankTradeReqDto.setReceiveAccountNo(receiveAccountNo);
            bankTradeReqDto.setCompanyId(fundFreezenFlow.getCompanyId());
            bankTradeReqDto.setSyncTrade(true);
            bankTradeReqDto.setProductName(orderSnapshot);
            bankTradeReqDto.setAccountModel(fundFreezenFlow.getAccountModel());
            bankTradeReqDto.setAccountSubType(fundFreezenFlow.getAccountSubType());
            bankTradeReqDto.setAccountFlowId(consumeFlowId);
            bankTradeReqDto.setReceiveAccountName(receiveAccountName);

            /*
              分贝券消费，都设置为个人
              QX 2021-12-13 FBT-9351
             */
            bankTradeReqDto.setCustomerType(CustomerTypeEnum.PERSONAL.getType());
            if (BankNameEnum.isLfBank(bankName)) {
                /*
                  廊坊银行-商品名称-个人消费传入订单信息
                 */
                bankTradeReqDto.setGoods(orderSnapshot);
                /*
                  廊坊银行-用途-消费传入场景类型
                 */
                bankTradeReqDto.setFunds(orderSnapshot);
                /*
                  廊坊银行-用途-消费传入发放券企业账号
                 */
                bankTradeReqDto.setMarketUserId(Long.valueOf(fundFreezenFlow.getBankAcctId()));
            }
            FinhubLogger.info("iBankTradeService.bankConsume,参数:{}", JsonUtils.toJson(bankTradeReqDto));
            BankTradeRespDto bankTradeRespDto = iBankTradeService.bankConsume(bankTradeReqDto);
            FinhubLogger.info("iBankTradeService.bankConsume,返回:{}", JsonUtils.toJson(bankTradeRespDto));
            //上账发送消息--自动对账
            // 发送消息，移到方法外侧
        } catch (Exception ex) {
            FinhubLogger.error(BANK_TRANSFER_ERROR, ex);
        } finally {
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                FinhubLogger.warn("【分贝券消费上账】redis解锁异常", e);
            }
        }
    }

    /**
     * 分贝券退款，同步银行上账
     **/
    public void refundSyncBank(String refundFlowId, String orderSnapshot, String payAccountNo, String receiveAccountName) {
        //需要增加分布式锁，获取到锁，继续执行，否则结束
        String lockKey = MessageFormat.format(RedisKeyConstant.ACCOUNT_FLOW_SYNC_BANK_KEY, refundFlowId);
        try {
            boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                return;
            }
        } catch (InterruptedException e) {
            FinhubLogger.error("【新账户系统异常】调用银行转账，尝试加锁异常", e);
        }
        try {
            FundFreezenFlow refundFlow = fundFreezenFlowService.queryByFreezenFlowId(refundFlowId);
            if (ObjUtils.isNull(refundFlow) ) {
                return;
            }
            //更新上账次数
            updateCallBankNum(refundFlow);
            FundFreezenFlow consumeFlow = fundFreezenFlowService.querySuccessFlowByVoucherIdAndBizNo(refundFlow.getAccountId(), refundFlow.getVoucherId(), refundFlow.getBizNo(), FreezenChangeType.PAY.getKey());
            if (ObjUtils.isNull(consumeFlow)) {
                FinhubLogger.error("【新账户系统异常】调用银行转账异常,未找到正向流水,refundFlowId:{}", refundFlowId);
                return;
            }
            String bankName = refundFlow.getBankName();
            if (!BankConfigUtil.needCallBank(bankName, refundFlow.getCompanyId())) {
                return;
            }
            BigDecimal operationAmount = refundFlow.getOperationAmount().setScale(0, ROUND_HALF_DOWN).abs();
            BankTradeReqDto bankTradeReqDto = new BankTradeReqDto();
            bankTradeReqDto.setBankName(bankName);
            bankTradeReqDto.setOperationAmount(operationAmount);
            bankTradeReqDto.setOperationUserId(refundFlow.getOperationUserId());
            bankTradeReqDto.setTxnId(refundFlowId);
            bankTradeReqDto.setOrgSysOrdNo(consumeFlow.getBankTransNo());
            bankTradeReqDto.setPayAccountNo(payAccountNo);
            bankTradeReqDto.setReceiveAccountNo(refundFlow.getGuaranteeBankAcctId());
            bankTradeReqDto.setReceiveAccountName(receiveAccountName);
            bankTradeReqDto.setCompanyId(refundFlow.getCompanyId());
            bankTradeReqDto.setSyncTrade(true);
            bankTradeReqDto.setProductName(orderSnapshot);
            bankTradeReqDto.setAccountModel(consumeFlow.getAccountModel());
            bankTradeReqDto.setAccountSubType(consumeFlow.getAccountSubType());
            bankTradeReqDto.setAccountFlowId(refundFlowId);
            /*
              分贝券消费退款，都设置为个人
              QX 2021-12-13 FBT-9351
             */
            bankTradeReqDto.setCustomerType(CustomerTypeEnum.PERSONAL.getType());
            if (BankNameEnum.isLfBank(bankName)) {
                /*
                  廊坊银行-用途-消费传入发放券企业账号
                 */
                bankTradeReqDto.setMarketUserId(Long.valueOf(consumeFlow.getBankAcctId()));
            }
            FinhubLogger.info("iBankTradeService.bankRefund,参数:{}", JsonUtils.toJson(bankTradeReqDto));
            BankTradeRespDto bankTradeRespDto = iBankTradeService.bankRefund(bankTradeReqDto);
            FinhubLogger.info("iBankTradeService.bankRefund,返回：{}", JsonUtils.toJson(bankTradeRespDto));
            //上账发送消息--自动对账
            // 发送消息，移到方法外侧
        } catch (Exception ex) {
            FinhubLogger.error(BANK_TRANSFER_ERROR, ex);
        } finally {
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                FinhubLogger.warn("【分贝券退款上账】redis解锁异常", e);
            }
        }
    }

    /**
     * TODO 确定下
     * getFreezeOrderNo 通用方法
     * @param bizNo 各流水表唯一业务ID
     * @param accountSubType 子账户类型
     * @param accountModel 账户模式
     */
    private String getFreezeOrderNo(String bizNo,Integer accountSubType,Integer accountModel){
        if (FundAccountModelType.isRecharge(accountModel)){
            if (FundAccountSubType.isBusinessAccount(accountSubType)){
                List<AcctBusinessDebitFlow>  acctBusinessDebitFlows = uAcctBusinessDebitFlowService.queryAccountSubFlowByBizNo(bizNo,FundAccountSubOptType.FROZEN_VOUCHER_GRANT.getKey());
                if (ObjUtils.isNotEmpty(acctBusinessDebitFlows)){
                    return acctBusinessDebitFlows.get(0).getBankTransNo();
                }
            }
            if (FundAccountSubType.isIndividualAccount(accountSubType)){
                List<AcctIndividualDebitFlow> acctIndividualDebitFlows = uAcctIndividualDebitFlowService.queryAccountSubFlowByBizNo(bizNo,FundAccountSubOptType.FROZEN_VOUCHER_GRANT.getKey());
                if (ObjUtils.isNotEmpty(acctIndividualDebitFlows)){
                    return acctIndividualDebitFlows.get(0).getBankTransNo();
                }
            }
        }
        if (FundAccountModelType.isCredit(accountModel)){
            if (FundAccountSubType.isBusinessAccount(accountSubType)){
                List<AcctBusinessCreditFlow>  acctBusinessCreditFlows = uAcctBusinessCreditFlowService.queryAccountSubFlowByBizNo(bizNo,FundAccountSubOptType.FROZEN_VOUCHER_GRANT.getKey());
                if (ObjUtils.isNotEmpty(acctBusinessCreditFlows)){
                    return acctBusinessCreditFlows.get(0).getBankTransNo();
                }
            }
            if (FundAccountSubType.isIndividualAccount(accountSubType)){
                List<AcctIndividualCreditFlow>  acctIndividualCreditFlows = uAcctIndividualCreditFlowService.queryAccountSubFlowByBizNo(bizNo,FundAccountSubOptType.PUBLIC_CONSUME.getKey());
                if (ObjUtils.isNotEmpty(acctIndividualCreditFlows)){
                    return acctIndividualCreditFlows.get(0).getBankTransNo();
                }
            }
        }
        return null;
    }

    /**
     * 分贝券发放，上帐。发对账消息
     * @param fundFreezenFlow
     * @param payAccountNo
     * @param receiveAccountNo
     * @param receiveAccountName
     * @param freezeFlag
     */
    private void callBankTransfer(FundFreezenFlow fundFreezenFlow, String payAccountNo, String receiveAccountNo, String receiveAccountName, String freezeFlag) {
        String frozenFlowId = fundFreezenFlow.getFreezenFlowId();
        //需要增加分布式锁，获取到锁，继续执行，否则结束
        String lockKey = MessageFormat.format(RedisKeyConstant.ACCOUNT_FLOW_SYNC_BANK_KEY, frozenFlowId);
        try {
            boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                return;
            }
        } catch (InterruptedException e) {
            FinhubLogger.error("【新账户系统异常】调用银行转账，尝试加锁异常", e);
        }
        try {
            //更新上账次数
            updateCallBankNum(fundFreezenFlow);
            //银行上账
            BigDecimal operationAmount = fundFreezenFlow.getOperationAmount().setScale(0, ROUND_HALF_DOWN).abs();
            BankTransferReqDto bankTradeReqDto = new BankTransferReqDto();
            bankTradeReqDto.setBankName(fundFreezenFlow.getBankName());
            bankTradeReqDto.setOperationAmount(operationAmount);
            bankTradeReqDto.setOperationUserId(fundFreezenFlow.getOperationUserId());
            bankTradeReqDto.setTxnId(frozenFlowId);
            bankTradeReqDto.setPayAccountNo(payAccountNo);
            bankTradeReqDto.setReceiveAccountNo(receiveAccountNo);
            bankTradeReqDto.setReceiveAccountName(receiveAccountName);
            bankTradeReqDto.setCompanyId(fundFreezenFlow.getCompanyId());
            bankTradeReqDto.setSyncTrade(true);
            bankTradeReqDto.setAccountSubType(fundFreezenFlow.getAccountSubType());
            bankTradeReqDto.setAccountModel(fundFreezenFlow.getAccountModel());
            bankTradeReqDto.setAccountFlowId(frozenFlowId);
            bankTradeReqDto.setFreezeFlag(freezeFlag);
            if (FreezenChangeType.isUnfreezing(fundFreezenFlow.getOperationType()) || 
            		FreezenChangeType.isUnfreezing2Fbt(fundFreezenFlow.getOperationType())){
                FundFreezenFlow consumeFlow = fundFreezenFlowService.querySuccessFlowByVoucherIdAndBizNo(fundFreezenFlow.getAccountId(), fundFreezenFlow.getVoucherId(), fundFreezenFlow.getBizNo(), FreezenChangeType.FREEZING.getKey());
                if (Objects.isNull(consumeFlow)) {
                    FinhubLogger.error("【新账户系统异常】调用银行转账异常,未找到正向流水,refundFlowId:{}", fundFreezenFlow);
                    return;
                }
                bankTradeReqDto.setFreezeOrderNo(getFreezeOrderNo(consumeFlow.getBizNo(),consumeFlow.getAccountSubType(),consumeFlow.getAccountModel()));
            }
            /**
             * 转账，都设置为企业
             * QX 2021-12-13 FBT-9351
             */
            bankTradeReqDto.setCustomerType(CustomerTypeEnum.ENTERPRISE.getType());
            FinhubLogger.info("iBankTradeService.bankTransfer,参数:{}", JsonUtils.toJson(bankTradeReqDto));
            BankTradeRespDto bankTradeRespDto = iBankTradeService.bankTransfer(bankTradeReqDto);
            if (ObjUtils.isNull(bankTradeRespDto)) {
                FinhubLogger.error("【新账户系统异常】调用银行转账异常,bankTradeReqDto:{}", bankTradeReqDto);
            }
            FinhubLogger.info("iBankTradeService.bankTransfer,返回:{}", JsonUtils.toJson(bankTradeRespDto));
            //上账发送消息--自动对账
            // 发送消息，移到方法外侧
        } catch (Exception ex) {
            FinhubLogger.error(BANK_TRANSFER_ERROR, ex);
        } finally {
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                FinhubLogger.warn("【个人账户上账】redis解锁异常", e);
            }
        }
    }

    private AcctOperationRespDTO reduceAccount4VoucherGrant(AcctFreezeReqDTO acctFreezeReqDTO, AcctCommonBaseDTO currentAvailableAccount, AcctGuaranteeRespDTO guaranteeRespDTO, String freezenFlowId) {
        Integer accountModel = currentAvailableAccount.getAccountModel();
        AcctFreezeReqDTO reqDTO = new AcctFreezeReqDTO();
        BeanUtils.copyProperties(acctFreezeReqDTO, reqDTO);
        reqDTO.setFreezenFlowId(freezenFlowId);
        //减扣账户
        reqDTO.setFreezenUseType(FreezenUseType.INDIVIDUAL_VOUCHERS);
        reqDTO.setAccountId(currentAvailableAccount.getAccountId());
        //冻结时，个人账户的对手账户是担保账户
        reqDTO.setTargetBankAccountNo(guaranteeRespDTO.getGuaranteeBankAccountNo());
        reqDTO.setTargetBankAcctId(guaranteeRespDTO.getGuaranteeBankAcctId());
        reqDTO.setTargetBankName(guaranteeRespDTO.getGuaranteeBankName());
        ValidateUtils.validate(reqDTO);
        AcctOperationRespDTO acctOperationRespDTO = null;
        if (FundAccountModelType.isCredit(accountModel)) {
            acctOperationRespDTO = uAcctIndividualCreditService.frozen(reqDTO);
        } else if (FundAccountModelType.isRecharge(accountModel)) {
            acctOperationRespDTO = uAcctIndividualDebitService.frozen(reqDTO);
        } else {
            throw new FinPayException(GlobalResponseCode.FREEZEN_BUDGET_NOT_EXIST);
        }
        return acctOperationRespDTO;
    }

    private AcctOperationRespDTO addAccount4VoucherRecovery(AcctFreezenReleaseReqDTO freezenReleaseReqDTO, AcctGuaranteeRespDTO guaranteeRespDTO, Integer accountModel) {
        //解冻时，个人账户的对手账户是担保账户
        AcctFreezenReleaseReqDTO acctReleaseReqDTO = new AcctFreezenReleaseReqDTO();
        BeanUtils.copyProperties(freezenReleaseReqDTO, acctReleaseReqDTO);
        acctReleaseReqDTO.setTargetBankAccountNo(guaranteeRespDTO.getGuaranteeBankAccountNo());
        acctReleaseReqDTO.setTargetBankAcctId(guaranteeRespDTO.getGuaranteeBankAcctId());
        acctReleaseReqDTO.setTargetBankName(guaranteeRespDTO.getGuaranteeBankName());
        AcctOperationRespDTO addAccountRespDTO = null;
        if (FundAccountModelType.isCredit(accountModel)) {
            addAccountRespDTO = uAcctIndividualCreditService.unfreeze(acctReleaseReqDTO);
        } else if (FundAccountModelType.isRecharge(accountModel)) {
            addAccountRespDTO = uAcctIndividualDebitService.unfreeze(acctReleaseReqDTO);
        } else {
            throw new FinPayException(GlobalResponseCode.FREEZEN_BUDGET_NOT_EXIST);
        }
        return addAccountRespDTO;
    }

    private AcctOperationRespDTO addFrozen4VoucherGrant(AcctFreezeReqDTO acctFreezeReqDTO, AcctCommonBaseDTO currentAvailableAccount, FundFreezen fundFreezen, String freezenFlowId, AcctGuaranteeRespDTO guaranteeRespDTO) {
        String bankName = currentAvailableAccount.getBankName();
        String bankAccountNo = currentAvailableAccount.getBankAccountNo();
        String bankAcctId = currentAvailableAccount.getBankAcctId();
        FreezenOperationReqDTO freezenOperationReqDTO = new FreezenOperationReqDTO();
        BeanUtils.copyProperties(acctFreezeReqDTO, freezenOperationReqDTO);
        freezenOperationReqDTO.setFreezenUseType(FreezenUseType.INDIVIDUAL_VOUCHERS);
        freezenOperationReqDTO.setAccountId(currentAvailableAccount.getAccountId());
        freezenOperationReqDTO.setAccountModel(currentAvailableAccount.getAccountModel());
        freezenOperationReqDTO.setBankName(bankName);
        freezenOperationReqDTO.setBankAccountNo(bankAccountNo);
        freezenOperationReqDTO.setBankAcctId(bankAcctId);
        freezenOperationReqDTO.setVerifyNo(acctFreezeReqDTO.getBizNo());
        freezenOperationReqDTO.setGuaranteeBankAccountNo(fundFreezen.getGuaranteeBankAccountNo());
        freezenOperationReqDTO.setGuaranteeBankName(fundFreezen.getGuaranteeBankName());
        freezenOperationReqDTO.setGuaranteeBankAcctId(fundFreezen.getGuaranteeBankAcctId());
        //冻结时，担保账户的对手账户是个人账户
        freezenOperationReqDTO.setTargetBankAccountNo(bankAccountNo);
        freezenOperationReqDTO.setTargetBankAcctId(bankAcctId);
        freezenOperationReqDTO.setTargetBankName(bankName);
        freezenOperationReqDTO.setFreezenFlowId(freezenFlowId);
        freezenOperationReqDTO.setGuaranteeCompanyMainId(guaranteeRespDTO.getCompanyMainId());
        freezenOperationReqDTO.setGuaranteeAccountId(guaranteeRespDTO.getAccountId());
        freezenOperationReqDTO.setTargetCompanyMainId(currentAvailableAccount.getCompanyMainId());
        freezenOperationReqDTO.setTargetAccountId(currentAvailableAccount.getAccountId());
        return uFundFreezenService.frozen4Voucher(freezenOperationReqDTO, fundFreezen);
    }

    private void reduceFrozen4VoucherRecovery(AcctFreezenReleaseReqDTO freezenReleaseReqDTO, FundFreezen fundFreezen, String freezenReleaseFlowId, AcctTargetBaseDTO targetAcctDTO, AcctGuaranteeRespDTO guaranteeRespDTO, FreezenChangeType freezenChangeType) {
        FreezenOperationReqDTO releaseOperationReqDTO = new FreezenOperationReqDTO();
        BeanUtils.copyProperties(freezenReleaseReqDTO, releaseOperationReqDTO);
        releaseOperationReqDTO.setTargetBankAccountNo(targetAcctDTO.getTargetBankAccountNo());
        releaseOperationReqDTO.setTargetBankAcctId(targetAcctDTO.getTargetBankAcctId());
        releaseOperationReqDTO.setTargetBankName(targetAcctDTO.getTargetBankName());
        releaseOperationReqDTO.setFreezenFlowId(freezenReleaseFlowId);
        releaseOperationReqDTO.setFreezenUseType(FreezenUseType.INDIVIDUAL_VOUCHERS);
        releaseOperationReqDTO.setGuaranteeCompanyMainId(guaranteeRespDTO.getCompanyMainId());
        releaseOperationReqDTO.setGuaranteeAccountId(guaranteeRespDTO.getAccountId());
        releaseOperationReqDTO.setTargetCompanyMainId(targetAcctDTO.getTargetCompanyMainId());
        releaseOperationReqDTO.setTargetAccountId(targetAcctDTO.getTargetAccountId());
        releaseOperationReqDTO.setOperationType(freezenChangeType);
        uFundFreezenService.freezenRelease4Voucher(releaseOperationReqDTO, fundFreezen);
    }

    private AcctOperationRespDTO reduceFrozenAccount4VoucherConsumer(AcctFreezenConsumeReqDTO consumeFreezeDTO, BankAcct receiptBankAcct, AcctGuaranteeRespDTO guaranteeRespDTO) {
        FreezenOperationReqDTO consumeOperationReqDTO = new FreezenOperationReqDTO();
        BeanUtils.copyProperties(consumeFreezeDTO, consumeOperationReqDTO);
        consumeOperationReqDTO.setTargetBankAccountNo(receiptBankAcct.getBankAccountNo());
        consumeOperationReqDTO.setTargetBankAcctId(receiptBankAcct.getBankAcctId());
        consumeOperationReqDTO.setGuaranteeCompanyMainId(guaranteeRespDTO.getCompanyMainId());
        consumeOperationReqDTO.setGuaranteeAccountId(guaranteeRespDTO.getAccountId());
        consumeOperationReqDTO.setTargetCompanyMainId(receiptBankAcct.getCompanyMainId());
        consumeOperationReqDTO.setTargetAccountId(receiptBankAcct.getAccountId());
        return uFundFreezenService.freezenConsume4Voucher(consumeOperationReqDTO);
    }

    private AcctOperationRespDTO addFrozenAccount4VoucherRefund(AcctFreezenRefundReqDTO refundFreezeDTO, BankAcct receiptBankAcct, AcctGuaranteeRespDTO guaranteeRespDTO) {
        FreezenOperationReqDTO refundOperationDTO = new FreezenOperationReqDTO();
        BeanUtils.copyProperties(refundFreezeDTO, refundOperationDTO);
        refundOperationDTO.setTargetBankAccountNo(receiptBankAcct.getBankAccountNo());
        refundOperationDTO.setTargetBankAcctId(receiptBankAcct.getBankAcctId());
        refundOperationDTO.setTargetBankName(receiptBankAcct.getBankName());
        refundOperationDTO.setGuaranteeCompanyMainId(guaranteeRespDTO.getCompanyMainId());
        refundOperationDTO.setGuaranteeAccountId(guaranteeRespDTO.getAccountId());
        refundOperationDTO.setTargetCompanyMainId(receiptBankAcct.getCompanyMainId());
        refundOperationDTO.setTargetAccountId(receiptBankAcct.getAccountId());
        return uFundFreezenService.freezenRefund4Voucher(refundOperationDTO);
    }

    private AcctOperationRespDTO addGuaranteeAccount4VoucherGrant(AcctFreezeReqDTO acctFreezeReqDTO, AcctCommonBaseDTO currentAvailableAccount, String guaranteeAccountId) {
        String bankName = currentAvailableAccount.getBankName();
        BigDecimal operationAmount = acctFreezeReqDTO.getOperationAmount();
        if (BankNameEnum.guaranteeByCompany(bankName) && BankNameEnum.isFbt(bankName)) {
            return null;
        }
        if (BankNameEnum.guaranteeByPlatform(bankName)) {
            BankAcctTransDTO bankAcctTransDTO = new BankAcctTransDTO();
            BeanUtils.copyProperties(acctFreezeReqDTO, bankAcctTransDTO);
            bankAcctTransDTO.setAccountModel(currentAvailableAccount.getAccountModel());
            bankAcctTransDTO.setOperationType(FundPlatAcctOptType.VOUCHER_GRANT);
            bankAcctTransDTO.setBankAccountNo(currentAvailableAccount.getBankAccountNo());
            bankAcctTransDTO.setBankAcctId(currentAvailableAccount.getBankAcctId());
            bankAcctTransDTO.setBankName(bankName);
            bankAcctTransDTO.setOperationTypeDesc(FundPlatAcctOptType.VOUCHER_GRANT.getDesc());
            bankAcctTransDTO.setCompanyMainId(currentAvailableAccount.getCompanyMainId());
            bankAcctTransDTO.setCompanyId(currentAvailableAccount.getCompanyId());
            bankAcctTransDTO.setOperationAmount(operationAmount);
            bankAcctTransDTO.setTradeType(FundPlatAcctOptType.VOUCHER_GRANT.getTradeType().getCode());
            bankAcctTransDTO.setOrderType(CategoryTypeEnum.VOUCHER_GRANT_TASK.getCode());
            bankAcctTransDTO.setUnTxnId(acctFreezeReqDTO.getFreezenFlowId());
            return uBankAcctService.addBalanceByAccountId(guaranteeAccountId, operationAmount, bankAcctTransDTO);
        }
        return null;
    }

    private AcctOperationRespDTO reduceGuarantee4VoucherRecovery(AcctFreezenReleaseReqDTO freezenReleaseReqDTO, AcctGuaranteeRespDTO guaranteeRespDTO, AcctTargetBaseDTO targetAcctDTO, String freezenReleaseFlowId, String freezenFlowId) {
        BigDecimal operationAmount = freezenReleaseReqDTO.getOperationAmount();
        String targetBankName = guaranteeRespDTO.getGuaranteeBankName();
        if (BankNameEnum.guaranteeByCompany(targetBankName) && BankNameEnum.isFbt(targetBankName)) {
            return null;
        }
        if (BankNameEnum.guaranteeByPlatform(targetBankName)) {
            BankAcctTransDTO bankAcctTransDTO = new BankAcctTransDTO();
            BeanUtils.copyProperties(freezenReleaseReqDTO, bankAcctTransDTO);
            bankAcctTransDTO.setAccountModel(targetAcctDTO.getTargetAccountModel());
            bankAcctTransDTO.setOperationType(FundPlatAcctOptType.VOUCHER_RECALL);
            bankAcctTransDTO.setBankAccountNo(targetAcctDTO.getTargetBankAccountNo());
            bankAcctTransDTO.setBankAcctId(targetAcctDTO.getTargetBankAcctId());
            bankAcctTransDTO.setBankName(targetBankName);
            bankAcctTransDTO.setOperationTypeDesc(FundPlatAcctOptType.VOUCHER_RECALL.getDesc());
            bankAcctTransDTO.setCompanyMainId(targetAcctDTO.getTargetCompanyMainId());
            bankAcctTransDTO.setCompanyId(targetAcctDTO.getTargetCompanyId());
            bankAcctTransDTO.setOperationAmount(operationAmount);
            bankAcctTransDTO.setTradeType(FundPlatAcctOptType.VOUCHER_RECALL.getTradeType().getCode());
            bankAcctTransDTO.setOrderType(CategoryTypeEnum.VOUCHER_GRANT_TASK.getCode());
            bankAcctTransDTO.setUnTxnId(freezenFlowId);
            bankAcctTransDTO.setUnReTxnId(freezenReleaseFlowId);
            return uBankAcctService.reduceBalanceByAccountId(guaranteeRespDTO.getAccountId(), operationAmount, bankAcctTransDTO);
        }
        return null;
    }

    private AcctOperationRespDTO reduceGuaranteeAccount4VoucherConsumer(AcctFreezenConsumeReqDTO consumeFreezeDTO, AcctGuaranteeRespDTO guaranteeRespDTO, BankAcct receiptBankAcct, String frozenAccountFlowId) {
        String bankName = guaranteeRespDTO.getGuaranteeBankName();
        BigDecimal operationAmount = consumeFreezeDTO.getOperationAmount();
        if (BankNameEnum.guaranteeByCompany(bankName) && BankNameEnum.isFbt(bankName)) {
            return null;
        }
        if (BankNameEnum.guaranteeByPlatform(bankName)) {
            BankAcctTransDTO bankAcctTransDTO = new BankAcctTransDTO();
            BeanUtils.copyProperties(consumeFreezeDTO, bankAcctTransDTO);
            bankAcctTransDTO.setOperationType(FundPlatAcctOptType.VOUCHER_CONSUME);
            bankAcctTransDTO.setBankAccountNo(receiptBankAcct.getBankAccountNo());
            bankAcctTransDTO.setBankAcctId(receiptBankAcct.getBankAcctId());
            bankAcctTransDTO.setBankName(receiptBankAcct.getBankName());
            bankAcctTransDTO.setOperationTypeDesc(FundPlatAcctOptType.VOUCHER_CONSUME.getDesc());
            bankAcctTransDTO.setCompanyMainId(receiptBankAcct.getCompanyMainId());
            bankAcctTransDTO.setCompanyId(receiptBankAcct.getCompanyId());
            bankAcctTransDTO.setOperationAmount(operationAmount);
            bankAcctTransDTO.setTradeType(FundPlatAcctOptType.VOUCHER_CONSUME.getTradeType().getCode());
            bankAcctTransDTO.setUnTxnId(frozenAccountFlowId);
            return uBankAcctService.reduceBalanceByAccountId(guaranteeRespDTO.getAccountId(), operationAmount, bankAcctTransDTO);
        }
        return null;
    }

    private AcctOperationRespDTO addGuaranteeAccount4VoucherRefund(AcctFreezenRefundReqDTO refundFreezeDTO, AcctGuaranteeRespDTO guaranteeRespDTO, BankAcct receiptBankAcct, String frozenRefundFlowId, String consumerFlowId) {
        String bankName = guaranteeRespDTO.getGuaranteeBankName();
        BigDecimal operationAmount = refundFreezeDTO.getOperationAmount();
        if (BankNameEnum.guaranteeByCompany(bankName) && BankNameEnum.isFbt(bankName)) {
            return null;
        }
        if (BankNameEnum.guaranteeByPlatform(bankName)) {
            BankAcctTransDTO bankAcctTransDTO = new BankAcctTransDTO();
            BeanUtils.copyProperties(refundFreezeDTO, bankAcctTransDTO);
            bankAcctTransDTO.setOperationType(FundPlatAcctOptType.VOUCHER_REFUND);
            bankAcctTransDTO.setBankAccountNo(receiptBankAcct.getBankAccountNo());
            bankAcctTransDTO.setBankAcctId(receiptBankAcct.getBankAcctId());
            bankAcctTransDTO.setBankName(bankName);
            bankAcctTransDTO.setOperationTypeDesc(FundPlatAcctOptType.VOUCHER_REFUND.getDesc());
            bankAcctTransDTO.setCompanyMainId(receiptBankAcct.getCompanyMainId());
            bankAcctTransDTO.setCompanyId(receiptBankAcct.getCompanyId());
            bankAcctTransDTO.setOperationAmount(operationAmount);
            bankAcctTransDTO.setTradeType(FundPlatAcctOptType.VOUCHER_REFUND.getTradeType().getCode());
            bankAcctTransDTO.setUnTxnId(consumerFlowId);
            bankAcctTransDTO.setUnReTxnId(frozenRefundFlowId);
            return uBankAcctService.addBalanceByAccountId(guaranteeRespDTO.getAccountId(), operationAmount, bankAcctTransDTO);
        }
        return null;
    }

    private AcctOperationRespDTO addReceiptAccount4VoucherRecovery(AcctFreezenReleaseReqDTO freezenReleaseReqDTO, AcctGuaranteeRespDTO guaranteeRespDTO, String receiptAccountId, String freezenReleaseFlowId, String freezenFlowId) {
        String companyMainId = guaranteeRespDTO.getCompanyMainId();
        BigDecimal operationAmount = freezenReleaseReqDTO.getOperationAmount();
        BankAcctTransDTO bankAcctTransDTO = new BankAcctTransDTO();
        BeanUtils.copyProperties(freezenReleaseReqDTO, bankAcctTransDTO);
        bankAcctTransDTO.setOperationType(FundPlatAcctOptType.VOUCHER_RECALL_TO_RECEIPT);
        bankAcctTransDTO.setBankAccountNo(guaranteeRespDTO.getGuaranteeBankAccountNo());
        bankAcctTransDTO.setBankAcctId(guaranteeRespDTO.getGuaranteeBankAcctId());
        bankAcctTransDTO.setBankName(guaranteeRespDTO.getGuaranteeBankName());
        bankAcctTransDTO.setOperationTypeDesc(FundPlatAcctOptType.VOUCHER_RECALL_TO_RECEIPT.getDesc());
        bankAcctTransDTO.setCompanyMainId(ObjUtils.isBlank(companyMainId) ? freezenReleaseReqDTO.getCompanyId() : companyMainId);
        bankAcctTransDTO.setCompanyId(guaranteeRespDTO.getCompanyId());
        bankAcctTransDTO.setOperationAmount(operationAmount);
        bankAcctTransDTO.setTradeType(FundPlatAcctOptType.VOUCHER_RECALL_TO_RECEIPT.getTradeType().getCode());
        bankAcctTransDTO.setOrderType(CategoryTypeEnum.VOUCHER_GRANT_TASK.getCode());
        bankAcctTransDTO.setUnTxnId(freezenFlowId);
        bankAcctTransDTO.setUnReTxnId(freezenReleaseFlowId);
        return uBankAcctService.addBalanceByAccountId(receiptAccountId, operationAmount, bankAcctTransDTO);
    }

    private void addReceiptAccount4VoucherConsumer(AcctFreezenConsumeReqDTO consumeFreezeDTO, AcctGuaranteeRespDTO guaranteeRespDTO, BankAcct receiptBankAcct, String frozenAccountFlowId) {
        String companyMainId = guaranteeRespDTO.getCompanyMainId();
        BigDecimal operationAmount = consumeFreezeDTO.getOperationAmount();
        BankAcctTransDTO bankAcctTransDTO = new BankAcctTransDTO();
        BeanUtils.copyProperties(consumeFreezeDTO, bankAcctTransDTO);
        bankAcctTransDTO.setOperationType(FundPlatAcctOptType.VOUCHER_CONSUME);
        bankAcctTransDTO.setBankAccountNo(guaranteeRespDTO.getGuaranteeBankAccountNo());
        bankAcctTransDTO.setBankAcctId(guaranteeRespDTO.getGuaranteeBankAcctId());
        bankAcctTransDTO.setBankName(guaranteeRespDTO.getGuaranteeBankName());
        bankAcctTransDTO.setOperationTypeDesc(FundPlatAcctOptType.VOUCHER_CONSUME.getDesc());
        bankAcctTransDTO.setCompanyMainId(ObjUtils.isBlank(companyMainId) ? consumeFreezeDTO.getCompanyId() : companyMainId);
        bankAcctTransDTO.setCompanyId(guaranteeRespDTO.getCompanyId());
        bankAcctTransDTO.setOperationAmount(operationAmount);
        bankAcctTransDTO.setTradeType(FundPlatAcctOptType.VOUCHER_CONSUME.getTradeType().getCode());
        bankAcctTransDTO.setUnTxnId(frozenAccountFlowId);
        uBankAcctService.addBalanceByAccountId(receiptBankAcct.getAccountId(), operationAmount, bankAcctTransDTO);
    }

    private AcctOperationRespDTO reduceReceiptAccount4VoucherRefund(AcctFreezenRefundReqDTO refundFreezeDTO, AcctGuaranteeRespDTO guaranteeRespDTO, BankAcct receiptBankAcct, String frozenRefundFlowId, String consumerFlowId) {
        String companyMainId = guaranteeRespDTO.getCompanyMainId();
        BigDecimal operationAmount = refundFreezeDTO.getOperationAmount();
        BankAcctTransDTO bankAcctTransDTO = new BankAcctTransDTO();
        BeanUtils.copyProperties(refundFreezeDTO, bankAcctTransDTO);
        bankAcctTransDTO.setOperationType(FundPlatAcctOptType.VOUCHER_REFUND);
        bankAcctTransDTO.setBankAccountNo(guaranteeRespDTO.getGuaranteeBankAccountNo());
        bankAcctTransDTO.setBankAcctId(guaranteeRespDTO.getGuaranteeBankAcctId());
        bankAcctTransDTO.setBankName(guaranteeRespDTO.getGuaranteeBankName());
        bankAcctTransDTO.setOperationTypeDesc(FundPlatAcctOptType.VOUCHER_REFUND.getDesc());
        bankAcctTransDTO.setCompanyMainId(ObjUtils.isBlank(companyMainId) ? refundFreezeDTO.getCompanyId() : companyMainId);
        bankAcctTransDTO.setCompanyId(guaranteeRespDTO.getCompanyId());
        bankAcctTransDTO.setOperationAmount(operationAmount);
        bankAcctTransDTO.setTradeType(FundPlatAcctOptType.VOUCHER_REFUND.getTradeType().getCode());
        bankAcctTransDTO.setUnTxnId(consumerFlowId);
        bankAcctTransDTO.setUnReTxnId(frozenRefundFlowId);
        return uBankAcctService.reduceBalanceByAccountId(receiptBankAcct.getAccountId(), operationAmount, bankAcctTransDTO);
    }

    private FundFreezen queryFundFreezen(AcctCommonBaseDTO currentAvailableAccount, AcctGuaranteeRespDTO guaranteeBankAcct) {
        String companyId = currentAvailableAccount.getCompanyId();
        String bankName = currentAvailableAccount.getBankName();
        String accountId = currentAvailableAccount.getAccountId();
        FundFreezen fundFreezen = uFundFreezenService.queryFundFreezen(accountId, FreezenUseType.INDIVIDUAL_VOUCHERS);
        if (Objects.isNull(fundFreezen)) {
            FreezenOperationReqDTO freezenCreateReqDTO = new FreezenOperationReqDTO();
            freezenCreateReqDTO.setBankAccountNo(currentAvailableAccount.getBankAccountNo());
            freezenCreateReqDTO.setBankName(bankName);
            freezenCreateReqDTO.setBankAcctId(currentAvailableAccount.getBankAcctId());
            freezenCreateReqDTO.setAccountGeneralId(currentAvailableAccount.getAccountGeneralId());
            freezenCreateReqDTO.setCompanyMainId(currentAvailableAccount.getCompanyMainId());
            freezenCreateReqDTO.setAccountId(accountId);
            freezenCreateReqDTO.setCompanyId(companyId);
            freezenCreateReqDTO.setAccountSubType(currentAvailableAccount.getAccountSubType());
            freezenCreateReqDTO.setAccountModel(currentAvailableAccount.getAccountModel());
            freezenCreateReqDTO.setGuaranteeBankAccountNo(guaranteeBankAcct.getGuaranteeBankAccountNo());
            freezenCreateReqDTO.setGuaranteeBankName(guaranteeBankAcct.getGuaranteeBankName());
            freezenCreateReqDTO.setGuaranteeBankAcctId(guaranteeBankAcct.getGuaranteeBankAcctId());
            fundFreezen = uFundFreezenService.createFundFreezen(freezenCreateReqDTO, FreezenUseType.INDIVIDUAL_VOUCHERS);
        }
        return fundFreezen;
    }

    /**
     * 本类中，发送对账消息
     * QX 2021-12-10 FBT-9264
     * @param freezenFlowId 冻结池流水ID
     */
    public void sendAutoAcctCheckingMsg(String freezenFlowId) {
        if(StringUtils.isBlank(freezenFlowId)){
            return;
        }
        FundFreezenFlow fundFreezenFlow = fundFreezenFlowService.queryByFreezenFlowId(freezenFlowId);
        if(fundFreezenFlow == null){
            return;
        }
        KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
        BeanUtils.copyProperties(fundFreezenFlow, kafkaAutoAcctCheckingMsg);
        kafkaAutoAcctCheckingMsg.setAccountFlowId(fundFreezenFlow.getFreezenFlowId());
        autoAcctCheckingEventUtil.sendAccountChangeMsgEvent(kafkaAutoAcctCheckingMsg);
    }
}
