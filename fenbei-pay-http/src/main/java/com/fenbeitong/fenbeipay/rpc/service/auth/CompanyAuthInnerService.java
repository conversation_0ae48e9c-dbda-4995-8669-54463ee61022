package com.fenbeitong.fenbeipay.rpc.service.auth;

import java.util.Objects;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthResultDTO;
import com.fenbeitong.dech.api.service.airwallex.AirwallexAuthPayService;
import com.fenbeitong.fenbeipay.acctdech.service.PayCompanyAuthService;
import com.fenbeitong.fenbeipay.api.model.dto.AuthRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.QueryAuthRequestDTO;
import com.fenbeitong.fenbeipay.dto.acctdech.CompanyAuthRecord;
import com.fenbeitong.finhub.common.utils.FinhubLogger;

@Service
public class CompanyAuthInnerService {

	@Autowired
	private PayCompanyAuthService companyAuthService;
	
	@Autowired
    private AirwallexAuthPayService airwallexAuthPayService;
	
	public AuthRespDTO queryAuthResultByAirwallex(QueryAuthRequestDTO request) {
		AuthResultDTO authResult = airwallexAuthPayService.queryAuthResult(AuthRequestDTO.builder().companyId(request.getCompanyId()).build());
		if (Objects.isNull(authResult)) {
			return AuthRespDTO.builder().status(0).companyId(request.getCompanyId()).authorized(Boolean.FALSE).build();
		}
		
		return AuthRespDTO.builder().status(authResult.getStatus()).companyId(request.getCompanyId()).authorized(authResult.isAuthorized()).build();
	}
	
	public AuthResultDTO authorize(AuthRequestDTO request) {
		FinhubLogger.info("Airwallex用户授权请求->{}", JSON.toJSONString(request));
		AuthResultDTO result = airwallexAuthPayService.authorize(request);
		if (Objects.nonNull(result) && result.isAuthorized()) {
			CompanyAuthRecord authRecord = CompanyAuthRecord.builder()
					.id(ObjectId.get().toString())
					.companyId(request.getCompanyId())
					.accountId(result.getAccountId())
					.accountName(result.getAccountName())
					.accountNo(result.getAccountNumber())
					.status(result.getStatus())
					.build();
			companyAuthService.saveCompanyAuth(authRecord);
		}
		return result;
		
	}

}
