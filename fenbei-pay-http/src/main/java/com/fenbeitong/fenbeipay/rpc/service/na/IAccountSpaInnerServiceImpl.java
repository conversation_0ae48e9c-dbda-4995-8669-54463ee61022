package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAccountRequestLogService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAccountSpaInnerService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountSpaInnerTransferInRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountSpaInnerTransferOutRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountSpaInnerTransferQueryRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountSpaInnerTransferRespDTO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSpaInnerService;
import com.fenbeitong.fenbeipay.dto.acctdech.AccountRequestLog;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/10
 */

@Service("iAccountSpaInnerService")
public class IAccountSpaInnerServiceImpl implements IAccountSpaInnerService {
    @Autowired
    UAccountSpaInnerService uAccountSpaInnerService;
    @Autowired
    UAccountRequestLogService uAccountRequestLogService;
    @Override
    public AccountSpaInnerTransferInRespDTO transferIn(AccountSpaInnerTransferInReqDTO accountSpaInnerReqDTO) {
        try{
            FinhubLogger.info("transferIn log:{}",JsonUtils.toJson(accountSpaInnerReqDTO));
            ValidateUtils.validate(accountSpaInnerReqDTO);
            AccountRequestLog accountRequestLog = new AccountRequestLog();
            accountRequestLog.setCompanyId(accountSpaInnerReqDTO.getCompanyId());
            accountRequestLog.setBizNo(accountSpaInnerReqDTO.getBizNo());
            accountRequestLog.setRequestMethod("transferIn");
            accountRequestLog.setRequestData(JsonUtils.toJson(accountSpaInnerReqDTO));
            uAccountRequestLogService.saveRequestLog(accountRequestLog);
            return uAccountSpaInnerService.transferIn(accountSpaInnerReqDTO);
        } catch (Exception e){
            FinhubLogger.error("transferIn error:",e);
            return AccountSpaInnerTransferInRespDTO.failed(e.getMessage());
        }
    }

    @Override
    public AccountSpaInnerTransferOutRespDTO transferOut(AccountSpaInnerTransferOutReqDTO accountSpaInnerTransferOutReqDTO) {
        try {
            FinhubLogger.info("transferOut log:{}",JsonUtils.toJson(accountSpaInnerTransferOutReqDTO));
            ValidateUtils.validate(accountSpaInnerTransferOutReqDTO);
            AccountRequestLog accountRequestLog = new AccountRequestLog();
            accountRequestLog.setCompanyId(accountSpaInnerTransferOutReqDTO.getCompanyId());
            accountRequestLog.setBizNo(accountSpaInnerTransferOutReqDTO.getBizNo());
            accountRequestLog.setRequestMethod("transferOut");
            accountRequestLog.setRequestData(JsonUtils.toJson(accountSpaInnerTransferOutReqDTO));
            uAccountRequestLogService.saveRequestLog(accountRequestLog);
            return uAccountSpaInnerService.transferOut(accountSpaInnerTransferOutReqDTO);
        }catch (Exception e){
            FinhubLogger.error("transferIn error:",e);
            return AccountSpaInnerTransferOutRespDTO.failed(e.getMessage());
        }
    }

    @Override
    public AccountSpaInnerTransferRespDTO transferFromPublicToInner(AccountSpaPublicAndInnerTransferReqDTO accountSpaPublicAndInnerTransferReqDTO) {
        try {
            FinhubLogger.info("transferFromPublicToInner log:{}",JsonUtils.toJson(accountSpaPublicAndInnerTransferReqDTO));
            ValidateUtils.validate(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaPublicTransferReqDTO());
            ValidateUtils.validate(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaInnerTransferInReqDTO());
            AccountRequestLog accountRequestLog = new AccountRequestLog();
            accountRequestLog.setCompanyId(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaPublicTransferReqDTO().getCompanyId());
            accountRequestLog.setBizNo(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaPublicTransferReqDTO().getBizNo());
            accountRequestLog.setRequestMethod("transferFromPublicToInner");
            accountRequestLog.setRequestData(JsonUtils.toJson(accountSpaPublicAndInnerTransferReqDTO));
            uAccountRequestLogService.saveRequestLog(accountRequestLog);
            return uAccountSpaInnerService.transferFromPublicToInner(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaPublicTransferReqDTO(),accountSpaPublicAndInnerTransferReqDTO.getAccountSpaInnerTransferInReqDTO());
        }catch (Exception e){
            FinhubLogger.error("transferIn error:",e);
            return AccountSpaInnerTransferRespDTO.failed(e.getMessage());
        }
    }

    @Override
    public AccountSpaInnerTransferRespDTO transferFromInnerToPublic(AccountSpaPublicAndInnerTransferReqDTO accountSpaPublicAndInnerTransferReqDTO) {
        try {
            FinhubLogger.info("transferFromInnerToPublic log:{}",JsonUtils.toJson(accountSpaPublicAndInnerTransferReqDTO));
            ValidateUtils.validate(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaPublicTransferReqDTO());
            ValidateUtils.validate(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaInnerTransferOutReqDTO());
            AccountRequestLog accountRequestLog = new AccountRequestLog();
            accountRequestLog.setCompanyId(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaPublicTransferReqDTO().getCompanyId());
            accountRequestLog.setBizNo(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaPublicTransferReqDTO().getBizNo());
            accountRequestLog.setRequestMethod("transferFromPublicToInner");
            accountRequestLog.setRequestData(JsonUtils.toJson(accountSpaPublicAndInnerTransferReqDTO));
            uAccountRequestLogService.saveRequestLog(accountRequestLog);
            return uAccountSpaInnerService.transferFromInnerToPublic(accountSpaPublicAndInnerTransferReqDTO.getAccountSpaPublicTransferReqDTO(),accountSpaPublicAndInnerTransferReqDTO.getAccountSpaInnerTransferOutReqDTO());
        }catch (Exception e){
            FinhubLogger.error("transferIn error:",e);
            return AccountSpaInnerTransferRespDTO.failed(e.getMessage());
        }
    }

    @Override
    public AccountSpaInnerTransferRespDTO transferInnerToInner(AccountSpaInnerTransferReqDTO accountSpaInnerTransferReqDTO) {
        try{
            FinhubLogger.info("transferInnerToInner log:{}",JsonUtils.toJson(accountSpaInnerTransferReqDTO));
            AccountRequestLog accountRequestLog = new AccountRequestLog();
            accountRequestLog.setCompanyId(accountSpaInnerTransferReqDTO.getCompanyId());
            accountRequestLog.setBizNo(accountSpaInnerTransferReqDTO.getBizNo());
            accountRequestLog.setRequestMethod("transferInnerToInner");
            accountRequestLog.setRequestData(JsonUtils.toJson(accountSpaInnerTransferReqDTO));
            uAccountRequestLogService.saveRequestLog(accountRequestLog);
            return uAccountSpaInnerService.transferInnerToInner(accountSpaInnerTransferReqDTO);
        }catch (Exception e){
            FinhubLogger.error("transferInnerToInner err:{}", JsonUtils.toJson(accountSpaInnerTransferReqDTO),e);
            AccountSpaInnerTransferRespDTO accountSpaInnerTransferRespDTO = new AccountSpaInnerTransferRespDTO();
            accountSpaInnerTransferRespDTO.setTransResult(Boolean.FALSE);
            accountSpaInnerTransferRespDTO.setTransResultDesc(e.getMessage());
            return accountSpaInnerTransferRespDTO;
        }

    }

    @Override
    public AccountSpaInnerTransferQueryRespDTO transferQuery(AccountSpaInnerTransferQueryReqDTO accountSpaInnerTransferQueryReqDTO) {
        return uAccountSpaInnerService.transferQuery(accountSpaInnerTransferQueryReqDTO);
    }

    @Override
    public boolean updateInfoByRequestNo(AccountSpaInnerUpdateDTO accountSpaInnerUpdateDTO) {
        FinhubLogger.info("spa-public,updateInfoByRequestNo params is {}", JsonUtils.toJson(accountSpaInnerUpdateDTO));
        return uAccountSpaInnerService.updateInfoByRequestNo(accountSpaInnerUpdateDTO);
    }

    @Override
    public boolean updateShowStatusByRequestNo(String payRequestNo, String refundRequestNo) {
        FinhubLogger.info("spa-public,updateShowStatusByRequestNo payRequestNo is {}, refundRequestNo is {} ", payRequestNo, refundRequestNo);
        return uAccountSpaInnerService.updateShowStatusByRequestNo(payRequestNo,refundRequestNo);
    }

    @Override
    public boolean updateInfoByRequestNo(List<AccountSpaInnerBatchUpdateDTO> accountSpaInnerBatchUpdateDTOS) {
        FinhubLogger.info("spa-public updateInfoByRequestNo accountSpaInnerBatchUpdateDTOS :{} ", accountSpaInnerBatchUpdateDTOS);
        return uAccountSpaInnerService.updateInfoByRequestNo(accountSpaInnerBatchUpdateDTOS);
    }


}
