package com.fenbeitong.fenbeipay.rpc.service.na;

import com.fenbeitong.fenbeipay.acctdech.service.BankAcctFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.BankAcctService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UBankAcctService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.impl.UAcctGeneralServiceImpl;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctGeneralOptReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountExecuteReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountQueryReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountRollBackReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountUpdateFlowReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountExecuteResultRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.ResultRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.tools.ResultTool;
import com.fenbeitong.fenbeipay.api.service.na.IAccountComponentService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.AutoAcctCheckingEventUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcct;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcctFlow;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundAcctGeneralOptType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 提供给支付引擎（paycore）的账户组件实现类
 *
 * <AUTHOR>
 * @date 2022/6/25
 */
@Service("iAccountComponentService")
public class AccountComponentServiceImpl implements IAccountComponentService {
    private static final String SUCCESS = "S";
    private static final String FAILURE = "F";
    private static final String PROCESS = "P";

    @Resource
    private UAcctGeneralServiceImpl uAcctGeneralService;
    @Resource
    private UBankAcctService uBankAcctService;
    @Resource
    private BankAcctService bankAcctService;
    @Resource
    private BankAcctFlowService bankAcctFlowService;
    @Resource
    private AccountGeneralService accountGeneralService;
    @Resource
    private AccountGeneralFlowService accountGeneralFlowService;

    @Autowired
    protected AutoAcctCheckingEventUtil autoAcctCheckingEventUtil;
    @Override
    public ResultRespDTO<AccountExecuteResultRespDTO> execute(AccountExecuteReqDTO req) {
        try {
            // 校验参数
            ValidateUtils.validate(req);
            // 选择账户
            FundAccountSubType acctType = FundAccountSubType.getEnum(req.getAccountType());
            switch (acctType) {
                case GENERAL_ACCOUNT:
                    return ResultTool.isSuccess(optGeneralAccount(req));
                default:
                    FinhubLogger.error("账户类型不存在 accountType={}", acctType);
                    return ResultTool.isFailure(GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getCode(), GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getMsg());
            }

        } catch (ValidateException ve) {
            FinhubLogger.error("参数异常", ve);
            return ResultTool.isFailure(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), ve.getMessage());
        } catch (FinhubException fpe) {
            FinhubLogger.error("业务异常", fpe);
            return ResultTool.isFailure(fpe.getCode(), fpe.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("未知异常", e);
            return ResultTool.isFailure();
        }
    }

    private AccountExecuteResultRespDTO optGeneralAccount(AccountExecuteReqDTO req) {
        // 根据操作类型走不同的账户操作
        FundAcctGeneralOptType optType = FundAcctGeneralOptType.getEnum(req.getOperationType());
        switch (optType) {
            case WITHDRAWAL:
                if (OperationChannelType.isWeb(req.getOperationChannelType())) {
                    uAcctGeneralService.cashWithdrawal(buildOptParam(req));
                } else if (OperationChannelType.isStereo(req.getOperationChannelType())) {
                    uBankAcctService.bankAcctWithdrawal4Stereo(buildOptParam(req));
                } else {
                    FinhubLogger.error("提现渠道不支持 operationChannelType={}", req.getOperationChannelType());
                    throw new FinPayException(GlobalResponseCode.BANK_GENERAL_CASH_OUT_ERROR);
                }
                break;
            default:
                FinhubLogger.error("暂不支持的操作类型 optType={}", optType);
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountExecuteResultRespDTO resp = new AccountExecuteResultRespDTO();
        resp.setRequestNo(req.getRequestNo());
        resp.setResult(SUCCESS);
        return resp;
    }

    private AcctGeneralOptReqDTO buildOptParam(AccountExecuteReqDTO req) {
        String bankAccountNo;
        String companyId;
        String companyMainId;
        if (OperationChannelType.isWeb(req.getOperationChannelType())) {
            // 查询余额账户信息
            AccountGeneral acct = accountGeneralService.findByAccountId(req.getAccountId());
            if (null == acct) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
            }
            bankAccountNo = acct.getBankAccountNo();
            companyId = acct.getCompanyId();
            companyMainId = acct.getCompanyMainId();
        } else if (OperationChannelType.isStereo(req.getOperationChannelType())) {
            // 查询分贝通收款（平台账户）账户信息
            BankAcct acct = bankAcctService.queryByAccountId(req.getAccountId());
            if (null == acct) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_BANK_NOT_EXIST);
            }
            bankAccountNo = acct.getBankAccountNo();
            companyId = acct.getCompanyId();
            companyMainId = acct.getCompanyMainId();
        } else {
            FinhubLogger.error("提现渠道不支持 operationChannelType={}", req.getOperationChannelType());
            throw new FinPayException(GlobalResponseCode.BANK_GENERAL_CASH_OUT_ERROR);
        }
        AcctGeneralOptReqDTO optParam = new AcctGeneralOptReqDTO();
        optParam.setBankAccountNo(bankAccountNo);
        optParam.setCompanyId(companyId);
        optParam.setCompanyMainId(companyMainId);
        optParam.setBankName(req.getBankCode());
        optParam.setRequestNo(req.getRequestNo());
        optParam.setCustomerServiceId(req.getCustomerServiceId());
        optParam.setCustomerServiceName(req.getCustomerServiceName());
        optParam.setOperationAmount(req.getOperationAmount());
        optParam.setOperationChannelType(req.getOperationChannelType());
        optParam.setOperationDescription(req.getRemark());
        optParam.setRemark(req.getRemark());
        optParam.setOperationUserId(req.getOperationUserId());
        optParam.setOperationUserName(req.getOperationUserName());
        optParam.setRequestBankFlag(false);
        optParam.setTargetAccountSubType(req.getTargetAccountSubType());
        optParam.setTargetBankAccountName(req.getTargetBankAccountName());
        optParam.setTargetBankAccountNo(req.getTargetAccount());
        optParam.setTargetBankName(req.getTargetBankName());
        optParam.setBizNo("");
        optParam.setVerifyCode("");
        optParam.setVerifyCodePhoneNum("");
        return optParam;
    }

    @Override
    public ResultRespDTO<AccountExecuteResultRespDTO> rollBack(AccountRollBackReqDTO req) {
        try {
            // 校验参数
            ValidateUtils.validate(req);
            // 选择账户
            FundAccountSubType acctType = FundAccountSubType.getEnum(req.getAccountType());
            switch (acctType) {
                case GENERAL_ACCOUNT:
                    return ResultTool.isSuccess(rollGeneralAccount(req));
                default:
                    FinhubLogger.error("账户类型不存在 accountType={}", acctType);
                    return ResultTool.isFailure(GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getCode(), GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getMsg());
            }

        } catch (ValidateException ve) {
            FinhubLogger.error("参数异常", ve);
            return ResultTool.isFailure(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), ve.getMessage());
        } catch (FinhubException fpe) {
            FinhubLogger.error("业务异常", fpe);
            return ResultTool.isFailure(fpe.getCode(), fpe.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("未知异常", e);
            return ResultTool.isFailure();
        }
    }

    private AccountExecuteResultRespDTO rollGeneralAccount(AccountRollBackReqDTO req) {
        Integer operationType;
        Integer operationChannelType;
        // 查询余额账户流水信息
        AccountGeneralFlow generalFlow = accountGeneralFlowService.queryByRequestNo(req.getOriRequestNo());
        if (null != generalFlow) {
            operationType = generalFlow.getOperationType();
            operationChannelType = generalFlow.getOperationChannelType();
        } else {
            // 如果余额账户没查到 去分贝通收款账户查询分贝通收款（平台账户）账户流水信息
            BankAcctFlow bank = bankAcctFlowService.queryByRequestNo(req.getOriRequestNo());
            if (null == bank) {
                FinhubLogger.error("账户流水不存在 requestNo={}", req.getOriRequestNo());
                throw new FinPayException(GlobalResponseCode.ACCOUNT_BANK_FLOW_NOT_EXIST);
            }
            operationType = bank.getOperationType();
            operationChannelType = bank.getOperationChannelType();
        }
        // 根据操作类型走不同的账户操作
        FundAcctGeneralOptType optType = FundAcctGeneralOptType.getEnum(operationType);
        switch (optType) {
            case WITHDRAWAL:
                if (OperationChannelType.isWeb(operationChannelType)) {
                    uAcctGeneralService.cashWithdrawalCallBack(req.getOriRequestNo(), req.getRequestNo());
                } else if (OperationChannelType.isStereo(operationChannelType)) {
                    uBankAcctService.cashWithdrawalCallBack(req.getOriRequestNo(), req.getRequestNo());
                } else {
                    FinhubLogger.error("提现渠道不支持 operationChannelType={}", operationChannelType);
                    throw new FinPayException(GlobalResponseCode.BANK_GENERAL_CASH_OUT_ERROR);
                }
                break;
            default:
                FinhubLogger.error("暂不支持的操作类型 optType={}", optType);
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountExecuteResultRespDTO resp = new AccountExecuteResultRespDTO();
        resp.setRequestNo(req.getRequestNo());
        resp.setResult(SUCCESS);
        return resp;
    }

    @Override
    public ResultRespDTO<AccountExecuteResultRespDTO> queryResult(AccountQueryReqDTO req) {
        try {
            // 校验参数
            ValidateUtils.validate(req);
            // 选择账户
            FundAccountSubType acctType = FundAccountSubType.getEnum(req.getAccountType());
            switch (acctType) {
                case GENERAL_ACCOUNT:
                    return ResultTool.isSuccess(qryGeneralAccount(req));
                default:
                    FinhubLogger.error("账户类型不存在 accountType={}", acctType);
                    return ResultTool.isFailure(GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getCode(), GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getMsg());
            }

        } catch (ValidateException ve) {
            FinhubLogger.error("参数异常", ve);
            return ResultTool.isFailure(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), ve.getMessage());
        } catch (FinhubException fpe) {
            FinhubLogger.error("业务异常", fpe);
            return ResultTool.isFailure(fpe.getCode(), fpe.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("未知异常", e);
            return ResultTool.isFailure();
        }
    }

    private AccountExecuteResultRespDTO qryGeneralAccount(AccountQueryReqDTO req) {
        // 查询余额账户流水信息
        AccountGeneralFlow flow = accountGeneralFlowService.queryByRequestNo(req.getRequestNo());
        if (flow == null) {
            // 如果余额账户没查到 去分贝通收款账户查询分贝通收款（平台账户）账户流水信息
            BankAcctFlow acctFlow = bankAcctFlowService.queryByRequestNo(req.getRequestNo());
            if (acctFlow == null) {
                throw new FinPayException(GlobalResponseCode.RESULT_NOT_EXIST);
            }
        }
        AccountExecuteResultRespDTO resp = new AccountExecuteResultRespDTO();
        resp.setRequestNo(req.getRequestNo());
        resp.setResult(SUCCESS);
        return resp;
    }

    @Override
    public ResultRespDTO<AccountExecuteResultRespDTO> updateFlow(AccountUpdateFlowReqDTO req) {
        try {
            // 校验参数
            ValidateUtils.validate(req);
            // 选择账户
            FundAccountSubType acctType = FundAccountSubType.getEnum(req.getAccountType());
            switch (acctType) {
                case GENERAL_ACCOUNT:
                    return ResultTool.isSuccess(updateGeneralAccountFlow(req));
                default:
                    FinhubLogger.error("账户类型不存在 accountType={}", acctType);
                    return ResultTool.isFailure(GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getCode(), GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getMsg());
            }

        } catch (ValidateException ve) {
            FinhubLogger.error("参数异常", ve);
            return ResultTool.isFailure(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), ve.getMessage());
        } catch (FinhubException fpe) {
            FinhubLogger.error("业务异常", fpe);
            return ResultTool.isFailure(fpe.getCode(), fpe.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("未知异常", e);
            return ResultTool.isFailure();
        }
    }

    private AccountExecuteResultRespDTO updateGeneralAccountFlow(AccountUpdateFlowReqDTO req) {
        // 查询余额账户流水信息
        AccountGeneralFlow generalFlow = accountGeneralFlowService.queryByRequestNo(req.getRequestNo());
        FinhubLogger.info("余额账户流水信息:{}", JsonUtils.toJson(generalFlow));
        if (null != generalFlow) {
            generalFlow.setBankTransNo(req.getSysOrdNo());
            generalFlow.setSyncBankTransNo(req.getBankTransNo());
            generalFlow.setSyncBankStatus(req.getBankStatus());
            generalFlow.setSyncBankAmount(req.getBankAmt());
            generalFlow.setSyncBankTime(req.getBankTime());
            accountGeneralFlowService.updateFlow(generalFlow);
            // 发送对账消息
            KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
            BeanUtils.copyProperties(generalFlow, kafkaAutoAcctCheckingMsg);
            autoAcctCheckingEventUtil.sendAccountChangeMsgEvent(kafkaAutoAcctCheckingMsg);
        } else {
            // 如果余额账户没查到 去分贝通收款账户查询分贝通收款（平台账户）账户流水信息
            BankAcctFlow acctFlow = bankAcctFlowService.queryByRequestNo(req.getRequestNo());
            if (null == acctFlow) {
                FinhubLogger.error("账户流水不存在 requestNo={}", req.getRequestNo());
                throw new FinPayException(GlobalResponseCode.ACCOUNT_BANK_FLOW_NOT_EXIST);
            }
            acctFlow.setBankTransNo(req.getSysOrdNo());
            acctFlow.setSyncBankTransNo(req.getBankTransNo());
            acctFlow.setSyncBankStatus(req.getBankStatus());
            acctFlow.setSyncBankAmount(req.getBankAmt());
            acctFlow.setSyncBankTime(req.getBankTime());
            bankAcctFlowService.updateFlow(acctFlow);
            // 发送对账消息
            KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg = new KafkaAutoAcctCheckingMsg();
            BeanUtils.copyProperties(acctFlow, kafkaAutoAcctCheckingMsg);
            autoAcctCheckingEventUtil.sendAccountChangeMsgEvent(kafkaAutoAcctCheckingMsg);
        }
        AccountExecuteResultRespDTO resp = new AccountExecuteResultRespDTO();
        resp.setRequestNo(req.getRequestNo());
        resp.setResult(SUCCESS);
        return resp;
    }
}
