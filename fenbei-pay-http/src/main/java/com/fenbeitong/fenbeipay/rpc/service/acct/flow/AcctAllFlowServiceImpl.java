package com.fenbeitong.fenbeipay.rpc.service.acct.flow;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountAllFlowFlagEnum;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctAllFlowConvertDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctFlowStereoPageServiceReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctFlowStereoPageServiceResqDTO;
import com.fenbeitong.fenbeipay.dto.flow.AccountAllFlow;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.pay.search.dbf.mapper.AccountAllFlowMapper;
import com.fenbeitong.pay.search.dto.AccountAllFlowReqDTO;
import com.fenbeitong.pay.search.service.AccountAllFlowService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AcctAllFlowServiceImpl{

    @Autowired
    AccountAllFlowService accountAllFlowService;
    @Autowired
    AccountAllFlowMapper accountAllFlowMapper;

    public ResponsePage<AcctFlowStereoPageServiceResqDTO> queryPageAcctAllFlow(AcctFlowStereoPageServiceReqDTO reqDTO) {
        ResponsePage<AcctFlowStereoPageServiceResqDTO> responsePage = new ResponsePage<>();

        FinhubLogger.info("【stereo流水查询】大数据开始");
        long startTime = System.currentTimeMillis();
        AccountAllFlowReqDTO query = new AccountAllFlowReqDTO();
        BeanUtils.copyProperties(reqDTO, query);
        paramsConvert(reqDTO, query);

        /*
          特殊逻辑。如果查询红包券，银行名称不是分贝通，直接返回
          QX 2022-03-20
         */
        if(AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey().equals(query.getFlowFlag())){
            if (ObjUtils.isNotBlank(reqDTO.getFundPlatform()) && !com.google.common.base.Objects.equal( FundPlatformEnum.FBT.getCode(), reqDTO.getFundPlatform())){
                return responsePage.ofNull();
            }
        }

        FinhubLogger.info("【stereo流水查询】查询大数据 req:{}", JsonUtils.toJson(query));
        ResponsePage<AccountAllFlow> flows = accountAllFlowService.queryPage(query);
        FinhubLogger.info("【stereo流水查询】查询大数据 req:{},resp:{}", JsonUtils.toJson(flows));


        responsePage.setTotalCount(flows.getTotalCount());
        if (CollectionUtils.isNotEmpty(flows.getDataList())) {
            // 查询退款流水对应的消费流水
            Map<String, String> bizIdAccountFlowIdMap = Maps.newHashMap();
            List<String> bizIds = flows.getDataList().stream().filter(v -> Integer.valueOf(FundAcctDebitOptType.PUBLIC_CONSUME_REFUND.getKey()).equals(v.getOperationType())).map(v -> v.getBizId()).collect(Collectors.toList());
            if (reqDTO.isOriginAccountFlowIdTag() && CollectionUtils.isNotEmpty(bizIds)) {
                List<AccountAllFlow> bizIdFlow = accountAllFlowMapper.queryByBizIds(reqDTO.getCompanyId(), bizIds);
                bizIdAccountFlowIdMap = bizIdFlow.stream().filter(v -> Integer.valueOf(FundAcctDebitOptType.PUBLIC_CONSUME.getKey()).equals(v.getOperationType())).collect(Collectors.toMap(AccountAllFlow::getBizId, AccountAllFlow::getAccountFlowId, (s1, s2) -> s2));
            }

            List<AcctFlowStereoPageServiceResqDTO> stereoPageServiceResqDTOS = Lists.newArrayList();
            Map<String, String> finalBizIdAccountFlowIdMap = bizIdAccountFlowIdMap;
            flows.getDataList().forEach(f -> {
                AcctFlowStereoPageServiceResqDTO acctFlowRespDTO = new AcctFlowStereoPageServiceResqDTO();

                // 根据入参设置 QX 2022-03-20
                f.setAccountSubType(reqDTO.getAccountSubType());
                BeanUtils.copyProperties(f, acctFlowRespDTO);
                retConvert(f, acctFlowRespDTO);
                // 如果是虚拟卡 QX 2022-03-20
                if(AccountAllFlowFlagEnum.TB_ACCT_COMPANY_CARD_FLOW.getKey().equals(f.getFlowFlag())){
                    // 额度发放 / 额度退还, 展示对手账户详情
                    acctFlowRespDTO.setTargetBankAcctId(f.getTargetBankAcctId());
                    if (ObjUtils.isNotBlank(f.getTargetBankAcctId())) {
                        acctFlowRespDTO.setTargetBankShow(f.getEmployeeName());
                        acctFlowRespDTO.setShowTargetAcct(true);
                        acctFlowRespDTO.setTargetAccount(f.getBankAccountNo());
                        acctFlowRespDTO.setTargetAccountName(f.getEmployeeName());
                        acctFlowRespDTO.setTargetBankName(BankNameEnum.getBankEnum(f.getBankName()).getName());
                        acctFlowRespDTO.setTargetBankAllName(BankNameEnum.getBankEnum(f.getBankName()).getName());
                    }
                    //兼容代码,落流水时有为空数据
                    if (StringUtils.isBlank(acctFlowRespDTO.getOperationTypeDesc())){
                        acctFlowRespDTO.setOperationTypeDesc(FundAcctCompanyCardOptType.getEnum(f.getOperationType()).getValue());
                    }
                }
                if (AccountAllFlowFlagEnum.TB_ACCT_REIMBURSEMENT_FLOW.getKey().equals(f.getFlowFlag())){
                    acctFlowRespDTO.setTargetBankAllName(f.getTargetBankFullName());
                }
                // 如果是汇总账户，设置为不显示 QX 2022-03-24
                if(AccountAllFlowFlagEnum.TB_ACCOUNT_GENERAL_FLOW.getKey().equals(f.getFlowFlag())){
                    acctFlowRespDTO.setShowTargetAcct(true);
                }
                acctFlowRespDTO.setOriginAccountFlowId(finalBizIdAccountFlowIdMap.get(acctFlowRespDTO.getBizNo()));
                stereoPageServiceResqDTOS.add(acctFlowRespDTO);
            });
            responsePage.setDataList(stereoPageServiceResqDTOS);
        }
        if (reqDTO.getIsSummaryAmount() && flows.getTotalAmountData() != null) {
            AccountAllFlow flow = flows.getTotalAmountData();
            AcctFlowStereoPageServiceResqDTO acctFlowStereoPageRespDTO = new AcctFlowStereoPageServiceResqDTO();
            BeanUtils.copyProperties(flow, acctFlowStereoPageRespDTO);
            responsePage.setTotalAmountData(acctFlowStereoPageRespDTO);
        }
        long endTime = System.currentTimeMillis();
        FinhubLogger.info("【stereo流水查询】大数据结束。时间{}, 耗时{}", new Date(endTime), endTime - startTime);
        return responsePage;
    }

    /**
     * 入参转换（提交规范）
     * @param reqDTO
     * @param query
     */
    private void paramsConvert(AcctFlowStereoPageServiceReqDTO reqDTO, AccountAllFlowReqDTO query){
        // 三个字段与接口不一致，转换
        query.setBankName(reqDTO.getFundPlatform());
        query.setCategoryTypes(reqDTO.getOrderTypes());
        query.setBizId(reqDTO.getBizNo());
        query.setFlowFlag(AcctAllFlowConvertDTO.getFlowFlag(reqDTO.getAccountSubType(), reqDTO.getAccountModel(), reqDTO.getFundPlatform()));
        boolean isFlowTableHaveNoAccountSubType = Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_GENERAL_FLOW.getKey(), query.getFlowFlag())
                || Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_PUBLIC_FLOW.getKey(), query.getFlowFlag())
                || Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey(), query.getFlowFlag());
        if (isFlowTableHaveNoAccountSubType){
            query.setAccountSubType(null);
        }
        // 红包券这样，去掉 QX 2022-03-24
        boolean isFlowTableHaveNoBankName = Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey(), query.getFlowFlag());
        if (isFlowTableHaveNoBankName){
            query.setBankName(null);
        }
        boolean isFlowTableHaveNoBankAccountNoAndBankTransNo = Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey(), query.getFlowFlag());
        if (isFlowTableHaveNoBankAccountNoAndBankTransNo){
            query.setBankAccountNo(null);
            query.setBankTransNo(null);
            query.setSyncBankTransNo(null);
        }
        boolean isFlowTableHaveNoOrderType = Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_GENERAL_FLOW.getKey(), query.getFlowFlag());
        if (isFlowTableHaveNoOrderType){
            query.setCategoryTypes(null);
        }
    }

    /**
     * 返回转换
     * @param f
     * @param acctFlowRespDTO
     */
    private void retConvert(AccountAllFlow f, AcctFlowStereoPageServiceResqDTO acctFlowRespDTO){
        // 字段转换
        acctFlowRespDTO.setFundPlatform(f.getBankName());
        acctFlowRespDTO.setTradeTypeName(AcctAllFlowConvertDTO.getTradeTypeName(f.getTradeType())); // 交易类型 描述

        // 业务类型 描述（根据账户类型区分）
        acctFlowRespDTO.setOperationTypeDesc(AcctAllFlowConvertDTO.getOperationTypeDesc(f.getFlowFlag(), f.getOperationType()));
        acctFlowRespDTO.setOrderType(f.getCategoryType());
        if (FundAcctPublicOptType.PAY_CANCEL.getKey() == f.getOperationType()) {
            acctFlowRespDTO.setBizNo(f.getReBizNo());
        } else {
            acctFlowRespDTO.setBizNo(f.getBizId());
            // 平安存rebizNO
            if (BankNameEnum.SPABANK.getCode().equals(f.getBankName())
                && FundAcctPublicOptType.DISHONOURED.getKey() == f.getOperationType()) {
                acctFlowRespDTO.setBizNo(f.getReBizNo());
            }
        }

        /**
         * 对公付款，大数据没有值，页面（对手账户、户名）
         * 大数据做逻辑转换，将原流水表【seller_bank_acct_name】写入到【company_main_name】，与其他流水保持一致
         * QX 2022-03-19
         */
        acctFlowRespDTO.setTargetAccountName(f.getCompanyMainName()); // 目标账户名（对手账户名）

        acctFlowRespDTO.setTargetBankAllName(BankNameEnum.getBankEnum(f.getTargetBankName()).getName()); // 银行名称包括支行


        // 逻辑转换
        acctFlowRespDTO.setShowTargetAcct(AcctAllFlowConvertDTO.getShowTargetAcct(f.getBankAccountNo(), f.getTargetAccount())); // 逻辑字段
        //需要有记账时间的才显示
        if(FundAcctSyncBankStatus.noSyncNew(f.getSyncBankStatus())){
            //银行记账时间
            acctFlowRespDTO.setSyncBankTime(null);
        }

        /*------------ 特殊逻辑--------------*/
        /**
         * 总账户，不应该显示
         * QX 2022-03-19
         */
        if(AccountAllFlowFlagEnum.TB_ACCOUNT_GENERAL_FLOW.getKey().equals(f.getFlowFlag())){
            acctFlowRespDTO.setTargetAccountName(f.getTargetBankAccountName()); // 如果是总账户，对手账户（户名）为空
            acctFlowRespDTO.setTargetAccount(f.getTargetAccount()); // 如果是总账户，对手账户（账号）为空
            acctFlowRespDTO.setTargetBankAllName(null); // 如果是总账户，对手账户（银行）为空
        }
        /**
         * 对公付款，页面没有值
         * QX 2022-03-19
         */
        if(AccountAllFlowFlagEnum.TB_ACCOUNT_PUBLIC_FLOW.getKey().equals(f.getFlowFlag())){
            acctFlowRespDTO.setTargetBankAllName(f.getTargetBankFullName()); // 如果是对公付款账户，对手账户（银行）取这个值
        }
        FinhubLogger.info("【stereo流水查询】问题排查2,数据库流水 {}", JsonUtils.toJson(f));

        // 注意顺序，放到最后。 QX 2022-03-24
        acctFlowRespDTO.setTargetBankShow(AcctAllFlowConvertDTO.getTargetBankShow(f.getBankName(), f.getOperationType(), f.getBankAccountNo(), f.getAccountSubType(),
                f.getTargetAccount(), acctFlowRespDTO.getTargetBankAllName() ,f.getTargetBankName())); // web显示的对手名称

    }
}
