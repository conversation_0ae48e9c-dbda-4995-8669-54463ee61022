package com.fenbeitong.fenbeipay.rpc.service.acct.mgr;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAccountSpaInnerService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AccountSpaInnerFlowReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AccountSpaInnerReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountSpaInnerFlowRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountSpaInnerRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.ISpaInnerAcctService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/10
 */
@Service("iSpaInnerAcctService")
public class ISpaInnerActServiceImpl implements ISpaInnerAcctService {
    @Autowired
    private UAccountSpaInnerService uAccountSpaInnerService;
    @Override
    public ResponsePage<AccountSpaInnerRespDTO> queryInnerAcctByPage(AccountSpaInnerReqDTO accountSpaInnerReqDTO) {
        return uAccountSpaInnerService.queryInnerAcctByPage(accountSpaInnerReqDTO);
    }

    @Override
    public ResponsePage<AccountSpaInnerFlowRespDTO> queryInnerAcctFlowListByPage(AccountSpaInnerFlowReqDTO accountSpaInnerFlowReqDTO) {
        return uAccountSpaInnerService.queryInnerAcctFlowListByPage(accountSpaInnerFlowReqDTO);
    }

    @Override
    public List<AccountSpaInnerRespDTO> queryByBankAccountNo(String bankAccountNo) {
        return uAccountSpaInnerService.queryByBankAccountNo(bankAccountNo);
    }

    @Override
    public AccountSpaInnerRespDTO queryInnerAcctFlowByBankTransNo(String bankTransNo) {
        return uAccountSpaInnerService.queryInnerAcctFlowByBankTransNo(bankTransNo);
    }
}
