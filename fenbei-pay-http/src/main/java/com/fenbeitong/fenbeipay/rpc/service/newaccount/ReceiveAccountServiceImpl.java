package com.fenbeitong.fenbeipay.rpc.service.newaccount;

import com.fenbeitong.fenbeipay.api.base.ThirdCommonRes;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountReceiceConfigRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.newaccount.req.ReceiveAccountDelReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.newaccount.req.ReceiveAccountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.newaccount.resp.AccountReceiveConfigRpcRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.newaccount.resp.ReceiveAccountDetailRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.newaccount.resp.ReceiveAccountRespDTO;
import com.fenbeitong.fenbeipay.api.service.newcount.IReceiveAccountService;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveConfigService;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoDetailService;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoService;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName ReceiveAccountServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/12/293:15 PM
 **/
@Service("iReceiveAccountService")
public class ReceiveAccountServiceImpl implements IReceiveAccountService {
    @Autowired
    private AccountReceiveInfoService accountReceiveInfoService;
    @Autowired
    private AccountReceiveInfoDetailService accountReceiveInfoDetailService;
    @Autowired
    private AccountReceiveConfigService accountReceiveConfigService;

    @Override
    public ThirdCommonRes<List<ReceiveAccountRespDTO>> batchInsertReceiveAccount(List<ReceiveAccountReqDTO> receiveAcountInsertDTOList) {
        return accountReceiveInfoDetailService.batchAddEmployeeAccount(receiveAcountInsertDTOList, true);
    }

    @Override
    public ThirdCommonRes<List<ReceiveAccountRespDTO>> batchUpdateReceiveAccount(List<ReceiveAccountReqDTO> receiveAccountUdpateDTOList) {
        return accountReceiveInfoDetailService.batchUpdateEmployeeAccount(receiveAccountUdpateDTOList, true);
    }

    @Override
    public ThirdCommonRes<List<ReceiveAccountRespDTO>> batchDelReceiveAccount(List<ReceiveAccountDelReqDTO> receiveAccountDelDTOList) {
        return accountReceiveInfoDetailService.batchRemoveEmployeeAccount(receiveAccountDelDTOList);
    }

    @Override
    public List<ReceiveAccountDetailRespDTO> queryReceiveAccounts(String companyId, List<String> userIds) {
        return accountReceiveInfoDetailService.getEmployeeAccountListByType(companyId, userIds);
    }

    @Override
    public AccountReceiveConfigRpcRespDTO queryAccountReceiveConfig(String companyId) {
        AccountReceiceConfigRespDTO respDTO = accountReceiveConfigService.queryReceiveConfig(companyId,null);
        if (ObjUtils.isNotEmpty(respDTO)){
            AccountReceiveConfigRpcRespDTO result = new AccountReceiveConfigRpcRespDTO();
            BeanUtils.copyProperties(respDTO,result);
            return result;
        }
        return null;
    }
}
