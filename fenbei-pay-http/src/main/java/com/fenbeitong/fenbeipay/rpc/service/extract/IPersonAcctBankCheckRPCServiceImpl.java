package com.fenbeitong.fenbeipay.rpc.service.extract;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.PersonAcctEnum;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.PersonAcctBankCheckProcessReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.PersonAcctBankCheckSearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.PersonAcctBankCheckSearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.extract.IPersonAcctBankCheckRPCService;
import com.fenbeitong.fenbeipay.dto.extract.AcctPersonBankCheck;
import com.fenbeitong.fenbeipay.extract.db.mapper.AcctPersonBankCheckMapper;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: guogx
 * @Date: 2022/11/3 下午9:47
 */
@Service("iPersonAcctBankCheckRPCService")
public class IPersonAcctBankCheckRPCServiceImpl implements IPersonAcctBankCheckRPCService {

    @Resource
    private AcctPersonBankCheckMapper acctPersonBankCheckMapper;

    @Override
    public ResponsePage<PersonAcctBankCheckSearchRespRPCDTO> queryPage(PersonAcctBankCheckSearchReqRPCDTO searchReqRPCDTO) {
        FinhubLogger.info("IPersonAcctBankCheckRPCServiceImpl#queryPage#req:{}", JSON.toJSON(searchReqRPCDTO));
        ResponsePage<PersonAcctBankCheckSearchRespRPCDTO> respPage = new ResponsePage<>();
        Example example = getExample(searchReqRPCDTO);
        int count = acctPersonBankCheckMapper.selectCountByExample(example);
        respPage.setTotalCount(count);
        FinhubLogger.info("IPersonAcctBankCheckRPCServiceImpl#queryPage#resp.totalCount:{}", count);
        if(count > 0){
            PageHelper.startPage(searchReqRPCDTO.getPageNo(), searchReqRPCDTO.getPageSize());
            List<AcctPersonBankCheck> acctPersonBankCheckList = acctPersonBankCheckMapper.selectByExample(example);
            List<PersonAcctBankCheckSearchRespRPCDTO> dtoList = acctPersonBankCheckList.stream().map(item -> convertPo2Dto(item)).collect(Collectors.toList());
            respPage.setDataList(dtoList);
        }
        return respPage;
    }



    @Override
    public boolean extractProcess(PersonAcctBankCheckProcessReqDTO processReqDTO) {
        Example example = new Example(AcctPersonBankCheck.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("extractId", processReqDTO.getExtractId());
        AcctPersonBankCheck acctPersonBankCheck = new AcctPersonBankCheck();
        acctPersonBankCheck.setRemark(processReqDTO.getRemark());
        acctPersonBankCheck.setProcessStatus(processReqDTO.getProcessStatus());
        int result = acctPersonBankCheckMapper.updateByExampleSelective(acctPersonBankCheck, example);
        return result==1;
    }


    private Example getExample(PersonAcctBankCheckSearchReqRPCDTO searchReqRPCDTO) {
        Example example = new Example(AcctPersonBankCheck.class);
        Example.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotBlank(searchReqRPCDTO.getEmployeeName())){
            criteria.andEqualTo("employeeName", searchReqRPCDTO.getEmployeeName());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getEmployeePhone())){
            criteria.andEqualTo("employeePhone", searchReqRPCDTO.getEmployeePhone());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getCompanyName())){
            criteria.andEqualTo("companyName", searchReqRPCDTO.getCompanyName());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getBankName())){
            criteria.andEqualTo("bankName", searchReqRPCDTO.getBankName());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getCompanyBankAcctId())){
            criteria.andEqualTo("companyBankAcctId", searchReqRPCDTO.getCompanyBankAcctId());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getCompanyBankAccountNo())){
            criteria.andEqualTo("companyBankAccountNo", searchReqRPCDTO.getCompanyBankAccountNo());
        }
        if(StringUtils.isNotBlank(searchReqRPCDTO.getBankAccountNo())){
            criteria.andEqualTo("bankAccountNo", searchReqRPCDTO.getBankAccountNo());
        }
        String billTimeFrom = formatBillTime(searchReqRPCDTO.getBillTimeFrom());
        if(StringUtils.isNotBlank(billTimeFrom)){
            criteria.andGreaterThanOrEqualTo("extractTime", billTimeFrom);
        }
        String billTimeTo = formatBillTime(searchReqRPCDTO.getBillTimeTo());
        if(StringUtils.isNotBlank(billTimeTo)){
            criteria.andLessThanOrEqualTo("extractTime", billTimeTo);
        }
        if(searchReqRPCDTO.getAccountType() != null){
            criteria.andEqualTo("accountType", searchReqRPCDTO.getAccountType());
        }
        if(searchReqRPCDTO.getExtractStatus() != null){
            criteria.andEqualTo("extractStatus", searchReqRPCDTO.getExtractStatus());
        }
        if(searchReqRPCDTO.getProcessStatus() != null){
            criteria.andEqualTo("processStatus", searchReqRPCDTO.getProcessStatus());
        }
        example.orderBy("id").desc();
        return example;
    }

    private PersonAcctBankCheckSearchRespRPCDTO convertPo2Dto(AcctPersonBankCheck acctPersonBankCheck){
        PersonAcctBankCheckSearchRespRPCDTO personAcctBankCheckSearchRespRPCDTO = new PersonAcctBankCheckSearchRespRPCDTO();
        BeanUtils.copyProperties(acctPersonBankCheck, personAcctBankCheckSearchRespRPCDTO);
        personAcctBankCheckSearchRespRPCDTO.setBillTime(acctPersonBankCheck.getExtractTime());
        personAcctBankCheckSearchRespRPCDTO.setCreateTimeStr(DateUtil.formatDateTime(acctPersonBankCheck.getCreateTime()));
        personAcctBankCheckSearchRespRPCDTO.setAccountTypeShow(PersonAcctEnum.AccountType.getEnum(acctPersonBankCheck.getAccountType()).msg());
        personAcctBankCheckSearchRespRPCDTO.setBankNameShow(BankNameEnum.getBankEnum(acctPersonBankCheck.getBankName()).getName());
        personAcctBankCheckSearchRespRPCDTO.setExtractStatusShow(PersonAcctEnum.ExtractStatus.getEnum(acctPersonBankCheck.getExtractStatus()).msg());
        personAcctBankCheckSearchRespRPCDTO.setProcessStatusShow(PersonAcctEnum.ProcessStatus.getEnum(acctPersonBankCheck.getProcessStatus()).msg());
        return personAcctBankCheckSearchRespRPCDTO;
    }

    private String formatBillTime(String billTime){
        try{
            if(StringUtils.isNotBlank(billTime)){
                //2022-11-06 00:00:00
                billTime = billTime.substring(0,10).replaceAll("-","");
            }
        }catch (Exception e){
            FinhubLogger.error("IPersonAcctBankCheckRPCServiceImpl#formatBillTime#error#req:{}", billTime, e);
        }
        return billTime;
    }


}
