package com.fenbeitong.fenbeipay.rpc.service.sas;

import com.fenbeitong.fenbeipay.api.model.dto.sas.req.PayPwdVerifyReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.sas.req.SasSecurityVerifyReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.sas.resp.SasSecurityVerifyRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.sas.resp.SasVerifyResultRespDTO;
import com.fenbeitong.fenbeipay.api.service.sas.IPayPwdService;
import com.fenbeitong.fenbeipay.api.util.AESUtils;
import com.fenbeitong.fenbeipay.api.util.RSAUtils;
import com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant;
import com.fenbeitong.fenbeipay.core.enums.auth.AuthCode;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.sas.ConfirmPayPwdRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.PayPasswordReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryPayPwdRespVo;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasEmployeePayPwdManager;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasPayCaptchaManager;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasSecurityVerifyManager;
import com.fenbeitong.fenbeipay.sas.pwd.query.manager.SearchEmployeePayPwdManager;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.VersionUtils;
import com.fenbeitong.usercenter.api.model.enums.common.CommonAuthCodeEnums;
import com.fenbeitong.usercenter.api.service.common.ICommonService;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Base64;
import java.util.Map;

/**
 * @Description: 支付密码逻辑
 * @ClassName: IPayPwdService
 * @Author: wh
 * @Version: 3.7.1
 */
@Service("iPayPwdService")
public class IPayPwdServiceImpl implements IPayPwdService {
    //比较的版本
    private final static String COMPARE_VERSION = "3.7.1";

    @Autowired
    private SearchEmployeePayPwdManager searchEmployeePayPwdManager;

    @Autowired
    private SasEmployeePayPwdManager sasEmployeePayPwdManager;

    @Autowired
    private SasPayCaptchaManager sasPayCaptchaManager;

    @Autowired
    private SasSecurityVerifyManager securityVerifyManager;

    @Autowired
    private ICommonService iCommonService;

    @Value("${pay.password.rsa.privateKey}")
    private String rsaPrivateKey;

    @Value("${pay.password.aes.key}")
    private String aesKey;

    private static final String ENCRYPT_RSA = "02";

    @Autowired
    private DingDingMsgService dingDingMsgService;

    @Override
    public void verifyPayPwd(String version,String companyId, String employeeId, String pwd, String encryptType) {
        try {
            FinhubLogger.info("需要验证支付密码：版本{},公司{},员工{}",version,companyId,employeeId);
            //企业没开支付密码权限不用验证支付密码
            if (ObjUtils.isNotEmpty(companyId)&&verifyPwdPayAuth(companyId,employeeId)){ return; }
            //查询是否存在密码
            QueryPayPwdRespVo queryPayPwdRespVo = searchEmployeePayPwdManager.queryExistPayPassword(employeeId);
            //不存在支付密码：3.7.1前可以过。等于3.7.1和之后升级版本
            if (ObjUtils.isEmpty(queryPayPwdRespVo) || !queryPayPwdRespVo.getIsExistPwd()) {
                if (VersionUtils.lessThan(version, COMPARE_VERSION)) {
                    return;
                }
                throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_PAY_ADD_PWD);
            }
            //存在支付密码：3.7.1前升级版本。等于3.7.1和之后做密码验证
            if (VersionUtils.lessThan(version, COMPARE_VERSION)) {
                throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_UPGRADE_VERSION);
            }

            if (!StringUtils.isEmpty(pwd) && ENCRYPT_RSA.equals(encryptType)) {
                pwd = decryptRSAPwd(pwd);
            }
            PayPasswordReqVo passwordReqVo = PayPasswordReqVo.builder().
                    employeePwd(pwd)
                    .build();
            ConfirmPayPwdRespVo confirmPayPwdRespVo = sasEmployeePayPwdManager.confirmPayPassword(passwordReqVo, employeeId);
            //验证密码
            if (!confirmPayPwdRespVo.isCorrectConfirm) {
                throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_OPERATION_ERROR);
            }
        } catch (FinPayException e) {
            FinhubLogger.warn("【支付功能密码输入异常】verifyPayPwd 参数={}:{}:{}:{}", version, companyId, employeeId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【支付功能密码输入验证异常】verifyPayPwd 参数={}:{}:{}:{}", version, companyId, employeeId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                    GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("【支付功能密码输入验证异常】verifyPayPwd 参数={}:{}:{}:{}", version, companyId, employeeId, e);
            throw e;
        } catch (Exception e) {
            FinhubLogger.error("【支付功能密码输入系统异常】verifyPayPwd 参数={}:{}:{}:{}", version, companyId, employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                    GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public void verifyPaymentPayPwd(String companyId, String employeeId, String pwd, String encryptType) {

        try {
            FinhubLogger.info("验证支付密码：companyId={}, employeeId={}",companyId,employeeId);
            //企业没开支付密码权限不用验证支付密码
            if (ObjUtils.isNotEmpty(companyId)&&verifyPwdPayAuth(companyId,employeeId)){ return; }
            //查询是否存在密码
            QueryPayPwdRespVo queryPayPwdRespVo = searchEmployeePayPwdManager.queryExistPayPassword(employeeId);
            if (ObjUtils.isEmpty(queryPayPwdRespVo) || !queryPayPwdRespVo.getIsExistPwd()) {

                throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_EMPTY);
            }

            if (!StringUtils.isEmpty(pwd) && ENCRYPT_RSA.equals(encryptType)) {
                pwd = decryptRSAPwd(pwd);
            }

            PayPasswordReqVo passwordReqVo = PayPasswordReqVo.builder().
                employeePwd(pwd)
                .build();
            ConfirmPayPwdRespVo confirmPayPwdRespVo = sasEmployeePayPwdManager.confirmPayPassword(passwordReqVo, employeeId);
            //验证密码
            if (!confirmPayPwdRespVo.isCorrectConfirm) {
                throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_OPERATION_ERROR);
            }
        } catch (FinPayException e) {
            FinhubLogger.error("【验证支付密码异常】verifyPayPwd 参数={}:{}:{}", companyId, employeeId, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【验证支付密码异常】verifyPayPwd 参数={}:{}:{}", companyId, employeeId, e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),
                GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【验证支付密码系统异常】verifyPayPwd 参数={}:{}:{}", companyId, employeeId, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(),
                GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @Override
    public boolean queryIsExistPayPassword(String employeeId) {
        QueryPayPwdRespVo queryPayPwdRespVo = searchEmployeePayPwdManager.queryExistPayPassword(employeeId);
        return queryPayPwdRespVo.getIsExistPwd();
    }

    @Override
    public SasVerifyResultRespDTO queryVerifyResult(PayPwdVerifyReqDTO payPwdVerifyReqDTO) {
        SasVerifyResultRespDTO resultRespDTO = new SasVerifyResultRespDTO();
        String businessId = payPwdVerifyReqDTO.getBusinessId();
        String employeeId = payPwdVerifyReqDTO.getEmployeeId();

        resultRespDTO.setIsPwdVerifyPass(sasEmployeePayPwdManager.queryIsVerifyPwdPass(businessId, employeeId));
        resultRespDTO.setIsCaptchaVerifyPass(sasPayCaptchaManager.queryIsVerifyCaptchaPass(businessId, employeeId));

        return resultRespDTO;
    }

    @Override
    public SasSecurityVerifyRespDTO queryIsSecurityVerifyPass(SasSecurityVerifyReqDTO securityVerifyReqDTO) {
        return securityVerifyManager.queryIsSecurityVerifyPass(securityVerifyReqDTO);
    }

    /**
     * RSA解密处理，返回AES加密密文
     * @return
     */
    private String decryptRSAPwd(String pwd) {
        try {
            //由于老流程是APP端AES加密，服务端将AES密文加盐，并SHA512加密生成密文并存储
            //所以为了兼容老数据，APP端将密码RSA公钥加密后，服务端私钥解密，然后AES加密，再将AES密文加盐，并SHA512加密生成密文并存储
            byte[] decrypt = RSAUtils.decrypt(Base64.getDecoder().decode(pwd), rsaPrivateKey);
            return AESUtils.EnCode(new String(decrypt), aesKey);
        } catch (Exception e) {
            FinhubLogger.error("【支付密码RSA处理失败】pwd 参数={}:{}", pwd, e);
            dingDingMsgService.sendMsg("【支付密码RSA处理失败】" + e.toString());
            throw new FinPayException(GlobalResponseCode.PASSWORD_DECRYPT_FAIL);
        }
    }


    /***
     * @Description: 验证支付密码支付权限
     * @Param: [companyId, employeeId]
     */
    private boolean verifyPwdPayAuth(String companyId, String employeeId) {
        try {
            Boolean isFbqPayAuth = false;
            Map<String, Integer> companyEmployeeAuth = iCommonService.queryCompanyEmployeeAuth(companyId, employeeId,
                    CommonAuthCodeEnums.PAYMENT_CODE.getCommonAuthType().getKey(), AuthCode.pwdPayCode());
            FinhubLogger.info("验证支付密码权限返回结构{},{},{}", companyId, employeeId, JsonUtils.toJson(companyEmployeeAuth));
            //返回map对象 0:无权限 1:有权限
            if (PayCenterConstant.PWD_PAY_NO_EXIST_AUTH == companyEmployeeAuth.get(CommonAuthCodeEnums.PAYMENT_CODE.getKey())) {
                isFbqPayAuth = true;
            }
            return isFbqPayAuth;
        } catch (Exception e) {
            FinhubLogger.error("验证支付密码权限参数{} 失败：{}", companyId, employeeId, e);
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_VERIFY_AUTH_PBQ_ERROR);
        }
    }

}
