package com.fenbeitong.fenbeipay.rpc.service.cashier;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessCreditService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessDebitService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctIndividualCreditService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctIndividualDebitService;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayStatus;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderRootTypeEnum;
import com.fenbeitong.fenbeipay.api.model.ResultDTO;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.*;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementService;
import com.fenbeitong.fenbeipay.cashier.exception.CashierOrderException;
import com.fenbeitong.fenbeipay.cashier.manager.CashierOrderCostAttributionManager;
import com.fenbeitong.fenbeipay.cashier.manager.CashierOrderSettlementRelationManager;
import com.fenbeitong.fenbeipay.cashier.manager.KafkaCashierPayManager;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.cashier.settlement.PublicPayOrderSettlementService;
import com.fenbeitong.fenbeipay.core.annotation.CashierTrailCompanyRejectAnnotation;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementRelation;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.core.model.vo.vouchers.VoucherPayDTO;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.FinPayNoMsgException;
import com.fenbeitong.fenbeipay.core.utils.FinhubExceptionUtil;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FinhubMessageType;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.dto.orgunit.OrgUnitResult;
import com.fenbeitong.usercenter.api.service.orgunit.IOrgUnitService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.RandomUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.BANK_SPA_OFF_ERROR_OVER;

@Service("iCashierOrderSettlementService")
public class ICashierOrderSettlementServiceImpl implements ICashierOrderSettlementService {

    @Autowired
    private CashierOrderSettlementService cashierOrderSettlementService;

    @Autowired
    private PublicPayOrderSettlementService publicPayOrderSettlementService;

    @Autowired
    private KafkaCashierPayManager kafkaCashierPayManager;

    @Autowired
    protected RedissonService redissonService;

    @Autowired
    protected UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    protected UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    protected UAcctIndividualDebitService uAcctIndividualDebitService;

    @Autowired
    protected UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    protected CashierOrderSettlementRelationManager cashierOrderSettlementRelationManager;

    @Autowired
    protected DingDingMsgService dingDingMsgService;

    @Autowired
    protected CashierOrderCostAttributionManager cashierOrderCostAttributionManager;

    @Autowired
    private IOrgUnitService iOrgUnitService;
    @Autowired
    private CashierOrderRefundSettlementService cashierOrderRefundSettlementService;

    /**
     * 强制解锁时间设置
     */
    private final long lockTimeRefund = 10000L;

    /**
     * 等待时间
     **/
    private final long waitTimeRefund = 400L;

    private static final String RPC_EXCEPTION_MSG = "【收银台】【RPC创建支付交易异常】：{}";

    private static final String RPC_CREATE_PAY_EXCEPTION_MSG = "【收银台】【RPC创建并自动支付异常】：{}";

    private static final String RPC_PAY_EXCEPTION_MSG = "【收银台】【RPC支付异常】：{}";

    private static final String RPC_CANCEL_EXCEPTION_MSG = "【收银台】【RPC取消支付异常】：{}";

    private static final String RPC_ACK_PAY_EXCEPTION_MSG = "【收银台】【RPC回填单创建并支付扣除预算异常】：{}";

    private static final String RPC_CANCEL_DISC_EXCEPTION_MSG = "【收银台】【RPC取消减免单异常】：{}";

    private static final String RELIEF_EXCEPTION_MSG = "【收银台】【RPC查询减免单原单信息参数异常】：{}";

    @Override
    public PersonFBBQAbilityRPCDTO payBQAbility(PersonFBBQAbilityRPCVo personFBBQAbilityRPCVo) {
        PersonFBBQAbilityReqVo personFBBQAbilityReqVo = new PersonFBBQAbilityReqVo();
        BeanUtils.copyProperties(personFBBQAbilityRPCVo, personFBBQAbilityReqVo);
        PersonFBBQAbilityRespVo personFBBQAbilityRespVo = cashierOrderSettlementService.payBQAbility(personFBBQAbilityReqVo);
        PersonFBBQAbilityRPCDTO personFBBQAbilityRPCDTO = new PersonFBBQAbilityRPCDTO();
        BeanUtils.copyProperties(personFBBQAbilityRespVo, personFBBQAbilityRPCDTO);
        return personFBBQAbilityRPCDTO;
    }

    @Override
    public CashierCreateTradeRPCDTO createOrderTrade(CashierCreateTradeRPCVo cashierCreateTradeRPCVo) {
        FinhubLogger.info("【收银台】【RPC创建支付交易参数】：{}", cashierCreateTradeRPCVo.toString());
        CashierCreateTradeReqVo cashierCreateTradeReqVo = new CashierCreateTradeReqVo();
        BeanUtils.copyProperties(cashierCreateTradeRPCVo, cashierCreateTradeReqVo);
        CashierCreateTradeRespVo cashierCreateTradeResVo = null;
        try {
            cashierCreateTradeResVo = cashierOrderSettlementService.createOrderTradeAndSaas(cashierCreateTradeReqVo);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn(RPC_EXCEPTION_MSG, cashierCreateTradeRPCVo.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error(RPC_EXCEPTION_MSG, cashierCreateTradeRPCVo.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error(RPC_EXCEPTION_MSG, cashierCreateTradeRPCVo.toString(), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.error(RPC_EXCEPTION_MSG, cashierCreateTradeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error(RPC_EXCEPTION_MSG, cashierCreateTradeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        //通知
        CashierCreateTradeRespVo finalCashierCreateTradeResVo = cashierCreateTradeResVo;
        CompletableFuture.runAsync(() -> cashierOrderSettlementService.callCashierSettlement(finalCashierCreateTradeResVo.getFbOrderId(), finalCashierCreateTradeResVo.getCashierTxnId(), finalCashierCreateTradeResVo.getEmployeeId()));
        CashierCreateTradeRPCDTO cashierCreateTradeDTO = new CashierCreateTradeRPCDTO();
        BeanUtils.copyProperties(cashierCreateTradeResVo, cashierCreateTradeDTO);
        return cashierCreateTradeDTO;
    }

    @Override
    public CashierPayTradeRPCDTO payOrderTrade(CashierPayTradeRPCVo cashierPayTradeRPCVo) {
        FinhubLogger.info("【收银台】【RPC支付参数】：{}", cashierPayTradeRPCVo.toString());
        CashierPayTradeReqVo cashierPayTradeReqVo = cashierPayTradeReqVo4VoucherBuilder(cashierPayTradeRPCVo);
        CashierPayTradeRespVo cashierPayTradeRespVo = null;

        try {
            cashierPayTradeRespVo = cashierOrderSettlementService.payOrderTradeAndSaas(cashierPayTradeReqVo);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn(RPC_PAY_EXCEPTION_MSG, cashierPayTradeRPCVo.toString(), accountEx);
            throw accountEx;
        } catch (FinPayNoMsgException payEx) {
            FinhubLogger.warn(RPC_PAY_EXCEPTION_MSG, cashierPayTradeRPCVo.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (FinPayException payEx) {
            FinhubLogger.error(RPC_PAY_EXCEPTION_MSG, cashierPayTradeRPCVo.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error(RPC_PAY_EXCEPTION_MSG, cashierPayTradeRPCVo.toString(), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.error(RPC_PAY_EXCEPTION_MSG, cashierPayTradeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error(RPC_PAY_EXCEPTION_MSG, cashierPayTradeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        //通知
        CashierPayTradeRespVo finalCashierPayTradeRespVo = cashierPayTradeRespVo;
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.callCashierSettlement(finalCashierPayTradeRespVo.getFbOrderId(), finalCashierPayTradeRespVo.getCashierTxnId(), finalCashierPayTradeRespVo.getEmployeeId());
        });
        CashierPayTradeRPCDTO cashierPayTradeRPCDTO = new CashierPayTradeRPCDTO();
        BeanUtils.copyProperties(cashierPayTradeRespVo, cashierPayTradeRPCDTO);

        // 【add children list】
        // copy子单
        try {
            if (cashierPayTradeRespVo.getChildrenList() != null) {
                FinhubLogger.info("CashierPayTradeRPCDTO append childrenList " + cashierPayTradeRespVo.getFbOrderId());
                List<CashierPayTradeRPCDTO> childrenList = Lists.newArrayList();
                List<CashierOrderSettlement> sourceList = cashierPayTradeRespVo.getChildrenList();
                for (CashierOrderSettlement order : sourceList) {
                    CashierPayTradeRPCDTO child = new CashierPayTradeRPCDTO();
                    BeanUtils.copyProperties(order, child);
                    if (child.getAmountCompany() == null) {
                        child.setAmountCompany(BigDecimal.ZERO);
                    }
                    if (child.getAmountRedcoupon() == null) {
                        child.setAmountRedcoupon(BigDecimal.ZERO);
                    }
                    childrenList.add(child);
                }
                cashierPayTradeRPCDTO.setChildrenList(childrenList);
            }
        } catch (Exception e) {
            FinhubLogger.error(e.getMessage(), e);
        }
        return cashierPayTradeRPCDTO;
    }

    @Override
    public CashierChangePriceTradeRespDTO changePriceOrderTrade(CashierChangePriceTradeRPCVo cashierChangePriceTradeRPCVo) {
        FinhubLogger.info("【收银台】【RPC未支付收银单改价参数】：{}", cashierChangePriceTradeRPCVo.toString());
        CashierChangePriceTradeReqVo requestVo = new CashierChangePriceTradeReqVo();
        BeanUtils.copyProperties(cashierChangePriceTradeRPCVo, requestVo);
        try {
            CashierChangePriceTradeRespVo changePriceRespVo = cashierOrderSettlementService.changePriceOrderTrade(requestVo);
            CashierChangePriceTradeRespDTO changePriceRespDTO = new CashierChangePriceTradeRespDTO();
            BeanUtils.copyProperties(changePriceRespVo, changePriceRespDTO);
            return changePriceRespDTO;
        }  catch (FinPayException payEx) {
            FinhubLogger.error("【收银台】【RPC未支付收银单改价异常】：{}", cashierChangePriceTradeRPCVo.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        }  catch (ValidateException e) {
            FinhubLogger.error("【收银台】【RPC未支付收银单改价参数校验异常】：{}", cashierChangePriceTradeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        }  catch (Exception e) {
            FinhubLogger.error("【收银台】【RPC未支付收银单改价异常】：{}", cashierChangePriceTradeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * VO 转 DTO
     * @param cashierPayTradeRPCVo cashierPayTradeRPCVo
     */
    private CashierPayTradeReqVo cashierPayTradeReqVo4VoucherBuilder(CashierPayTradeRPCVo cashierPayTradeRPCVo){
        CashierPayTradeReqVo cashierPayTradeReqVo = new CashierPayTradeReqVo();
        BeanUtils.copyProperties(cashierPayTradeRPCVo, cashierPayTradeReqVo);
        if (ObjUtils.isNotEmpty(cashierPayTradeRPCVo.getVoucherPayRPCVos())) {
            List<VoucherPayDTO> voucherPayDTOS = cashierPayTradeRPCVo.getVoucherPayRPCVos().stream().map(voucherPayRPCVo -> {
                VoucherPayDTO voucherPayDTO = new VoucherPayDTO();
                BeanUtils.copyProperties(voucherPayRPCVo, voucherPayDTO);
                return voucherPayDTO;
            }).collect(Collectors.toList());
            cashierPayTradeReqVo.setVoucherPayDTOList(voucherPayDTOS);
        }
        return cashierPayTradeReqVo;
    }

    @Override
    public CashierCancelTradeRPCDTO cancelOrderTrade(CashierCancelTradePPCVo cashierCancelTradePPCVo) {
        FinhubLogger.info("【收银台】【RPC取消支付参数】：{}", cashierCancelTradePPCVo.toString());
        cashierCancelTradePPCVo = ValidateUtils.validate(cashierCancelTradePPCVo);
        CashierCancelTradeReqVo cashierCancelTradeReqVo = new CashierCancelTradeReqVo();
        BeanUtils.copyProperties(cashierCancelTradePPCVo, cashierCancelTradeReqVo);
        //校验参数
        cashierCancelTradeReqVo = ValidateUtils.validate(cashierCancelTradeReqVo);
        CashierCancelTradeRespVo cashierCancelTradeResVo = null;
        try {
            cashierCancelTradeResVo = cashierOrderSettlementService.cancelOrderTradeAndRefundSaas(cashierCancelTradeReqVo);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn(RPC_CANCEL_EXCEPTION_MSG, cashierCancelTradePPCVo.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error(RPC_CANCEL_EXCEPTION_MSG, cashierCancelTradePPCVo.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error(RPC_CANCEL_EXCEPTION_MSG, cashierCancelTradePPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error(RPC_CANCEL_EXCEPTION_MSG, cashierCancelTradePPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        CashierCancelTradeRPCDTO cashierCancelTradeDTO = new CashierCancelTradeRPCDTO();
        BeanUtils.copyProperties(cashierCancelTradeResVo, cashierCancelTradeDTO);
        return cashierCancelTradeDTO;
    }

    /**
     * 创建支付交易流水+支付完成+返回支付结果+无管控预算
     * 使用场景：回填单+审批单，只能有商务账户(因公)扣款
     * 创建支付流水号，并且支付后，直接返回支付结果，没有支付回掉，没有APP上用户点击"立即支付"
     *
     * @param createPayTradePPCVo
     * @return
     */
    @Override
    public CashierCreatePayTradeRPCDTO createAndPayOrderTrade(CashierCreatePayTradeRPCVo createPayTradePPCVo) {
        return this.createAndPayOrderTrade(createPayTradePPCVo, false);
    }

    /**
     * 创建支付交易流水+支付完成+返回支付结果+扣除管控预算
     * 使用场景：回填单+审批单，只能有商务账户(因公)扣款
     * 创建支付流水号，并且支付后，直接返回支付结果，没有支付回掉，没有APP上用户点击"立即支付"
     *
     * @param createPayTradePPCVo
     * @return
     */
    @Override
    public CashierCreatePayTradeRPCDTO createAndPayOrderTradeSass(CashierCreatePayTradeRPCVo createPayTradePPCVo) {
        return this.createAndPayOrderTrade(createPayTradePPCVo, true);
    }

//    @Override
//    public CashierCreatePayTradeRPCDTO createAndPayOrderTradeSass(CashierCreatePayTradeRPCVo createPayTradePPCVo) {
//        FinhubLogger.info("【收银台】【RPC回填单创建并支付参数】：{}", createPayTradePPCVo.toString());
//        createPayTradePPCVo = ValidateUtils.validate(createPayTradePPCVo);
//        CashierCreatePayTradeReqVo createPayTradeReqVo = new CashierCreatePayTradeReqVo();
//        BeanUtils.copyProperties(createPayTradePPCVo, createPayTradeReqVo);
//        //校验参数
//        ValidateUtils.validate(createPayTradeReqVo);
//        boolean saasBudget = false;
//        CashierCreatePayTradeRespVo createPayTradeRespVo = null;
//        try {
//            createPayTradeRespVo = cashierOrderSettlementService.createAndPayOrderTradeOrSass(createPayTradeReqVo, saasBudget);
//        } catch (FinAccountNoEnoughException accountEx) {
//            FinhubLogger.warn("【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), accountEx);
//            throw accountEx;
//        } catch (FinPayException payEx) {
//            FinhubLogger.error("【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), payEx);
//            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
//        } catch (ValidateException e) {
//            FinhubLogger.error("【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), e);
//            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
//        } catch (Exception e) {
//            FinhubLogger.error("【收银台】【RPC回填单创建并支付异常】：{}", createPayTradePPCVo.toString(), e);
//            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
//        }
//        CashierCreatePayTradeRPCDTO createPayTradeRPCDTO = new CashierCreatePayTradeRPCDTO();
//        BeanUtils.copyProperties(createPayTradeRespVo, createPayTradeRPCDTO);
//        CompletableFuture.runAsync(() -> cashierOrderSettlementService.callCashierSettlement(createPayTradeRPCDTO.getFbOrderId(), createPayTradeRPCDTO.getCashierTxnId(), createPayTradeRPCDTO.getEmployeeId()));
//
//        // 【add children list】
//        // copy子单
//        try {
//            if (createPayTradeRespVo.getChildrenList() != null) {
//                List<CashierPayTradeRPCDTO> childrenList = Lists.newArrayList();
//                List<CashierOrderSettlement> sourceList = createPayTradeRespVo.getChildrenList();
//                for (CashierOrderSettlement order : sourceList) {
//                    CashierPayTradeRPCDTO child = new CashierPayTradeRPCDTO();
//                    BeanUtils.copyProperties(order, child);
//                    if (child.getAmountCompany() == null) {
//                        child.setAmountCompany(BigDecimal.ZERO);
//                    }
//                    if (child.getAmountRedcoupon() == null) {
//                        child.setAmountRedcoupon(BigDecimal.ZERO);
//                    }
//                    childrenList.add(child);
//                }
//                createPayTradeRPCDTO.setChildrenList(childrenList);
//            }
//        } catch (Exception e) {
//            FinhubLogger.error(e.getMessage(), e);
//        }
//
//        return createPayTradeRPCDTO;
//    }

    private CashierCreatePayTradeRPCDTO createAndPayOrderTrade(CashierCreatePayTradeRPCVo createPayTradePPCVo, boolean saasBudget) {
        FinhubLogger.info("【收银台】【RPC回填单创建并支付（扣除预算：{}）参数】：{}", saasBudget, createPayTradePPCVo.toString());
        ValidateUtils.validate(createPayTradePPCVo);
        CashierCreatePayTradeReqVo createPayTradeReqVo = new CashierCreatePayTradeReqVo();
        BeanUtils.copyProperties(createPayTradePPCVo, createPayTradeReqVo);
        //校验参数
        ValidateUtils.validate(createPayTradeReqVo);
        CashierCreatePayTradeRespVo createPayTradeRespVo = null;
        try {
            createPayTradeRespVo = cashierOrderSettlementService.createAndPayOrderTradeOrSass(createPayTradeReqVo, saasBudget);
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn(RPC_ACK_PAY_EXCEPTION_MSG, createPayTradePPCVo.toString(), e);
            throw e;
        } catch (FinPayException e) {
            FinhubLogger.error(RPC_ACK_PAY_EXCEPTION_MSG, createPayTradePPCVo.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error(RPC_ACK_PAY_EXCEPTION_MSG, createPayTradePPCVo.toString(), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.error(RPC_ACK_PAY_EXCEPTION_MSG, createPayTradePPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error(RPC_ACK_PAY_EXCEPTION_MSG, createPayTradePPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        CashierCreatePayTradeRPCDTO createPayTradeRPCDTO = new CashierCreatePayTradeRPCDTO();
        BeanUtils.copyProperties(createPayTradeRespVo, createPayTradeRPCDTO);
        CompletableFuture.runAsync(() -> cashierOrderSettlementService.callCashierSettlement(createPayTradeRPCDTO.getFbOrderId(), createPayTradeRPCDTO.getCashierTxnId(), createPayTradeRPCDTO.getEmployeeId()));

        // 【add children list】
        // copy子单
        try {
            if (createPayTradeRespVo.getChildrenList() != null) {
                List<CashierPayTradeRPCDTO> childrenList = Lists.newArrayList();
                List<CashierOrderSettlement> sourceList = createPayTradeRespVo.getChildrenList();
                for (CashierOrderSettlement order : sourceList) {
                    CashierPayTradeRPCDTO child = new CashierPayTradeRPCDTO();
                    BeanUtils.copyProperties(order, child);
                    if (child.getAmountCompany() == null) {
                        child.setAmountCompany(BigDecimal.ZERO);
                    }
                    if (child.getAmountRedcoupon() == null) {
                        child.setAmountRedcoupon(BigDecimal.ZERO);
                    }
                    childrenList.add(child);
                }
                createPayTradeRPCDTO.setChildrenList(childrenList);
            }
        } catch (Exception e) {
            FinhubLogger.error(e.getMessage(), e);
        }

        return createPayTradeRPCDTO;
    }


    /**
     * @Description: (RPC) 场景OC系统调用，创建支付交易流水+支付完成+通知结果
     * 无需用户在APP收银台点击“立即支付”，自动完成扣款
     * 使用场景：万能订单，没有员工信息支付。
     * 创建支付流水号，并且支付后，支付有回掉，无需用户在APP收银台点击“立即支付”，自动完成扣款
     * @Param: [cashierCreateTradeRPCVo]
     * @Author: wh
     */
    @Override
    public CashierCreatePayTradeRPCDTO createAndPayOrderTrade4NoEmployee(CashierCreatePayNoEmployeeTradeReqRPCVo reqRPCVo) {
        FinhubLogger.info("【收银台】【RPC回填单创建并支付无员工扣除预算参数】：{}", reqRPCVo.toString());
        reqRPCVo = ValidateUtils.validate(reqRPCVo);
        CashierCreatePayNoEmployeeTradeReqVo tradeReqVo = new CashierCreatePayNoEmployeeTradeReqVo();
        BeanUtils.copyProperties(reqRPCVo, tradeReqVo);
        CashierCreatePayNoEmployeeTradeRespVo respVo = null;
        try {
            respVo = cashierOrderSettlementService.createAndPayOrderTrade4NoEmployee(tradeReqVo);
        } catch (FinAccountNoEnoughException accountEx) {
            throw accountEx;
        } catch (FinPayException payEx) {
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (FinhubException e) {
            throw e;
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

        CashierCreatePayTradeRPCDTO createPayTradeRPCDTO = new CashierCreatePayTradeRPCDTO();
        BeanUtils.copyProperties(respVo, createPayTradeRPCDTO);
        //通知
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.checkPayStatusAndCallBizByFbOrderId(createPayTradeRPCDTO.getFbOrderId());
        });
        return createPayTradeRPCDTO;
    }

    @Override
    public ResultDTO notifyThirdPartyPayResult(String cashierTxnId, String payTxnId, Integer payStatus) {
        CashierOrderSettlement cashierOrder = cashierOrderSettlementService.notifyThirdPartyPayResult(cashierTxnId, payTxnId, payStatus);
        cashierOrderSettlementService.callCashierSettlement(cashierOrder.getFbOrderId(), cashierOrder.getCashierTxnId(), cashierOrder.getEmployeeId());
        
        try {
            if (CashierPayStatus.cashierHadCancel(cashierOrder.getPayStatus())) {
                cashierOrderSettlementService.refundThirdPartyPayWhenOrderCanceled(cashierOrder);
            }
        } catch (Exception e) {
            FinhubLogger.warn("【收银台】订单取消但是第三方支付成功退款异常，原因：{}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<CashierQueryRPCDTO> getPayDetail(CashierQueryReqRPCVo cashierQueryReqRPCVo) {

        if (cashierQueryReqRPCVo.getFbMainOrderId() != null){
            List<CashierQueryRPCDTO> cashierQueryRPCDTOS = new ArrayList<>();
            //查询子单
            List<CashierOrderSettlementRelation> cashierOrderSettlementRelations =  cashierOrderSettlementRelationManager.queryByFbOrderMainId(cashierQueryReqRPCVo.getFbMainOrderId());
            if (CollectionUtils.isEmpty(cashierOrderSettlementRelations)){
                return cashierQueryRPCDTOS;
            }
            for (CashierOrderSettlementRelation cashierOrderSettlementRelation :cashierOrderSettlementRelations) {
                CashierQueryReqVo cashierQueryReqVo = new CashierQueryReqVo();
                BeanUtils.copyProperties(cashierQueryReqRPCVo, cashierQueryReqVo);
                cashierQueryReqVo.setFbOrderId(cashierOrderSettlementRelation.getFbOrderId());
                List<CashierQueryRPCDTO> subList = getPayDetail0(cashierQueryReqVo);
                cashierQueryRPCDTOS.addAll(subList);
            }
            return cashierQueryRPCDTOS;
        }else {
            ValidateUtils.validate(cashierQueryReqRPCVo);
            CashierQueryReqVo cashierQueryReqVo = new CashierQueryReqVo();
            BeanUtils.copyProperties(cashierQueryReqRPCVo, cashierQueryReqVo);
            cashierQueryReqVo.setFbOrderId(cashierQueryReqRPCVo.getFbOrderId());
            return getPayDetail0(cashierQueryReqVo);
        }

    }


    public List<CashierQueryRPCDTO> getPayDetail0(CashierQueryReqVo cashierQueryReqVo){
        List<CashierQueryRespVo> cashierQueryRespVos = cashierOrderSettlementService.queryCashierDetail(cashierQueryReqVo);
        List<CashierQueryRPCDTO> cashierQueryRPCDTOS = new ArrayList<>();
        if (ObjUtils.isEmpty(cashierQueryRespVos)){
            return cashierQueryRPCDTOS;
        }
        CashierQueryRPCDTO cashierQueryRPCDTO;
        CashierCostAttributionRPCDTO cashierCostAttributionRPCDTO;
        for (CashierQueryRespVo cashierQueryRespVo : cashierQueryRespVos) {
            cashierQueryRPCDTO = new CashierQueryRPCDTO();
            cashierCostAttributionRPCDTO = new CashierCostAttributionRPCDTO();
            CashierOrderCostAttribution costAttribution = cashierQueryRespVo.getCostAttribution();
            BeanUtils.copyProperties(cashierQueryRespVo, cashierQueryRPCDTO);
            if (ObjUtils.isNotEmpty(costAttribution)) {
                BeanUtils.copyProperties(costAttribution, cashierCostAttributionRPCDTO);
                cashierQueryRPCDTO.setCashierCostAttributionRPCDTO(cashierCostAttributionRPCDTO);
            }
            if (CollectionUtils.isNotEmpty(cashierQueryRespVo.getAttributions())) {
                List<CashierCostAttributionRPCDTO> attributions = Lists.newArrayList();
                for (CashierOrderCostAttribution c : cashierQueryRespVo.getAttributions()) {
                    CashierCostAttributionRPCDTO dto = new CashierCostAttributionRPCDTO();
                    BeanUtils.copyProperties(c, dto);
                    attributions.add(dto);
                }
                cashierQueryRPCDTO.setAttributions(attributions);
            }
            cashierQueryRPCDTOS.add(cashierQueryRPCDTO);
        }
        return cashierQueryRPCDTOS;
    }

    @Override
    public List<CashierBatchQueryRPCDTO> batchQuery(CashierBatchQueryReqRPCVo cashierBatchQueryReqRpcVo) {
        if(ObjUtils.isEmpty(cashierBatchQueryReqRpcVo)||cashierBatchQueryReqRpcVo.getFbOrderIds().length<1){
            return null;
        }
        CashierBatchQueryReqVo cashierBatchQueryReqVo = new CashierBatchQueryReqVo();
        BeanUtils.copyProperties(cashierBatchQueryReqRpcVo, cashierBatchQueryReqVo);
        cashierBatchQueryReqVo.setFbOrderIds(cashierBatchQueryReqRpcVo.getFbOrderIds());
        List<CashierBatchQueryRespVo> respVoList = cashierOrderSettlementService.batchQuery(cashierBatchQueryReqVo);
        List<CashierBatchQueryRPCDTO> batchQueryRPCDTOS = new ArrayList<>();
        CashierBatchQueryRPCDTO cashierBatchQueryRPCDTO;
        if(ObjUtils.isNotEmpty(respVoList)){
            for (CashierBatchQueryRespVo cashierBatchQueryRespVo : respVoList) {
                cashierBatchQueryRPCDTO = new CashierBatchQueryRPCDTO();
                BeanUtils.copyProperties(cashierBatchQueryRespVo, cashierBatchQueryRPCDTO);
                batchQueryRPCDTOS.add(cashierBatchQueryRPCDTO);
            }
        }
        return batchQueryRPCDTOS;
    }

    @Override
    @CashierTrailCompanyRejectAnnotation("试用账户禁用")
    public CashierCreatePayTradeRPCDTO createAndPayBankTradeOrSaas(CashierCreatePayTradeRPCVo createPayTradeRPCVo, boolean saasBuget) {
        try{
            CashierCreatePayTradeReqVo createPayTradeReqVo = new CashierCreatePayTradeReqVo();
            BeanUtils.copyProperties(createPayTradeRPCVo, createPayTradeReqVo);
            CashierCreatePayTradeRespVo andPayBankTradeOrSaas = cashierOrderSettlementService.createAndPayBankTradeOrSaas(createPayTradeReqVo, false);
            CashierCreatePayTradeRPCDTO cashierCreatePayTradeRPCDTO = new CashierCreatePayTradeRPCDTO();
            BeanUtils.copyProperties(andPayBankTradeOrSaas, cashierCreatePayTradeRPCDTO);
            CompletableFuture.runAsync(() -> {
                CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementService.selectCashierSettlementByFbOrderIdAndEmpId(andPayBankTradeOrSaas.getFbOrderId(), createPayTradeRPCVo.getEmployeeId(), OrderRootTypeEnum.ROOT_ORDER.getKey());
                kafkaCashierPayManager.pushCashierStatusUpdate(cashierOrderSettlement);
            });
            return cashierCreatePayTradeRPCDTO;
        } catch (FinPayException e){
            FinhubLogger.warn("createAndPayBankTradeOrSaas warn createPayTradeRPCVo={}", JsonUtils.toJson(createPayTradeRPCVo), e);
            throw new FinhubException(e.getCode(), FinhubMessageType.TIP_WINDOW, e.getMessage());
        } catch (FinhubException e){
            FinhubLogger.warn("createAndPayBankTradeOrSaas warn createPayTradeRPCVo={}", JsonUtils.toJson(createPayTradeRPCVo), e);
            throw e;
        } catch (Exception e){
            FinhubLogger.error("createAndPayBankTradeOrSaas error createPayTradeRPCVo={}", JsonUtils.toJson(createPayTradeRPCVo), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalResponseCode.EXCEPTION);
        }
    }

    @Override
    public CreateAcctPublicPayTradeRPCDTO createAcctPublicPayTrade(CashierAcctPublicPayTradeRPCVo createPayTradePPCVo) {
        try {
            createPayTradePPCVo.checkReq();
        } catch (FinhubException e) {
            throw new FinhubException(GlobalResponseCode.CASHIER_PUBLIC_PAYMENT_FAILED.getCode(), e.getMessage());
        }
        //平安直联虚拟卡停用，根据银行侧状态提示
        if (StringUtils.isNotBlank(createPayTradePPCVo.getBankName())&& BankNameEnum.isSpa(createPayTradePPCVo.getBankName())){
            throw new FinhubException(BANK_SPA_OFF_ERROR_OVER.getCode(),BANK_SPA_OFF_ERROR_OVER.getType(),BANK_SPA_OFF_ERROR_OVER.getMsg());
        }

        final String lockKey = MessageFormat.format(RedisKeyConstant.CASHIER_PAY_ACCTPUBLIC_ORDERID_KEY, createPayTradePPCVo.getBankAccountNo());
        try {
            //加锁
            boolean lock = redissonService.tryLock(waitTimeRefund, lockTimeRefund, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.info("加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new CashierOrderException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER, CashierOrderException.ORDERSTATE.FAILED, "加锁失败，未取得锁，" + lockKey);
            }
            CashierAcctPublicPayTradeReqVo createPayTradeReqVo = new CashierAcctPublicPayTradeReqVo();
            BeanUtils.copyProperties(createPayTradePPCVo, createPayTradeReqVo);

            CashierCreatePayTradeVo createPayTradeVo = publicPayOrderSettlementService.createOrderAcctPublicPayTrade(createPayTradeReqVo);
            CreateAcctPublicPayTradeRPCDTO cashierCreatePayTradeRPCDTO = new CreateAcctPublicPayTradeRPCDTO();
            BeanUtils.copyProperties(createPayTradeVo, cashierCreatePayTradeRPCDTO);
            CompletableFuture.runAsync(() -> {
                CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementService.selectCashierSettlementByFbOrderIdAndEmpId(createPayTradeVo.getFbOrderId(), createPayTradePPCVo.getEmployeeId(), OrderRootTypeEnum.ROOT_ORDER.getKey());
                kafkaCashierPayManager.pushCashierStatusUpdate(cashierOrderSettlement);
            });
            return cashierCreatePayTradeRPCDTO;
        }
        // 20221018 王叔立：对公付款接口与上游约定，确定交易失败，返回CASHIER_PUBLIC_PAYMENT_FAILED 53050198；交易进行中发生错误（不确定是否发起付款），返回CASHIER_PUBLIC_PAYMENT_ERROR 53050199；没有其他情况
        catch (CashierOrderException e) {
            FinhubLogger.info("对公付款付款异常：req={}, errorlog={}", JsonUtils.toJson(createPayTradePPCVo), e.getMessage(), e);
            GlobalResponseCode grc = GlobalResponseCode.CASHIER_PUBLIC_PAYMENT_ERROR;
            if (CashierOrderException.ORDERSTATE.FAILED.equals(e.getOrderState())) {
                grc = GlobalResponseCode.CASHIER_PUBLIC_PAYMENT_FAILED;
            }
            // finhub在dubbo rpc调用中能够传递code、msg，code使用CASHIER_PUBLIC_PAYMENT_ERROR/CASHIER_PUBLIC_PAYMENT_FAILED之一，msg使用具体错误信息
            throw new FinhubException(grc.getCode(), e.getMessage());
        } catch (Exception e) {
            String msg = e.getMessage();
            if (e instanceof FinPayException) {
                msg = GlobalResponseCode.getByCode(((FinPayException) e).getCode()).getMsg() + ", " + msg;
            }
            FinhubLogger.info("对公付款付款异常：req={}", JsonUtils.toJson(createPayTradePPCVo), e);
            throw new FinhubException(GlobalResponseCode.CASHIER_PUBLIC_PAYMENT_ERROR.getCode(), msg);
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("对公支付释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("对公支付释放锁失败：{}", lockKey, e);
            }
        }
    }

    @Override
    @CashierTrailCompanyRejectAnnotation("试用账户禁用")
    public CashierCreateTradeRPCDTO createTrade4XeReliefOrSass(CashierTradeXeReliefRPCVo refundTradeXeRPCVo, boolean sassBuget) {
        FinhubLogger.info("【收银台】【RPC取消减免单参数】：{}", refundTradeXeRPCVo.toString());
        ValidateUtils.validate(refundTradeXeRPCVo);

        CashierCreateTradeRespVo cashierCreateTradeResVo = null;
        try {
            cashierCreateTradeResVo = cashierOrderSettlementService.createTrade4XeReliefOrSass(refundTradeXeRPCVo, sassBuget);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn(RPC_CANCEL_DISC_EXCEPTION_MSG, refundTradeXeRPCVo.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error(RPC_CANCEL_DISC_EXCEPTION_MSG, refundTradeXeRPCVo.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error(RPC_CANCEL_DISC_EXCEPTION_MSG, refundTradeXeRPCVo.toString(), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.error(RPC_CANCEL_DISC_EXCEPTION_MSG, refundTradeXeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error(RPC_CANCEL_DISC_EXCEPTION_MSG, refundTradeXeRPCVo.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        //通知
        CashierCreateTradeRespVo finalCashierCreateTradeResVo = cashierCreateTradeResVo;
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.callCashierSettlement(finalCashierCreateTradeResVo.getFbOrderId(), finalCashierCreateTradeResVo.getCashierTxnId(), refundTradeXeRPCVo.getEmployeeId());
        });
        CashierCreateTradeRPCDTO cashierCreateTradeDTO = new CashierCreateTradeRPCDTO();
        BeanUtils.copyProperties(cashierCreateTradeResVo, cashierCreateTradeDTO);
        return cashierCreateTradeDTO;
    }

    @Override
    public void synPayOrderTrade(CashierTradeSynOrderRPCVo cashierTradeSynOrderRPCVo) {
        try {
            CashierTradeSynOrderReqVo cashierTradeSynOrderReqVo = new CashierTradeSynOrderReqVo();
            BeanUtils.copyProperties(cashierTradeSynOrderRPCVo, cashierTradeSynOrderReqVo);
            ValidateUtils.validate(cashierTradeSynOrderReqVo);
            cashierOrderSettlementService.synPayOrderTrade(cashierTradeSynOrderReqVo);
        } catch (FinPayException payEx) {
            FinhubLogger.error("【收银台】【场景同步业务模式错误】：{}={}", JsonUtils.toJson(cashierTradeSynOrderRPCVo), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【收银台】【场景同步业务模式参数错误】：{}={}", JsonUtils.toJson(cashierTradeSynOrderRPCVo), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【收银台】【场景同步业务模式异常】：{}={}", JsonUtils.toJson(cashierTradeSynOrderRPCVo), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

    }

    @Override
    @CashierTrailCompanyRejectAnnotation("试用账户禁用")
    public CashierCreateAutoPayTradeRpcDTO createAndAutoPayOrderTrade(CashierCreateAutoPayTradeRpcVO cashierCreateAutoPayTradeRpcVO) {
        FinhubLogger.info("【收银台】【RPC创建并自动支付参数】：{}", cashierCreateAutoPayTradeRpcVO.toString());
        ValidateUtils.validate(cashierCreateAutoPayTradeRpcVO);
        CashierCreateAutoPayTradeRespVO cashierCreateAutoPayTradeRespVO = null;
        try {
            CashierCreateAutoPayTradePrivateReqVO cashierCreateAutoPayTradePrivateReqVO = new CashierCreateAutoPayTradePrivateReqVO();
            BeanUtils.copyProperties(cashierCreateAutoPayTradeRpcVO, cashierCreateAutoPayTradePrivateReqVO);
            cashierCreateAutoPayTradeRespVO = cashierOrderSettlementService.createAndAutoPayOrderTrade4Private(cashierCreateAutoPayTradePrivateReqVO);
        } catch (FinAccountNoEnoughException e) {
            FinhubLogger.warn(RPC_CREATE_PAY_EXCEPTION_MSG, cashierCreateAutoPayTradeRpcVO.toString(), e);
            throw e;
        } catch (FinPayException e) {
            FinhubLogger.error(RPC_CREATE_PAY_EXCEPTION_MSG, cashierCreateAutoPayTradeRpcVO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error(RPC_CREATE_PAY_EXCEPTION_MSG, cashierCreateAutoPayTradeRpcVO.toString(), e);
            throw e;
        } catch (ValidateException e) {
            FinhubLogger.error(RPC_CREATE_PAY_EXCEPTION_MSG, cashierCreateAutoPayTradeRpcVO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error(RPC_CREATE_PAY_EXCEPTION_MSG, cashierCreateAutoPayTradeRpcVO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        CashierCreateAutoPayTradeRpcDTO cashierCreateAutoPayTradeRpcDTO = new CashierCreateAutoPayTradeRpcDTO();
        BeanUtils.copyProperties(cashierCreateAutoPayTradeRespVO, cashierCreateAutoPayTradeRpcDTO);
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.callCashierSettlement(cashierCreateAutoPayTradeRpcDTO.getFbOrderId(), cashierCreateAutoPayTradeRpcDTO.getCashierTxnId(), cashierCreateAutoPayTradeRpcDTO.getEmployeeId());
        });
        return cashierCreateAutoPayTradeRpcDTO;
    }

    @Override
    public CashierQueryOriginal4ReliefRPCDTO getOriginal4ReliefTrade(CashierQueryOriginal4ReliefRPCVO cashierQueryOriginal4ReliefRPCVO) {
        FinhubLogger.info("【收银台】【RPC查询减免单原单信息参数】：{}", cashierQueryOriginal4ReliefRPCVO.toString());
        CashierQueryOriginal4ReliefRPCDTO cashierQueryOriginal4ReliefRPCDTO = new CashierQueryOriginal4ReliefRPCDTO();
        try {
            CashierQueryOriginal4ReliefRespRPCVO cashierQueryOriginal4ReliefRespRPCVO = cashierOrderSettlementService.getOriginal4ReliefTrade(cashierQueryOriginal4ReliefRPCVO);
            BeanUtils.copyProperties(cashierQueryOriginal4ReliefRespRPCVO,cashierQueryOriginal4ReliefRPCDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn(RELIEF_EXCEPTION_MSG, cashierQueryOriginal4ReliefRPCVO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException payEx) {
            FinhubLogger.error(RELIEF_EXCEPTION_MSG, cashierQueryOriginal4ReliefRPCVO.toString(), payEx);
            throw new FinhubException(payEx.getCode(), payEx.getType(), payEx.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error(RELIEF_EXCEPTION_MSG, cashierQueryOriginal4ReliefRPCVO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error(RELIEF_EXCEPTION_MSG, cashierQueryOriginal4ReliefRPCVO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

        return cashierQueryOriginal4ReliefRPCDTO;
    }

    @Override
    @CashierTrailCompanyRejectAnnotation("试用账户禁用")
    public CreateAndPayTrade4ReimbursementRPCDTO createAndPayTrade4Reimbursement(CashierPayTrade4ReimbursementRPCVO cashierPayTrade4ReimbursementRPCVO) {
        FinhubLogger.info("【收银台】【RPC报销打款参数】：{}", cashierPayTrade4ReimbursementRPCVO.toString());
        cashierPayTrade4ReimbursementRPCVO.checkReq();

        //平安停用，根据银行侧状态提示
        if (StringUtils.isNotBlank(cashierPayTrade4ReimbursementRPCVO.getBankName())&& BankNameEnum.isSpa(cashierPayTrade4ReimbursementRPCVO.getBankName())){
            throw new FinhubException(BANK_SPA_OFF_ERROR_OVER.getCode(),BANK_SPA_OFF_ERROR_OVER.getType(),BANK_SPA_OFF_ERROR_OVER.getMsg());
        }
        final String lockKey = MessageFormat.format(RedisKeyConstant.CASHIER_PAY_REIMBURSEMENT_ORDERID_KEY, cashierPayTrade4ReimbursementRPCVO.getBankAccountNo());
        try {
            //加锁
            boolean lock = redissonService.tryLock(waitTimeRefund, lockTimeRefund, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.info("加锁结果:{},{}" ,lock , lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            CashierPayTrade4ReimbursementReqVO cashierPayTrade4ReimbursementReqVO = new CashierPayTrade4ReimbursementReqVO();
            BeanUtils.copyProperties(cashierPayTrade4ReimbursementRPCVO, cashierPayTrade4ReimbursementReqVO);
            CashierCreatePayTradeVo createPayTradeVo = cashierOrderSettlementService.createAndPayTrade4Reimbursement(cashierPayTrade4ReimbursementReqVO);
            CreateAndPayTrade4ReimbursementRPCDTO cashierCreatePayTradeRPCDTO = new CreateAndPayTrade4ReimbursementRPCDTO();
            BeanUtils.copyProperties(createPayTradeVo, cashierCreatePayTradeRPCDTO);
            CompletableFuture.runAsync(() -> {
                CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementService.selectCashierSettlementByFbOrderIdAndEmpId(createPayTradeVo.getFbOrderId(), cashierPayTrade4ReimbursementReqVO.getEmployeeId(), OrderRootTypeEnum.ROOT_ORDER.getKey());
                kafkaCashierPayManager.pushCashierStatusUpdate(cashierOrderSettlement);
            });
            return cashierCreatePayTradeRPCDTO;
        } catch (InterruptedException e) {
            throw new FinhubException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER.getCode()
                    ,GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER.getType(),
                    GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER.getMsg());
        } catch (FinPayException e) {
            FinhubLogger.error("【收银台】【RPC报销打款参数】：{}", cashierPayTrade4ReimbursementRPCVO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("【收银台】【RPC报销打款参数】：{}", cashierPayTrade4ReimbursementRPCVO.toString(), e);
            throw e;
        } catch (Exception e) {
            throw e;
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("对公支付释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("对公支付释放锁失败：{}", lockKey, e);
            }
        }
    }

    @Override
    @CashierTrailCompanyRejectAnnotation("试用账户禁用")
    public CashierCreatePayTradeRPCDTO createAndPayOrderTrade4NoEmployee4OA(CashierCreatePayNoEmployeeTradeReqRPCVo cashierCreatePayNoEmployeeTradeReqRPCVo) {
        FinhubLogger.info("【收银台】【火车票代打离职人员支付】：{}", cashierCreatePayNoEmployeeTradeReqRPCVo.toString());
        cashierCreatePayNoEmployeeTradeReqRPCVo = ValidateUtils.validate(cashierCreatePayNoEmployeeTradeReqRPCVo);
        CashierCreatePayNoEmployeeTradeReqVo tradeReqVo = new CashierCreatePayNoEmployeeTradeReqVo();
        BeanUtils.copyProperties(cashierCreatePayNoEmployeeTradeReqRPCVo, tradeReqVo);
        CashierCreatePayNoEmployeeTradeRespVo respVo = null;
        try {
            respVo = cashierOrderSettlementService.createAndPayOrderTrade4NoEmployee4OA(tradeReqVo);
        } catch (FinAccountNoEnoughException e) {
            throw e;
        } catch (FinPayException e) {
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            throw e;
        } catch (ValidateException e) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }

        CashierCreatePayTradeRPCDTO createPayTradeRPCDTO = new CashierCreatePayTradeRPCDTO();
        BeanUtils.copyProperties(respVo, createPayTradeRPCDTO);
        //通知
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.checkPayStatusAndCallBizByFbOrderId(createPayTradeRPCDTO.getFbOrderId());
        });
        return createPayTradeRPCDTO;
    }

    @Override
    public int refreshCostAttribution(CashierRefreshCostAttributionRPCVO cashierRefreshCostAttributionRPCVO) {
        List<CashierRefreshApplyCostAttribution> costAttributions = cashierRefreshCostAttributionRPCVO.getApplyCostAttributions();
        int count = 0;
        if (CollectionUtils.isNotEmpty(costAttributions)){
            for (CashierRefreshApplyCostAttribution costAttribution :costAttributions) {
                int update = refreshCostAttribution0(costAttribution);
                count = count + update;
            }
        }
        return count;
    }

    @Override
    public boolean updateCashierOrderSettlementPayResult(String fbOrderId, Integer Status) {
        cashierOrderSettlementService.updateCashierOrderSettlementPay(fbOrderId,Status);
        return true ;
    }

    @Override
    public CashierInfoQueryRPCDTO queryCashierInfo(String fbOrderId, Integer orderRootType) {
        CashierInfoQueryRPCDTO cashierInfoQueryRPCDTO = new CashierInfoQueryRPCDTO();
        cashierInfoQueryRPCDTO.setOrderType(orderRootType);
        List<CashierOrderSettlement> cashierOrderSettlements = cashierOrderSettlementService.queryCashierInfo(fbOrderId,orderRootType);
        BigDecimal totalPayAmount = BigDecimal.ZERO;
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        if (ObjUtils.isNotEmpty(cashierOrderSettlements)){
            cashierInfoQueryRPCDTO.setRootOrderId(cashierOrderSettlements.get(0).getRootOrderId());
            cashierInfoQueryRPCDTO.setCompanyId(cashierOrderSettlements.get(0).getCompanyId());
            cashierInfoQueryRPCDTO.setEmployeeId(cashierOrderSettlements.get(0).getEmployeeId());
            List<CashierInfoQueryPayData> cashierInfoQueryPayDataList = new ArrayList<>();
            for (CashierOrderSettlement cashierOrderSettlement:cashierOrderSettlements){
                CashierInfoQueryPayData cashierInfoQueryPayData = new CashierInfoQueryPayData();
                cashierInfoQueryPayData.setCashierTxnId(cashierOrderSettlement.getCashierTxnId());
                cashierInfoQueryPayData.setRootOrderId(cashierOrderSettlement.getRootOrderId());
                cashierInfoQueryPayData.setFbOrderId(cashierOrderSettlement.getFbOrderId());
                cashierInfoQueryPayData.setOrderType(cashierOrderSettlement.getOrderType());
                cashierInfoQueryPayData.setAmountAll(cashierOrderSettlement.getAmountAll());
                cashierInfoQueryPayData.setAmountFbb(cashierOrderSettlement.getAmountFbb());
                cashierInfoQueryPayData.setAmountPersonal(cashierOrderSettlement.getAmountPersonal());
                cashierInfoQueryPayData.setAmountCompany(cashierOrderSettlement.getAmountCompany());
                cashierInfoQueryPayData.setAmountBank(cashierOrderSettlement.getAmountBank());
                cashierInfoQueryPayData.setAmountPublic(cashierOrderSettlement.getAmountPublic());
                cashierInfoQueryPayData.setAmountThird(cashierOrderSettlement.getAmountThird());
                cashierInfoQueryPayData.setAmountVoucher(cashierOrderSettlement.getAmountVoucher());
                cashierInfoQueryPayData.setAmountRedCoupon(cashierOrderSettlement.getAmountCoupon());
                cashierInfoQueryPayDataList.add(cashierInfoQueryPayData);
                totalPayAmount = totalPayAmount.add(cashierOrderSettlement.getAmountAll());
            }
            cashierInfoQueryRPCDTO.setPayOrder(cashierInfoQueryPayDataList);
        }
        List<CashierOrderRefundSettlement> cashierOrderRefundSettlements = cashierOrderRefundSettlementService.queryCashierRefundSettlementByFbOrderId(fbOrderId,orderRootType);
        if (ObjUtils.isNotEmpty(cashierOrderRefundSettlements)){
            List<CashierInfoQueryRefundData> cashierInfoQueryRefundDataList = new ArrayList<>();
            for (CashierOrderRefundSettlement cashierOrderRefundSettlement:cashierOrderRefundSettlements){
                CashierInfoQueryRefundData cashierInfoQueryRefundData = new CashierInfoQueryRefundData();
                cashierInfoQueryRefundData.setCashierTxnId(cashierOrderRefundSettlement.getCashierTxnId());
                cashierInfoQueryRefundData.setRootOrderId(cashierOrderRefundSettlement.getRootOrderId());
                cashierInfoQueryRefundData.setOrderType(cashierOrderRefundSettlement.getOrderType());
                cashierInfoQueryRefundData.setFbOrderId(cashierOrderRefundSettlement.getFbOrderId());
                cashierInfoQueryRefundData.setRefundAmountCompany(cashierOrderRefundSettlement.getRefundAmountCompany());
                cashierInfoQueryRefundData.setRefundAmountBank(cashierOrderRefundSettlement.getRefundAmountBank());
                cashierInfoQueryRefundData.setRefundAmountAll(cashierOrderRefundSettlement.getRefundAmountAll());
                cashierInfoQueryRefundData.setRefundAmountFbb(cashierOrderRefundSettlement.getRefundAmountFbb());
                cashierInfoQueryRefundData.setRefundAmountPublic(cashierOrderRefundSettlement.getRefundAmountPublic());
                cashierInfoQueryRefundData.setRefundAmountPersonal(cashierOrderRefundSettlement.getRefundAmountPersonal());
                cashierInfoQueryRefundData.setRefundAmountThird(cashierOrderRefundSettlement.getRefundAmountThird());
                cashierInfoQueryRefundData.setRefundAmountVoucher(cashierOrderRefundSettlement.getRefundAmountVoucher());
                cashierInfoQueryRefundData.setRefundAmountRedCoupon(cashierOrderRefundSettlement.getRefundAmountRedcoupon());
                cashierInfoQueryRefundDataList.add(cashierInfoQueryRefundData);
                totalRefundAmount = totalRefundAmount.add(cashierOrderRefundSettlement.getRefundAmountAll());
            }
            cashierInfoQueryRPCDTO.setRefundOrder(cashierInfoQueryRefundDataList);
        }
        cashierInfoQueryRPCDTO.setTotalPayAmount(totalPayAmount);
        cashierInfoQueryRPCDTO.setTotalRefundAmount(totalRefundAmount);
        cashierInfoQueryRPCDTO.setTotalRemainAmount(totalPayAmount.subtract(totalRefundAmount));

        return cashierInfoQueryRPCDTO;
    }

    public int refreshCostAttribution0(CashierRefreshApplyCostAttribution cashierRefreshApplyCostAttribution){
        List<CashierOrderCostAttribution> cashierOrderCostAttributions = cashierOrderCostAttributionManager.getCashierOrderCostAttributionByFbOrderId(cashierRefreshApplyCostAttribution.getFbOrderId());
        int count = 0;
        if (CollectionUtils.isNotEmpty(cashierOrderCostAttributions)){
            String companyId = cashierOrderCostAttributions.get(0).getCompanyId();
            String employeeId = cashierOrderCostAttributions.get(0).getEmployeeId();
            String systemExt = cashierOrderCostAttributions.get(0).getSystemExt();
            Integer usePersonalBudget = cashierOrderCostAttributions.get(0).getUsePersonalBudget();
            String pCostAttribution = cashierOrderCostAttributions.get(0).getpCostAttribution();
            Integer budgetOpt = cashierOrderCostAttributions.get(0).getBudgetOpt();
            String customExt = cashierOrderCostAttributions.get(0).getCustomExt();
            Integer costAttributionOpt = cashierOrderCostAttributions.get(0).getCostAttributionOpt();

            List<CashierRefreshCostAttribution> cashierRefreshCostAttributionList = cashierRefreshApplyCostAttribution.getCostAttributions();
            for (CashierRefreshCostAttribution cashierRefreshCostAttribution: cashierRefreshCostAttributionList){
                if (cashierRefreshCostAttribution.getCostAttributionType() == null){
                    continue;
                }
                //因公支付保存费用归属信息
                CashierOrderCostAttribution attribution = new CashierOrderCostAttribution();
                String id = RandomUtils.bsonId();
                attribution.setId(id);
                attribution.setFbOrderId(cashierRefreshApplyCostAttribution.getFbOrderId());
                attribution.setCostAttributionId(cashierRefreshCostAttribution.getCostAttributionId());
                attribution.setCostAttributionType(cashierRefreshCostAttribution.getCostAttributionType());
                attribution.setCostAttributionName(cashierRefreshCostAttribution.getCostAttributionName());
                attribution.setpCostAttribution(pCostAttribution);
                attribution.setCustomExt(customExt);
                attribution.setCostAttributionPath(cashierRefreshCostAttribution.getCostAttributionName());
                attribution.setCostAttributionOpt(costAttributionOpt);
                attribution.setBudgetOpt(budgetOpt);
                attribution.setCompanyId(companyId);
                attribution.setEmployeeId(employeeId);
                attribution.setOrderType(cashierRefreshApplyCostAttribution.getOrderType());
                attribution.setSystemExt(systemExt);
                attribution.setUsePersonalBudget(usePersonalBudget);
                attribution.setCreateTime(new Date());
                CashierOrderCostAttribution costAttribution = cashierOrderCostAttributionManager.queryByFbOrderIdAndType(cashierRefreshApplyCostAttribution.getFbOrderId(),cashierRefreshCostAttribution.getCostAttributionType());

                if (cashierRefreshCostAttribution.getCostAttributionType() == 1){
                    attribution.setCostAttributionPath(getAllUnitPath(companyId,cashierRefreshCostAttribution.getCostAttributionId(),cashierRefreshCostAttribution.getCostAttributionName()));
                }
                if (costAttribution != null){
                    //更新
                    attribution.setId(costAttribution.getId());
                    cashierOrderCostAttributionManager.updateCostAttributionByRefresh(attribution);
                    count++;
                } else{
                    if (cashierOrderCostAttributionManager.save(attribution)) {
                        count++;
                    }
                }
            }
        }
        return count;
    }

    public String getAllUnitPath(String companyId, String costAttributionId, String costAttributionName) {
        try {
            OrgUnitResult result = iOrgUnitService.queryOrgUnitAndParentUnit(companyId, costAttributionId);
            if (ObjUtils.isNotEmpty(result)) {
                return result.getOrgUnitFullName();
            }
        } catch (Exception e) {
            return costAttributionName;
        }
        return costAttributionName;
    }
}