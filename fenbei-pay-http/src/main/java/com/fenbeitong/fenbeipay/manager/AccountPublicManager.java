package com.fenbeitong.fenbeipay.manager;

import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctPublicRespDTO;
import java.util.List;

/**
 * 对公付款逻辑处理
 * @author: zhaoxu
 * @date: 2022-03-25 16:02:12
 */
public interface AccountPublicManager {

    /**
     * 根据权限查询用户下的对公账户、查询账户对应公司信息
     * @author: zhaoxu
     * @date: 2022-03-25 16:10:44
     */
    AcctPublicRespDTO publicAcctList(String companyId, List<Integer> status, List<String> companyAccountIds, String companyNameLike, String version);
}
