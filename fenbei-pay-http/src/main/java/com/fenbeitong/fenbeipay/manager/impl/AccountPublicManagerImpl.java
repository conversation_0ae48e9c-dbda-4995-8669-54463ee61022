package com.fenbeitong.fenbeipay.manager.impl;

import com.fenbeitong.common.utils.app.VersionUtils;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicSearchService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctPublicAcctRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctPublicRespDTO;
import com.fenbeitong.fenbeipay.api.util.BankNameShowConfig;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.manager.AccountPublicManager;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.mask.utils.DataMaskUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 对公付款逻辑处理
 * @author: zhaoxu
 * @date: 2022-03-25 16:13:18
 */
@Service
public class AccountPublicManagerImpl implements AccountPublicManager {

    @Autowired
    private UAcctCompanyMainService uAcctCompanyMainService;

    @Autowired
    private AcctPublicSearchService acctPublicSearchService;

    @Autowired
    private UAcctCommonService uAcctCommonService;

    /**
     * 根据权限查询用户下的对公账户、查询账户对应公司信息
     * @author: zhaoxu
     * @date: 2022-03-25 16:10:44
     */
    @Override
    public AcctPublicRespDTO publicAcctList(String companyId, List<Integer> status, List<String> companyAccountIds, String businessName, String version) {

        // 根据权限查询用户下的对公账户
        List<AccountPublic> accountPublicList = acctPublicSearchService.queryAcctPublicListCommon(companyId, status, companyAccountIds);

        if (CollectionUtils.isEmpty(accountPublicList)) {
            AcctPublicRespDTO respDTO = new AcctPublicRespDTO();
            respDTO.setPublicAcctRespDTOList(Lists.newArrayList());
            return respDTO;
        }
        // 查询账户对应公司信息
        List<String> companyMainIds = accountPublicList.stream().filter(e -> StringUtils.isNotBlank(e.getCompanyMainId())).map(AccountPublic::getCompanyMainId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(companyMainIds)) {
            AcctPublicRespDTO respDTO = new AcctPublicRespDTO();
            respDTO.setPublicAcctRespDTOList(Lists.newArrayList());
            return respDTO;
        }
        List<AcctCompanyMain> acctCompanyMains = uAcctCompanyMainService.findByMainIds(companyMainIds);
        Map<String, AcctCompanyMain> mapByMainId = acctCompanyMains.stream().collect(Collectors.toMap(AcctCompanyMain::getCompanyMainId, Function.identity(), (key1, key2) -> key2));

        // 过滤主体公司
        if (StringUtils.isNotBlank(businessName)) {
            accountPublicList = accountPublicList.stream().filter(accountPublic -> mapByMainId.get(accountPublic.getCompanyMainId()) != null
                    && mapByMainId.get(accountPublic.getCompanyMainId()).getBusinessName().contains(businessName)).collect(Collectors.toList());
        }

        // 拼装返回参数
        return buildAcctPublicRespDTO(accountPublicList, mapByMainId, version);
    }

    /**
     * 返回参数拼装
     * @author: zhaoxu
     * @date: 2022-03-25 16:17:45
     */
    public AcctPublicRespDTO buildAcctPublicRespDTO(List<AccountPublic> accountPublicList, Map<String, AcctCompanyMain> mapByMainId, String version) {
        AcctPublicRespDTO respDTO = new AcctPublicRespDTO();
        List<AcctPublicAcctRespDTO> acctPublicAcctRespDTOS = new ArrayList<>();
        accountPublicList.stream().forEach(accountPublic -> {
            AcctPublicAcctRespDTO acctPublicAcctRespDTO = new AcctPublicAcctRespDTO();
            BankNameEnum bankEnum = BankNameEnum.getBankEnum(accountPublic.getBankAccountName());

            acctPublicAcctRespDTO.setCompanyMainId(accountPublic.getCompanyMainId());
            acctPublicAcctRespDTO.setBankAccountNo(accountPublic.getBankAccountNo());
            acctPublicAcctRespDTO.setBankAccountNoMask(DataMaskUtils.bankCard(accountPublic.getBankAccountNo()));
            acctPublicAcctRespDTO.setBankAccountName(accountPublic.getBankAccountName());
            acctPublicAcctRespDTO.setBankName(bankEnum.getName());
            acctPublicAcctRespDTO.setCompanyId(accountPublic.getCompanyId());
            //String businessName = Objects.isNull(mapByMainId.get(accountPublic.getCompanyMainId())) ? "" : mapByMainId.get(accountPublic.getCompanyMainId()).getBusinessName();
            AcctCompanyMain acctCompanyMain = mapByMainId.get(accountPublic.getCompanyMainId());
            String businessName = accountPublic.getBankAccountAcctName();
            if(Objects.nonNull(acctCompanyMain)){
                /**
                 * ZHIFU-5070 中信账户名称优化
                 * 1.上线之后展示主体名称
                 * 2.白名单内展示主体名称
                 */

                String showMainName = uAcctCommonService.getShowMainName(bankEnum.getCode(), acctCompanyMain.getBusinessName(), accountPublic.getBankAccountNo(), acctCompanyMain);
                businessName = showMainName;
            }
            acctPublicAcctRespDTO.setBusinessName(businessName);
            acctPublicAcctRespDTO.setDesc(BankNameShowConfig.makeBankPublicShowName(accountPublic.getBankAccountName(), accountPublic.getBankAccountNo(), businessName));
            acctPublicAcctRespDTO.setBalance(accountPublic.getBalance());
            acctPublicAcctRespDTO.setCompanyAccountId(accountPublic.getCompanyAccountId());
            acctPublicAcctRespDTO.setAccountId(accountPublic.getCompanyAccountId());
            if (VersionUtils.checkVersionGreaterEqualThan(version, 5114)){
                acctPublicAcctRespDTO.setBankBackgroundUrl(bankEnum.getBankBackground());
            }else {
                acctPublicAcctRespDTO.setBankBackgroundUrl(bankEnum.getBankBackgroundHorizontal());
            }
            acctPublicAcctRespDTO.setBankIconUrl(bankEnum.getBankIconUrl());
            acctPublicAcctRespDTOS.add(acctPublicAcctRespDTO);
        });
        respDTO.setPublicAcctRespDTOList(acctPublicAcctRespDTOS);
        return respDTO;
    }
}
