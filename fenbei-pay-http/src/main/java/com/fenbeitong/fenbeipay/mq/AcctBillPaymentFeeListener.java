package com.fenbeitong.fenbeipay.mq;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.acctdech.dto.BillPaymentFeeMsg;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctIndividualCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessCreditService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctIndividualCreditService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferBaseReqDTO;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.constant.core.UcMessageCode;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.finhub.common.constant.FundAcctCreditOptType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 账单费用处理
 * <AUTHOR>
 * @date 2023-08-31 15:26:12
 */
@Service
@Slf4j
public class AcctBillPaymentFeeListener implements MessageListenerConcurrently {

    /**强制解锁时间设置*/
    private static final long LOCK_TIME = 6000L;

    /**等待时间**/
    private static final long WAIT_TIME = 6100L;

    @Resource
    private RedissonService redissonService;

    @Resource
    private UAcctIndividualCreditService uAcctIndividualCreditService;

    @Resource
    private UAcctBusinessCreditService uAcctBusinessCreditService;

    @Resource
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Resource
    private AcctIndividualCreditFlowService acctIndividualCreditFlowService;

    @SneakyThrows
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        if (list.size() > 1) {
            log.error("AcctBillPaymentFeeServiceImpl consumeMessage error size:{}", list.size());
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        String messageStr = new String(list.get(0).getBody(), "utf-8");
        log.info("账单费用处理msg:{}", messageStr);
        BillPaymentFeeMsg billPaymentFeeMsg = JSONObject.parseObject(messageStr, BillPaymentFeeMsg.class);
        String lockKey = "ACCT_BILL_PAYMENT_FEE_" + billPaymentFeeMsg.getCompanyId() + "_" + billPaymentFeeMsg.getAcctType();
        try {
            boolean lock = redissonService.tryLock(LOCK_TIME, WAIT_TIME, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                log.warn("【账单费用获取锁失败！！】messageStr:{}", messageStr);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;            }
            if (CoreConstant.BILL_FEE_ACCT_TYPE_BUSINESS.equals(billPaymentFeeMsg.getAcctType())) {
                businessHandle(billPaymentFeeMsg);
            } else if (CoreConstant.BILL_FEE_ACCT_TYPE_INDIVIDUAL.equals(billPaymentFeeMsg.getAcctType())) {
                individualHandle(billPaymentFeeMsg);
            } else {
                log.error("【账单费用账户类型异常】messageStr:{}", messageStr);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }catch (FinPayException e){
            log.error("【账单费用处理失败！！】messageStr:{}", messageStr, e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }catch (Exception e) {
            FinhubLogger.error("【账单费用处理error】messageStr:{}", messageStr, e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        } finally {
            //释放锁
            try {
                redissonService.unLock(lockKey);
                log.info("释放锁成功：{}", lockKey);
            }catch (Exception e){
                log.error("释放锁失败：{}", lockKey,e);
            }
        }
    }

    /**
     * 商务账户处理
     * <AUTHOR>
     * @date 2023-08-31 17:27:01
     */
    private void businessHandle(BillPaymentFeeMsg billPaymentFeeMsg) {
        List<AcctBusinessCreditFlow> acctBusinessCreditFlows = acctBusinessCreditFlowService.queryAccountSubFlowByBizNo(billPaymentFeeMsg.getSysPaymentBizNo(), FundAcctCreditOptType.REPAYMENT_TO_BUSINESS.getKey());
        if (CollectionUtils.isNotEmpty(acctBusinessCreditFlows)) {
            log.error("businessHandle 流水 error messageStr:{}", JSONObject.toJSONString(billPaymentFeeMsg));
            throw new FinhubException(UcMessageCode.FAIL, "账单费用流水已存在");
        }
        List<AcctBusinessCredit> businessCredits = uAcctBusinessCreditService.findByCompanyId(billPaymentFeeMsg.getCompanyId());
        if (CollectionUtils.isEmpty(businessCredits) || businessCredits.size() != 1) {
            log.error("businessHandle 账户 error messageStr:{}", JSONObject.toJSONString(billPaymentFeeMsg));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_EXIST);
        }
        AcctBusinessCredit acctBusinessCredit = businessCredits.get(0);
        uAcctBusinessCreditService.adjustAmount(buildBusinessDTO(acctBusinessCredit, billPaymentFeeMsg), acctBusinessCredit, buildBusinessCondition(acctBusinessCredit, billPaymentFeeMsg));
    }

    /**
     * 补助福利账户处理
     * <AUTHOR>
     * @date 2023-08-31 17:27:05
     */
    private void individualHandle(BillPaymentFeeMsg billPaymentFeeMsg) {
        List<AcctIndividualCreditFlow> acctIndividualCreditFlows = acctIndividualCreditFlowService.queryAccountSubFlowByBizNo(billPaymentFeeMsg.getSysPaymentBizNo(), FundAcctCreditOptType.REPAYMENT_TO_INDIVIDUAL.getKey());
        if (CollectionUtils.isNotEmpty(acctIndividualCreditFlows)) {
            log.error("individualHandle 流水 error messageStr:{}", JSONObject.toJSONString(billPaymentFeeMsg));
            throw new FinhubException(UcMessageCode.FAIL, "账单费用流水已存在");
        }
        List<AcctIndividualCredit> individualCredits = uAcctIndividualCreditService.findByCompanyId(billPaymentFeeMsg.getCompanyId());
        if (CollectionUtils.isEmpty(individualCredits) || individualCredits.size() != 1) {
            log.error("individualHandle 账户 error messageStr:{}", JSONObject.toJSONString(billPaymentFeeMsg));
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_INDIVIDUAL_NOT_EXIST);
        }
        AcctIndividualCredit acctIndividualCredit = individualCredits.get(0);
        uAcctIndividualCreditService.adjustAmount(buildIndividualDTO(acctIndividualCredit, billPaymentFeeMsg), acctIndividualCredit, buildIndividualCondition(acctIndividualCredit, billPaymentFeeMsg));
    }

    /**
     * 商务消费构建请求参数
     */
    private AcctTransferBaseReqDTO buildBusinessDTO(AcctBusinessCredit acctBusinessCredit, BillPaymentFeeMsg billPaymentFeeMsg) {
        AcctTransferBaseReqDTO reqDTO = new AcctTransferBaseReqDTO();
        reqDTO.setCompanyId(acctBusinessCredit.getCompanyId());
        reqDTO.setCompanyMainId(acctBusinessCredit.getCompanyMainId());
        reqDTO.setBankName(acctBusinessCredit.getBankName());
        reqDTO.setBankAccountNo(acctBusinessCredit.getBankAccountNo());
        reqDTO.setAccountSubType(acctBusinessCredit.getAccountSubType());
        reqDTO.setOperationAmount(billPaymentFeeMsg.getServiceFeeToYuan());
        reqDTO.setBizNo(billPaymentFeeMsg.getSysPaymentBizNo());
        reqDTO.setOperationDescription(String.format("%s账单授信服务费", billPaymentFeeMsg.getBillNo()));
        reqDTO.setOperationUserId(billPaymentFeeMsg.getOperationUserId());
        reqDTO.setOperationUserName("分贝通");
        reqDTO.setOperationChannelType(OperationChannelType.SYSTEM.getKey());
        reqDTO.setCustomerServiceId("分贝通");
        reqDTO.setCustomerServiceName("分贝通");
        reqDTO.setRemark(String.format("%s账单授信服务费", billPaymentFeeMsg.getBillNo()));
        return reqDTO;
    }

    /**
     * 补助福利构建请求参数
     */
    private AcctTransferBaseReqDTO buildIndividualDTO(AcctIndividualCredit acctIndividualCredit, BillPaymentFeeMsg billPaymentFeeMsg) {
        AcctTransferBaseReqDTO reqDTO = new AcctTransferBaseReqDTO();
        reqDTO.setCompanyId(acctIndividualCredit.getCompanyId());
        reqDTO.setCompanyMainId(acctIndividualCredit.getCompanyMainId());
        reqDTO.setBankName(acctIndividualCredit.getBankName());
        reqDTO.setBankAccountNo(acctIndividualCredit.getBankAccountNo());
        reqDTO.setAccountSubType(acctIndividualCredit.getAccountSubType());
        reqDTO.setOperationAmount(billPaymentFeeMsg.getServiceFeeToYuan());
        reqDTO.setBizNo(billPaymentFeeMsg.getSysPaymentBizNo());
        reqDTO.setOperationDescription(String.format("%s账单授信服务费", billPaymentFeeMsg.getBillNo()));
        reqDTO.setOperationUserId(billPaymentFeeMsg.getOperationUserId());
        reqDTO.setOperationUserName("分贝通");
        reqDTO.setOperationChannelType(OperationChannelType.SYSTEM.getKey());
        reqDTO.setCustomerServiceId("分贝通");
        reqDTO.setCustomerServiceName("分贝通");
        reqDTO.setRemark(String.format("%s账单授信服务费", billPaymentFeeMsg.getBillNo()));
        return reqDTO;
    }

    /**
     * 补助福利
     * <AUTHOR>
     * @date 2023-08-31 19:38:11
     */
    private AcctCreditCondition buildBusinessCondition(AcctBusinessCredit businessCredit, BillPaymentFeeMsg billPaymentFeeMsg) {
        // 补助福利condition构建
        AcctCreditCondition individualCondition = new AcctCreditCondition();
        individualCondition.setOptType(FundAcctCreditOptType.REPAYMENT_TO_BUSINESS);
        individualCondition.setCompanyId(businessCredit.getCompanyId());
        individualCondition.setAccountId(businessCredit.getAccountId());
        individualCondition.setOperationAmount(billPaymentFeeMsg.getServiceFeeToYuan().negate());
        individualCondition.setOriginInitCredit(businessCredit.getInitCredit());
        individualCondition.setInitCredit(businessCredit.getInitCredit());
        individualCondition.setOriginTempAmount(businessCredit.getTempAmount());
        individualCondition.setTempAmount(businessCredit.getTempAmount());

        return individualCondition;
    }

    /**
     * 补助福利
     * <AUTHOR>
     * @date 2023-08-31 19:38:11
     */
    private AcctCreditCondition buildIndividualCondition(AcctIndividualCredit individualCredit, BillPaymentFeeMsg billPaymentFeeMsg) {
        // 补助福利condition构建
        AcctCreditCondition individualCondition = new AcctCreditCondition();
        individualCondition.setOptType(FundAcctCreditOptType.REPAYMENT_TO_INDIVIDUAL);
        individualCondition.setCompanyId(individualCredit.getCompanyId());
        individualCondition.setAccountId(individualCredit.getAccountId());
        individualCondition.setOperationAmount(billPaymentFeeMsg.getServiceFeeToYuan().negate());
        individualCondition.setOriginInitCredit(individualCredit.getInitCredit());
        individualCondition.setOriginTempAmount(individualCredit.getTempAmount());
        individualCondition.setTempAmount(individualCredit.getTempAmount());

        return individualCondition;
    }
}
