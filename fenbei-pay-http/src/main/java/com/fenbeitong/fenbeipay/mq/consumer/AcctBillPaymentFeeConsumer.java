package com.fenbeitong.fenbeipay.mq.consumer;

import com.fenbeitong.fenbeipay.mq.AcctBillPaymentFeeListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 账单消息
 * <AUTHOR>
 * @date 2023-09-04 14:53:56
 */
@Lazy(value = false)
@Component
@Slf4j
public class AcctBillPaymentFeeConsumer {

    @Autowired
    private AcctBillPaymentFeeListener acctBillPaymentFeeListener;

    @Value("${rocketmq.nameserAddr}")
    private String addr;

    @Value("${rocketmq.consumer.prepayment.bill.message.topic}")
    private String topic;

    @Value("${rocketmq.consumer.prepayment.bill.message.group}")
    private String group;

    @PostConstruct
    public void consumerStart() {
        try {
            // 1.实例化消息生产者，指定组名
            DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(group);
            // 2.指定NameServer地址信息.
            consumer.setNamesrvAddr(addr);
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
            // 3.订阅Topic
            consumer.subscribe(topic, "*");
            // 4.负载均衡模式消费(默认就是负载均衡模式)
            consumer.setMessageModel(MessageModel.CLUSTERING);
            // 5.注册回调函数，处理消息
            consumer.registerMessageListener(acctBillPaymentFeeListener);
            consumer.setPullBatchSize(1);
            // 6.启动消息者
            consumer.start();
            log.info("账单消息consumer start =======================, group:{} topic:{}, add:{}", group, topic, addr);
        } catch (MQClientException e) {
            log.warn("账单消息构造MQ消费者失败,", e);
        }
    }

}
