package com.fenbeitong.fenbeipay.http.controller.cashier;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.RepaymentQueryRespDTO;
import com.fenbeitong.acctperson.api.service.trade.IBankUserCardTradeService;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.BizPayType;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayStatus;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderRootTypeEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderType;
import com.fenbeitong.fenbeipay.api.constant.enums.sas.SecurityBusinessCaseEnum;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierSearchPayStatusRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.sas.req.SasSecurityVerifyReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.sas.resp.SasSecurityVerifyRespDTO;
import com.fenbeitong.fenbeipay.api.service.sas.IPayPwdService;
import com.fenbeitong.fenbeipay.awplus.biz.PayBiz;
import com.fenbeitong.fenbeipay.cashier.settlement.impl.CashierOrderSettlementServiceImpl;
import com.fenbeitong.fenbeipay.core.common.base.PayContext;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.FinPayNoMsgException;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasSecurityVerifyManager;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.finhub.common.utils.VersionUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.COMPANY_ID;
import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.USER_ID;

/**
 * 收银台收款Http服务Controller
 * RPC服务请参考fenbei-pay-api
 * @version  2018年11月19日10:56:58
 * <AUTHOR>
 */

@HttpService("/cashier/pay")
public class CashierOrderSettlementController extends BaseController {

    @Autowired
    private CashierOrderSettlementServiceImpl cashierOrderSettlementService;

    @Autowired
    private IPayPwdService iPayPwdService;

    @Autowired
    private PayBiz payBiz;
    
    @Autowired
    private DingDingMsgService dingDingMsgService;
    
    @Autowired
    private IBankUserCardTradeService iBankUserCardTradeService;

    @Autowired
    private SasSecurityVerifyManager securityVerifyManager;

    private static final String SAS_BUSINESS_VERSION = "5.1.9";

    @Deprecated
    @HttpService(value = "/mywallet/v2",method = RequestMethod.POST)
    public void myWallet(HttpRequest request, HttpResponse response){
        MyWalletReqVo myWalletReqVo = new MyWalletReqVo();
        myWalletReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        myWalletReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        MyWalletRespVo myWalletRespVo = cashierOrderSettlementService.myWallet(myWalletReqVo);
        ResponseResultUtils.success(response,myWalletRespVo);
    }
    
    /**
     * (HTTP)个人钱包界面
     */
    @HttpService(value = "/mywallet/v4",method = RequestMethod.POST)
    public void myFbWallet(HttpRequest request, HttpResponse response){
        String employeeId = request.getAttribute(USER_ID).toString();
        MyFbbRespVo myFbbRespVo = cashierOrderSettlementService.myFbbWallet(employeeId);
        ResponseResultUtils.success(response,myFbbRespVo);
    }

    /**
     * (HTTP)个人钱包界面
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/myvoucher/v2",method = RequestMethod.POST)
    public void myVoucher(HttpRequest request, HttpResponse response){
        MyWalletReqVo myWalletReqVo = new MyWalletReqVo();
        myWalletReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        myWalletReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        MyVoucherRespVo vo= cashierOrderSettlementService.myVoucher(myWalletReqVo);
        ResponseResultUtils.success(response,vo);
    }

    /**
     * (HTTP)可用分贝券+分贝币
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/bqability/v2",method = RequestMethod.POST)
    public void payBQAbility(HttpRequest request, HttpResponse response){
        PersonFBBQAbilityReqVo personFBBQAbilityReqVo = request.getBodyObject(PersonFBBQAbilityReqVo.class);
        personFBBQAbilityReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        personFBBQAbilityReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        PersonFBBQAbilityRespVo personFBBQAbilityVo = cashierOrderSettlementService.payBQAbility(personFBBQAbilityReqVo);
        ResponseResultUtils.success(response,personFBBQAbilityVo);
    }

    /**
     * (HTTP+RPC)可用支付能力获取+支付交易信息--返回当前用户可用支付能力（券|币），微信，支付宝
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/payability/v2",method = RequestMethod.POST)
    public void getAllPayAbility(HttpRequest request, HttpResponse response){
        PersonPayAbilityReqVo personPayAbilityReqVo = request.getBodyObject(PersonPayAbilityReqVo.class);
        if (Objects.nonNull(personPayAbilityReqVo)) {
        	Object userId = request.getAttribute(USER_ID);
            Object companyId = request.getAttribute(COMPANY_ID);
            if (Objects.isNull(userId) || Objects.isNull(companyId)) {
            	userId = getUserId(request);
            	companyId = getUserCompanyId(request);
            }
            personPayAbilityReqVo.setEmployeeId(userId.toString());
            personPayAbilityReqVo.setCompanyId(companyId.toString());
            PersonPayAbilityRespVo personPayAbilityVo = cashierOrderSettlementService.getAllPayAbility(personPayAbilityReqVo);
            ResponseResultUtils.success(response,personPayAbilityVo);
        } else {
        	FinhubLogger.warn("【收银台】获取支付能力收到无效请求，用户：{}", getUserId(request));
        }
    }

    /**
     *（该接口为RPC,同时提供HTTP仅作为测试用）
     * (RPC) 场景OC系统调用，生成支付交易流水号，如果因公支付，企业支付部分需要提前扣除，也在此扣除
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/createordertrade/v2",method = RequestMethod.POST)
    public void  createOrderTrade(HttpRequest request, HttpResponse response){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = request.getBodyObject(CashierCreateTradeReqVo.class);
        cashierCreateTradeReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        cashierCreateTradeReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        cashierCreateTradeReqVo.checkParams();
        CashierCreateTradeRespVo cashierCreateTradeRespVo = cashierOrderSettlementService.createOrderTradeAndSaas(cashierCreateTradeReqVo);
        //通知
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.callCashierSettlement(cashierCreateTradeRespVo.getFbOrderId(),cashierCreateTradeRespVo.getCashierTxnId(), cashierCreateTradeRespVo.getEmployeeId());
                });
        ResponseResultUtils.success(response,cashierCreateTradeRespVo);
    }

    /**
     * (HTTP)支付接口-接收用户选择的各种支付能力，并做扣减操作
     * 根据需要三方支付(支付宝|微信)支付的金额，发起三方支付，获得支付票据信息,返给APP|H5
     * WX统一下单wiki：https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=9_1
     * AliPay统一收单交易创建接口wiki:https://docs.open.alipay.com/api_1/alipay.trade.create/
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/payordertrade/v2", method = RequestMethod.POST)
    public void payOrderTrade(HttpRequest request, HttpResponse response) {
        String userId = getUserId(request);
        CashierPayTradeReqVo cashierPayTradeReqVo = request.getBodyObject(CashierPayTradeReqVo.class);
        cashierPayTradeReqVo.of(userId, request.getAttribute(COMPANY_ID).toString());
        CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementService.selectCashierSettlementByFbOrderIdAndEmpIdAndTradeId(cashierPayTradeReqVo.getFbOrderId(),cashierPayTradeReqVo.getFbTradeId(), userId);
         if (Objects.isNull(cashierOrderSettlement)) {
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }

        //判断是否是混合支付不是纯3方支付
        if (BigDecimalUtils.hasPrice(cashierOrderSettlement.getAmountAll().subtract(cashierPayTradeReqVo.getThirdPartPayPrice()))) {
            String version = request.getHeader("client_version");
            //校验是否通过安全认证（支付密码、短信验证码）
            if (cashierPayTradeReqVo.getEmployeePwd() != null) {
                iPayPwdService.verifyPayPwd(version, cashierPayTradeReqVo.getCompanyId(),
                        userId, cashierPayTradeReqVo.getEmployeePwd(), cashierPayTradeReqVo.getEncryptType());
            } else if (!StringUtils.isBlank(version) && VersionUtils.greaterThanOrEqualTo(version, SAS_BUSINESS_VERSION)) {
                SasSecurityVerifyReqDTO securityVerifyReqDTO = new SasSecurityVerifyReqDTO(cashierPayTradeReqVo.getSasBusinessId(), cashierOrderSettlement.getEmployeeId(), cashierOrderSettlement.getCompanyId(),
                        SecurityBusinessCaseEnum.CASHIER.getCode(), buildPayType(cashierPayTradeReqVo, cashierOrderSettlement));
                SasSecurityVerifyRespDTO securityVerifyRespDTO = securityVerifyManager.queryIsSecurityVerifyPass(securityVerifyReqDTO);
                if (!securityVerifyRespDTO.getIsSecurityVerifyPass()) {
                    throw new FinPayException(GlobalResponseCode.SAS_PAY_SECURITY_NOT_PASS);
                }
            } else {
                FinhubLogger.info("【收银台】支付单{}未校验是否通过安全认证（支付密码、短信验证码）", cashierOrderSettlement.getCashierTxnId());
            }
        }

        //TODO......企业web支付只有企业微信不需要分贝通支付密码
        //正常支付逻辑
        CashierPayTradeRespVo cashierPayTradeRespVo = null;
        OrderType orderType = OrderType.getEnum(cashierPayTradeReqVo.getOrderType());
        try {
        	PayContext.setClientIp(request.getIp());
            cashierPayTradeRespVo = cashierOrderSettlementService.payOrderTradeAndSaas(cashierPayTradeReqVo);
        } catch (FinPayNoMsgException e) {
            String msgError = "【收银台】支付异常=场景" + orderType.getValue() + "订单号:" + cashierPayTradeReqVo.getFbOrderId() + e.getMessage();
            FinhubLogger.warn(msgError, e);
            throw e;
        } catch (FinPayException e) {
            String msgError = "【收银台】支付异常=场景" + orderType.getValue() + "订单号:" + cashierPayTradeReqVo.getFbOrderId() + e.getMessage();
            if (e.ignoreReport()) {
                FinhubLogger.warn(msgError, e);
            } else {
                FinhubLogger.error(msgError, e);
            }
            
            throw e;
        } catch (FinhubException e) {
        	String msgError = "【收银台】支付异常:场景" + orderType.getValue() + "订单号:" + cashierPayTradeReqVo.getFbOrderId() + e.getMessage();
        	if (Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_HAD_INVALID.getCode(), e.getCode()) ||
        			Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_HAD_CANCEL.getCode(), e.getCode()) ||
        			Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_HAD_PAY.getCode(), e.getCode()) ||
        			Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_HAD_DONE.getCode(), e.getCode()) ||
        			Objects.equals(GlobalResponseCode.CASHIER_PAY_WX_FORBIDDEN_ERROR.getCode(), e.getCode()) ||
                    Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_STATUS_ERROR.getCode(), e.getCode())) {
                    FinhubLogger.warn(msgError, e);
        	} else {
        	    dingDingMsgService.sendMsg(msgError);
                FinhubLogger.error(msgError, e);
        	}
            
            throw e;
        } catch (Exception e) {
            String msgError = "【收银台】支付异常:场景" + orderType.getValue() + "订单号:" + cashierPayTradeReqVo.getFbOrderId() + e.getMessage();
            if (!Objects.equals(GlobalResponseCode.ACCOUNT_SUB_BUSINESS_NOT_ENOUGH.getMsg(), e.getMessage())) {
                dingDingMsgService.sendMsg(msgError);
                FinhubLogger.error(msgError, e);
            }else {
                FinhubLogger.warn(msgError, e);
            }
            throw e;
        } finally {
        	PayContext.clearConext();
		}
        //通知
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.callCashierSettlement(cashierOrderSettlement.getFbOrderId(),cashierOrderSettlement.getCashierTxnId(), cashierPayTradeReqVo.getEmployeeId());
        });
        ResponseResultUtils.success(response, cashierPayTradeRespVo);
    }

    private String buildPayType(CashierPayTradeReqVo cashierPayTradeReqVo, CashierOrderSettlement cashierOrderSettlement) {
        StringBuilder bizPayType = new StringBuilder();
        BigDecimal publicNetSettlePrice = cashierOrderSettlement.getAmountPublic().subtract(cashierOrderSettlement.getCompanyNoSettlePrice());
        CashierBizPayTypeReqVo bizPayTypeReqVo = CashierBizPayTypeReqVo.of(cashierOrderSettlement.getBizPayType());
        if (BigDecimalUtils.hasPrice(publicNetSettlePrice) && bizPayTypeReqVo.isCompanyPay() &&
                cashierPayTradeReqVo.isCompanyNotPrePay(cashierOrderSettlement.getAccountType(), cashierOrderSettlement.getCompanyPrePay())) {
            bizPayType.append(BizPayType.COMPANY.name()).append(",");
        }

        if (BigDecimalUtils.hasPrice(cashierPayTradeReqVo.getPersonFbbPayPrice())) {
            bizPayType.append(BizPayType.FBB.name()).append(",");
        }

        if (BigDecimalUtils.hasPrice(cashierPayTradeReqVo.getPersonVouchersPayPrice())) {
            bizPayType.append(BizPayType.FBQ.name()).append(",");
        }

        if (BigDecimalUtils.hasPrice(cashierPayTradeReqVo.getBankCardPayPrice())) {
            bizPayType.append(BizPayType.BANK_CARD.name()).append(",");
        }

        if (BigDecimalUtils.hasPrice(cashierPayTradeReqVo.getBindBankCardPayPrice())) {
            bizPayType.append(BizPayType.BIND_BANK_CARD.name()).append(",");
        }

        return bizPayType.toString();
    }

    /**
     * 支付结果查询
     *  当前仅用于错花还款
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/payordertrade/query/v2", method = RequestMethod.POST)
    public void payOrderTradeQuery(HttpRequest request, HttpResponse response) {
        String userId = getUserId(request);
        CashierPayTradeQueryReqVo cashierPayTradeReqVo = request.getBodyObject(CashierPayTradeQueryReqVo.class);
        cashierPayTradeReqVo.of(userId, request.getAttribute(COMPANY_ID).toString());
        CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementService.selectCashierSettlementByFbOrderIdAndEmpIdAndTradeId(cashierPayTradeReqVo.getFbOrderId(),null, userId);
        if (Objects.isNull(cashierOrderSettlement)) {
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }

        CashierPayTradeQueryRespVo queryRespVo = new CashierPayTradeQueryRespVo();
        queryRespVo.setFbOrderId(cashierOrderSettlement.getFbOrderId());
        queryRespVo.setOrderType(cashierOrderSettlement.getOrderType());
        queryRespVo.setPayStatus(cashierOrderSettlement.getPayStatus());
        //广发错花还款
        if (OrderType.isBankIndividual(cashierOrderSettlement.getOrderType())){
            CashierBizPayTypeReqVo cashierBizPayType = CashierBizPayTypeReqVo.of(cashierOrderSettlement.getBizPayType());
            if (cashierBizPayType.isBankCard() || cashierBizPayType.isBindBandCard()){
                //查询支付状态
                RepaymentQueryRespDTO repaymentQueryRespDTO = iBankUserCardTradeService.queryRepaymentStatus(cashierOrderSettlement.getCashierTxnId());
                FinhubLogger.info("【收银台】查询acctPerson 错花支付结果  req = {} res = {}", cashierOrderSettlement.getCashierTxnId() , JSON.toJSONString(repaymentQueryRespDTO));

                Integer cashierPayStatus = CashierPayStatus.CASHIER_SUCCESS_PAY.getKey();
                if (repaymentQueryRespDTO != null){
                    Integer cgbStatus = null;
                    //4-还款失败 41-充值失败 42-圈存失败
                    if (repaymentQueryRespDTO.getOrderStatus() == 4) {
                        cashierPayStatus = CashierPayStatus.CASHIER_FAILED.getKey();
                    } else if (repaymentQueryRespDTO.getOrderStatus() == 41){
                        cgbStatus = 0;
                        cashierPayStatus = CashierPayStatus.CASHIER_FAILED.getKey();
                    }
                    if (repaymentQueryRespDTO.getOrderStatus() == 42){
                        cgbStatus = 1;
                        cashierPayStatus = CashierPayStatus.CASHIER_FAILED.getKey();
                    }
                    //处理平安返回信息  如果状态返回是0 那么返回等待下一次查询
                    if (repaymentQueryRespDTO.getOrderStatus() != 0 ){
                        FinhubLogger.info("【收银台】更新错花支付结果 status = {}", repaymentQueryRespDTO.getOrderStatus());

                        queryRespVo.setCgbStatus(cgbStatus);
                        queryRespVo.setCgbFailReason(repaymentQueryRespDTO.getFailureReason());
                        queryRespVo.setPayFailReason(repaymentQueryRespDTO.getFailureReason());
                        if (CashierPayStatus.CASHIER_BINDIND_CARD_TRADEING.getKey() == cashierOrderSettlement.getPayStatus()){
                            cashierOrderSettlementService.updateCashierOrderSettlement4HaveDone(cashierOrderSettlement,cashierPayStatus);
                        }
                    }

                }
            }
        }
        ResponseResultUtils.success(response, queryRespVo);
    }

    @HttpService(value = "/paythirdtrade/v2",method = RequestMethod.POST)
    public void  payThirdTrade(HttpRequest request, HttpResponse response){
        CashierPayThirdTradeReqVo cashierPayThirdTradeReqVo = request.getBodyObject(CashierPayThirdTradeReqVo.class);
        cashierPayThirdTradeReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        cashierPayThirdTradeReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        CashierPayThirdTradeRespVo  cashierPayThirdTradeRespVo = null;
        try{
        	PayContext.setClientIp(request.getIp());
            cashierPayThirdTradeRespVo = cashierOrderSettlementService.payThirdTradeAndCallBiz(cashierPayThirdTradeReqVo);
        } catch (FinPayNoMsgException e){
            String msgError = "【收银台】三方支付异常=场景订单号:"+cashierPayThirdTradeReqVo.getFbOrderId()+e.getMessage();
            FinhubLogger.warn(msgError,e);
            throw e;
        } catch (FinhubException e) {
        	String msgError = "【收银台】三方支付异常,场景订单号:" + cashierPayThirdTradeReqVo.getFbOrderId() + e.getMessage();
        	if (!Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_HAD_INVALID.getCode(), e.getCode()) && 
        			!Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_HAD_CANCEL.getCode(), e.getCode()) && 
        			!Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_HAD_PAY.getCode(), e.getCode()) && 
        			!Objects.equals(GlobalResponseCode.CASHIER_PAY_SETTLE_HAD_DONE.getCode(), e.getCode()) && 
        			!Objects.equals(GlobalResponseCode.CASHIER_PAY_WX_FORBIDDEN_ERROR.getCode(), e.getCode())) {
        		dingDingMsgService.sendMsg(msgError);
        		FinhubLogger.error(msgError, e);
        	} else {
        		FinhubLogger.warn(msgError, e);
        	}
            
            throw e;
        } catch (Exception e){
            String msgError = "【收银台】三方支付异常,场景订单号:"+cashierPayThirdTradeReqVo.getFbOrderId()+e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError,e);
            throw e;
        } finally {
        	PayContext.clearConext();
		}
        //通知
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.callCashierSettlement(cashierPayThirdTradeReqVo.getFbOrderId(),cashierPayThirdTradeReqVo.getCashierTxnId(), cashierPayThirdTradeReqVo.getEmployeeId());
        });
        ResponseResultUtils.success(response,cashierPayThirdTradeRespVo);
    }

    /**
     * (HTTP)CallBack支付回掉-第三方支付结果异步支付回掉(支付宝+微信)
     */
    @HttpService("/callback/v2/{channel}")
    public void thirdPartyCallBack(HttpRequest request, HttpResponse response){
        String channel=request.getPathValue("channel");
        String resultStr = cashierOrderSettlementService.thirdPartyCallBack(channel,request,response);
        response.setResult(resultStr);
    }

    /**
     * （HTTP）交易详情查询（总交易+子交易）
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/querypay/detail/v2",method = RequestMethod.POST)
    public void getPayDetail(HttpRequest request, HttpResponse response){
        CashierQueryReqVo cashierQueryReqVo = request.getBodyObject(CashierQueryReqVo.class);
        cashierQueryReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        cashierQueryReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        List<CashierQueryRespVo> settlement = cashierOrderSettlementService.queryCashierDetail(cashierQueryReqVo);
        ResponseResultUtils.success(response,settlement);
    }

    /**
     * （HTTP）交易详情查询（总交易+子交易）
     * @param request
     * @param response
     * @return
     * @since V4.5.0
     */
    @HttpService(value = "/querypay/detail/reimburse/v1",method = RequestMethod.POST)
    public void getPayDetailReimburse(HttpRequest request, HttpResponse response){
        CashierQueryReqVo cashierQueryReqVo = request.getBodyObject(CashierQueryReqVo.class);
        cashierQueryReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        cashierQueryReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        CashierOrderSettlement settlement = cashierOrderSettlementService.selectCashierSettlementByFbOrderIdAndEmpId(cashierQueryReqVo.getFbOrderId(),cashierQueryReqVo.getEmployeeId(), OrderRootTypeEnum.ROOT_ORDER.getKey());
        CashierReimburseQueryRespVo reimburseQueryRespVo = new CashierReimburseQueryRespVo();
        BeanUtils.copyProperties(settlement,reimburseQueryRespVo);
        ResponseResultUtils.success(response,reimburseQueryRespVo);
    }

    /**
     * （HTTP）查询个人支付信息状态
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/query/employee/third")
    public void queryEmployeePayThird(HttpRequest request, HttpResponse response){
        QueryPayThirdReqVo queryPayThirdReqVo = request.getBodyObject(QueryPayThirdReqVo.class);
        QueryPayThirdRespVo queryPayThirdRespVo = payBiz.queryEmployeePayThird(queryPayThirdReqVo.getFbOrderNo());
        ResponseResultUtils.success(response,queryPayThirdRespVo);
    }

    /**
     * （HTTP）批量查询个人支付信息状态
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/query/batch/third")
    public void queryBatchThird(HttpRequest request, HttpResponse response){
        QueryBatchPayThirdReqVo queryBatchPayThirdReqVo = request.getBodyObject(QueryBatchPayThirdReqVo.class);
        QueryBatchPayThirdRespVo queryPayThirdRespVo = payBiz.queryBatchThird(queryBatchPayThirdReqVo.getFbOrderNoList());
        ResponseResultUtils.success(response,queryPayThirdRespVo);
    }

    /**
     * (HTTP)个人钱包界面
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/third/redirect",method = RequestMethod.POST)
    public void thirdRedirect(HttpRequest request, HttpResponse response){
        ThirdPayReqVo thirdPayReqVo = request.getBodyObject(ThirdPayReqVo.class);
        ResponseResultUtils.redirect(response,null,thirdPayReqVo.getReqUrl());
    }

    /**
     * (HTTP)个人钱包界面
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/third/redirect1",method = RequestMethod.GET)
    public void thirdRedirect1(HttpRequest request, HttpResponse response){
        String path = request.getParameter("path");
        response.redirect(path);
    }

    /**
     * (HTTP)查询支付状态
     *
     * 目前只用在支付宝PC扫码支付场景，需要轮训此接口获取支付状态决定是否跳转
     */
    @HttpService(value = "/query/payStatus",method = RequestMethod.GET)
    public void queryPayStatus(HttpRequest request, HttpResponse response){
        String fbOrderId = request.getParameter("fbOrderId");
        if (StringUtils.isBlank(fbOrderId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        CashierSearchPayStatusRPCDTO resp = cashierOrderSettlementService.queryPayStatus(fbOrderId);
        ResponseResultUtils.success(response, resp);
    }

}