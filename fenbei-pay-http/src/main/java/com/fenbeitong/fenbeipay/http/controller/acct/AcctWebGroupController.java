package com.fenbeitong.fenbeipay.http.controller.acct;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.activity.api.util.GlobalCoreResponseCode;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.http.vo.FxAcctFlowReq;
import com.fenbeitong.finhub.common.entity.ResponsePageDTO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.mask.utils.DataMaskUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.fxpay.api.interfaces.FxCompanyAcctFacade;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.fxpay.api.vo.acct.CompanyAcctFlowDTO;
import com.fenbeitong.fxpay.api.vo.acct.CompanyAcctFlowQueryRequestDTO;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.KeyValue;
import org.apache.commons.collections4.keyvalue.DefaultKeyValue;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Maps;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferRechargeReqDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyAccountInfo;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserLoginVo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2022-11-28 03:48:02 
*/
@HttpService("/fbp/acct/web/group")
public class AcctWebGroupController extends AcctAbstractController {

	@Autowired
    private IAcctFundMgrService acctFundMgrService;
	
	@Autowired
    private UAcctCommonService acctCommonService;
	
	@Autowired
    private FxCompanyAcctFacade fxCompanyAcctFacade;
	
	/**
	 * 对应登录用户的集团资金调拨功能是否可用
	 * @param request
	 * @param response
	 */
	@HttpService(value = "/transfer/available", method = RequestMethod.GET)
    public void isTransferAvailable(HttpRequest request, HttpResponse response) {
		KeyValue<AvailableGroupCompanyDTO, List<CompanyAccountInfo>> kv = getUserAuthInfoFromSession(request);
		AvailableGroupCompanyDTO dto = kv.getKey();
		boolean available = acctCommonService.isGroupTransferAvailable(kv.getValue(), dto);
		dto.setAvailable(available);
		
		ResponseResultUtils.success(response, dto);
	}
	
	@SuppressWarnings("unchecked")
	private KeyValue<AvailableGroupCompanyDTO, List<CompanyAccountInfo>> getUserAuthInfoFromSession(HttpRequest request) {
		UserLoginVo loginVo = getUserLogin(request);
		AvailableGroupCompanyDTO dto = AvailableGroupCompanyDTO.builder()
				.groupId(loginVo.getUser_info().getGroupId())
				.companyId(loginVo.getCompany_info().getId())
				.companyName(loginVo.getCompany_info().getName())
				.userId(loginVo.getUser_info().getId())
				.userName(loginVo.getUser_info().getName())
				.userPhone(loginVo.getUser_info().getPhone())
				.build();
		List<CompanyAccountInfo> cais = (List<CompanyAccountInfo>) request.getAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS);
		return new DefaultKeyValue<AvailableGroupCompanyDTO, List<CompanyAccountInfo>>(dto, cais);
	}
	
	/**
	 * 转出账户列表
	 * @param request
	 * @param response
	 */
	@HttpService(value = "/transfer/out/accts", method = RequestMethod.GET)
    public void transferOutAccts(HttpRequest request, HttpResponse response) {
		KeyValue<AvailableGroupCompanyDTO, List<CompanyAccountInfo>> kv = getUserAuthInfoFromSession(request);
		AcctDebitMainPoolRespDTO accts = acctCommonService.queryFBTAcctFromGroup(kv.getValue());
		Map<String, Object> result = Maps.newHashMap();
		result.put("userName", kv.getKey().getUserName());
		result.put("userPhone", kv.getKey().getUserPhone());
		Optional.ofNullable(accts).ifPresent(acct -> {
			result.put("asset", acct.getAsset());
			result.put("accts", acct.getAcctDebitMainRespDTOList());
		});
		ResponseResultUtils.success(response, result);
	}
	
	/**
	 * 转入账户列表
	 * @param request
	 * @param response
	 */
	@HttpService(value = "/transfer/into/accts", method = RequestMethod.GET)
    public void transferIntoAccts(HttpRequest request, HttpResponse response) {
		String generalId = request.getParameter("accountGeneralId");
		if (StringUtils.isBlank(generalId)) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		KeyValue<AvailableGroupCompanyDTO, List<CompanyAccountInfo>> kv = getUserAuthInfoFromSession(request);
		Map<String, Object> result = Maps.newHashMap();
		result.put("userName", kv.getKey().getUserName());
		result.put("userPhone", kv.getKey().getUserPhone());
		
		AcctDebitMainPoolRespDTO accts = acctCommonService.queryFBTAcctFromGroup(kv.getValue());
		if (Objects.nonNull(accts) &&  CollectionUtils.isNotEmpty(accts.getAcctDebitMainRespDTOList())) {
			Map<Boolean, List<AcctDebitMainRespDTO>> group = accts.getAcctDebitMainRespDTOList()
					.stream()
					.collect(Collectors.groupingBy(a -> Objects.equals(generalId, a.getAccountGeneralId())));
			if (!group.containsKey(Boolean.TRUE)) {
				throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
			}
			result.put("asset", accts.getAsset());
			result.put("accts", group.get(Boolean.FALSE));
		}
		ResponseResultUtils.success(response, result);
	}
	
	/**
	 * 资金调拨 发送短信验证码
	 * @param request
	 * @param response
	 */
	@HttpService(value = "/transfer/send/msg", method = RequestMethod.POST)
    public void sendMsg(HttpRequest request, HttpResponse response) {
		if (StringUtils.isBlank(request.getBody())) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		KeyValue<AvailableGroupCompanyDTO, List<CompanyAccountInfo>> kv = getUserAuthInfoFromSession(request);
		boolean available = acctCommonService.isGroupTransferAvailable(kv.getValue(), kv.getKey());
		if (!available) {
			throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_OPT_CHECK_ERROR);
		}
		
		AcctTransferRechargeReqDTO transferRequest = JSON.parseObject(request.getBody(), AcctTransferRechargeReqDTO.class);
		UserInfoVO userInfo = getUserInfo(request);
		if (Objects.nonNull(userInfo) && Objects.nonNull(transferRequest) && 
				(StringUtils.isBlank(transferRequest.getVerifyCodePhoneNum()) || 
						!Objects.equals(transferRequest.getVerifyCodePhoneNum(), userInfo.getPhone()))) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		String sequenceNo = acctFundMgrService.sendSmsCaptcha4GroupTransfer(transferRequest);
		Map<String, Object> result = Maps.newHashMap();
		result.put("sequenceNo", sequenceNo);
		result.put("success", Boolean.TRUE);
		ResponseResultUtils.success(response, result);
	}
	
	/**
	 * 执行资金调拨
	 * @param request
	 * @param response
	 */
	@HttpService(value = "/transfer/exec", method = RequestMethod.POST)
    public void transferExec(HttpRequest request, HttpResponse response) {
		if (StringUtils.isBlank(request.getBody())) {
			throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
		}
		
		KeyValue<AvailableGroupCompanyDTO, List<CompanyAccountInfo>> kv = getUserAuthInfoFromSession(request);
		
		AcctTransferRechargeReqDTO transferRequest = JSON.parseObject(request.getBody(), AcctTransferRechargeReqDTO.class);
		AvailableGroupCompanyDTO userInfo = kv.getKey();
		transferRequest.setOperationUserId(userInfo.getUserId());
		transferRequest.setOperationUserName(userInfo.getUserName());
		transferRequest.setBankName(BankNameEnum.FBT.getCode());
		transferRequest.setTargetBankName(BankNameEnum.FBT.getCode());
		transferRequest.setOperationChannelType(OperationChannelType.WEB.getKey());
		
		List<AccountGeneral> generals = acctCommonService.getAvailableGeneralAcct4GroupTransfer(kv.getValue(), kv.getKey(), false);
		Set<String> generalIds = Optional.ofNullable(generals).orElse(Collections.emptyList())
				.stream()
				.map(AccountGeneral :: getAccountGeneralId)
				.collect(Collectors.toSet());
		
		AcctCommonOptRespDTO resp = acctFundMgrService.transferGeneralBetweenGroupMemberRechargeAcct(transferRequest, generalIds);
		ResponseResultUtils.success(response, resp);
	}

	/**
	 * 外币流水回单
	 * <AUTHOR>
	 * @date 2023-06-17 16:09:02
	 */
	@HttpService(value = "/flow/list", method = RequestMethod.POST)
	public void flowList(HttpRequest request, HttpResponse response) {
		FxAcctFlowReq res = request.getBodyObject(FxAcctFlowReq.class);
		if (StringUtils.isBlank(res.getAccountId()) || StringUtils.isBlank(res.getCompanyId())) {
			FinhubLogger.error("外币流水回单参数异常：{}", JSONObject.toJSONString(res));
			throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
		}
		ResponseVo<ResponsePageDTO<CompanyAcctFlowDTO>> resp = queryAcctFlow(res);
		if (!resp.isSuccess()) {
			ResponseResultUtils.fail(response, new FinhubException(GlobalCoreResponseCode.EXCEPTION.getCode(), resp.getMessage()));
			return;
		}
		ResponseResultUtils.success(response, resp.getData());
	}

	/**
	 * 外币流水回单
	 * <AUTHOR>
	 * @date 2023-06-17 16:09:02
	 */
	@HttpService(value = "/export/flow/list", method = RequestMethod.POST)
	public void exportFlowList(HttpRequest request, HttpResponse response) {
		FxAcctFlowReq res = request.getBodyObject(FxAcctFlowReq.class);
		ResponseVo<ResponsePageDTO<CompanyAcctFlowDTO>> resp = queryAcctFlow(res);
		if (!resp.isSuccess()) {
			ResponseResultUtils.fail(response, new FinhubException(GlobalCoreResponseCode.EXCEPTION.getCode(), resp.getMessage()));
			return;
		}
		ResponseResultUtils.success(response, resp.getData().getDataList() == null ? Lists.newArrayList() : resp.getData().getDataList());
	}

	/**
	 * 集团账户有流水权限的账户
	 * <AUTHOR>
	 * @date 2023-06-17 16:09:02
	 */
	@HttpService(value = "/flow/fxAcctAuthList", method = RequestMethod.POST)
	public void acctAuthList(HttpRequest request, HttpResponse response) {
		FxAcctFlowReq req = request.getBodyObject(FxAcctFlowReq.class);
		String companyId = req == null ? null : req.getCompanyId();

		if (StringUtils.isBlank(companyId)) {
			ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "companyId必填"));
			return;
		}
		String menuCode = request.getHeader(CoreConstant.ACCT_GROUP_MENU_CODE);
		try {
			FinhubLogger.info("集团账户有流水权限的账户acctAuthList companyId:{},menuCode:{}", companyId, menuCode);
			List<CompanyAccountInfo> result = (List<CompanyAccountInfo>) request.getAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS);

			// 过滤无权限公司
			Optional<CompanyAccountInfo> accountInfoOptional = result.stream().filter(v -> v.getCompanyId().equals(companyId)).findFirst();
			if (!accountInfoOptional.isPresent()) {
				ResponseResultUtils.success(response, Lists.newArrayList());
				return;
			}

			CompanyAccountInfo companyAccountInfo = accountInfoOptional.get();
			FinhubLogger.info("集团账户有流水权限的账户 res:{}", JSONObject.toJSONString(result));
			List<FxCompanyAcctInfo> fxAccounts = companyAccountInfo.getFxAccounts();
			Map<String, String> res = Maps.newHashMap();
			if (CollectionUtils.isNotEmpty(fxAccounts)) {
				res = fxAccounts.stream().collect(Collectors.toMap(FxCompanyAcctInfo::getAccountId, v -> FxAcctChannelEnum.AIRWALLEX.getChannelName() + "(" + DataMaskUtils.bankCard(v.getId()) + ")"));
			}
			ResponseResultUtils.success(response, res);
		} catch (Exception e) {
			FinhubLogger.error("集团账户有流水权限的账户 error companyId:{}", e);
			ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode()));
		}
	}

	/**
	 * 集团所有有权限账户信息
	 * <AUTHOR>
	 * @date 2023-06-17 16:09:02
	 */
	@HttpService(value = "/fxCompanyAcct", method = RequestMethod.GET)
	public void fxCompanyAcct(HttpRequest request, HttpResponse response) {
		try {
			List<CompanyAccountInfo> result = (List<CompanyAccountInfo>) request.getAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS);
			if (CollectionUtils.isNotEmpty(result)) {
				for (CompanyAccountInfo companyAccountInfo : result) {
					companyAccountInfo.setAccounts(null);
					companyAccountInfo.setFxPermissionAccounts(null);
					companyAccountInfo.setPermissionAccounts(null);
				}
			}
			result = result.stream().filter(v -> CollectionUtils.isNotEmpty(v.getFxAccounts())).collect(Collectors.toList());
			ResponseResultUtils.success(response, result);
		} catch (Exception e) {
			FinhubLogger.error("集团所有有权限账户信息 error companyId:{}", e);
			ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode()));
		}
	}

	private ResponseVo<ResponsePageDTO<CompanyAcctFlowDTO>> queryAcctFlow(FxAcctFlowReq res) {
		ValidateUtils.validate(res);
		CompanyAcctFlowQueryRequestDTO requestDTO = new CompanyAcctFlowQueryRequestDTO();
		BeanUtils.copyProperties(res, requestDTO);
		ResponseVo<ResponsePageDTO<CompanyAcctFlowDTO>> resp = fxCompanyAcctFacade.queryAcctFlow(requestDTO);
		return resp;
	}
}
