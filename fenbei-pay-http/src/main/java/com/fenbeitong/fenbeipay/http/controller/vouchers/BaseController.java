package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherCostStatusEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.WriteInvoiceType;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.constant.core.UcMessageCode;
import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserLoginVo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherCommonUtils;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherConstant;
import com.fenbeitong.fenbeipay.vouchers.dto.OperationUserInfoDTO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.sass.budget.service.api.dubbo.BudgetCompanyBusinessConfigService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleTypeDTO;
import com.fenbeitong.usercenter.api.model.dto.privilege.EmployeePrivilegeDto;
import com.fenbeitong.usercenter.api.model.enums.company.CompanyRuleType;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.entity.SwiftHashMap;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.server.HttpRequest;
import io.netty.handler.codec.http.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: java类作用描述
 * @ClassName: BaseController
 * @Author: zhangga
 * @CreateDate: 2018/12/26 下午4:45
 * @UpdateUser:
 * @UpdateDate: 2018/12/26 下午4:45
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Slf4j
@Controller
public class BaseController {

    @Autowired
    private IPrivilegeService privilegeService;
    @Autowired
    private ICompanyService iCompanyService;
    @Autowired
    private BudgetCompanyBusinessConfigService budgetCompanyBusinessConfigService;
    @Autowired
    private VoucherCommonUtils voucherCommonUtils;

    public static final String USER_ID = "userId";
    public static final String USER_NAME = "userName";
    public static final String USER_PHONE = "userPhone";
    public static final String COMPANY_ID = "companyId";
    public static final String USER_COMPANY_NAME = "user_company_name";
    public static final String USER_ORG_NAME = "user_org_name";
    public static final String COMPANY_NAME = "company_name";
    /**
     * resourceType 菜单类型 1:企业web，2:app
     **/
    public static final Integer WEB_TYPE = 1;
    public static final Integer APP_TYPE = 2;
    public static final String MENU_CODE = "menuCode";

    /**
     * checkPrivilege
     *
     * @return void
     * @Description 校验当前用户是否有权限操作
     * @Date 下午6:01 2018/12/11
     * @Param [companyId, currentUserId, menuCode]
     **/
    public OperationUserInfoDTO checkPrivilege(HttpRequest request) {
        Object currentUserId = request.getAttribute(USER_ID);
        Object currentUserName = request.getAttribute(USER_COMPANY_NAME);
        Object currentUserPhone = request.getAttribute(USER_PHONE);
        Object currentUserOrgName = request.getAttribute(USER_ORG_NAME);
        Object companyId = request.getAttribute(COMPANY_ID);
        if (ObjUtils.isEmpty(currentUserId) || ObjUtils.isEmpty(currentUserName) || ObjUtils.isEmpty(currentUserPhone) || ObjUtils.isEmpty(currentUserOrgName) || ObjUtils.isEmpty(companyId)) {
            throw new FinhubException(UcMessageCode.NO_AUTH);
        }

        String menuCode = request.getHeader(MENU_CODE);
        if (ObjUtils.isEmpty(menuCode)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }

        // 集团版权限暂时放通
        if (!getUserLogin(request).getUser_info().isGroupLogin()) {
            EmployeePrivilegeDto employeePrivilege = privilegeService.queryEmployeeMenuPrivilegeByCode(companyId.toString(), currentUserId.toString(), menuCode, WEB_TYPE);
            if (employeePrivilege == null) {
                throw new FinPayException(GlobalResponseCode.NO_AUTH);
            }
            //HasDredge 是否开通菜单权限 HasAuth 是否有菜单权限
            if (!employeePrivilege.getHasDredge() || !employeePrivilege.getHasAuth()) {
                throw new FinPayException(GlobalResponseCode.NO_AUTH);
            }
        }
        OperationUserInfoDTO operationUserInfoDTO = new OperationUserInfoDTO();
        operationUserInfoDTO.setCompanyId(companyId.toString());
        operationUserInfoDTO.setOperationUserId(currentUserId.toString());
        operationUserInfoDTO.setOperationUserName(currentUserName.toString());
        operationUserInfoDTO.setOperationUserPhone(currentUserPhone.toString());
        operationUserInfoDTO.setOperationUserDepartment(currentUserOrgName.toString());
        return operationUserInfoDTO;
    }

    public OperationUserInfoDTO checkPrivilegeNoMenu(HttpRequest request) {
        Object currentUserId = request.getAttribute(USER_ID);
        Object currentUserName = request.getAttribute(USER_COMPANY_NAME);
        Object currentUserPhone = request.getAttribute(USER_PHONE);
        Object currentUserOrgName = request.getAttribute(USER_ORG_NAME);
        Object companyId = request.getAttribute(COMPANY_ID);
        if (ObjUtils.isEmpty(currentUserId) || ObjUtils.isEmpty(currentUserName) || ObjUtils.isEmpty(currentUserPhone) || ObjUtils.isEmpty(currentUserOrgName) || ObjUtils.isEmpty(companyId)) {
            throw new FinhubException(UcMessageCode.NO_AUTH);
        }
        OperationUserInfoDTO operationUserInfoDTO = new OperationUserInfoDTO();
        operationUserInfoDTO.setCompanyId(companyId.toString());
        operationUserInfoDTO.setOperationUserId(currentUserId.toString());
        operationUserInfoDTO.setOperationUserName(currentUserName.toString());
        operationUserInfoDTO.setOperationUserPhone(currentUserPhone.toString());
        operationUserInfoDTO.setOperationUserDepartment(currentUserOrgName.toString());
        operationUserInfoDTO.setCompanyName(COMPANY_NAME);
        return operationUserInfoDTO;
    }

    public void checkInvoicePrivilege(OperationUserInfoDTO operationUserInfoDTO, Integer writeInvoiceType) {
        //校验当前企业券是否可先开票
        if (!checkInvoiceAdvancePrivilege(operationUserInfoDTO.getCompanyId(), writeInvoiceType)) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_ADVANCE_INVOICE_AUTO);
        }
    }

    /**
     * 校验当前企业券是否可先开票
     * @param companyId
     * @param writeInvoiceType
     * @return
     */
    public boolean checkInvoiceAdvancePrivilege(String companyId, Integer writeInvoiceType) {
        List<CompanyRuleTypeDTO> ruleTypeDTOS = iCompanyService.queryCompanyRuleByType(companyId, Lists.newArrayList(CompanyRuleType.CompanyBeforehandInvoiceType));
        if (ObjUtils.isEmpty(ruleTypeDTOS)) {
            return false;
        }
        CompanyRuleTypeDTO typeDTO = ruleTypeDTOS.get(0);
        if (typeDTO.getState() == CoreConstant.NO && writeInvoiceType != null && WriteInvoiceType.isAdvance(writeInvoiceType)) {
            return false;
        }
        return true;
    }

    /**
     * 校验当前企业预算配置及参数
     * @param companyId
     * @return
     */
    public VoucherCostStatusEnum checkCostConfig(String companyId , String costInfo, String dateOfExpense) {
        //暂时关闭，等待接口调整
        boolean voucherOccupyBudget = voucherCommonUtils.queryCostConfig(companyId);
        if (voucherOccupyBudget) {
            if (StringUtils.isBlank(costInfo)) {
                throw new FinPayException(GlobalResponseCode.VOUCHER_PAYMENT_PARAM_ERROR_COST);
            }
            if (StringUtils.isBlank(dateOfExpense)){
                throw new FinPayException(GlobalResponseCode.VOUCHER_PAYMENT_DATE_ERROR_COST);
            }
            return VoucherCostStatusEnum.NEED_OCCUPY;
        }else {
            return VoucherCostStatusEnum.NOT_OCCUPY;
        }
    }

    /**
     * 校验当前企业预算配置及参数
     * @param companyId
     * @return
     */
    public VoucherCostStatusEnum checkCostConfig(String companyId , String costInfo) {
        //暂时关闭，等待接口调整
        boolean voucherOccupyBudget = voucherCommonUtils.queryCostConfig(companyId);
        if (voucherOccupyBudget) {
            if (StringUtils.isBlank(costInfo)) {
                throw new FinPayException(GlobalResponseCode.VOUCHER_PAYMENT_PARAM_ERROR_COST);
            }
            return VoucherCostStatusEnum.NEED_OCCUPY;
        }else {
            return VoucherCostStatusEnum.NOT_OCCUPY;
        }
    }

    /**
     * 获取需要操作的公司id
     * <AUTHOR>
     * @date 2022-07-05 18:47:01
     */
    protected String getOperationCompanyId(HttpRequest request) {
        UserLoginVo userLogin = getUserLogin(request);
        // 集团版标识
        if (userLogin.getUser_info().isGroupLogin()) {
            if (HttpMethod.GET.name().equalsIgnoreCase(request.getMethod())) {
                return request.getParameter(CoreConstant.ACCT_GROUP_COMPANY_ID);
            }
            if (HttpMethod.POST.name().equalsIgnoreCase(request.getMethod())) {
                SwiftHashMap<String, Object> bodyMap = request.getBodyMap();
                if (bodyMap == null) {
                    return null;
                }
                return bodyMap.getString(CoreConstant.ACCT_GROUP_COMPANY_ID);
            }
        }
        // 非集团版
        return request.getAttribute(COMPANY_ID).toString();
    }

    /**
     * 获取登录信息
     * <AUTHOR>
     * @date 2022-07-11 11:36:12
     */
    protected UserLoginVo getUserLogin(HttpRequest request) {
        UserLoginVo user = (UserLoginVo)request.getAttribute(PayConstant.REQ_USER);
        if(user == null) {
            throw new FinPayException(GlobalResponseCode.RemoteService401);
        }
        return user;
    }

    /**
     * 查询当前用户
     * @param request
     * @return
     */
    public OperationUserInfoDTO getUserInfo(HttpRequest request) {
        Object currentUserId = request.getAttribute(USER_ID);
        Object currentUserName = request.getAttribute(USER_COMPANY_NAME);
        Object currentUserPhone = request.getAttribute(USER_PHONE);
        Object currentUserOrgName = request.getAttribute(USER_ORG_NAME);
        Object companyId = request.getAttribute(COMPANY_ID);
        if (ObjUtils.isEmpty(currentUserId) || ObjUtils.isEmpty(currentUserName) || ObjUtils.isEmpty(currentUserPhone) || ObjUtils.isEmpty(currentUserOrgName) || ObjUtils.isEmpty(companyId)) {
            throw new FinhubException(UcMessageCode.NO_AUTH);
        }
        OperationUserInfoDTO operationUserInfoDTO = new OperationUserInfoDTO();
        operationUserInfoDTO.setCompanyId(companyId.toString());
        operationUserInfoDTO.setOperationUserId(currentUserId.toString());
        operationUserInfoDTO.setOperationUserName(currentUserName.toString());
        operationUserInfoDTO.setOperationUserPhone(currentUserPhone.toString());
        operationUserInfoDTO.setOperationUserDepartment(currentUserOrgName.toString());
        return operationUserInfoDTO;
    }
}
