package com.fenbeitong.fenbeipay.http.interceptor;

import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.constant.core.UcMessageCode;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserLoginVo;
import com.fenbeitong.finhub.auth.constant.UserAttributeConstant;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.usercenter.api.model.dto.auth.OrgUnitVO;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.route.HandlerInterceptor;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import io.netty.handler.codec.http.HttpResponseStatus;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * 外部，web和APP登录拦截器
 */
public class AppLoginInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(AppLoginInterceptor.class);

    @Override
    public boolean preHandle(HttpRequest request, HttpResponse response) throws FinhubException {
        logger.debug("AppLoginInterceptor[preHandle]");
        // 从头信息获取token
        String token = request.getHeader(CoreConstant.KEY_NAME_TOKEN);
        // 如果为空，则从参数获取token
        if (StringUtils.isEmpty(token)) {
            token = request.getParameter(CoreConstant.KEY_NAME_TOKEN);
        }

        // 获取登录信息
        try {
            UserComInfoVO userComInfo = (UserComInfoVO) request.getAttribute(UserAttributeConstant.USER_COM_INFO);
            logger.debug("url:{}, user_info:{}", request.getUri(), JsonUtils.toJson(userComInfo));
            if (Objects.isNull(userComInfo)) {
                response.setStatus(HttpResponseStatus.UNAUTHORIZED);
                throw new FinhubException(GlobalResponseCode.RemoteService401.getCode(), GlobalResponseCode.RemoteService401.getType(), GlobalResponseCode.RemoteService401.getMsg());
            }
            String menuCode = request.getHeader(CoreConstant.ACCT_GROUP_MENU_CODE);

            // 将uc解析的用户信息转换成本系统的用户对象
            UserLoginVo userLoginVo = getUserLoginVo(userComInfo, menuCode);

            request.setAttribute("user", userLoginVo);
            request.setAttribute("userId", userLoginVo.getUser_info().getId());
            request.setAttribute("userName", userLoginVo.getUser_info().getName());
            request.setAttribute("userPhone", userLoginVo.getUser_info().getPhone());
            request.setAttribute("isMoreCompany", userLoginVo.getIs_more());
            request.setAttribute("groupId", userLoginVo.getUser_info().getGroupId());
            if (userLoginVo.getCompany_info() != null) {
                request.setAttribute("companyId", userLoginVo.getCompany_info().getId());
                request.setAttribute("user_company_name", userLoginVo.getCompany_info().getUser_name());
                request.setAttribute("company_name", userLoginVo.getCompany_info().getName());
                request.setAttribute("user_org_name", userLoginVo.getCompany_info().getOrg_unit().getName());
            }
            request.setAttribute("token", token);
        } catch (FinhubException e) {
            throw e;
        } catch (Exception e) {
            logger.error("AppLoginInterceptor[ERROR]", e);
            throw new FinhubException(UcMessageCode.ILLEGAL_ARGUMENT);
        }
        return true;
    }

    /**
     * 类型转化
     * @param userComInfo
     * @param menuCode
     * @return
     */
    private UserLoginVo getUserLoginVo(UserComInfoVO userComInfo, String menuCode) {
        UserLoginVo userLoginVo = new UserLoginVo();

        UserInfoVO user_info = new UserInfoVO();
        user_info.setId(userComInfo.getUser_id());
        user_info.setName(userComInfo.getUser_name());
        user_info.setPhone(userComInfo.getUser_phone());
        user_info.setGroupId(userComInfo.getGroup_id());
        if (StringUtils.isNotBlank(menuCode) && menuCode.startsWith(CoreConstant.ACCT_GROUP_MENU_CODE_SUFFIX)) {
            user_info.setGroupLogin(true);
        }
        userLoginVo.setUser_info(user_info);

        CompanyInfoVO companyInfo = new CompanyInfoVO();
        companyInfo.setId(userComInfo.getCompany_id());
        companyInfo.setName(userComInfo.getCompany_name());
        companyInfo.setUser_name(userComInfo.getUser_name());
        OrgUnitVO orgUnitVO = new OrgUnitVO();
        orgUnitVO.setName(userComInfo.getOrgUnit_name());
        companyInfo.setOrg_unit(orgUnitVO);
        userLoginVo.setCompany_info(companyInfo);

        userLoginVo.setIs_more(userComInfo.getIs_more());
        return userLoginVo;
    }

    @Override
    public void postHandle(HttpRequest request, HttpResponse response) throws FinhubException {
        logger.debug("AppLoginInterceptor[postHandle]");
    }

}
