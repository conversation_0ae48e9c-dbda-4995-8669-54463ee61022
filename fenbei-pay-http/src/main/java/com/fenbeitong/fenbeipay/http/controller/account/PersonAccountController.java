package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.account.person.PersonAccountService;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.PersonFbbFlowDto;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.fbcoin.FbbQueryDetailReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.fbcoin.FbbQueryDetailRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.fbcoin.FbbQueryListReqVo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Title: PersonAccountController
 * @ProjectName fenbei-pay
 * @Description: 个人分贝币账户相关接口
 * <AUTHOR>
 * @date 2019/1/14 10:28
 */
@HttpService("/fbp/person")
public class PersonAccountController {

    @Autowired
    private PersonAccountService personAccountService;

    private static final String USER_ID = "userId";


    /**
     * @Description: 查询企业分贝币流水详情
     * @param: request
     * @param: response
     * @return: json
     * @auther: wh
     * @date: 2019/1/14 10:30 AM
     */
    @HttpService(value = "/fbb/query/detail/v3", method = RequestMethod.POST)
    public void queryDetail(HttpRequest request, HttpResponse response) {
        Object userId = request.getAttribute(USER_ID);
        FbbQueryDetailReqVo fbbQueryDetailReqVo = request.getBodyObject(FbbQueryDetailReqVo.class);
        if (ObjUtils.isEmpty(userId)){
            throw new FinPayException(GlobalResponseCode.TOKEN_EXPIRE);
        }
        fbbQueryDetailReqVo.setEmployeeId(userId.toString());
        //获取企业分贝币操作记录
        FbbQueryDetailRespVo fbbQueryDetailRespVo = personAccountService.queryDetail(fbbQueryDetailReqVo);
        ResponseResultUtils.success(response, fbbQueryDetailRespVo);
    }

   /**
     * @Description: 给APP提供 个人分贝币流水列表 版本3.2.1 之后
     *  支付项目新填http接口 版本3.2.1  之前的还是读UC
     * @Date: 2019/7/3 12:39 PM
     */
    @HttpService(value = "/fbb_flow_list/v3", method = RequestMethod.POST)
    public void queryFbbFlowList(HttpRequest request, HttpResponse response) {
        Object userId = request.getAttribute(USER_ID);
        FbbQueryListReqVo fbbQueryDetailReqVo = request.getBodyObject(FbbQueryListReqVo.class);
        if (ObjUtils.isEmpty(userId)){
            throw new FinPayException(GlobalResponseCode.TOKEN_EXPIRE);
        }
        List<PersonFbbFlowDto> personFbbFlowDtoList;
        try {
            personFbbFlowDtoList = personAccountService.queryPersonFbbFlowList(userId.toString(), fbbQueryDetailReqVo.getType(), fbbQueryDetailReqVo.getPageNo(), fbbQueryDetailReqVo.getPageSize());
        } catch (FinhubException e) {
            FinhubLogger.error("[获取个人分贝币流水集合，异常！] ={}", JsonUtils.toJson(fbbQueryDetailReqVo),e);
            throw e;
        } catch (Exception e) {
            FinhubLogger.error("[获取个人分贝币流水集合，失败！] ={}", JsonUtils.toJson(fbbQueryDetailReqVo),e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),GlobalResponseCode.EXCEPTION.getType(),GlobalResponseCode.EXCEPTION.getMsg());
        }
        Map<String,Object> data =new HashMap<>();
        data.put("personFbbFlowList",ObjUtils.isEmpty(personFbbFlowDtoList)?new ArrayList<>():personFbbFlowDtoList);
        ResponseResultUtils.success(response, data);
    }

}
