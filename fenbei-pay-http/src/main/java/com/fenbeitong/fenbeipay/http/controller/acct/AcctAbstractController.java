package com.fenbeitong.fenbeipay.http.controller.acct;

import com.fenbeitong.fenbei.settlement.external.api.api.bill.IBillOpenApi;
import com.fenbeitong.fenbeipay.acctdech.service.AcctOverseaService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctRechargeWhiteListService;
import com.fenbeitong.fenbeipay.acctdech.service.BankAcctService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicDechService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicSearchService;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyNewDto;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021/1/11
 * @Description
 */
public class AcctAbstractController extends BaseController {

    /**
     * U层充值&授信账户复合Service
     */
    @Autowired
    protected UAcctCommonService uAcctCommonService;
    @Autowired
    protected AccountRedcouponSearchService acctRedcouponSearchService;

    /**
     * U层充值账户复合Service
     */
    @Autowired
    protected UAcctCommonDebitService uAcctCommonDebitService;

    @Autowired
    protected AcctPublicDechService acctPublicDechService;
    /**
     * U层授信账户复合Service
     */
    @Autowired
    protected UAcctCommonCreditService  uAcctCommonCreditService;

    @Autowired
    protected UAcctGeneralService uAcctGeneralService;
    @Autowired
    protected AcctPublicSearchService acctPublicSearchService;
    @Autowired
    protected UAcctBusinessDebitService uAcctBusinessDebitService;
    @Autowired
    protected UAcctIndividualDebitService uAcctIndividualDebitService;
    @Autowired
    protected UAcctCompanyCardService uAcctCompanyCardService;
    @Autowired
    protected UAcctBusinessCreditService uAcctBusinessCreditService;
    @Autowired
    protected UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    protected UAcctBusinessDebitFlowService uAcctBusinessDebitFlowService;
    @Autowired
    protected UAcctIndividualDebitFlowService uAcctIndividualDebitFlowService;
    @Autowired
    protected UAcctBusinessCreditFlowService uAcctBusinessCreditFlowService;
    @Autowired
    protected UAcctIndividualCreditFlowService uAcctIndividualCreditFlowService;
    
    @Autowired
    protected AcctRechargeWhiteListService acctRechargeWhiteListService;
    
    @Autowired
    protected UAcctCompanyCardFlowService uAcctCompanyCardFlowService;
    
    @Autowired
    protected AccountGeneralFlowService accountGeneralFlowService;
    
    @Autowired
    protected UAcctReimbursementService uAcctReimbursementService;

    @Autowired
    protected UAcctCompanyMainService uAcctCompanyMainService;

    @Autowired
    protected UAcctComUpdateService uAcctComUpdateService;

    @Autowired
    protected AcctOverseaService acctOverseaService;
    
    @Autowired
    protected IPrivilegeService iPrivilegeService;

    @Autowired
    protected ICompanyService iCompanyService;

    @Autowired
    protected BankAcctService bankAcctService;

    @Autowired
    protected EmployeeService employeeService;
    
    @Autowired
    protected IBillOpenApi iBillOpenApi;
    
    protected boolean checkTrailCompany(String companyId){
        CompanyNewDto companyNewDto = iCompanyService.queryCompanyNewByCompanyId(companyId);
        if (companyNewDto == null ){
            return true;
        }
        boolean isTrail = companyNewDto.getCooperateState() == 2
                || companyNewDto.getCooperateState() == 3
                || companyNewDto.getCooperateState() == 4
                || companyNewDto.getCooperateState() == 5
                || companyNewDto.getCooperateState() == 9999;
        return isTrail;
    }
}
