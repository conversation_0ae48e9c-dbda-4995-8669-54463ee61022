package com.fenbeitong.fenbeipay.http.controller.newaccount.dto;

import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountGeneralOperationType;
import com.luastar.swift.base.utils.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: java类作用描述
 * @ClassName: AccountGeneralFlowVoForExport
 * @Author: zhangga
 * @CreateDate: 2019/3/18 2:55 PM
 * @UpdateUser:
 * @UpdateDate: 2019/3/18 2:55 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class AccountGeneralFlowVoForExport {


    private String accountFlowId;
    /**
     * 交易类型 ：1-开户;2-充值;3-提现;4-消费;51-转至商户账户;52-转至个人账户;53-转至企业账户；6-冻结;7-解冻
     */
    private Integer operationType;
    private String operationTypeName;

    /**
     * 交易金额 单位：分
     */
    private BigDecimal operationAmount;

    /**
     * 交易说明
     */
    private String operationDescription;

    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 创建时间
     */
    private Date createTime;
    private String operationTime;

    public String getOperationTypeName() {
        return operationType != null ? AccountGeneralOperationType.getEnum(operationType).getValue() : operationTypeName;
    }

    public String getOperationTime() {
        return operationTime = DateUtils.format(createTime);
    }
}