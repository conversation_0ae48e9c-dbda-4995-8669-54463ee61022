package com.fenbeitong.fenbeipay.http.controller.transfer;

import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctBalanceAdjustReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferCommonReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctBalanceAdjustRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCommonOptRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.SimpleAccountGeneralInfoResp;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.api.service.na.IAccountGeneralService;
import com.fenbeitong.fenbeipay.http.controller.transfer.req.BalanceAdjustBatchReq;
import com.fenbeitong.fenbeipay.http.controller.transfer.req.BalanceAdjustItem;
import com.fenbeitong.fenbeipay.http.controller.transfer.req.BalanceAdjustReq;
import com.fenbeitong.fenbeipay.http.controller.transfer.req.TransferBiz2GeneralReq;
import com.fenbeitong.finhub.common.constant.FundAcctGeneralOptType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.RandomUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: guogx
 * @Date: 2022/11/19 上午11:44
 */
@HttpService("/internal/hupo/migrate")
public class HupoMigrateCgbController {

    @Resource
    private IAcctMgrService acctMgrService;

    @Resource
    private IAcctFundMgrService acctFundMgrService;

    @Resource
    private IAccountGeneralService accountGeneralService;

    @HttpService(value = "/transfer/biz2General", method = RequestMethod.POST)
    public void transferBiz2General(HttpRequest request, HttpResponse response) {
        long start = System.currentTimeMillis();
        TransferBiz2GeneralReq biz2GeneralReq = request.getBodyObject(TransferBiz2GeneralReq.class);
        biz2GeneralReq.check();
        FinhubLogger.info("HupoMigrateCgbController#transferBiz2General#start#req:{}", JSON.toJSONString(biz2GeneralReq));
        AcctCommonOptRespDTO acctCommonOptRespDTO = doTransferOut2General(biz2GeneralReq);
        long end = System.currentTimeMillis();
        FinhubLogger.info("HupoMigrateCgbController#transferBiz2General#end#req:{},resp:{},duration:{}", JSON.toJSONString(acctCommonOptRespDTO), JSON.toJSONString(acctCommonOptRespDTO), end-start);
        ResponseResultUtils.success(response, acctCommonOptRespDTO);
    }


    @HttpService(value = "/balance/adjust/decrease", method = RequestMethod.POST)
    public void balanceAdjustDecrease(HttpRequest request, HttpResponse response) {
        long start = System.currentTimeMillis();
        BalanceAdjustReq balanceAdjustReq = request.getBodyObject(BalanceAdjustReq.class);
        balanceAdjustReq.check();
        FinhubLogger.info("HupoMigrateCgbController#balanceAdjust#start#req:{}", JSON.toJSONString(balanceAdjustReq));
        AcctBalanceAdjustRespDTO acctBalanceAdjustRespDTO = doAcctBalanceAdjust(balanceAdjustReq);
        long end = System.currentTimeMillis();
        FinhubLogger.info("HupoMigrateCgbController#balanceAdjust#end#req:{},resp:{},duration:{}", JSON.toJSONString(balanceAdjustReq), JSON.toJSONString(acctBalanceAdjustRespDTO), end-start);
        ResponseResultUtils.success(response, acctBalanceAdjustRespDTO);
    }

    @HttpService(value = "/balance/adjust/batch-decrease", method = RequestMethod.POST)
    public void balanceBatchAdjustDecrease(HttpRequest request, HttpResponse response) {
        BalanceAdjustBatchReq balanceAdjustBatchReq = request.getBodyObject(BalanceAdjustBatchReq.class);
        balanceAdjustBatchReq.check();
        FinhubLogger.info("HupoMigrateCgbController.balanceBatchAdjustDecrease() invoking, req:{}", JSON.toJSONString(balanceAdjustBatchReq));

        int total = 0;
        int failed = 0;
        List<String> failedList = Lists.newArrayList();

        for (BalanceAdjustItem item: balanceAdjustBatchReq.getAccountList()){
            try {
                BalanceAdjustReq balanceAdjustReq = new BalanceAdjustReq();
                balanceAdjustReq.setAccountId(item.getAccountId());
                balanceAdjustReq.setCompanyId(item.getCompanyId());
                balanceAdjustReq.setAdjustAmount(item.getAdjustAmount());
                balanceAdjustReq.setBankName(balanceAdjustBatchReq.getBankName());
                balanceAdjustReq.setOperateId(balanceAdjustBatchReq.getOperateId());
                balanceAdjustReq.setOperateName(balanceAdjustBatchReq.getOperateName());
                balanceAdjustReq.setRemark(balanceAdjustBatchReq.getRemark());
                AcctBalanceAdjustRespDTO acctBalanceAdjustRespDTO = doAcctBalanceAdjust(balanceAdjustReq);
                FinhubLogger.info("HupoMigrateCgbController.balanceBatchAdjustDecrease(), item finished:{}, resp:{}", JSON.toJSONString(item), JSON.toJSONString(acctBalanceAdjustRespDTO));
                total++;
                if (acctBalanceAdjustRespDTO == null) {
                    failed++;
                    failedList.add(balanceAdjustReq.getCompanyId());
                }
            } catch (Exception e) {
                FinhubLogger.error("HupoMigrateCgbController.balanceBatchAdjustDecrease(), error while handling item: {}, errMsg: {}", JSON.toJSONString(item), e.getMessage(), e);
            }
        }
        FinhubLogger.info("HupoMigrateCgbController.balanceBatchAdjustDecrease(), whole finished. req:{}", JSON.toJSONString(balanceAdjustBatchReq));
        ResponseResultUtils.success(response, "invoking finished, total = " + total + ", failed = " + failed + ", failedList: " + failedList.toString());
    }



    private AcctCommonOptRespDTO doTransferOut2General(TransferBiz2GeneralReq biz2GeneralReq){
        AcctCommonOptRespDTO acctCommonOptRespDTO = null;
        try{
            AcctTransferCommonReqDTO transferCommonReqDTO = new AcctTransferCommonReqDTO();
            BeanUtils.copyProperties(biz2GeneralReq, transferCommonReqDTO);
            FinhubLogger.info("HupoMigrateCgbController#doTransferOut2General-transferOut2General-in#req:{}", JSON.toJSONString(transferCommonReqDTO));
            acctCommonOptRespDTO = acctFundMgrService.transferOut2General(transferCommonReqDTO);
            FinhubLogger.info("HupoMigrateCgbController#doTransferOut2General-transferOut2General-in#resp:{}", JSON.toJSONString(acctCommonOptRespDTO));
        } catch (Exception e){
            FinhubLogger.error("HupoMigrateCgbController#doTransferOut2General#error#req:{}", JSON.toJSONString(biz2GeneralReq), e);
        }
        return acctCommonOptRespDTO;
    }


    private AcctBalanceAdjustRespDTO doAcctBalanceAdjust(BalanceAdjustReq balanceAdjustReq){
        AcctBalanceAdjustRespDTO acctBalanceAdjustRespDTO = null;
        try{
            SimpleAccountGeneralInfoResp simpleAccountInfo = accountGeneralService.querySimpleAccountGeneralInfo(balanceAdjustReq.getCompanyId(), balanceAdjustReq.getBankName());
            if (simpleAccountInfo == null || !balanceAdjustReq.getAccountId().equals(simpleAccountInfo.getAccountId())){
                FinhubLogger.error("HupoMigrateCgbController.doAcctBalanceAdjust(), 按公司ID和平台方查询待调数账户与所提供的accountId不一致，req: {}, queryResult: {}", JSON.toJSONString(balanceAdjustReq), JSON.toJSONString(simpleAccountInfo));
                throw new FinhubException(1, "按公司ID和平台方查询待调数账户与所提供的accountId不一致");
            }
            //调减
            AcctBalanceAdjustReqDTO adjustReqDto = new AcctBalanceAdjustReqDTO();
            adjustReqDto.setCompanyId(balanceAdjustReq.getCompanyId());
            adjustReqDto.setBizNo(RandomUtils.bsonId());
//            adjustReqDto.setOriginBizNo();
            adjustReqDto.setBankName(balanceAdjustReq.getBankName());
            adjustReqDto.setOperationAmount(balanceAdjustReq.getAdjustAmount());
            adjustReqDto.setOperationType(FundAcctGeneralOptType.ADJUST_WITHDRAWAL.getKey());
            adjustReqDto.setOperationTypeDesc(FundAcctGeneralOptType.ADJUST_WITHDRAWAL.getDesc());
            adjustReqDto.setCustomerServiceId(balanceAdjustReq.getOperateId());
            adjustReqDto.setCustomerServiceName(balanceAdjustReq.getOperateName());
            adjustReqDto.setOperationDescription("人工调额");
            adjustReqDto.setOperationUserId(balanceAdjustReq.getOperateId());
            adjustReqDto.setOperationUserName(balanceAdjustReq.getOperateName());
            adjustReqDto.setOperationChannelType(OperationChannelType.MANUAL.getKey());
            String remark = "单边调减:" + Optional.ofNullable(balanceAdjustReq.getRemark()).orElse("");
            adjustReqDto.setRemark(remark);
            FinhubLogger.info("HupoMigrateCgbController#doAcctBalanceAdjust-acctBalanceAdjust-in#req:{}", JSON.toJSONString(adjustReqDto));
            acctBalanceAdjustRespDTO = acctMgrService.acctBalanceAdjust(adjustReqDto);
            FinhubLogger.info("HupoMigrateCgbController#doAcctBalanceAdjust-acctBalanceAdjust-in#resp:{}", JSON.toJSONString(acctBalanceAdjustRespDTO));
        } catch (Exception e){
            FinhubLogger.error("HupoMigrateCgbController#doAcctBalanceAdjust#error#req:{}", JSON.toJSONString(balanceAdjustReq), e);
        }
        return acctBalanceAdjustRespDTO;
    }

}
