package com.fenbeitong.fenbeipay.http.controller.tools;

import com.fenbeitong.fenbeipay.api.model.dto.tools.FreezenHandlerToolDTO;
import com.fenbeitong.fenbeipay.cashier.tools.FreezenToolsService;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

@HttpService("/internal/tools")
public class InternalToolsFreezenController {

    @Autowired
    private FreezenToolsService freezenToolsService;

    /**
     * @Description: 修改冻结流水
     * @methodName: batchEditFreezenFlow
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2021/4/23 10:31 上午
     **/
    @HttpService(value = "/batch/edit/freezen/flow", method = RequestMethod.POST)
    public void batchEditFreezenFlow(HttpRequest request, HttpResponse response) {
        FreezenHandlerToolDTO handlerToolDTO = request.getBodyObject(FreezenHandlerToolDTO.class);
        //参数校验
        freezenToolsService.batchEditFreezenFlow(handlerToolDTO);
        ResponseResultUtils.success(response, handlerToolDTO);
    }

    @HttpService(value = "/history/freezen/flow", method = RequestMethod.POST)
    public void historyFreezenFlow(HttpRequest request, HttpResponse response) {
        FreezenHandlerToolDTO handlerToolDTO = request.getBodyObject(FreezenHandlerToolDTO.class);
        //参数校验
        freezenToolsService.historyFreezenFlow(handlerToolDTO);
        ResponseResultUtils.success(response, handlerToolDTO);
    }
}
