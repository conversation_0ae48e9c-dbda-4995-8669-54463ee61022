package com.fenbeitong.fenbeipay.http.controller.acct;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyCardFlowService;
import com.fenbeitong.fenbeipay.api.model.dto.bank.BankCardDTO;
import com.fenbeitong.fenbeipay.bank.base.conver.BankCardConver;
import com.fenbeitong.fenbeipay.bank.base.manager.IBankCardManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.ApplyCardManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.VirtualCardCreditManager;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.fenbeipay.rpc.service.base.IBaseAcctKafkaServiceImpl;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.finhub.kafka.msg.dech.KafkaDechTradeResultMsg;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

@HttpService("/internal/virtual/card")
public class VirtualCardInnerController {

    @Autowired
    IBankCardManager iBankCardManager;

    @Autowired
    ApplyCardManager applyCardManager;

    @Autowired
    UAcctCompanyCardFlowService uAcctCompanyCardFlowService;
    @Autowired
    IBaseAcctKafkaServiceImpl iBaseAcctKafkaService;
    @Autowired
    VirtualCardCreditManager virtualCardCreditManager;

    /**
     * 虚拟卡信息更新
     * @param request
     * @param response
     */
    @HttpService(value = "/**************/update", method = RequestMethod.POST)
    public void update(HttpRequest request, HttpResponse response) {
        BankCardDTO reqDTO = request.getBodyObject(BankCardDTO.class);
        BankCard bankCard = BankCardConver.copyBankCardDTO2BankCard(reqDTO);
        iBankCardManager.updateBankCardById(bankCard,reqDTO.getId());
        ResponseResultUtils.success(response, null);
    }

    @HttpService(value = "/**************/update/type", method = RequestMethod.POST)
    public void updateType(HttpRequest request, HttpResponse response) {
        BankCardDTO reqDTO = request.getBodyObject(BankCardDTO.class);
        BankCard bankCard = BankCardConver.copyBankCardDTO2BankCard(reqDTO);
        iBankCardManager.updateBankCardTypeByCompanyId(bankCard.getCompanyId(),bankCard.getEmployeeId(),reqDTO.getCardStatus());
        ResponseResultUtils.success(response, null);
    }


    @HttpService(value = "/**************/flow/company/upgrade", method = RequestMethod.POST)
    public void flowCompanyUpgrade(HttpRequest request, HttpResponse response) {
        int count =  uAcctCompanyCardFlowService.updateUpgradeReturnFlow();
        ResponseResultUtils.success(response,count);
    }

    @HttpService(value = "/innerTest", method = RequestMethod.POST)
    public void innerTest(HttpRequest request, HttpResponse response) {
        KafkaDechTradeResultMsg iMessage = request.getBodyObject(KafkaDechTradeResultMsg.class);
        iBaseAcctKafkaService.handlerVirtualCardByMsg(iMessage);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/innerFix", method = RequestMethod.POST)
    public void innerFix(HttpRequest request, HttpResponse response) {
        KafkaDechTradeResultMsg iMessage = request.getBodyObject(KafkaDechTradeResultMsg.class);
        virtualCardCreditManager.handleRefundCreditAfterBankSuccess(iMessage.getTxnId(),iMessage.getAccountFlowId());
        ResponseResultUtils.success(response);
    }

}
