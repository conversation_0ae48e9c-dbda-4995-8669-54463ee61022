package com.fenbeitong.fenbeipay.http.controller.bank;

import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.BankUserCardOptReqDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.BankUserCardSearchReqDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.TradeStatusQueryReqDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.BankUserCardInfoRespDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.TradeRespDTO;
import com.fenbeitong.acctperson.api.service.search.IBankUserCardSearchService;
import com.fenbeitong.acctperson.api.service.trade.IBankUserCardTradeService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyCardService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctByCompanyModelReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctDebitByCompanyModelRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AllAcctByCompanyModelRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByComIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctComGwAcctRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctComGwRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankPettySearchService;
import com.fenbeitong.fenbeipay.api.service.gateway.IAcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.bank.base.db.mapper.BankCardApplyFlowMapper;
import com.fenbeitong.fenbeipay.bank.base.vo.BankCardSleepDataManual;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.SearchCardManager;
import com.fenbeitong.fenbeipay.core.enums.paycenter.AccountType;
import com.fenbeitong.fenbeipay.core.enums.virtualcard.VirtualCardTradeFundType;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.fenbeipay.dto.bank.BankCardApplyFlow;
import com.fenbeitong.fenbeipay.http.vo.BankUserCardOptDTO;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.CompanyCooperatingModel;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundBankUserCardOptType;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyAccountModelDetailDTO;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyModelUpdateV2DTO;
import com.fenbeitong.usercenter.api.service.company.ICompanyCooperatingModelService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@HttpService("/internal/bank/card/upgrade")
public class BankZbCardInnerController {

    @Autowired
    private IBankPettySearchService iBankPettySearchService;
    @Autowired
    private IBankUserCardTradeService iBankUserCardTradeService;
    @Autowired
    private IBankUserCardSearchService iBankUserCardSearchService;
    @Autowired
    private UAcctCompanyCardService uAcctCompanyCardService;
    @Autowired
    private ICompanyCooperatingModelService iCompanyCooperatingModelService;
    @Autowired
    private IAcctCompanyGatewayService iAcctCompanyGatewayService;
    @Autowired
    private IAcctMgrService iAcctMgrService;
    @Autowired
    private BankCardApplyFlowMapper bankCardApplyFlowMapper;
    @Autowired
    private SearchCardManager searchCardManager;

    @HttpService(value = "/manual/flow", method = RequestMethod.POST)
    public void manualCashFlow(HttpRequest request, HttpResponse response){
        //操作金额
        BankUserCardOptDTO reqDTO = request.getBodyObject(BankUserCardOptDTO.class);
        String bankAcctId = reqDTO.getBankAcctId();
        BigDecimal operationAmount = reqDTO.getOperationAmount();
        String bankName = BankNameEnum.ZBBANK.getCode();
        String bizNo = reqDTO.getBizNo();
        //保存交易流水
        BankCard bankCard = searchCardManager.queryByBankAcctId4Upgrade(bankAcctId,bankName);
        BankCardApplyFlow bankCardApplyFlow=new BankCardApplyFlow();
        bankCardApplyFlow.setCompanyId(bankCard.getCompanyId());
        bankCardApplyFlow.setEmployeeId(bankCard.getEmployeeId());
        bankCardApplyFlow.setEmployeeName(bankCard.getEmployeeName());
        bankCardApplyFlow.setEmployeePhone(bankCard.getEmployeePhone());
        bankCardApplyFlow.setFbCardNo(bankCard.getFbCardNo());
        bankCardApplyFlow.setBankAccountNo(bankCard.getBankAccountNo());
        bankCardApplyFlow.setBankName(bankCard.getBankName());
        bankCardApplyFlow.setAccountType(AccountType.Public_Type.getKey());
        bankCardApplyFlow.setUserUnitName(bankCard.getUserUnitName());
        bankCardApplyFlow.setCardModel(bankCard.getCardModel());
        bankCardApplyFlow.setCurrentAmount(bankCard.getCardBalance());
        bankCardApplyFlow.setOperationAmount(operationAmount);
        bankCardApplyFlow.setBalance(bankCard.getCardBalance().subtract(operationAmount));
        bankCardApplyFlow.setOperationCompanyAmount(operationAmount);
        bankCardApplyFlow.setCurrentCompanyAmount(bankCard.getCardCompanyBalance());
        bankCardApplyFlow.setCompanyBalance(bankCard.getCardCompanyBalance().subtract(operationAmount));
//        bankCardApplyFlow.setCompanyName(bankCard.getCom);
        bankCardApplyFlow.setUserUnitName(bankCard.getUserUnitName());
        bankCardApplyFlow.setBizNo(bizNo);
//        bankCardApplyFlow.setBankTransNo(bankCard.getBankTransNo());
        bankCardApplyFlow.setOperationType(BankApplyType.CONSUMPTION.getKey());
        bankCardApplyFlow.setOperationChannel(OperationChannelType.SYSTEM.getKey());
        bankCardApplyFlow.setOperationUserId("SYSTEM");
        bankCardApplyFlow.setOperationUserName("SYSTEM");
        bankCardApplyFlow.setCreateTime(new Date());
        bankCardApplyFlow.setApplyReason("众邦升级提现");
        bankCardApplyFlow.setApplyReasonDesc("众邦升级提现");
        bankCardApplyFlow.setPettyId(bankCard.getPettyId());
        bankCardApplyFlow.setTradeFundType(VirtualCardTradeFundType.COMPANY.getType());
        int count = bankCardApplyFlowMapper.insert(bankCardApplyFlow);
        ResponseResultUtils.success(response, count);
    }

    @HttpService(value = "/petty/stop", method = RequestMethod.POST)
    public void pettyStop(HttpRequest request, HttpResponse response) {
        iBankPettySearchService.findPettyOfRoundAndUpdateToStop();
        ResponseResultUtils.success(response, null);
    }

    @HttpService(value = "/cash/out", method = RequestMethod.POST)
    public void cashOut(HttpRequest request, HttpResponse response) {
        BankCardSleepDataManual reqDTO = request.getBodyObject(BankCardSleepDataManual.class);
        BankUserCardSearchReqDTO bankUserCardSearchReqDTO = new BankUserCardSearchReqDTO();
        bankUserCardSearchReqDTO.setCompanyId(reqDTO.getCompanyId());
        if (reqDTO.getEmployeeId()!= null) {
            bankUserCardSearchReqDTO.setEmployeeId(reqDTO.getEmployeeId());
        }
        bankUserCardSearchReqDTO.setBankName(BankNameEnum.ZBBANK.getCode());
        List<BankUserCardInfoRespDTO> bankUserCardInfoRespDTOS =  iBankUserCardSearchService.queryAllBankUserCardInfo(bankUserCardSearchReqDTO);
        if (bankUserCardInfoRespDTOS != null && bankUserCardInfoRespDTOS.size() > 0){
            for (BankUserCardInfoRespDTO bankUserCardInfoRespDTO:bankUserCardInfoRespDTOS) {
                cashOut0(reqDTO.getCompanyId(),bankUserCardInfoRespDTO);
            }
        }
        ResponseResultUtils.success(response, null);
    }
    @HttpService(value = "/cash/out/query", method = RequestMethod.POST)
    public void cashOutQuery(HttpRequest request, HttpResponse response) {
        BankCardSleepDataManual reqDTO = request.getBodyObject(BankCardSleepDataManual.class);
        TradeStatusQueryReqDTO tradeStatusQueryReqDTO = new TradeStatusQueryReqDTO();
        tradeStatusQueryReqDTO.setTradeType("3");
        tradeStatusQueryReqDTO.setOrderId(reqDTO.getCompanyId());
        TradeRespDTO tradeRespDTO =  iBankUserCardTradeService.queryTradeStatus(tradeStatusQueryReqDTO);
        ResponseResultUtils.success(response, tradeRespDTO);
    }

    @HttpService(value = "/account/stop", method = RequestMethod.POST)
    public void accountStop(HttpRequest request, HttpResponse response) {
        List<CompanyAccountModelDetailDTO> listO = Lists.newArrayList();
        BankCardSleepDataManual reqDTO = request.getBodyObject(BankCardSleepDataManual.class);
        AcctComGwByComIdReqDTO acctComGwByComIdReqDTO = new AcctComGwByComIdReqDTO();
        CheckUtils.checkNull(reqDTO.getCompanyId(), "企业ID不能为空");
        acctComGwByComIdReqDTO.setCompanyId(reqDTO.getCompanyId());
        AcctComGwRespDTO actGwsByComId = iAcctCompanyGatewayService.findActGwsByComId(acctComGwByComIdReqDTO);
        List<AcctComGwAcctRespDTO> acctComGwAcctRespDTOS = actGwsByComId.getAcctComGwAcctRespDTOS();


        for (AcctComGwAcctRespDTO acctComGwAcctRespDTO : acctComGwAcctRespDTOS) {
            CompanyAccountModelDetailDTO companyAccountModelDetailDTO = new CompanyAccountModelDetailDTO();
            companyAccountModelDetailDTO.setAccountId(acctComGwAcctRespDTO.getAccountId());
            companyAccountModelDetailDTO.setCompanyId(acctComGwAcctRespDTO.getCompanyId());
            companyAccountModelDetailDTO.setAccountModel(acctComGwAcctRespDTO.getAccountModel());
            companyAccountModelDetailDTO.setAccountSubType(acctComGwAcctRespDTO.getAccountSubType());
            companyAccountModelDetailDTO.setOperationUserId("SYSTEM");
            if (acctComGwAcctRespDTO.getAccountSubType() != FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey()) {
                listO.add(companyAccountModelDetailDTO);
            }
        }
        //备用金账户查询
        AcctByCompanyModelReqDTO acctByCompanyModelReqDTO = new AcctByCompanyModelReqDTO();
        acctByCompanyModelReqDTO.setCompanyId(reqDTO.getCompanyId());
        acctByCompanyModelReqDTO.setUpdateCompanyModel(CompanyCooperatingModel.RECHARGE.getKey());
        AllAcctByCompanyModelRespDTO allAcctByCompanyModelRespDTO = iAcctMgrService.getAllAcctByCompanyModel(acctByCompanyModelReqDTO);
        String accountId = null;
        if (allAcctByCompanyModelRespDTO != null && allAcctByCompanyModelRespDTO.getAcctDebitMainRespDTOList() != null){
            List<AcctDebitByCompanyModelRespDTO> acctDebitByCompanyModelRespDTOS =  allAcctByCompanyModelRespDTO.getAcctDebitMainRespDTOList();
            for (AcctDebitByCompanyModelRespDTO acctDebitByCompanyModelRespDTO: acctDebitByCompanyModelRespDTOS){
                if (acctDebitByCompanyModelRespDTO.getCompanyCardRespDTO() != null && acctDebitByCompanyModelRespDTO.getCompanyCardRespDTO().isActive()){
                    CompanyAccountModelDetailDTO companyAccountModelDetailDTO = new CompanyAccountModelDetailDTO();
                    companyAccountModelDetailDTO.setAccountId(acctDebitByCompanyModelRespDTO.getCompanyCardRespDTO().getAccountId());
                    companyAccountModelDetailDTO.setCompanyId(acctDebitByCompanyModelRespDTO.getCompanyCardRespDTO().getCompanyId());
                    companyAccountModelDetailDTO.setAccountModel(acctDebitByCompanyModelRespDTO.getCompanyCardRespDTO().getAccountModel());
                    companyAccountModelDetailDTO.setAccountSubType(acctDebitByCompanyModelRespDTO.getCompanyCardRespDTO().getAccountSubType());
                    companyAccountModelDetailDTO.setOperationUserId("SYSTEM");
                    if (!BankNameEnum.isZBBank(acctDebitByCompanyModelRespDTO.getCompanyCardRespDTO().getBankName())){
                        listO.add(companyAccountModelDetailDTO);
                    }else {
                        accountId = acctDebitByCompanyModelRespDTO.getCompanyCardRespDTO().getAccountId();
                    }
                }
            }
        }


        CompanyModelUpdateV2DTO companyModelUpdateV2DTO = new CompanyModelUpdateV2DTO();
        companyModelUpdateV2DTO.setCompanyId(reqDTO.getCompanyId());
        companyModelUpdateV2DTO.setAcctEffectReqList(listO);
        FinhubLogger.info("updateCompanyAccountV2:{}" , JsonUtils.toJson(companyModelUpdateV2DTO));
        iCompanyCooperatingModelService.updateCompanyAccountV2(companyModelUpdateV2DTO);
        if (!StringUtils.isBlank(accountId)) {
            uAcctCompanyCardService.disableAccountById("SYSTEM", accountId);
        }
        ResponseResultUtils.success(response, null);
    }

    public void cashOut0(String companyId,BankUserCardInfoRespDTO bankUserCardOptReqDTO){

        BankUserCardOptReqDTO bankOptReqDTO = new BankUserCardOptReqDTO();
        bankOptReqDTO.setCompanyId(companyId);
        bankOptReqDTO.setEmployeeId(bankUserCardOptReqDTO.getEmployeeId());
        bankOptReqDTO.setOperationType(FundBankUserCardOptType.WITHDRAWAL.getKey());
        bankOptReqDTO.setOperationAmount(bankUserCardOptReqDTO.getCardBalance());
        bankOptReqDTO.setTargetBankAccountNo(bankUserCardOptReqDTO.getBankAccountNo());
        bankOptReqDTO.setBankName(BankNameEnum.ZBBANK.getCode());
        bankOptReqDTO.setBankAcctId(bankUserCardOptReqDTO.getBankAcctId());
        bankOptReqDTO.setBizNo(IDGen.genId("CAI"));
        bankOptReqDTO.setOperationUserId(bankUserCardOptReqDTO.getEmployeeId());
        bankOptReqDTO.setOperationUserName(bankUserCardOptReqDTO.getEmployeeName());
        bankOptReqDTO.setBankAccountNo(bankUserCardOptReqDTO.getBankAccountNo());
        TradeRespDTO tradeRespDTO = iBankUserCardTradeService.withdrawalByBank(bankOptReqDTO);
    }
}
