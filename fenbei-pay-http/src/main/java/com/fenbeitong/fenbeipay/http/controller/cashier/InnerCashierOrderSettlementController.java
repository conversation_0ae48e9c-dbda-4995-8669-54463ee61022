package com.fenbeitong.fenbeipay.http.controller.cashier;


import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierOrderAutoPayStatus;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.PublicPayResult;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierCreatePayTradeRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierCreatePayTradeRPCVo;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierPayDetailRPCVo;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.CashierConfirmPayReqVO;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementLimitRequestService;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementV2Service;
import com.fenbeitong.fenbeipay.awplus.biz.PayBiz;
import com.fenbeitong.fenbeipay.awplus.model.dto.PersonPayRecordRespDTO;
import com.fenbeitong.fenbeipay.awplus.model.po.RefundsRequestPo;
import com.fenbeitong.fenbeipay.awplus.selector.PayChannelRouteBiz;
import com.fenbeitong.fenbeipay.awplus.service.gateway.huifu.app.HuifuAliPayAndRefundService;
import com.fenbeitong.fenbeipay.awplus.service.gateway.huifu.app.HuifuPayBaseService;
import com.fenbeitong.fenbeipay.cashier.manager.CashierOrderAutoPayRecordManager;
import com.fenbeitong.fenbeipay.cashier.manager.CashierOrderSettlementManager;
import com.fenbeitong.fenbeipay.cashier.pay.CashierOrderSettlementPayService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.cashier.settlement.PublicPayOrderSettlementService;
import com.fenbeitong.fenbeipay.core.common.base.PayContext;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderAutoPayRecord;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPay;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PayChannelRoute;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.CASHIER_CRONTAB_HEAD;
import static com.fenbeitong.finhub.common.utils.DateUtils.FORMAT_DATE_TIME_PATTERN;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * (内部系统调用的HTTP服务)收银台收款Http服务Controller
 * RPC服务请参考fenbei-pay-api
 * @version  2018年11月19日10:56:58
 * <AUTHOR>
 */

@HttpService("/internal/cashier/pay")
public class InnerCashierOrderSettlementController {

    @Autowired
    private PayBiz payBiz;

    @Autowired
    private CashierOrderSettlementService cashierOrderSettlementService;
    @Autowired
    private ICashierOrderSettlementV2Service cashierOrderSettlementV2Service;
    @Autowired
    private PublicPayOrderSettlementService publicPayOrderSettlementService;
    @Autowired
    private CashierOrderSettlementPayService cashierOrderSettlementPayService;
    @Autowired
    private CashierSearchOrderService cashierSearchOrderService;

    @Autowired
    private PayChannelRouteBiz payChannelRouteBiz;

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private HuifuAliPayAndRefundService huifuAliPayAndRefundService;

    @Autowired
    private ICashierOrderSettlementLimitRequestService iCashierOrderSettlementLimitRequestService;

    @Autowired
    private CashierOrderSettlementManager cashierOrderSettlementManager;

    @Autowired
    private CashierOrderAutoPayRecordManager cashierOrderAutoPayRecordManager;

    /**
     * 时间程序，拉取三方支付结果，更新支付状态，并通知场景方
     * @param request
     * @param response
     */
    @HttpService(value = "/cronpullthird/v2",method = RequestMethod.POST)
    public void queryThirdPay(HttpRequest request, HttpResponse response){
        FinhubLogger.info(CASHIER_CRONTAB_HEAD+"拉取三方支付结果，更新支付状态，并通知场景方...Start.....");
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.cronPullThirdResult();
        });
        ResponseResultUtils.success(response,null);
    }


    /**
     * 广发直联错花还款
     * 时间程序，拉取银行卡支付结果，更新支付状态，并通知场景方
     * @param request
     * @param response
     */
    @HttpService(value = "/cronpull/bankcard/v2",method = RequestMethod.POST)
    public void cronpullBankCardV2(HttpRequest request, HttpResponse response){
        FinhubLogger.info(CASHIER_CRONTAB_HEAD+"拉取银行卡支付结果，更新支付状态，并通知场景方...Start.....");
        int pullCount = cashierOrderSettlementService.cronPullBankCardResult();
        ResponseResultUtils.success(response,pullCount);
    }

    /**
     * 时间程序，时间已经过期，状态为：交易待支付；等待三方支付中；支付交易已创建；
     * 打车场景没有自动取消
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/croncanceltrade/v2",method = RequestMethod.POST)
    public void  cronCancelOrderTrade(HttpRequest request, HttpResponse response){
        FinhubLogger.info(CASHIER_CRONTAB_HEAD+"取消到期未支付，并返还已扣分贝券+分贝币+因公...Start.....");
        StringBuffer sb = new StringBuffer();
        List<CashierOrderSettlement> cashierOrderSettlements = cashierOrderSettlementService.queryCashierCanCancel();
        if (CollectionUtils.isNotEmpty(cashierOrderSettlements)) {
            for (CashierOrderSettlement cashierOrderSettlement : cashierOrderSettlements) {
                try{
                    String orderId  = cashierOrderSettlementService.cronCancelOrderTrade(cashierOrderSettlement);
                    if(orderId!=null){
                        sb.append(orderId);
                    }
                }catch (Exception e){
                    FinhubLogger.error(CASHIER_CRONTAB_HEAD + "取消交易出错 cashierOrderSettlement={},Exception={}", JsonUtils.toJson(cashierOrderSettlement), e);
                    continue;
                }
            }
        }
        ResponseResultUtils.success(response,sb.toString());
    }

    /**
     * 时间程序，通知场景方完成订单，状态为已支付
     * @param request
     * @param response
     */
    @HttpService(value = "/croncallbiz4haddone/v2",method = RequestMethod.POST)
    public void cronCallbBiz4HadDone(HttpRequest request, HttpResponse response){
        FinhubLogger.info(CASHIER_CRONTAB_HEAD+"通知场景方完成订单，状态为已支付...Start.....");
        CompletableFuture.runAsync(() ->{
         cashierOrderSettlementService.cronCallBiz4HadDone();
        });
        ResponseResultUtils.success(response);
    }

    /**
     * (HTTP Internal)取消支付，只有待三方支付的可以取消，已支付与已完成的是退款流程
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/cancelordertrade/v2",method = RequestMethod.POST)
    public void  cancelOrderTrade(HttpRequest request, HttpResponse response){
        CashierCancelTradeReqVo cashierCancelTradeReqVo = request.getBodyObject(CashierCancelTradeReqVo.class);
        if(cashierCancelTradeReqVo == null || StringUtils.isEmpty(cashierCancelTradeReqVo.getEmployeeId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        CashierCancelTradeRespVo cashierCancelTradeRespVo = cashierOrderSettlementService.cancelOrderTradeAndRefundSaas(cashierCancelTradeReqVo);
        ResponseResultUtils.success(response,cashierCancelTradeRespVo);
    }

    /**
     * (HTTP Internal+RPC)获取可用分贝券与分贝币
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/bqability/v2",method = RequestMethod.POST)
    public void payBQAbility(HttpRequest request, HttpResponse response){
        PersonFBBQAbilityReqVo personFBBQAbilityReqVo =request.getBodyObject(PersonFBBQAbilityReqVo.class);
        PersonFBBQAbilityRespVo personFBBQAbilityVo = cashierOrderSettlementService.payBQAbility(personFBBQAbilityReqVo);
        ResponseResultUtils.success(response,personFBBQAbilityVo);
    }

    /**
     * (HTTP Internal+RPC)交易详情查询（因公+因私【分贝券|分贝币|个人支付】)
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/querypay/detail/v2",method = RequestMethod.POST)
    public void getPayDetail(HttpRequest request, HttpResponse response){
        CashierQueryReqVo cashierQueryReqVo = request.getBodyObject(CashierQueryReqVo.class);
        List<CashierQueryRespVo> settlement = cashierOrderSettlementService.queryCashierDetail(cashierQueryReqVo);
        ResponseResultUtils.success(response,settlement);
    }

    /**
     * (HTTP Internal+RPC)可用支付能力获取(券|币|微信|支付宝)+订单支付详情
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/payability/v2",method = RequestMethod.POST)
    public void getAllPayAbility(HttpRequest request, HttpResponse response){
        PersonPayAbilityReqVo personPayAbilityReqVo = request.getBodyObject(PersonPayAbilityReqVo.class);
        PersonPayAbilityRespVo personPayAbilityVo = cashierOrderSettlementService.getAllPayAbility(personPayAbilityReqVo);
        ResponseResultUtils.success(response,personPayAbilityVo);
    }

    /**
     * (HTTP Internal+RPC)创建支付交易流水号(也称为：预支付)
     * 场景OC系统调用，生成支付交易流水号，如果因公支付，企业支付部分需要提前扣除，也在此扣除
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/createordertrade/v2",method = RequestMethod.POST)
    public void  createOrderTrade(HttpRequest request, HttpResponse response){
        CashierCreateTradeReqVo cashierCreateTradeReqVo = request.getBodyObject(CashierCreateTradeReqVo.class);
        if(StringUtils.isEmpty(cashierCreateTradeReqVo.getEmployeeId())||StringUtils.isEmpty(cashierCreateTradeReqVo.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        CashierCreateTradeRespVo cashierCreateTradeRespVo;
        try {
            cashierCreateTradeRespVo = cashierOrderSettlementService.createOrderTradeAndSaas(cashierCreateTradeReqVo);
        } catch (Exception e) {
            String msgError = "【收银台】创建支付异常:场景" + cashierCreateTradeReqVo.getOrderType() + "订单号:" + cashierCreateTradeReqVo.getFbOrderId() + e.getMessage();
            FinhubLogger.error(msgError, e);
            throw e;
        }
        //通知
        CompletableFuture.runAsync(() -> cashierOrderSettlementService.callCashierSettlement(cashierCreateTradeRespVo.getFbOrderId(),cashierCreateTradeRespVo.getCashierTxnId(), cashierCreateTradeRespVo.getEmployeeId()));
        ResponseResultUtils.success(response,cashierCreateTradeRespVo);
    }

    /**
     * (HTTP内网)支付接口-接收用户选择的各种支付能力，并做扣减操作
     * 根据需要三方支付(支付宝|微信)支付的金额，发起三方支付，获得支付票据信息,返给APP|H5
     * WX统一下单wiki：https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=9_1
     * AliPay统一收单交易创建接口wiki:https://docs.open.alipay.com/api_1/alipay.trade.create/
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/payordertrade/v2", method = RequestMethod.POST)
    public void payOrderTrade(HttpRequest request, HttpResponse response) {
        CashierPayTradeReqVo cashierPayTradeReqVo = request.getBodyObject(CashierPayTradeReqVo.class);
        if (StringUtils.isEmpty(cashierPayTradeReqVo.getEmployeeId()) || StringUtils.isEmpty(cashierPayTradeReqVo.getCompanyId())) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        PayContext.setClientIp(request.getIp());
        CashierPayTradeRespVo cashierPayTradeRespVo = cashierOrderSettlementService.payOrderTradeAndSaas(cashierPayTradeReqVo);

        CompletableFuture.runAsync(() -> {
            //通知
            cashierOrderSettlementService.callCashierSettlement(cashierPayTradeRespVo.getFbOrderId(),cashierPayTradeRespVo.getCashierTxnId(), cashierPayTradeReqVo.getEmployeeId());
        });

        ResponseResultUtils.success(response, cashierPayTradeRespVo);
    }

    /**
     * 创建支付交易流水号+支付完成+无管控+无回掉(万能单)
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/createpaytrade/v2",method = RequestMethod.POST)
    public void  createPayOrderTrade(HttpRequest request, HttpResponse response){
        CashierCreatePayTradeReqVo createPayTradeReqVo = request.getBodyObject(CashierCreatePayTradeReqVo.class);
        if(StringUtils.isEmpty(createPayTradeReqVo.getEmployeeId())||StringUtils.isEmpty(createPayTradeReqVo.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        boolean saasBuget = false;
        CashierCreatePayTradeRespVo createPayTradeRespVo = cashierOrderSettlementService.createAndPayOrderTradeOrSass(createPayTradeReqVo,saasBuget);
        //无通知
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.callCashierSettlement(createPayTradeRespVo.getFbOrderId(),createPayTradeRespVo.getCashierTxnId(), createPayTradeRespVo.getEmployeeId());
        });
        ResponseResultUtils.success(response,createPayTradeRespVo);
    }

    /**
     * 客服操作，修改收银数据后，重新支付
     * 如果没有个人支付，只有企业支付，则直接支付，并通知场景
     * 如果有个人支付，个人支付金额需要提交后让用户自行支付请提前协商好再操作
     * 状态图：fenbei-pay-cs-update-cashier.puml
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/cs_update_cashier/v2",method = RequestMethod.POST)
    public void  csUpdateCashier(HttpRequest request, HttpResponse response){
        CashierCsUpdateReqVo updateVo = request.getBodyObject(CashierCsUpdateReqVo.class);
        if(updateVo.hasNoAmount()){
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_NOMATCH_ERROR);
        }
        boolean saasBuget = true;
        CashierUpdatePayTradeRespVo updatePayTradeRespVo = cashierOrderSettlementService.csUpdateCashier(updateVo,saasBuget);
        ResponseResultUtils.success(response,updatePayTradeRespVo);
    }

    /**
     * 支付完成+有管控+无回掉(客服操作支付)
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/paytrade4cs/v2",method = RequestMethod.POST)
    public void  payOrderTrade4CS(HttpRequest request, HttpResponse response){
        CashierCsPayReqVo cashierCsPayReqVo = request.getBodyObject(CashierCsPayReqVo.class);
        boolean saasBuget = true;
        CashierCreatePayTradeRespVo createPayTradeRespVo = cashierOrderSettlementService.payOrderTradeOrSass4CS(cashierCsPayReqVo,saasBuget);
        //无通知
        ResponseResultUtils.success(response,createPayTradeRespVo);
    }

    /**
     * 创建支付交易流水号+支付完成+有管控+无回掉(回填单)
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/createpaytrade_sass/v2",method = RequestMethod.POST)
    public void  createPayOrderTradeSaas(HttpRequest request, HttpResponse response){
        CashierCreatePayTradeReqVo createPayTradeReqVo = request.getBodyObject(CashierCreatePayTradeReqVo.class);
        if(StringUtils.isEmpty(createPayTradeReqVo.getEmployeeId())||StringUtils.isEmpty(createPayTradeReqVo.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        CashierCreatePayTradeRespVo createPayTradeRespVo = cashierOrderSettlementService.createAndPayOrderTradeOrSass(createPayTradeReqVo,true);
        //子单 通知
        CompletableFuture.runAsync(() -> cashierOrderSettlementService.callCashierSettlement(createPayTradeRespVo.getFbOrderId(),createPayTradeRespVo.getCashierTxnId(), createPayTradeRespVo.getEmployeeId()));
        ResponseResultUtils.success(response,createPayTradeRespVo);
    }

    /**
     * (HTTP Internal+RPC)业务系统查询支付结果
     * @param request
     * @param response
     * @return
     */
    @HttpService("/checkpayresult/v2")
    public void getPayResult(HttpRequest request, HttpResponse response){
        CashierQueryCommonReqVo cashierQueryCommonReqVo = request.getBodyObject(CashierQueryCommonReqVo.class);
        CashierOrderSettlement settlement = cashierOrderSettlementService.queryCashierOrderSettlement(cashierQueryCommonReqVo);
        ResponseResultUtils.success(response,settlement);
    }

    /**
     * 场景批量查询支付单[暂定最大1000条]
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/batchquery/order/v2",method = RequestMethod.POST)
    public void batchQuery(HttpRequest request, HttpResponse response){
        CashierBatchQueryReqVo cashierBatchQueryReqVo = request.getBodyObject(CashierBatchQueryReqVo.class);
        List<CashierBatchQueryRespVo> settlement = cashierOrderSettlementService.batchQuery(cashierBatchQueryReqVo);
        ResponseResultUtils.success(response,settlement);
    }

    @HttpService(value = "/createpaynoemployeetrade/v3",method = RequestMethod.POST)
    public void createAndPayOrderTrade4NoEmployee(HttpRequest request, HttpResponse response){
        CashierCreatePayNoEmployeeTradeReqVo cashierCreatePayNoEmployeeTradeReqVo = request.getBodyObject(CashierCreatePayNoEmployeeTradeReqVo.class);
        CashierCreatePayNoEmployeeTradeRespVo CashierCreatePayNoEmployeeTradeRespVo = cashierOrderSettlementService.createAndPayOrderTrade4NoEmployee(cashierCreatePayNoEmployeeTradeReqVo);
        //通知
        cashierOrderSettlementService.checkPayStatusAndCallBizByFbOrderId(CashierCreatePayNoEmployeeTradeRespVo.getFbOrderId());
        CashierCreatePayTradeRespVo createPayTradeRPCDTO = new CashierCreatePayTradeRespVo();
        BeanUtils.copyProperties(CashierCreatePayNoEmployeeTradeRespVo, createPayTradeRPCDTO);
        ResponseResultUtils.success(response, createPayTradeRPCDTO);
    }

    @HttpService(value = "/synpayordertrade/v3",method = RequestMethod.POST)
    public void  synPayOrderTrade(HttpRequest request, HttpResponse response){
        CashierTradeSynOrderReqVo cashierTradeSynOrderReqVo = request.getBodyObject(CashierTradeSynOrderReqVo.class);
        cashierOrderSettlementService.synPayOrderTrade(cashierTradeSynOrderReqVo);
        ResponseResultUtils.success(response, null);
    }

    /**
     * 清洗费用归属中部门全路径，补数据功能，补得随便使用
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/costpath/v1",method = RequestMethod.POST)
    public void  updateCostUnitPath(HttpRequest request, HttpResponse response){
        String startTime  = request.getParameter("startTime");
        String endTime  = request.getParameter("endTime");
        if(ObjUtils.isBlank(startTime)||ObjUtils.isBlank(endTime)){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date start = DateUtils.toDate(startTime,FORMAT_DATE_TIME_PATTERN);
        Date end = DateUtils.toDate(endTime,FORMAT_DATE_TIME_PATTERN);

        int i = cashierOrderSettlementService.updateCostUnitPath(start, end);
        //无通知
        ResponseResultUtils.success(response,i);
    }

    /**
     * 余额不足订单 定时去发起支付
     * @param request
     * @param response
     */
    @HttpService(value = "/cron/call/notenough",method = RequestMethod.POST)
    public void cronCallNotEnough(HttpRequest request, HttpResponse response){
        int count = cashierOrderSettlementService.cronCallNotEnough();
        ResponseResultUtils.success(response,count);
    }

    /**
     * 订单中心清洗历史支付流水
     * @param request
     * @param response
     */
    @HttpService(value = "record/history/query",method = RequestMethod.POST)
    public void recordHistoryQuery(HttpRequest request, HttpResponse response){
        List<String> fbOrderIds = request.getBodyArray(String.class);
        List<PersonPayRecordRespDTO> personRefundRecordRespDTOList = payBiz.queryPersonPayRecordHistory(fbOrderIds);
        ResponseResultUtils.success(response,personRefundRecordRespDTOList);
    }

    @HttpService(value = "bank/ent/pay/result/query")
    public void bankEntPayResultQuery(HttpRequest request, HttpResponse response){
        List<String> fbOrderIds = request.getBodyArray(String.class);
        for (String fbOrderId:fbOrderIds) {
            CashierConfirmPayReqVO cashierPayReqVO = new CashierConfirmPayReqVO();
            cashierPayReqVO.setFbOrderId(fbOrderId);
            cashierOrderSettlementV2Service.confirmPay(cashierPayReqVO);
        }
    }

    @HttpService(value = "salary/batch/pay")
    public void salaryBatchPay(HttpRequest request, HttpResponse response){
        cashierOrderSettlementV2Service.asyncPayByCron();
        ResponseResultUtils.success(response,"call success");
    }

    @HttpService(value = "salary/batch/pay/result/notify")
    public void salaryBatchPayResultNotify(HttpRequest request, HttpResponse response){
        cashierOrderSettlementV2Service.asyncNotifyByCron();
        ResponseResultUtils.success(response,"call success");
    }

    @HttpService(value = "salary/batch/pay/result")
    public void salaryBatchPayResult(HttpRequest request, HttpResponse response){
        cashierOrderSettlementV2Service.queryByCron();
        ResponseResultUtils.success(response,"call success");
    }


    @HttpService(value = "/cron/recover-public-pay-batch")
    public void recoverPublicePayBatch(HttpRequest request, HttpResponse response) {
        try {
            String beginTimeString = request.getParameter("beginTime");
            String endTimeString = request.getParameter("endTime");
            Timestamp beginTime = StringUtils.isBlank(beginTimeString) ? null : new Timestamp(DateUtil.getDateFromStringByDefaultDateFormat(beginTimeString).getTime());
            Timestamp endTime = StringUtils.isBlank(endTimeString) ? null : new Timestamp(DateUtil.getDateFromStringByDefaultDateFormat(endTimeString).getTime());
            publicPayOrderSettlementService.recoverOrderAcctPublicPayTrade(beginTime, endTime);
            ResponseResultUtils.success(response, "call success");
        } catch (Exception e) {
            FinhubLogger.error("error while recoverPublicePayBatch, {}", e.getMessage(), e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), e.getMessage()));
        }
    }

    @HttpService(value = "/recover-public-pay-by-order")
    public void recoverPublicePayByOrder(HttpRequest request, HttpResponse response) {
        try {
            String fbOrderId = request.getParameter("fbOrderId");
            if (StringUtils.isBlank(fbOrderId)) {
                FinhubLogger.error("param fbOrderId is null while recoverPublicePayByOrder");
                ResponseResultUtils.success(response, "call failed");
            }
            List<CashierOrderSettlementPay> payOrderList = cashierOrderSettlementPayService.getCashierPayListByFbOrderId(fbOrderId);
            if (payOrderList.size() < 1) {
                String msg = "found none by fbOrderId while recoverPublicePayByOrder, " + fbOrderId;
                FinhubLogger.error(msg);
                ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), msg));
                return;
            } else if (payOrderList.size() > 1) {
                String msg = "found more than 1 records by fbOrderId while recoverPublicePayByOrder, " + fbOrderId;
                FinhubLogger.error(msg);
                ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), msg));
                return;
            }
            CashierOrderSettlementPay payOrder = payOrderList.get(0);
            publicPayOrderSettlementService.recoverOrderAcctPublicPayTrade(payOrder);
            ResponseResultUtils.success(response, "call success");
        } catch (Exception e) {
            FinhubLogger.error("error while recoverPublicePayByOrder, {}", e.getMessage(), e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), e.getMessage()));
        }
    }

    @HttpService(value = "/query-order-result")
    public void queryOrderResult(HttpRequest request, HttpResponse response) {
        try {
            String fbOrderId = request.getParameter("fbOrderId");
            CashierPayDetailRPCVo req = new CashierPayDetailRPCVo();
            req.setFbOrderId(fbOrderId);
            PublicPayResult result = cashierSearchOrderService.queryPublicPayOrderResult(fbOrderId);
            FinhubLogger.info("result = {}", result);
            ResponseResultUtils.success(response, result);
        } catch (Exception e) {
            FinhubLogger.error(e.getMessage(), e);
        }
    }
    
    @HttpService(value = "/upsert/pay/hannel",method = RequestMethod.POST)
    public void upsertPayChannel(HttpRequest request, HttpResponse response){
    	List<PayChannelRoute> list = JSON.parseObject(request.getBody(), new TypeReference<List<PayChannelRoute>>() {});
    	boolean done = payChannelRouteBiz.saveOrUpdatePayChannelRoute(list);
    	ResponseResultUtils.success(response, done);
    }

    @HttpService(value = "/retry/refund/direct", method = RequestMethod.POST)
    public void retryRefundDirect(HttpRequest request, HttpResponse response){
        RefundsRequestPo refundRequest = request.getBodyObject(RefundsRequestPo.class);
        huifuAliPayAndRefundService.refund(refundRequest);
        ResponseResultUtils.success(response, true);
    }
    
    @HttpService(value = "/del/retry/refund")
    public void delRetryRefund(HttpRequest request, HttpResponse response){
    	redisDao.deleteKey(HuifuPayBaseService.REFUND_FAILURE_RETRY_COLLECTION);
        ResponseResultUtils.success(response, true);
    }

    /**
     * 取消订单
     * 取消订单中包含三方支付成功的
     * @param request 请求参数
     * @param response 返回参数
     */
    @HttpService(value = "/cron/call/cancel/refund",method = RequestMethod.POST)
    public void cronCallCancelRefund(HttpRequest request, HttpResponse response){
        int count = cashierOrderSettlementService.cronCallCancelRefund();
        ResponseResultUtils.success(response,count);
    }

    @HttpService(value = "/mock/third/pay/callback",method = RequestMethod.POST)
    public void mockUpdate(HttpRequest request, HttpResponse response){
        String cashierTxnId = request.getParameter("cashierTxnId");
        String payTxnId = request.getParameter("payTxnId");
        Integer payStatus = Integer.valueOf(request.getParameter("payStatus"));
        cashierOrderSettlementService.notifyThirdPartyPayResult(cashierTxnId, payTxnId,payStatus);
    }

    /**
     * 风控拦截发起退款
     * <AUTHOR>
     * @date 2023-03-11 19:05:44
     */
    @HttpService(value = "/third/risk/refund")
    public void thirdRiskRefund(HttpRequest request, HttpResponse response){
        CompletableFuture.runAsync(() -> cashierOrderSettlementService.thirdRiskRefundTask());
    }

    /**
     * 内部测试接口
     * <AUTHOR>
     * @date 2023-03-11 19:05:44
     */
    @HttpService(value = "/createAndPayOrderTrade")
    public void createAndPayOrderTrade(HttpRequest request, HttpResponse response){
        CashierCreatePayTradeRPCVo createPayTradePPCVo = request.getBodyObject(CashierCreatePayTradeRPCVo.class);
        CashierCreatePayTradeRPCDTO andPayOrderTrade = iCashierOrderSettlementLimitRequestService.createAndPayOrderTrade(createPayTradePPCVo);
    }


    /**
     * 补调自动付款接口-按订单ID
     *
     */
    @HttpService(value = "/try-auto-pay-by-cashier-order-id")
    public void tryAutoPayByCashierOrderId(HttpRequest request, HttpResponse response) {
        String cashierOrderId = request.getParameter("cashierOrderId");
        FinhubLogger.info("tryAutoPayByCashierOrderId调用，" + cashierOrderId);
        if (StringUtils.isBlank(cashierOrderId)) {
            throw new IllegalArgumentException("param cashierOrderId is null");
        }
        try {
            CashierOrderAutoPayRecord autoPayRecord = this.cashierOrderAutoPayRecordManager.queryByCashierOrderId(cashierOrderId);
            CashierOrderSettlement cashierOrder = cashierOrderSettlementManager.queryById(cashierOrderId, false);
            if (autoPayRecord == null || cashierOrder == null) {
                throw new IllegalArgumentException("param cashierOrderId invalid, found no record, " + cashierOrderId);
            }
            if (autoPayRecord.getStatus() != CashierOrderAutoPayStatus.INIT.getCode()) {
                throw new FinhubException(1, "invalid autoPayRecord status, not INIT, " + cashierOrderId);
            }
            if (this.cashierOrderAutoPayRecordManager.validateAutoPayStatus(cashierOrder)) {
                cashierOrderSettlementService.payTotalPublicOrderTrade(cashierOrderId);
            } else {
                FinhubLogger.info("处理充值后自动支付订单时，检查收银台订单状态已不是待支付状态，略过此订单，" + cashierOrderId);
            }
            cashierOrderAutoPayRecordManager.updateStatus(autoPayRecord.getId(), CashierOrderAutoPayStatus.FINISHED.getCode());
            autoPayRecord.setStatus(CashierOrderAutoPayStatus.FINISHED.getCode());
            ResponseResultUtils.success(response, autoPayRecord);
        } catch (Exception e) {
            if (!(e instanceof FinAccountNoEnoughException)) {
                FinhubLogger.error("error while payTotalPublicOrderTrade by tryAutoPayByCashierOrderId(), {}, error: {}", cashierOrderId, e.getMessage(), e);
            } else {
                FinhubLogger.info("account not enough while payTotalPublicOrderTrade by tryAutoPayByCashierOrderId(), {}", cashierOrderId);
            }
            ResponseResultUtils.fail(response, new FinhubException(1, e.getMessage()));
        }
    }

    /**
     * 补调自动付款接口-按公司
     *
     */
    @HttpService(value = "/try-auto-pay-by-company-id")
    public void tryAutoPayByCompanyId(HttpRequest request, HttpResponse response) {
        String companyId = request.getParameter("companyId");
        String beginTimeStr = request.getParameter("beginTime");
        FinhubLogger.info("tryAutoPayByCompanyId，" + companyId);
        if (StringUtils.isBlank(companyId)) {
            throw new IllegalArgumentException("param companyId is null");
        }
        try {
            Date beginTime;
            if (StringUtils.isNotBlank(beginTimeStr)) {
                beginTime = com.fenbeitong.common.utils.date.DateUtils.parse(beginTimeStr, "yyyy-MM-dd");
            } else {
                beginTime = com.fenbeitong.common.utils.date.DateUtils.addDay(new Date(), -30 * 6);
            }
            List<CashierOrderAutoPayRecord> recordList = cashierOrderAutoPayRecordManager.queryToBePaidByCompanyId(companyId, beginTime);
            int succCount = 0;
            for (CashierOrderAutoPayRecord record : recordList) {
                try {
                    CashierOrderSettlement cashierOrder = cashierOrderSettlementManager.queryById(record.getCashierOrderId(), false);
                    if (this.cashierOrderAutoPayRecordManager.validateAutoPayStatus(cashierOrder)) {
                        cashierOrderSettlementService.payTotalPublicOrderTrade(record.getCashierOrderId());
                        succCount++;
                    } else {
                        FinhubLogger.info("处理充值后自动支付订单时，检查收银台订单状态已不是待支付状态，略过此订单，" + record.getCashierOrderId());
                    }
                    cashierOrderAutoPayRecordManager.updateStatus(record.getId(), CashierOrderAutoPayStatus.FINISHED.getCode());
                    succCount++;
                } catch (Exception e) {
                    if (!(e instanceof FinAccountNoEnoughException)) {
                        FinhubLogger.error("error while payTotalPublicOrderTrade by tryAutoPayByCompanyId, {}, error: {}", record.getCashierOrderId(), e.getMessage(), e);
                    } else {
                        FinhubLogger.info("account not enough while payTotalPublicOrderTrade by tryAutoPayByCompanyId, {}", record.getCashierOrderId());
                    }
                }
            }
            if (!recordList.isEmpty()) {
                FinhubLogger.info("payTotalPublicOrderTrade by AutoPayCashierOrderListener, total: {}, succ: {}", recordList.size(), succCount);
            }
            if (recordList.size() >= 100) {
                FinhubLogger.info("payTotalPublicOrderTrade by AutoPayCashierOrderListener, 企业有超过100条待支付记录，超过监听器最大处理限量，需继续处理。" + companyId);
            }
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("companyId", companyId);
            resultMap.put("orderSize", recordList.size());
            resultMap.put("successSize", succCount);
            ResponseResultUtils.success(response, resultMap);
        } catch (Exception e) {
            FinhubLogger.error("error while tryAutoPayByCompanyId(), companyId: {}, errormsg: {}", companyId, e.getMessage(), e);
            ResponseResultUtils.fail(response, new FinhubException(1, e.getMessage()));
        }
    }

    /**
     * 时间程序，拉取airwallex支付结果，更新支付状态
     */
    @HttpService(value = "/cron/airwallex",method = RequestMethod.POST)
    public void queryAirwallexOrder(HttpRequest request, HttpResponse response){
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.cronPullAirwallexOrderResult();
        });
        ResponseResultUtils.success(response,null);
    }
}
