package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessDebitFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctGeneralService;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankRefundTradeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.CompanyCreditInfoDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankAccountService;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubService;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyCreditInfoVo;
import com.fenbeitong.fenbeipay.core.service.kafka.AccountInfoUpdateKafka;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessDebitFlow;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * 账户相关接口
 * Created by xjf on 2018/8/10.
 * @modify renfj on 2018/12/17
 */
@HttpService("/internal/account")
public class InnerAccountController {

    @Autowired
    private IAccountSubService iAccountSubService;
    @Autowired
    private IBankAccountService iBankAccountService;

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private AccountInfoUpdateKafka accountInfoUpdateKafka;
    @Autowired
    private UAcctGeneralService uAcctGeneralService;
    
    @Autowired
    private UAcctBusinessDebitFlowService acctBusinessDebitFlowService;


    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/company/credit/overview/{company_id}", method = RequestMethod.GET)
    public void companyCreditOverview(HttpRequest request, HttpResponse response) {
        String companyId = request.getPathValue("company_id");
        CheckUtils.checkEmpty(companyId, "company_id is null");
        CompanyCreditInfoDTO companyCreditInfoDTO = iAccountSubService.getCreditOverView(companyId);
        CompanyCreditInfoVo companyCreditInfoVo = null;
        if (ObjUtils.isNotBlank(companyCreditInfoDTO)) {
            companyCreditInfoVo = new CompanyCreditInfoVo(companyCreditInfoDTO.getInitCredit(), companyCreditInfoDTO.getAvailableCredit(),
                    companyCreditInfoDTO.getVoucherCredit(), companyCreditInfoDTO.getIndividualCredit(),companyCreditInfoDTO.getIndividualInitCredit(),
                    companyCreditInfoDTO.getCompanyCooperatingModel(),companyCreditInfoDTO.getBussinessAccountModelType(),
                    companyCreditInfoDTO.getIndividualAccountModelType());
        }
        ResponseResultUtils.success(response, companyCreditInfoVo);
    }

    @HttpService(value = "/repaymentRefund", method = RequestMethod.POST)
    public void repaymentRefund(HttpRequest request, HttpResponse response) {
        BankRefundTradeReqDTO accountSubVo = request.getBodyObject(BankRefundTradeReqDTO.class);
        iBankAccountService.repaymentRefund(accountSubVo);
        ResponseResultUtils.success(response);

    }
    
    @HttpService(value = "/update/business/acct/receipt", method = RequestMethod.POST)
    public void updateReceipt(HttpRequest request, HttpResponse response) {
    	AcctBusinessDebitFlow debitFlow = request.getBodyObject(AcctBusinessDebitFlow.class);
        acctBusinessDebitFlowService.updateById(AcctBusinessDebitFlow.builder().id(debitFlow.getId()).costImageUrl(debitFlow.getCostImageUrl()).build());
        ResponseResultUtils.success(response);

    }

    @HttpService(value = "/setRediss", method = RequestMethod.POST)
    public void setRediss(HttpRequest request, HttpResponse response) {
        Map<String, String> bodyObject = request.getBodyObject(Map.class);
        redisDao.getRedisTemplate().opsForValue().set(bodyObject.get("key"), bodyObject.get("value"));
        ResponseResultUtils.success(response, "success");
    }
    @HttpService(value = "/getRediss", method = RequestMethod.POST)
    public void getRediss(HttpRequest request, HttpResponse response) {
        Map<String, String> bodyObject = request.getBodyObject(Map.class);
        Object upgradeInfo = redisDao.getRedisTemplate().opsForValue().get(bodyObject.get("key"));
        ResponseResultUtils.success(response, upgradeInfo);
    }

    @HttpService(value = "/delRediss", method = RequestMethod.POST)
    public void delRediss(HttpRequest request, HttpResponse response) {
        Map<String, String> bodyObject = request.getBodyObject(Map.class);
        redisDao.getRedisTemplate().delete(bodyObject.get("key").toString());
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/sendAccountInfoUpdateMsg", method = RequestMethod.POST)
    public void sendAccountInfoUpdateMsg(HttpRequest request, HttpResponse response) {
        Map<String, Object> reqMap = request.getBodyObject(Map.class);
        String companyId = reqMap.get("companyId").toString();
        String accountId = reqMap.get("accountId").toString();
        String businessType = reqMap.get("businessType").toString();
        Integer optType = Integer.valueOf(reqMap.get("optType").toString());
        accountInfoUpdateKafka.sendAccountInfoUpdateMsg(companyId, accountId, businessType, optType);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/queryTxnReceipt", method = RequestMethod.POST)
    public void queryTxnReceiptInner(HttpRequest request, HttpResponse response) {
        Map<String, Object> reqMap = request.getBodyObject(Map.class);
        String companyId = reqMap.get("companyId").toString();
        String bankAccountNo = reqMap.get("bankAccountNo").toString();
        String bankName = reqMap.get("bankName").toString();
        String startTime = reqMap.get("startTime").toString();
        String endTime = reqMap.get("endTime").toString();
        uAcctGeneralService.queryTxnReceiptInner(companyId, bankAccountNo, bankName, startTime, endTime);
        ResponseResultUtils.success(response);
    }

}
