package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.fenbeitong.fenbeipay.vouchers.dto.VouchersUpdateDTO;
import com.fenbeitong.fenbeipay.vouchers.service.VoucherHandleService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskDetailsService;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: java类作用描述
 * @ClassName: innerVouchersController
 * @Author: zhangga
 * @CreateDate: 2019/1/22 12:25 PM
 * @UpdateUser:
 * @UpdateDate: 2019/1/22 12:25 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/internal/vouchers")
public class voucherDataCleanController {

    @Autowired
    private VouchersTaskDetailsService vouchersTaskDetailsService;
    @Autowired
    private VoucherHandleService voucherHandleService;

    /**
     * createVouchersType
     *
     * @return void
     * @Description 创建分贝券分类
     * @Date 下午4:56 2018/12/26
     * @Param [request, response]
     **/
//    @HttpService(value = "/clean/voucher/v1", method = RequestMethod.POST)
//    public void createVouchersType(HttpRequest request, HttpResponse response) {
//        VouchersTypeDTO vouchersTypeDTO = request.getBodyObject(VouchersTypeDTO.class);
////        vouchersTypeService.createVouchersType(vouchersTypeDTO);
//        ResponseResultUtils.success(response);
//    }


    /**
     * 更新部门信息
     * @param request
     * @param response
     */
    @HttpService(value = "/updateVoucherDepartment", method = RequestMethod.POST)
    public void updateVoucherDepartment(HttpRequest request, HttpResponse response) {
        VouchersUpdateDTO vouchersUpdateDTO = request.getBodyObject(VouchersUpdateDTO.class);
        FinhubLogger.info("【更新部门信息】请求参数：{}", vouchersUpdateDTO);
        if (CollectionUtils.isEmpty(vouchersUpdateDTO.getVoucherTaskIds())) {
            throw new ValidateException("任务ID对象不能为空");
        }
        ResponseResultUtils.success(response, voucherHandleService.updateVoucherDepartment(vouchersUpdateDTO));
    }



    /**
     * 更新部门信息
     * @param request
     * @param response
     */
    @HttpService(value = "/updateVoucherDepartmentById", method = RequestMethod.POST)
    public void updateVoucherDepartmentByDetailId(HttpRequest request, HttpResponse response) {
        VouchersUpdateDTO vouchersUpdateDTO = request.getBodyObject(VouchersUpdateDTO.class);
        FinhubLogger.info("【更新部门信息】请求参数：{}", vouchersUpdateDTO);
        if (CollectionUtils.isEmpty(vouchersUpdateDTO.getVoucherTaskDetailIds())) {
            throw new ValidateException("任务明细ID对象不能为空");
        }
        ResponseResultUtils.success(response, vouchersTaskDetailsService.updateVoucherDepartmentByDetailId(vouchersUpdateDTO));
    }

    /**
     * 更新分贝券员工手机号
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/updateEmployeePhone", method = RequestMethod.POST)
    public void updateVoucherPhone(HttpRequest request, HttpResponse response) {
        VouchersUpdateDTO vouchersUpdateDTO = request.getBodyObject(VouchersUpdateDTO.class);
        FinhubLogger.info("【更新分贝券员工手机号】请求参数：{}", vouchersUpdateDTO);
        if (ObjUtils.isNull(vouchersUpdateDTO)) {
            throw new ValidateException("参数对象不能为空");
        }
        ResponseResultUtils.success(response, voucherHandleService.updateEmployeePhone(vouchersUpdateDTO));
    }
}
