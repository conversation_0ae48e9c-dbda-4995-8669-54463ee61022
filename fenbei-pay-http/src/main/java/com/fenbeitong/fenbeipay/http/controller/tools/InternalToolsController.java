package com.fenbeitong.fenbeipay.http.controller.tools;

import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountModelType;
import com.fenbeitong.fenbeipay.api.model.dto.tools.AcctHandlerToolDTO;
import com.fenbeitong.fenbeipay.api.model.dto.tools.CashierHandlerToolDTO;
import com.fenbeitong.fenbeipay.api.model.dto.tools.CorrectDuplicatedRefundReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.tools.OperationInfo;
import com.fenbeitong.fenbeipay.api.service.personaccount.IPersonAccountService;
import com.fenbeitong.fenbeipay.cashier.tools.CashierToolsService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.personpay.PayChannel;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.rpc.service.extract.IAcctExtractDayServiceImpl;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Description: java类作用描述
 * @ClassName: internalToolsController
 * @Author: zhangga
 * @CreateDate: 2021/4/22 7:45 下午
 * @UpdateUser:
 * @UpdateDate: 2021/4/22 7:45 下午
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Slf4j
@HttpService("/internal/tools")
public class InternalToolsController {


    @Autowired
    private CashierToolsService cashierToolsService;

    @Autowired
    IAcctExtractDayServiceImpl iAcctExtractDayServiceImpl;

    @Autowired
    IPersonAccountService personAccountService;


    /**
     * @Description: 根据订单增加账户金额
     * 只有正向单，只需增加收银台支付金额、扣除商务账户余额
     * @methodName: reduceAccountAmount
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2021/4/23 10:31 上午
     **/
    @HttpService(value = "/operation/business/account/amount", method = RequestMethod.POST)
    public void operationAccountAmountByCashier(HttpRequest request, HttpResponse response) {
        AcctHandlerToolDTO handlerToolDTO = request.getBodyObject(AcctHandlerToolDTO.class);
        handlerToolDTO.setPayChannel(PayChannel.PAY_COM_APP.name());
        //参数校验
        cashierToolsService.operationCashierAndAccountByCashier(handlerToolDTO);
        ResponseResultUtils.success(response, handlerToolDTO);
    }

    /**
     * @Description: 根据订单增加账户金额
     * 只有逆向单，只需增加收银台支付金额、扣除商务账户余额
     * @methodName: addAccountAmount
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2021/4/23 10:32 上午
     **/
    @HttpService(value = "/operation/business/account/amount/refund", method = RequestMethod.POST)
    public void operationAccountAmountByCashierRefund(HttpRequest request, HttpResponse response) {
        AcctHandlerToolDTO handlerToolDTO = request.getBodyObject(AcctHandlerToolDTO.class);
        handlerToolDTO.setPayChannel(PayChannel.PAY_COM_APP.name());
        cashierToolsService.operationCashierAndAccountByCashierRefund(handlerToolDTO);
        ResponseResultUtils.success(response, handlerToolDTO);
    }

    @HttpService(value = "/operation/redcoupon/account/amount", method = RequestMethod.POST)
    public void operationRedcouponAccountAmountByCashier(HttpRequest request, HttpResponse response) {
        AcctHandlerToolDTO handlerToolDTO = request.getBodyObject(AcctHandlerToolDTO.class);
        handlerToolDTO.setPayChannel(PayChannel.PAY_RCP_APP.name());
        cashierToolsService.operationCashierAndAccountByCashier(handlerToolDTO);
        ResponseResultUtils.success(response, handlerToolDTO);
    }

    @HttpService(value = "/operation/redcoupon/account/amount/refund", method = RequestMethod.POST)
    public void operationRedcouponAccountAmountByCashierRefund(HttpRequest request, HttpResponse response) {
        AcctHandlerToolDTO handlerToolDTO = request.getBodyObject(AcctHandlerToolDTO.class);
        handlerToolDTO.setPayChannel(PayChannel.PAY_RCP_APP.name());
        cashierToolsService.operationCashierAndAccountByCashierRefund(handlerToolDTO);
        ResponseResultUtils.success(response, handlerToolDTO);
    }

    @HttpService(value = "/operation/cashier/amount", method = RequestMethod.POST)
    public void operationCashier(HttpRequest request, HttpResponse response) {
        CashierHandlerToolDTO handlerToolDTO = request.getBodyObject(CashierHandlerToolDTO.class);
        List<String> payChannels = handlerToolDTO.getPayChannels();
        if (ObjUtils.isEmpty(payChannels)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        cashierToolsService.operationCashier(handlerToolDTO);
        ResponseResultUtils.success(response, handlerToolDTO);
    }

    @HttpService(value = "/operation/cashier/amount/refund", method = RequestMethod.POST)
    public void operationCashierRefund(HttpRequest request, HttpResponse response) {
        CashierHandlerToolDTO handlerToolDTO = request.getBodyObject(CashierHandlerToolDTO.class);
        cashierToolsService.operationCashierRefund(handlerToolDTO);
        ResponseResultUtils.success(response, handlerToolDTO);
    }

    /**
     * 修正账户字段
     * @param request
     * @param response
     */
    @Deprecated
    @HttpService(value = "/operation/cashier/account/sub/id", method = RequestMethod.POST)
    public void operationCashierAccountInfo(HttpRequest request, HttpResponse response) {
        JSONObject jsonObject = JSON.parseObject(request.getBody());
        String companyId = jsonObject.getString("companyId");
        String employeeId = jsonObject.getString("employeeId");
        String fbOrderId = jsonObject.getString("fbOrderId");
        int updateCount = cashierToolsService.operationCashierAccountInfo(companyId,employeeId,fbOrderId);
        JSONObject result = new JSONObject();
        result.put("updateCount",updateCount);
        ResponseResultUtils.success(response, result);
    }

    /**
     * 手工接口，根据id删除一条日切记录
     * QX 2022-04-11 在uat测试
     * @param request
     * @param response
     */
    @HttpService(value = "/delete/{id}", method = RequestMethod.GET)
    public void deleteById(HttpRequest request, HttpResponse response) {
        String id = request.getPathValue("id");
        int count = 0;
        if(StringUtil.isNotBlank(id)){
            Long aLong = Long.valueOf(id);
            count = iAcctExtractDayServiceImpl.deleteById(aLong);
        }
        ResponseResultUtils.success(response, count);
    }

    /**
     * 手工接口，根据id删除白名单
     * QX 2022-04-18 在uat测试
     * @param request
     * @param response
     */
    @HttpService(value = "/manualRecord/delete/{id}", method = RequestMethod.GET)
    public void deleteManualRecord(HttpRequest request, HttpResponse response) {
        String id = request.getPathValue("id");
        int count = 0;
        if(StringUtil.isNotBlank(id)){
            Long aLong = Long.valueOf(id);
            count = iAcctExtractDayServiceImpl.deleteManualRecord(aLong);
        }
        ResponseResultUtils.success(response, count);
    }

    @HttpService(value = "/operation/cashier/correct-duplicated-refund", method = RequestMethod.POST)
    public void correctDuplicatedRefund(HttpRequest request, HttpResponse response) {
        try {
            CorrectDuplicatedRefundReqDTO req = request.getBodyObject(CorrectDuplicatedRefundReqDTO.class);
            FundAccountSubType accountSubType = FundAccountSubType.getEnum(req.getAccountSubType());
            AccountModelType accountModelType = AccountModelType.getEnum(req.getAccountModelType());
            OperationInfo operationInfo = new OperationInfo();
            operationInfo.setOperatorId(req.getOperatorId());
            operationInfo.setOperatorName(req.getOperatorName());
            operationInfo.setOperationDesc(req.getOperationDesc());

            cashierToolsService.correctDuplicatedRefund(req.getFlowId(), accountSubType, accountModelType, operationInfo);
            ResponseResultUtils.success(response, req);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            ResponseResultUtils.success(response, e);
        }
    }
}
