package com.fenbeitong.fenbeipay.http.controller.transfer.req;

import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description:
 * @Author: guogx
 * @Date: 2022/11/19 下午3:46
 */
@Data
public class BalanceAdjustReq implements Serializable {

    /**
     * 账户ID、参数提供以确保准确性
     */
    private String accountId;

    /***
     * 被调整企业的id
     */
    private String companyId;

    /**
     * 平台方
     */
    private String bankName;

    /***
     * 调整金额，单位分
     */
    private BigDecimal adjustAmount;

    /** 操作人id **/
    private String operateId;

    /** 操作人名称 **/
    private String operateName;

    /** 备注 **/
    private String remark;

    public void check(){
        if(StringUtils.isBlank(companyId)){
            throw new FinhubException(1, "被调整企业的id不能为空companyId");
        }
        if(adjustAmount == null || adjustAmount.compareTo(BigDecimal.ZERO)<=0){
            throw new FinhubException(1, "调整金额必须为正数，单位为分adjustAmount");
        }
        if (StringUtils.isBlank(bankName) || BankNameEnum.UN_KNOW.equals(BankNameEnum.getBankEnum(bankName))) {
            throw new FinhubException(1, "被调整企业平台方不能为空bankName");
        }
        if(StringUtils.isBlank(operateId)){
            throw new FinhubException(1, "操作人id不能为空operateId");
        }
        if(StringUtils.isBlank(operateName)){
            throw new FinhubException(1, "操作人名称不能为空operateName");
        }
    }


}
