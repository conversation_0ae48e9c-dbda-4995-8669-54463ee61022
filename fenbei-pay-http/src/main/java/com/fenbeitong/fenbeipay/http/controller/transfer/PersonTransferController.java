package com.fenbeitong.fenbeipay.http.controller.transfer;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VoucherTransferLastDelRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VoucherTransferLastRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.sas.IPayPwdService;
import com.fenbeitong.fenbeipay.cashier.transfer.PersonTransferService;
import com.fenbeitong.fenbeipay.cashier.transfer.VoucherTransferLastService;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.constant.core.UcMessageCode;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.dto.VoucherBatchTransferDTO;
import com.fenbeitong.fenbeipay.core.model.dto.VoucherTransferDTO;
import com.fenbeitong.fenbeipay.core.model.vo.transfer.EmployeeVO;
import com.fenbeitong.fenbeipay.core.model.vo.transfer.VoucherTransferVO;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.vo.VoucherForWalletVO;
import com.fenbeitong.finhub.common.entity.ResponsePageDTO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 分贝券个人钱包controller
 * @ClassName: VouchersForAppController
 * @Author: zhangga
 * @CreateDate: 2018/12/11 下午6:49
 * @UpdateUser:
 * @UpdateDate: 2018/12/11 下午6:49
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService
public class PersonTransferController {

    @Autowired
    private PersonTransferService personTransferService;

    @Autowired
    private VoucherTransferLastService voucherPersonTransferLastService;


    @Autowired
    private IPayPwdService iPayPwdService;


    private static final String USER_ID = "userId";
    private static final String USER_PHONE = "userPhone";
    private static final String USER_COMPANY_NAME = "user_company_name";
    private static final String COMPANY_ID = "companyId";
    private static final int PHONE_SIZE = 11;

    /**
     * getEmployeeByPhone
     *
     * @return void
     * @Description 根据手机号查询被赠送人信息
     * @Date 4:49 PM 2019/1/15
     * @Param [request, response]
     **/
    @HttpService(value = "/vouchers/transfer/person/{toEmployeePhone}/v2", method = RequestMethod.GET)
    public void getEmployeeByPhone(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String currentUserPhone = request.getAttribute(USER_PHONE).toString();
        String currentUserCompanyId = request.getAttribute(COMPANY_ID).toString();
        String currentUserId = request.getAttribute(USER_ID).toString();
        String toEmployeePhone = request.getPathValue("toEmployeePhone");
        checkPhone(toEmployeePhone, currentUserPhone);
        EmployeeVO employeeInfo = personTransferService.getEmployeeInfo(currentUserCompanyId, toEmployeePhone);
        if (currentUserId.equals(employeeInfo.getEmployeeId())) {
            throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_PHONE_CAN_NOT_SELF);
        }
        ResponseResultUtils.success(response, employeeInfo);
    }

    /**
     * getTransferVouchers
     *
     * @return void
     * @Description 获取可用转账分贝券列表
     * @Date 5:52 PM 2019/1/15
     * @Param [request, response]
     **/
    @HttpService(value = "/vouchers/transfer/person/vouchers/v2", method = RequestMethod.POST)
    public void getTransferVouchers(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String currentUserId = request.getAttribute(USER_ID).toString();
        String currentUserCompanyId = request.getAttribute(COMPANY_ID).toString();
        PageBean pageDTO = request.getBodyObject(PageBean.class);
        ResponsePage<VoucherForWalletVO> responsePage = personTransferService.getTransferVouchers(currentUserId, currentUserCompanyId, pageDTO);
        ResponseResultUtils.success(response, responsePage);
    }

    /**
     * transferVoucher
     *
     * @return void
     * @Description 确认转账
     * @Date 11:38 AM 2019/1/15
     * @Param [request, response]
     **/
    @HttpService(value = "/vouchers/transfer/person/v2", method = RequestMethod.POST)
    public void transferVoucher(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String currentUserId = request.getAttribute(USER_ID).toString();
        String currentUserName = request.getAttribute(USER_COMPANY_NAME).toString();
        String currentUserCompanyId = request.getAttribute(COMPANY_ID).toString();
        String currentUserPhone = request.getAttribute(USER_PHONE).toString();
        VoucherTransferDTO transferDTO = request.getBodyObject(VoucherTransferDTO.class);
        BigDecimal transferAmount = transferDTO.getTransferAmount();
        if (transferAmount == null || transferAmount.compareTo(BigDecimal.ONE) < 0) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        //验证支付密码
        iPayPwdService.verifyPayPwd(request.getHeader("client_version"), currentUserCompanyId,
                currentUserId, transferDTO.getEmployeePwd(), transferDTO.getEncryptType());
        //正常转劵业务
        transferDTO.setFromEmployeeId(currentUserId);
        transferDTO.setFromEmployeeName(currentUserName);
        transferDTO.setFromCompanyId(currentUserCompanyId);
        transferDTO.setToCompanyId(currentUserCompanyId);
        FinhubLogger.debug("【个人转账参数】：{}", JSONObject.toJSONString(transferDTO));
        checkPhone(transferDTO.getToEmployeePhone(), currentUserPhone);
        personTransferService.transferVoucher(transferDTO);
        ResponseResultUtils.success(response);
    }

    /**
     * transferVoucherFlow
     *
     * @return void
     * @Description 查看个人分贝券转账记录
     * @Date 3:28 PM 2019/1/15
     * @Param [request, response]
     **/
    @HttpService(value = "/vouchers/transfer/person/flow/v2", method = RequestMethod.POST)
    public void transferVoucherFlow(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String currentUserId = request.getAttribute(USER_ID).toString();
        String currentUserCompanyId = request.getAttribute(COMPANY_ID).toString();
        List<VoucherTransferVO> list = personTransferService.getTransferVoucherFlow(currentUserCompanyId, currentUserId);
        ResponseResultUtils.success(response, list);
    }


    /**
     * @Description: 批量转让分贝券
     * @methodName: batchTransferVoucher
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2021/9/22 11:29 上午
     **/
    @HttpService(value = "/vouchers/transfer/batch/v3", method = RequestMethod.POST)
    public void batchTransferVoucher(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String currentUserId = request.getAttribute(USER_ID).toString();
        String currentUserCompanyId = request.getAttribute(COMPANY_ID).toString();
        //20211011
        VoucherBatchTransferDTO batchTransferDTO = request.getBodyObject(VoucherBatchTransferDTO.class);
        batchTransferDTO.setFromEmployeeId(currentUserId);
        batchTransferDTO.setFromEmployeeName(request.getAttribute(USER_COMPANY_NAME).toString());
        batchTransferDTO.setFromEmployeePhone(request.getAttribute(USER_PHONE).toString());
        batchTransferDTO.setFromCompanyId(currentUserCompanyId);
        batchTransferDTO.setToCompanyId(currentUserCompanyId);
        //参数校验
        checkBatchTransferParam(batchTransferDTO);
        FinhubLogger.debug("【个人转账参数】：{}", JSONObject.toJSONString(batchTransferDTO));
        //验证支付密码
        iPayPwdService.verifyPayPwd(request.getHeader("client_version"), currentUserCompanyId,
                currentUserId, batchTransferDTO.getEmployeePwd(), batchTransferDTO.getEncryptType());
        //正常转劵业务
        Integer batch = batchTransferDTO.getBatch();
        if (batch == 1) {
            VoucherTransferDTO transferDTO = new VoucherTransferDTO();
            BeanUtils.copyProperties(batchTransferDTO, transferDTO);
            transferDTO.setVoucherId(batchTransferDTO.getVoucherIdList().get(0));
            checkPhone(transferDTO.getToEmployeePhone(), request.getAttribute(USER_PHONE).toString());
            personTransferService.transferVoucher(transferDTO);
        } else {
            personTransferService.batchTransferVoucher(batchTransferDTO);
        }
        ResponseResultUtils.success(response);
    }


    /**
     * 查询企业转让券配置
     * @param request
     * @param response
     */
    @HttpService(value = "/vouchers/transfer/query/background", method = RequestMethod.POST)
    public void queryBackground(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String currentUserCompanyId = request.getAttribute(COMPANY_ID).toString();
        ResponseResultUtils.success(response, personTransferService.getTransferBackgroundConfig(currentUserCompanyId));
    }

    /**
     * 查询最近转让人
     *
     * @since v4.9.6
     **/
    @HttpService(value = "/vouchers/recent/transfer/employee/v3", method = RequestMethod.GET)
    public void recentTransferRecords(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String currentUserId = request.getAttribute(USER_ID).toString();
        String currentUserCompanyId = request.getAttribute(COMPANY_ID).toString();
        List<VoucherTransferLastRespRPCDTO> transferVoucherLast = voucherPersonTransferLastService.findTransferVoucherLast(currentUserCompanyId, currentUserId);
        ResponsePageDTO<VoucherTransferLastRespRPCDTO> responsePageDTO = new ResponsePageDTO();
        responsePageDTO.setTotalCount(transferVoucherLast.size());
        responsePageDTO.setDataList(transferVoucherLast);
        ResponseResultUtils.success(response, responsePageDTO);
    }

    /**
     * 删除最近转让人
     *
     * @since v4.9.6
     **/
    @HttpService(value = "/vouchers/recent/transfer/employee/delete/v3", method = RequestMethod.POST)
    public void deleteRecentTransferRecord(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String currentUserId = request.getAttribute(USER_ID).toString();
        String currentUserCompanyId = request.getAttribute(COMPANY_ID).toString();
        VoucherTransferLastDelRPCDTO delRPCDTO = request.getBodyObject(VoucherTransferLastDelRPCDTO.class);
        int count = voucherPersonTransferLastService.updateDeleteStatusByToEIds(currentUserCompanyId, currentUserId, delRPCDTO.getToEmployeeIds());
        ResponseResultUtils.success(response);
    }


//=========================================================private method=============================================================

    /**
     * checkPrivilege
     *
     * @return void
     * @Description 权限校验
     * @Date 11:38 AM 2019/1/15
     * @Param [request]
     **/
    private void checkPrivilege(HttpRequest request) {
        Object currentUserId = request.getAttribute(USER_ID);
        Object currentUserPhone = request.getAttribute(USER_PHONE);
        Object companyId = request.getAttribute(COMPANY_ID);
        Object currentUserName = request.getAttribute(USER_COMPANY_NAME);
        if (ObjUtils.isEmpty(currentUserId) || ObjUtils.isEmpty(currentUserPhone) || ObjUtils.isEmpty(companyId) || ObjUtils.isEmpty(currentUserName)) {
            throw new FinhubException(UcMessageCode.NO_AUTH);
        }
    }

    /**
     * checkPhone
     *
     * @return void
     * @Description 检查手机号
     * @Date 8:17 PM 2019/1/14
     * @Param [phone, currentUserPhone]
     **/
    private void checkPhone(String phone, String currentUserPhone) {
//        String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$";
        if (phone.length() != PHONE_SIZE) {
            throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_PHONE_ERROR);
        }
//        Pattern p = Pattern.compile(regex);
//        Matcher m = p.matcher(phone);
//        boolean isMatch = m.matches();
//        if (!isMatch) {
//            throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_PHONE_ERROR);
//        }
        if (currentUserPhone.equals(phone)) {
            throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_PHONE_CAN_NOT_SELF);
        }
    }

    private void checkBatchTransferParam(VoucherBatchTransferDTO batchTransferDTO) {
        String toEmployeeId = batchTransferDTO.getToEmployeeId();
        if (StringUtils.isBlank(toEmployeeId)) {
            throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_CAN_NOT_CHECK_USER);
        }
        String toEmployeePhone = batchTransferDTO.getToEmployeePhone();
        if (StringUtils.isBlank(toEmployeePhone)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (toEmployeeId.equalsIgnoreCase(batchTransferDTO.getFromEmployeeId()) || toEmployeePhone.equalsIgnoreCase(batchTransferDTO.getFromEmployeePhone())) {
            throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_PHONE_CAN_NOT_SELF);
        }
        List<String> voucherIdList = batchTransferDTO.getVoucherIdList();
        if (CollectionUtils.isEmpty(voucherIdList)) {
            throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_CAN_NOT_CHECK_VOUCHER);
        }
        if (voucherIdList.size() > 20) {
            throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_CAN_NOT_MORE_THAN_20);
        }
        Integer batch = batchTransferDTO.getBatch();
        if (batch == null) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "batch 不能为空");
        }
        BigDecimal transferAmount = batchTransferDTO.getTransferAmount();
        if (batch == 1) {
            if (transferAmount == null || transferAmount.compareTo(BigDecimal.ONE) < 0) {
                throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_NO_TRANSFER_AMOUNT);
            }
            if (voucherIdList.size() != 1) {
                throw new FinPayException(GlobalResponseCode.PERSON_TRANSFER_CAN_NOT_CHECK_MORE);
            }
        }
    }

}
