package com.fenbeitong.fenbeipay.http.controller.sas;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.bank.company.service.costAtt.CostAttributionService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderCostAttributionService;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CostAttributionListRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CostAttributionReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryCostAttributionReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryCostAttributionResqVo;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> WEB 端
 * @date 2022/1/11 3:15 下午
 * @Description 备用金管理查询(公司的费用归属列表)
 */
@HttpService("/sas/pay")
public class WebPettyCostAttributionController extends BaseController {

    @Autowired
    private CostAttributionService costAttributionService;

    @HttpService(value = "/costAttribution/list",method = RequestMethod.POST)
    public void costAttributionList(HttpRequest request, HttpResponse response){
        String companyId=getUserCompanyId(request);
        QueryCostAttributionReqVo reqVo= request.getBodyObject(QueryCostAttributionReqVo.class);
        reqVo.setCompanyId(companyId);
        ResponsePage<QueryCostAttributionResqVo> data =  costAttributionService.getCostAttList(reqVo);
        ResponseResultUtils.success(response, data);
    }
}
