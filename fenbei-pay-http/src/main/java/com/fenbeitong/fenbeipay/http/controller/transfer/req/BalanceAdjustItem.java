package com.fenbeitong.fenbeipay.http.controller.transfer.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName BalanceAdjustItem
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/12/12 4:08 下午
 * @Version 1.0
 **/
@Data
public class BalanceAdjustItem implements Serializable {

    /**
     * 账户ID、参数提供以确保准确性
     */
    private String accountId;
    /***
     * 被调整企业的id
     */
    private String companyId;

    /***
     * 调整金额，单位分
     */
    private BigDecimal adjustAmount;

}
