package com.fenbeitong.fenbeipay.http.controller.acct;

import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson.JSON;
import com.fenbeitong.activity.api.util.GlobalCoreResponseCode;
import com.fenbeitong.fenbeipay.acctdech.manager.IBankCardTrapFlowManager;
import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctFlowSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.spa.SpaManualReceiptQueryReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AcctRedcouponFlowReqDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponFlowVO;
import com.fenbeitong.fenbeipay.api.service.acct.flow.IAcctFlowService;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctCompanySwitchService;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctReimbursementMgrService;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctAppPayService;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctGuaranteeService;
import com.fenbeitong.fenbeipay.api.util.BeanCopierUtils;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.BankConfigUtil;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.bank.BankCardTrapFlow;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.extract.service.UBankAcctExtractService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaBankConsumeTaskMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaBankRefundTaskMsg;
import com.google.common.collect.Lists;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.CASHIER_CRONTAB_HEAD;

/**
 * <AUTHOR>
 * @Date 2021/1/11
 * @Description 新账户体系接口
 */
@HttpService("/internal/acct/company")
public class AcctInnerController extends AcctAbstractController {

    @Autowired
    private IAcctCompanySwitchService iAcctCompanySwitchService;

    @Autowired
    private UAcctBusinessCreditFlowService uAcctBusinessCreditFlowService;

    @Autowired
    private UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    private UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    private UAcctBusinessDebitFlowService uAcctBusinessDebitFlowService;
    @Autowired
    private IAcctGuaranteeService iAcctGuaranteeService;

    @Autowired
    private UBankAcctService uBankAcctService;

    @Autowired
    private UBankAcctFlowService uBankAcctFlowService;

    @Autowired
    private IAcctAppPayService iAcctAppPayService;

    @Autowired
    private IAcctMgrService iAcctMgrService;

    @Autowired
    private IAcctFlowService iAcctFlowService;
    
    @Autowired
    private ThreadPoolTaskExecutor asyncExecutor;

    @Autowired
    private DingDingMsgService dingDingMsgService;
    @Autowired
    IAcctReimbursementMgrService iAcctReimbursementMgrService;
    @Autowired
    private UBankAcctExtractService uBankAcctExtractService;
    @Autowired
    private IBankCardTrapFlowManager iBankCardTrapFlowManager;

    /**
     * 添加切换的企业账户
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/switch/create", method = RequestMethod.POST)
    public void addAcctCompanySwitch(HttpRequest request, HttpResponse response) {
        AcctCompanySwitchAddReqDTO reqDTO = request.getBodyObject(AcctCompanySwitchAddReqDTO.class);
        iAcctCompanySwitchService.createAcctCompanySwitch(reqDTO);
        ResponseResultUtils.success(response, null);
    }

    /**
     * 添加切换的企业账户
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/switch/del/{companyId}", method = RequestMethod.POST)
    public void delAcctCompanySwitch(HttpRequest request, HttpResponse response) {
        String companyId = request.getPathValue("companyId");
        iAcctCompanySwitchService.delAcctCompanySwitch(companyId);
        ResponseResultUtils.success(response, null);
    }


    /**
     * 新账户体系 个账户信息总揽 如果商务账户授信 其他账户都是充值
     *
     * @param request
     * @param response
     */
    @Deprecated
    @HttpService(value = "/general/{companyId}", method = RequestMethod.GET)
    public void companyOverview(HttpRequest request, HttpResponse response) {
    }

    /**
     * 导出账户流水
     * 一般户流水(充值/授信)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/general/flow", method = RequestMethod.POST)
    public void baseAccountFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        if(StringUtils.isBlank(reqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctGeneralFlowRespDTO> page = accountGeneralFlowService.queryAccountGeneralFlow(reqDTO);
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        List<AcctGeneralFlowRespDTO> dataList = page.getDataList();
        List<AcctGeneralFlowRespDTO> respAccountGeneralFlow = dataList.stream().map(flowRespDTO -> {
            AcctGeneralFlowRespDTO respDTO = new AcctGeneralFlowRespDTO();
            BeanUtils.copyProperties(flowRespDTO, respDTO);
            respDTO.setBalance(BigDecimalUtils.fen2yuan(flowRespDTO.getBalance()));
            respDTO.setOperationAmount(BigDecimalUtils.fen2yuan(flowRespDTO.getOperationAmount()));
            return respDTO;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, respAccountGeneralFlow);
    }

    /**
     * 商务账户流水(商务)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/business/debit/flow", method = RequestMethod.POST)
    public void rechargeFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        if(StringUtils.isBlank(reqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctFlowRespDTO> flowRespDTOResponsePage;
        if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            flowRespDTOResponsePage = uAcctBusinessDebitFlowService.queryAccountDebitFlow(reqDTO);
        } else {
            flowRespDTOResponsePage = uAcctBusinessCreditFlowService.queryAccountCreditFlow(reqDTO);
        }
        if (Objects.isNull(flowRespDTOResponsePage)||CollectionUtils.isEmpty(flowRespDTOResponsePage.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        // 增加对手账户名
        List<BankAcct> bankAccts = bankAcctService.queryAll();
        Map<String, String> bankAcctMap = bankAccts.stream().collect(Collectors.toMap(BankAcct::getBankAccountNo, BankAcct::getCompanyMainName, (v1, v2) -> v1));
        List<AcctFlowRespDTO> dataList = flowRespDTOResponsePage.getDataList();
        List<AcctFlowRespDTO> resqBusinessDebitFlow = dataList.stream().map(acctFlowRespDTO -> {
            AcctFlowRespDTO acctFlowRespResqDTO = new AcctFlowRespDTO();
            BeanUtils.copyProperties(acctFlowRespDTO, acctFlowRespResqDTO);
            acctFlowRespResqDTO.setBalance(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getBalance()));
            acctFlowRespResqDTO.setOperationAmount(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getOperationAmount()));
            acctFlowRespResqDTO.setTargetAccountName(bankAcctMap.get(acctFlowRespDTO.getTargetAccount()));
            acctFlowRespResqDTO.setTargetBankAllName(BankNameEnum.getBankEnum(acctFlowRespDTO.getTargetBankName()).getName());
            return acctFlowRespResqDTO;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, resqBusinessDebitFlow);
    }

    /**
     * 商务账户流水(商务)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/business/debit/bill/flow", method = RequestMethod.POST)
    public void businessDebitFlowByStereoBill(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        if(StringUtils.isBlank(reqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctFlowRespDTO> flowRespDTOResponsePage;
        if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            flowRespDTOResponsePage = uAcctBusinessDebitFlowService.queryAccountDebitFlowByBillNo(reqDTO);
        } else {
            flowRespDTOResponsePage = uAcctBusinessCreditFlowService.queryAccountCreditFlowByBillNo(reqDTO);
        }
        if (Objects.isNull(flowRespDTOResponsePage)||CollectionUtils.isEmpty(flowRespDTOResponsePage.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        // 增加对手账户名
        List<BankAcct> bankAccts = bankAcctService.queryAll();
        Map<String, String> bankAcctMap = bankAccts.stream().collect(Collectors.toMap(BankAcct::getBankAccountNo, BankAcct::getCompanyMainName, (v1, v2) -> v1));
        List<AcctFlowRespDTO> dataList = flowRespDTOResponsePage.getDataList();
        List<AcctFlowRespDTO> respBusinessDebitFlow = dataList.stream().map(acctFlowRespDTO -> {
            AcctFlowRespDTO acctFlowRespRespDTO = new AcctFlowRespDTO();
            BeanUtils.copyProperties(acctFlowRespDTO, acctFlowRespRespDTO);
            acctFlowRespRespDTO.setBalance(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getBalance()));
            acctFlowRespRespDTO.setOperationAmount(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getOperationAmount()));
            acctFlowRespRespDTO.setTargetAccountName(bankAcctMap.get(acctFlowRespDTO.getTargetAccount()));
            acctFlowRespRespDTO.setTargetBankAllName(BankNameEnum.getBankEnum(acctFlowRespDTO.getTargetBankName()).getName());
            return acctFlowRespRespDTO;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, respBusinessDebitFlow);
    }

    /**
     * 个人账户流水(个人)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/individual/debit/flow", method = RequestMethod.POST)
    public void creditFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        if(StringUtils.isBlank(reqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctFlowRespDTO> flowRespDTOResponsePage;
        if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            flowRespDTOResponsePage = uAcctIndividualDebitFlowService.queryAccountDebitFlow(reqDTO);
        } else {
            flowRespDTOResponsePage = uAcctIndividualCreditFlowService.queryAccountCreditFlow(reqDTO);
        }
        if (Objects.isNull(flowRespDTOResponsePage)||CollectionUtils.isEmpty(flowRespDTOResponsePage.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        // 增加对手账户名
        List<BankAcct> bankAccts = bankAcctService.queryAll();
        Map<String, String> bankAcctMap = bankAccts.stream().collect(Collectors.toMap(BankAcct::getBankAccountNo, BankAcct::getCompanyMainName, (v1, v2) -> v1));
        List<AcctFlowRespDTO> dataList = flowRespDTOResponsePage.getDataList();
        List<AcctFlowRespDTO> resqAccountGeneralFlow = dataList.stream().map(acctFlowRespDTO -> {
            AcctFlowRespDTO acctFlowResqDTO = new AcctFlowRespDTO();
            BeanUtils.copyProperties(acctFlowRespDTO, acctFlowResqDTO);
            acctFlowResqDTO.setBalance(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getBalance()));
            acctFlowResqDTO.setOperationAmount(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getOperationAmount()));
            acctFlowResqDTO.setTargetAccountName(bankAcctMap.get(acctFlowRespDTO.getTargetAccount()));
            acctFlowResqDTO.setTargetBankAllName(BankNameEnum.getBankEnum(acctFlowRespDTO.getTargetBankName()).getName());
            return acctFlowResqDTO;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, resqAccountGeneralFlow);
    }

    /**
     * 企业虚拟卡流水
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/card/flow", method = RequestMethod.POST)
    public void companyCardFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        if(StringUtils.isBlank(reqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctFlowRespDTO> companyCardPage = uAcctCompanyCardFlowService.queryAccountCompanyCardFlow(reqDTO);
        if (Objects.isNull(companyCardPage)||CollectionUtils.isEmpty(companyCardPage.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        List<AcctFlowRespDTO> dataList = companyCardPage.getDataList();
        List<AcctFlowRespDTO> resqCompanyCardPageFlow = dataList.stream().map(acctFlowRespDTO -> {
            AcctFlowRespDTO acctFlowResqDTO = new AcctFlowRespDTO();
            BeanUtils.copyProperties(acctFlowRespDTO, acctFlowResqDTO);
            acctFlowResqDTO.setBalance(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getBalance()));
            acctFlowResqDTO.setOperationAmount(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getOperationAmount()));
            return acctFlowResqDTO;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, resqCompanyCardPageFlow);
    }

    /**
     * 海外卡流水导出
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/oversea/acct/flow", method = RequestMethod.POST)
    public void overseaAcctFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        if(StringUtils.isBlank(reqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date minDateOfQuery = DateUtil.convertDate("2024-07-21 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctFlowRespDTO> overseaAcctFlowPage = acctOverseaService.queryAccountFlow(reqDTO);
        if (Objects.isNull(overseaAcctFlowPage) || CollectionUtils.isEmpty(overseaAcctFlowPage.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        
        List<AcctFlowRespDTO> dataList = overseaAcctFlowPage.getDataList();
        List<AcctFlowRespDTO> resqCompanyCardPageFlow = dataList.stream().map(acctFlowRespDTO -> {
            AcctFlowRespDTO acctFlowResq = new AcctFlowRespDTO();
            BeanUtils.copyProperties(acctFlowRespDTO, acctFlowResq);
            acctFlowResq.setBalance(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getBalance()));
            acctFlowResq.setOperationAmount(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getOperationAmount()));
            return acctFlowResq;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, resqCompanyCardPageFlow);
    }

    /**
     * 企业对公流水
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/public/flow", method = RequestMethod.POST)
    public void acctPublicFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        if(StringUtils.isBlank(reqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctFlowRespDTO> accountCompanyCardFlow = acctPublicSearchService.queryAccountCompanyCardFlow(reqDTO);
        if (Objects.isNull(accountCompanyCardFlow)||CollectionUtils.isEmpty(accountCompanyCardFlow.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        List<AcctFlowRespDTO> dataList = accountCompanyCardFlow.getDataList();
        List<AcctFlowRespDTO> acctPublicPageFlow = dataList.stream().map(acctFlowRespDTO -> {
            AcctFlowRespDTO acctPublicResqDTO = new AcctFlowRespDTO();
            BeanUtils.copyProperties(acctFlowRespDTO, acctPublicResqDTO);
            acctPublicResqDTO.setBalance(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getBalance()));
            acctPublicResqDTO.setOperationAmount(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getOperationAmount()));
            return acctPublicResqDTO;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, acctPublicPageFlow);
    }

    /**
     * 红包券流水
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/redcoupon/flow", method = RequestMethod.POST)
    public void redcouponFlow(HttpRequest request, HttpResponse response) {
        AcctRedcouponFlowReqDTO dto = request.getBodyObject(AcctRedcouponFlowReqDTO.class);
        if(StringUtils.isBlank(dto.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (dto.getStartTime() == null
                || dto.getStartTime().before(minDateOfQuery)) {
            dto.setStartTime(minDateOfQuery);
            dto.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AccountRedcouponFlowVO> accountCompanyCardFlow = acctRedcouponSearchService.queryAcctCouponFlowList(dto);
        if (Objects.isNull(accountCompanyCardFlow)||CollectionUtils.isEmpty(accountCompanyCardFlow.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        List<AccountRedcouponFlowVO> dataList = accountCompanyCardFlow.getDataList();
        List<AccountRedcouponFlowVO> acctPublicPageFlow = dataList.stream().map(acctFlowRespDTO -> {
            AccountRedcouponFlowVO accountRedcouponFlowVO = new AccountRedcouponFlowVO();
            BeanUtils.copyProperties(acctFlowRespDTO, accountRedcouponFlowVO);
            accountRedcouponFlowVO.setOperationAmount(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getOperationAmount()));
            accountRedcouponFlowVO.setBalance(BigDecimalUtils.fen2yuan(acctFlowRespDTO.getBalance()));
            return accountRedcouponFlowVO;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, acctPublicPageFlow);
    }
    /**
     * 充值账户 余额账户转账转出
     *
     * @param request
     * @param response
     */
    @Deprecated
    @HttpService(value = "/balanceAccount/transfer/out", method = RequestMethod.POST)
    public void transferOut(HttpRequest request, HttpResponse response) {
    }

    /**
     * 新账户体系 个账户信息总揽 如果商务账户授信 其他账户都是充值
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/overview/{companyId}", method = RequestMethod.GET)
    public void companyOverview1(HttpRequest request, HttpResponse response) {
        String companyId = request.getPathValue("companyId");
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        AcctOverviewRespDTO respDTO = uAcctCommonService.queryAcctOverview(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 企业账户-账户管理
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/find/main/mgr/{companyId}", method = RequestMethod.GET)
    public void queryMainAcctMgr(HttpRequest request, HttpResponse response) {
        String companyId = request.getPathValue("companyId");
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        AcctOverviewSimpleRespDTO simpleRespDTO = uAcctCommonService.queryAcctOverviewMgr(reqDTO);
        ResponseResultUtils.success(response, simpleRespDTO);
    }


    /**
     * 时间程序，商务授信，更新退款状态，并通知场景方
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/business/credit/flow", method = RequestMethod.POST)
    public void updateBusinessCreditFlow(HttpRequest request, HttpResponse response) {
        FinhubLogger.info(CASHIER_CRONTAB_HEAD + "拉取app付商务授信上账失败结果，更新退款状态，...Start.....");
        int totalCount = uAcctBusinessCreditFlowService.queryNeedCallBankFlowCount();
        if (totalCount <= 0) {
            return;
        }
        Integer pageSize = 200;
        int pageNo = totalCount / pageSize + 1;
        for (int i = 0; i < pageNo; i++) {
            List<AcctBusinessCreditFlow> acctBusinessCreditFlows = uAcctBusinessCreditFlowService.queryNeedCallBankFlow(i * pageSize, pageSize);
            if (CollectionUtils.isNotEmpty(acctBusinessCreditFlows)) {
                Map<Integer, List<AcctBusinessCreditFlow>> collect = acctBusinessCreditFlows.stream().collect(Collectors.groupingBy(AcctBusinessCreditFlow::getOperationType));
                List<AcctBusinessCreditFlow> acctConsumeFlows = collect.get(41);
                List<AcctBusinessCreditFlow> acctRefundFlows = collect.get(42);
                if (CollectionUtils.isNotEmpty(acctConsumeFlows)) {
                    for (AcctBusinessCreditFlow e : acctConsumeFlows) {
                        try {
                            if (e.getCallbackNum() >= 10) {
                                //发钉钉
                                String msgError = "【fenbei-pay】商务授信消费资金流水上账超过10次，不再自动上账，需手动处理,资金流水id :" + e.getAccountFlowId();
                                dingDingMsgService.sendMsg(msgError);
                                continue;
                            }
                            AcctSubOperationReqRPCDTO bankTradeReqDto = new AcctSubOperationReqRPCDTO();
                            bankTradeReqDto.setBankName(e.getBankName());
                            bankTradeReqDto.setCompanyId(e.getCompanyId());
                            bankTradeReqDto.setOperationAmount(e.getOperationAmount());
                            bankTradeReqDto.setBankAcctId(e.getBankAcctId());
                            bankTradeReqDto.setOperationUserId(e.getOperationUserId());
                            bankTradeReqDto.setBizNo(e.getBizNo());
                            bankTradeReqDto.setCashierTxnId(e.getCashierTxnId());
                            bankTradeReqDto.setAccountSubType(e.getAccountSubType());
                            bankTradeReqDto.setAccountModel(e.getAccountModel());
                            uAcctBusinessCreditService.callBankConsume(bankTradeReqDto, e,e.getTargetBankAcctId());
                        }catch (Exception ex){
                            FinhubLogger.error("【4.0账户系统异常】商务授信消费调用银行转账异常,定时轮训：{}",e.getAccountFlowId());
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(acctRefundFlows)) {
                    for (AcctBusinessCreditFlow e : acctRefundFlows) {
                        try {
                            if (e.getCallbackNum() >= 10) {
                                //发钉钉
                                String msgError = "【fenbei-pay】商务授信退款资金流水上账超过10次，不再自动上账，需手动处理,资金流水id :" + e.getAccountFlowId();
                                dingDingMsgService.sendMsg(msgError);
                                continue;
                            }
                            AcctRefundReqDTO acctRefundReqDTO = new AcctRefundReqDTO();
                            acctRefundReqDTO.setCompanyId(e.getCompanyId());
                            acctRefundReqDTO.setOperationAmount(e.getOperationAmount());
                            acctRefundReqDTO.setBankName(e.getBankName());
                            acctRefundReqDTO.setOperationUserId(e.getOperationUserId());
                            acctRefundReqDTO.setBizNo(e.getBizNo());
                            acctRefundReqDTO.setReBizNo(e.getReBizNo());
                            acctRefundReqDTO.setBankAcctId(e.getBankAcctId());
                            acctRefundReqDTO.setRefundTxnId(e.getRefundTxnId());
                            acctRefundReqDTO.setAccountSubType(e.getAccountSubType());
                            acctRefundReqDTO.setAccountModel(e.getAccountModel());
                            uAcctBusinessCreditService.callBankRefund(acctRefundReqDTO, e,e.getBankAcctId());
                        }
                        catch(Exception ex){
                            FinhubLogger.error("【4.0账户系统异常】商务授信退款调用银行转账异常,定时轮训：{}",e.getAccountFlowId());
                        }
                    }
                }
            }
        }
        ResponseResultUtils.success(response);
    }


    /**
     * 时间程序，拉取三方退款结果，更新退款状态，并通知场景方
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/business/debit/flow", method = RequestMethod.POST)
    public void updateBusinessDebitFlow(HttpRequest request, HttpResponse response) {
        FinhubLogger.info(CASHIER_CRONTAB_HEAD + "拉取app付商务充值上账失败结果，更新退款状态...Start.....");
        int totalCount = uAcctBusinessDebitFlowService.queryNeedCallBankFlowCount();
        if (totalCount <= 0) {
            return;
        }
        Integer pageSize = 200;
        int pageNo = totalCount / pageSize + 1;
        for (int i = 0; i < pageNo; i++) {
            List<AcctBusinessDebitFlow> acctBusinessDebitFlows = uAcctBusinessDebitFlowService.queryNeedCallBankFlow(i * pageSize, pageSize);
            if (CollectionUtils.isNotEmpty(acctBusinessDebitFlows)) {
                Map<Integer, List<AcctBusinessDebitFlow>> collect = acctBusinessDebitFlows.stream().filter(e-> BankConfigUtil.needCallBank(e.getBankName(), e.getCompanyId())).collect(Collectors.groupingBy(AcctBusinessDebitFlow::getOperationType));
                List<AcctBusinessDebitFlow> acctConsumeFlows = collect.get(41);
                List<AcctBusinessDebitFlow> acctRefundFlows = collect.get(42);
                if (CollectionUtils.isNotEmpty(acctConsumeFlows)) {
                    for (AcctBusinessDebitFlow e : acctConsumeFlows) {
                        try {
                            if (e.getCallbackNum() >= 10) {
                                //发钉钉
                                String msgError = "【fenbei-pay】商务充值消费资金流水上账超过10次，不再自动上账，需手动处理,资金流水id :" + e.getAccountFlowId();
                                dingDingMsgService.sendMsg(msgError);
                                continue;
                            }
                            AcctSubOperationReqRPCDTO bankTradeReqDto = new AcctSubOperationReqRPCDTO();
                            bankTradeReqDto.setBankName(e.getBankName());
                            bankTradeReqDto.setCompanyId(e.getCompanyId());
                            bankTradeReqDto.setOperationAmount(e.getOperationAmount());
                            bankTradeReqDto.setBankAcctId(e.getBankAcctId());
                            bankTradeReqDto.setOperationUserId(e.getOperationUserId());
                            bankTradeReqDto.setBizNo(e.getBizNo());
                            bankTradeReqDto.setCashierTxnId(e.getCashierTxnId());
                            bankTradeReqDto.setAccountSubType(e.getAccountSubType());
                            bankTradeReqDto.setAccountModel(e.getAccountModel());
                            uAcctBusinessDebitService.callBankConsume(bankTradeReqDto, e,e.getTargetBankAcctId());
                        }catch (Exception ex){
                            FinhubLogger.error("【4.0账户系统异常】商务充值消费调用银行转账异常,定时轮训：{}",e.getAccountFlowId());
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(acctRefundFlows)) {
                    for (AcctBusinessDebitFlow e : acctRefundFlows) {
                        try {
                            if (e.getCallbackNum() >= 10) {
                                //发钉钉
                                String msgError = "【fenbei-pay】商务充值退款资金流水上账超过10次，不再自动上账，需手动处理,资金流水id :" + e.getAccountFlowId();
                                dingDingMsgService.sendMsg(msgError);
                                continue;
                            }
                            AcctRefundReqDTO acctRefundReqDTO = new AcctRefundReqDTO();
                            acctRefundReqDTO.setCompanyId(e.getCompanyId());
                            acctRefundReqDTO.setOperationAmount(e.getOperationAmount());
                            acctRefundReqDTO.setBankName(e.getBankName());
                            acctRefundReqDTO.setOperationUserId(e.getOperationUserId());
                            acctRefundReqDTO.setBizNo(e.getBizNo());
                            acctRefundReqDTO.setReBizNo(e.getReBizNo());
                            acctRefundReqDTO.setBankAcctId(e.getBankAcctId());
                            acctRefundReqDTO.setRefundTxnId(e.getRefundTxnId());
                            acctRefundReqDTO.setAccountSubType(e.getAccountSubType());
                            acctRefundReqDTO.setAccountModel(e.getAccountModel());
                            uAcctBusinessDebitService.callBankRefund(acctRefundReqDTO, e,e.getTargetBankAcctId());
                        }catch (Exception ex){
                            FinhubLogger.error("【4.0账户系统异常】商务充值退款调用银行转账异常,定时轮训：{}",e.getAccountFlowId());
                        }
                    }
                }
            }
        }
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/guarantee/acct/call/bank")
    public void guaranteeAcctCallBank(HttpRequest request, HttpResponse response) {
        CompletableFuture.runAsync(() ->
                iAcctGuaranteeService.asyncCallBank());
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/freeze/acct/call/back/{id}/{num}")
    public void updateCallBack(HttpRequest request, HttpResponse response) {
        Long id = Long.valueOf(request.getPathValue("id"));
        Integer num = Integer.valueOf(request.getPathValue("num"));
        iAcctGuaranteeService.updateCallBack(id,num);
        ResponseResultUtils.success(response);
    }


    /**
     * 平台账户同步余额
     * @param request
     * @param response
     */
    @HttpService(value = "/acct/plat/balance", method = RequestMethod.POST)
    public void  syncPaltAcctBalance(HttpRequest request, HttpResponse response){

        uBankAcctService.syncPaltAcctBalance();
        ResponseResultUtils.success(response);
    }



    /**
     * 手动银行上账商务消费
     * @param request
     * @param response
     */
    @HttpService(value = "/acct/sync/bank/consume", method = RequestMethod.POST)
    public void  syncBankConsume(HttpRequest request, HttpResponse response){
        KafkaBankConsumeTaskMsg kafkaBankConsumeTaskMsg = request.getBodyObject(KafkaBankConsumeTaskMsg.class);
        uAcctCommonService.syncBankConsume(kafkaBankConsumeTaskMsg);
        ResponseResultUtils.success(response);
    }

    /**
     * 手动银行上账商务消费退款
     * @param request
     * @param response
     */
    @HttpService(value = "/acct/sync/bank/refund", method = RequestMethod.POST)
    public void  syncBankRefund(HttpRequest request, HttpResponse response){
        KafkaBankRefundTaskMsg kafkaBankConsumeTaskMsg = request.getBodyObject(KafkaBankRefundTaskMsg.class);
        uAcctCommonService.syncBankRefund(kafkaBankConsumeTaskMsg);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/refund/operation/manual")
    public void refund(HttpRequest request, HttpResponse response) {
        AcctRefundReqDTO dto = request.getBodyObject(AcctRefundReqDTO.class);
        if(StringUtils.isBlank(dto.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        iAcctAppPayService.refund(dto);
        ResponseResultUtils.success(response);
    }

    /**
     * stereo导出账户流水1
     * 一般户流水(充值/授信)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/stereo/acct/flow", method = RequestMethod.POST)
    public void importAcctFlowSearchStereoPage(HttpRequest request, HttpResponse response) {
        AcctFlowStereoPageServiceReqDTO accountSubVo = request.getBodyObject(AcctFlowStereoPageServiceReqDTO.class);
        ResponsePage<AcctFlowStereoPageServiceResqDTO> responsePage = iAcctFlowService.acctFlowSearchStereoPage(accountSubVo);
        if (Objects.isNull(responsePage)||CollectionUtils.isEmpty(responsePage.getDataList())) {
            ResponseResultUtils.success(response, Collections.emptyList());
            return;
        }
        List<AcctFlowStereoPageServiceResqDTO> dataList = responsePage.getDataList();
        List<AcctFlowForStereoRespDTO> respDTOList = dataList.stream().map(s->{
            AcctFlowForStereoRespDTO respDTO = new AcctFlowForStereoRespDTO();
            BeanCopierUtils.copyProperties(s, respDTO);
            //企业名称
            List<AccountGeneral> accountGenerals = uAcctGeneralService.findByCompanyId(s.getCompanyId());
            if (CollectionUtils.isNotEmpty(accountGenerals)) {
                respDTO.setCompanyName(accountGenerals.get(0).getCompanyName());
            }
            if(StringUtils.isNotBlank(s.getFundPlatform())){
                //众邦银行间连单独处理
                if(Objects.equals(s.getFundPlatform(),BankCoreConstant.ZBBANKH_CODE)){
                    respDTO.setFundPlatformName(BankCoreConstant.ZBBANKH_NAME);
                }else{
                    respDTO.setFundPlatformName(FundPlatformEnum.findPlatformByCode(s.getFundPlatform()).getName());
                }
            }
            //业务类型
            if(!Objects.isNull(s.getOperationTypeDesc())){
                respDTO.setOperationTypeName(s.getOperationTypeDesc());
            }
            //账户类型
            if(!Objects.isNull(s.getAccountSubType())){
                respDTO.setAccountSubTypeName(FundAccountSubType.getEnum(s.getAccountSubType()).getValue());
            }
            //业务账户类型
            if(null != s.getAccountModel()){
                respDTO.setAccountModelName(FundAccountModelType.getEnum(s.getAccountModel()).getValue());
            }

            // 场景
            if(Objects.nonNull(s.getOrderType())){
                CategoryTypeEnum categoryTypeEnum = CategoryTypeEnum.valueOf(s.getOrderType());
                respDTO.setOrderType(categoryTypeEnum == null ? "" : categoryTypeEnum.getName());
            }
            //操作金额
            respDTO.setOperationAmount(BigDecimalUtils.fen2yuan(s.getOperationAmount()));
            //余额
            respDTO.setBalance(BigDecimalUtils.fen2yuan(s.getBalance()));
            //操作时间
            respDTO.setCreateTime(DateUtils.formatTime(s.getCreateTime()));
            //需要有记账时间的才显示
            if(!FundAcctSyncBankStatus.noSyncNew(s.getSyncBankStatus())){
                //银行记账时间
                respDTO.setSyncBankTime(DateUtils.formatTime(s.getSyncBankTime()));
            }
            if(!s.getShowTargetAcct()){
                respDTO.setTargetAccount("");
                respDTO.setTargetAccountName("");
                respDTO.setTargetBankAllName("");
            }
            respDTO.setBillSummaryDesc(s.getBillSummaryDesc());
            // 安全脱敏
            respDTO.setBankAccountNo(DesensitizedUtil.bankCard(s.getBankAccountNo()));
            respDTO.setTargetAccount(DesensitizedUtil.bankCard(s.getTargetAccount()));
            respDTO.setTargetAccountName(DesensitizedUtil.chineseName(s.getTargetAccountName()));
            return respDTO;
        }).collect(Collectors.toList());
        ResponseResultUtils.success(response, respDTOList);
    }

    @HttpService(value = "/acct/company/name")
    public void changeCompanyName(HttpRequest request, HttpResponse response) {
        AcctChangeNameReqDTO changeNameReqDTO = request.getBodyObject(AcctChangeNameReqDTO.class);
        if(StringUtils.isBlank(changeNameReqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        iAcctMgrService.changeCompanyName(changeNameReqDTO);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/acct/company/upgrade")
    public void upgradeAcctCompany(HttpRequest request, HttpResponse response){
        AcctCompanyUpgradesReqDTO upgradesReqDTO = request.getBodyObject(AcctCompanyUpgradesReqDTO.class);
        List<AcctCompanyUpgradedRespDTO>  upgradedRespDTOS=Lists.newArrayList();
        try {
            List<String> companyIds = upgradesReqDTO.getCompanyIds();
            if(CollectionUtils.isNotEmpty(companyIds)){
                AcctCompanyUpgradedRespDTO acctCompanyUpgradedRespDTO;
                AcctCompanyUpgradeReqDTO acctCompanyUpgradeReqDTO;
                for (String companyId:companyIds) {
                    acctCompanyUpgradeReqDTO = new AcctCompanyUpgradeReqDTO();
                    acctCompanyUpgradeReqDTO.setCompanyId(companyId);
                    acctCompanyUpgradedRespDTO = uAcctCommonService.upgradeAcctCompany(acctCompanyUpgradeReqDTO);
                    upgradedRespDTOS.add(acctCompanyUpgradedRespDTO);
                }
            }
            ResponseResultUtils.success(response,upgradedRespDTOS);
        }  catch (FinPayException e) {
            FinhubLogger.error("【新账户4.0系统异常】企业升级 参数：{}=={}", JsonUtils.toJson(upgradesReqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户4.0系统异常】企业升级 参数：{}=={}", JsonUtils.toJson(upgradesReqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户4.0系统异常】企业升级 参数：{}=={}", JsonUtils.toJson(upgradesReqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    @HttpService(value = "/acct/business/debit/consume")
    public void acctBusinessDebitConsume(HttpRequest request, HttpResponse response) {
        AcctInnerConsumeReqDTO acctInnerConsumeReqDTO= request.getBodyObject(AcctInnerConsumeReqDTO.class);
        AcctConsumeReqDTO acctConsumeReqDTO = new AcctConsumeReqDTO();
        BeanUtils.copyProperties(acctInnerConsumeReqDTO,acctConsumeReqDTO);
        acctConsumeReqDTO.setOperationType(FundAcctDebitOptType.getEnum(acctInnerConsumeReqDTO.getOperationType()));
        FinhubLogger.info("acctBusinessDebitConsume req:{}", JSON.toJSONString(acctConsumeReqDTO));
        AcctOperationRespDTO acctOperationRespDTO = uAcctBusinessDebitService.consume(acctConsumeReqDTO);
        FinhubLogger.info("acctBusinessDebitConsume req:{},resp:{}", JSON.toJSONString(acctConsumeReqDTO),JSON.toJSONString(acctOperationRespDTO));
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/acct/business/debit/refund")
    public void acctBusinessDebitRefund(HttpRequest request, HttpResponse response) {
        AcctInnerRefundReqDTO acctInnerRefundReqDTO= request.getBodyObject(AcctInnerRefundReqDTO.class);
        AcctRefundReqDTO acctRefundReqDTO = new AcctRefundReqDTO();
        BeanUtils.copyProperties(acctInnerRefundReqDTO,acctRefundReqDTO);
        acctRefundReqDTO.setOperationType(FundAcctDebitOptType.getEnum(acctInnerRefundReqDTO.getOperationType()));
        FinhubLogger.info("acctBusinessDebitRefund req:{}", JSON.toJSONString(acctRefundReqDTO));
        AcctOperationRespDTO acctOperationRespDTO = uAcctBusinessDebitService.refund(acctRefundReqDTO);
        FinhubLogger.info("acctBusinessDebitRefund req:{},resp:{}", JSON.toJSONString(acctRefundReqDTO),JSON.toJSONString(acctOperationRespDTO));
        ResponseResultUtils.success(response);
    }

    /**
     * 新账户体系 个账户信息总览
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/overview/V5/{companyId}", method = RequestMethod.GET)
    public void queryWebAcctOverviewV5(HttpRequest request, HttpResponse response) {
        String companyId = request.getPathValue("companyId");
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        AcctOverviewV5RespDTO respDTO = uAcctCommonService.queryWebAcctOverviewV5(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }
    
    @HttpService(value = "/app/overview/V5/{companyId}", method = RequestMethod.GET)
    public void acctOverview(HttpRequest request, HttpResponse response) {
    	String companyId = request.getPathValue("companyId");
    	AppOverviewRequestDTO req = AppOverviewRequestDTO.builder()
    			.companyId(companyId)
    			.build();
    	AppAcctOverviewDTO overview = uAcctCommonService.queryAppAcctOverviewV5(req);
    	ResponseResultUtils.success(response, overview);
    }
    
    @HttpService(value = "/app/overview/list/{companyId}", method = RequestMethod.GET)
	public void acctListOverview(HttpRequest request, HttpResponse response) {
		String companyId = request.getPathValue("companyId");
		
		AppOverviewRequestDTO req = AppOverviewRequestDTO.builder()
				.companyId(companyId)
				.withDetail(true)
				.build();
		AppAcctOverviewDTO overview = uAcctCommonService.queryAppAcctOverviewV5(req);
    	ResponseResultUtils.success(response, overview);
	}
    
    @HttpService(value = "/app/overview/detail", method = RequestMethod.GET)
	public void acctListDetail(HttpRequest request, HttpResponse response) {
	  String companyId = request.getParameter("companyId");
      String clientType = request.getParameter("client_type");
      String clientVersion = request.getParameter("client_version");
      String bankAcctNo = request.getParameter("bankAccountNo");
      String fxAccountId = request.getParameter("fxAccountId");

	  AppOverviewRequestDTO req = AppOverviewRequestDTO.builder()
			  .companyId(companyId)
			  .bankAcctNo(bankAcctNo)
              .clientType(clientType)
              .clientVersion(clientVersion)
              .fxAccountId(fxAccountId)
			  .build();
	  AcctDebitMainDetailDTO detail = uAcctCommonService.queryAppAcctOverviewDetail(req);
	  ResponseResultUtils.success(response, detail);
	}

    /**
     * 企业账户-账户总览
     * 新账户体系 个账户信息总揽 如果商务账户授信 其他账户都是充值
     * @param request HttpRequest
     * @param response HttpResponse
     */
    @HttpService(value = "/overview/V5/accountList", method = RequestMethod.POST)
    public void accountList(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO acctOptFlowReqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        if(StringUtils.isBlank(acctOptFlowReqDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(acctOptFlowReqDTO.getCompanyId());
        List<AcctOverviewV5ListRespDTO> respDTOList = uAcctCommonService.queryWebAcctList(reqDTO);
        ResponseResultUtils.success(response, respDTOList);
    }



    /**
     * TODO 待定，等场景介入时再确定具体实现
     * 定时程序,更新上账状态
     * 每天早上9点执行
     * @param request
     * @param response
     */
    @HttpService(value = "/reimbursement/flow/syncbankstatus", method = RequestMethod.POST)
    public void reimbursementFlowUpdate(HttpRequest request, HttpResponse response) {
        FinhubLogger.info(CASHIER_CRONTAB_HEAD + "拉取报销账户上账结果，更新状态...Start.....");
        uAcctReimbursementService.updateNeedCallBank();
        ResponseResultUtils.success(response);
    }


    /**
     * 人工创建报销账户
     * @param request
     * @param response
     */
    @HttpService(value = "/manual/create/reimbursement", method = RequestMethod.POST)
    public void manualCreateReimbursement(HttpRequest request, HttpResponse response) {
        ReimbursementManualCreateAccountReqDTO reimbursementManualCreateAccountReqDTO = request.getBodyObject(ReimbursementManualCreateAccountReqDTO.class);
        iAcctReimbursementMgrService.createAccount(reimbursementManualCreateAccountReqDTO);
        ResponseResultUtils.success(response);
    }

    /**
     * 员工报销账户
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/import/reimbursement/flow", method = RequestMethod.POST)
    public void reimbursementFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        ResponsePage<AcctFlowRespDTO> flow = uAcctReimbursementService.queryReimbursementFlowPage(reqDTO);
//        makeTargetAccountName(flow);
        if (flow != null) {
        	Optional.ofNullable(flow.getDataList()).orElse(Collections.emptyList()).forEach(af -> {
        		af.setBalance(BigDecimalUtils.fen2yuan(af.getBalance()));
        		af.setOperationAmount(BigDecimalUtils.fen2yuan(af.getOperationAmount()));
        	});
            ResponseResultUtils.success(response, flow.getDataList());
        } else {
            ResponseResultUtils.success(response, Lists.newArrayList());
        }
    }

    @HttpService(value = "/queryMainAcctList", method = RequestMethod.POST)
    public void queryMainAcctList(HttpRequest request, HttpResponse response) {
        Map<String, String> map = request.getBodyObject(Map.class);
        String companyId = map.get("companyId");
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        List<AcctBaseMainRespDTO> acctBaseMainRespDTOS = uAcctCommonService.queryMainAcctView(reqDTO);
        AcctCompanyMainRespDTO acctCompanyMainRespDTO = AcctCompanyMainRespDTO.builder()
                .acctBaseMainRespDTOS(acctBaseMainRespDTOS)
                .build();
        ResponseResultUtils.success(response, acctCompanyMainRespDTO);
    }
    
    /**
     * 平台流水修复
     * @param request
     * @param response
     */
    @HttpService(value = "/fixBankAcctFlow", method = RequestMethod.POST)
    public void fixBankAcctFlow(HttpRequest request, HttpResponse response) {
        BankAcctFlowSearchReqDTO reqDTO = request.getBodyObject(BankAcctFlowSearchReqDTO.class);
        List<BankAcctFlow> flow = uBankAcctFlowService.queryBankAcctFlowList(reqDTO);
        FinhubLogger.info("修复分贝券退还到收款账户数据，flow：{}", JsonUtils.toJson(flow));
        if (CollectionUtils.isNotEmpty(flow)) {
            for (BankAcctFlow bankAcctFlow : flow) {
                BankAcctFlow update = new BankAcctFlow();
                update.setBankAcctFlowId(bankAcctFlow.getBankAcctFlowId());
                // 如果入参是负，判断是否小于0 ，不是就变成负数
                if (reqDTO.getSign().equals("-")) {
                    if(bankAcctFlow.getOperationAmount().compareTo(BigDecimal.ZERO) > 0 ){
                        update.setOperationAmount(BigDecimal.ZERO.subtract(bankAcctFlow.getOperationAmount()));
                    }else {
                        continue;
                    }
                }else {
                    if(bankAcctFlow.getOperationAmount().compareTo(BigDecimal.ZERO) < 0 ){
                        update.setOperationAmount(bankAcctFlow.getOperationAmount().abs());
                    }else {
                        continue;
                    }
                }
                update.setUpdateTime(new Date());
                uBankAcctFlowService.updateByFLowIdSelective(update);
            }
            List<String> bankAcctIdList = flow.stream().map(BankAcctFlow::getBankAcctId).collect(Collectors.toList());
            List<BankAcct> bankAcctList = uBankAcctService.queryAll().stream().filter(s -> bankAcctIdList.contains(s.getBankAcctId())).collect(Collectors.toList());
            // 开始时间
            Date startExtractTime = reqDTO.getTradeStartTime();
            // 结束时间
            Date endExtractTime = reqDTO.getTradeEndTime();
            while (!DateUtils.isSameDate(startExtractTime,endExtractTime)){
                Date extractTime = startExtractTime;
                uBankAcctExtractService.saveBankAcctExtractDayDoHandle(new ArrayList<>(), bankAcctList, new ArrayList<>(), extractTime);
                startExtractTime = DateUtils.addDay(startExtractTime,1);
            }
        }
    }


    /**
     * 平台账户数据修改
     * @param request
     * @param response
     */
    @HttpService(value = "/updateBankAcct", method = RequestMethod.POST)
    public void updateBankAcct(HttpRequest request, HttpResponse response) {
        BankAcct reqDTO = request.getBodyObject(BankAcct.class);
        boolean updateBankAcct = uBankAcctService.updateBankAcct(reqDTO);
        ResponseResultUtils.success(response, updateBankAcct);
    }

    /**
     * 插入主体数据
     * @param request
     * @param response
     */
    @HttpService(value = "/insertCompanyMain", method = RequestMethod.POST)
    public void insertCompanyMain(HttpRequest request, HttpResponse response) {
        AcctCreateMainReqDTO createMainReqDTO = request.getBodyObject(AcctCreateMainReqDTO.class);
        //保存主体信息
        AcctCompanyMainReqDTO companyMainReqDTO = AcctCompanyMainReqDTO.builder().build();
        BeanUtils.copyProperties(createMainReqDTO, companyMainReqDTO);
        AcctCompanyMain acctCompanyMain = uAcctCompanyMainService.saveAcctCompanyMain(companyMainReqDTO);
        ResponseResultUtils.success(response, acctCompanyMain);
    }

    /**
     * 查询主体数据
     * @param request
     * @param response
     */
    @HttpService(value = "/queryCompanyMain", method = RequestMethod.POST)
    public void queryCompanyMain(HttpRequest request, HttpResponse response) {
        List<String> mainIds = request.getBodyObject(List.class);
        List<AcctCompanyMain> byMainIds = uAcctCompanyMainService.findByMainIds(mainIds);
        ResponseResultUtils.success(response, byMainIds);
    }
    
    /**
     * 下载连连电子回单
     * @param request
     * @param response
     */
    @HttpService(value = "/download/receipt/from/lianlian", method = RequestMethod.POST)
    public void downloadReceiptFromLL(HttpRequest request, HttpResponse response) {
    	CompletableFuture.runAsync(() -> {
    		uAcctGeneralService.downloadReceiptFromLianlian();
    		FinhubLogger.info("补偿下载连连充值电子流水电子回单完成本次执行");
    	}, asyncExecutor);
    	
        ResponseResultUtils.success(response);
    }

    /**
     * 平安电子回单列表人工填充
     * <AUTHOR>
     * @date 2023-07-14 10:58:27
     */
    @HttpService(value = "/manual/spa/receipt", method = RequestMethod.POST)
    public void manualSpaReceipt(HttpRequest request, HttpResponse response) {
        SpaManualReceiptQueryReqDTO req = request.getBodyObject(SpaManualReceiptQueryReqDTO.class);
        FinhubLogger.info("平安电子回单人工更新 req:{}", JSON.toJSONString(req));
        ValidateUtils.validate(req);
        boolean info;
        try {
            BankCardTrapFlow bankCardTrapFlowExisted =  iBankCardTrapFlowManager.queryTrapFlowBySubTrapNo(req.getTxnId());
            if (Objects.nonNull(bankCardTrapFlowExisted)){
                BankCardTrapFlow bankCardTrapFlow = new BankCardTrapFlow();
                bankCardTrapFlow.setId(bankCardTrapFlowExisted.getId());
                bankCardTrapFlow.setCostImageUrl(req.getCostImageUrl());
                bankCardTrapFlow.setCostImageStatus(req.getCostImageStatus());
                bankCardTrapFlow.setCostImageTime(req.getCostImageTime());
                if (bankCardTrapFlowExisted.getOperationAmount() == null || BigDecimalUtils.hasPrice(req.getTrapAmount())){
                    bankCardTrapFlow.setOperationAmount(req.getTrapAmount());
                }

                info = iBankCardTrapFlowManager.updateById(bankCardTrapFlow);
                ResponseResultUtils.success(response, info);
            }else {
                ResponseResultUtils.success(response, req);
            }

        }catch (Exception e){
            FinhubLogger.warn("平安电子回单查询异常", e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalCoreResponseCode.EXCEPTION.getCode()));
        }

    }

    /**
     * 平安电子回单列表人工填充
     * @date 2023-07-14 10:58:27
     */
    @HttpService(value = "/manual/spa/receipt/fix", method = RequestMethod.POST)
    public void manualSpaReceiptFix(HttpRequest request, HttpResponse response) {
        SpaManualReceiptQueryReqDTO req = request.getBodyObject(SpaManualReceiptQueryReqDTO.class);
        FinhubLogger.info("平安电子回单人工更新作废 req:{}", JSON.toJSONString(req));
        ValidateUtils.validate(req);
        boolean info;
        try {
            BankCardTrapFlow bankCardTrapFlowExisted =  iBankCardTrapFlowManager.queryTrapFlowBySubTrapNo(req.getTxnId(),req.getId());
            if (Objects.nonNull(bankCardTrapFlowExisted)){
                BankCardTrapFlow bankCardTrapFlow = new BankCardTrapFlow();
                bankCardTrapFlow.setId(bankCardTrapFlowExisted.getId());
                bankCardTrapFlow.setTrapNo(bankCardTrapFlowExisted.getSubTrapNo());
                if (BigDecimalUtils.hasPrice(req.getTrapAmount())) {
                    bankCardTrapFlow.setSubOperationAmount(req.getTrapAmount());
                }
                info = iBankCardTrapFlowManager.updateById(bankCardTrapFlow);
                ResponseResultUtils.success(response, info);
            }else {
                ResponseResultUtils.success(response, req);
            }

        }catch (Exception e){
            FinhubLogger.warn("平安电子回单查询异常", e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalCoreResponseCode.EXCEPTION.getCode()));
        }

    }
}
