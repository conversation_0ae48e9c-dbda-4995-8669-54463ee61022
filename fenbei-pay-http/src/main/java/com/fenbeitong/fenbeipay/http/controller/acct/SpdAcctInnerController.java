package com.fenbeitong.fenbeipay.http.controller.acct;

import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.api.model.dto.BankFlowRespDto;
import com.fenbeitong.dech.api.model.dto.spd.SpdVirtualAllocatedListRespDTO;
import com.fenbeitong.dech.api.model.dto.spd.SpdVirtualAllocatedQueryRpcReqDTO;
import com.fenbeitong.dech.api.service.IBankTradeFlowSearchService;
import com.fenbeitong.dech.api.service.ISpdSearchService;
import com.fenbeitong.fenbeipay.acctdech.service.BankAcctService;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctSearchReqDTO;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcct;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.http.vo.SpdRechargeJonConfigVO;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.http.HttpHeaders;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-04-23 下午2:17
 */

@HttpService("/internal/acct/spd")
public class SpdAcctInnerController {

    @Autowired
    private ISpdSearchService iSpdSearchService;

    @Autowired
    private AccountGeneralService accountGeneralService;

    @Autowired
    private BankAcctService bankAcctService;

    @Autowired
    IBankTradeFlowSearchService iBankTradeFlowSearchService;

    @Value("${spd.noticeUrl}")
    private String noticeUrl;

    private final static Integer TIMEOUT = 20000;

    private static Map<String, String> headerMap = new HashMap<>();
    static {
        headerMap.put(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());
    }


    /**
     * 虚账户已分摊查询通知bank(充值+不明资金)
     * @param request
     * @param response
     */
    @HttpService(value = "/recharge/process", method = RequestMethod.POST)
    public void rechargeProcess(HttpRequest request, HttpResponse response) {
        String jobConfig = request.getParameter("jobConfig");
        SpdRechargeJonConfigVO spdRechargeJonConfigVO = JSON.parseObject(jobConfig, SpdRechargeJonConfigVO.class);
        BankAcctSearchReqDTO bankAcctSearchReqDTO = new BankAcctSearchReqDTO();
        bankAcctSearchReqDTO.setBankName(BankNameEnum.SPD.getCode());
        List<BankAcct> bankAccts = bankAcctService.queryBySerachReq(bankAcctSearchReqDTO);
        List<String> bankAcctNos = bankAccts.stream().map(BankAcct::getBankAccountNo).collect(Collectors.toList());
        if(ObjUtils.isNotEmpty(spdRechargeJonConfigVO) && spdRechargeJonConfigVO.getIsRetry()){
            AccountGeneral byBankAcctId = accountGeneralService.findByBankAcctId(BankNameEnum.SPD.getCode(), spdRechargeJonConfigVO.getVirtualNo());
            if(Objects.nonNull(byBankAcctId)){
                noticeBank(byBankAcctId,spdRechargeJonConfigVO.getShareDate(),spdRechargeJonConfigVO.getShareDate(),spdRechargeJonConfigVO.getJnlSeqNo(),bankAcctNos);
            }
        }else{
            String endDate = DateUtils.format(new Date(), DateUtils.FORMAT_DATE_YYYYMMDD);
            Date date = DateUtils.addDay(new Date(), -1);
            String startDate = DateUtils.format(date, DateUtils.FORMAT_DATE_YYYYMMDD);
            List<AccountGeneral> allAccountGeneral = accountGeneralService.findAllAccountGeneral(Lists.newArrayList(BankNameEnum.SPD.getCode()));
            if(CollectionUtils.isNotEmpty(allAccountGeneral)){
                for (AccountGeneral accountGeneral : allAccountGeneral) {
                    noticeBank(accountGeneral,startDate,endDate,null,bankAcctNos);
                }
            }
        }
        ResponseResultUtils.success(response, null);
    }

    private void noticeBank(AccountGeneral accountGeneral,String startDate,String endDate,String jnlSeqNo,List<String> bankAcctNos){
        CompletableFuture.runAsync(()->{
            SpdVirtualAllocatedQueryRpcReqDTO spdVirtualAllocatedQueryRpcReqDTO = new SpdVirtualAllocatedQueryRpcReqDTO();
            spdVirtualAllocatedQueryRpcReqDTO.setVirtualAcctNo(accountGeneral.getBankAccountNo());
            spdVirtualAllocatedQueryRpcReqDTO.setBeginDate(startDate);
            spdVirtualAllocatedQueryRpcReqDTO.setEndDate(endDate);
            spdVirtualAllocatedQueryRpcReqDTO.setJnlSeqNo(jnlSeqNo);
            List<SpdVirtualAllocatedListRespDTO> spdVirtualAllocatedListRespDTOS = iSpdSearchService.queryVirtualAllocatedDetail(spdVirtualAllocatedQueryRpcReqDTO);
            FinhubLogger.info("SpdAcctInnerController noticebank queryVirtualAllocatedDetail result{}", JsonUtils.toJson(spdVirtualAllocatedListRespDTOS));
            //过滤贷｜对手方平台账户｜提现退汇
            spdVirtualAllocatedListRespDTOS = spdVirtualAllocatedListRespDTOS.stream().filter(spdVirtualAllocatedListRespDTO ->
                    (spdVirtualAllocatedListRespDTO.getDebitCreditFlag().equals("1")&&!bankAcctNos.contains(spdVirtualAllocatedListRespDTO.getOppositeAcctNo()))).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(spdVirtualAllocatedListRespDTOS)){
                for (SpdVirtualAllocatedListRespDTO spdVirtualAllocatedListRespDTO : spdVirtualAllocatedListRespDTOS) {
                    BankFlowRespDto bankFlowRespDto = iBankTradeFlowSearchService.queryCashOutFlowByBankNameAndTellerTransNo(BankNameEnum.SPD.getCode(), jnlSeqNo);
                    if(Objects.nonNull(bankFlowRespDto)){
                        //提现退汇存在流水，跳过
                        continue;
                    }
                    //http通知bank
                    try {
                        FinhubLogger.info("SpdAcctInnerController noticeBank param{}",JsonUtils.toJson(spdVirtualAllocatedListRespDTO));
                        String result = HttpClientUtils.postBody(noticeUrl, JsonUtils.toJson(spdVirtualAllocatedListRespDTO), TIMEOUT, CharsetUtil.UTF_8, headerMap);
                        FinhubLogger.info("spdnotice result {}", JsonUtils.toJson(result));
                    }catch (Exception e){
                        FinhubLogger.error("浦发线下充值通知bank异常 err=",e);
                    }catch (Throwable re){
                        FinhubLogger.error("浦发线下充值通知bank异常 err=",re);
                    }
                }
            }
        });
    }
}
