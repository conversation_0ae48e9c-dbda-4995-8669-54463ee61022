package com.fenbeitong.fenbeipay.http.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-11-10 上午11:44
 */
@Data
public class PersonAcctTransferVO implements Serializable {

    private String employeeId;

    /**
     *  0  非全部升级 1 全部升级
     */
    @NotNull(message = "isAll不能为空")
    private Integer isAll;
}
