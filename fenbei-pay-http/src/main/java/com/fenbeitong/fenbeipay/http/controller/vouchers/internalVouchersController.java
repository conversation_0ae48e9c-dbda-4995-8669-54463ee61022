package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherFlowType;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherSourceType;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTaskType;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskCreateRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersTaskRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersTaskResponseRPCDTO;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTaskService;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskTimingMapper;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.voucher.VoucherTaskStatus;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.*;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.dto.*;
import com.fenbeitong.fenbeipay.vouchers.service.VoucherHandleService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersOperationFlowService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.task.service.*;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTypeService;
import com.fenbeitong.fenbeipay.vouchers.vo.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: java类作用描述
 * @ClassName: innerVouchersController
 * @Author: zhangga
 * @CreateDate: 2019/1/22 12:25 PM
 * @UpdateUser:
 * @UpdateDate: 2019/1/22 12:25 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/internal/vouchers")
public class internalVouchersController extends BaseController{

    @Autowired
    private VouchersTypeService vouchersTypeService;
    @Autowired
    private VouchersOperationFlowService vouchersOperationFlowService;
    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    private VouchersGrantRecoveryTaskService vouchersGrantRecoveryTaskService;
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private ICompanyService iCompanyService;
    @Autowired
    private IVouchersTaskService iVouchersTaskService;
    @Autowired
    private VouchersTaskDelayService vouchersTaskDelayService;

    @Autowired
    private VouchersTaskTimingService vouchersTaskTimingService;

    @Autowired
    private VouchersTaskTimingMapper vouchersTaskTimingMapper;

    @Autowired
    private VoucherHandleService voucherHandleService;


    @Autowired
    private VouchersTaskService vouchersTaskService;


    /**
     * createVouchersType
     *
     * @return void
     * @Description 创建分贝券分类
     * @Date 下午4:56 2018/12/26
     * @Param [request, response]
     **/
    @HttpService(value = "/create/vouchers_type/v2", method = RequestMethod.POST)
    public void createVouchersType(HttpRequest request, HttpResponse response) {
        VouchersTypeDTO vouchersTypeDTO = request.getBodyObject(VouchersTypeDTO.class);
        vouchersTypeService.createVouchersType(vouchersTypeDTO);
        ResponseResultUtils.success(response);
    }

    /**
     * deleteVouchersType
     *
     * @return void
     * @Description 删除分贝券分类
     * @Date 下午5:42 2018/12/26
     * @Param [request, response]
     **/
    @HttpService(value = "/delete/vouchers_type/v2", method = RequestMethod.POST)
    public void deleteVouchersType(HttpRequest request, HttpResponse response) {
        VouchersTypeKeysDTO idsDTO = request.getBodyObject(VouchersTypeKeysDTO.class);
        if (ObjUtils.isEmpty(idsDTO)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        List<String> keys = idsDTO.getKeys();
        keys.forEach(key -> {
            vouchersTypeService.deleteVouchersTypeByKey(key, idsDTO.getOperationUserId());
        });
        ResponseResultUtils.success(response);
    }

    /**
     * updateVouchersType
     *
     * @return void
     * @Description 修改分贝券分类
     * @Date 下午5:41 2018/12/26
     * @Param [request, response]
     **/
    @HttpService(value = "/update/vouchers_type/v2", method = RequestMethod.POST)
    public void updateVouchersType(HttpRequest request, HttpResponse response) {
        VouchersTypeDTO vouchersTypeDTO = request.getBodyObject(VouchersTypeDTO.class);
        vouchersTypeService.updateVouchersTypeByKey(vouchersTypeDTO);
        ResponseResultUtils.success(response);
    }

    /**
     * getVouchersTypeByKey
     *
     * @return void
     * @Description 获取分贝券分类详情
     * @Date 下午1:52 2018/12/27
     * @Param [request, response]
     **/
    @HttpService(value = "/vouchers_type/{key}/v2", method = RequestMethod.GET)
    public void getVouchersTypeByKey(HttpRequest request, HttpResponse response) {
        String key = request.getPathValue("key");
        if (ObjUtils.isEmpty(key) || "{key}".equals(key)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        VouchersType vouchersType = vouchersTypeService.getVouchersTypeByKey(key);
        //封装VO参数
        VouchersTypeVO vouchersTypeVO = new VouchersTypeVO();
        vouchersTypeVO.encapsulationVO(vouchersType);
        ResponseResultUtils.success(response, vouchersTypeVO);
    }

    /**
     * selectVouchersTypeList
     *
     * @return void
     * @Description 分贝券列表查询
     * @Date 8:06 PM 2019/1/7
     * @Param [request, response]
     **/
    @HttpService(value = "/vouchers_type/list/v2", method = RequestMethod.POST)
    public void selectVouchersTypeList(HttpRequest request, HttpResponse response) {
        VouchersTypeRequestDTO requestDTO = request.getBodyObject(VouchersTypeRequestDTO.class);
        List<VouchersTypeVO> result = new ArrayList<>();
        vouchersTypeService.getVouchersTypeList(requestDTO).forEach(vouchersType -> {
            //封装VO参数
            VouchersTypeVO vouchersTypeVO = new VouchersTypeVO();
            vouchersTypeVO.encapsulationVO(vouchersType);
            result.add(vouchersTypeVO);
        });
        ResponseResultUtils.success(response, result);
    }

    /**
     * innerVouchersWithdraw
     *
     * @return void
     * @Description 分贝券内部撤回接口
     * @Date 8:06 PM 2019/1/7
     * @Param [request, response]
     **/
    @HttpService(value = "/withdraw/v2", method = RequestMethod.POST)
    public void innerVouchersWithdraw(HttpRequest request, HttpResponse response) {
        VouchersWithdrawTasksDTO recoveryTasksDTO = request.getBodyObject(VouchersWithdrawTasksDTO.class);
        String grantTaskId = recoveryTasksDTO.getTaskId();
        if (ObjUtils.isEmpty(grantTaskId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (!recoveryTasksDTO.getWithdrawAll() && (ObjUtils.isEmpty(recoveryTasksDTO.getVoucherIds())
                || ObjUtils.isEmpty(recoveryTasksDTO.getEmployeeIds()))) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        VouchersTask vouchersTask = vouchersTaskHandleService.getVouchersTaskById(grantTaskId, recoveryTasksDTO.getCompanyId());
        if (ObjUtils.isEmpty(vouchersTask)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (vouchersTask.getStatus() == VoucherTaskStatus.WAIT.getValue()) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "分贝券发放任务未完成");
        }
        //更改发放任务状态为 5-发放任务全部撤回
        if (vouchersTask.getStatus() == VoucherTaskStatus.SELECT_WITHDRAWAL_ALL.getValue()) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "分贝券发放任务已全部撤回，不可多次撤回");
        }
        String taskId = vouchersGrantRecoveryTaskService.createVouchersWithdrawTask(recoveryTasksDTO);
        vouchersGrantRecoveryTaskService.publishAndStart(taskId);
        if (taskId != null) {
            ResponseResultUtils.success(response, taskId);
        } else {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
    }

    /**
     * @Description: 技术后台发放分贝券
     * @methodName: grantVoucher
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/10 11:43 AM
     **/
//    @HttpService(value = "/grant/v2", method = RequestMethod.POST)
//    public void grantVoucher(HttpRequest request, HttpResponse response) {
//        VouchersGrantTaskCreateDTO requestDTO = request.getBodyObject(VouchersGrantTaskCreateDTO.class);
//        //校验当前企业券是否可先开票
//        List<CompanyRuleTypeDTO> ruleTypeDTOS = iCompanyService.queryCompanyRuleByType(requestDTO.getCompanyId(), Lists.newArrayList(CompanyRuleType.CompanyBeforehandInvoiceType));
//        if (ObjUtils.isEmpty(ruleTypeDTOS)) {
//            throw new FinPayException(GlobalResponseCode.NO_AUTH);
//        }
//        CompanyRuleTypeDTO typeDTO = ruleTypeDTOS.get(0);
//        Integer writeInvoiceType = requestDTO.getWriteInvoiceType();
//        if (typeDTO.getState() == CoreConstant.NO && writeInvoiceType != null && WriteInvoiceType.isAdvance(writeInvoiceType)) {
//            throw new FinPayException(GlobalResponseCode.NO_AUTH);
//        }
//        CompanyFbqRuleDTO companyFbqRuleDTO = iCompanyService.queryCompanyFbqRule(requestDTO.getCompanyId());
//        requestDTO.checkParameters(companyFbqRuleDTO);
//
//        //检查是否开启预算占用
//        VoucherCostStatusEnum voucherCostStatusEnum = checkCostConfig(requestDTO.getCompanyId(), requestDTO.getCostInfo(), requestDTO.getDateOfExpense());
//        requestDTO.setCostStatus(voucherCostStatusEnum.getStatus());
//
//        VouchersGrantRecoveryTasks grantTask = vouchersGrantRecoveryTaskService.createVouchersGrantTask(requestDTO);
//        CompletableFuture.runAsync(() -> vouchersGrantRecoveryTaskService.addVouchersTaskDetailsGrant(grantTask));
//        ResponseResultUtils.success(response, grantTask);
//    }

    @HttpService(value = "/export/vouchers/flow", method = RequestMethod.POST)
    public void exportVouchersFlow(HttpRequest request, HttpResponse response) {
        VouchersFlowRequestDTO vouchersFlowQuery = request.getBodyObject(VouchersFlowRequestDTO.class);
        FinhubLogger.info("【分贝券流水导出参数】{}", vouchersFlowQuery.toString());
        List<VouchersFlowVO> vouchersFlowList = vouchersOperationFlowService.queryExport(vouchersFlowQuery);
        vouchersFlowList.forEach(vouchersFlowVO -> {
            vouchersFlowVO.setAmount(BigDecimalUtils.fenToYuan(vouchersFlowVO.getAmount()));
            if (VoucherFlowType.isConsumption(vouchersFlowVO.getType())) {
                vouchersFlowVO.setAmount(vouchersFlowVO.getAmount().negate());
            }
        });
        ResponseResultUtils.success(response, vouchersFlowList);
    }

    @HttpService(value = "/task/export/flow", method = RequestMethod.POST)
    public void exportTaskFlow(HttpRequest request, HttpResponse response) {
        FinhubLogger.info("分贝券流水任务导出请求参数：{}",request.getBody());
        VouchersOperationRequestVO requestVO = request.getBodyObject(VouchersOperationRequestVO.class);
        ResponsePage<VouchersOperationResponseVO> responsePage =  vouchersOperationFlowService.getVoucherFlowList(requestVO);
        ResponseResultUtils.success(response, responsePage.getDataList());
    }

    @HttpService(value = "/export/grant_recovery/statistics", method = RequestMethod.POST)
    public void exportGrantRecoveryStatistics(HttpRequest request, HttpResponse response) {
        VouchersRequestDTO vouchersFlowQuery = request.getBodyObject(VouchersRequestDTO.class);
        FinhubLogger.info("【分贝券发放统计导出参数】{}", vouchersFlowQuery.toString());
        List<VoucherForStatisticsVO> voucherForStatisticsVOList = vouchersPersonService.queryExport(vouchersFlowQuery);
        voucherForStatisticsVOList.forEach(vo -> {
            vo.setVoucherDenomination(BigDecimalUtils.fenToYuan(vo.getVoucherDenomination()));
            vo.setUsedAmount(BigDecimalUtils.fenToYuan(vo.getUsedAmount()));
            vo.setBalance(BigDecimalUtils.fenToYuan(vo.getBalance()));
            vo.setRecoveryAmount(BigDecimalUtils.fenToYuan(vo.getRecoveryAmount()));
        });
        ResponseResultUtils.success(response, voucherForStatisticsVOList);
    }

    @HttpService(value = "/export/recovery/statistics", method = RequestMethod.POST)
    public void exportRecoveryStatistics(HttpRequest request, HttpResponse response) {
        VouchersRequestDTO vouchersFlowQuery = request.getBodyObject(VouchersRequestDTO.class);
        FinhubLogger.info("【分贝券回收统计导出参数】{}", vouchersFlowQuery.toString());
        Date startTime = vouchersFlowQuery.getStartTime();
        Date endTime = vouchersFlowQuery.getEndTime();
        if (startTime == null || endTime == null) {
            endTime = new Date();
            startTime = DateUtils.addYear(endTime, -3);
        }
        if (DateUtils.addYear(startTime, 3).before(endTime)) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_RECOVERY_STATISTICS_TIME_ERROR);
        }
        vouchersFlowQuery.setStartRecoveryTime(startTime);
        vouchersFlowQuery.setEndRecoveryTime(endTime);
        vouchersFlowQuery.setStartTime(null);
        vouchersFlowQuery.setEndTime(null);
        List<VoucherForStatisticsVO> voucherForStatisticsVOList = vouchersPersonService.queryRecoveryExport(vouchersFlowQuery);
        voucherForStatisticsVOList.forEach(vo -> {
            vo.setVoucherDenomination(BigDecimalUtils.fenToYuan(vo.getVoucherDenomination()));
            vo.setUsedAmount(BigDecimalUtils.fenToYuan(vo.getUsedAmount()));
            vo.setBalance(BigDecimalUtils.fenToYuan(vo.getBalance()));
            vo.setRecoveryAmount(BigDecimalUtils.fenToYuan(vo.getRecoveryAmount()));
        });
        ResponseResultUtils.success(response, voucherForStatisticsVOList);
    }

    @HttpService(value = "/start/task/{vouchersTaskId}/{companyId}/v3", method = RequestMethod.GET)
    public void startVouchersTask(HttpRequest request, HttpResponse response) {
        //校验权限
        String vouchersTaskId = request.getPathValue("vouchersTaskId");
        String companyId = request.getPathValue("companyId");
        if (ObjUtils.isEmpty(vouchersTaskId) || ObjUtils.isEmpty(companyId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        boolean taskId = vouchersTaskHandleService.startGrantTask(vouchersTaskId, companyId);
        ResponseResultUtils.success(response, taskId);
    }

    /**
     * @Description: 适用于 已扣除账户金额，但是分贝券未发放，技术后台处理
     * //TODO 例如： 发放任务启动成功，已经成功扣除账户余额，但是在执行发券明细是有部分消息丢失或者其他原因导致消息消费失败，而部分员工或者全部员工未发放情况
     * @methodName: retryStartTaskUnDeductionAccountQuota
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2020/8/28 8:47 PM
     **/
    @HttpService(value = "/retry/task/only/details/{vouchersTaskId}/{companyId}/v3", method = RequestMethod.GET)
    public void retryStartTaskUnDeductionAccountQuota(HttpRequest request, HttpResponse response) {
        //校验权限
        String vouchersTaskId = request.getPathValue("vouchersTaskId");
        String companyId = request.getPathValue("companyId");
        if (ObjUtils.isEmpty(vouchersTaskId) || ObjUtils.isEmpty(companyId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        boolean taskId = vouchersTaskHandleService.retryStartTaskUnDeductionAccountQuota(vouchersTaskId, companyId);
        ResponseResultUtils.success(response, taskId);
    }

//    @HttpService(value = "/consume", method = RequestMethod.POST)
//    public void consumeVoucher(HttpRequest request, HttpResponse response) {
//        VouchersPaymentParams vouchersPaymentParams = request.getBodyObject(VouchersPaymentParams.class);
//        ResultDO<VoucherPayResult> resultDO = vouchersPersonService.vouchersPayment(vouchersPaymentParams);
//        ResponseResultUtils.success(response, resultDO);
//    }
//
//    @HttpService(value = "/refund", method = RequestMethod.POST)
//    public void refundVoucher(HttpRequest request, HttpResponse response) {
//        VouchersRefundParams vouchersRefundParams = request.getBodyObject(VouchersRefundParams.class);
//        ResultDO<VoucherPayResult> resultDO = vouchersPersonService.vouchersRefund(vouchersRefundParams);
//        ResponseResultUtils.success(response, resultDO);
//    }

    @HttpService(value = "/query/person/available/voucher/amount/{employeeId}", method = RequestMethod.GET)
    public void queryAvailableVoucherAmount(HttpRequest request, HttpResponse response) {
        String employeeId = request.getPathValue("employeeId");
        VoucherStatisticsAmountVO statisticsAmountVO = vouchersPersonService.queryAvailableVoucherAmount(employeeId);
        ResponseResultUtils.success(response, statisticsAmountVO);
    }

    /**
     * @Description: 更新或添加分贝券任务&明细
     **/
    @HttpService(value = "/create/grant/voucher/task", method = RequestMethod.POST)
    public void updateVoucherTask(HttpRequest request, HttpResponse response) {
        VouchersUpdateTaskDTO updateTaskDTO = request.getBodyObject(VouchersUpdateTaskDTO.class);
        String companyId = updateTaskDTO.getCompanyId();
        String vouchersTaskId = updateTaskDTO.getVouchersTaskId();
        Integer voucherTaskType = updateTaskDTO.getVoucherTaskType();
        JSONObject data = updateTaskDTO.getData();
        if (voucherTaskType == VoucherTaskType.EXAMINE_APPROVE_GRANT.getValue()) {
            VouchersTaskRPCDTO vouchersTaskRPCDTO = JsonUtils.toObj(data.toJSONString(), VouchersTaskRPCDTO.class);
            Integer voucherSourceType = VoucherSourceType.COMPANY_GRANT_EXAMINE_APPROVE.getValue();
            vouchersTaskRPCDTO.setIsStart(false);
            VouchersTaskResponseRPCDTO vouchersGrantTask = iVouchersTaskService.createVouchersGrantTask(vouchersTaskRPCDTO, voucherTaskType, voucherSourceType);
            if (!ObjUtils.isBlank(vouchersTaskId)) {
                updateVoucherTaskId(companyId, vouchersTaskId, vouchersGrantTask.getTaskId());
            }
        }
        if (voucherTaskType == VoucherTaskType.OPEN_API_GRANT.getValue() || voucherTaskType == VoucherTaskType.ALLOWANCE_MILEAGE_GRANT.getValue()) {
            VouchersTaskCreateRPCDTO vouchersTaskDTO = JsonUtils.toObj(data.toJSONString(), VouchersTaskCreateRPCDTO.class);
            vouchersTaskDTO.setIsStart(false);
            VouchersTaskResponseRPCDTO task = iVouchersTaskService.createGrantTaskByVouchersTemplet(vouchersTaskDTO);
            if (!ObjUtils.isBlank(vouchersTaskId)) {
                updateVoucherTaskId(companyId, vouchersTaskId, task.getTaskId());
            }
        }
        ResponseResultUtils.success(response, updateTaskDTO);
    }

    private void updateVoucherTaskId(String companyId, String newVouchersTaskId, String currentVouchersTaskId) {
        VouchersTask vouchersTask = vouchersTaskHandleService.getVouchersTaskById(currentVouchersTaskId, companyId);
        vouchersTask.setVouchersTaskId(newVouchersTaskId);
        vouchersTaskHandleService.updateVouchersTask(vouchersTask);
        VouchersTaskDetails details = new VouchersTaskDetails();
        details.setVouchersTaskId(newVouchersTaskId);
        details.setUpdateTime(new Date());
        vouchersTaskHandleService.updateVouchersTaskDetails(currentVouchersTaskId, details);
    }

    @HttpService(value = "/update/voucher/task/status", method = RequestMethod.POST)
    public void updateVoucherTaskStatus(HttpRequest request, HttpResponse response) {
        VouchersUpdateTaskDTO updateTaskDTO = request.getBodyObject(VouchersUpdateTaskDTO.class);
        String vouchersTaskId = updateTaskDTO.getVouchersTaskId();
        Integer taskStatus = updateTaskDTO.getTaskStatus();
        String companyId = updateTaskDTO.getCompanyId();
        VouchersTask vouchersTask = vouchersTaskHandleService.getVouchersTaskById(vouchersTaskId, companyId);
        vouchersTask.setStatus(taskStatus);
        vouchersTaskHandleService.updateVouchersTask(vouchersTask);
        ResponseResultUtils.success(response, updateTaskDTO);
    }

    /**
     * @Description: 仅支持全部明细插入
     * @methodName: addVoucherTaskDetails
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2021/4/2 2:39 下午
     **/
//    @HttpService(value = "/add/voucher/task/details", method = RequestMethod.POST)
//    public void addVoucherTaskDetails(HttpRequest request, HttpResponse response) {
//        VouchersUpdateTaskDTO updateTaskDTO = request.getBodyObject(VouchersUpdateTaskDTO.class);
//        String companyId = updateTaskDTO.getCompanyId();
//        String vouchersTaskId = updateTaskDTO.getVouchersTaskId();
//        VouchersGrantRecoveryTasks grantRecoveryTask = vouchersGrantRecoveryTaskService.getVouchersGrantRecoveryTaskByBizNo(companyId, vouchersTaskId);
//        if (ObjUtils.isNull(grantRecoveryTask)) {
//            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
//        }
//        vouchersGrantRecoveryTaskService.addVouchersTaskDetailsGrant(grantRecoveryTask);
//        ResponseResultUtils.success(response, updateTaskDTO);
//    }

    /**
     * 分贝券定时延迟发放
     * @param request
     * @param response
     */
    @HttpService(value = "/start/schedule/task", method = RequestMethod.POST)
    public void startVoucherScheduleTask(HttpRequest request, HttpResponse response) {
        VouchersGrantTaskDTO taskDTO = request.getBodyObject(VouchersGrantTaskDTO.class);
        FinhubLogger.info("【分贝券定时延迟发放任务】启动发放任务，任务参数：{}", taskDTO);
        if (ObjUtils.isNull(taskDTO)) {
            throw new ValidateException("参数对象不能为空");
        }
        vouchersTaskDelayService.startVouchersTaskGrant(taskDTO);
        ResponseResultUtils.success(response);
    }


    /**
     * 更新分贝券过期时间
     * @param request
     * @param response
     */
    @HttpService(value = "/update/voucher/expiryTime", method = RequestMethod.POST)
    public void updateVoucherExpiryTime(HttpRequest request, HttpResponse response) {
        VouchersUpdateDTO vouchersUpdateDTO = request.getBodyObject(VouchersUpdateDTO.class);
        FinhubLogger.info("【更新分贝券过期时间】请求参数：{}", vouchersUpdateDTO);
        if (ObjUtils.isNull(vouchersUpdateDTO)) {
            throw new ValidateException("参数对象不能为空");
        }
        ResponseResultUtils.success(response, voucherHandleService.updateVoucherExpiryTime(vouchersUpdateDTO));
    }


    /**
     * 日历检查
     * @param request
     * @param response
     */
    @HttpService(value = "/checkCalendar", method = RequestMethod.POST)
    public void checkCalendar(HttpRequest request, HttpResponse response) {
        VouchersTaskTimingCheckDTO checkDTO = request.getBodyObject(VouchersTaskTimingCheckDTO.class);
        boolean checkCalendarAlready =  vouchersTaskTimingService.checkCalendarAlready(checkDTO);
        JSONObject result = new JSONObject();
        result.put("checkCalendarAlready",checkCalendarAlready);
        VouchersTaskTimingExample example = new VouchersTaskTimingExample();
        VouchersTaskTimingExample.Criteria criteria = example.createCriteria();
        criteria.andCompanyIdEqualTo(checkDTO.getCompanyId()).
                andGrantStatusEqualTo(2);
        List<VouchersTaskTiming> dataList = vouchersTaskTimingMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(dataList)){
           boolean checkGrantDayByFrequency=  vouchersTaskTimingService.checkGrantDayByFrequency(dataList.get(0));
            result.put("checkGrantDayByFrequency",checkGrantDayByFrequency);
        }
        ResponseResultUtils.success(response,result);
    }


    /**
     * 发送kafka消息
     * @param request
     * @param response
     */
    @HttpService(value = "/sendKafkaMsg", method = RequestMethod.POST)
    public void sendKafkaVoucherTaskMsg(HttpRequest request, HttpResponse response) {
        VouchersTask vouchersTask = request.getBodyObject(VouchersTask.class);
        vouchersTaskService.finishVouchersTask(vouchersTask);
        ResponseResultUtils.success(response);
    }

}
