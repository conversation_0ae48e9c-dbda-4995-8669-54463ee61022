package com.fenbeitong.fenbeipay.http.controller.acct;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessGeneralService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.AcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UCompanySwitchService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.impl.AccountInnerService;
import com.fenbeitong.fenbeipay.acctpublic.manager.AcctPublicManager;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenChangeType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.accountpublic.AccountPublicStatus;
import com.fenbeitong.fenbeipay.api.model.dto.AuthRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.QueryAuthRequestDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferCommonReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferCreditToEachReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctTransferDebitReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AppOverviewRequestDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByComIdReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByMIdBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponInfoVO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.api.service.auth.CompanyAuthService;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.enums.auth.AuthObjMenuCodeEnum;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.dto.account.AcctCashWithDrawalSmsDTO;
import com.fenbeitong.fenbeipay.core.model.dto.account.AcctGeneralCashWithDrawalDTO;
import com.fenbeitong.fenbeipay.core.model.dto.account.BankCollectionAccountInfo;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.service.auth.DataAuthService;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessDebit;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyCard;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctIndividualDebit;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.nf.FundFreezen;
import com.fenbeitong.fenbeipay.manager.AccountPublicManager;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountGeneralService;
import com.fenbeitong.fenbeipay.nf.unit.service.UFundFreezenService;
import com.fenbeitong.finhub.auth.constant.UserAttributeConstant;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.auth.annotation.FinhubRequiresPermissions;

import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.CompanyMainTypeEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundPlatformEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.fenbeitong.stereo.api.model.dto.pay.BankAccountInfoDTO;
import com.fenbeitong.stereo.api.service.pay.IPayAccountService;
import com.fenbeitong.usercenter.api.model.dto.privilege.AuthObjResDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.fenbeitong.finhub.common.auth.constant.ElementConstant.ACCOUNT_OVERVIEW;
import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.COMPANY_ID;
import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.USER_ID;

/**
 * <AUTHOR>
 * @Date 2021/1/11
 * @Description 新账户体系接口4.0
 */
@HttpService("/fbp/acct/app/company")
public class AcctAppController extends AcctAbstractController {

    @Autowired
    private UAccountGeneralService uAccountGeneralService;

    @Autowired
    private AcctPublicManager acctPublicManager;

    @Autowired
    private AcctCompanyGatewayService acctCompanyGatewayService;

    @Autowired
    private UFundFreezenService uFundFreezenService;

    @Autowired
    private UCompanySwitchService uCompanySwitchService;

    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    private AccountPublicManager accountPublicManager;

    @Autowired
    private DataAuthService dataAuthService;
    
    @Autowired
    private IAcctFundMgrService iAcctFundMgrService;
    
    @Autowired
    private IAcctMgrService acctMgrService;
    
    @Autowired
    private IPayAccountService payAccountService;
    
    @Autowired
    private AcctBusinessGeneralService acctBusinessGeneralService;
    
    @Autowired
    private AcctCompanyMainService acctCompanyMainService;
    
    @Autowired
    private AccountInnerService accountInnerService;
    
    @Autowired
    private CompanyAuthService companyAuthService;
    
    /**
     * 海外卡收款账户缓存KEY
     */
    private static final String AIRWALLEX_ACCOUNT_INFO_KEY = "AIRWALLEX_ACCOUNT_INFO_KEY";

    @Autowired
    private RedisDao redisDao;

    static {
		try {
			Field field = JsonUtils.class.getDeclaredField("defaultMapper");
			field.setAccessible(true);
			ObjectMapper defaultMapper = (ObjectMapper) field.get(null);
			defaultMapper.enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN);
		} catch (Exception e) {
			FinhubLogger.error("error when init JsonUtils->", e);
		}
	}

    /**
     * 我的-账户与消费分析(新账户体系)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/my_account_analysis", method = RequestMethod.GET)
    public void myAccountAnalysisNew(HttpRequest request, HttpResponse response) {

        AcctAnalysisReqDTO acctAnalysisReqDTO = new AcctAnalysisReqDTO();
        boolean voucherStat = request.getBooleanParameter("voucherStat", false);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        acctAnalysisReqDTO.setVoucherStat(voucherStat);
        acctAnalysisReqDTO.setEmployeeId(request.getAttribute(USER_ID).toString());
        acctAnalysisReqDTO.setCompanyId(companyId);
        AcctAnalysisAppRespDTO analysisAppRespDTO = new AcctAnalysisAppRespDTO();
        try {
            //总览
            AcctGeneralAppRespDTO generalAppRespDTO = new AcctGeneralAppRespDTO();
            //商务
            AcctSubAppRespDTO acctSubAppRespDTO = null;
            //个人
            AcctSubIndividualAppRespDTO individualAppRespDTO = null;
            //虚拟卡
            AcctCompanyCardAppRespDTO acctCompanyCardAppRespDTO = null;
            //对公账户
            List<AcctPublicAppRespDTO> acctPublicAppRespDTOS = null;

            //所有余额账户
            List<AccountGeneral> accountGenerals = uAccountGeneralService.findByCompanyId(companyId);
            //所有商务充值账户
            List<AcctBusinessDebit> businessDebits = uAcctBusinessDebitService.findByCompanyId(companyId);
            //所有个人充值账户
            List<AcctIndividualDebit> individualDebits = uAcctIndividualDebitService.findByCompanyId(companyId);
            //所有授信账户
            List<AcctCompanyCard> acctCompanyCards = uAcctCompanyCardService.findByCompanyId(companyId);
            //所有对公账户
            List<AccountPublic> accountPublics = acctPublicManager.findAcctPublicNameByCompanyId(companyId);
            BigDecimal allBalance = BigDecimal.ZERO;
            String companyName = "";
            boolean companySwitch = uCompanySwitchService.isCompanySwitch(companyId);
            if (CollectionUtils.isNotEmpty(accountGenerals)) {
                BigDecimal reduce = accountGenerals.stream().map(AccountGeneral::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                allBalance = allBalance.add(reduce);
                companyName = accountGenerals.get(0).getCompanyName();
            }
            if (CollectionUtils.isNotEmpty(businessDebits)) {
                BigDecimal reduce = businessDebits.stream().map(AcctBusinessDebit::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                allBalance = allBalance.add(reduce);
                companyName = businessDebits.get(0).getCompanyName();
            }
            if (CollectionUtils.isNotEmpty(individualDebits)) {
                BigDecimal reduce = individualDebits.stream().map(AcctIndividualDebit::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                allBalance = allBalance.add(reduce);
                companyName = individualDebits.get(0).getCompanyName();
            }
            if (CollectionUtils.isNotEmpty(acctCompanyCards)) {
                BigDecimal reduce = acctCompanyCards.stream().map(AcctCompanyCard::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                allBalance = allBalance.add(reduce);
                companyName = acctCompanyCards.get(0).getCompanyName();
            }
            if (CollectionUtils.isNotEmpty(accountPublics)) {
                BigDecimal reduce = accountPublics.stream().map(AccountPublic::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                allBalance = allBalance.add(reduce);
                companyName = accountPublics.get(0).getCompanyName();
            }
            generalAppRespDTO.setBalance(allBalance);
            generalAppRespDTO.setCompanyId(companyId);
            generalAppRespDTO.setCompanyName(companyName);
            if (!companySwitch) {
                List<AccountGeneral> collect = accountGenerals.stream().filter(e -> BankNameEnum.isFbt(e.getBankName())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    generalAppRespDTO.setBalance(collect.get(0).getBalance());
                }
            }

            //子账户 激活的
            AcctComGwByComIdReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwByComIdReqDTO();
            acctComGwBySubAndBankReqDTO.setCompanyId(acctAnalysisReqDTO.getCompanyId());
            List<AcctCommonBaseDTO> accountSubList = acctCompanyGatewayService.findActCommonByComId(acctComGwBySubAndBankReqDTO);
            if (CollectionUtils.isEmpty(accountSubList)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
            }
            if (CollectionUtils.isNotEmpty(accountSubList)) {
                for (AcctCommonBaseDTO accountsub : accountSubList) {
                    //企业
                    if (FundAccountSubType.isBusinessAccount(accountsub.getAccountSubType())) {
                        acctSubAppRespDTO = new AcctSubAppRespDTO();
                        BeanUtils.copyProperties(accountsub, acctSubAppRespDTO);
                        continue;
                    }
                    //个人
                    if (FundAccountSubType.isIndividualAccount(accountsub.getAccountSubType())) {
                        individualAppRespDTO = new AcctSubIndividualAppRespDTO();
                        BeanUtils.copyProperties(accountsub, individualAppRespDTO);
                        if (acctAnalysisReqDTO.isVoucherStat()) {
                            Date currentMonthStart = DateUtil.getTimesMonthStart();
                            Date currentMonthEnd = DateUtil.getTimesNextMonthStart();
                            BigDecimal grantAmount = uFundFreezenService.querySumFreezeBudgetFlow(acctAnalysisReqDTO.getCompanyId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountsub.getAccountModel(), FreezenUseType.INDIVIDUAL_VOUCHERS, currentMonthStart, currentMonthEnd, Arrays.asList(FreezenChangeType.FREEZING.getKey()));
                            BigDecimal withdrawalAmount = uFundFreezenService.querySumFreezeBudgetFlow(acctAnalysisReqDTO.getCompanyId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountsub.getAccountModel(), FreezenUseType.INDIVIDUAL_VOUCHERS, currentMonthStart, currentMonthEnd, Arrays.asList(FreezenChangeType.UNFREEZING.getKey()));
                            BigDecimal consumeAmount = uFundFreezenService.querySumFreezeBudgetFlow(acctAnalysisReqDTO.getCompanyId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountsub.getAccountModel(), FreezenUseType.INDIVIDUAL_VOUCHERS, currentMonthStart, currentMonthEnd, Arrays.asList(FreezenChangeType.PAY.getKey(), FreezenChangeType.REFUND.getKey()));
                            individualAppRespDTO.setVoucherConsumeAmount(consumeAmount.abs());
                            individualAppRespDTO.setVoucherGrantAmount(grantAmount.abs());
                            individualAppRespDTO.setVoucherWithdrawalAmount(withdrawalAmount.abs());
                        }
                        continue;
                    }

                    //虚拟卡
                    if (FundAccountSubType.isComCardAccount(accountsub.getAccountSubType())) {
                        acctCompanyCardAppRespDTO = new AcctCompanyCardAppRespDTO();
                        acctCompanyCardAppRespDTO.setBalance(accountsub.getBalance());
                        acctCompanyCardAppRespDTO.setBankAccountNo(accountsub.getBankAccountNo());
                        acctCompanyCardAppRespDTO.setBankCode(accountsub.getBankName());
                        acctCompanyCardAppRespDTO.setBankName(BankNameEnum.getBankEnum(accountsub.getBankName()).getName());
                        acctCompanyCardAppRespDTO.setBalance(accountsub.getBalance());
                        acctCompanyCardAppRespDTO.setBalanceEmployee(BigDecimal.ZERO);
                        Integer accountSubType = FundAccountSubType.BUSINESS_ACCOUNT.getKey();
                        //不在白名单 是商务账户 在白名单 在区分平台方
                        if (companySwitch) {
                            accountSubType = FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey();
                        }
                        List<FundFreezen> fundFreezens = uFundFreezenService.queryByCompanyIdAndUseTypes(companyId, accountSubType, Arrays.asList(accountsub.getAccountModel()), FreezenUseType.bankFreezenInt());
                        if (CollectionUtils.isNotEmpty(fundFreezens)) {
                            BigDecimal freezeBalance = BigDecimal.ZERO;
                            if (BankNameEnum.isFbt(accountsub.getBankName())) {
                                List<FundFreezen> collect = fundFreezens.stream().filter(fundFreezen -> FundPlatformEnum.isFBTPlatform(fundFreezen.getBankName())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(collect)) {
                                    freezeBalance = collect.stream().map(e -> e.getFreezeBalance()).reduce(BigDecimal.ZERO, BigDecimal::add);
                                }
                            } else {
                                FundFreezen fundFreezen = fundFreezens.stream().filter(Objects::nonNull)
                                        .filter(fundFreez -> accountsub.getBankName().equalsIgnoreCase(fundFreez.getBankName())).findFirst().orElse(null);
                                if (Objects.nonNull(fundFreezen)) {
                                    freezeBalance = fundFreezen.getFreezeBalance();
                                }
                            }
                            acctCompanyCardAppRespDTO.setBalanceEmployee(freezeBalance);
                        }
                    }
                }
            }

            //对公账户
            if (CollectionUtils.isNotEmpty(accountPublics)) {
                List<AcctPublicAppRespDTO> accountPublicRespVoBack = new ArrayList<>();
                List<String> companyMainIds = accountPublics.stream().map(AccountPublic::getCompanyMainId).collect(Collectors.toList());
                List<AcctCompanyMain> acctCompanyMains = uAcctCompanyMainService.findByMainIds(companyMainIds);
                Map<String, String> mapIdAndName = acctCompanyMains.stream().collect(Collectors.toMap(AcctCompanyMain::getCompanyMainId, AcctCompanyMain::getBusinessName, (v1, v2) -> v1));

                accountPublics.stream().forEach(accountPublic -> {
                    AcctPublicAppRespDTO acctPublicAppRespDTO = new AcctPublicAppRespDTO();
                    acctPublicAppRespDTO.setBankAccountNo(accountPublic.getBankAccountNo());
                    acctPublicAppRespDTO.setBankCode(accountPublic.getBankAccountName());
                    acctPublicAppRespDTO.setBankName(BankNameEnum.getBankEnum(accountPublic.getBankAccountName()).getName());
                    acctPublicAppRespDTO.setBalance(accountPublic.getBalance());
                    acctPublicAppRespDTO.setCompanyMainName(mapIdAndName.get(accountPublic.getCompanyMainId()));
                    acctPublicAppRespDTO.setCompanyMainId(accountPublic.getCompanyMainId());
                    acctPublicAppRespDTO.setCompanyMainType(accountPublic.getCompanyMainType());
                    acctPublicAppRespDTO.setCompanyMainTypeDesc(CompanyMainTypeEnum.getEnum(accountPublic.getCompanyMainType()).getName());
                    accountPublicRespVoBack.add(acctPublicAppRespDTO);
                });
                acctPublicAppRespDTOS = accountPublicRespVoBack;
            }


            //企业账户 老版本要求结构不变更
            AcctSubAppRespDTO enterpriseAccountSub = new AcctSubAppRespDTO();
            //增加红包券余额
            AcctRedcouponInfoAppRespDTO accountRedcouponInfo = new AcctRedcouponInfoAppRespDTO();
            AccountRedcouponInfoVO redcouponInfoVO = accountRedcouponSearchService.queryAccountCouponInfoVO(companyId);
            if (redcouponInfoVO == null) {
                redcouponInfoVO = new AccountRedcouponInfoVO();
            }
            BeanUtils.copyProperties(redcouponInfoVO, accountRedcouponInfo);
            analysisAppRespDTO = new AcctAnalysisAppRespDTO(generalAppRespDTO, acctSubAppRespDTO, individualAppRespDTO, enterpriseAccountSub, accountRedcouponInfo, acctCompanyCardAppRespDTO, acctPublicAppRespDTOS, companySwitch);
        } catch (Exception ex) {
            FinhubLogger.error("【app账户总览异常】,公司id={}==={}", companyId, ex);
        }
        ResponseResultUtils.success(response, analysisAppRespDTO);
    }
    
    @FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/overview/V5", method = RequestMethod.GET)
    public void acctOverview(HttpRequest request, HttpResponse response) {
    	String companyId = getUserCompanyId(request);
        FinhubLogger.info("企业账户-账户总览acctOverview companyId为:{} ， userId = {}",companyId ,getUserId(request));
    	AppOverviewRequestDTO req = AppOverviewRequestDTO.builder()
    			.companyId(companyId)
    			.build();
    	AppAcctOverviewDTO overview = uAcctCommonService.queryAppAcctOverviewV5(req);
    	ResponseResultUtils.success(response, overview);
    }
    
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
	@HttpService(value = "/overview/list", method = RequestMethod.GET)
	public void acctListOverview(HttpRequest request, HttpResponse response) {
		String companyId = getUserCompanyId(request);
		
		AppOverviewRequestDTO req = AppOverviewRequestDTO.builder()
				.companyId(companyId)
				.withDetail(true)
				.build();
		AppAcctOverviewDTO overview = uAcctCommonService.queryAppAcctOverviewV5(req);
    	ResponseResultUtils.success(response, overview);
	}
  
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
	@HttpService(value = "/overview/detail", method = RequestMethod.GET)
	public void acctListDetail(HttpRequest request, HttpResponse response) {
	  String companyId = getUserCompanyId(request);
      String clientType = request.getParameter("client_type");
      String clientVersion = request.getParameter("client_version");
      String bankAcctNo = request.getParameter("bankAccountNo");
      String fxAccountId = request.getParameter("fxAccountId");

	  AppOverviewRequestDTO req = AppOverviewRequestDTO.builder()
			  .companyId(companyId)
			  .bankAcctNo(bankAcctNo)
              .clientType(clientType)
              .clientVersion(clientVersion)
              .fxAccountId(fxAccountId)
			  .build();
	  AcctDebitMainDetailDTO detail = uAcctCommonService.queryAppAcctOverviewDetail(req);
	  ResponseResultUtils.success(response, detail);
	}
	
	/**
     * 充值账户-单个主体下所有账户的展示
     *
     * @param request
     * @param response
     */
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/main/debit/show", method = RequestMethod.POST)
    public void debitMainShow(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO req = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AcctDebitMainRespDTO resp = uAcctCommonDebitService.findDebitMain(req);
        ResponseResultUtils.success(response, resp);
    }
    
    /**
     * 充值账户-余额账户转账
     */
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/transferOut2Others",method = RequestMethod.POST)
    public void transferOut2Others(HttpRequest request, HttpResponse response) {
		UserInfoVO user = getUserInfo(request);
		
        verifyIdentity(request);
        String body = request.getBody();
        AcctTransferDebitReqDTO param = JsonUtils.toObj(body, AcctTransferDebitReqDTO.class);
        param.setOperationUserId(user.getId());
        param.setOperationUserName(user.getName());
        param.setOperationChannelType(OperationChannelType.EMPLOYEE.getKey());
        FinhubLogger.info("【APP】企业信息管理-账户管理-余额管理-可用余额账户转出,接口[IAcctFundMgrService#transferOut2Others]-请求信息{}",JsonUtils.toJson(param));
        AcctCommonOptRespDTO acctCommonOptRespDTO = null;
		try {
			acctCommonOptRespDTO = iAcctFundMgrService.transferOut2Others(param);
		} catch (Exception e) {
			acctCommonOptRespDTO = new AcctCommonOptRespDTO();
			acctCommonOptRespDTO.setCode(-1);
			acctCommonOptRespDTO.setErrorMsg(e.getMessage());
		}
        FinhubLogger.info("【APP】企业信息管理-账户管理-余额管理-可用余额账户转出,接口[IAcctFundMgrService#transferOut2Others]-响应信息信息{}",JsonUtils.toJson(acctCommonOptRespDTO));
        ResponseResultUtils.success(response, acctCommonOptRespDTO);
    }
	
	/**
     * 充值账户-转回总账户
     */
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/transferOut2General",method = RequestMethod.POST)
    public void transferOut2General(HttpRequest request, HttpResponse response) {
        String body = request.getBody();
        AcctTransferCommonReqDTO param = JsonUtils.toObj(body, AcctTransferCommonReqDTO.class);
        param.setTargetBankName(param.getBankName());
        param.setTargetBankAccountNo(param.getBankAccountNo());
        UserInfoVO user = getUserInfo(request);
        param.setOperationUserId(user.getId());
        param.setOperationUserName(user.getName());
        param.setOperationChannelType(OperationChannelType.EMPLOYEE.getKey());
        FinhubLogger.info("【APP】企业信息管理-账户管理-余额管理-转入可用余额账户,接口[IAcctFundMgrService#transferOut2General]-请求信息{}",JsonUtils.toJson(param));
        AcctCommonOptRespDTO acctCommonOptRespDTO;
		try {
			acctCommonOptRespDTO = iAcctFundMgrService.transferOut2General(param);
		} catch (Exception e) {
			acctCommonOptRespDTO = new AcctCommonOptRespDTO();
			acctCommonOptRespDTO.setCode(-1);
			acctCommonOptRespDTO.setErrorMsg(e.getMessage());
		}
        FinhubLogger.info("【APP】企业信息管理-账户管理-余额管理-转入可用余额账户,接口[IAcctFundMgrService#transferOut2General]-相应信息{}",JsonUtils.toJson(acctCommonOptRespDTO));
        ResponseResultUtils.success(response, acctCommonOptRespDTO);
    }
	
	/**
     * 授信账户-主体
     *
     * @param request
     * @param response
     */
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/main/credit/show", method = RequestMethod.POST)
    public void creditMainShow(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO reqDTO = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AcctCreditMainRespDTO respDTO = uAcctCommonCreditService.findCreditMain(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }
	
	/**
     * 充值账户-单个主体下主体&余额账户信息
     *
     * @param request
     * @param response
     */
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/main/base/info", method = RequestMethod.POST)
    public void queryMainInfo(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO reqDTO = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AcctCompanyMainDetailRespDTO resp = uAcctCommonService.queryCompanyMainDetailInfo(reqDTO);
        if (Objects.nonNull(resp) && 
        		!Objects.equals(BankNameEnum.CGB.getCode(), reqDTO.getBankName()) && 
        		!Objects.equals(BankNameEnum.LFBANK.getCode(), reqDTO.getBankName())) {
        	UserInfoVO user = getUserInfo(request);
        	resp.setAgentName(user.getName());
        	resp.setAgentPhone(user.getPhone());
        }
        resp.setBankNameDesc(BankNameEnum.getBankEnum(resp.getBankName()).getName());
        ResponseResultUtils.success(response, resp);
    }
	
	/**
     * 提现发短信,包含廊坊银行、广发银行和其他
     */
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/withdraw/send/msg",method = RequestMethod.POST)
    public void cashWithdrawalSendSms(HttpRequest request, HttpResponse response) {
    	FinhubLogger.info("发送短信请求头->{}, 参数->{}", JSON.toJSONString(request.getHeaderMap()), request.getBody());
        AcctCashWithDrawalSmsDTO bodyObject = request.getBodyObject(AcctCashWithDrawalSmsDTO.class);
        String token = request.getHeader("X-Auth-Token");
        bodyObject.setToken(token);
        String smsId = acctBusinessGeneralService.sendSms4Withdraw(bodyObject);
        Map<String, Object> result = Maps.newHashMap();
        result.put("success", true);
        result.put("smsId", smsId);
        ResponseResultUtils.success(response, result);
    }
	
	/**
     * 余额账户-提现
     */
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/cashWithdrawal",method = RequestMethod.POST)
    public void cashWithdrawal(HttpRequest request, HttpResponse response) {
        AcctGeneralCashWithDrawalDTO param = request.getBodyObject(AcctGeneralCashWithDrawalDTO.class);
        UserInfoVO user = getUserInfo(request);
        param.setOperationUserId(user.getId());
        param.setOperationUserName(user.getName());
        param.setOperationChannelType(OperationChannelType.EMPLOYEE.getKey());
        
        AcctGeneralOptRespDTO acctGeneralOptRespDTO;
		try {
			//校验手机号，安全漏洞
	        checkParams(param, request);
			acctGeneralOptRespDTO = acctBusinessGeneralService.bankAcctCashWithdrawal(param);
		} catch (Exception e) {
			FinhubLogger.error("error when cashWithdrawal->{}", e);
			acctGeneralOptRespDTO = new AcctGeneralOptRespDTO();
			if (e instanceof FinhubException) {
				int code = ((FinhubException)e).getCode();
				if (Objects.equals(GlobalResponseCode.ACCOUNT_BANK_WITHDRAWAL_PROCESSING.getCode(), code)) {
					acctGeneralOptRespDTO.setCode(1);
				} else {
					acctGeneralOptRespDTO.setCode(-1);
				}
			} else {
				acctGeneralOptRespDTO.setCode(-1);
			}
			
			acctGeneralOptRespDTO.setErrorMsg(e.getMessage());
		}
        ResponseResultUtils.success(response, acctGeneralOptRespDTO);
    }
    
    private void checkParams(AcctGeneralCashWithDrawalDTO withdrawalDTO, HttpRequest request) {
        String sendMsgPhone = null;
        if(BankNameEnum.isCgb(withdrawalDTO.getBankName()) || BankNameEnum.isLfBank(withdrawalDTO.getBankName()) || BankNameEnum.isLianlian(withdrawalDTO.getBankName()) ){
            //如果是广发取经办人姓名手机号
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(withdrawalDTO.getCompanyId(), withdrawalDTO.getCompanyMainId(), withdrawalDTO.getBankName());
            if(Objects.nonNull(acctCompanyMain)){
                sendMsgPhone = acctCompanyMain.getAgentPhone();
            }
        } else {
            // 获取登录信息
            UserComInfoVO userComInfo = (UserComInfoVO) request.getAttribute(UserAttributeConstant.USER_COM_INFO);
            if(Objects.nonNull(userComInfo)){
                sendMsgPhone = userComInfo.getUser_phone();
            }
        }
        if(StringUtils.isNotBlank(sendMsgPhone) && 
        		StringUtils.isNotBlank(withdrawalDTO.getVerifyCodePhoneNum()) && 
        		!sendMsgPhone.equals(withdrawalDTO.getVerifyCodePhoneNum())){
            FinhubLogger.error("AcctAppController#checkParams#提现接口手机号不一致,应:{},实:{}", sendMsgPhone, sendMsgPhone.equals(withdrawalDTO.getVerifyCodePhoneNum()));
            throw new FinhubException(GlobalResponseCode.EXCEPTION_DATA_ERROR.getCode(), "提现手机号匹配失败");
        }
    }
	
	/**
     * 授信账户-额度转移
     * 转移额度到指定账户（先调减，后调增）商务授信与个人授信互转额度
     */
	@FinhubRequiresPermissions({ACCOUNT_OVERVIEW + ":*"})
    @HttpService(value = "/adjustFromSub",method = RequestMethod.POST)
    public void adjustFromSubV2(HttpRequest request, HttpResponse response) {
		verifyIdentity(request);
        String body = request.getBody();
        AcctTransferCreditToEachReqDTO param = JsonUtils.toObj(body, AcctTransferCreditToEachReqDTO.class);
        param.setBankName(BankNameEnum.FBT.getCode());
        param.setTargetBankName(BankNameEnum.FBT.getCode());
        param.setTargetBankAccountNo(param.getBankAccountNo());
        param.setOperationChannelType(OperationChannelType.EMPLOYEE.getKey());
        UserInfoVO user = getUserInfo(request);
        param.setOperationUserId(user.getId());
        param.setOperationUserName(user.getName());
        AcctCommonOptRespDTO acctCommonOptRespDTO;
		try {
			acctCommonOptRespDTO = iAcctFundMgrService.adjustFromSub(param);
		} catch (Exception e) {
			acctCommonOptRespDTO = new AcctCommonOptRespDTO();
			acctCommonOptRespDTO.setCode(-1);
			acctCommonOptRespDTO.setErrorMsg(e.getMessage());
		}
        FinhubLogger.info("【APP】>>>授信账户-额度转移充值账户接口调用结束,返回值:{}", JsonUtils.toJson(acctCommonOptRespDTO));
        ResponseResultUtils.success(response, acctCommonOptRespDTO);
    }
	
	@HttpService(value = "/recharge/config", method = RequestMethod.GET)
    public void rechargeConfig(HttpRequest request, HttpResponse response) {
        String companyId = getOperationCompanyId(request);
        try {
            boolean isTrialAndMarketVersion = checkTrailCompany(companyId);
            if (isTrialAndMarketVersion){
                Map<String,Object> resultMap = Maps.newHashMap();
                ResponseResultUtils.success(response,resultMap);
                return;
            }
        }catch (Exception e){
            FinhubLogger.error("试用版查询UC异常",e);
        }
        String companyMainId = request.getParameter("companyMainId");
        String bankName = request.getParameter("bankName");
        String bankAccountNo = request.getParameter("bankAccountNo");
        Map<String,Object> resultMap = Maps.newHashMap();
        // 判断是否是海外卡账户
        if (FxAcctChannelEnum.isAirWallex(bankName)) {
            getAccountInfo4AirWallex(resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        //收款账户信息
        boolean isFbt = BankNameEnum.isFbt(bankName);
        if (isFbt) {
        	String token = request.getHeader("X-Auth-Token");
        	routeAndGetAccountInfo(companyId, resultMap, token);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        // 查询企业是否切换
        boolean isCompanySwitch = uCompanySwitchService.isCompanySwitch(companyId);
        if (!isCompanySwitch){
            getOldAccountInfo(companyId,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        // 查询收款账户信息
        AcctComGwByMIdBankReqDTO acctComGwByMIdBankReqDTO = new AcctComGwByMIdBankReqDTO();
        acctComGwByMIdBankReqDTO.setCompanyId(companyId);
        acctComGwByMIdBankReqDTO.setCompanyMainId(companyMainId);
        acctComGwByMIdBankReqDTO.setBankName(bankName);
        acctComGwByMIdBankReqDTO.setBankAccountNo(bankAccountNo);
        //平安银行
        if (BankNameEnum.isSpa(bankName)){
        	accountInnerService.getAccountInfo4Spa(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isCgb(bankName)){
        	accountInnerService.getAccountInfo4Cgb(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isLfBank(bankName)){
        	accountInnerService.getAccountInfo4LfBank(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isSpd(bankName)){
        	accountInnerService.getAccountInfo4SpdBank(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isZBBank(bankName)){
        	accountInnerService.getAccountInfo4ZBBank(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isCitic(bankName)){
            //默认充值账户
        	accountInnerService.getAccountInfo4Citic(acctComGwByMIdBankReqDTO,resultMap);
            //绑定账户
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        
        if (BankNameEnum.isLianlian(bankName)) {
        	accountInnerService.getRechargeAccountInfo(acctComGwByMIdBankReqDTO, resultMap);
            ResponseResultUtils.success(response, resultMap);
            return;
        }
        
        getAccountInfoByAccountInfo(acctComGwByMIdBankReqDTO,resultMap);
        ResponseResultUtils.success(response,resultMap);
    }

    private void getAccountInfo4AirWallex(Map<String, Object> resultMap) {
        //收款账户信息
        String accountInfos = redisDao.getRedisTemplate().opsForValue().get(AIRWALLEX_ACCOUNT_INFO_KEY).toString();
        List<Map<String, Object>> accountInfoList = JsonUtils.toObj(accountInfos, List.class);
        resultMap.put("accountInfo",accountInfoList);
    }

    /**
     * 查询账户信息
     */
    private void getAccountInfoByAccountInfo(AcctComGwByMIdBankReqDTO acctComGwByMIdBankReqDTO, Map<String, Object> resultMap) {
        //收款账户信息
        List<BankCollectionAccountInfo> accountInfoList = null;
        // 调用RPC接口查询充值配置信息
        AcctCompanyMainDetailRespDTO acctMainBaseInfoRespDTO = acctMgrService.queryMainAndAcct(acctComGwByMIdBankReqDTO);
        // 根据结果组合参数bankAccountNo
        if(Objects.nonNull(acctMainBaseInfoRespDTO)){
            accountInfoList = Lists.newArrayList();
            BankCollectionAccountInfo bankCollectionAccountInfo = new BankCollectionAccountInfo();
            bankCollectionAccountInfo.setCollCompanyName(acctMainBaseInfoRespDTO.getCompanyMainName()); // 开户主体名称
            bankCollectionAccountInfo.setCollDentificationNumber(acctMainBaseInfoRespDTO.getBusinessLicenseCode()); // 营业执照号
            bankCollectionAccountInfo.setCollCompanyOpenBank(BankNameEnum.getBankEnum(acctMainBaseInfoRespDTO.getBankName()).getName()); // 开户行名称
            bankCollectionAccountInfo.setCollCompanyFixedTelephone(acctMainBaseInfoRespDTO.getBusinessPhone()); // 开户手机号
            bankCollectionAccountInfo.setCollCompanyBankAccount(acctMainBaseInfoRespDTO.getBankAccountNo()); // 企业虚拟账户
            bankCollectionAccountInfo.setBankBranchName(acctMainBaseInfoRespDTO.getBankBranchName()); // 支行名称
            
            if (Objects.equals(BankNameEnum.CITIC.getCode(), acctMainBaseInfoRespDTO.getBankName())) {
            	bankCollectionAccountInfo.setBankBranchName(bankCollectionAccountInfo.getCollCompanyOpenBank() + Optional.ofNullable(acctMainBaseInfoRespDTO.getBankBranchName()).orElse(""));
            }
            
            if (StringUtils.isBlank(acctMainBaseInfoRespDTO.getBankBranchName())) {
            	bankCollectionAccountInfo.setBankBranchName(bankCollectionAccountInfo.getCollCompanyOpenBank());
            }
            
            accountInfoList.add(bankCollectionAccountInfo);
        }
        FinhubLogger.info("companyId为:{}的收款账户信息为:{}", acctComGwByMIdBankReqDTO.getCompanyId(), JSON.toJSONString(accountInfoList));
        resultMap.put("accountInfo",accountInfoList);
    }
	
	private void getOldAccountInfo(String companyId, Map<String, Object> resultMap) {
        List<BankAccountInfoDTO> bankAccountInfoDTOS = payAccountService.selectAccountInfoByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(bankAccountInfoDTOS) ) {
        	bankAccountInfoDTOS.stream().forEach(acct -> {
        		if (StringUtils.isBlank(acct.getBankBranchName())) {
        			acct.setBankBranchName(acct.getCollCompanyOpenBank());
        		}
        	});
        }
        FinhubLogger.info("companyId为:{}的收款账户信息为:{}",companyId,JSON.toJSONString(bankAccountInfoDTOS));
        resultMap.put("accountInfo",bankAccountInfoDTOS);
    }
	
	private void routeAndGetAccountInfo(String companyId, Map<String, Object> resultMap, String token) {
		boolean enabled = acctBusinessGeneralService.isRedeemVoucherEnabled(token);
		if (enabled) {
			getOldAccountInfo(companyId, resultMap);
		} else {
			initWithDefaultAccts(resultMap);
		}
	}
	
	private void initWithDefaultAccts(Map<String, Object> resultMap) {
		String account = "[\n" +
                "  {\n" +
                "  \t\"collCompanyName\":\"北京分贝国际旅行社有限公司\",\n" +
                "    \"bankBranchName\":\"中国建设银行北京西直门北大街支行\",\n" +
                "    \"collCompanyBankAccount\":\"11050163720000000051\"\n" +
                "  },{\n" +
                "  \t\"collCompanyName\":\"北京分贝通科技有限公司\",\n" +
                "    \"bankBranchName\":\"中国建设银行北京西直门北大街支行\",\n" +
                "    \"collCompanyBankAccount\":\"11050163720000000082\"\n" +
                "  },{\n" +
                "  \t\"collCompanyName\":\"北京分贝商贸有限公司\",\n" +
                "    \"bankBranchName\":\"中国建设银行北京东四十条支行\",\n" +
                "    \"collCompanyBankAccount\":\"11050170520000000288\"\n" +
                "  }\n" +
                "]";
        List<Map<String, Object>> accts = JsonUtils.toObj(account, List.class);
        resultMap.put("accountInfo",accts);
	}

    /**
     * 对公账户-根据权限查询所有对公账户
     * @author: zhaoxu
     * @date: 2022-03-24 14:12:01
     */
    @HttpService(value = "/public/acct/list", method = RequestMethod.GET)
    public void publicAcctList(HttpRequest request, HttpResponse response) {
        AcctPublicRespDTO respDTO = new AcctPublicRespDTO();
        String companyId = getUserCompanyId(request);
        String userId = getUserId(request);
        String version = request.getHeader("client_version");

        String businessName = request.getParameter("businessName");
        try {

            // todo 新权限 done
            // List<EmployeePaymentAuthReqDTO.AccountInfo> changeAccountList = iPrivilegeService.getChangeAccountList(companyId, userId);
            AuthObjResDTO authObjResDTO = dataAuthService.getDataAuth(AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
            if(authObjResDTO == null){
                FinhubLogger.error("对公账户-查询所有对公账户, 未查询到权限数据, 参数: menuCode: {}, companyId: {}, userId: {}", AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
                ResponseResultUtils.success(response, respDTO);
                return;
            }
            if (!authObjResDTO.getAccount().getAllData() && CollectionUtils.isEmpty(authObjResDTO.getAccount().getPartDataIdList())) {
                FinhubLogger.warn("对公账户-查询所有对公账户, 未查询到有权限的账户");
                ResponseResultUtils.success(response, respDTO);
                return;
            }

            // 根据权限查询用户下的对公账户
            List<String> companyAccountIds = dataAuthService.getCompanyAuthChangeAccountIdList(companyId, authObjResDTO);

            // 权限校验
            // List<EmployeePaymentAuthReqDTO.AccountInfo> changeAccountList = iPrivilegeService.getChangeAccountList(companyId, userId);
            if(CollectionUtils.isEmpty(companyAccountIds)){
                ResponseResultUtils.success(response, respDTO);
                return;
            }

            // List<String> companyAccountIds = changeAccountList.stream().map(EmployeePaymentAuthReqDTO.AccountInfo::getId).collect(Collectors.toList());
            // 查询启用、禁用的账户状态
            List<Integer> status = Arrays.asList(AccountPublicStatus.NORMAL.getKey(), AccountPublicStatus.DISABLE.getKey());

            // 根据权限查询用户下的对公账户、查询账户对应公司信息
            respDTO = accountPublicManager.publicAcctList(companyId, status, companyAccountIds, businessName, version);

            // 总金额
            BigDecimal totalBalance = acctPublicSearchService.queryAcctPublicTotalBalance(companyId, status, companyAccountIds);
            respDTO.setTotalBalance(totalBalance);

            ResponseResultUtils.success(response, respDTO);
        } catch (Exception e) {
            FinhubLogger.error("【app账户总览异常】,公司id={}，userId={}", companyId, userId, e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getMsg()));
        } catch (Throwable e) {
            FinhubLogger.error("【app账户总览异常】,公司id={}，userId={}", companyId, userId, e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getMsg()));
        }
    }

    /**
     * 对公账户-根据权限查询所有对公账户总金额
     * @author: zhaoxu
     * @date: 2022-03-24 14:12:01
     */
    @HttpService(value = "/public/acct/totalBalance", method = RequestMethod.GET)
    public void totalBalance(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        String userId = getUserId(request);
        try {
            Map result = new HashMap<String, Object>();

            // todo 新权限 done
            // List<EmployeePaymentAuthReqDTO.AccountInfo> changeAccountList = iPrivilegeService.getChangeAccountList(companyId, userId);
            AuthObjResDTO authObjResDTO = dataAuthService.getDataAuth(AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
            if(authObjResDTO == null){
                FinhubLogger.error("对公账户-根据权限查询所有对公账户总金额, 未查询到权限数据, 参数: menuCode: {}, companyId: {}, userId: {}", AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
                result.put("totalBalance", BigDecimal.ZERO);
                ResponseResultUtils.success(response, result);
                return;
            }
            if (!authObjResDTO.getAccount().getAllData() && CollectionUtils.isEmpty(authObjResDTO.getAccount().getPartDataIdList())) {
                FinhubLogger.warn("对公账户-根据权限查询所有对公账户总金额, 未查询到有权限的账户");
                result.put("totalBalance", BigDecimal.ZERO);
                ResponseResultUtils.success(response, result);
                return;
            }
            // 权限校验
            // List<EmployeePaymentAuthReqDTO.AccountInfo> changeAccountList = iPrivilegeService.getChangeAccountList(companyId, userId);

            List<String> companyAccountIds = dataAuthService.getCompanyAuthChangeAccountIdList(companyId, authObjResDTO);
            if(CollectionUtils.isEmpty(companyAccountIds)){
                result.put("totalBalance", BigDecimal.ZERO);
                ResponseResultUtils.success(response, result);
                return;
            }

            // 根据权限查询用户下的对公账户
            List<Integer> status = Arrays.asList(AccountPublicStatus.NORMAL.getKey(), AccountPublicStatus.DISABLE.getKey());

            // List<String> companyAccountIds = changeAccountList.stream().map(EmployeePaymentAuthReqDTO.AccountInfo::getId).collect(Collectors.toList());
            BigDecimal totalBalance = acctPublicSearchService.queryAcctPublicTotalBalance(companyId, status, companyAccountIds);
            result.put("totalBalance", totalBalance);

            // 拼装返回参数
            ResponseResultUtils.success(response, result);
        } catch (Exception e) {
            FinhubLogger.error("【app账户总金额异常】,公司id={}，userId={}", companyId, userId, e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getMsg()));
        } catch (Throwable e) {
            FinhubLogger.error("【app账户总金额异常】,公司id={}，userId={}", companyId, userId, e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getMsg()));
        }
    }
    
    /**
     * 企业是否做过Airwallex授权
     * @param request
     * @param response
     */
    @HttpService(value = "/is/aw/auth")
    public void isAuthorizedByAirwallex(HttpRequest request, HttpResponse response) {
    	AuthRespDTO resp = companyAuthService.queryAuthResultByAirwallex(QueryAuthRequestDTO.builder().companyId(getUserCompanyId(request)).build());
    	ResponseResultUtils.success(response, resp);
    }

    /**
     * 查询企业信息,判断是否为试用版
     * @param request
     * @param response
     */
    @HttpService(value = "/info", method = RequestMethod.GET)
    public void info(HttpRequest request, HttpResponse response) {
        String companyId = getOperationCompanyId(request);
        Map<String,Object> resultMap = Maps.newHashMap();
        try {
            boolean isTrialAndMarketVersion = checkTrailCompany(companyId);
            if (isTrialAndMarketVersion){
                resultMap.put("rechargeAcctHide",true);
                ResponseResultUtils.success(response,resultMap);
                return;
            }
        }catch (Exception e){
            FinhubLogger.error("试用版查询UC异常",e);
        }
        resultMap.put("rechargeAcctHide",false);
        ResponseResultUtils.success(response,resultMap);
    }
}
