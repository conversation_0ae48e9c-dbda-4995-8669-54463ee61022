package com.fenbeitong.fenbeipay.http.controller.redcoupon;

import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponOperationType;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponFlowVO;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponFlow;
import com.fenbeitong.fenbeipay.redcoupon.service.AccountRedcouponService;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.dto.AccountRedcouponFlowReqDTO;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 内网接口
 * @ClassName: InnerAccountRedcouponController
 * @Version: 3.7.0
 */
@HttpService("/internal/redcoupon")
public class InnerAccountRedcouponController {

    @Autowired
    private AccountRedcouponService accountRedcouponService;
    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;


    /**
     * 红包券过期回收
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/time/recall", method = RequestMethod.POST)
    public void timeRecall(HttpRequest request, HttpResponse response) {
        //作废该接口
        return;
//        accountRedcouponService.timeRecall();
    }

    @HttpService(value = "/export/redcoupon/flow", method = RequestMethod.POST)
    public void exportRedcouponFlow(HttpRequest request, HttpResponse response) {
        AccountRedcouponFlowReqDTO redcouponQuery = request.getBodyObject(AccountRedcouponFlowReqDTO.class);
        FinhubLogger.info("【红包券流水导出参数】{}", redcouponQuery.toString());
        ValidateUtils.validate(redcouponQuery);
        Date startTime = redcouponQuery.getStartTime();
        Date endTime = redcouponQuery.getEndTime();
        if (startTime == null || endTime == null) {
            endTime = new Date();
            startTime = DateUtil.changeDateMonth(endTime, -1);
            redcouponQuery.setStartTime(startTime);
            redcouponQuery.setEndTime(endTime);
        }
        redcouponQuery.setOperationTypeList(RedcouponOperationType.WEB_SHOW_TYPE);
        if(null != redcouponQuery.getOperationType()){
            if(RedcouponOperationType.FROZEN_VOUCHER_RECALL.getKey() == redcouponQuery.getOperationType()){
                //分贝券失效返还 分系统和人工
                redcouponQuery.setOperationTypeList(RedcouponOperationType.VOUCHER_RECALL);
                redcouponQuery.setOperationType(null);
            }else if(RedcouponOperationType.STEREO_GRANT.getKey() == redcouponQuery.getOperationType()){
                    //红包券发放 包含 发放，增加额度
                    redcouponQuery.setOperationTypeList(RedcouponOperationType.STEREO_GRANT_ADD);
                    redcouponQuery.setOperationType(null);
                }
        }
        List<AccountRedcouponFlow> accountRedcouponFlowList = accountRedcouponSearchService.queryRedcouponFlowExport(redcouponQuery);
        List<AccountRedcouponFlowVO> accountRedcouponFlowVOS = new ArrayList<>();
        accountRedcouponFlowList.forEach(accountSubFlow -> {
            AccountRedcouponFlowVO redcouponFlowVO = new AccountRedcouponFlowVO();
            BeanUtils.copyProperties(accountSubFlow, redcouponFlowVO);
            BigDecimal fenToYuan = BigDecimalUtils.fenToYuan(redcouponFlowVO.getOperationAmount());
            if (accountSubFlow.getOperationType() == RedcouponOperationType.PUBLIC_CONSUME.getKey() ||
                    accountSubFlow.getOperationType() == RedcouponOperationType.BANK_APPLY.getKey() ||
                    RedcouponOperationType.STEREO_RECALL.getKey() == accountSubFlow.getOperationType() ||
                    RedcouponOperationType.FROZEN_VOUCHER_GRANT.getKey() == accountSubFlow.getOperationType() ||
                    RedcouponOperationType.SYSTEM_TIME_RECALL.getKey() == accountSubFlow.getOperationType()) {
                fenToYuan = fenToYuan.negate();
            }
            redcouponFlowVO.setBalance(BigDecimalUtils.fenToYuan(redcouponFlowVO.getBalance()));
            redcouponFlowVO.setTradeDescription(redcouponFlowVO.getTradeDescription(redcouponFlowVO.getOperationType()));
            redcouponFlowVO.setOperationUserName(redcouponFlowVO.getoperationUserName(redcouponFlowVO.getOperationType(),redcouponFlowVO.getOperationUserName()));
            String operationValue = RedcouponOperationType.getEnum(redcouponFlowVO.getOperationType()).getValue();
            redcouponFlowVO.setOperationDescription(operationValue);
            redcouponFlowVO.setOperationAmount(fenToYuan);
            redcouponFlowVO.setOperationUserPhone(redcouponFlowVO.getOperationUserPhone(redcouponFlowVO.getOperationType(),redcouponFlowVO.getOperationUserPhone()));
            accountRedcouponFlowVOS.add(redcouponFlowVO);
        });
        ResponseResultUtils.success(response, accountRedcouponFlowVOS);
    }
}
