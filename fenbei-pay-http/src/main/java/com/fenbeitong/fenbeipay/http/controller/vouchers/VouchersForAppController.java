package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.StereoMessageCode;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersSortReqDTO;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersOperationFlowService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.vo.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyFbqRuleDTO;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.luastar.swift.base.entity.SwiftHashMap;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 分贝券个人钱包controller
 * @ClassName: VouchersForAppController
 * @Author: zhangga
 * @CreateDate: 2018/12/11 下午6:49
 * @UpdateUser:
 * @UpdateDate: 2018/12/11 下午6:49
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/vouchers/management")
public class VouchersForAppController {
    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    private VouchersOperationFlowService vouchersOperationFlowService;
    @Autowired
    private IVouchersPersonService iVouchersPersonService;

    private static final String USER_ID = "userId";
    private static final String COMPANY_ID = "companyId";

    /**
     * selectAvailableVoucherListByEmployeeId
     *
     * @return void
     * @Description 查询员工可用分贝券
     * @Date 下午4:14 2018/11/28
     * @Param [request, response]
     **/
    @HttpService(value = "/select_available_voucher_list_by_employee_id/v2", method = RequestMethod.POST)
    public void selectAvailableVoucherListByEmployeeId(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        List<VoucherForWalletVO> vouchersPersonList = vouchersPersonService.selectAvailableVoucherListByEmployeeId(currentUserId);
        ResponseResultUtils.success(response, vouchersPersonList);
    }

    @HttpService(value = "/select_available_voucher_list_by_employee_id/v4", method = RequestMethod.POST)
    public void selectAvailableVoucherListByEmployeeIdV4(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        List<VoucherForWalletVO> vouchersPersonList = vouchersPersonService.selectAvailableVoucherListByEmployeeId(currentUserId);
        ResponsePage<VoucherForWalletVO> responsePage = new ResponsePage<>();
        BigDecimal totalBalance = BigDecimal.ZERO;
        for (VoucherForWalletVO vo : vouchersPersonList) {
            totalBalance = totalBalance.add(vo.getBalance());
        }
        String pageNo = request.getParameter("pageNo");
        if (pageNo != null && Integer.valueOf(pageNo) != 1) {
            responsePage.setDataList(new ArrayList<>());
            responsePage.setCondition(totalBalance);
            responsePage.setTotalCount(vouchersPersonList.size());
            ResponseResultUtils.success(response, responsePage);
            return;
        }
        responsePage.setDataList(vouchersPersonList);
        responsePage.setCondition(totalBalance);
        responsePage.setTotalCount(vouchersPersonList.size());
        ResponseResultUtils.success(response, responsePage);
    }

    @HttpService(value = "/select/person/available/vouchers/v4", method = RequestMethod.POST)
    public void selectAvailableVouchersByEmployeeIdV421(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        PageBean pageBean = request.getBodyObject(PageBean.class);
        VouchersResponsePageVO responsePage = vouchersPersonService.selectAvailableVoucherListByPage(currentUserId, pageBean);
        responsePage.setMoreCompany((Boolean)request.getAttribute("isMoreCompany"));
        ResponseResultUtils.success(response, responsePage);
    }


    /**
     * 查询排序分贝券
     * @param request
     * @param response
     */
    @HttpService(value = "/select/person/vouchers/v4", method = RequestMethod.POST)
    public void selectSortVouchersByEmployeeId(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        VouchersSortReqDTO reqDTO = request.getBodyObject(VouchersSortReqDTO.class);
        VouchersResponsePageVO responsePage = vouchersPersonService.selectSortVoucherListByPage(currentUserId, reqDTO);
        Boolean isMoreCompany = (Boolean) request.getAttribute("isMoreCompany");
        boolean moreCompanyVoucher = false;
        if (isMoreCompany){
            moreCompanyVoucher = vouchersPersonService.queryMoreCompanyVouchers(request.getHeader(PayConstant.KEY_NAME_TOKEN), currentUserId);
        }
        Object currentCompanyName = request.getAttribute(BaseController.COMPANY_NAME);
        responsePage.setMoreCompany(moreCompanyVoucher, currentCompanyName != null ? currentCompanyName.toString() : "");
        ResponseResultUtils.success(response, responsePage);
    }

    /**
     * 查询排序分贝券 场景
     * @param request
     * @param response
     */
    @HttpService(value = "/select/person/vouchers/scene", method = RequestMethod.GET)
    public void selectVouchersScene(HttpRequest request, HttpResponse response) {
        VouchersSortReqDTO reqDTO = request.getBodyObject(VouchersSortReqDTO.class);
        List<VoucherSceneVO> list = vouchersPersonService.selectVouchersScene();
        ResponseResultUtils.success(response, list);
    }

    /**
     * 转让分贝券 名称清洗
     * 2022年1月1日(含)之后，类型为同事转让，且名称为同事转让券
     * @param request
     * @param response
     */
    @HttpService(value = "/select/person/vouchers/clearVoucherName", method = RequestMethod.POST)
    public void clearVoucherName(HttpRequest request, HttpResponse response) {
        List<String> vouchersIds = request.getBodyObject(List.class);
        Long count = vouchersPersonService.clearVoucherName(vouchersIds);
        ResponseResultUtils.success(response, count);
    }

    /**
     * selectUnavailableVoucherListByEmployeeId
     *
     * @return void
     * @Description 查询员工不可用分贝券
     * @Date 下午4:14 2018/11/28
     * @Param [request, response]
     **/
    @HttpService(value = "/select_unavailable_voucher_list_by_employee_id/v2", method = RequestMethod.POST)
    public void selectUnavailableVoucherListByEmployeeId(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        List<VoucherForWalletVO> vouchersPersonList = vouchersPersonService.selectUnAvailableVoucherListByEmployeeId(currentUserId);
        ResponseResultUtils.success(response, vouchersPersonList);
    }

    @HttpService(value = "/select/person/unavailable/vouchers/v4", method = RequestMethod.POST)
    public void selectUnavailableVouchersByEmployeeIdV421(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        PageBean pageBean = request.getBodyObject(PageBean.class);
        List<VoucherForWalletVO> responsePage = vouchersPersonService.selectUnavailableVoucherListByPage(currentUserId, pageBean);
        ResponseResultUtils.success(response, responsePage);
    }

    /**
     * selectVoucherFlowByVoucherId
     *
     * @return void
     * @Description 分贝券流水查询
     * @Date 下午4:45 2018/11/28
     * @Param [request, response]
     **/
    @HttpService(value = "/select_voucher_flow_by_voucher_id/v2", method = RequestMethod.POST)
    public void selectVoucherFlowByVoucherId(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        SwiftHashMap<String, Object> bodyMap = request.getBodyMap();
        if (ObjUtils.isNotEmpty(bodyMap)) {
            String voucherId = bodyMap.get("voucherId").toString();
            if (ObjUtils.isNotEmpty(voucherId)) {
                VouchersFlowResponseVO vouchersOperationFlows = vouchersOperationFlowService.selectVouchersOperationFlowByVoucherId(currentUserId, voucherId);
                ResponseResultUtils.success(response, vouchersOperationFlows);
            } else {
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
        } else {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
    }

    @HttpService(value = "/query/user/has/vouchers/v3", method = RequestMethod.GET)
    public void queryIsHaveUsableVouchers(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        String companyId = request.getAttribute(COMPANY_ID).toString();
        boolean hasUsableVouchers = iVouchersPersonService.queryIsHaveUsableVouchers(companyId, currentUserId);
        JSONObject json = new JSONObject();
        json.put("has_usable_vouchers", hasUsableVouchers);
        ResponseResultUtils.success(response, json);
    }

    @HttpService(value = "/query/person/available/voucher/amount/v4", method = RequestMethod.GET)
    public void queryAvailableVoucherAmount(HttpRequest request, HttpResponse response) {
        String currentUserId = request.getAttribute(USER_ID).toString();
        VoucherStatisticsAmountVO totalAmount = vouchersPersonService.queryAvailableVoucherAmount(currentUserId);
        ResponseResultUtils.success(response, totalAmount);
    }


}
