package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.account.person.PersonAccountService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponOperationType;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AccountRedEnvelopeUnFreezeReqDTO;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedEnvelopeService;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersOperationFlowMapper;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcoupon;
import com.fenbeitong.fenbeipay.http.vo.FlowDataUpdateVO;
import com.fenbeitong.fenbeipay.http.vo.PersonAcctTransferVO;
import com.fenbeitong.fenbeipay.http.vo.RedcouponDataVO;
import com.fenbeitong.fenbeipay.http.vo.TransferFlagUpdateVO;
import com.fenbeitong.fenbeipay.redcoupon.db.mapper.AccountRedcouponMapper;
import com.fenbeitong.fenbeipay.redcoupon.service.AccountRedcouponService;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description 数据脚本接口（更新流水）
 * @date 2022-09-20 下午8:13
 */
@HttpService("/internal/data")
public class DataFixController {

    @Autowired
    VouchersOperationFlowMapper vouchersOperationFlowMapper;

    @Autowired
    AccountRedcouponService accountRedcouponService;

    @Autowired
    PersonAccountService personAccountService;

    @Autowired
    AccountRedcouponMapper accountRedcouponMapper;

    @Autowired
    PersonAccountMapper personAccountMapper;

    @Autowired
    IAccountRedEnvelopeService iAccountRedEnvelopeService;

    @HttpService(value = "/flow/update/time", method = RequestMethod.POST)
    public void flowUpdateTime(HttpRequest request, HttpResponse response) {
        FlowDataUpdateVO bodyObject = request.getBodyObject(FlowDataUpdateVO.class);
        switch(bodyObject.getTableType()){
            case "voucher":
                List<String> ids = vouchersOperationFlowMapper.queryIdsByDate(bodyObject.getStartDate(),bodyObject.getEndDate());
                for (String id : ids) {
                    vouchersOperationFlowMapper.updateTimeById(id);
                }
            default:
                break;
        }
        ResponseResultUtils.success(response, true);
    }

    /**
     * 红包券数据升级
     * @param request
     * @param response
     */
    @HttpService(value = "/redcoupon/transfer",method = RequestMethod.POST)
    public void redcouponTransfer(HttpRequest request, HttpResponse response) throws Exception {
        RedcouponDataVO redcouponDataVO = request.getBodyObject(RedcouponDataVO.class);
        if(0==redcouponDataVO.getIsAll()){
            accountRedcouponService.washDataByCompanyId(redcouponDataVO.getCompanyId());
        }else{
            accountRedcouponService.washDataAll();
        }
        ResponseResultUtils.success(response, true);
    }

    /**
     * 分贝币数据升级
     * @param request
     * @param response
     */
    @HttpService(value = "/personacct/transfer",method = RequestMethod.POST)
    public void personAcctTransfer(HttpRequest request, HttpResponse response) throws Exception {
        PersonAcctTransferVO personAcctTransferVO = request.getBodyObject(PersonAcctTransferVO.class);
        if(0==personAcctTransferVO.getIsAll()){
            personAccountService.washDataByEmployeeId(personAcctTransferVO.getEmployeeId());
        }else{
            personAccountService.washDataAll();
        }
        ResponseResultUtils.success(response, true);
    }

    @HttpService(value="/update/transferFlag",method = RequestMethod.POST)
    public void updateTransferFlag(HttpRequest request, HttpResponse response) {
        TransferFlagUpdateVO transferFlagUpdateVO = request.getBodyObject(TransferFlagUpdateVO.class);
        if(1==transferFlagUpdateVO.getType()){
            AccountRedcoupon accountRedcoupon = new AccountRedcoupon();
            accountRedcoupon.setId(Long.valueOf(transferFlagUpdateVO.getId()));
            accountRedcoupon.setUpgradeFlag(0);
            accountRedcouponMapper.updateByPrimaryKeySelective(accountRedcoupon);
        }else{
            PersonAccount personAccount = new PersonAccount();
            personAccount.setId(transferFlagUpdateVO.getId());
            personAccount.setUpgradeFlag(0);
            personAccountMapper.updateByPrimaryKeySelective(personAccount);
        }
        ResponseResultUtils.success(response, true);
    }

    @HttpService(value="/fbq/fix",method = RequestMethod.POST)
    public void fbqFix(HttpRequest request, HttpResponse response) {
        AccountRedEnvelopeUnFreezeReqDTO accountRedEnvelopeUnFreezeReqDTO = request.getBodyObject(AccountRedEnvelopeUnFreezeReqDTO.class);
        accountRedEnvelopeUnFreezeReqDTO.setOperationType(RedcouponOperationType.FROZEN_VOUCHER_RECALL);
        accountRedEnvelopeUnFreezeReqDTO.setFreezenUseType(FreezenUseType.INDIVIDUAL_VOUCHERS);
        iAccountRedEnvelopeService.unFreeze(accountRedEnvelopeUnFreezeReqDTO);
        ResponseResultUtils.success(response, true);
    }
    
    @HttpService(value="/recall/expired/fbb",method = RequestMethod.POST)
    public void recallExpiredFbb(HttpRequest request, HttpResponse response){
    	CompletableFuture.runAsync(() -> 
    		personAccountService.recallAllExpiredFbb()
    	);
    	ResponseResultUtils.success(response, true);
    }
}
