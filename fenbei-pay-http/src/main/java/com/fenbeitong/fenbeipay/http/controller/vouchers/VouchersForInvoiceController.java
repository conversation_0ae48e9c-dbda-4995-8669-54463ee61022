package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.WriteInvoiceStatus;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.WriteInvoiceType;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.voucher.VoucherTaskStatus;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.dto.OperationUserInfoDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTasksRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.api.model.vo.voucher.VouchersInvoiceTaskStatisticsVO;
import com.fenbeitong.fenbeipay.vouchers.vo.VouchersTaskVO;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 分贝券企业web管理controller
 * @ClassName: VouchersForWebController
 * @Author: zhangga
 * @CreateDate: 2018/12/11 下午6:48
 * @UpdateUser:
 * @UpdateDate: 2018/12/11 下午6:48
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/vouchers/invoice")
public class VouchersForInvoiceController extends BaseController {

    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;

    /**
     * @Description: 查询可开票任务列表
     * @methodName: queryCanInvoiceVoucherTasks
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2020/2/17 11:08 AM
     **/
    @HttpService(value = "/query/task/list/v3", method = RequestMethod.POST)
    public void queryCanInvoiceVoucherTasks(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersTasksRequestDTO tasksRequestDTO = request.getBodyObject(VouchersTasksRequestDTO.class);
        tasksRequestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        tasksRequestDTO.setWriteInvoiceType(WriteInvoiceType.INVOICE_ADVANCE.getValue());
        tasksRequestDTO.setWriteInvoiceStatus(WriteInvoiceStatus.UN_INVOICED.getValue());
        tasksRequestDTO.setStatus(VoucherTaskStatus.FINISH.getValue());
        Date billMilepostTime = tasksRequestDTO.getBillMilepostTime();
        if (billMilepostTime == null) {
            billMilepostTime = new Date();
        }
        Date startTime = tasksRequestDTO.getStartTime();
        if (startTime == null) {
            startTime = DateUtils.parseTime("2018-07-01 00:00:00");
        }
        Date endTime = tasksRequestDTO.getEndTime();
        if (endTime == null || endTime.compareTo(billMilepostTime) > 0) {
            endTime = billMilepostTime;
        }
        tasksRequestDTO.setStartTime(startTime);
        tasksRequestDTO.setEndTime(endTime);
        ResponsePage<VouchersTask> data = vouchersTaskHandleService.queryCanInvoiceVoucherTasks(tasksRequestDTO);
        ResponsePage<VouchersTaskVO> responsePage = new ResponsePage<>();
        responsePage.setTotalCount(data.getTotalCount());
        if (data.getTotalCount() > 0) {
            List<VouchersTaskVO> vouchersTaskVOS = new ArrayList<>();
            data.getDataList().forEach(vouchersTask -> {
                VouchersTaskVO vouchersTaskVO = new VouchersTaskVO();
                vouchersTaskVO.encapsulationVO(vouchersTask);
                vouchersTaskVO.setVouchersOperationAmount(vouchersTask.getVouchersOperationAmount().subtract(vouchersTask.getTotalRecoveryAmount()));
                vouchersTaskVOS.add(vouchersTaskVO);
            });
            responsePage.setDataList(vouchersTaskVOS);
        }
        ResponseResultUtils.success(response, responsePage);
    }

    /**
     * @Description: 选择并统计可开票券任务
     * @methodName: selectCanInvoiceVoucherTasks
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2020/2/17 11:08 AM
     **/
    @HttpService(value = "/select/task/v3", method = RequestMethod.POST)
    public void selectCanInvoiceVoucherTasks(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersTasksRequestDTO tasksRequestDTO = request.getBodyObject(VouchersTasksRequestDTO.class);
        List<Long> idList = tasksRequestDTO.getIdList();
        if (!tasksRequestDTO.isSelectAll() && ObjUtils.isEmpty(idList)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (tasksRequestDTO.isSelectAll()) {
            tasksRequestDTO.setIdList(null);
        } else {
            tasksRequestDTO = new VouchersTasksRequestDTO();
            tasksRequestDTO.setIdList(idList);
        }
        tasksRequestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        tasksRequestDTO.setWriteInvoiceType(WriteInvoiceType.INVOICE_ADVANCE.getValue());
        tasksRequestDTO.setWriteInvoiceStatus(WriteInvoiceStatus.UN_INVOICED.getValue());
        tasksRequestDTO.setStatus(VoucherTaskStatus.FINISH.getValue());
        VouchersInvoiceTaskStatisticsVO data = vouchersTaskHandleService.statisticsVoucherTasks(tasksRequestDTO);
        ResponseResultUtils.success(response, data);
    }

    /**
     * @Description: 校验是否有开票
     * @methodName: checkInvoicePrivilege
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2020/2/17 11:08 AM
     **/
    @HttpService(value = "/check/privilege/v3", method = RequestMethod.GET)
    public void checkInvoicePrivilege(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilegeNoMenu(request);
        boolean privilege = checkInvoiceAdvancePrivilege(operationUserInfoDTO.getCompanyId(), WriteInvoiceType.INVOICE_ADVANCE.getValue());
        ResponseResultUtils.success(response, privilege);
    }

}
