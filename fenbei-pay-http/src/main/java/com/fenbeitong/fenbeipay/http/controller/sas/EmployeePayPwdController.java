package com.fenbeitong.fenbeipay.http.controller.sas;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.model.vo.sas.CaptchaVerifyReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.sas.SasSecurityVerifyModeReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.sas.SasSecurityVerifyModeRespVO;
import com.fenbeitong.fenbeipay.api.util.AESUtils;
import com.fenbeitong.fenbeipay.api.util.PwdVerifyUtils;
import com.fenbeitong.fenbeipay.api.util.RSAUtils;
import com.fenbeitong.fenbeipay.awplus.utils.JsonUtil;
import com.fenbeitong.fenbeipay.core.constant.core.UcMessageCode;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserLoginVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.ConfirmPayPwdRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.PayPasswordReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.UpdatePayPasswordReqVo;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasEmployeePayPwdManager;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasPayCaptchaManager;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasSecurityVerifyManager;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.usercenter.api.model.dto.auth.TwoFactorAuthVerityDTO;
import com.fenbeitong.usercenter.api.service.auth.ITwoFactorAuthService;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import com.fenbeitong.fenbeipay.core.service.i18.MessageService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;


/**
 * @Description: 支付密码流程
 * @ClassName: EmployeePayPwdController
 * @Version: 3.7.0
 */
@HttpService("/sas")
public class EmployeePayPwdController extends BaseController {

    private final String CREATE_PAY_PASSWORD_MSG_KEY = "server-fenbeipay-sas-password_set_success";

    private final String UPDATE_PAY_PASSWORD_MSG_KEY = "server-fenbeipay-sas-password_change_success";

    @Value("${pay.password.rsa.privateKey}")
    private String rsaPrivateKey;

    @Value("${pay.password.aes.key}")
    private String aesKey;

    @Value("${pay.password.birthdays}")
    private String birthdays = "";

    @Autowired
    private SasEmployeePayPwdManager sasEmployeePayPwdManager;

    @Autowired
    private SasPayCaptchaManager sasPayCaptchaManager;

    @Autowired
    private DingDingMsgService dingDingMsgService;

    @Autowired
    private ITwoFactorAuthService iTwoFactorAuthService;

    @Autowired
    private SasSecurityVerifyManager sasSecurityVerifyManager;

    /**
     * 获取安全认证方式
     */
    @HttpService(value = "/security/verify/mode", method = RequestMethod.POST)
    public void getSecurityVerifyMode(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserLoginVo userLoginVo = getUserLogin(request);
        SasSecurityVerifyModeReqVO reqVO = request.getBodyObject(SasSecurityVerifyModeReqVO.class);
        ValidateUtils.validate(reqVO);

        String companyId = userLoginVo.getCompany_info().getId();
        String employeeId = userLoginVo.getUser_info().getId();
        SasSecurityVerifyModeRespVO respVO = sasSecurityVerifyManager.getSecurityVerifyMode(reqVO, companyId, employeeId);
        ResponseResultUtils.success(response, respVO);
    }

    /**
     * @Description: 设置支付密码, 新版本已经弃用
     * @Author: wh
     * @Date: 2019/11/9 10:51 AM
     */
    @HttpService(value = "/create/pay/pwd/v3", method = RequestMethod.POST)
    public void createPayPassword(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            FinhubLogger.info("设置支付密码{}", JsonUtils.toJson(userInfo));
            PayPasswordReqVo payPasswordReqVo = request.getBodyObject(PayPasswordReqVo.class);

            sasEmployeePayPwdManager.createPayPassword(payPasswordReqVo, userInfo);

            ResponseResultUtils.success(response, MessageService.me().getMessage(CREATE_PAY_PASSWORD_MSG_KEY));
        } catch (FinPayException e) {
            FinhubLogger.warn("设置支付密码失败:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("设置支付密码系统异常:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * @Description: 确认支付密码, 新版本已经弃用
     * @Author: wh
     * @Date: 2019/11/9 10:51 AM
     */
    @HttpService(value = "/confirm/pay/pwd/v3", method = RequestMethod.POST)
    public void confirmPayPassword(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            FinhubLogger.info("确认支付密码{}", JsonUtils.toJson(userInfo));
            PayPasswordReqVo payPasswordReqVo = request.getBodyObject(PayPasswordReqVo.class);

            ConfirmPayPwdRespVo confirmPayPwdRespVo = sasEmployeePayPwdManager.confirmPayPasswordV4(payPasswordReqVo, userInfo);

            ResponseResultUtils.success(response, confirmPayPwdRespVo);
        } catch (FinPayException e) {
            FinhubLogger.error("确认支付密码失败:参数{}", userInfo, e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("确认支付密码系统异常:参数{}", userInfo, e);
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * @Description: 修改支付密码, 新版本已经弃用
     * @Author: wh
     * @Date: 2019/11/9 10:51 AM
     */
    @HttpService(value = "/update/pay/pwd/v3", method = RequestMethod.POST)
    public void updatePayPassword(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            FinhubLogger.info("修改支付密码{}", JsonUtils.toJson(userInfo));
            UpdatePayPasswordReqVo updatePayPasswordReqVo = request.getBodyObject(UpdatePayPasswordReqVo.class);

            sasEmployeePayPwdManager.updatePayPassword(updatePayPasswordReqVo, userInfo);

            ResponseResultUtils.success(response, MessageService.me().getMessage(UPDATE_PAY_PASSWORD_MSG_KEY));
        } catch (FinPayException e) {
            FinhubLogger.error("修改支付密码失败:FinPay参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }catch (FinhubException e) {
            FinhubLogger.error("修改支付密码失败:finhub参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        }catch (Exception e) {
            FinhubLogger.error("修改支付密码系统异常:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }


    /**
     * @Description: 重置(找回)支付密码, 新版本已经弃用
     * @Author: wh
     * @Date: 2019/11/9 10:51 AM
     */
    @HttpService(value = "/reset/pay/pwd/v3", method = RequestMethod.POST)
    public void resetPayPassword(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        CompanyInfoVO userCompanyInfo = getUserCompanyInfo(request);
        String clientType = getClientType(request);
        try {
            FinhubLogger.info("重置(找回)支付密码 userInfo={}, companyInfo={}, clientType={}", JsonUtils.toJson(userInfo), JsonUtil.toJson(userCompanyInfo), clientType);
            PayPasswordReqVo payPasswordReqVo = request.getBodyObject(PayPasswordReqVo.class);

            sasEmployeePayPwdManager.resetPayPassword(payPasswordReqVo, userInfo, userCompanyInfo, clientType);

            ResponseResultUtils.success(response, MessageService.me().getMessage(UPDATE_PAY_PASSWORD_MSG_KEY));
        } catch (FinPayException e) {
            FinhubLogger.error("找回支付密码失败:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("找回支付密码系统异常:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * @Description: 设置支付密码
     * @Author: liyi
     * @Date: 2022/8/1 10:51 AM
     */
    @HttpService(value = "/create/pay/pwd/v4", method = RequestMethod.POST)
    public void createPayPasswordV4(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            FinhubLogger.info("设置支付密码{}", JsonUtils.toJson(userInfo));
            PayPasswordReqVo payPasswordReqVo = request.getBodyObject(PayPasswordReqVo.class);
            String employeePwd = payPasswordReqVo.getEmployeePwd();
            if (!StringUtils.isEmpty(employeePwd)) {
                payPasswordReqVo.setEmployeePwd(handlerRSAPwd(employeePwd, true, userInfo.getPhone(), birthdays));
            }

            sasEmployeePayPwdManager.createPayPassword(payPasswordReqVo, userInfo);

            ResponseResultUtils.success(response, MessageService.me().getMessage(CREATE_PAY_PASSWORD_MSG_KEY));
        } catch (FinPayException e) {
            FinhubLogger.warn("设置支付密码失败:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("设置支付密码失败:参数{}", JsonUtil.toJson(userInfo), e);
            throw e;
        } catch (Exception e) {
            FinhubLogger.error("设置支付密码系统异常:参数{}", JsonUtil.toJson(userInfo), e);
            dingDingMsgService.sendMsg("用户设置支付密码系统异常，" + e.toString());
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * @Description: 确认支付密码
     * @Author: liyi
     * @Date: 2022/8/1 10:51 AM
     */
    @HttpService(value = "/confirm/pay/pwd/v4", method = RequestMethod.POST)
    public void confirmPayPasswordV4(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            FinhubLogger.info("确认支付密码{}", JsonUtils.toJson(userInfo));
            PayPasswordReqVo payPasswordReqVo = request.getBodyObject(PayPasswordReqVo.class);
            String employeePwd = payPasswordReqVo.getEmployeePwd();
            if (!StringUtils.isEmpty(employeePwd)) {
                payPasswordReqVo.setEmployeePwd(handlerRSAPwd(employeePwd));
            }

            ConfirmPayPwdRespVo confirmPayPwdRespVo=sasEmployeePayPwdManager.confirmPayPasswordV4(payPasswordReqVo, userInfo);

            ResponseResultUtils.success(response, confirmPayPwdRespVo);
        } catch (FinPayException e) {
            FinhubLogger.error("确认支付密码失败:参数{}", JsonUtils.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("设置支付密码失败:参数{}", JsonUtil.toJson(userInfo), e);
            throw e;
        }  catch (Exception e) {
            FinhubLogger.error("确认支付密码系统异常:参数{}", JsonUtils.toJson(userInfo), e);
            dingDingMsgService.sendMsg("用户确认支付密码系统异常，" + e.toString());
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * @Description: 确认支付密码，用于用户在新设备登录时验证支付密码
     * @Author: liyi
     * @Date: 2022/8/1 10:51 AM
     */
    @HttpService(value = "/login/confirm/pay/pwd/v4", method = RequestMethod.POST)
    public void loginConfirmPayPasswordV4(HttpRequest request, HttpResponse response) {
        PayPasswordReqVo payPasswordReqVo = request.getBodyObject(PayPasswordReqVo.class);
        if (StringUtils.isEmpty(payPasswordReqVo.getBusinessId())) {
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
        try {
            FinhubLogger.info("确认支付密码loginConfirmPayPasswordV4 businessId:{}", payPasswordReqVo.getBusinessId());
            TwoFactorAuthVerityDTO twoFactorAuthVerityDTO = iTwoFactorAuthService.checked(payPasswordReqVo.getBusinessId());

            String employeePwd = payPasswordReqVo.getEmployeePwd();
            if (!StringUtils.isEmpty(employeePwd)) {
                payPasswordReqVo.setEmployeePwd(handlerRSAPwd(employeePwd));
            }

            List<String> employeeIdList = twoFactorAuthVerityDTO.getEmployeeIdList();
            if (CollectionUtils.isEmpty(employeeIdList)) {
                employeeIdList = Lists.newArrayList(twoFactorAuthVerityDTO.getEmployeeId());
            }
            ConfirmPayPwdRespVo confirmPayPwdRespVo = sasEmployeePayPwdManager.confirmPayPasswordBusinessIdV4(payPasswordReqVo, employeeIdList);

            FinhubLogger.info("确认支付密码loginConfirmPayPasswordV4 businessId:{},res:{}", payPasswordReqVo.getBusinessId(), JSONObject.toJSONString(confirmPayPwdRespVo));
            ResponseResultUtils.success(response, confirmPayPwdRespVo);
        } catch (FinPayException e) {
            FinhubLogger.error("确认支付密码失败:参数{}", JsonUtils.toJson(payPasswordReqVo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("确认支付密码失败:参数{}", JsonUtil.toJson(payPasswordReqVo), e);
            throw e;
        }  catch (Exception e) {
            FinhubLogger.error("确认支付密码系统异常:参数{}", JsonUtils.toJson(payPasswordReqVo), e);
            dingDingMsgService.sendMsg("用户确认支付密码系统异常，" + e.toString());
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * @Description: 校验密码设置的是否合法
     * @Author: liyi
     * @Date: 2022/8/1 10:51 AM
     */
    @HttpService(value = "/verify/pay/pwd/legal", method = RequestMethod.POST)
    public void verifyPasswordIsLegal(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        String clientType = getClientType(request);
        try {
            FinhubLogger.info("校验密码设置的是否合法 userInfo={}, clientType={}", JsonUtils.toJson(userInfo), clientType);
            PayPasswordReqVo payPasswordReqVo = request.getBodyObject(PayPasswordReqVo.class);
            String employeePwd = payPasswordReqVo.getEmployeePwd();
            if (StringUtils.isEmpty(employeePwd)) {
                throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_IS_SIMPLE);
            }
            handlerRSAPwd(employeePwd, true, userInfo.getPhone(), birthdays);

            ResponseResultUtils.success(response, MessageService.me().getMessage("server-fenbeipay-compliant_pwd_setting"));
        } catch (FinPayException e) {
            FinhubLogger.warn("校验密码设置的是否合法:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.warn("校验密码设置的是否合法:参数{}", JsonUtil.toJson(userInfo), e);
            throw e;
        } catch (Exception e) {
            FinhubLogger.warn("校验密码设置的是否合法:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * @Description: 修改支付密码
     * @Author: liyi
     * @Date: 2022/8/1 10:51 AM
     */
    @HttpService(value = "/update/pay/pwd/v4", method = RequestMethod.POST)
    public void updatePayPasswordV4(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            FinhubLogger.info("修改支付密码{}", JsonUtils.toJson(userInfo));
            UpdatePayPasswordReqVo updatePayPasswordReqVo = request.getBodyObject(UpdatePayPasswordReqVo.class);
            String employeePwd = updatePayPasswordReqVo.getEmployeePwd();
            if (!StringUtils.isEmpty(employeePwd)) {
                updatePayPasswordReqVo.setEmployeePwd(handlerRSAPwd(employeePwd));
            }
            String employeeNewPwd = updatePayPasswordReqVo.getEmployeeNewPwd();
            if (!StringUtils.isEmpty(employeeNewPwd)) {
                updatePayPasswordReqVo.setEmployeeNewPwd(handlerRSAPwd(employeeNewPwd, true, userInfo.getPhone(), birthdays));
            }

            sasEmployeePayPwdManager.updatePayPassword(updatePayPasswordReqVo, userInfo);

            ResponseResultUtils.success(response, MessageService.me().getMessage(UPDATE_PAY_PASSWORD_MSG_KEY));
        } catch (FinPayException e) {
            FinhubLogger.error("修改支付密码失败:FinPay参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("修改支付密码失败:finhub参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("修改支付密码系统异常:参数{}", JsonUtil.toJson(userInfo), e);
            dingDingMsgService.sendMsg("用户修改支付密码系统异常，" + e.toString());
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * @Description: 重置(找回)支付密码
     * @Author: liyi
     * @Date: 2022/8/1 10:51 AM
     */
    @HttpService(value = "/reset/pay/pwd/v4", method = RequestMethod.POST)
    public void resetPayPasswordV4(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        CompanyInfoVO userCompanyInfo = getUserCompanyInfo(request);
        String clientType = getClientType(request);
        try {
            FinhubLogger.info("重置(找回)支付密码 userInfo={}, companyInfo={}, clientType={}", JsonUtils.toJson(userInfo), JsonUtil.toJson(userCompanyInfo), clientType);
            PayPasswordReqVo payPasswordReqVo = request.getBodyObject(PayPasswordReqVo.class);
            String employeePwd = payPasswordReqVo.getEmployeePwd();
            if (!StringUtils.isEmpty(employeePwd)) {
                payPasswordReqVo.setEmployeePwd(handlerRSAPwd(employeePwd, true, userInfo.getPhone(), birthdays));
            }

            sasEmployeePayPwdManager.resetPayPassword(payPasswordReqVo, userInfo, userCompanyInfo, clientType);

            ResponseResultUtils.success(response, MessageService.me().getMessage(UPDATE_PAY_PASSWORD_MSG_KEY));
        } catch (FinPayException e) {
            if(e.ignoreReport()){
                FinhubLogger.warn("找回支付密码失败:参数{}", JsonUtil.toJson(userInfo), e);
            }else{
                FinhubLogger.error("找回支付密码失败:参数{}", JsonUtil.toJson(userInfo), e);
            }
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (FinhubException e) {
            FinhubLogger.error("找回支付密码失败:参数{}", JsonUtil.toJson(userInfo), e);
            throw e;
        } catch (Exception e) {
            FinhubLogger.error("找回支付密码系统异常:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }


    /**
     * RSA解密处理，返回AES加密密文
     * @param pwd
     * @return
     */
    private String handlerRSAPwd(String pwd) {
        try {
            //由于老流程是APP端AES加密，服务端将AES密文加盐，并SHA512加密生成密文并存储
            //所以为了兼容老数据，APP端将密码RSA公钥加密后，服务端私钥解密，然后AES加密，再将AES密文加盐，并SHA512加密生成密文并存储
            byte[] decrypt = RSAUtils.decrypt(Base64.getDecoder().decode(pwd), rsaPrivateKey);
            String realPwd = new String(decrypt);
            return AESUtils.EnCode(realPwd, aesKey);
        } catch (FinPayException e) {
            throw e;
        } catch (Exception e) {
            FinhubLogger.error("【支付密码RSA处理失败】pwd 参数={}:{}", pwd, e);
            dingDingMsgService.sendMsg("【支付密码RSA处理失败】" + e.toString());
            throw new FinPayException(GlobalResponseCode.PASSWORD_DECRYPT_FAIL);
        }
    }

    /**
     * RSA解密处理，返回AES加密密文
     * @param pwd RSA加密密文
     * @param isValid 是否校验密码是否合法
     * @param phone 用户手机号
     * @param birthdays 用户生日串，由于有些用户没有设置生日，为了应对云闪付APP检测，这里写死几个，提供给检测方检测，生产环境不校验
     * @return
     */
    private String handlerRSAPwd(String pwd, boolean isValid, String phone, String birthdays) {
        try {
            //由于老流程是APP端AES加密，服务端将AES密文加盐，并SHA512加密生成密文并存储
            //所以为了兼容老数据，APP端将密码RSA公钥加密后，服务端私钥解密，然后AES加密，再将AES密文加盐，并SHA512加密生成密文并存储
            byte[] decrypt = RSAUtils.decrypt(Base64.getDecoder().decode(pwd), rsaPrivateKey);
            String realPwd = new String(decrypt);

            if (isValid) {
                if (PwdVerifyUtils.simpleNumCheck(realPwd)) {
                    throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_SIMPLE);
                }
                if (PwdVerifyUtils.phoneInfoCheck(realPwd, phone) || birthdays.contains(realPwd)) {
                    throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_HAS_USER_INFO);
                }
            }

            return AESUtils.EnCode(realPwd, aesKey);
        } catch (FinPayException e) {
            throw e;
        } catch (Exception e) {
            FinhubLogger.error("【支付密码RSA处理失败】pwd 参数={}:{}", pwd, e);
            dingDingMsgService.sendMsg("【支付密码RSA处理失败】" + e.toString());
            throw new FinPayException(GlobalResponseCode.PASSWORD_DECRYPT_FAIL);
        }
    }

    /**
     * 发送短信验证码
     * @param request
     * @param response
     */
    @HttpService(value = "/captcha/send",method = RequestMethod.POST)
    public void sendCaptcha(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            JSONObject params = request.getBodyObject(JSONObject.class);
            ResponseResultUtils.success(response, sasPayCaptchaManager.sendCaptcha(userInfo.getPhone(), params.getString("payBalance"), params.getString("businessCase")));
        } catch (FinPayException e) {
            FinhubLogger.error("付款发送短信验证码处理失败:参数{}", JsonUtils.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("付款发送短信验证码系统异常:参数{}", JsonUtils.toJson(userInfo), e);
            dingDingMsgService.sendMsg("付款发送短信验证码系统异常，" + e.toString());
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

    /**
     * 验证短信验证码
     * @param request
     * @param response
     */
    @HttpService(value = "/captcha/verify",method = RequestMethod.POST)
    public void verifyCaptcha(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            CaptchaVerifyReqVO requestVO = request.getBodyObject(CaptchaVerifyReqVO.class);
            ResponseResultUtils.success(response, sasPayCaptchaManager.verifyCaptcha(requestVO, userInfo));
        } catch (FinPayException e) {
            FinhubLogger.error("付款发送短信验证码处理失败:参数{}", JsonUtils.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("付款发送短信验证码系统异常:参数{}", JsonUtils.toJson(userInfo), e);
            dingDingMsgService.sendMsg("付款发送短信验证码系统异常，" + e.toString());
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }
}
