package com.fenbeitong.fenbeipay.http.controller.transfer.req;

import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName BalanceAdjustBatchReq
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/12/12 4:07 下午
 * @Version 1.0
 **/
@Data
public class BalanceAdjustBatchReq implements Serializable {

    List<BalanceAdjustItem> accountList;

    /**
     * 平台方
     */
    private String bankName;

    /** 操作人id **/
    private String operateId;

    /** 操作人名称 **/
    private String operateName;

    /** 备注 **/
    private String remark;

    public void check() {

        if (accountList == null || accountList.isEmpty()) {
            throw new FinhubException(1, "被调整企业列表不能为空");
        }
        for (BalanceAdjustItem item : accountList) {
            if (StringUtils.isBlank(item.getAccountId())) {
                throw new FinhubException(1, "被调整企业账户id不能为空accountId");
            }
            if (StringUtils.isBlank(item.getCompanyId())) {
                throw new FinhubException(1, "被调整企业的id不能为空companyId");
            }
            if (item.getAdjustAmount() == null || item.getAdjustAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new FinhubException(1, "调整金额必须为正数，单位为分adjustAmount");
            }
        }
        if (StringUtils.isBlank(bankName) || BankNameEnum.UN_KNOW.equals(BankNameEnum.getBankEnum(bankName))) {
            throw new FinhubException(1, "被调整企业平台方不能为空bankName");
        }
        if (StringUtils.isBlank(operateName)) {
            throw new FinhubException(1, "操作人名称不能为空operateName");
        }
        if (StringUtils.isBlank(remark)) {
            throw new FinhubException(1, "备注不能为空remark");
        }
    }
}
