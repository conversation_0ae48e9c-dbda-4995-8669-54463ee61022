package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.account.currency.GrantTaskDetailService;
import com.fenbeitong.fenbeipay.account.currency.RecallTaskDetailService;
import com.fenbeitong.fenbeipay.account.currency.TaskService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.core.model.vo.account.*;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * 分贝币发放相关接口
 * Created by wh on 2018/01/01.
 */
@HttpService("/internal/currency")
public class CurrencyController {

    @Autowired
    private TaskService taskService;

    @Autowired
    private GrantTaskDetailService grantTaskDetailService;

    @Autowired
    private RecallTaskDetailService recallTaskDetailService;

    /**
     * createGrantTask
     *
     * @return void
     * @Description 创建分贝币发放任务
     * @Date 下午8:23 2018/12/20
     * @Param [request, response]
     **/
    @HttpService(value = "/create/grant/task/v3", method = RequestMethod.POST)
    public void createGrantTask(HttpRequest request, HttpResponse response) {
        FbbGrantRecallTasksVo tasksDTO = request.getBodyObject(FbbGrantRecallTasksVo.class);
        FbbGrantRecallTasksRespVo grantTask = taskService.createGrantTask(tasksDTO);
        ResponseResultUtils.success(response, grantTask);
    }

    /**
     * createGrantTask
     *
     * @return void
     * @Description 执行发放分贝币
     * @Date 下午8:23 2018/12/20
     * @Param [request, response]
     **/
    @HttpService(value = "/execute/grant/task/v3", method = RequestMethod.POST)
    public void executeGrantTask(HttpRequest request, HttpResponse response) {
        FbbExecuteGrantTasksVo grantTasksVo = request.getBodyObject(FbbExecuteGrantTasksVo.class);
        FbbTasksBaseRespVo grantTask = taskService.executeGrantTask(grantTasksVo);
        taskService.startGrantTask(grantTask.getTasksId());
        ResponseResultUtils.success(response, grantTask);
    }

    /**
     * createGrantTask
     *
     * @return void
     * @Description 任务列表分页展示
     * @Date 下午8:23 2018/12/20
     * @Param [request, response]
     **/
    @HttpService(value = "/query/list/task/v3", method = RequestMethod.POST)
    public void taskList(HttpRequest request, HttpResponse response) {
        FbbTasksListVo fbbTasksListVo = request.getBodyObject(FbbTasksListVo.class);
        ResponsePage<FbbTasksListRespVo> page = taskService.taskList(fbbTasksListVo);
        ResponseResultUtils.success(response,page );
    }


    /**
     * taskDetail
     *
     * @return void
     * @Description 任务详情展示
     * @Date 下午8:23 2018/12/20
     * @Param [request, response]
     **/
    @HttpService(value = "/query/detail/task/v3", method = RequestMethod.POST)
    public void taskDetail(HttpRequest request, HttpResponse response) {
        FbbTasksBaseVo fbbTasksListVo = request.getBodyObject(FbbTasksBaseVo.class);
        FbbTasksDetailRespVo fbbTasksDetailRespVo = taskService.taskDetail(fbbTasksListVo);
        ResponseResultUtils.success(response, fbbTasksDetailRespVo);
    }


    /**
     * createGrantTask
     *
     * @return void
     * @Description 批量添加发放的用户
     * @Date 下午8:23 2019/01/4
     * @Param [request, response]
     **/
    @HttpService(value = "/batchadd/grant/employee/v3", method = RequestMethod.POST)
    public void batchAdd(HttpRequest request, HttpResponse response) {
        FbbGrantUserTasksVo fbGrantRecallTasksVo = request.getBodyObject(FbbGrantUserTasksVo.class);
        FbbGrantUserTasksRespVo fbGrantRecallTasksRespVo = grantTaskDetailService.batchAdd(fbGrantRecallTasksVo);
        ResponseResultUtils.success(response, fbGrantRecallTasksRespVo);
    }


    /**
     * createGrantTask
     *
     * @return void
     * @Description 分页查询操作的用户
     * @Date 下午8:23 2019/01/4
     * @Param [request, response]
     **/
    @HttpService(value = "/batchquery/employee/v3", method = RequestMethod.POST)
    public void employeesList(HttpRequest request, HttpResponse response) {
        FbbTaskEmployeesListVo fbbTaskEmployeesListVo = request.getBodyObject(FbbTaskEmployeesListVo.class);
        ResponsePage<FbbTaskEmployeesListResqVo> page = taskService.employeesList(fbbTaskEmployeesListVo);
        ResponseResultUtils.success(response, page);
    }


    ///////////recall//////////////

    /**
     * createGrantTask
     *
     * @return void
     * @Description 创建分贝币撤回任务
     * @Date 下午8:23 2018/12/20
     * @Param [request, response]
     **/
    @HttpService(value = "/create/recall/task/v3", method = RequestMethod.POST)
    public void createRecallTask(HttpRequest request, HttpResponse response) {
        FbbRecallTasksVo tasksDTO = request.getBodyObject(FbbRecallTasksVo.class);
        FbbTasksBaseRespVo grantTask = taskService.createRecallTask(tasksDTO);
        ResponseResultUtils.success(response, grantTask);
    }


    /**
     * createGrantTask
     *
     * @return void
     * @Description 批量添加撤回的用户信息
     * @Date 下午8:23 2018/12/20
     * @Param [request, response]
     **/
    @HttpService(value = "/batch_add/recall/task/v3", method = RequestMethod.POST)
    public void batchAddRecallTask(HttpRequest request, HttpResponse response) {
        FbbRecallTEmployeesLisVo tasksDTO = request.getBodyObject(FbbRecallTEmployeesLisVo.class);
        FbbGrantUserTasksRespVo grantTask = recallTaskDetailService.batchAddRecallTask(tasksDTO);
        ResponseResultUtils.success(response, grantTask);
    }


    /**
     * createGrantTask
     *
     * @return void
     * @Description 执行撤回操作
     * @Date 下午8:23 2018/12/20
     * @Param [request, response]
     **/
    @HttpService(value = "/execute/recall/task/v3", method = RequestMethod.POST)
    public void executeRecallTask(HttpRequest request, HttpResponse response) {
        FbbExecuteGrantTasksVo tasksDTO = request.getBodyObject(FbbExecuteGrantTasksVo.class);
        FbbTasksBaseRespVo grantTask = taskService.executeRecallTask(tasksDTO);
        taskService.startTask(grantTask.getTasksId());
        ResponseResultUtils.success(response, grantTask);
    }

    /**
     * createGrantTask
     *
     * @return void
     * @Description 根据订单id查询FBB的发放和撤回
     * @Date 下午8:23 2019/01/21
     * @Param [request, response]
     **/
    @HttpService(value = "/query/order_id_grant_recall/v3", method = RequestMethod.POST)
    public void queryGrantRecallByOrderId(HttpRequest request, HttpResponse response) {
        FbbOrderBaseVo fbbOrderBaseVo = request.getBodyObject(FbbOrderBaseVo.class);
        List<FbbOrderListRespVo> grantTask = grantTaskDetailService.queryGrantRecallByOrderId(fbbOrderBaseVo);
        ResponseResultUtils.success(response, grantTask);
    }

    /**
     * checkTaskIsOvertime
     *
     * @return void
     * @Description 检查任务是否超时
     * @Date 下午8:51 2018/12/24
     * @Param [request, response]
     **/
    @HttpService(value = "/checktask_is_overtime/v3")
    public void checkTaskIsOvertime(HttpRequest request, HttpResponse response) {
        taskService.checkTaskIsOvertime();
    }

}
