package com.fenbeitong.fenbeipay.http.controller.acct;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctFlowSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.BankAcctSearchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.BankAcctFlowSearchRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.resp.BankAcctSearchRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IBankAcctService;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName BankAcctControllerTest
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/4/26
 **/
@HttpService("/internal/bankAcct/test")
public class BankAcctControllerTest {

    @Autowired
    private IBankAcctService iBankAcctService;


    @HttpService(value = "/queryBankAcctListByPage", method = RequestMethod.POST)
    public void queryBankAcctListByPage(HttpRequest request, HttpResponse response) {
        BankAcctSearchReqDTO queryReq = request.getBodyObject(BankAcctSearchReqDTO.class);
        ResponsePage<BankAcctSearchRespDTO> page = iBankAcctService.queryBankAcctListByPage(queryReq);
        ResponseResultUtils.success(response, page);
    }

    @HttpService(value = "/queryBankAcctFlowListByPage", method = RequestMethod.POST)
    public void queryBankAcctFlowListByPage(HttpRequest request, HttpResponse response) {
        BankAcctFlowSearchReqDTO queryReq = request.getBodyObject(BankAcctFlowSearchReqDTO.class);
        ResponsePage<BankAcctFlowSearchRespDTO> page = iBankAcctService.queryBankAcctFlowListByPage(queryReq);
        ResponseResultUtils.success(response, page);
    }

}
