package com.fenbeitong.fenbeipay.http.controller.extract;

import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayShowPageReqDTO;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description:
 * @Author: guogx
 * @Date: 2022/8/4 下午4:37
 */
@HttpService("/auth/internal/fbp/web/extractDay")
public class AuthExtractDayImprotController extends ExtractDayImprotController {

    @HttpService(value = "/getListPage", method = RequestMethod.POST)
    @Override
    public void getListPage(HttpRequest request, HttpResponse response) {
        String companyId = verifyCompanyId(request);
        //底层接口未校验companyId，故此处仅在传参不为空时校验权限
        ExtractDayShowPageReqDTO queryReq = request.getBodyObject(ExtractDayShowPageReqDTO.class);
        if(StringUtils.isNotBlank(queryReq.getCompanyId()) && !queryReq.getCompanyId().equals(companyId)){
            throw new FinPayException(GlobalResponseCode.TOKEN_EXPIRE);
        }
        super.getListPage(request, response);
    }

}
