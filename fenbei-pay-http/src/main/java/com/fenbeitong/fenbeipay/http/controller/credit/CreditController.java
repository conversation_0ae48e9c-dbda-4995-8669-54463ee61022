package com.fenbeitong.fenbeipay.http.controller.credit;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonCreditService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctAdjustCreditReqDTO;
import com.fenbeitong.fenbeipay.http.controller.acct.AcctAbstractController;
import com.fenbeitong.fenbeipay.http.vo.RedcouponDataVO;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * 授信额度调整
 * @author: zhaoxu
 * @date: 2022-06-02 13:57:55
 */
@HttpService("/internal/credit")
public class CreditController extends AcctAbstractController {

    @Autowired
    private UAcctCommonCreditService uAcctCommonCreditService;

    /**
     * 定时任务执行恢复临时额度
     * @author: zhaoxu
     * @date: 2022-06-08 12:32:16
     */
    @HttpService(value = "/recover")
    public void companyCreditOverview(HttpRequest request, HttpResponse response) {
        uAcctCommonCreditService.recoverTempAmountTask();
    }

    @HttpService(value = "/adjust",method = RequestMethod.POST)
    public void adjust(HttpRequest request, HttpResponse response) {
        List<AcctAdjustCreditReqDTO> acctAdjustCreditReqDTOS = request.getBodyArray(AcctAdjustCreditReqDTO.class);
        if (!CollectionUtils.isEmpty(acctAdjustCreditReqDTOS)){
            for (AcctAdjustCreditReqDTO ac : acctAdjustCreditReqDTOS) {
                uAcctCommonCreditService.adjust(ac);
            }
        }

    }
}
