package com.fenbeitong.fenbeipay.http.controller.sas;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.core.model.vo.sas.SearchVirtualAccountFlowReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.SearchVirtualAccountFlowRespVo;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.AccountVirtualCardFlowService;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> WEB 端
 * @date 2022/2/24 3:15 下午
 * @Description 查询虚拟卡子帐户流水接口
 *
 * 此接口已经迁移到biz-card 工程中，此方法废弃 2022-07-29  zhangzhaozhao 迁出
 */
@HttpService("/sas/pay")
public class WebAccountFlowController extends BaseController {

    @Autowired
    private AccountVirtualCardFlowService virtualCardFlowService;

    @HttpService(value = "web/company/virtualCard/flow",method = RequestMethod.POST)
    public void searchAccountFlow(HttpRequest request, HttpResponse response){
        String companyId=getUserCompanyId(request);
        SearchVirtualAccountFlowReqVo reqVo= request.getBodyObject(SearchVirtualAccountFlowReqVo.class);
        reqVo.setCompanyId(companyId);
        reqVo.checkReqWeb();
        ResponsePage<SearchVirtualAccountFlowRespVo> data =  virtualCardFlowService.searchVirtualAccountFlowByCompany(reqVo);
        ResponseResultUtils.success(response, data);
    }
}
