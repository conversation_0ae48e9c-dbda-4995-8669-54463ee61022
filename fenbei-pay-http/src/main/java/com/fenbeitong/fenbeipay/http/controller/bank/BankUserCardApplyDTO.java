package com.fenbeitong.fenbeipay.http.controller.bank;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class BankUserCardApplyDTO implements Serializable {

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 银行名称
     * @see com.fenbeitong.finhub.common.constant.BankNameEnum
     */
    private String bankName;


    /**
     * 银行账户Id
     */
    private String bankAccountNo;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal operationAmount;

    /**
     * 当前操作业务ID：消费为订单ID,转入转出为子账户类型key,提现为提现单号
     */
    private String bizNo;

    /**
     * 交易说明(内部说明)
     */
    private String operationDescription;

    /**
     * 操作人名称 uc查不到时取传的
     */
    private String operationUserName;

}
