package com.fenbeitong.fenbeipay.http.controller.newaccount;

import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessCredit;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessDebit;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctIndividualCredit;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctIndividualDebit;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessDebitFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctIndividualCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctIndividualDebitFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessCreditService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessDebitService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctIndividualCreditService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctIndividualDebitService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSearchListReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSearchListRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountFlowVO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountGeneralFlowVO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountSubFlowVO;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.common.base.export.AccountQuery;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoDetailService;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoService;
import com.fenbeitong.fenbeipay.na.service.AccountSubFlowService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description: java类作用描述
 * @ClassName: AccountInnerController
 * @Author: zhangga
 * @CreateDate: 2019/4/9 2:52 PM
 * @UpdateUser:
 * @UpdateDate: 2019/4/9 2:52 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/internal/new/account")
public class AccountInnerController {

    @Autowired
    private AccountGeneralFlowService accountGeneralFlowService;
    @Autowired
    private AccountSubFlowService accountSubFlowService;

    @Autowired
    private AccountReceiveInfoService accountReceiveInfoService;

    @Autowired
    private UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    private UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    private UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    private UAcctIndividualDebitService uAcctIndividualDebitService;

    @Autowired
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Autowired
    private AcctBusinessDebitFlowService acctBusinessDebitFlowService;

    @Autowired
    private AcctIndividualCreditFlowService acctIndividualCreditFlowService;

    @Autowired
    private AcctIndividualDebitFlowService acctIndividualDebitFlowService;

    @Autowired
    private AccountReceiveInfoDetailService accountReceiveInfoDetailService;

    /**
     * @Description: 导出主账户流水
     * @methodName: createVouchersType
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/4/9 2:56 PM
     **/
    @HttpService(value = "/export/general/flow", method = RequestMethod.POST)
    public void exportGeneralFlow(HttpRequest request, HttpResponse response) {
        AccountQuery accountQuery = request.getBodyObject(AccountQuery.class);
        FinhubLogger.info("【新账户操作流水导出参数】{}", accountQuery.toString());
        ValidateUtils.validate(accountQuery);
        List<AccountGeneralFlowVO> accountGeneralFlowList = accountGeneralFlowService.queryExport(accountQuery, new PageBean(accountQuery.getPageNo(), accountQuery.getPageSize()), accountQuery.getStartTime(), accountQuery.getEndTime());
        accountGeneralFlowList.forEach(accountGeneralFlowVO -> {
            accountGeneralFlowVO.setOperationAmount(BigDecimalUtils.fenToYuan(accountGeneralFlowVO.getOperationAmount()));
        });
        ResponseResultUtils.success(response, accountGeneralFlowList);
    }

    /**
     * @Description: 导出子账户流水
     * @methodName: exportSubFlow
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/4/9 2:58 PM
     **/
    @HttpService(value = "/export/sub/flow", method = RequestMethod.POST)
    public void exportSubFlow(HttpRequest request, HttpResponse response) {
        AccountQuery accountQuery = request.getBodyObject(AccountQuery.class);
        FinhubLogger.info("【新账户操作流水导出参数】{}", accountQuery.toString());
        ValidateUtils.validate(accountQuery);
        Integer accountSubType = accountQuery.getAccountSubType();
        if (null == accountSubType) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "[accountSubType]参数不能为空");
        }
        List<AccountSubFlowVO> accountSubFlowVOS = new ArrayList<>();
        Integer accountModel = accountQuery.getAccountModel();
        if (Objects.isNull(accountModel) || (!FundAccountModelType.isCredit(accountModel) && !FundAccountModelType.isRecharge(accountModel))) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "[accountSubType]参数不能为空");
        }
        List<AccountFlowVO> voList = new ArrayList<>();
        if (FundAccountSubType.isBusinessAccount(accountSubType) &&
                FundAccountModelType.isRecharge(accountModel)) {
            AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(accountQuery.getCompanyId(), BankNameEnum.FBT.getCode(), accountQuery.getCompanyId());
            if (Objects.nonNull(acctBusinessDebit)) {
                accountQuery.setAccountId(acctBusinessDebit.getAccountId());
                voList = acctBusinessDebitFlowService.queryExport(accountQuery, new PageBean(accountQuery.getPageNo(), accountQuery.getPageSize()), accountQuery.getStartTime(), accountQuery.getEndTime());
            }
        } else if (FundAccountSubType.isBusinessAccount(accountSubType) &&
                FundAccountModelType.isCredit(accountModel)) {
            AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(accountQuery.getCompanyId(),BankNameEnum.FBT.getCode(), accountQuery.getCompanyId());
            if (Objects.nonNull(acctBusinessCredit)) {
                accountQuery.setAccountId(acctBusinessCredit.getAccountId());
                voList = acctBusinessCreditFlowService.queryExport(accountQuery, new PageBean(accountQuery.getPageNo(), accountQuery.getPageSize()), accountQuery.getStartTime(), accountQuery.getEndTime());
            }
        } else if (FundAccountSubType.isIndividualAccount(accountSubType) &&
                FundAccountModelType.isRecharge(accountModel)) {
            AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(accountQuery.getCompanyId(),BankNameEnum.FBT.getCode(), accountQuery.getCompanyId());
            if (Objects.nonNull(acctIndividualDebit)) {
                accountQuery.setAccountId(acctIndividualDebit.getAccountId());
                voList = acctIndividualDebitFlowService.queryExport(accountQuery, new PageBean(accountQuery.getPageNo(), accountQuery.getPageSize()), accountQuery.getStartTime(), accountQuery.getEndTime());
            }
        } else if (FundAccountSubType.isIndividualAccount(accountSubType) &&
                FundAccountModelType.isCredit(accountModel)) {
            AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(accountQuery.getCompanyId(),BankNameEnum.FBT.getCode(), accountQuery.getCompanyId());
            if (Objects.nonNull(acctIndividualCredit)) {
                accountQuery.setAccountId(acctIndividualCredit.getAccountId());
                voList = acctIndividualCreditFlowService.queryExport(accountQuery, new PageBean(accountQuery.getPageNo(), accountQuery.getPageSize()), accountQuery.getStartTime(), accountQuery.getEndTime());
            }
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            voList.stream().forEach(e -> {
                AccountSubFlowVO accountSubFlowVO = new AccountSubFlowVO();
                BeanUtils.copyProperties(e, accountSubFlowVO);
                accountSubFlowVO.setOperationAmount(BigDecimalUtils.fenToYuan(accountSubFlowVO.getOperationAmount()));
                BigDecimal balance = e.getBalance();
                accountSubFlowVO.setBalance(ObjUtils.isNull(balance) ? null : balance.toString());
                if((Objects.nonNull(e.getOperationType()) && (e.getOperationType() == 41 || e.getOperationType() == 42)) && Objects.nonNull(e.getOrderType())) {
                    CategoryTypeEnum categoryTypeEnum = CategoryTypeEnum.valueOf(e.getOrderType());
                    accountSubFlowVO.setOrderTypeName(Objects.nonNull(categoryTypeEnum)?categoryTypeEnum.getName():"");
                }else{
                    accountSubFlowVO.setBizNo("");
                    accountSubFlowVO.setOrderTypeName("");
                }
                accountSubFlowVOS.add(accountSubFlowVO);
            });
        }
        ResponseResultUtils.success(response, accountSubFlowVOS);
    }

    @HttpService(value = "/export/employeeInfoList", method = RequestMethod.POST)
    public void getEmployeeInfoList(HttpRequest request, HttpResponse response) {

        AccountSearchListReqDTO reqDTO = request.getBodyObject(AccountSearchListReqDTO.class);
        ResponsePage<AccountSearchListRespDTO> responsePage = accountReceiveInfoService.getEmployeeInfoListExport(reqDTO);
        ResponseResultUtils.success(response, responsePage.getDataList());
    }


    @HttpService(value = "/employee/account/syncByCompany", method = RequestMethod.POST)
    public void syncEmployeeAccountByCompanyId(HttpRequest request, HttpResponse response) {
        String body = request.getBody();
        if (ObjUtils.isBlank(body)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        accountReceiveInfoDetailService.syncEmployeeAccountByCompanyId(body);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/employee/account/syncByUser", method = RequestMethod.POST)
    public void syncEmployeeAccountByUserId(HttpRequest request, HttpResponse response) {
        String body = request.getBody();
        if (ObjUtils.isBlank(body)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(),GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        accountReceiveInfoDetailService.syncEmployeeAccountByUserId(body);
        ResponseResultUtils.success(response);
    }

}
