package com.fenbeitong.fenbeipay.http.controller.newaccount;


import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctConsumeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOperationRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubOperationReqRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctAppPayService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * (内部系统调用的HTTP服务)收银台收款Http服务Controller
 */

@HttpService("/internal/new/account/sub")
public class InnerAccountSubController {

    @Autowired
    private IAcctAppPayService acctAppPayService;
    /**
     * (HTTP Internal+RPC)创建支付交易流水号(也称为：预支付)
     * 场景OC系统调用，生成支付交易流水号，如果因公支付，企业支付部分需要提前扣除，也在此扣除
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/consume/v1",method = RequestMethod.POST)
    public void  consume(HttpRequest request, HttpResponse response){
        AccountSubOperationReqRPCDTO subOperationReqRPCDTO= request.getBodyObject(AccountSubOperationReqRPCDTO.class);
        FinhubLogger.info("【新账户系统，消费】cashWithdrawalCDTO 参数：{}", subOperationReqRPCDTO);
        if(StringUtils.isEmpty(subOperationReqRPCDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AcctOperationRespDTO respRPCDTO=new AcctOperationRespDTO();
        try {
            //校验字段
            ValidateUtils.validate(subOperationReqRPCDTO);
            AcctConsumeReqDTO acctConsumeReqDTO = new AcctConsumeReqDTO();
            BeanUtils.copyProperties(acctConsumeReqDTO,subOperationReqRPCDTO);
            respRPCDTO = acctAppPayService.consume(acctConsumeReqDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】consume 参数：{}账户余额不足", subOperationReqRPCDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户系统异常】consume 参数：{}", subOperationReqRPCDTO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】consume 参数：{}", subOperationReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】consume 参数：{}", subOperationReqRPCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        ResponseResultUtils.success(response,respRPCDTO);
    }




}
