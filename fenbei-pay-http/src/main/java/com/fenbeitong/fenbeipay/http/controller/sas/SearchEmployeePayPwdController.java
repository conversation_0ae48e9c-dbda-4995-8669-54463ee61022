package com.fenbeitong.fenbeipay.http.controller.sas;

import com.fenbeitong.fenbeipay.awplus.utils.JsonUtil;
import com.fenbeitong.fenbeipay.core.constant.core.UcMessageCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryPayPwdRespVo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.fenbeipay.sas.pwd.query.manager.SearchEmployeePayPwdManager;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * @Description: 支付密码流程
 * @ClassName: EmployeePayPwdController
 * @Version: 3.7.0
 */
@HttpService("/sas/query")
public class SearchEmployeePayPwdController extends BaseController {

    @Autowired
    private SearchEmployeePayPwdManager searchEmployeePayPwdManager;

    /**
     * @Description: 查询是否存在支付密码
     * @Author: wh
     * @Date: 2019/11/9 10:51 AM
     */
    @HttpService(value = "/exist/pay/pwd")
    public void queryExistPayPassword(HttpRequest request, HttpResponse response) {
        //获取用户信息
        UserInfoVO userInfo = getUserInfo(request);
        try {
            FinhubLogger.info("查询是否存在支付密码{}", JsonUtils.toJson(userInfo));
            QueryPayPwdRespVo queryPayPwdRespVo= searchEmployeePayPwdManager.queryExistPayPassword(
                    userInfo.getId());

            ResponseResultUtils.success(response, queryPayPwdRespVo);
        } catch (FinPayException e) {
            FinhubLogger.error("查询是否存在支付密码:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("查询是否存在支付密码系统异常:参数{}", JsonUtil.toJson(userInfo), e);
            throw new FinhubException(UcMessageCode.EXCEPTION);
        }
    }

}
