package com.fenbeitong.fenbeipay.http.controller.newaccount;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditMflowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessDebitMflowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctIndividualCreditMflowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctIndividualDebitMflowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountInstructionRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountGeneralFlowVO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountGeneralInfoVO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponInfoVO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountSubInfoVO;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportDTO;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportQuery;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.service.file.BaseTemplateService;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.acctdech.unit.service.AcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.http.controller.newaccount.dto.AccountGeneralFlowReqDTO;
import com.fenbeitong.fenbeipay.http.controller.vouchers.BaseController;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralFlowService;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountGeneralService;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountSubService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountModelType;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeSimpleInfoContract;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.BANK_ACCOUNT_NO;
import static com.fenbeitong.fenbeipay.core.service.file.StaticPageType.ACCOUNT_EFFECTIVE_INSTRUCTIONS;

/**
 * @Description: java类作用描述
 * @ClassName: AccountGeneralController
 * @Author: zhangga
 * @CreateDate: 2019/3/18 3:00 PM
 * @UpdateUser:
 * @UpdateDate: 2019/3/18 3:00 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/fbp/na/account_general")
public class AccountGeneralController extends BaseController {

    @Autowired
    private UAccountGeneralService uAccountGeneralService;

    @Autowired
    private UAccountSubService uAccountSubService;

    @Autowired
    private AccountGeneralFlowService accountGeneralFlowService;

    @Autowired
    private EmployeeService employeeService;


    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    private UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    private UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    private UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    private UAcctIndividualDebitService uAcctIndividualDebitService;

    @Autowired
    private AcctIndividualDebitMflowService acctIndividualDebitMflowService;

    @Autowired
    private AcctIndividualCreditMflowService acctIndividualCreditMflowService;

    @Autowired
    private AcctBusinessDebitMflowService acctBusinessDebitMflowService;

    @Autowired
    private AcctBusinessCreditMflowService acctBusinessCreditMflowService;

    @Autowired
    private AcctCompanyGatewayService acctCompanyGatewayService;

    @Autowired
    protected BaseTemplateService baseTemplateService;

    @Value("${export.task.host}")
    private String exportTaskHost;
    @Value("${export.query.host}")
    private String exportQueryHost;

    @HttpService(value = "/info", method = RequestMethod.GET)
    public void accountGeneralInfo(HttpRequest request, HttpResponse response) {
//        checkPrivilege(request);
        String companyId = request.getAttribute(COMPANY_ID).toString();
//        AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyId(companyId);
        List<AccountGeneral> accountGenerals = uAccountGeneralService.findByCompanyIdAndBankName(companyId, BankNameEnum.FBT.getCode());
        if (CollectionUtils.isEmpty(accountGenerals)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountGeneral accountGeneral = accountGenerals.get(0);
//        List<AccountGeneralFlowVO> voList = accountGeneralFlowService.getByPageBeanVO(companyId, new PageBean(1, 6));
        List<AccountGeneralFlowVO> voList = accountGeneralFlowService.getByAcctGeneralIdPageBeanVO(accountGeneral.getAccountGeneralId(), new PageBean(1, 6));
        //公司下的所有子账户，不区分激活、启用状态
//        List<AccountSub> accountSubList = uAccountSubService.findByCompanyId(companyId);

        List<AcctBusinessCredit> businessCredits = uAcctBusinessCreditService.findByCompanyIdAndBankName(companyId, BankNameEnum.FBT.getCode());
        List<AcctBusinessDebit> businessDebits = uAcctBusinessDebitService.findActivityAccountByCompanyIdAndBankName(companyId, BankNameEnum.FBT.getCode(), null);
        List<AcctIndividualCredit> individualCredits = uAcctIndividualCreditService.findByCompanyIdAndBankName(companyId, BankNameEnum.FBT.getCode());
        List<AcctIndividualDebit> individualDebits = uAcctIndividualDebitService.findCompanyIdAndBankName(companyId, BankNameEnum.FBT.getCode());
        //增加红包券余额
        AccountRedcouponInfoVO redcouponInfoVO = accountRedcouponSearchService.queryAccountCouponInfoVO(companyId);
        List<AccountSubInfoVO> accountSubVoList = new ArrayList<>();
        AccountSubInfoVO vo;
//        for (AccountSub accountSub : accountSubList) {
//            if (FundAccountSubType.isEnterpriseAccount(accountSub.getAccountSubType())) {
//                continue;
//            }
//            vo = new AccountSubInfoVO();
//            //企业账户设置红包券
//            if (FundAccountSubType.isBusinessAccount(accountSub.getAccountSubType())) {
//                vo.setHasRedcoupon(redcouponInfoVO != null);
//                vo.setAccountRedcouponInfo(redcouponInfoVO);
//            }
//            BeanUtils.copyProperties(accountSub, vo);
//            accountSubVoList.add(vo);
//        }
        if (CollectionUtils.isNotEmpty(businessCredits)) {
            for (AcctBusinessCredit accountSub : businessCredits) {
                vo = new AccountSubInfoVO();
                //企业账户设置红包券
                vo.setHasRedcoupon(redcouponInfoVO != null);
                vo.setAccountRedcouponInfo(redcouponInfoVO);
                BeanUtils.copyProperties(accountSub, vo);
                vo.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                vo.setAccountModel(FundAccountModelType.CREDIT.getKey());
                accountSubVoList.add(vo);
            }
        }
        if (CollectionUtils.isNotEmpty(businessDebits)) {
            for (AcctBusinessDebit accountSub : businessDebits) {
                vo = new AccountSubInfoVO();
                vo.setHasRedcoupon(redcouponInfoVO != null);
                vo.setAccountRedcouponInfo(redcouponInfoVO);
                BeanUtils.copyProperties(accountSub, vo);
                vo.setAccountSubType(FundAccountSubType.BUSINESS_ACCOUNT.getKey());
                vo.setAccountModel(FundAccountModelType.RECHARGE.getKey());
                accountSubVoList.add(vo);
            }
        }
        if (CollectionUtils.isNotEmpty(individualCredits)) {
            for (AcctIndividualCredit accountSub : individualCredits) {
                vo = new AccountSubInfoVO();
                BeanUtils.copyProperties(accountSub, vo);
                vo.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                vo.setAccountModel(FundAccountModelType.CREDIT.getKey());
                accountSubVoList.add(vo);
            }
        }
        if (CollectionUtils.isNotEmpty(individualDebits)) {
            for (AcctIndividualDebit accountSub : individualDebits) {
                vo = new AccountSubInfoVO();
                BeanUtils.copyProperties(accountSub, vo);
                vo.setAccountSubType(FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
                vo.setAccountModel(FundAccountModelType.RECHARGE.getKey());
                accountSubVoList.add(vo);
            }
        }
//        String effectiveInstruction = uAccountSubService.accountEffectiveInstruction(companyId);
        String effectiveInstruction = accountEffectiveInstruction(companyId);
        AccountGeneralInfoVO accountGeneralInfoVO = new AccountGeneralInfoVO();
        BeanUtils.copyProperties(accountGeneral, accountGeneralInfoVO);
        accountGeneralInfoVO.setEffectiveInstruction(effectiveInstruction);
        accountGeneralInfoVO.setHasRedcoupon(redcouponInfoVO != null);
        accountGeneralInfoVO.setAccountRedcouponInfo(redcouponInfoVO);
        accountGeneralInfoVO.setAccountGeneralFlowList(voList);
        accountGeneralInfoVO.setAccountSubList(accountSubVoList);
        accountGeneralInfoVO.setHasRedcoupon(redcouponInfoVO != null);
        ResponseResultUtils.success(response, accountGeneralInfoVO);
    }

    @HttpService(value = "/balance", method = RequestMethod.GET)
    public void queryBalance(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String companyId = request.getAttribute(COMPANY_ID).toString();
//        AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyId(companyId);
        List<AccountGeneral> accountGenerals = uAccountGeneralService.findByCompanyIdAndBankName(companyId, BankNameEnum.FBT.getCode());
        if (CollectionUtils.isEmpty(accountGenerals)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountGeneral accountGeneral = accountGenerals.get(0);
        if (ObjUtils.isEmpty(accountGeneral)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountGeneralInfoVO accountGeneralInfoVO = new AccountGeneralInfoVO();
        BeanUtils.copyProperties(accountGeneral, accountGeneralInfoVO);
        ResponseResultUtils.success(response, accountGeneralInfoVO);
    }

    @HttpService(value = "/flow", method = RequestMethod.POST)
    public void queryFlow(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String companyId = getOperationCompanyId(request);
        List<AccountGeneral> accountGenerals = uAccountGeneralService.findByCompanyIdAndBankName(companyId, BankNameEnum.FBT.getCode());
        if (CollectionUtils.isEmpty(accountGenerals)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountGeneral accountGeneral = accountGenerals.get(0);
        AccountGeneralFlowReqDTO dto = request.getBodyObject(AccountGeneralFlowReqDTO.class);
        Date startTime = null;
        Date endTime = null;
        PageBean pageBean = new PageBean();
        AccountGeneralFlow requestParam = new AccountGeneralFlow();
        if (ObjUtils.isNotEmpty(dto)) {
            startTime = dto.getStartTime();
            endTime = dto.getEndTime();
            pageBean.setPageNo(dto.getPageNo());
            pageBean.setPageSize(dto.getPageSize());
            BeanUtils.copyProperties(dto, requestParam);
        }
        requestParam.setCompanyId(companyId);
        requestParam.setAccountGeneralId(accountGeneral.getAccountGeneralId());
        //be
        ResponsePage<AccountGeneralFlowVO> operationFlows = accountGeneralFlowService.queryPage(requestParam, pageBean, startTime, endTime);
        ResponseResultUtils.success(response, operationFlows);
    }

    @HttpService(value = "/flow/export", method = RequestMethod.POST)
    public void exportFlow(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        AccountGeneralFlowReqDTO dto = request.getBodyObject(AccountGeneralFlowReqDTO.class);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        String operationUserId = request.getAttribute(USER_ID).toString();
        String operationUserName = request.getAttribute(USER_COMPANY_NAME).toString();
        List<AccountGeneral> accountGenerals = uAccountGeneralService.findByCompanyIdAndBankName(companyId, BankNameEnum.FBT.getCode());
        if (CollectionUtils.isEmpty(accountGenerals)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountGeneral accountGeneral = accountGenerals.get(0);
//        Date startTime = dto.getStartTime();
//        Date endTime = dto.getEndTime();
        ExportQuery exportQuery = dto.getExportQuery();
        if (exportQuery == null) {
            FinhubLogger.error("【导出报表】参数错误，exportQuery：{}", exportQuery.toString());
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        //判断是否超过一年
//        Date startTimeAfter1Year = DateUtil.changeDateYear(startTime, 1);
//        if (endTime.compareTo(startTimeAfter1Year) > 0) {
//            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_EXPORT_OUT_TIME);
//        }
        ExportDTO exportDTO = new ExportDTO();
        String exportQueryUrl = exportQueryHost + "/internal/new/account/export/general/flow";
        List<String> list = new ArrayList<>();
        EmployeeSimpleInfoContract employeeSimpleInfo = employeeService.getEmployeeSimpleInfo(operationUserId, companyId);
        if (null != employeeSimpleInfo && ObjUtils.isNotEmpty(employeeSimpleInfo.getOrg_unit_list())) {
            employeeSimpleInfo.getOrg_unit_list().forEach(orgUnitListBean -> {
                list.add(orgUnitListBean.getId());
            });
        }
        JSONObject query = dto.getQuery(companyId, accountGeneral.getAccountGeneralId());
        String parentIds = StringUtils.join(list, ",");
        String taskName = "总账户流水导出_" + DateUtils.format(new Date(), "yyyyMMdd_HHmmss");
        exportDTO.dtoBuild(query, exportQueryUrl, taskName, operationUserId, operationUserName, parentIds, exportDTO.getAccountColumns(), exportQuery);
        String jsonString = JSONObject.toJSONString(exportDTO);
        String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/task", jsonString);
        FinhubLogger.info("【导出报表】返回参数：{}", postBody);
        Object taskId = JSONObject.parseObject(postBody).getJSONObject("data").get("taskId");
        ResponseResultUtils.success(response, taskId);
    }

    private String accountEffectiveInstruction(String companyId) {

        try {
            List<AcctBusinessCreditMflow> businessCreditMflows = acctBusinessCreditMflowService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
            List<AcctBusinessDebitMflow> businessDebitMflows = acctBusinessDebitMflowService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
            List<AcctIndividualCreditMflow> individualCreditMflows = acctIndividualCreditMflowService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
            List<AcctIndividualDebitMflow> individualDebitMflows = acctIndividualDebitMflowService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
            if (CollectionUtils.isEmpty(businessCreditMflows) &&
                    CollectionUtils.isEmpty(businessDebitMflows) &&
                    CollectionUtils.isEmpty(individualCreditMflows) &&
                    CollectionUtils.isEmpty(individualDebitMflows)) {
                return null;
            }
            int num = 0;
            Date date = null;
            if (CollectionUtils.isNotEmpty(businessCreditMflows)) {
                if (!DateUtil.changeDateDay(businessCreditMflows.get(0).getCreateTime(), 30).before(new Date())) {
                    num++;
                    date = businessCreditMflows.get(0).getCreateTime();
                }
            }
            if (CollectionUtils.isNotEmpty(businessDebitMflows)) {
                if (!DateUtil.changeDateDay(businessDebitMflows.get(0).getCreateTime(), 30).before(new Date())) {
                    num++;
                    if (date.before(businessDebitMflows.get(0).getCreateTime())) {
                        date = businessDebitMflows.get(0).getCreateTime();
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(individualCreditMflows)) {
                if (!DateUtil.changeDateDay(individualCreditMflows.get(0).getCreateTime(), 30).before(new Date())) {
                    num++;
                    if (date.before(individualCreditMflows.get(0).getCreateTime())) {
                        date = individualCreditMflows.get(0).getCreateTime();
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(individualDebitMflows)) {
                if (!DateUtil.changeDateDay(individualDebitMflows.get(0).getCreateTime(), 30).before(new Date())) {
                    num++;
                    if (date.before(individualDebitMflows.get(0).getCreateTime())) {
                        date = individualDebitMflows.get(0).getCreateTime();
                    }
                }
            }
            if (num == 0) {
                return null;
            }
            AcctComGwByBankReqDTO acctComGwByBankReqDTO = new AcctComGwByBankReqDTO();
            acctComGwByBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
            acctComGwByBankReqDTO.setCompanyId(companyId);
            acctComGwByBankReqDTO.setBankAccountNo(companyId);
            List<AcctCommonBaseDTO> actCommonByBank = acctCompanyGatewayService.findActCommonByBank(acctComGwByBankReqDTO);
            if (CollectionUtils.isEmpty(actCommonByBank)) {
                return null;
            }
            String templateFile = ACCOUNT_EFFECTIVE_INSTRUCTIONS.getPage();
            String instruction = baseTemplateService.renderTemplate(templateFile, null);
            SimpleDateFormat format = new SimpleDateFormat("yyyy年MM月dd日HH:mm");
            String time = format.format(date);
            String businessAccount = "";
            String personAccount = "";
            for (AcctCommonBaseDTO acctCommonBaseDTO : actCommonByBank) {
                if (FundAccountSubType.isBusinessAccount(acctCommonBaseDTO.getAccountSubType())) {
                    businessAccount = FundAccountModelType.getNameFromCode(acctCommonBaseDTO.getAccountModel());
                }
                if (FundAccountSubType.isIndividualAccount(acctCommonBaseDTO.getAccountSubType())) {
                    personAccount = FundAccountModelType.getNameFromCode(acctCommonBaseDTO.getAccountModel());
                }
            }
            instruction = String.format(instruction, time, businessAccount, personAccount);
            AccountInstructionRespDTO respDTO = JsonUtils.toObj(instruction, AccountInstructionRespDTO.class);
            return respDTO.getTitle();
        } catch (Exception e) {
            FinhubLogger.error("accountEffectiveInstruction error:{}", e);
        }
        return null;
    }
}
