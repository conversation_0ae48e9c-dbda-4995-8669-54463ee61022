package com.fenbeitong.fenbeipay.http.controller.cashier;


import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierMultipleCreateTradeRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierMultipleCreateTradeRPCVo;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierMultipleOrderSettlementService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.CASHIER_CRONTAB_HEAD;

/**
 * 一订单多次交易+多订单同时交易 
 * 收银台收款Http服务Controller
 * RPC服务请参考fenbei-pay-api
 * @version  2020年07月09日15:50:35
 * <AUTHOR>
 * @since v4.2.0
 */

@HttpService("/internal/cashier/pay/multiple")
public class InnerCashierOrderMultipleSettlementController extends BaseController {

    @Autowired
    private CashierOrderSettlementService cashierOrderSettlementService;


    @Autowired
    private CashierMultipleOrderSettlementService cashierMultipleOrderSettlementService;


    /**
     * (HTTP Internal+RPC)创建支付交易流水号(也称为：预支付)
     * 场景OC系统调用，生成支付交易流水号，如果因公支付，企业支付部分需要提前扣除，也在此扣除
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/createordertrade/v1",method = RequestMethod.POST)
    public void  createOrderTrade(HttpRequest request, HttpResponse response){
        CashierMultipleCreateTradeRPCVo cashierMultipleCreateTradeRPCVo = request.getBodyObject(CashierMultipleCreateTradeRPCVo.class);
        if(StringUtils.isEmpty(cashierMultipleCreateTradeRPCVo.getEmployeeId())||StringUtils.isEmpty(cashierMultipleCreateTradeRPCVo.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        CashierMultipleCreateTradeRPCDTO orderMultiplePayTradeAndSaas;
        try {
             orderMultiplePayTradeAndSaas = cashierMultipleOrderSettlementService.createOrderMultiplePayTradeAndSaas(cashierMultipleCreateTradeRPCVo);
        } catch (Exception e) {
            String msgError = "【收银台】一笔订单多次支付创建支付异常:交易单号：" + cashierMultipleCreateTradeRPCVo.getFbTradeId() + "订单号:" + cashierMultipleCreateTradeRPCVo.getFbOrderId() + e.getMessage();
            FinhubLogger.error(msgError, e);
            throw e;
        }
        //通知
        CompletableFuture.runAsync(() -> {
            cashierOrderSettlementService.checkPayStatusAndCallBiz(orderMultiplePayTradeAndSaas.getFbOrderId(),orderMultiplePayTradeAndSaas.getFbTradeId(), orderMultiplePayTradeAndSaas.getEmployeeId());
        });
        ResponseResultUtils.success(response,orderMultiplePayTradeAndSaas);
    }

    /**
     * 时间程序，拉取三方退款结果，更新退款状态，并通知场景方
     * @param request
     * @param response
     */
    @HttpService(value = "/cronpullthirdrefund/v2",method = RequestMethod.POST)
    public void queryThirdPay(HttpRequest request, HttpResponse response){
        FinhubLogger.info(CASHIER_CRONTAB_HEAD+"拉取退款结果，更新退款状态，并通知场景方...Start.....");
        int count = cashierMultipleOrderSettlementService.cronThirdRefundResult();
        ResponseResultUtils.success(response,count);
    }


}