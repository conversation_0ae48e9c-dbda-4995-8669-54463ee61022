package com.fenbeitong.fenbeipay.http.controller.extract;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.BankUserCardFlowQueryReqDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.BankUserCardSearchReqDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.BankUserCardFlowRespDTO;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.resp.BankUserCardInfoRespDTO;
import com.fenbeitong.acctperson.api.service.search.IBankUserCardSearchService;
import com.fenbeitong.dech.api.model.dto.cgb.CgbQueryAccountBalanceReqDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbQueryAccountBalanceRespDTO;
import com.fenbeitong.dech.api.service.ICgbVirtualCardService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyCardService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.PersonAcctEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyType;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardBalanceCheckDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardBalanceCheckReloadDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankSearchCardDetailRespDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctVcardDiffProcessService;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.BankCardApplyFlowManger;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyCard;
import com.fenbeitong.fenbeipay.dto.bank.BankCardApplyFlow;
import com.fenbeitong.fenbeipay.dto.bank.BankCardApplyFlowQuery;
import com.fenbeitong.fenbeipay.dto.extract.AcctPersonBankCheck;
import com.fenbeitong.fenbeipay.dto.extract.AcctPersonExtractDay;
import com.fenbeitong.fenbeipay.extract.db.mapper.AcctPersonBankCheckMapper;
import com.fenbeitong.fenbeipay.extract.db.mapper.AcctPersonExtractDayMapper;
import com.fenbeitong.fenbeipay.extract.vo.PersonAcctExtractBalanceVO;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAcctDirectAcctTypeEnum;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.google.common.collect.Lists;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @Description:
 * @Author: guogx
 * @Date: 2022/10/28 下午2:56
 */
@HttpService("/internal/person/acct")
public class PersonAcctExtractController {


    @Resource
    private IBankCardSearchService bankCardSearchService;

    @Resource
    private AcctCompanyCardService acctCompanyCardService;

    @Resource
    private BankCardApplyFlowManger bankCardApplyFlowManger;

    @Resource
    private AcctPersonExtractDayMapper acctPersonExtractDayMapper;

    @Resource
    private AcctPersonBankCheckMapper acctPersonBankCheckMapper;

    @Resource
    private IBankUserCardSearchService bankUserCardSearchService;

    @Resource
    private ICgbVirtualCardService cgbVirtualCardService;

    @Resource
    private IAcctVcardDiffProcessService iAcctVcardDiffProcessService;


    @HttpService("/extract/day/execute")
    public void dayExecute(HttpRequest request, HttpResponse response) {
        long start = System.currentTimeMillis();
        String jobConfig = request.getParameter("jobConfig");
        BankCardBalanceCheckDTO bankCardBalanceCheckDTO = buildPersonAcctDayExtractDTO(jobConfig);
        FinhubLogger.info("PersonAcctExtractController#dayExecute#start#req:{}",JSON.toJSON(bankCardBalanceCheckDTO));

        //获取虚拟卡账户
        List<BankSearchCardDetailRespDTO> bankCardList = bankCardSearchService.queryVirtualCardList(bankCardBalanceCheckDTO);
        if(CollectionUtils.isEmpty(bankCardList)){
            FinhubLogger.info("PersonAcctExtractController#dayExecute#start#账户信息不存在,无需对账:{}",JSON.toJSON(bankCardBalanceCheckDTO));
            return;
        }

        //当天跑前一天的日切数据
        Date billTimeStart = bankCardBalanceCheckDTO.getBillTimeFrom();
        Date billTimeEnd = bankCardBalanceCheckDTO.getBillTimeTo();
        FinhubLogger.info("PersonAcctExtractController#dayExecute#billTime:{}-{},card.size:{}",DateUtil.formatDateTime(billTimeStart),DateUtil.formatDateTime(billTimeEnd), bankCardList.size());

        //保存备用金账户日切数据
        for(BankSearchCardDetailRespDTO bankCard : bankCardList){
            PersonAcctExtractBalanceVO bankCardExtractBalanceVO = personAcctDoHandleDay(bankCard, billTimeStart, billTimeEnd);
            AcctCompanyCard acctCompanyCard = acctCompanyCardService.findByCompanyIdAndBank(bankCard.getCompanyId(), bankCard.getBankName(), bankCard.getCompanyBankAccountNo());
            savePersonAcctExtractDay(bankCard, acctCompanyCard, bankCardExtractBalanceVO);
            //获取个人资金账户
            BankUserCardInfoRespDTO bankUserCard = getBankUserCard(bankCard);
            if(bankUserCard != null){
                //保存个人资金账户日切流水
                PersonAcctExtractBalanceVO userCardExtractBalanceVO = personAcctDoHandleDay(bankUserCard, billTimeStart, billTimeEnd);
                savePersonAcctExtractDay(bankCard, acctCompanyCard, userCardExtractBalanceVO);
            }
        }
        long end = System.currentTimeMillis();
        FinhubLogger.info("PersonAcctExtractController#dayExecute#end#billTime:{},duration:{}",DateUtil.formatDate(billTimeStart),end-start);
    }

    private BankUserCardInfoRespDTO getBankUserCard(BankSearchCardDetailRespDTO bankCard) {
        BankUserCardInfoRespDTO bankUserCardInfoRespDTO = null;
        try{
            BankUserCardSearchReqDTO bankUserCardSearchReqDTO = new BankUserCardSearchReqDTO();
            bankUserCardSearchReqDTO.setBankName(bankCard.getBankName());
            bankUserCardSearchReqDTO.setEmployeeId(bankCard.getEmployeeId());
            bankUserCardSearchReqDTO.setCompanyId(bankCard.getCompanyId());
            FinhubLogger.info("PersonAcctExtractController#getBankUserCard-queryBankUserCardInfo-in#req:{}",JSON.toJSON(bankUserCardSearchReqDTO));
            if (StringUtils.isNotBlank(bankCard.getCompanyId())){
                bankUserCardInfoRespDTO = bankUserCardSearchService.queryBankUserCardInfo(bankUserCardSearchReqDTO);
                FinhubLogger.info("PersonAcctExtractController#getBankUserCard-queryBankUserCardInfo-in#resp:{}",JSON.toJSON(bankUserCardInfoRespDTO));
            } else {
                FinhubLogger.warn("PersonAcctExtractController#getBankUserCard-queryBankUserCardInfo-in#卡companyId为空#req:{}",JSON.toJSON(bankCard));
            }

        }catch (Exception e){
            FinhubLogger.warn("PersonAcctExtractController#getBankUserCard-queryBankUserCardInfo-in#获取个人资金账户失败#req:{}",JSON.toJSON(bankCard), e);
        }
        return bankUserCardInfoRespDTO;
    }

    private List<BankUserCardFlowRespDTO> queryBankUserCardFlowList(BankUserCardInfoRespDTO bankUserCard, Date startTime, Date endTime) {
        List<BankUserCardFlowRespDTO>  bankUserCardFlowList = Lists.newArrayList();
        try{
            BankUserCardFlowQueryReqDTO bankUserCardFlowQueryReqDTO = new BankUserCardFlowQueryReqDTO();
            bankUserCardFlowQueryReqDTO.setEmployeeId(bankUserCard.getEmployeeId());
            bankUserCardFlowQueryReqDTO.setBankName(bankUserCard.getBankName());
            bankUserCardFlowQueryReqDTO.setUserCardNo(bankUserCard.getUserCardNo());
            bankUserCardFlowQueryReqDTO.setCreateTimeGeFrom(startTime);
            bankUserCardFlowQueryReqDTO.setCreateTimeLeTo(endTime);
            FinhubLogger.info("PersonAcctExtractController#queryBankUserCardFlowList-in#req:{}", JSON.toJSON(bankUserCardFlowQueryReqDTO));
            bankUserCardFlowList = bankUserCardSearchService.queryBankUserCardFlowList(bankUserCardFlowQueryReqDTO);
            if(CollectionUtils.isNotEmpty(bankUserCardFlowList)){
                FinhubLogger.info("PersonAcctExtractController#queryBankUserCardFlowList-in#resp.count:{}", bankUserCardFlowList.size());
            }
        }catch (Exception e){
            FinhubLogger.error("PersonAcctExtractController#queryBankUserCardFlowList-in#获取个人资金账户流水失败#req:{}",JSON.toJSON(bankUserCard), e);
        }
        return bankUserCardFlowList;
    }

    private PersonAcctExtractBalanceVO personAcctDoHandleDay(BankSearchCardDetailRespDTO bankCard, Date startTime, Date endTime) {
        PersonAcctExtractBalanceVO personAcctExtractBalanceVO = new PersonAcctExtractBalanceVO();

        //期初期末余额
        BigDecimal dayBeginBalance = BigDecimal.ZERO;
        BigDecimal dayFinalBalance = BigDecimal.ZERO;

        //当日收入
        BigDecimal dayIncome = BigDecimal.ZERO;
        //当日支出
        BigDecimal dayExpenditure = BigDecimal.ZERO;
        BankCardApplyFlowQuery query = new BankCardApplyFlowQuery();
        query.setBankName(BankNameEnum.CGB.getCode());
        query.setEmployeeId(bankCard.getEmployeeId());
        query.setCreateTimeGeFrom(startTime);
        query.setCreateTimeLeTo(endTime);
        List<BankCardApplyFlow> bankCardApplyFlowList = bankCardApplyFlowManger.queryList(query);
        if(CollectionUtils.isNotEmpty(bankCardApplyFlowList)){
            //计算本期收入
            dayIncome = bankCardApplyFlowList.stream().filter(item ->
                    BankApplyType.APPLY_CREDIT.getKey() == item.getOperationType() ||
                            BankApplyType.REFUND.getKey() == item.getOperationType() ||
                            BankApplyType.RETURN.getKey() == item.getOperationType() ||
                            BankApplyType.CARD_REPAYMENT.getKey() == item.getOperationType())
                    .map(BankCardApplyFlow::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //计算本期支出
            dayExpenditure = bankCardApplyFlowList.stream().filter(item ->
                    BankApplyType.REFUND_CREDIT.getKey() == item.getOperationType() ||
                            BankApplyType.CONSUMPTION.getKey() == item.getOperationType() ||
                            BankApplyType.CARD_REPAYMENT_REFUND.getKey() == item.getOperationType())
                    .map(BankCardApplyFlow::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            dayBeginBalance = bankCardApplyFlowList.get(bankCardApplyFlowList.size()-1).getCurrentAmount();
            dayFinalBalance = bankCardApplyFlowList.get(0).getBalance();
        }
        personAcctExtractBalanceVO.setBillTime(DateUtil.format(startTime,"yyyyMMdd"));
        personAcctExtractBalanceVO.setBeginBalance(dayBeginBalance);
        personAcctExtractBalanceVO.setFinalBalance(dayFinalBalance);
        personAcctExtractBalanceVO.setIncome(dayIncome);
        personAcctExtractBalanceVO.setExpenditure(dayExpenditure);
        personAcctExtractBalanceVO.setAccountType(PersonAcctEnum.AccountType.BANK_CARD.code());
        int result = personAcctExtractBalanceVO.getFinalBalance().compareTo(personAcctExtractBalanceVO.getBeginBalance().add(personAcctExtractBalanceVO.getIncome()).subtract(personAcctExtractBalanceVO.getExpenditure()));
        personAcctExtractBalanceVO.setExtractStatus(result==0?PersonAcctEnum.ExtractStatus.YES.code():PersonAcctEnum.ExtractStatus.NO.code());
        return personAcctExtractBalanceVO;
    }

    private PersonAcctExtractBalanceVO personAcctDoHandleDay(BankUserCardInfoRespDTO bankUserCard, Date startTime, Date endTime) {
        PersonAcctExtractBalanceVO personAcctExtractBalanceVO = new PersonAcctExtractBalanceVO();

        //期初期末余额
        BigDecimal dayBeginBalance = BigDecimal.ZERO;
        BigDecimal dayFinalBalance = BigDecimal.ZERO;

        //当日收入
        BigDecimal dayIncome = BigDecimal.ZERO;
        //当日支出
        BigDecimal dayExpenditure = BigDecimal.ZERO;

        //获取个人资金账户流水
        List<BankUserCardFlowRespDTO> bankUserCardFlowDTOList = queryBankUserCardFlowList(bankUserCard, startTime, endTime);

        if(CollectionUtils.isNotEmpty(bankUserCardFlowDTOList)){
            //计算本期收入
            //    RECHARGE(2, "充值", PlusMinusEnum.PLUS, "充值", FundAcctTradeType.RECHARGE),
            //    WITHDRAWAL(3, "提现", PlusMinusEnum.MINUS, "提现", FundAcctTradeType.WITHDRAW),
            //    WITHDRAWAL_REFUND(4, "提现退回", PlusMinusEnum.PLUS, "提现退回", FundAcctTradeType.REFUND),
            //    REPAYMENT(81, "还款", PlusMinusEnum.MINUS, "还款", FundAcctTradeType.CONSUME),
            //    REPAYMENT_REFUND(82, "还款退回", PlusMinusEnum.PLUS, "还款退回", FundAcctTradeType.REFUND);
            dayIncome = bankUserCardFlowDTOList.stream().filter(item ->
                    2 == item.getOperationType() || 4 == item.getOperationType() || 82 == item.getOperationType())
                    .map(BankUserCardFlowRespDTO::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //计算本期支出
            dayExpenditure = bankUserCardFlowDTOList.stream().filter(item ->
                    3 == item.getOperationType() || 81 == item.getOperationType())
                    .map(BankUserCardFlowRespDTO::getOperationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            dayBeginBalance = bankUserCardFlowDTOList.get(bankUserCardFlowDTOList.size()-1).getCurrentBalance();
            dayFinalBalance = bankUserCardFlowDTOList.get(0).getBalance();
        }
        personAcctExtractBalanceVO.setBillTime(DateUtil.format(startTime,"yyyyMMdd"));
        personAcctExtractBalanceVO.setBeginBalance(dayBeginBalance);
        personAcctExtractBalanceVO.setFinalBalance(dayFinalBalance);
        personAcctExtractBalanceVO.setIncome(dayIncome);
        personAcctExtractBalanceVO.setExpenditure(dayExpenditure.abs());
        personAcctExtractBalanceVO.setAccountType(PersonAcctEnum.AccountType.USER_CARD.code());
        int result = personAcctExtractBalanceVO.getFinalBalance().compareTo(personAcctExtractBalanceVO.getBeginBalance().add(personAcctExtractBalanceVO.getIncome()).subtract(personAcctExtractBalanceVO.getExpenditure()));
        personAcctExtractBalanceVO.setExtractStatus(result==0?PersonAcctEnum.ExtractStatus.YES.code():PersonAcctEnum.ExtractStatus.NO.code());
        return personAcctExtractBalanceVO;
    }

    private void savePersonAcctExtractDay(BankSearchCardDetailRespDTO bankCard, AcctCompanyCard acctCompanyCard, PersonAcctExtractBalanceVO personAcctExtractBalanceVO) {
        try{
            //已存在，则忽略
            Example example = new Example(AcctPersonExtractDay.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("employeeId", bankCard.getEmployeeId());
            criteria.andEqualTo("bankName", bankCard.getBankName());
            criteria.andEqualTo("accountType", personAcctExtractBalanceVO.getAccountType());
            criteria.andEqualTo("billTime", personAcctExtractBalanceVO.getBillTime());
            List<AcctPersonExtractDay> acctPersonExtractDayList = acctPersonExtractDayMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(acctPersonExtractDayList)){
                FinhubLogger.info("PersonAcctExtractController#savePersonAcctExtractDay#对账记录已存在#employeeId:{},billTime:{}", bankCard.getEmployeeId(), personAcctExtractBalanceVO.getBillTime());
                return ;
            }
            AcctPersonExtractDay acctExtractDay = new AcctPersonExtractDay();
            acctExtractDay.setBillTime(personAcctExtractBalanceVO.getBillTime());
            acctExtractDay.setEmployeeId(bankCard.getEmployeeId());
            acctExtractDay.setEmployeeName(bankCard.getEmployeeName());
            acctExtractDay.setEmployeePhone(bankCard.getEmployeePhone());
            acctExtractDay.setBankAccountNo(bankCard.getBankAccountNo());
            acctExtractDay.setBankAcctId(bankCard.getBankAcctId());
            acctExtractDay.setCompanyId(bankCard.getCompanyId());
            if(acctCompanyCard != null){
                acctExtractDay.setCompanyName(acctCompanyCard.getCompanyName());
                acctExtractDay.setCompanyMainId(acctCompanyCard.getCompanyMainId());

            }
            acctExtractDay.setCompanyBankAccountNo(bankCard.getCompanyBankAccountNo());
            acctExtractDay.setCompanyBankAcctId(bankCard.getCompanyBankAcctId());
            acctExtractDay.setAccountType(personAcctExtractBalanceVO.getAccountType());
            acctExtractDay.setBankName(bankCard.getBankName());
            acctExtractDay.setBeginBalance(personAcctExtractBalanceVO.getBeginBalance());
            acctExtractDay.setFinalBalance(personAcctExtractBalanceVO.getFinalBalance());
            acctExtractDay.setIncome(personAcctExtractBalanceVO.getIncome());
            acctExtractDay.setExpenditure(personAcctExtractBalanceVO.getExpenditure());
            acctExtractDay.setExtractStatus(personAcctExtractBalanceVO.getExtractStatus());
            acctExtractDay.setCreateTime(DateUtils.now());
            acctExtractDay.setUpdateTime(acctExtractDay.getCreateTime());
            acctPersonExtractDayMapper.insert(acctExtractDay);
        }catch (Exception e){
            FinhubLogger.error("PersonAcctExtractController#savePersonAcctExtractDay#req:{}", JSON.toJSON(bankCard),e);
        }
    }


    @HttpService(value = "/balance/check", method = RequestMethod.POST)
    public void acctCheck(HttpRequest request, HttpResponse response) {
        long start = System.currentTimeMillis();
        String jobConfig = request.getParameter("jobConfig");
        BankCardBalanceCheckDTO bankCardBalanceCheckDTO = buildPersonAcctBalanceCheckDTO(jobConfig);
        FinhubLogger.info("PersonAcctExtractController#acctCheck#start#req:{}",JSON.toJSON(bankCardBalanceCheckDTO));

        //获取虚拟卡账户
        List<BankSearchCardDetailRespDTO> bankCardList = bankCardSearchService.queryVirtualCardList(bankCardBalanceCheckDTO);
        if(CollectionUtils.isEmpty(bankCardList)){
            FinhubLogger.info("PersonAcctExtractController#acctCheck#start#账户信息不存在,无需对账:{}",JSON.toJSON(bankCardBalanceCheckDTO));
            return;
        }
        String extractTime = DateUtil.format(bankCardBalanceCheckDTO.getBillTimeFrom(),"yyyyMMdd");
        //当天跑实时数据
        FinhubLogger.info("PersonAcctExtractController#acctCheck#extractTime:{},card.size:{}", extractTime, bankCardList.size());

        //保存余额对账记录
        for(BankSearchCardDetailRespDTO bankCard : bankCardList){
            //获取个人资金账户
            BankUserCardInfoRespDTO bankUserCard = getBankUserCard(bankCard);
            singlePersonAcctBalanceCheck(bankCard, bankUserCard, extractTime);
        }
        long end = System.currentTimeMillis();
        FinhubLogger.info("PersonAcctExtractController#acctCheck#end#extractTime:{},duration:{}",extractTime, end-start);

    }

    @HttpService(value = "/balance/check/reload", method = RequestMethod.POST)
    public void acctCheckReload(HttpRequest request, HttpResponse response) {
        long start = System.currentTimeMillis();
        String jobConfig = request.getParameter("jobConfig");
        BankCardBalanceCheckReloadDTO bankCardBalanceCheckReloadDTO = buildPersonAcctBalanceCheckReloadDTO(jobConfig);
        FinhubLogger.info("PersonAcctExtractController#acctCheckReload#start#req:{}",JSON.toJSON(bankCardBalanceCheckReloadDTO));
        //获取数据
        List<AcctPersonBankCheck> acctPersonBankCheckList = queryAcctPersonBankCheckList(bankCardBalanceCheckReloadDTO);
        if(CollectionUtils.isNotEmpty(acctPersonBankCheckList)){
            for(AcctPersonBankCheck bankCheck : acctPersonBankCheckList){
                singlePersonAcctBalanceCheckReload(bankCheck);
            }
        }
        long end = System.currentTimeMillis();
        FinhubLogger.info("PersonAcctExtractController#acctCheckReload#end#duration:{}", end-start);

    }

    private BankCardBalanceCheckDTO buildPersonAcctDayExtractDTO(String reqJson) {
        BankCardBalanceCheckDTO bankCardBalanceCheckDTO = new BankCardBalanceCheckDTO();
        if(StringUtils.isNotBlank(reqJson)){
            bankCardBalanceCheckDTO = JSON.parseObject(reqJson, BankCardBalanceCheckDTO.class);
        }
        //默认当天定时任务跑前一天的日切数据
        if(bankCardBalanceCheckDTO.getBillTimeFrom() == null){
            bankCardBalanceCheckDTO.setBillTimeFrom(DateUtils.addDay(DateUtil.beginOfDay(DateUtils.now()), -1));
        }
        if(bankCardBalanceCheckDTO.getBillTimeTo() == null){
            bankCardBalanceCheckDTO.setBillTimeTo(DateUtils.addDay(DateUtil.endOfDay(DateUtils.now()), -1));
        }
        //默认只获取广发直连数据
        bankCardBalanceCheckDTO.setBankName(BankNameEnum.CGB.getCode());
        bankCardBalanceCheckDTO.setDirectAcctType(FundAcctDirectAcctTypeEnum.BANK.getKey());
        return bankCardBalanceCheckDTO;
    }


    private BankCardBalanceCheckDTO buildPersonAcctBalanceCheckDTO(String reqJson) {
        BankCardBalanceCheckDTO bankCardBalanceCheckDTO = new BankCardBalanceCheckDTO();
        if(StringUtils.isNotBlank(reqJson)){
            bankCardBalanceCheckDTO = JSON.parseObject(reqJson, BankCardBalanceCheckDTO.class);
        }
        //默认当天定时任务跑实时对账数据
        if(bankCardBalanceCheckDTO.getBillTimeFrom() == null){
            bankCardBalanceCheckDTO.setBillTimeFrom(DateUtils.now());
        }
        //默认只获取广发直连数据
        bankCardBalanceCheckDTO.setBankName(BankNameEnum.CGB.getCode());
        bankCardBalanceCheckDTO.setDirectAcctType(FundAcctDirectAcctTypeEnum.BANK.getKey());
        return bankCardBalanceCheckDTO;
    }

    private BankCardBalanceCheckReloadDTO buildPersonAcctBalanceCheckReloadDTO(String reqJson) {
        BankCardBalanceCheckReloadDTO bankCardBalanceCheckReloadDTO = new BankCardBalanceCheckReloadDTO();
        if(StringUtils.isNotBlank(reqJson)){
            bankCardBalanceCheckReloadDTO = JSON.parseObject(reqJson, BankCardBalanceCheckReloadDTO.class);
        }
        if(StringUtils.isBlank(bankCardBalanceCheckReloadDTO.getExtractTimeFrom()) && StringUtils.isBlank(bankCardBalanceCheckReloadDTO.getExtractTimeTo()) &&
                bankCardBalanceCheckReloadDTO.getCreateTimeFrom()==null && bankCardBalanceCheckReloadDTO.getCreateTimeTo()==null){
            bankCardBalanceCheckReloadDTO.setCreateTimeFrom(DateUtil.beginOfDay(DateUtils.now()));
            bankCardBalanceCheckReloadDTO.setCreateTimeTo(DateUtils.now());
        }
        return bankCardBalanceCheckReloadDTO;
    }

    private void singlePersonAcctBalanceCheck(BankSearchCardDetailRespDTO bankCard, BankUserCardInfoRespDTO bankUserCard, String extractTime) {
        try{
            //获取广发直连银行数据
            CgbQueryAccountBalanceRespDTO cgbAccountBalanceResp = doRPCQueryCgbVirtualCardBalance(bankCard);
            if(cgbAccountBalanceResp == null || cgbAccountBalanceResp.getVAccUnableAmt()==null || cgbAccountBalanceResp.getVAccAmt()==null){
                FinhubLogger.info("PersonAcctExtractController#singlePersonAcctBalanceCheck#获取银行账户信息失败#req:{}", JSON.toJSONString(bankCard));
                return ;
            }
            //获取个人对应企业信息
            AcctCompanyCard acctCompanyCard = acctCompanyCardService.findByCompanyIdAndBank(bankCard.getCompanyId(), bankCard.getBankName(), bankCard.getCompanyBankAccountNo());
            //保存备用金账户余额对账记录
            saveAcctPersonBankCheckByBankCard(bankCard, acctCompanyCard, cgbAccountBalanceResp, extractTime);
            if(bankUserCard != null){
                saveAcctPersonBankCheckByBankUserCard(bankCard, bankUserCard, acctCompanyCard, cgbAccountBalanceResp, extractTime);
            }
        }catch (Exception e){
            FinhubLogger.error("PersonAcctExtractController#singlePersonAcctBalanceCheck#个人账户对账异常#req:{}", JSON.toJSONString(bankCard), e);
        }
    }

    private CgbQueryAccountBalanceRespDTO doRPCQueryCgbVirtualCardBalance(BankSearchCardDetailRespDTO bankCard) {
        CgbQueryAccountBalanceRespDTO cgbCreateAccountTypeOPRespDTO = null;
        try{
            CgbQueryAccountBalanceReqDTO cgbQueryAccountBalanceReqDTO = new CgbQueryAccountBalanceReqDTO();
            cgbQueryAccountBalanceReqDTO.setAccountType("E");
            cgbQueryAccountBalanceReqDTO.setSubAccNo(bankCard.getBankAccountNo());
            cgbQueryAccountBalanceReqDTO.setChannel("330");
            cgbQueryAccountBalanceReqDTO.setTxnId("SN"+System.nanoTime());
            FinhubLogger.info("PersonAcctExtractController#doRPCQueryCgbVirtualCardBalance-queryAccountBalance-in#req:{}", JSON.toJSONString(cgbQueryAccountBalanceReqDTO));
            cgbCreateAccountTypeOPRespDTO = cgbVirtualCardService.queryAccountBalance(cgbQueryAccountBalanceReqDTO);
            FinhubLogger.info("PersonAcctExtractController#doRPCQueryCgbVirtualCardBalance-queryAccountBalance-in#resp:{}", JSON.toJSONString(cgbCreateAccountTypeOPRespDTO));
        } catch (Exception e){
            FinhubLogger.error("PersonAcctExtractController#doRPCQueryCgbVirtualCardBalance-queryAccountBalance-in#req:{}", JSON.toJSONString(bankCard), e);
        }
        return cgbCreateAccountTypeOPRespDTO;
    }

    private void saveAcctPersonBankCheckByBankCard(BankSearchCardDetailRespDTO bankCard, AcctCompanyCard acctCompanyCard, CgbQueryAccountBalanceRespDTO cgbAccountBalanceResp, String extractTime) {

        try{
            //已存在，则忽略
            if(isPersonAcctBankCheckExist(bankCard, extractTime,PersonAcctEnum.AccountType.BANK_CARD.code())){
                FinhubLogger.info("PersonAcctExtractController#saveAcctPersonBankCheckByBankCard#对账记录已存在#employeeId:{},extractTime:{}", bankCard.getEmployeeId(), extractTime);
                return ;
            }
            AcctPersonBankCheck acctPersonBankCheck = new AcctPersonBankCheck();
            acctPersonBankCheck.setExtractId(IDGen.genId(IDGen.PERSON_ACCOUNT_BALANCE_CHECK_PREFIX));
            acctPersonBankCheck.setExtractTime(extractTime);
            acctPersonBankCheck.setEmployeeId(bankCard.getEmployeeId());
            acctPersonBankCheck.setEmployeeName(bankCard.getEmployeeName());
            acctPersonBankCheck.setEmployeePhone(bankCard.getEmployeePhone());
            acctPersonBankCheck.setBankAccountNo(bankCard.getBankAccountNo());
            acctPersonBankCheck.setBankAcctId(bankCard.getBankAcctId());
            acctPersonBankCheck.setCompanyId(bankCard.getCompanyId());
            if(acctCompanyCard != null){
                acctPersonBankCheck.setCompanyName(acctCompanyCard.getCompanyName());
                acctPersonBankCheck.setCompanyMainId(acctCompanyCard.getCompanyMainId());
            }
            acctPersonBankCheck.setCompanyBankAccountNo(bankCard.getCompanyBankAccountNo());
            acctPersonBankCheck.setCompanyBankAcctId(bankCard.getCompanyBankAcctId());
            acctPersonBankCheck.setAccountType(PersonAcctEnum.AccountType.BANK_CARD.code());
            acctPersonBankCheck.setBankName(bankCard.getBankName());
            acctPersonBankCheck.setFbtAcctBalance(bankCard.getCardAcctBalance());
            acctPersonBankCheck.setBankAcctBalance(new BigDecimal(cgbAccountBalanceResp.getEAccUnableAmt()).multiply(new BigDecimal(100)));
            int extractResult = acctPersonBankCheck.getFbtAcctBalance().compareTo(acctPersonBankCheck.getBankAcctBalance().subtract(new BigDecimal(1)));
            acctPersonBankCheck.setExtractStatus(extractResult==0?PersonAcctEnum.ExtractStatus.YES.code():PersonAcctEnum.ExtractStatus.NO.code());
            acctPersonBankCheck.setProcessStatus(acctPersonBankCheck.getExtractStatus()==PersonAcctEnum.ExtractStatus.YES.code()?PersonAcctEnum.ProcessStatus.DONE.code():PersonAcctEnum.ProcessStatus.WAITING.code());
//            acctPersonBankCheck.setRemark();
            acctPersonBankCheck.setCreateTime(DateUtils.now());
            acctPersonBankCheck.setUpdateTime(acctPersonBankCheck.getCreateTime());
            acctPersonBankCheckMapper.insert(acctPersonBankCheck);
        }catch (Exception e){
            FinhubLogger.error("PersonAcctExtractController#saveAcctPersonBankCheckByBankCard#req:{}", JSON.toJSON(bankCard),e);
        }
    }


    private void saveAcctPersonBankCheckByBankUserCard(BankSearchCardDetailRespDTO bankCard, BankUserCardInfoRespDTO bankUserCard, AcctCompanyCard acctCompanyCard, CgbQueryAccountBalanceRespDTO cgbAccountBalanceResp, String extractTime) {
        try{
            //已存在，则忽略
            if(isPersonAcctBankCheckExist(bankCard, extractTime,PersonAcctEnum.AccountType.USER_CARD.code())){
                FinhubLogger.info("PersonAcctExtractController#saveAcctPersonBankCheckByBankUserCard#对账记录已存在#employeeId:{},extractTime:{}", bankCard.getEmployeeId(), extractTime);
                return ;
            }
            AcctPersonBankCheck acctPersonBankCheck = new AcctPersonBankCheck();
            acctPersonBankCheck.setExtractId(IDGen.genId(IDGen.PERSON_ACCOUNT_BALANCE_CHECK_PREFIX));
            acctPersonBankCheck.setExtractTime(extractTime);
            acctPersonBankCheck.setEmployeeId(bankCard.getEmployeeId());
            acctPersonBankCheck.setEmployeeName(bankCard.getEmployeeName());
            acctPersonBankCheck.setEmployeePhone(bankCard.getEmployeePhone());
            acctPersonBankCheck.setBankAccountNo(bankCard.getBankAccountNo());
            acctPersonBankCheck.setBankAcctId(bankCard.getBankAcctId());
            acctPersonBankCheck.setCompanyId(bankCard.getCompanyId());
            if(acctCompanyCard != null){
                acctPersonBankCheck.setCompanyName(acctCompanyCard.getCompanyName());
                acctPersonBankCheck.setCompanyMainId(acctCompanyCard.getCompanyMainId());
            }
            acctPersonBankCheck.setCompanyBankAccountNo(bankCard.getCompanyBankAccountNo());
            acctPersonBankCheck.setCompanyBankAcctId(bankCard.getCompanyBankAcctId());
            acctPersonBankCheck.setAccountType(PersonAcctEnum.AccountType.USER_CARD.code());
            acctPersonBankCheck.setBankName(bankCard.getBankName());
            acctPersonBankCheck.setFbtAcctBalance(bankUserCard.getCardBalance());
            acctPersonBankCheck.setBankAcctBalance(new BigDecimal(cgbAccountBalanceResp.getCurrentAvaBalance()).multiply(new BigDecimal(100)));
            int extractResult = (acctPersonBankCheck.getFbtAcctBalance().add(new BigDecimal(1))).compareTo(acctPersonBankCheck.getBankAcctBalance());
            acctPersonBankCheck.setExtractStatus(extractResult==0?PersonAcctEnum.ExtractStatus.YES.code():PersonAcctEnum.ExtractStatus.NO.code());
            acctPersonBankCheck.setProcessStatus(acctPersonBankCheck.getExtractStatus()==PersonAcctEnum.ExtractStatus.YES.code()?PersonAcctEnum.ProcessStatus.DONE.code():PersonAcctEnum.ProcessStatus.WAITING.code());
//            acctPersonBankCheck.setRemark();
            acctPersonBankCheck.setCreateTime(DateUtils.now());
            acctPersonBankCheck.setUpdateTime(acctPersonBankCheck.getCreateTime());
            acctPersonBankCheckMapper.insert(acctPersonBankCheck);
        }catch (Exception e){
            FinhubLogger.error("PersonAcctExtractController#saveAcctPersonBankCheckByBankUserCard#req:{}", JSON.toJSON(bankCard),e);
        }
    }

    private boolean isPersonAcctBankCheckExist(BankSearchCardDetailRespDTO bankCard, String extractTime, int accountType){
        Example example = new Example(AcctPersonBankCheck.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("employeeId", bankCard.getEmployeeId());
        criteria.andEqualTo("bankName", bankCard.getBankName());
        criteria.andEqualTo("accountType", accountType);
        criteria.andEqualTo("extractTime", extractTime);
        List<AcctPersonBankCheck> acctPersonBankCheckList = acctPersonBankCheckMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(acctPersonBankCheckList);
    }

    private List<AcctPersonBankCheck> queryAcctPersonBankCheckList(BankCardBalanceCheckReloadDTO bankCardBalanceCheckReloadDTO) {
        Example example = new Example(AcctPersonBankCheck.class);
        Example.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotBlank(bankCardBalanceCheckReloadDTO.getExtractId())){
            criteria.andEqualTo("extractId", bankCardBalanceCheckReloadDTO.getExtractId());
        }
        if(bankCardBalanceCheckReloadDTO.getCreateTimeFrom() != null){
            criteria.andGreaterThanOrEqualTo("createTime", bankCardBalanceCheckReloadDTO.getCreateTimeFrom());
        }
        if(bankCardBalanceCheckReloadDTO.getCreateTimeTo() != null){
            criteria.andLessThanOrEqualTo("createTime", bankCardBalanceCheckReloadDTO.getCreateTimeTo());
        }
        if(StringUtils.isNotBlank(bankCardBalanceCheckReloadDTO.getExtractTimeFrom())){
            criteria.andGreaterThanOrEqualTo("extractTime", bankCardBalanceCheckReloadDTO.getExtractTimeFrom());
        }
        if(StringUtils.isNotBlank(bankCardBalanceCheckReloadDTO.getExtractTimeTo())){
            criteria.andLessThanOrEqualTo("extractTime", bankCardBalanceCheckReloadDTO.getExtractTimeTo());
        }
        if(bankCardBalanceCheckReloadDTO.getAccountType() != null){
            criteria.andEqualTo("accountType", bankCardBalanceCheckReloadDTO.getAccountType());
        }
        return acctPersonBankCheckMapper.selectByExample(example);
    }

    private void singlePersonAcctBalanceCheckReload(AcctPersonBankCheck bankCheck) {
        try{
            //获取虚拟卡信息
            BankSearchCardDetailRespDTO bankCard = bankCardSearchService.queryByBankAccountNo(bankCheck.getBankAccountNo());
            CgbQueryAccountBalanceRespDTO cgbAccountBalanceResp = doRPCQueryCgbVirtualCardBalance(bankCard);
            AcctPersonBankCheck acctPersonBankCheck = new AcctPersonBankCheck();
            acctPersonBankCheck.setRemark("reload");
            acctPersonBankCheck.setUpdateTime(DateUtils.now());
            acctPersonBankCheck.setId(bankCheck.getId());
            if(PersonAcctEnum.AccountType.BANK_CARD.code() == bankCheck.getAccountType()){
                acctPersonBankCheck.setFbtAcctBalance(bankCard.getCardAcctBalance());
                acctPersonBankCheck.setBankAcctBalance(new BigDecimal(cgbAccountBalanceResp.getEAccUnableAmt()).multiply(new BigDecimal(100)));
                int extractResult = acctPersonBankCheck.getFbtAcctBalance().compareTo(acctPersonBankCheck.getBankAcctBalance().subtract(new BigDecimal(1)));
                acctPersonBankCheck.setExtractStatus(extractResult==0?PersonAcctEnum.ExtractStatus.YES.code():PersonAcctEnum.ExtractStatus.NO.code());
                acctPersonBankCheck.setProcessStatus(acctPersonBankCheck.getExtractStatus()==PersonAcctEnum.ExtractStatus.YES.code()?PersonAcctEnum.ProcessStatus.DONE.code():PersonAcctEnum.ProcessStatus.WAITING.code());
            }else if(PersonAcctEnum.AccountType.USER_CARD.code() == bankCheck.getAccountType()){
                BankUserCardInfoRespDTO bankUserCard = getBankUserCard(bankCard);
                acctPersonBankCheck.setFbtAcctBalance(bankUserCard.getCardBalance());
                acctPersonBankCheck.setBankAcctBalance(new BigDecimal(cgbAccountBalanceResp.getCurrentAvaBalance()).multiply(new BigDecimal(100)));
                int extractResult = (acctPersonBankCheck.getFbtAcctBalance().add(new BigDecimal(1))).compareTo(acctPersonBankCheck.getBankAcctBalance());
                acctPersonBankCheck.setExtractStatus(extractResult==0?PersonAcctEnum.ExtractStatus.YES.code():PersonAcctEnum.ExtractStatus.NO.code());
                acctPersonBankCheck.setProcessStatus(acctPersonBankCheck.getExtractStatus()==PersonAcctEnum.ExtractStatus.YES.code()?PersonAcctEnum.ProcessStatus.DONE.code():PersonAcctEnum.ProcessStatus.WAITING.code());
            }
            acctPersonBankCheckMapper.updateByPrimaryKeySelective(acctPersonBankCheck);
        }catch (Exception e){
            FinhubLogger.error("PersonAcctExtractController#singlePersonAcctBalanceCheckReload#req:{}", JSON.toJSON(bankCheck),e);
        }
    }

    @HttpService(value = "/diff/processing/handle", method = RequestMethod.POST)
    public void diffProcessHandle(HttpRequest request, HttpResponse response) {
        CompletableFuture.runAsync(() -> {
            iAcctVcardDiffProcessService.processingHandler();
        });
        ResponseResultUtils.success(response, true);
    }
}
