package com.fenbeitong.fenbeipay.http.controller.account;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.core.model.vo.account.AcctCheckVo;
import com.fenbeitong.fenbeipay.extract.manager.AcctCheckManagerService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Arrays;
import java.util.List;

/**
 * 账户对账相关
 * <AUTHOR>
 * @date 2021/3/19
 */
@HttpService("/")
public class AcctCheckController {
    @Autowired
    AcctCheckManagerService acctCheckManagerService;
    @Value("${acct.extract.bankName}")
    private String extractBankName;

    /**
     * 账户对账定时任务http接口
     * @param request 请求
     * @param response 返回
     */
    @HttpService(value = "fbp/account/check", method = RequestMethod.POST)
    public void acctCheck(HttpRequest request, HttpResponse response) {
        String jobConfig = request.getParameter("jobConfig");
        AcctCheckVo acctCheckVo = JSON.parseObject(jobConfig,AcctCheckVo.class);
        ValidateUtils.validate(acctCheckVo);
        FinhubLogger.info("【分贝通账户余额-银行账户余额】对账请求参数[{}][{}][{}][{}]",acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),acctCheckVo.getCheckTarget());
        List<String> bankNames = Arrays.asList(extractBankName.split(","));
        for (String bankName : bankNames) {
            if(!bankName.equals("FBT") && !BankNameEnum.isLfBank(bankName)){
                acctCheckManagerService.check(acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),bankName);
            }
        }
        FinhubLogger.info("【分贝通账户余额-银行账户余额】对账完成[{}][{}][{}][{}]",acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),acctCheckVo.getCheckTarget());
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "fbp/account/lfbank/check", method = RequestMethod.POST)
    public void lfbankAcctCheck(HttpRequest request, HttpResponse response) {
        String jobConfig = request.getParameter("jobConfig");
        AcctCheckVo acctCheckVo = JSON.parseObject(jobConfig,AcctCheckVo.class);
        ValidateUtils.validate(acctCheckVo);
        FinhubLogger.info("【分贝通账户余额-银行账户余额】对账请求参数[{}][{}][{}][{}]",acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),acctCheckVo.getCheckTarget());
        acctCheckManagerService.check(acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),BankNameEnum.LFBANK.getCode());
        FinhubLogger.info("【分贝通账户余额-银行账户余额】对账完成[{}][{}][{}][{}]",acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),acctCheckVo.getCheckTarget());
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "fbp/account/lianlian/check", method = RequestMethod.POST)
    public void lianlianAcctCheck(HttpRequest request, HttpResponse response) {
        String jobConfig = request.getParameter("jobConfig");
        AcctCheckVo acctCheckVo = JSON.parseObject(jobConfig,AcctCheckVo.class);
        ValidateUtils.validate(acctCheckVo);
        FinhubLogger.info("【分贝通账户余额-银行账户余额】对账请求参数[{}][{}][{}][{}]",acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),acctCheckVo.getCheckTarget());
        acctCheckManagerService.check(acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),BankNameEnum.LIANLIAN.getCode());
        FinhubLogger.info("【分贝通账户余额-银行账户余额】对账完成[{}][{}][{}][{}]",acctCheckVo.getIsRetry(),acctCheckVo.getRetryBillTime(),acctCheckVo.getCheckType(),acctCheckVo.getCheckTarget());
        ResponseResultUtils.success(response);
    }
}
