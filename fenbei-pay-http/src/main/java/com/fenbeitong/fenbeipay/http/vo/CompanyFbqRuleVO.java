package com.fenbeitong.fenbeipay.http.vo;

import com.fenbeitong.usercenter.api.model.dto.company.CompanyFbqRuleDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyFbqRuleVO implements Serializable {

    /**
     * 分贝券回收类型 0:立即回收 1:暂不回收
     */
    private Integer fbqRecycleType;

    /**
     * 是否提供先开票功能 0:否 1: 是
     */
    private Integer provideBeforehandInvoice;

    /**
     * 先票分贝券最长有效期（天）
     */
    private Integer beforeInvoiceMaxValidDays;

    /**
     * 后票分贝券最长有效期（天）
     */
    private Integer afterInvoiceMaxValidDays;

    public CompanyFbqRuleVO(CompanyFbqRuleDTO dto){
        this.fbqRecycleType = dto.getFbqRecycleType();
        this.provideBeforehandInvoice = dto.getProvideBeforehandInvoice();
        this.beforeInvoiceMaxValidDays = dto.getBeforeInvoiceMaxValidityDay();
        this.afterInvoiceMaxValidDays = dto.getAfterInvoiceMaxValidityDay();
    }
}
