package com.fenbeitong.fenbeipay.http.controller.newaccount.dto;

import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: java类作用描述
 * @ClassName: AccountCouponFlowReqDTO
 * @Author: zhangga
 * @CreateDate: 2019/12/6 6:15 PM
 * @UpdateUser:
 * @UpdateDate: 2019/12/6 6:15 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class AccountCouponFlowReqDTO extends PageBean {

    private String companyId;
    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 员工手机号
     */
    private String employeePhone;

    /**
     * 场景类型
     */
    private Integer fbOrderType;
    /**
     * 创建时间
     */
    @DateTimeFormat
    private Date startTime;
    @DateTimeFormat
    private Date endTime;
}