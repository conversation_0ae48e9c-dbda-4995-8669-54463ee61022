package com.fenbeitong.fenbeipay.http.controller.extract;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.fenbeitong.fenbeipay.extract.service.BizAcctCheckService;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2022-11-07 05:59:58 
*/
@HttpService("/internal/check/bill")
public class BizAcctCheckController {

	@Autowired
	private BizAcctCheckService bizAcctCheckService;
	
	@HttpService(value = "/do/check")
    public void doCheck(HttpRequest request, HttpResponse response) {
		FinhubLogger.info("开始调度对账任务->{}", LocalDateTime.now());
		String fromStr = request.getParameter("from");
		String toStr = request.getParameter("to");
		final Date from = StringUtils.isNotBlank(fromStr) ? DateUtils.parseDate(fromStr) : null;
		
		final Date to = StringUtils.isNotBlank(toStr) ? DateUtils.parseDate(toStr) : null;

		List<String> companyIds = bizAcctCheckService.getCheckList();
		if (CollectionUtils.isNotEmpty(companyIds)) {
			bizAcctCheckService.check(companyIds, from, to);
		} else {
			bizAcctCheckService.check(from, to);
		}

		ResponseResultUtils.success(response, Boolean.TRUE);
	}
	
	@HttpService(value = "/add/check/list", method = RequestMethod.GET)
    public void add(HttpRequest request, HttpResponse response) {
		String companyIds = request.getParameter("companyIds");
		bizAcctCheckService.addCheckList(companyIds);
		ResponseResultUtils.success(response, companyIds);
	}
	
	@HttpService(value = "/remove/check/list", method = RequestMethod.GET)
    public void remove(HttpRequest request, HttpResponse response) {
		bizAcctCheckService.remove();
		ResponseResultUtils.success(response, Boolean.TRUE);
	}

}
