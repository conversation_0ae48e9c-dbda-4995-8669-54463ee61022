package com.fenbeitong.fenbeipay.http.interceptor;

import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.config.PropertyUtils;
import com.luastar.swift.http.route.HandlerInterceptor;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 跨域请求拦截器
 */
public class CrosInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(CrosInterceptor.class);

    private static final String ACCESS_CONTROL_ALLOW_ORIGIN = PropertyUtils.getString("access.control.allow.origin", "*");

    public boolean preHandle(HttpRequest request, HttpResponse response) throws Exception {
        logger.info("CrosInterceptor[preHandle]");
        response.setHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN.toString(), ACCESS_CONTROL_ALLOW_ORIGIN);
        response.setHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_METHODS.toString(), "GET,PUT,POST,DELETE,OPTIONS");
        response.setHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_HEADERS.toString(), "X-Auth-Token,Cookie,X-Requested-With,Content-Type,Cache-Control");
        response.setHeader(HttpHeaderNames.ACCESS_CONTROL_ALLOW_CREDENTIALS.toString(), "true");
        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
            ResponseResultUtils.success(response, null);
            return false;
        }
        return true;
    }

    public void postHandle(HttpRequest request, HttpResponse response) throws Exception {
        logger.info("CrosInterceptor[postHandle]");
    }

}
