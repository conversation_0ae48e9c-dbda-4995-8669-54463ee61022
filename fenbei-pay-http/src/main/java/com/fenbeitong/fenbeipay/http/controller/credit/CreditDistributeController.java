package com.fenbeitong.fenbeipay.http.controller.credit;

import com.fenbeitong.fenbeipay.api.base.ResponseInfoPage;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardCreditDistributeReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankCardCreditDistributeRespDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 虚拟卡额度记录
 */
@HttpService("/fbp/creditDistribute")
public class CreditDistributeController extends BaseController {


    @Autowired
    private IBankCardSearchService iBankCardSearchService;


    @HttpService(value = "/queryCreditDistributePageByAccountNo", method = RequestMethod.POST)
    public void queryCreditDistributePageByAccountNo(HttpRequest request, HttpResponse response) {
        BankCardCreditDistributeReqDTO queryReq = request.getBodyObject(BankCardCreditDistributeReqDTO.class);
        ResponseInfoPage<BankCardCreditDistributeRespDTO> page = iBankCardSearchService.queryCreditDistributePageByAccountNo(queryReq);
        ResponseResultUtils.success(response, page);
    }


}