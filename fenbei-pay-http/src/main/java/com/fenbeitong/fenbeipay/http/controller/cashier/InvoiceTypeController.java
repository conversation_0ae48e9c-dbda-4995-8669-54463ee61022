package com.fenbeitong.fenbeipay.http.controller.cashier;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierInvoiceType;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierInvoiceTypeVO;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

import java.util.ArrayList;
import java.util.List;

@HttpService("/cashier/pay")
public class InvoiceTypeController {

    @HttpService(value = "/invoice/type/list/v3", method = RequestMethod.GET)
    public void invoiceTypeList(HttpRequest request, HttpResponse response) {
        CashierInvoiceType[] values = CashierInvoiceType.values();
        List<CashierInvoiceTypeVO> list = new ArrayList<>();
        for (CashierInvoiceType value : values) {
            list.add(new CashierInvoiceTypeVO(value.getKey(), value.getValue(), value.getDescription()));
        }
        ResponseResultUtils.success(response, list);
    }


}
