package com.fenbeitong.fenbeipay.http.controller.extract;

import com.fenbeitong.fenbeipay.extract.service.impl.HttpClientUtil;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

import java.util.Map;

@HttpService("/internal/flowRecon")
public class FlowReconWebTestController {

    public static final String appCode = "18923fb3629b42dfa06d3a15e25e3570"; // 环境授权，线上也用这个值


    /**
     * from提交测试（方式一：已验证通过）
     * @param request
     * @param response
     */
    @HttpService(value = "/doPostForm", method = RequestMethod.POST)
    public void getBillCodes(HttpRequest request, HttpResponse response) {
        Map mapParams = request.getBodyObject(Map.class);
        FinhubLogger.info("HTTP测试-doPostForm。入参{}", JsonUtils.toJson(mapParams));
        String data = HttpClientUtil.doPostForm(mapParams.get("url").toString(), mapParams, appCode);
        ResponseResultUtils.success(response, data);
    }

    /**
     * from提交测试（方式二：已验证）
     * @param request
     * @param response
     */
    @HttpService(value = "/doPost", method = RequestMethod.POST)
    public void doPost(HttpRequest request, HttpResponse response) {
        Map mapParams = request.getBodyObject(Map.class);
        FinhubLogger.info("HTTP测试-doPost。入参{}", JsonUtils.toJson(mapParams));
        String data = HttpClientUtil.doPost(mapParams.get("url").toString(), mapParams);
        ResponseResultUtils.success(response, data);
    }

}
