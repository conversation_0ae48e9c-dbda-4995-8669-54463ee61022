package com.fenbeitong.fenbeipay.http.controller.cashier;

import com.fenbeitong.fenbeipay.api.service.cashier.ICashierThirdPayBillService;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.TbThirdPayBillTaskMapper;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTask;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTaskExample;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@HttpService("/internal/cashier/task")
public class InnerTaskController {

    @Autowired
    private ICashierThirdPayBillService iCashierThirdPayBillService;
    @Autowired
    TbThirdPayBillTaskMapper tbThirdPayBillTaskMapper;
    @HttpService(value = "/third/pay/bill/init",method = RequestMethod.POST)
    public void taskThirdPayBillInit(HttpRequest request, HttpResponse response){
        //从入参获取或者每日初始化前一天
        String billDate = DateUtils.format(DateUtils.addDay(new Date(),-1), DateUtils.FORMAT_DATE_PATTERN);
        iCashierThirdPayBillService.initTask(billDate);
    }

    @HttpService(value = "/third/pay/bill/init/{year}/{month}/{day}",method = RequestMethod.GET)
    public void taskThirdPayBillInit4Mannual(HttpRequest request, HttpResponse response){
        //账单日期向前减少的值
        Integer year = Integer.valueOf(request.getPathValue("year"));
        Integer month = Integer.valueOf(request.getPathValue("month"));
        Integer day = Integer.valueOf(request.getPathValue("day"));
        //从入参获取或者每日初始化前一天
        String billDate = year + "-" + month + "-" + day;
        iCashierThirdPayBillService.initTask(billDate);
    }

    /**
     * 执行账单查询任务
     */
    @HttpService(value = "/third/pay/bill/query",method = RequestMethod.POST)
    public void taskThirdPayBillQuery(HttpRequest request, HttpResponse response){
        iCashierThirdPayBillService.runTask();
    }

    @HttpService(value = "/third/pay/bill/update",method = RequestMethod.POST)
    public void taskThirdPayBillUpdate4Mannual(HttpRequest request, HttpResponse response){
        TbThirdPayBillTask tbThirdPayBillTask = request.getBodyObject(TbThirdPayBillTask.class);
        TbThirdPayBillTaskExample updateExample = new TbThirdPayBillTaskExample();
        TbThirdPayBillTaskExample.Criteria criteria = updateExample.createCriteria();
        criteria.andPayAccountEqualTo(tbThirdPayBillTask.getPayAccount())
                .andBillTimeEqualTo(tbThirdPayBillTask.getBillTime());
        TbThirdPayBillTask update = new TbThirdPayBillTask();
        update.setPayAccount(tbThirdPayBillTask.getPayAccount());
        update.setTaskStatus(tbThirdPayBillTask.getTaskStatus());
        if (tbThirdPayBillTask.getRemark() != null) {
            update.setRemark(tbThirdPayBillTask.getRemark());
        }
        tbThirdPayBillTaskMapper.updateByExampleSelective(update,updateExample);
    }
}
