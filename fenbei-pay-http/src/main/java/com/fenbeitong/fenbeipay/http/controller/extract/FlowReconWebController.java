package com.fenbeitong.fenbeipay.http.controller.extract;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.*;
import com.fenbeitong.fenbeipay.extract.service.impl.UFlowReconWebServiceImpl;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.FundAcctDebitOptType;
import com.fenbeitong.finhub.common.constant.FundAcctTradeType;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@HttpService("/fbp/web/companyAcct/flowRecon")
public class FlowReconWebController {


    @Autowired
    UFlowReconWebServiceImpl uFlowReconWebServiceImpl;

    /**
     * 获取账期列表
     * @param request
     * @param response
     */
    @HttpService(value = "/getBillCodes", method = RequestMethod.POST)
    public void getBillCodes(HttpRequest request, HttpResponse response) {
        FlowReconWebBillCodesReqDTO queryReq = request.getBodyObject(FlowReconWebBillCodesReqDTO.class);
        FinhubLogger.info("获取账期列表。入参{}", JsonUtils.toJson(queryReq));
        queryReq.checkReqWeb();
        List<FlowReconWebBillCodesRespDTO> list = uFlowReconWebServiceImpl.getBillCodes(queryReq);
        ResponseResultUtils.success(response, list);
    }

    /**
     * 按照账期流水核对，汇总查询
     * @param request
     * @param response
     */
    @HttpService(value = "/getListFlowSummary", method = RequestMethod.POST)
    public void getListFlowSummary(HttpRequest request, HttpResponse response) {
        FlowReconWebListFlowSummaryReqDTO queryReq = request.getBodyObject(FlowReconWebListFlowSummaryReqDTO.class);
        FinhubLogger.info("按照账期流水核对，汇总查询。入参{}", JsonUtils.toJson(queryReq));
        queryReq.checkReqWeb();
        List<FlowReconWebListFlowSummaryRespDTO> list = uFlowReconWebServiceImpl.getListFlowSummary(queryReq);
        ResponseResultUtils.success(response, list);
    }

    /**
     * 按照账期流水核对，列表查询
     * @param request
     * @param response
     */
    @HttpService(value = "/getListFlow", method = RequestMethod.POST)
    public void getListFlow(HttpRequest request, HttpResponse response) {
        FlowReconWebListFlowReqDTO queryReq = request.getBodyObject(FlowReconWebListFlowReqDTO.class);
        FinhubLogger.info("按照账期流水核对，列表查询。入参{}", JsonUtils.toJson(queryReq));
        queryReq.checkReqWeb();
        ResponsePage<FlowReconWebListFlowRespDTO> page = uFlowReconWebServiceImpl.getListFlow(queryReq);
        ResponseResultUtils.success(response, page);
    }

    /**
     * 按照账单核对，汇总查询
     * @param request
     * @param response
     */
    @HttpService(value = "/getListBillSummary", method = RequestMethod.POST)
    public void getListBillSummary(HttpRequest request, HttpResponse response) {
        FlowReconWebListBillSummaryReqDTO queryReq = request.getBodyObject(FlowReconWebListBillSummaryReqDTO.class);
        FinhubLogger.info("按照账单核对，汇总查询。入参{}", JsonUtils.toJson(queryReq));
        queryReq.checkReqWeb();
        List<FlowReconWebListBillSummaryRespDTO> list = uFlowReconWebServiceImpl.getListBillSummary(queryReq);
        ResponseResultUtils.success(response, list);
    }

    /**
     * 按照账单核对，列表查询
     * @param request
     * @param response
     */
    @HttpService(value = "/getListBill", method = RequestMethod.POST)
    public void getListBill(HttpRequest request, HttpResponse response) {
        FlowReconWebListBillReqDTO queryReq = request.getBodyObject(FlowReconWebListBillReqDTO.class);
        FinhubLogger.info("按照账单核对，列表查询。入参{}", JsonUtils.toJson(queryReq));
        queryReq.checkReqWeb();
        ResponsePage<FlowReconWebListFlowRespDTO> page = uFlowReconWebServiceImpl.getListBill(queryReq);
        ResponseResultUtils.success(response, page);
    }

    /**
     * 下拉框选项
     * @param request
     * @param response
     */
    @HttpService(value = "/getSelectList", method = RequestMethod.POST)
    public void getSelectList(HttpRequest request, HttpResponse response) {
        FlowReconWebSelectRespDTO flowReconWebSelectRespDTO = new FlowReconWebSelectRespDTO();

        Map<Integer, String> webBusinessDebitEnumMap = FundAcctDebitOptType.getWebBusinessDebitEnumMap();
        flowReconWebSelectRespDTO.setOperationTypes(flowReconWebSelectRespDTO.getListFromMap(webBusinessDebitEnumMap));

        Map<Integer, String> webBusinessDebitTypes = FundAcctTradeType.getWebBusinessDebitTypes();
        flowReconWebSelectRespDTO.setTradeTypes(flowReconWebSelectRespDTO.getListFromMap(webBusinessDebitTypes));

        Map<Integer, String> categoryTypeMap = getCategoryTypeMap();
        flowReconWebSelectRespDTO.setCategoryTypes(flowReconWebSelectRespDTO.getListFromMap(categoryTypeMap));

        ResponseResultUtils.success(response, flowReconWebSelectRespDTO);
    }

    /**
     * 查询子账户流水 -订单场景类型
     * CategoryTypeEnum
     * @return
     */
    public static Map<Integer, String> getCategoryTypeMap() {
        Map<Integer, String> map = new HashMap<>();
        for (CategoryTypeEnum type : CategoryTypeEnum.values()) {
            if (!Objects.equals(-1, type.getCode())) {
                map.put(Integer.valueOf(type.getCode()), type.getName());
            }
        }
        return map;
    }

    /**
     * 按照账单核对，列表查询
     * @param request
     * @param response
     */
    @HttpService(value = "/export", method = RequestMethod.POST)
    public void export(HttpRequest request, HttpResponse response) {

        FlowReconWebExportReqDTO queryReq = request.getBodyObject(FlowReconWebExportReqDTO.class);
        FinhubLogger.info("企业web账单流水导出。入参{}", JsonUtils.toJson(queryReq));
        queryReq.checkReqWeb();

        String userId = ObjUtils.toString(request.getAttribute("userId"));
        String userName = ObjUtils.toString(request.getAttribute("userName"));

        JSONObject taskObject = JSON.parseObject(JSON.toJSONString(queryReq));
        taskObject.put("userId", userId);
        taskObject.put("userName", userName);

        String params = JSON.toJSONString(taskObject);
        String harmonyTaskId = uFlowReconWebServiceImpl.getHarmonyTaskId(params);

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("taskId", harmonyTaskId); // 用于前端轮询
        ResponseResultUtils.success(response, resultMap);
    }

}
