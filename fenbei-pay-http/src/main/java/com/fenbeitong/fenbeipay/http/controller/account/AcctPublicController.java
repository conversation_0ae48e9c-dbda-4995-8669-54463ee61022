package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicSearchService;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.AcctPublicDetailRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.AcctPublicSimpleInfoRespRPCDTO;
import com.fenbeitong.fenbeipay.api.util.BankNameShowConfig;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.acctpublic.AcctPublicFindReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.acctpublic.AcctPublicFindRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.acctpublic.AcctPublicStatusFindReqVo;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.mask.utils.DataMaskUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Title: PersonAccountController
 * @ProjectName fenbei-pay
 */
@HttpService("/fbp/acctpublic")
public class AcctPublicController extends BaseController {

    @Autowired
    private AcctPublicSearchService accountPublicSearchService;

    @Autowired
    private AcctPublicSearchService acctPublicSearchService;

    @Autowired
    protected UAcctCompanyMainService uAcctCompanyMainService;

    @Autowired
    private UAcctCommonService uAcctCommonService;

    /**
     * @Description: 查询企业对公账户
     * @param: request
     * @param: response
     * @date: 2019/1/14 10:30 AM
     */
    @HttpService(value = "/query/detail/{bankName}", method = RequestMethod.GET)
    public void queryDetail(HttpRequest request, HttpResponse response) {
        String bankName = request.getPathValue("bankName");
        String userCompanyId = getUserCompanyId(request);
        BankNameEnum bankEnum;
        try {
            bankEnum = BankNameEnum.getBankEnum(bankName);
        } catch (Exception e) {
            FinhubLogger.error("查询账户参数错误{}", bankName);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        if (ObjUtils.isEmpty(bankEnum) || ObjUtils.isEmpty(userCompanyId)) {
            FinhubLogger.error("查询账户参数错误{}=={}", bankName, userCompanyId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        AcctPublicDetailRespRPCDTO acctPublicDetailRespRPCDTO = accountPublicSearchService.queryByCompanyIdAndBankAccountName(userCompanyId, null,bankEnum);

        ResponseResultUtils.success(response, acctPublicDetailRespRPCDTO);
    }


    /**
     * @Description: 根据企业id和开户行查询是否正常使用的卡
     */
    @HttpService(value = "/query/exsit/normal/{bankName}", method = RequestMethod.GET)
    public void queryExsitNormalDetail(HttpRequest request, HttpResponse response) {
        String bankName = request.getPathValue("bankName");
        String userCompanyId = getUserCompanyId(request);
        BankNameEnum bankEnum;
        try {
            bankEnum = BankNameEnum.getBankEnum(bankName);
        } catch (Exception e) {
            FinhubLogger.error("查询账户参数错误{}", bankName);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        if (ObjUtils.isEmpty(bankEnum) || ObjUtils.isEmpty(userCompanyId)) {
            FinhubLogger.error("查询账户参数错误{}=={}", bankName, userCompanyId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        Boolean isQueryExsitNormal =accountPublicSearchService.queryExsitNormalDetail(userCompanyId,bankEnum);

        ResponseResultUtils.success(response, isQueryExsitNormal);
    }

    /**
     * @Description:App 根据企业id查询所有启用的账户主体
     */
    @HttpService(value = "/query/list", method = RequestMethod.POST)
    public void queryAcctPublicList(HttpRequest request, HttpResponse response) {
        String userCompanyId = getUserCompanyId(request);
        if (ObjUtils.isEmpty(userCompanyId)) {
            FinhubLogger.error("查询账户参数错误{}=={}", userCompanyId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        AcctPublicFindReqVo acctPublicFindReqVo = new AcctPublicFindReqVo();
        try{
            acctPublicFindReqVo = request.getBodyObject(AcctPublicFindReqVo.class);
        }catch (Exception e){
            FinhubLogger.warn("查询账户参数错误{}=={}",userCompanyId, e);
            //兼容IOS端问题
            String bankAccountAcctName = request.getParameter("bankAccountAcctName");
            acctPublicFindReqVo.setBankAccountAcctName(bankAccountAcctName);
        }
        acctPublicFindReqVo.setCompanyId(userCompanyId);

        AcctPublicFindRespVo acctPublicList =
                acctPublicSearchService.queryAcctPublicList(acctPublicFindReqVo);

        makeAcctPublicRespDTO(acctPublicList);

        ResponseResultUtils.success(response, acctPublicList);
    }


    /**
     * @Description:web 根据企业id查询所有启用的账户主体
     */
    @HttpService(value = "/web/query/employeey/list", method = RequestMethod.POST)
    public void queryWebAcctPublicList(HttpRequest request, HttpResponse response) {
        String userCompanyId = getUserCompanyId(request);
        String userId = getUserId(request);
        if (ObjUtils.isEmpty(userCompanyId)||ObjUtils.isEmpty(userId)) {
            FinhubLogger.error("查询账户参数错误{}=={}", userCompanyId);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode());
        }
        AcctPublicFindRespVo acctPublicList =
                acctPublicSearchService.queryAuthAcctPublicList(userCompanyId,userId);
        makeAcctPublicRespDTO(acctPublicList);

        ResponseResultUtils.success(response, acctPublicList);
    }

    private void makeAcctPublicRespDTO(AcctPublicFindRespVo acctPublicList) {
        List<AcctPublicSimpleInfoRespRPCDTO> simpleInfoRespRPCDTOS = acctPublicList.getSimpleInfoRespRPCDTOS();
        if(CollectionUtils.isNotEmpty(simpleInfoRespRPCDTOS)){
            List<String> companyMainIds = simpleInfoRespRPCDTOS.stream().filter(e -> !StringUtils.isBlank(e.getCompanyMainId())).map(AcctPublicSimpleInfoRespRPCDTO::getCompanyMainId).collect(Collectors.toList());
            List<AcctCompanyMain> byMainIds = uAcctCompanyMainService.findByMainIds(companyMainIds);
            Map<String, AcctCompanyMain> mapByMainId = byMainIds.stream().collect(Collectors.toMap(AcctCompanyMain::getCompanyMainId, Function.identity(), (key1, key2) -> key2));
            simpleInfoRespRPCDTOS.stream().forEach(e->{
                String bankAccountName = StringUtils.isBlank(e.getBankAccountName()) ? "" : e.getBankAccountName();
                String bankAccountNo = StringUtils.isBlank(e.getBankAccountNo()) ? "" : e.getBankAccountNo();
                //String businessName = Objects.isNull(mapByMainId.get(e.getCompanyMainId())) ? "" : mapByMainId.get(e.getCompanyMainId()).getBusinessName();
                AcctCompanyMain acctCompanyMain = mapByMainId.get(e.getCompanyMainId());
                String businessName = e.getBankAccountAcctName();
                if(Objects.nonNull(acctCompanyMain)){
                    /**
                     * ZHIFU-5070 中信账户名称优化
                     * 1.上线之后展示主体名称
                     * 2.白名单内展示主体名称
                     */
                    String showMainName = uAcctCommonService.getShowMainName(bankAccountName, acctCompanyMain.getBusinessName(), bankAccountNo, acctCompanyMain);
                    businessName = showMainName;
                }
                e.setDesc(BankNameShowConfig.makeBankPublicShowName(bankAccountName, bankAccountNo, businessName));
                e.setBankAccountAcctName(businessName);
                if (!StringUtils.isBlank(bankAccountName)){
                    BankNameEnum bankEnum = BankNameEnum.getBankEnum(bankAccountName);
                    e.setShowBankAccountName(bankEnum.getName());
                    e.setShowAccountName(bankEnum.getName());
                    e.setBankIconUrl(bankEnum.getBankIconUrl());
                    e.setBankBackgroundUrl(bankEnum.getBankBackground());
                    e.setBankAccountNoMask(DataMaskUtils.bankCard(bankAccountNo));
                }
            });
            acctPublicList.setSimpleInfoRespRPCDTOS(simpleInfoRespRPCDTOS);
        }
    }

    /**
     * @Description: 根据企业id查询所有启用和停用的账户主体
     */
    @HttpService(value = "/query/status/list", method = RequestMethod.POST)
    public void queryAcctPublicStatusList(HttpRequest request, HttpResponse response) {
        AcctPublicStatusFindReqVo acctPublicFindReqVo = request.getBodyObject(AcctPublicStatusFindReqVo.class);
        acctPublicFindReqVo.setCompanyId(getUserCompanyId(request));
        acctPublicFindReqVo.checkAcctPublicStatusReq();
        AcctPublicFindRespVo acctPublicList = acctPublicSearchService.queryAcctPublicStatusList(acctPublicFindReqVo);
        ResponseResultUtils.success(response, acctPublicList);
    }
}
