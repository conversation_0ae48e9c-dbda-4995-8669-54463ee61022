package com.fenbeitong.fenbeipay.http.service.group;

import com.fenbeitong.fenbeipay.acctdech.service.*;
import com.fenbeitong.fenbeipay.acctpublic.manager.AcctPublicFlowManager;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublicFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.*;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.na.service.impl.AccountGeneralFlowServiceImpl;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyNewDto;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * @Description: 集团版Service
 * @Author: liyi
 * @Date: 2022/8/8 上午11:08
 */
@Service
@Slf4j
public class GroupService {
    private static final HashMap<String, String> companyMap = new HashMap<>();

    private static Boolean stopFlag = Boolean.FALSE;

    @Autowired
    private ICompanyService iCompanyService;

    @Autowired
    private AccountGeneralFlowServiceImpl accountGeneralFlowService;

    @Autowired
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Autowired
    private AcctBusinessDebitFlowService acctBusinessDebitFlowService;

    @Autowired
    private AcctIndividualCreditFlowService acctIndividualCreditFlowService;

    @Autowired
    private AcctIndividualDebitFlowService acctIndividualDebitFlowService;

    @Autowired
    private AcctPublicFlowManager acctPublicFlowManager;

    @Autowired
    private AcctCompanyCardFlowService acctCompanyCardFlowService;

    /**
     * 清洗旧数据
     * 补充旧流水中操作人企业信息
     */
    public void cleanData(String startTime, String endTime) throws InterruptedException {
        cleanGeneralData(startTime, endTime);
        cleanBusinessCreditData(startTime, endTime);
        cleanBusinessDebitData(startTime, endTime);
        cleanIndividualCreditData(startTime, endTime);
        cleanIndividualDebitData(startTime, endTime);
        cleanCompanyCardData(startTime, endTime);
        cleanPublicData(startTime, endTime);
    }

    private void cleanGeneralData(String startTime, String endTime) throws InterruptedException {
        log.info("===========【开始】清洗余额账户流水数据===============");
        Long id = 0L;
        while (true) {
            List<AccountGeneralFlow> generalFlows =
                    accountGeneralFlowService.getOperatorCompanyIsEmptyData(startTime, endTime, id,  new PageBean(1, 50));
            if (CollectionUtils.isNotEmpty(generalFlows)) {
                for (AccountGeneralFlow flow : generalFlows) {
                    id = flow.getId();
                    String operationUserId = flow.getOperationUserId();
                    if (StringUtils.isBlank(operationUserId) || operationUserId.length() != 24) {
                        continue;
                    }
                    String companyId = flow.getCompanyId();
                    String companyName = companyMap.get(flow.getCompanyId());
                    if (StringUtils.isBlank(companyName)) {
                        CompanyNewDto company = iCompanyService.queryCompanyNewByCompanyId(companyId);
                        if (company == null) {
                            continue;
                        }
                        companyMap.put(company.getId().toString(), company.getCompanyName());
                        companyName = company.getCompanyName();
                    }

                    accountGeneralFlowService.updateOperatorCompanyInfo(flow.getId(), companyId, companyName);
                }
            } else {
                break;
            }
            log.info("-----------余额账户流水已经清洗到【" + id + "】-------------");
            Thread.sleep(100L);
            if (stopFlag) {
                throw new RuntimeException("停止任务");
            }
        }
        log.info("===========【结束】清洗余额账户流水数据===============");
    }

    private void cleanBusinessCreditData(String startTime, String endTime) throws InterruptedException {
        log.info("===========【开始】清洗商务消费授信账户流水数据===============");
        Long id = 0L;
        while (true) {
            List<AcctBusinessCreditFlow>  flows =
                    acctBusinessCreditFlowService.getOperatorCompanyIsEmptyData(startTime, endTime, id,  new PageBean(1, 50));
            if (CollectionUtils.isNotEmpty(flows)) {
                for (AcctBusinessCreditFlow flow : flows) {
                    id = flow.getId();
                    String operationUserId = flow.getOperationUserId();
                    if (StringUtils.isBlank(operationUserId) || operationUserId.length() != 24) {
                        continue;
                    }
                    String companyId = flow.getCompanyId();
                    String companyName = companyMap.get(flow.getCompanyId());
                    if (StringUtils.isBlank(companyName)) {
                        CompanyNewDto company = iCompanyService.queryCompanyNewByCompanyId(companyId);
                        if (company == null) {
                            continue;
                        }
                        companyMap.put(company.getId().toString(), company.getCompanyName());
                        companyName = company.getCompanyName();
                    }

                    acctBusinessCreditFlowService.updateOperatorCompanyInfo(flow.getId(), companyId, companyName);
                }
            } else {
                break;
            }
            log.info("-----------商务消费授信账户流水已经清洗到【" + id + "】-------------");
            Thread.sleep(100L);
            if (stopFlag) {
                throw new RuntimeException("停止任务");
            }
        }
        log.info("===========【结束】清洗商务消费授信账户流水数据===============");
    }

    private void cleanBusinessDebitData(String startTime, String endTime) throws InterruptedException {
        log.info("===========【开始】清洗商务消费充值账户流水数据===============");
        Long id = 0L;
        while (true) {
            List<AcctBusinessDebitFlow> flows = acctBusinessDebitFlowService.getOperatorCompanyIsEmptyData(startTime, endTime, id, new PageBean(1, 50));
            if (CollectionUtils.isNotEmpty(flows)) {
                for (AcctBusinessDebitFlow flow : flows) {
                    id = flow.getId();
                    String operationUserId = flow.getOperationUserId();
                    if (StringUtils.isBlank(operationUserId) || operationUserId.length() != 24) {
                        continue;
                    }
                    String companyId = flow.getCompanyId();
                    String companyName = companyMap.get(flow.getCompanyId());
                    if (StringUtils.isBlank(companyName)) {
                        CompanyNewDto company = iCompanyService.queryCompanyNewByCompanyId(companyId);
                        if (company == null) {
                            continue;
                        }
                        companyMap.put(company.getId().toString(), company.getCompanyName());
                        companyName = company.getCompanyName();
                    }

                    acctBusinessDebitFlowService.updateOperatorCompanyInfo(flow.getId(), companyId, companyName);
                }
            } else {
                break;
            }
            log.info("-----------商务消费充值账户流水已经清洗到【" + id + "】-------------");
            Thread.sleep(100L);
            if (stopFlag) {
                throw new RuntimeException("停止任务");
            }
        }
        log.info("===========【结束】清洗商务消费充值账户流水数据===============");
    }

    private void cleanIndividualCreditData(String startTime, String endTime) throws InterruptedException {
        log.info("===========【开始】清洗个人消费授信账户流水数据===============");
        Long id = 0L;
        while (true) {
            List<AcctIndividualCreditFlow> flows = acctIndividualCreditFlowService.getOperatorCompanyIsEmptyData(startTime, endTime, id, new PageBean(1, 50));
            if (CollectionUtils.isNotEmpty(flows)) {
                for (AcctIndividualCreditFlow flow : flows) {
                    id = flow.getId();
                    String operationUserId = flow.getOperationUserId();
                    if (StringUtils.isBlank(operationUserId) || operationUserId.length() != 24) {
                        continue;
                    }
                    String companyId = flow.getCompanyId();
                    String companyName = companyMap.get(flow.getCompanyId());
                    if (StringUtils.isBlank(companyName)) {
                        CompanyNewDto company = iCompanyService.queryCompanyNewByCompanyId(companyId);
                        if (company == null) {
                            continue;
                        }
                        companyMap.put(company.getId().toString(), company.getCompanyName());
                        companyName = company.getCompanyName();
                    }

                    AcctIndividualCreditFlow acctIndividualCreditFlow = new AcctIndividualCreditFlow();
                    acctIndividualCreditFlow.setId(flow.getId());
                    acctIndividualCreditFlow.setOperationUserCompanyId(companyId);
                    acctIndividualCreditFlow.setOperationUserCompanyName(companyName);
                    acctIndividualCreditFlowService.updateOperatorCompanyInfo(flow.getId(), companyId, companyName);
                }
            } else {
                break;
            }
            log.info("-----------个人消费授信账户流水已经清洗到【" + id + "】-------------");
            Thread.sleep(100L);
            if (stopFlag) {
                throw new RuntimeException("停止任务");
            }
        }
        log.info("===========【结束】清洗个人消费授信账户流水数据===============");
    }

    private void cleanIndividualDebitData(String startTime, String endTime) throws InterruptedException {
        log.info("===========【开始】清洗个人消费充值账户流水数据===============");
        Long id = 0L;
        while (true) {
            List<AcctIndividualDebitFlow> flows =
                    acctIndividualDebitFlowService.getOperatorCompanyIsEmptyData(startTime, endTime, id,  new PageBean(1, 50));
            if (CollectionUtils.isNotEmpty(flows)) {
                for (AcctIndividualDebitFlow flow : flows) {
                    id = flow.getId();
                    String operationUserId = flow.getOperationUserId();
                    if (StringUtils.isBlank(operationUserId) || operationUserId.length() != 24) {
                        continue;
                    }
                    String companyId = flow.getCompanyId();
                    String companyName = companyMap.get(flow.getCompanyId());
                    if (StringUtils.isBlank(companyName)) {
                        CompanyNewDto company = iCompanyService.queryCompanyNewByCompanyId(companyId);
                        if (company == null) {
                            continue;
                        }
                        companyMap.put(company.getId().toString(), company.getCompanyName());
                        companyName = company.getCompanyName();
                    }

                    acctIndividualDebitFlowService.updateOperatorCompanyInfo(flow.getId(), companyId, companyName);
                }
            } else {
                break;
            }
            log.info("-----------个人消费充值账户流水已经清洗到【" + id + "】-------------");
            Thread.sleep(100L);
            if (stopFlag) {
                throw new RuntimeException("停止任务");
            }
        }
        log.info("===========【结束】清洗个人消费充值账户流水数据===============");
    }

    private void cleanPublicData(String startTime, String endTime) throws InterruptedException {
        log.info("===========【开始】清洗对公付款账户流水数据===============");
        Long id = 0L;
        while (true) {
            List<AccountPublicFlow> flows = acctPublicFlowManager.getOperatorCompanyIsEmptyData(startTime, endTime, id, new PageBean(1, 50));
            if (CollectionUtils.isNotEmpty(flows)) {
                for (AccountPublicFlow flow : flows) {
                    id = flow.getId();
                    String operationUserId = flow.getOperationUserId();
                    if (StringUtils.isBlank(operationUserId) || operationUserId.length() != 24) {
                        continue;
                    }
                    String companyId = flow.getCompanyId();
                    String companyName = companyMap.get(flow.getCompanyId());
                    if (StringUtils.isBlank(companyName)) {
                        CompanyNewDto company = iCompanyService.queryCompanyNewByCompanyId(companyId);
                        if (company == null) {
                            continue;
                        }
                        companyMap.put(company.getId().toString(), company.getCompanyName());
                        companyName = company.getCompanyName();
                    }

                    acctPublicFlowManager.updateOperatorCompanyInfo(flow.getId(), companyId, companyName);
                }
            } else {
                break;
            }
            log.info("-----------对公付款账户流水已经清洗到【" + id + "】-------------");
            Thread.sleep(100L);
            if (stopFlag) {
                throw new RuntimeException("停止任务");
            }
        }
        log.info("===========【结束】清洗对公付款账户流水数据===============");
    }

    private void cleanCompanyCardData(String startTime, String endTime) throws InterruptedException {
        log.info("===========【开始】清洗备用金账户流水数据===============");
        Long id = 0L;
        while (true) {
            List<AcctCompanyCardFlow> flows = acctCompanyCardFlowService.getOperatorCompanyIsEmptyData(startTime, endTime, id, new PageBean(1, 50));
            if (CollectionUtils.isNotEmpty(flows)) {
                for (AcctCompanyCardFlow flow : flows) {
                    id = flow.getId();
                    String operationUserId = flow.getOperationUserId();
                    if (StringUtils.isBlank(operationUserId) || operationUserId.length() != 24) {
                        continue;
                    }
                    String companyId = flow.getCompanyId();
                    String companyName = companyMap.get(flow.getCompanyId());
                    if (StringUtils.isBlank(companyName)) {
                        CompanyNewDto company = iCompanyService.queryCompanyNewByCompanyId(companyId);
                        if (company == null) {
                            continue;
                        }
                        companyMap.put(company.getId().toString(), company.getCompanyName());
                        companyName = company.getCompanyName();
                    }

                    acctCompanyCardFlowService.updateOperatorCompanyInfo(flow.getId(), companyId, companyName);
                }
            } else {
                break;
            }
            log.info("-----------备用金账户流水已经清洗到【" + id + "】-------------");
            Thread.sleep(100L);
            if (stopFlag) {
                throw new RuntimeException("停止任务");
            }
        }
        log.info("===========【结束】清洗备用金账户流水数据===============");
    }


    public void stopCleanData() {
        stopFlag = Boolean.TRUE;
    }

    public void startCleanData() {
        stopFlag = Boolean.FALSE;
    }
}
