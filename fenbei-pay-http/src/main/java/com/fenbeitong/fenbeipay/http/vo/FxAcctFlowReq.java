package com.fenbeitong.fenbeipay.http.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class FxAcctFlowReq implements Serializable {

	private static final long serialVersionUID = 4066256918196532039L;

	/**
	 * 企业id
	 */
	private String companyId;
	
	/**
     * 账户ID
     */
    private String accountId;
    
    /**
     * 流水ID
     */
    private String accountFlowId;
    
    /**
     * 交易编码
     */
    private String bizNo;
    
    /**
	 * 交易时间开始
	 */
	private Date tradeTimeFrom;
	
	/**
	 * 交易时间截止
	 */
	private Date tradeTimeTo;
	
	/**
	 * 交易时间开始
	 */
	private Date utcTradeTimeFrom;
	
	/**
	 * 交易时间截止
	 */
	private Date utcTradeTimeTo;
	
	/**
	 * 交易流水号
	 */
	private String bankTransNo;
	
	/**
     * 操作人id
     */
    private String operationUserId;
        
    /**
     * 操作人姓名
     */
    private String operationUserName;
    
    /**
     * 操作人所在公司
     */
    private String operationUserCompanyName;
    
    /**
     * 操作人所在公司
     */
    private String operationUserCompanyId;
    
    /**
     * 业务类型
     */
    private Integer bizType;
    
    /**
     * 交易类型
     */
    private Integer tradeType;
    
    /**
     * 平台
     */
    private String channel;
    
    /**
     * 账户类型
     */
    private Integer acctType;
    
    @NotNull
	@Min(1)
	private Integer pageNo = 1;

    @NotNull
	@Max(100)
	private Integer pageSize = 20;
}
