package com.fenbeitong.fenbeipay.http.controller.newaccount.dto;

import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountSubOperationType;
import com.luastar.swift.base.utils.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: java类作用描述
 * @ClassName: AccountSubFlowVoForExport
 * @Author: zhangga
 * @CreateDate: 2019/3/18 2:57 PM
 * @UpdateUser:
 * @UpdateDate: 2019/3/18 2:57 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class AccountSubFlowVoForExport {

    private String accountFlowId;

    /**
     * 交易类型 ：1-开户;21-总账户转入;22-总账户还款;23-转回总账户;41-因公消费;42-因公消费退款;81-分贝券发放;82-分贝券回收;6-冻结;7-解冻
     */
    private Integer operationType;
    private String operationTypeName;

    /**
     * 交易金额 单位：分
     */
    private BigDecimal operationAmount;

    /**
     * 交易说明
     */
    private String operationDescription;

    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 创建时间
     */
    private Date createTime;
    private String operationTime;

    public String getOperationTypeName() {
        return operationType != null ? AccountSubOperationType.getEnum(operationType).getValue() : operationTypeName;
    }

    public String getOperationTime() {
        return operationTime = DateUtils.format(createTime);
    }
}