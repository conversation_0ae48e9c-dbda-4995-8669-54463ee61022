package com.fenbeitong.fenbeipay.http.controller.extract;

import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayManualRecordReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayShowReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.ExtractDayShowRespDTO;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractDayService;
import com.fenbeitong.fenbeipay.dto.extract.ExtractDayManualRecord;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.fenbeipay.rpc.service.extract.IAcctExtractDayServiceImpl;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

@HttpService("/fbp/web/extractDay")
public class ExtractDayShowController extends BaseController {


    @Autowired
    private IAcctExtractDayService iAcctExtractDayService;

    @Autowired
    IAcctExtractDayServiceImpl iAcctExtractDayServiceImpl;

    /**
     * 日终余额列表查询（如果为空，填充--）
     * @param request
     * @param response
     */
    @HttpService(value = "/getList", method = RequestMethod.POST)
    public void getList(HttpRequest request, HttpResponse response) {
        ExtractDayShowReqDTO queryReq = request.getBodyObject(ExtractDayShowReqDTO.class);
        ExtractDayShowRespDTO resp = iAcctExtractDayService.query(queryReq);
        ResponseResultUtils.success(response, resp);
    }

    /**
     * 获取是否执行过手动触发记录
     * @param request
     * @param response
     */
    @HttpService(value = "/getManualRecord", method = RequestMethod.POST)
    public void getManualRecord(HttpRequest request, HttpResponse response) {
        ExtractDayManualRecordReqDTO queryReq = request.getBodyObject(ExtractDayManualRecordReqDTO.class);
        ExtractDayManualRecord record = iAcctExtractDayServiceImpl.getManualRecord(queryReq);
        ResponseResultUtils.success(response, record);
    }

    /**
     * 用户点击触发获取日终余额，保存到数据库
     * @param request
     * @param response
     */
    @HttpService(value = "/addManualRecord", method = RequestMethod.POST)
    public void addManualRecord(HttpRequest request, HttpResponse response) {
        ExtractDayManualRecordReqDTO queryReq = request.getBodyObject(ExtractDayManualRecordReqDTO.class);
        ExtractDayManualRecord record = iAcctExtractDayServiceImpl.addManualRecord(queryReq);
        ResponseResultUtils.success(response, record);
    }

}
