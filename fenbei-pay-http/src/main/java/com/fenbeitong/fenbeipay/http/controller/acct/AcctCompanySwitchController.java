package com.fenbeitong.fenbeipay.http.controller.acct;

import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctCompanySwitchReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctCompanySwitchRespDTO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctCompanySwitchService;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.google.common.collect.Maps;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 企业切换新账户白名单
 *
 * @Author: wh
 * @Date: 2021/2/26 2:45 PM
 */
@HttpService("/fbp/acct/company/switch")
public class AcctCompanySwitchController extends BaseController {

    @Autowired
    private IAcctCompanySwitchService iAcctCompanySwitchService;
    final static int COMPANY_NEW_ACCT=1;
    final static int COMPANY_Old_ACCT=2;


    /**
     * 是否切换新的账户企业某账户
     * 1是新资金架构 2是老资金系统
     * @param request
     * @param response
     */
    @HttpService(value = "/acct/company", method = RequestMethod.POST)
    public void querySwitchIsCompany(HttpRequest request, HttpResponse response) {
        String companyId = getOperationCompanyId(request);
        Map<String, Object> respMap = Maps.newHashMap();
        respMap.put("companySwitchType",  iAcctCompanySwitchService.isCompanySwitch(companyId)?COMPANY_NEW_ACCT:COMPANY_Old_ACCT);
        ResponseResultUtils.success(response, respMap);
    }

    /**
     * 是否切换新的账户企业某账户
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/is/company", method = RequestMethod.POST)
    public void baseAccountFlow(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        AcctCompanySwitchReqDTO reqDTO = request.getBodyObject(AcctCompanySwitchReqDTO.class);
        reqDTO.setCompanyId(companyId);
        Map<String, Object> respMap = Maps.newHashMap();
        respMap.put("isCompanySwitch", iAcctCompanySwitchService.isAcctCompanySwitch(reqDTO));
        ResponseResultUtils.success(response, respMap);
    }
    /**
     * 查询是否切换新的账户企业
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/query/company", method = RequestMethod.POST)
    public void queryCompanySwitch(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        AcctCompanySwitchReqDTO reqDTO = request.getBodyObject(AcctCompanySwitchReqDTO.class);
        reqDTO.setCompanyId(companyId);
        AcctCompanySwitchRespDTO acctCompanySwitchRespDTO = iAcctCompanySwitchService.queryCompanySwitch(reqDTO);
        ResponseResultUtils.success(response, acctCompanySwitchRespDTO);
    }

    /**
     * 是否切换新的账户某企业
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/is/companyId", method = RequestMethod.POST)
    public void isCompanySwitch(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        Map<String, Object> respMap = Maps.newHashMap();
        respMap.put("isCompanySwitch", iAcctCompanySwitchService.isCompanySwitch(companyId));
        ResponseResultUtils.success(response, respMap);
    }


    /**
     * 根据公司id返回切换后的白名单数据
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/find/companyId", method = RequestMethod.POST)
    public void findCompanySwitch(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        List<AcctCompanySwitchRespDTO> companySwitch = iAcctCompanySwitchService.findCompanySwitch(companyId);
        ResponseResultUtils.success(response, companySwitch);
    }


}
