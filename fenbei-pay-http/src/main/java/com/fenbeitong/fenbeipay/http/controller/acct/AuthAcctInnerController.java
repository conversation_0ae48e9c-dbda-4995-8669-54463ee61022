package com.fenbeitong.fenbeipay.http.controller.acct;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessDebitFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctFlowStereoPageServiceReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctFlowExportRespDTO;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.dto.AcctFlowBaseDTO;
import com.fenbeitong.fenbeipay.core.model.dto.AcctFlowWithBillStereoDTO;
import com.fenbeitong.fenbeipay.core.model.dto.BillFlowPageQuery;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowADExample;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.service.bigdata.AccountBillFlowService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.google.common.collect.Lists;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Author: guogx
 * @Date: 2022/8/4 下午3:11
 */
@HttpService("/auth/internal/acct/company")
public class AuthAcctInnerController extends AcctInnerController{

    @Autowired
    private UAcctCommonService acctCommonService;
    
    @Resource
    private AccountBillFlowService accountBillFlowService;
    
    @Autowired
    private UAcctBusinessDebitFlowService acctBusinessDebitFlowService;

    @HttpService(value = "/import/general/flow", method = RequestMethod.POST)
    @Override
    public void baseAccountFlow(HttpRequest request, HttpResponse response) {
        verifyIdentity(request);
        super.baseAccountFlow(request, response);
    }

    @HttpService(value = "/import/business/debit/flow", method = RequestMethod.POST)
    @Override
    public void rechargeFlow(HttpRequest request, HttpResponse response) {
        verifyIdentity(request);
        super.rechargeFlow(request, response);
    }


    @HttpService(value = "/import/individual/debit/flow", method = RequestMethod.POST)
    @Override
    public void creditFlow(HttpRequest request, HttpResponse response) {
        verifyIdentity(request);
        super.creditFlow(request, response);
    }


    @HttpService(value = "/import/card/flow", method = RequestMethod.POST)
    @Override
    public void companyCardFlow(HttpRequest request, HttpResponse response) {
        verifyIdentity(request);
        super.companyCardFlow(request, response);
    }


    @HttpService(value = "/import/stereo/acct/flow", method = RequestMethod.POST)
    @Override
    public void importAcctFlowSearchStereoPage(HttpRequest request, HttpResponse response) {
        String companyId = verifyCompanyId(request);
        //底层接口未校验companyId，故此处仅在传参不为空时校验权限
        AcctFlowStereoPageServiceReqDTO accountSubVo = request.getBodyObject(AcctFlowStereoPageServiceReqDTO.class);
        if(isCommonAcct(request) && StringUtils.isNotBlank(accountSubVo.getCompanyId()) && !accountSubVo.getCompanyId().equals(companyId)){
            throw new FinPayException(GlobalResponseCode.TOKEN_EXPIRE);
        }
        super.importAcctFlowSearchStereoPage(request, response);
    }


    @HttpService(value = "/import/public/flow", method = RequestMethod.POST)
    @Override
    public void acctPublicFlow(HttpRequest request, HttpResponse response) {
        verifyIdentity(request);
        super.acctPublicFlow(request, response);
    }

    @HttpService(value = "/import/redcoupon/flow", method = RequestMethod.POST)
    @Override
    public void redcouponFlow(HttpRequest request, HttpResponse response) {
        verifyIdentity(request);
        super.redcouponFlow(request, response);
    }


    /**
     * @Description: 迁移前接口/api/stereo/web/flow/log/export/task(在stereo里)
     * @Author: guogx
     * @Date: 2022/8/11 上午11:25
     */
    @HttpService(value = "/export/flow", method = RequestMethod.POST)
    public void companyFlow(HttpRequest request, HttpResponse response) {
        //检验权限
        verifyIdentity(request);

        BillFlowPageQuery query = request.getBodyObject(BillFlowPageQuery.class);
        query.setStereo(false);
        query.setDirectFromWeb(true);
        //校验参数
        query.checkExportTaskParam();
        
        if (Objects.nonNull(query.getTargetAcctCode())) {
        	List<String> collectionAccts = acctBusinessDebitFlowService.getAccountNoByTargetAcctCode(query.getTargetAcctCode());
        	query.setTargetAccounts(collectionAccts);
        }

        FinhubLogger.info("AuthAcctInnerController#companyFlow#query:{}", JSON.toJSONString(query));
        //从大数据库获取数据
        HoloAdsAccountAllFlowADExample.Criteria criteria = accountBillFlowService.buildBillFlowSearchForWeb(query);
        List<AcctFlowBaseDTO> acctFlowBaseDTOList = accountBillFlowService.queryBillFlowPageData4Export(criteria, query);

        //处理数据
        List<AcctFlowBaseDTO> acctFlowBaseDTOListNew = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(acctFlowBaseDTOList)){
            for(AcctFlowBaseDTO acctFlowBaseDTO : acctFlowBaseDTOList){
                if(CollectionUtils.isNotEmpty(acctFlowBaseDTO.getBillSummary())){
                    for(AcctFlowBaseDTO.SummaryInfoBean summaryInfoBean :acctFlowBaseDTO.getBillSummary()){
                        AcctFlowWithBillStereoDTO acctFlowBaseDTONew = new AcctFlowWithBillStereoDTO();
                        BeanUtils.copyProperties(acctFlowBaseDTO, acctFlowBaseDTONew);
                        acctFlowBaseDTONew.setBillNo(summaryInfoBean.getBillNo());
                        acctFlowBaseDTONew.setOrderId(summaryInfoBean.getOrderId());
                        acctFlowBaseDTONew.setProductId(summaryInfoBean.getProductId());
                        acctFlowBaseDTONew.setBillDate(summaryInfoBean.getBillDate());
                        acctFlowBaseDTONew.setTotalAmount(summaryInfoBean.getTotalAmount());
                        if(summaryInfoBean.getUserVisibleState() != null){
                            acctFlowBaseDTONew.setUserVisibleState(summaryInfoBean.getUserVisibleState().getValue());
                        }
                        acctFlowBaseDTOListNew.add(acctFlowBaseDTONew);
                    }
                }else{
                    AcctFlowWithBillStereoDTO acctFlowBaseDTONew = new AcctFlowWithBillStereoDTO();
                    BeanUtils.copyProperties(acctFlowBaseDTO, acctFlowBaseDTONew);
                    acctFlowBaseDTOListNew.add(acctFlowBaseDTONew);
                }
            }
        }

        //返回数据
        ResponseResultUtils.success(response, acctFlowBaseDTOListNew);
    }

    @HttpService(value = "/export/excel", method = RequestMethod.POST)
    public void exportCompanyFlow(HttpRequest request, HttpResponse response) {
        //检验权限
        verifyIdentity(request);
        
        BillFlowPageQuery requestParam = request.getBodyObject(BillFlowPageQuery.class);
        requestParam.setStereo(false);
        requestParam.setDirectFromWeb(true);
        //校验参数
        requestParam.checkExportTaskParam();
        
        if (Objects.nonNull(requestParam.getTargetAcctCode())) {
            List<String> collectionAccts = acctBusinessDebitFlowService.getAccountNoByTargetAcctCode(requestParam.getTargetAcctCode());
            requestParam.setTargetAccounts(collectionAccts);
        }
        
        UserInfoVO userInfo = getUserInfo(request);
        requestParam.setUserId(userInfo.getId());
        requestParam.setUserName(userInfo.getName());
        requestParam.setCompanyId(getUserCompanyId(request));
        
        FinhubLogger.info("AuthAcctInnerController#exportCompanyFlow:{}", JSON.toJSONString(requestParam));
        //从大数据库获取数据
        AcctFlowExportRespDTO resp = acctCommonService.exportCompanyBillExcel(requestParam);        
        //返回数据
        ResponseResultUtils.success(response, resp);
    }
    
}
