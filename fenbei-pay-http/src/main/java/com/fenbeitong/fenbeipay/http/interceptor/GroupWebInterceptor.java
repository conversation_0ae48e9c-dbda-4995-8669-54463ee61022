package com.fenbeitong.fenbeipay.http.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.GroupAcctReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.FxCompanyAcctInfo;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.AccountInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyAccountInfo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.auth.constant.UserAttributeConstant;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.entity.SwiftHashMap;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.route.HandlerInterceptor;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import io.netty.handler.codec.http.HttpMethod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

/**
 * 集团版权限校验
 * <AUTHOR>
 * @date 2022-06-30 19:55:38
 */
public class GroupWebInterceptor implements HandlerInterceptor {

    @Autowired
    private UAcctCommonService uAcctCommonService;

    @Override
    public boolean preHandle(HttpRequest request, HttpResponse response) throws FinhubException {
        FinhubLogger.debug("GroupWebInterceptor[preHandle]");

        // 获取登录信息
        UserComInfoVO userComInfo = (UserComInfoVO) request.getAttribute(UserAttributeConstant.USER_COM_INFO);
        try {
            FinhubLogger.debug("groupWebInterceptor url:{}, user_info:{}", request.getUri(), JsonUtils.toJson(userComInfo));
            String menuCode = request.getHeader(CoreConstant.ACCT_GROUP_MENU_CODE);
            if (userComInfo != null && StringUtils.isNotBlank(menuCode) && menuCode.startsWith(CoreConstant.ACCT_GROUP_MENU_CODE_SUFFIX)) {
                // 集团版权限校验
                String groupCompanyId = getParameter(request, CoreConstant.ACCT_GROUP_COMPANY_ID);
                String groupAccountId = getParameter(request, CoreConstant.ACCT_GROUP_ACCOUNT_ID);
                String bankAccountNo = getParameter(request, CoreConstant.ACCT_GROUP_BANK_ACCOUNT_NO);
                List<CompanyAccountInfo> companyAccountInfos = uAcctCommonService.queryGroupAcctAuthList(new GroupAcctReqDTO(userComInfo.getGroup_id(), userComInfo.getCompany_id(), userComInfo.getUser_id(), menuCode));
                request.setAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS, companyAccountInfos);
                request.setAttribute(CoreConstant.ACCT_GROUP_MENU_CODE, menuCode);
                authCheck(companyAccountInfos, userComInfo, groupCompanyId, groupAccountId, bankAccountNo);
            }
        } catch (FinPayException e) {
            FinhubLogger.error("GroupWebInterceptor FinhubException url:{}, user_info:{}", request.getUri(), JsonUtils.toJson(userComInfo), e);
            throw e;
        } catch (Exception e) {
            FinhubLogger.error("GroupWebInterceptor exception url:{}, user_info:{}", request.getUri(), JsonUtils.toJson(userComInfo), e);
            throw e;
        }
        return true;
    }

    @Override
    public void postHandle(HttpRequest request, HttpResponse response) throws FinhubException {
        FinhubLogger.debug("GroupWebInterceptor[postHandle]");
    }

    /**
     * 权限校验
     * <AUTHOR>
     * @date 2022-07-01 11:35:55
     */
    private void authCheck(List<CompanyAccountInfo> companyAccountInfos, UserComInfoVO userComInfo, String groupCompanyId, String groupAccountId, String bankAccountNo) {
        if (StringUtils.isNotBlank(groupCompanyId)) {
            Optional<CompanyAccountInfo> companyAccountOptional = companyAccountInfos.stream().filter(v -> v.getCompanyId().equals(groupCompanyId) && !v.getAcctAuthTypeEnum().equals(CompanyAccountInfo.AcctAuthTypeEnum.NO_AUTH)).findFirst();
            // 判断是否有公司权限
            boolean hasCompanyAccount = companyAccountOptional.isPresent();
            if (!hasCompanyAccount) {
                FinhubLogger.error("GroupWebInterceptor authCheck company hasCompanyAccount fail,groupCompanyId:{},groupAccountId:{},userComInfo:{}", groupCompanyId, groupAccountId, JSONObject.toJSONString(userComInfo));
                throw new FinPayException(GlobalResponseCode.GROUP_ACCOUNT_AUTH_FAIL);
            }
            CompanyAccountInfo companyAccountInfo = companyAccountOptional.get();
            if (CompanyAccountInfo.AcctAuthEnum.NO_AUTH.equals(companyAccountInfo.getAcctAuthEnum()) && CompanyAccountInfo.AcctAuthTypeEnum.NO_AUTH.equals(companyAccountInfo.getAcctAuthTypeEnum())) {
                FinhubLogger.error("GroupWebInterceptor authCheck company fail,groupCompanyId:{},groupAccountId:{},userComInfo:{}", groupCompanyId, groupAccountId, JSONObject.toJSONString(userComInfo));
                throw new FinPayException(GlobalResponseCode.GROUP_ACCOUNT_AUTH_FAIL);
            }

            // 判断是否有操作账户权限
            List<AccountInfoVO> accounts = companyAccountInfo.getAccounts();
            List<FxCompanyAcctInfo> fxAccounts = companyAccountInfo.getFxAccounts();
            if (StringUtils.isNotBlank(groupAccountId)) {
                if (CollectionUtils.isEmpty(accounts) && CollectionUtils.isEmpty(fxAccounts)) {
                    FinhubLogger.error("GroupWebInterceptor authCheck account fail,groupCompanyId:{},groupAccountId:{},userComInfo:{}", groupCompanyId, groupAccountId, JSONObject.toJSONString(userComInfo));
                    throw new FinPayException(GlobalResponseCode.GROUP_ACCOUNT_AUTH_FAIL);
                }
                boolean hasAccount = accounts.stream().filter(v -> v.getAccountId().equals(groupAccountId)).findFirst().isPresent();
                boolean hasFxAccount = fxAccounts.stream().filter(v -> v.getAccountId().equals(groupAccountId)).findFirst().isPresent();
                if (!hasAccount && !hasFxAccount) {
                    FinhubLogger.error("GroupWebInterceptor authCheck account fail,groupCompanyId:{},groupAccountId:{},userComInfo:{}", groupCompanyId, groupAccountId, JSONObject.toJSONString(userComInfo));
                    throw new FinPayException(GlobalResponseCode.GROUP_ACCOUNT_AUTH_FAIL);
                }
            }

            // 判断是否有操作银行卡权限
            if (StringUtils.isNotBlank(bankAccountNo)) {
                if (CollectionUtils.isEmpty(accounts)) {
                    FinhubLogger.error("GroupWebInterceptor authCheck account fail,groupCompanyId:{},groupAccountId:{},userComInfo:{}", groupCompanyId, groupAccountId, JSONObject.toJSONString(userComInfo));
                    throw new FinPayException(GlobalResponseCode.GROUP_ACCOUNT_AUTH_FAIL);
                }
                boolean hasAccount = accounts.stream().filter(v -> v.getBankAccountNo().equals(bankAccountNo)).findFirst().isPresent();
                if (!hasAccount) {
                    FinhubLogger.error("GroupWebInterceptor authCheck account fail,groupCompanyId:{},groupAccountId:{},userComInfo:{}", groupCompanyId, groupAccountId, JSONObject.toJSONString(userComInfo));
                    throw new FinPayException(GlobalResponseCode.GROUP_ACCOUNT_AUTH_FAIL);
                }
            }
        }
    }

    /**
     * 获取参数
     * <AUTHOR>
     * @date 2022-07-12 14:03:18
     */
    private String getParameter(HttpRequest request, String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        if (HttpMethod.GET.name().equalsIgnoreCase(request.getMethod())) {
            return request.getParameter(key);
        }
        if (HttpMethod.POST.name().equalsIgnoreCase(request.getMethod())) {
            SwiftHashMap<String, Object> bodyMap = request.getBodyMap();
            if (bodyMap == null) {
                return null;
            }
            return bodyMap.getString(key);
        }
        return null;
    }
}
