package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.api.service.bank.cgb.IBankCgbAccountService;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 升级
 */
@HttpService("/internal/test")
@Slf4j
public class VirtualCardInnerUpgradeController {

    @Autowired
    IBankCgbAccountService iBankCgbAccountService;

    /**
     * 虚拟卡人工圈存
     * @param request 请求
     * @param response 返回
     */
    @HttpService(value = "/virtual/trap",method = RequestMethod.GET)
    public void virtualTrap(HttpRequest request, HttpResponse response)  {
//        iBankCgbAccountService.firstTrap();
    }

    /**
     * 虚拟卡人工圈存
     * @param request 请求
     * @param response 返回
     */
    @HttpService(value = "/virtual/first/trap",method = RequestMethod.GET)
    public void firstTrap(HttpRequest request, HttpResponse response)  {
//        iBankCgbAccountService.addTrap();
    }

    @HttpService(value = "/virtual/solve/trap",method = RequestMethod.GET)
    public void virtualSolveTrap(HttpRequest request, HttpResponse response)  {
//        iBankCgbAccountService.solveTrap4Upgrade();
    }

}
