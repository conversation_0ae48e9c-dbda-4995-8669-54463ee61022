package com.fenbeitong.fenbeipay.http.controller.cashier;


import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderRootTypeEnum;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderSettlementService;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.MessageFormat;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.COMPANY_ID;
import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.USER_ID;

/**
 * 收银台Http服务退款Controller
 * RPC服务请参考fenbei-pay-api
 * @version  2018年11月19日10:57:06
 * <AUTHOR>
 */

@HttpService("/cashier/refund")
public class CashierOrderRefundSettlementController {

    /**强制解锁时间设置*/
    private long LOCK_TIME_REFUND = 10000L;

    /**等待时间**/
    private long WAIT_TIME_REFUND = 400L;


    @Autowired
    private CashierOrderRefundSettlementService cashierOrderRefundSettlementService;

    @Autowired
    private CashierOrderSettlementService cashierOrderSettlementService;


    @Autowired
    protected RedissonService redissonService;

    /**
     * （该接口为RPC,同时提供HTTP仅作为测试用）
     * (RPC) 场景OC系统调用，生成退款交易流水号
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/refundtrade/v2", method = RequestMethod.POST)
    public void refundTrade(HttpRequest request, HttpResponse response) {
        CashierRefundTradeReqVo cashierRefundTradeReqVo = request.getBodyObject(CashierRefundTradeReqVo.class);
        final String lockKey = MessageFormat.format(RedisKeyConstant.CASHIER_REFUND_ORDERID_KEY, cashierRefundTradeReqVo.getFbOrderId());
        try {
            cashierRefundTradeReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
            cashierRefundTradeReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
            //加锁
            boolean lock = redissonService.tryLock(WAIT_TIME_REFUND, LOCK_TIME_REFUND, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.info("加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            CashierRefundTradeRespVo cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTradeAndSaas(cashierRefundTradeReqVo);
            cashierOrderRefundSettlementService.checkRefundStatusAndCallBiz(cashierRefundTradeRespVo.getRefundTxnId(), cashierRefundTradeReqVo.getEmployeeId());
            ResponseResultUtils.success(response, cashierRefundTradeRespVo);
        } catch (InterruptedException e) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
        } catch (Exception e) {
            throw e;
        } finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("释放锁失败：{}", lockKey, e);
            }
        }
    }

    /**
     * 退款详情查询
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/queryrefund/detail/v2",method = RequestMethod.POST)
    public void getRefundDetail(HttpRequest request, HttpResponse response){
        CashierRefundQueryReqVo refundQueryReqVo = request.getBodyObject(CashierRefundQueryReqVo.class);
        refundQueryReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        refundQueryReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        List<CashierRefundQueryRespVo> refundSettlements = cashierOrderRefundSettlementService.getRefundDetail(refundQueryReqVo);
        ResponseResultUtils.success(response,refundSettlements);
    }

    /**
     * （HTTP）交易详情查询
     * @param request
     * @param response
     * @return
     * @since V4.5.0
     */
    @HttpService(value = "/queryrefund/detail/reimburse/v1",method = RequestMethod.POST)
    public void getRefundDetailReimburse(HttpRequest request, HttpResponse response){
        CashierRefundQueryReqVo refundQueryReqVo = request.getBodyObject(CashierRefundQueryReqVo.class);
        refundQueryReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        refundQueryReqVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        CashierOrderRefundSettlement refundSettlement = cashierOrderRefundSettlementService.queryRefund(refundQueryReqVo.getRefundOrderId(), refundQueryReqVo.getFbOrderId(), refundQueryReqVo.getEmployeeId(), refundQueryReqVo.getCompanyId());
        CashierOrderSettlement settlement = cashierOrderSettlementService.selectCashierSettlementByFbOrderIdAndEmpId(refundQueryReqVo.getFbOrderId(),refundQueryReqVo.getEmployeeId(), OrderRootTypeEnum.ROOT_ORDER.getKey());
        CashierRefundReimburseQueryRespVo reimburseQueryRespVo = new CashierRefundReimburseQueryRespVo();
        BeanUtils.copyProperties(refundSettlement,reimburseQueryRespVo);
        reimburseQueryRespVo.setAmountAll(settlement.getAmountAll());
        ResponseResultUtils.success(response,reimburseQueryRespVo);
    }


}
