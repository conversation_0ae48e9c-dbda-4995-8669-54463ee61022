package com.fenbeitong.fenbeipay.http.controller.acct;

import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.BankUserCardReqDTO;
import com.fenbeitong.acctperson.api.service.trade.IBankUserCardTradeService;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankCardStatus;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.SearchCardManager;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName BankCardControllerTest
 * <AUTHOR>
 * @Date 2021/4/26
 **/
@HttpService("/internal/bankCard/test")
public class BankCardControllerTest {

    @Autowired
    private SearchCardManager searchCardManager;
    @Autowired
    private IBankUserCardTradeService iBankUserCardTradeService;

    @HttpService(value = "/createBankUserCard", method = RequestMethod.POST)
    public void createBankUserCard(HttpRequest request, HttpResponse response) {
        List<BankCard> bankCards = searchCardManager.queryByBankName("ZBBANK");
        if(ObjUtils.isEmpty(bankCards)){
            return;
        }
        int success = 0;
        for (BankCard bankCard : bankCards) {
            BankUserCardReqDTO bankUserCard = new BankUserCardReqDTO();
            BeanUtils.copyProperties(bankCard, bankUserCard);
            bankUserCard.setUserType(Integer.valueOf(bankCard.getUserType()));
            bankUserCard.setTransferCardSum(BigDecimal.ZERO);
            bankUserCard.setCardBalance(BigDecimal.ZERO);
            bankUserCard.setPayCardSum(BigDecimal.ZERO);
            bankUserCard.setRefundCardSum(BigDecimal.ZERO);
            bankUserCard.setCardStatus(BankCardStatus.NORMAL.getKey());
            bankUserCard.setCreateTime(new Date());
            bankUserCard.setUpdateTime(new Date());
            Boolean aBoolean = iBankUserCardTradeService.saveBankUserCard(bankUserCard);
            if(aBoolean){
                success = success + 1;
            }
        }
        int fail = bankCards.size() - success;
        String msg = "【批量创建个人资金账户】【总条数" + bankCards.size() + "条】【成功" + success + "条】【失败" + fail +"条】";
        ResponseResultUtils.success(response, msg);
    }

    /**
     * 人工创建
     * @param request
     * @param response
     */
    @HttpService(value = "/create/bank/user/card/manual", method = RequestMethod.POST)
    public void createBankUserCardByManual(HttpRequest request, HttpResponse response) {
        String companyId = "";
        if(!ObjUtils.isEmpty(request.getBodyMap())){
            companyId = request.getBodyMap().getString("companyId");
        }
        List<BankCard> bankCards = searchCardManager.queryByCompanyId(companyId,"CGB");
        if(ObjUtils.isEmpty(bankCards)){
            return;
        }
        int success = 0;
        for (BankCard bankCard : bankCards) {
            if (companyId.equals(bankCard.getCompanyId())) {
                BankUserCardReqDTO bankUserCard = new BankUserCardReqDTO();
                BeanUtils.copyProperties(bankCard, bankUserCard);
                bankUserCard.setUserType(Integer.valueOf(bankCard.getUserType()));
                bankUserCard.setTransferCardSum(BigDecimal.ZERO);
                bankUserCard.setCardBalance(BigDecimal.ZERO);
                bankUserCard.setPayCardSum(BigDecimal.ZERO);
                bankUserCard.setRefundCardSum(BigDecimal.ZERO);
                bankUserCard.setCardStatus(BankCardStatus.NORMAL.getKey());
                bankUserCard.setCreateTime(new Date());
                bankUserCard.setUpdateTime(new Date());
                Boolean aBoolean = iBankUserCardTradeService.saveBankUserCard(bankUserCard);
                if (aBoolean) {
                    success = success + 1;
                }
            }
        }
        int fail = bankCards.size() - success;
        String msg = "【批量创建个人资金账户】【总条数" + bankCards.size() + "条】【成功" + success + "条】【失败" + fail +"条】";
        ResponseResultUtils.success(response, msg);
    }

    @HttpService(value = "/spa/createBankUserCard", method = RequestMethod.POST)
    public void createBankUserCardForSpa(HttpRequest request, HttpResponse response) {
        List<BankCard> bankCards = searchCardManager.queryByBankName("SPABANK");
        if(ObjUtils.isEmpty(bankCards)){
            return;
        }
        int success = 0;
        for (BankCard bankCard : bankCards) {
            BankUserCardReqDTO bankUserCard = new BankUserCardReqDTO();
            BeanUtils.copyProperties(bankCard, bankUserCard);
            bankUserCard.setUserType(bankCard.getBankAccountType());
            bankUserCard.setTransferCardSum(BigDecimal.ZERO);
            bankUserCard.setCardBalance(BigDecimal.ZERO);
            bankUserCard.setPayCardSum(BigDecimal.ZERO);
            bankUserCard.setRefundCardSum(BigDecimal.ZERO);
            bankUserCard.setCardStatus(BankCardStatus.NORMAL.getKey());
            bankUserCard.setCreateTime(new Date());
            bankUserCard.setUpdateTime(new Date());
            Boolean aBoolean = iBankUserCardTradeService.saveBankUserCard(bankUserCard);
            if(aBoolean){
                success = success + 1;
            }
        }
        int fail = bankCards.size() - success;
        String msg = "【批量创建个人资金账户】【总条数" + bankCards.size() + "条】【成功" + success + "条】【失败" + fail +"条】";
        ResponseResultUtils.success(response, msg);
    }
}
