package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskTimingService;
import com.fenbeitong.fenbeipay.vouchers.unit.service.UVoucherRecoveryTaskService;
import com.fenbeitong.fenbeipay.vouchers.vo.VoucherExpiryStatisticsVO;
import com.fenbeitong.fenbeipay.vouchers.vo.VoucherStatisticsAmountVO;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 分贝通定时任务
 * @ClassName: VouchersForTimeController
 * @Author: zhangga
 * @CreateDate: 2018/12/11 下午7:36
 * @UpdateUser:
 * @UpdateDate: 2018/12/11 下午7:36
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/internal/vouchers")
public class VouchersForTimeController {

    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    private VouchersTaskTimingService vouchersTaskTimingService;
    @Autowired
    private UVoucherRecoveryTaskService uVoucherRecoveryTaskService;

    /**
     * @Description: 定时回收过期分贝券 每天凌晨 0点45分 开始执行
     * @methodName: vouchersRecoveryTaskV3
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2021/3/19 10:13 下午
     **/
    @HttpService(value = "/writeoff_task/v2")
    public void vouchersRecoveryTaskV3(HttpRequest request, HttpResponse response) {
        VoucherExpiryStatisticsVO statisticsVO = uVoucherRecoveryTaskService.statisticExpiryVouchers();
        List<VoucherStatisticsAmountVO> statisticsResults = statisticsVO.getStatisticsResults();
        CompletableFuture.runAsync(() -> recoveryExpiryVouchersTask(statisticsResults));
        ResponseResultUtils.success(response, statisticsVO);
    }

    private void recoveryExpiryVouchersTask(List<VoucherStatisticsAmountVO> statisticsResults) {
        for (VoucherStatisticsAmountVO result : statisticsResults) {
            String grantTaskId = result.get_id();
            Integer totalBalanceAmount = result.getTotalBalanceAmount();
            try {
                VouchersTask recoveryTask = uVoucherRecoveryTaskService.createExpiryRecoveryTask(grantTaskId, totalBalanceAmount);
                if (ObjUtils.isNull(recoveryTask)) {
                    continue;
                }
                uVoucherRecoveryTaskService.startExpireRecoveryTask(recoveryTask.getVouchersTaskId());
            } catch (Exception e) {
                FinhubLogger.error("【分贝券过期回收异常】发放任务id:{}", grantTaskId, e);
            }
        }
    }

    /**
     * checkTaskIsOvertime
     *
     * @return void
     * @Description 检查任务是否超时 每 10分钟 执行一次
     * @Date 下午8:51 2018/12/24
     * @Param [request, response]
     **/
    @HttpService(value = "/checktask_is_overtime/v2")
    public void checkTaskIsOvertime(HttpRequest request, HttpResponse response) {
        vouchersTaskHandleService.checkTaskIsOvertime();
        ResponseResultUtils.success(response, "分贝券异步任务成功");
    }

    /**
     * @Description: 分贝券到期提醒 每天上午 11点15分 开始执行
     * @methodName: sendVoucherExpiryNotice
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/4/25 3:14 PM
     **/
    @HttpService(value = "/send_voucher_expiry_notice/v3")
    public void sendVoucherExpiryNotice(HttpRequest request, HttpResponse response) {
        FinhubLogger.info("分贝券到期提醒开始");
        vouchersPersonService.sendVoucherExpiryNotice();
        FinhubLogger.info("分贝券到期提醒结束");
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/timing/start/or/stop/task/v3")
    public void timingStartOrStopTask(HttpRequest request, HttpResponse response) {
        vouchersTaskTimingService.timingStartOrStopTask();
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/timing/execute/task/and/grant/voucher/v3")
    public void timingExecuteTaskAndGrantVoucher(HttpRequest request, HttpResponse response) {
        CompletableFuture.runAsync(() -> vouchersTaskTimingService.sendGrantNotice());
        CompletableFuture.runAsync(() -> vouchersTaskTimingService.timingExecuteTaskAndGrantVoucher());
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/send_voucher_task_fail_notice/v4")
    public void sendVoucherTaskFailNotice(HttpRequest request, HttpResponse response) {
        vouchersTaskHandleService.sendVoucherTaskFailNotice();
        ResponseResultUtils.success(response);
    }

}
