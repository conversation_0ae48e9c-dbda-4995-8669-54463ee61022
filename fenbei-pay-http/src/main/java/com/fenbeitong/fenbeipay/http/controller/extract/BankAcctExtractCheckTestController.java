package com.fenbeitong.fenbeipay.http.controller.extract;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctBankCheckSearchRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctExtractDaySearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctExtractMonthSearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctBankCheckService;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractDayService;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractMonthService;
import com.fenbeitong.fenbeipay.dto.extract.AcctExtractDay;
import com.fenbeitong.fenbeipay.extract.model.dto.AcctExtractHandleDTO;
import com.fenbeitong.fenbeipay.extract.service.UAcctExtractDayService;
import com.fenbeitong.fenbeipay.extract.service.UAcctExtractService;
import com.fenbeitong.fenbeipay.extract.service.UBankAcctExtractService;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * @ClassName BankAcctExtractCheckTestController
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/3/23
 **/
@HttpService("/internal/extract/test")
public class BankAcctExtractCheckTestController {


    @Autowired
    private IAcctExtractDayService iAcctExtractDayService;

    @Autowired
    private IAcctExtractMonthService iAcctExtractMonthService;

    @Autowired
    private IAcctBankCheckService iAcctBankCheckService;

    @Autowired
    private UBankAcctExtractService uBankAcctExtractService;

    @Autowired
    private UAcctExtractService uAcctExtractService;

    @Autowired
    private UAcctExtractDayService uAcctExtractDayService;

    @HttpService(value = "/testExtractDay", method = RequestMethod.POST)
    public void testExtractDay(HttpRequest request, HttpResponse response) {
        AcctExtractDaySearchReqRPCDTO queryReq = request.getBodyObject(AcctExtractDaySearchReqRPCDTO.class);
        ResponsePage<AcctExtractDaySearchRespRPCDTO> page = iAcctExtractDayService.queryByPage(queryReq);
        ResponseResultUtils.success(response, page);
    }

    @HttpService(value = "/testExtractMonth", method = RequestMethod.POST)
    public void testExtractMonth(HttpRequest request, HttpResponse response) {
        AcctExtractMonthSearchReqRPCDTO queryReq = request.getBodyObject(AcctExtractMonthSearchReqRPCDTO.class);
        ResponsePage<AcctExtractMonthSearchRespRPCDTO> page = iAcctExtractMonthService.queryByPage(queryReq);
        ResponseResultUtils.success(response, page);
    }

    @HttpService(value = "/testExtract", method = RequestMethod.POST)
    public void testExtract(HttpRequest request, HttpResponse response) {
        AcctBankCheckSearchReqDTO queryReq = request.getBodyObject(AcctBankCheckSearchReqDTO.class);
        ResponsePage<AcctBankCheckSearchRespDTO> page = iAcctBankCheckService.queryByPage(queryReq);
        ResponseResultUtils.success(response, page);
    }

    @HttpService(value = "/testExtractRemark", method = RequestMethod.POST)
    public void testExtractRemark(HttpRequest request, HttpResponse response) {
        AcctBankCheckProcessReqDTO queryReq = request.getBodyObject(AcctBankCheckProcessReqDTO.class);
        iAcctBankCheckService.extractProcess(queryReq);
        ResponseResultUtils.success(response, null);
    }

    @HttpService(value = "/dealExtractData", method = RequestMethod.POST)
    public void dealExtract(HttpRequest request, HttpResponse response) {
        AcctBankCheckDealReqDTO dealReq = request.getBodyObject(AcctBankCheckDealReqDTO.class);
        String success = uBankAcctExtractService.dealExtract(dealReq);
        ResponseResultUtils.success(response, success);
    }

    @HttpService(value = "/dealExtractDayData", method = RequestMethod.POST)
    public void dealExtractDayData(HttpRequest request, HttpResponse response) {
        AcctExtractDay req = request.getBodyObject(AcctExtractDay.class);
        int i = uAcctExtractDayService.updateExtractDay(req);
        ResponseResultUtils.success(response, i);
    }


    @HttpService(value = "/batchSaveExtractData", method = RequestMethod.POST)
    public void batchSaveExtractData(HttpRequest request, HttpResponse response) {
        AcctBankCheckBatchReqDTO batchReqDTO = request.getBodyObject(AcctBankCheckBatchReqDTO.class);
        String success = uBankAcctExtractService.batchSaveExtractData(batchReqDTO);
        ResponseResultUtils.success(response, success);
    }

    @HttpService(value = "/testExtractDay/redCoupon", method = RequestMethod.POST)
    public void testExtractDayRedCoupon(HttpRequest request, HttpResponse response) {
        String jobConfig = request.getParameter("jobConfig");
        AcctExtractHandleDTO queryReq = JSON.parseObject(jobConfig,AcctExtractHandleDTO.class);
        if (queryReq!= null){
            uAcctExtractService.handle(queryReq);
            ResponseResultUtils.success(response);
            return;
        }
        // 执行非银行账户数据生成逻辑
        Date now = DateUtils.now();
        AcctExtractHandleDTO acctExtractHandleDTO = new AcctExtractHandleDTO();
        acctExtractHandleDTO.setExtractDate(now);
        acctExtractHandleDTO.setIsRetry(false);
        acctExtractHandleDTO.setExtractType(10);
        uAcctExtractService.handle(acctExtractHandleDTO);
        AcctExtractHandleDTO acctExtractHandleDTO1 = new AcctExtractHandleDTO();
        acctExtractHandleDTO1.setExtractDate(now);
        acctExtractHandleDTO1.setIsRetry(false);
        acctExtractHandleDTO1.setExtractType(20);
        uAcctExtractService.handle(acctExtractHandleDTO);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/update/data", method = RequestMethod.POST)
    public void updateData(HttpRequest request, HttpResponse response) {
        AcctExtractUpdateDataRPCDTO acctExtractUpdateDataRPCDTO = request.getBodyObject(AcctExtractUpdateDataRPCDTO.class);
        iAcctExtractDayService.updateExtractDay(acctExtractUpdateDataRPCDTO.getKey(),acctExtractUpdateDataRPCDTO.getValue(),acctExtractUpdateDataRPCDTO.getAccountType());
        iAcctExtractMonthService.updateExtractMonth(acctExtractUpdateDataRPCDTO.getKey(),acctExtractUpdateDataRPCDTO.getValue(),acctExtractUpdateDataRPCDTO.getAccountType());
        ResponseResultUtils.success(response);
    }
}
