package com.fenbeitong.fenbeipay.http.controller.cashier;


import com.fenbeitong.fenbeipay.cashier.pay.CashierOrderSettlementPayService;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.concurrent.CompletableFuture;

@HttpService("/internal/cashier/risk")
@Slf4j
public class InnerCashierOrderSettlementPayController {

    @Autowired
    private CashierOrderSettlementPayService cashierOrderSettlementPayService;

    /**
     * 获取第三方支付唯一标识
     * <AUTHOR>
     * @date 2023-02-16 14:11:12
     */
    @HttpService(value = "/getThirdId", method = RequestMethod.GET)
    public void getThirdId(HttpRequest request, HttpResponse response) {
        CompletableFuture.runAsync(() -> {
            try {
                Integer pageSize = request.getIntParameter("pageSize", 500);
                Integer dateBefore = request.getIntParameter("dateBefore", 30);
                FinhubLogger.info("获取第三方支付唯一标识开始执行");
                StopWatch sw = new StopWatch();
                sw.start();
                cashierOrderSettlementPayService.getThirdId(pageSize, dateBefore);
                sw.stop();
                FinhubLogger.info(String.format("获取第三方支付唯一标识,执行完毕耗时:%s", sw.getTotalTimeSeconds()));
            } catch (Exception e) {
                FinhubLogger.warn("获取第三方支付唯一标识初始化数据异常", e);
            } catch (Throwable e) {
                FinhubLogger.warn("throwable获取第三方支付唯一标识初始化数据异常", e);
                log.warn("log获取第三方支付唯一标识初始化数据异常", e);
            }
        });
        ResponseResultUtils.success(response, "success");
    }
}
