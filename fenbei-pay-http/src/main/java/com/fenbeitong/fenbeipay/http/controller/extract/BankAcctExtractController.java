package com.fenbeitong.fenbeipay.http.controller.extract;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.dto.extract.ExecuteAcctBillDTO;
import com.fenbeitong.fenbeipay.dto.extract.ExtractDayTask;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.extract.manager.ExtractDayTaskManagerService;
import com.fenbeitong.fenbeipay.extract.model.dto.AcctExtractHandleDTO;
import com.fenbeitong.fenbeipay.extract.service.UAcctExtractService;
import com.fenbeitong.fenbeipay.extract.service.UBankAcctExtractService;
import com.fenbeitong.fenbeipay.extract.service.impl.UAcctExtractDayTaskService;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.finhub.common.saturn.enums.recon.ExtractDayTaskTypeEnum;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.PAY_EMAIL_SUBJECT_PREFIX;

/**
 * BankAcctExtractController
 * 对账相关接口
 * <AUTHOR>
 * @date  2021/3/22
 **/
@HttpService("/internal/bankAcct/extract")
public class BankAcctExtractController {

    @Autowired
    private UBankAcctExtractService uBankAcctExtractService;
    @Autowired
    private AccountGeneralService accountGeneralService;
    @Value("${acct.extract.bankName}")
    private String extractBankName;

    @Autowired
    private UAcctExtractService uAcctExtractService;

    @Autowired
    ExtractDayTaskManagerService extractDayTaskManagerService;

    @Autowired
    UAcctExtractDayTaskService uAcctExtractDayTaskService;

    public static final Integer maxPageSize = 1000;

    /*
     * @MethodName: execute
     * @Description: 定时触发，生成分贝通月切余额落库
     * @Param: [request, response]
     * @Return: void
     * @Author: Jarvis.li
     * @Date: 2021/3/22
     **/
    @HttpService("/month/execute")
    public void monthExecute(HttpRequest request, HttpResponse response) {
        String jobConfig = request.getParameter("jobConfig");
        ExecuteAcctBillDTO executeAcctBillDTO = JSON.parseObject(jobConfig,ExecuteAcctBillDTO.class);
        FinhubLogger.info("定时任务执行月切...req:{}",JSON.toJSON(executeAcctBillDTO));
        Date now = DateUtils.now();
        if(executeAcctBillDTO.getIsRetry()){
            now = DateUtils.parse(executeAcctBillDTO.getBillTime(),DateUtils.FORMAT_DATE_PATTERN);
        }
        // 定时开始
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "定时任务执行分贝通月切余额生成开始...");
        // 查询所有企业主体信息
        // 现有银行
        List<String> bankNames = Arrays.asList(extractBankName.split(","));
        List<String> generalIds = accountGeneralService.findHaveFlowAccountGeneral(bankNames);
        if (CollectionUtils.isEmpty(generalIds)) {
            return;
        }
        saveBankAcctExtractMonthDoHandleBatch(generalIds, now);
        // 定时结束
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "定时任务执行分贝通月切余额生成结束...");
    }

    // 月批量保存
    private void saveBankAcctExtractMonthDoHandleBatch(List<String> generalIds, Date now) {
        List<List<String>> partition = com.google.common.collect.Lists.partition(generalIds, maxPageSize);
        for (List<String> subIds : partition) {
            List<AccountGeneral> accountGenerals = accountGeneralService.findByGeneralIds(subIds);
            // 执行数据生成逻辑
            uBankAcctExtractService.saveBankAcctExtractMonthDoHandle(accountGenerals, now);
        }
    }

    /*
    // 备份原来的代码，下方定义一个新的，包装任务的 QX 2022-04-02
     * @MethodName: execute授信
     * @Description: 定时触发，生成分贝通日切余额落库
     * @Param: [request, response]
     * @Return: void
     * @Author: Jarvis.li
     * @Date: 2021/3/22
     **/
    @HttpService("/credit/day/execute")
    public void creditDayExecute(HttpRequest request, HttpResponse response) {
        String jobConfig = request.getParameter("jobConfig");
        ExecuteAcctBillDTO executeAcctBillDTO = JSON.parseObject(jobConfig,ExecuteAcctBillDTO.class);
        FinhubLogger.info("定时任务执行授信日切...req:{}",JSON.toJSON(executeAcctBillDTO));
        Date now = DateUtils.now();
        // 执行非银行账户数据生成逻辑
        AcctExtractHandleDTO acctExtractHandleDTO = new AcctExtractHandleDTO();
        acctExtractHandleDTO.setExtractDate(now);
        acctExtractHandleDTO.setIsRetry(executeAcctBillDTO.getIsRetry());
        acctExtractHandleDTO.setExtractType(10);
        uAcctExtractService.handle(acctExtractHandleDTO);
        // 定时结束
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "定时任务执行分贝通授信日切余额生成结束...");
    }

    /*
     * @MethodName: execute授信
     * @Description: 定时触发，生成分贝通月切余额落库
     * @Param: [request, response]
     * @Return: void
     * @Author: Jarvis.li
     * @Date: 2021/3/22
     **/
    @HttpService("/credit/month/execute")
    public void creditMonthExecute(HttpRequest request, HttpResponse response) {
        String jobConfig = request.getParameter("jobConfig");
        ExecuteAcctBillDTO executeAcctBillDTO = JSON.parseObject(jobConfig,ExecuteAcctBillDTO.class);
        FinhubLogger.info("定时任务执行授信月切...req:{}",JSON.toJSON(executeAcctBillDTO));
        Date now = DateUtils.now();
        // 执行非银行账户数据生成逻辑
        AcctExtractHandleDTO acctExtractHandleDTO = new AcctExtractHandleDTO();
        acctExtractHandleDTO.setExtractType(20);
        acctExtractHandleDTO.setExtractDate(now);
        acctExtractHandleDTO.setIsRetry(executeAcctBillDTO.getIsRetry());
        uAcctExtractService.handle(acctExtractHandleDTO);
        // 定时结束
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "定时任务执行授信分贝通月切余额生成结束...");
    }


    /*-------------------日切余额包装任务，开始-----------------*/

    /**
     * 【有效充值账户日切跑批】，包装任务。原来的在上方
     * @param request
     * @param response
     */
    @HttpService("/day/execute/new")
    public void dayExecuteNew(HttpRequest request, HttpResponse response) {
        Date now = DateUtils.now();
        // 定时开始
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "【包装任务】分贝通账户，日终余额跑批。开始...");
        /*
          包装为一个任务
          QX 2022-03-25
         */
        String taskDate = DateUtils.formatDate(now);
        Integer taskType = ExtractDayTaskTypeEnum.ONE.getKey();
        ExtractDayTask task = extractDayTaskManagerService.getTask(taskDate, taskType);
        if(task != null){
            FinhubLogger.info("【包装任务】分贝通账户，日终余额跑批。已执行");
        }else {
            CompletableFuture.runAsync(() ->
                    doDayExecuteNew(taskDate,taskType));
        }
        // 定时结束
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "【包装任务】分贝通账户，日终余额跑批。结束...");
    }

    private void doDayExecuteNew(String taskDate,Integer taskType){
        // 正常开启任务
        extractDayTaskManagerService.startTask(taskDate, taskType);
        try {
            uAcctExtractDayTaskService.dayExecuteNew(taskDate);
        } catch (Exception e) {
            FinhubLogger.error("【包装任务】分贝通账户，日终余额跑批。执行异常",e);
            // 异常结束任务
            extractDayTaskManagerService.errorTask(taskDate, taskType);
        }
        // 正常结束任务
        extractDayTaskManagerService.endTask(taskDate, taskType);
    }

    /**
     * execute授信
     * 新定义的一个，包装任务的 QX 2022-04-02。原来的任务在上方
     * @param request
     * @param response
     */
    @HttpService("/credit/day/execute/new")
    public void creditDayExecuteNew(HttpRequest request, HttpResponse response) {
        FinhubLogger.info("【包装任务】授信账户，日终余额跑批。开始");
        /**
         * 包装为一个任务
         * QX 2022-03-25
         */
        Date now = DateUtils.now();
        String taskDate = DateUtils.formatDate(now);
        Integer taskType = ExtractDayTaskTypeEnum.TWO.getKey();
        ExtractDayTask task = extractDayTaskManagerService.getTask(taskDate, taskType);
        if(task != null){
            FinhubLogger.info("【包装任务】授信账户，日终余额跑批。已执行");
        }else {
            // 正常开启任务
            extractDayTaskManagerService.startTask(taskDate, taskType);
            try {
                uAcctExtractDayTaskService.creditDayExecuteNew(taskDate);
            } catch (Exception e) {
                FinhubLogger.error("【包装任务】授信账户，日终余额跑批。执行异常",e);
                // 异常结束任务
                extractDayTaskManagerService.errorTask(taskDate, taskType);
            }
            // 正常结束任务
            extractDayTaskManagerService.endTask(taskDate, taskType);

        }
        // 定时结束
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "【包装任务】授信账户，日终余额跑批。结束...");
    }

    /**
     * 【白名单】前一天
     * @param request
     * @param response
     */
    @HttpService("/day/execute/manual")
    public void manualYesterday(HttpRequest request, HttpResponse response) {
        // 定时开始
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "【包装任务、白名单前一天】日终余额跑批。开始...");
        String taskDate = DateUtils.formatDate(DateUtils.now());
        Integer taskType = ExtractDayTaskTypeEnum.THREE.getKey();
        ExtractDayTask task = extractDayTaskManagerService.getTask(taskDate, taskType);
        if(task != null){
            FinhubLogger.info("【包装任务、白名单前一天】日终余额跑批。已执行");
        }else {
            // 正常开启任务
            extractDayTaskManagerService.startTask(taskDate, taskType);
            try {
                uAcctExtractDayTaskService.manualYesterday(taskDate);
            } catch (Exception e) {
                FinhubLogger.error("【包装任务、白名单前一天】日终余额跑批。执行异常",e);
                // 异常结束任务
                extractDayTaskManagerService.errorTask(taskDate, taskType);
            }
            // 正常结束任务
            extractDayTaskManagerService.endTask(taskDate, taskType);

        }
        // 定时结束
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "【包装任务、白名单前一天】日终余额跑批。结束--");
    }

    /**
     * 【白名单】历史
     * @param request
     * @param response
     */
    @HttpService("/day/execute/manual/history")
    public void dayExecuteManualHistory(HttpRequest request, HttpResponse response) {
        Date now = DateUtils.now();
        // 定时开始
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "【包装任务、白名单历史】日终余额跑批。开始--");

        String taskDate = DateUtils.formatDate(now);
        Integer taskType = ExtractDayTaskTypeEnum.FOUR.getKey();
        ExtractDayTask task = extractDayTaskManagerService.getTask(taskDate, taskType);
        if(task != null){
            FinhubLogger.info("【包装任务、白名单历史】日终余额跑批。已执行");
        }else {

            // 正常开启任务
            extractDayTaskManagerService.startTask(taskDate, taskType);

            try {
                uAcctExtractDayTaskService.manualHistory(taskDate);
            } catch (Exception e) {
                FinhubLogger.error("【包装任务、白名单历史】日终余额跑批。执行异常",e);
                // 异常结束任务
                extractDayTaskManagerService.errorTask(taskDate, taskType);
            }
            // 正常结束任务
            extractDayTaskManagerService.endTask(taskDate, taskType);
        }
        // 定时结束
        FinhubLogger.info(PAY_EMAIL_SUBJECT_PREFIX + "【包装任务、白名单历史】日终余额跑批。结束--");
    }


    /**
     * 手工接口，将该任务设置为异常。
     * @param request
     * @param response
     */
    @HttpService(value = "/errorTask/{taskDate}/{taskType}", method = RequestMethod.GET)
    public void errorTask(HttpRequest request, HttpResponse response){
        String taskDate = request.getPathValue("taskDate");
        String taskType = request.getPathValue("taskType");
        if(StringUtil.isNotBlank(taskDate) && StringUtil.isNotBlank(taskType)){
            // 异常结束任务
            extractDayTaskManagerService.errorTask(taskDate, Integer.valueOf(taskType));
        }
        ResponseResultUtils.success(response, 1);
    }

    /**
     * 重新执行任务，手工接口。（开发用）
     * @param request
     * @param response
     */
    @HttpService(value = "/againTask/{taskDate}/{taskType}", method = RequestMethod.GET)
    public void againTask(HttpRequest request, HttpResponse response){
        String taskDate = request.getPathValue("taskDate");
        String taskTypeStr = request.getPathValue("taskType");
        if(StringUtil.isNotBlank(taskDate) && StringUtil.isNotBlank(taskTypeStr)){
            Integer taskType = Integer.valueOf(taskTypeStr);
            // 先设置为进行中
            extractDayTaskManagerService.runTask(taskDate,taskType);
            if(ExtractDayTaskTypeEnum.ONE.getKey().equals(taskType)){
                uAcctExtractDayTaskService.dayExecuteNew(taskDate);
            }else if(ExtractDayTaskTypeEnum.TWO.getKey().equals(taskType)){
                uAcctExtractDayTaskService.creditDayExecuteNew(taskDate);
            }else if(ExtractDayTaskTypeEnum.THREE.getKey().equals(taskType)){
                uAcctExtractDayTaskService.manualYesterday(taskDate);
            }else if(ExtractDayTaskTypeEnum.FOUR.getKey().equals(taskType)){
                uAcctExtractDayTaskService.manualHistory(taskDate);
            }
            // 完成任务
            extractDayTaskManagerService.endTask(taskDate,taskType);
        }
        ResponseResultUtils.success(response, 1);
    }

    /*-------------------日切余额包装任务，结束-----------------*/

}
