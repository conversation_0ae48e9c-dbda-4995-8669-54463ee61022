package com.fenbeitong.fenbeipay.http.controller.newaccount;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountRemoveReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSearchListReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountTypeEditReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountUploadReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSearchListRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountUploadCountRespDTO;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.http.controller.vouchers.BaseController;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoService;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.COMPANY_ID;

/**
 * <AUTHOR>
 * @Date 2020/11/5
 * @Description
 */
@HttpService("/fbp/employee")
public class AccountReceiveInfoController extends BaseController {


    @Autowired
    private AccountReceiveInfoService accountReceiveInfoService;


    @HttpService(value = "/syncEmployeeInfo/{companyId}",method = RequestMethod.GET)
    public void syncEmployeeInfo(HttpRequest request, HttpResponse response){

        String companyId = request.getPathValue("companyId");
        accountReceiveInfoService.syncEmployeeInfo(companyId);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/getEmployeeInfoList",method = RequestMethod.POST)
    public void getEmployeeInfoList(HttpRequest request, HttpResponse response){

        AccountSearchListReqDTO reqDTO = request.getBodyObject(AccountSearchListReqDTO.class);
        ResponsePage<AccountSearchListRespDTO> responsePage = accountReceiveInfoService.getEmployeeInfoList(reqDTO);
        ResponseResultUtils.success(response,responsePage);
    }

    @HttpService(value = "/editEmployeeInfo",method = RequestMethod.POST)
    public void editEmployeeInfo(HttpRequest request, HttpResponse response){

        AccountTypeEditReqDTO reqDTO = request.getBodyObject(AccountTypeEditReqDTO.class);
        accountReceiveInfoService.editAccountType(reqDTO);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/removeEmployee",method = RequestMethod.POST)
    public void removeEmployee(HttpRequest request, HttpResponse response){

        AccountRemoveReqDTO removeReqDTO = request.getBodyObject(AccountRemoveReqDTO.class);
        accountReceiveInfoService.removeAccount(removeReqDTO.getEmployeeIds());
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/uploadEmployee",method = RequestMethod.POST)
    public void uploadEmployee(HttpRequest request, HttpResponse response){
        String companyId = request.getAttribute(COMPANY_ID).toString();
        if(StringUtils.isBlank(companyId)){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountUploadReqDTO reqDTO = request.getBodyObject(AccountUploadReqDTO.class);
        AccountUploadCountRespDTO respDTO = accountReceiveInfoService.uploadEmployee(reqDTO.getFilePath(),companyId);
        ResponseResultUtils.success(response,respDTO);
    }
}
