package com.fenbeitong.fenbeipay.http.controller.acct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.activity.api.util.GlobalCoreResponseCode;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthResultDTO;
import com.fenbeitong.dech.api.model.dto.lf.LfTradeReceiptQueryReqDTO;
import com.fenbeitong.dech.api.model.dto.lf.LfTradeReceiptRespDTO;
import com.fenbeitong.dech.api.service.ILfBankSearchService;
import com.fenbeitong.fenbeipay.acctdech.enums.MergePdfEnum;
import com.fenbeitong.fenbeipay.acctdech.manager.IBankCardTrapFlowManager;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessGeneralService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessDebitFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UCompanySwitchService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.impl.AccountInnerService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.CategoryTypeMappingEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.account.CollectionAccountEntityForBizDebitEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.accountpublic.AccountPublicStatus;
import com.fenbeitong.fenbeipay.api.model.ResultDTO;
import com.fenbeitong.fenbeipay.api.model.dto.AirwallexAcctDetailRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.AuthRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.QueryAuthRequestDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.AcctRechargeWhiteListDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.AcctPublicSimpleInfoRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.spa.SpaReceiptQueryReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.spa.SpaReceiptQueryRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByMIdBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.PrepaymentReq;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.resp.AcctSubTypeMainRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AcctRedcouponFlowReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AcctTransferSaveReqDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponFlowVO;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchService;
import com.fenbeitong.fenbeipay.api.service.auth.CompanyAuthService;
import com.fenbeitong.fenbeipay.api.util.BankNameShowConfig;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.enums.auth.AuthObjMenuCodeEnum;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.dto.BillFlowPageQuery;
import com.fenbeitong.fenbeipay.core.model.dto.account.*;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyAccountInfo;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserLoginVo;
import com.fenbeitong.fenbeipay.core.service.auth.DataAuthService;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.accountpublic.AccountPublic;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcct;
import com.fenbeitong.fenbeipay.rpc.service.auth.CompanyAuthInnerService;
import com.fenbeitong.finhub.auth.constant.UserAttributeConstant;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.stereo.api.model.dto.pay.AcctRechargeReceiverUpdateDTO;
import com.fenbeitong.stereo.api.model.dto.pay.BankAccountInfoDTO;
import com.fenbeitong.stereo.api.model.dto.pay.BankAccountRechargeSmsDTO;
import com.fenbeitong.stereo.api.model.dto.pay.RechargeSmsDTO;
import com.fenbeitong.stereo.api.service.pay.IPayAccountService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyPlatformAccountConvertDTO;
import com.fenbeitong.usercenter.api.model.dto.privilege.AuthObjResDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.*;

/**
 * <AUTHOR>
 * @Date 2021/1/11
 * @Description 新账户体系接口
 */
@HttpService("/fbp/acct/web/company")
public class AcctWebController extends AcctAbstractController {
    /**
     * 电子回单下载日期间隔
     */
    private static final int TIME_INTERVAL = 31;
    /**
     * 电子回单下载数量限制
     */
    private static final int DOWNLOAD_RECEIPT_COUNT_LIMIT = 2000;
    /**
     * 海外卡收款账户缓存KEY
     */
    private static final String AIRWALLEX_ACCOUNT_INFO_KEY = "AIRWALLEX_ACCOUNT_INFO_KEY";
    
    @Autowired
    private DingDingMsgService dingDingMsgService;

    // 平安银行批量电子回单操作手册
    @Value("${bank.spa.receipt.operation.manual}")
    private String spaReceiptOperationManualUrl;
    // 平安银行批量电子回单地址
    @Value("${bank.spa.receipt.batch.url}")
    private String spaReceiptBatchUrl;

    @Autowired
    private UCompanySwitchService uCompanySwitchService;

    @Autowired
    private IPayAccountService iPayAccountService;

    @Autowired
    private AcctBusinessGeneralService acctBusinessGeneralService;

    @Autowired
    private IAcctFundMgrService iAcctFundMgrService;

    @Autowired
    private IAcctMgrService iAcctMgrService;

    @Autowired
    private DataAuthService dataAuthService;
    
    @Autowired
    private AcctCompanyMainService acctCompanyMainService;
    
    @Autowired
    private IAcctPublicSearchService iAcctPublicSearchService;

    @Autowired
    private ILfBankSearchService iLfBankSearchService;
    
    @Autowired
    private AccountInnerService accountInnerService;
    
    @Autowired
    private UAcctCommonService acctCommonService;
    
    @Autowired
    private RedisDao redisDao;

    @Autowired
    protected RedissonService redissonService;
    
    @Autowired
    private CompanyAuthService companyAuthService;
    
    @Autowired
    private CompanyAuthInnerService companyAuthInnerService;
    
    @Autowired
    private UAcctBusinessDebitFlowService acctBusinessDebitFlowService;
    
    @Autowired
    private IBankCardTrapFlowManager iBankCardTrapFlowManager;
    /**
     * 企业账户-账户总览
     * 新账户体系 个账户信息总揽 如果商务账户授信 其他账户都是充值
     *
     * @param request
     * @param response
     */
    @Deprecated
    @HttpService(value = "/overview", method = RequestMethod.GET)
    public void companyOverview(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        AcctOverviewRespDTO respDTO = uAcctCommonService.queryWebAcctOverview(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 企业账户-账户总览
     * 新账户体系 个账户信息总揽 如果商务账户授信 其他账户都是充值
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/overview/V5", method = RequestMethod.GET)
    public void companyOverviewV5(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        FinhubLogger.info("企业账户-账户总览companyOverviewV5 companyId为:{} ， userId = {}",companyId ,getUserId(request));
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        AcctOverviewV5RespDTO respDTO = uAcctCommonService.queryWebAcctOverviewV5(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }

    @HttpService(value = "/recharge/config", method = RequestMethod.GET)
    public void rechargeConfig(HttpRequest request, HttpResponse response) {
        String companyId = getOperationCompanyId(request);
        try {
            boolean isTrialAndMarketVersion = checkTrailCompany(companyId);
            if (isTrialAndMarketVersion){
                Map<String,Object> resultMap = Maps.newHashMap();
                resultMap.put("accountInfo", Collections.emptyList());
                Map<String,Object> receiverMap = Maps.newHashMap();
                receiverMap.put("receiverCount",0);
                receiverMap.put("receiverList", Collections.emptyList());
                resultMap.put("receiverInfo",receiverMap);
                resultMap.put("bindAccountInfo", Collections.emptyList());
                ResponseResultUtils.success(response,resultMap);
                return;
            }
        }catch (Exception e){
            FinhubLogger.error("试用版查询UC异常",e);
        }

        String companyMainId = request.getParameter("companyMainId");
        String bankName = request.getParameter("bankName");
        String bankAccountNo = request.getParameter("bankAccountNo");
        Map<String,Object> resultMap = Maps.newHashMap();
        // 判断是否是海外卡账户
        if (FxAcctChannelEnum.isAirWallex(bankName)) {
            //获取短信接收人数及接收人信息
            getSmsReceiverList(companyId,resultMap);
            getAccountInfo4AirWallex(resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        //获取默认充值账户信息
        getDefaultRechargeAccount(companyId,resultMap);
        //获取短信接收人数及接收人信息
        getSmsReceiverList(companyId,resultMap);
        //收款账户信息
        boolean isFbt = BankNameEnum.isFbt(bankName);
        if (isFbt){
            getOldAccountInfo(companyId,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        // 查询企业是否切换
        boolean isCompanySwitch = uCompanySwitchService.isCompanySwitch(companyId);
        if (!isCompanySwitch){
            getOldAccountInfo(companyId,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        // 查询收款账户信息
        AcctComGwByMIdBankReqDTO acctComGwByMIdBankReqDTO = new AcctComGwByMIdBankReqDTO();
        acctComGwByMIdBankReqDTO.setCompanyId(companyId);
        acctComGwByMIdBankReqDTO.setCompanyMainId(companyMainId);
        acctComGwByMIdBankReqDTO.setBankName(bankName);
        acctComGwByMIdBankReqDTO.setBankAccountNo(bankAccountNo);
        //平安银行
        if (BankNameEnum.isSpa(bankName)){
        	accountInnerService.getAccountInfo4Spa(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isCgb(bankName)){
        	accountInnerService.getAccountInfo4Cgb(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isLfBank(bankName)){
        	accountInnerService.getAccountInfo4LfBank(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isSpd(bankName)){
        	accountInnerService.getAccountInfo4SpdBank(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isZBBank(bankName)){
        	accountInnerService.getAccountInfo4ZBBank(acctComGwByMIdBankReqDTO,resultMap);
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        if (BankNameEnum.isCitic(bankName)){
            //默认充值账户
        	accountInnerService.getAccountInfo4Citic(acctComGwByMIdBankReqDTO,resultMap);
            //绑定账户
            ResponseResultUtils.success(response,resultMap);
            return;
        }
        
        if (BankNameEnum.isLianlian(bankName)) {
        	accountInnerService.getRechargeAccountInfo(acctComGwByMIdBankReqDTO, resultMap);
            ResponseResultUtils.success(response, resultMap);
            return;
        }
        
        getAccountInfoByAccountInfo(acctComGwByMIdBankReqDTO,resultMap);
        ResponseResultUtils.success(response,resultMap);
    }

    private void getAccountInfo4AirWallex(Map<String, Object> resultMap) {
        //收款账户信息
        String accountInfos = redisDao.getRedisTemplate().opsForValue().get(AIRWALLEX_ACCOUNT_INFO_KEY).toString();
        List<Map<String, Object>> accountInfoList = JsonUtils.toObj(accountInfos, List.class);
        resultMap.put("accountInfo",accountInfoList);
    }

    /**
     * 查询账户信息
     */
    private void getAccountInfoByAccountInfo(AcctComGwByMIdBankReqDTO acctComGwByMIdBankReqDTO, Map<String, Object> resultMap) {
        //收款账户信息
        List<BankCollectionAccountInfo> accountInfoList = null;
        // 调用RPC接口查询充值配置信息
        AcctCompanyMainDetailRespDTO acctMainBaseInfoRespDTO =  iAcctMgrService.queryMainAndAcct(acctComGwByMIdBankReqDTO);
        // 根据结果组合参数bankAccountNo
        if(acctMainBaseInfoRespDTO != null){
            accountInfoList = Lists.newArrayList();
            BankCollectionAccountInfo bankCollectionAccountInfo = new BankCollectionAccountInfo();
            bankCollectionAccountInfo.setCollCompanyName(acctMainBaseInfoRespDTO.getCompanyMainName()); // 开户主体名称
            bankCollectionAccountInfo.setCollDentificationNumber(acctMainBaseInfoRespDTO.getBusinessLicenseCode()); // 营业执照号
            bankCollectionAccountInfo.setCollCompanyOpenBank(BankNameEnum.getBankEnum(acctMainBaseInfoRespDTO.getBankName()).getName()); // 开户行名称
            bankCollectionAccountInfo.setCollCompanyFixedTelephone(acctMainBaseInfoRespDTO.getBusinessPhone()); // 开户手机号
            bankCollectionAccountInfo.setCollCompanyBankAccount(acctMainBaseInfoRespDTO.getBankAccountNo()); // 企业虚拟账户
            bankCollectionAccountInfo.setBankBranchName(ObjUtils.toString(acctMainBaseInfoRespDTO.getBankBranchName(), null)); // 支行名称
            
            if (Objects.equals(BankNameEnum.CITIC.getCode(), acctMainBaseInfoRespDTO.getBankName())) {
                bankCollectionAccountInfo.setBankBranchName(bankCollectionAccountInfo.getCollCompanyOpenBank() + Optional.ofNullable(acctMainBaseInfoRespDTO.getBankBranchName()).orElse(""));
            }
            if (StringUtils.isBlank(acctMainBaseInfoRespDTO.getBankBranchName())) {
                bankCollectionAccountInfo.setBankBranchName(bankCollectionAccountInfo.getCollCompanyOpenBank());
            }
            accountInfoList.add(bankCollectionAccountInfo);
        }
        FinhubLogger.info("companyId为:{}的收款账户信息为:{}",acctComGwByMIdBankReqDTO.getCompanyId(),JSON.toJSONString(accountInfoList));
        resultMap.put("accountInfo",accountInfoList);
    }
    
    private void getSmsReceiverList(String companyId, Map<String, Object> resultMap) {
        Map<String,Object> receiverMap = Maps.newHashMap();
        List<BankAccountRechargeSmsDTO> smsReceiverList = iPayAccountService.getSmsReceiverList(companyId);
        FinhubLogger.info("companyId为:{}的充值成功短信接收人信息为:{}",companyId,JSON.toJSONString(smsReceiverList));
        receiverMap.put("receiverCount",smsReceiverList.size());
        receiverMap.put("receiverList",smsReceiverList);
        resultMap.put("receiverInfo",receiverMap);
    }

    private void getDefaultRechargeAccount(String companyId, Map<String, Object> resultMap) {
        Integer accountSubType = iPayAccountService.selectRechargeTypeByCompanyId(companyId);
        resultMap.put("defaultRechargeAccount",FundAccountSubType.getEnum(accountSubType).getValue());
        resultMap.put("defaultRechargeAccountKey",accountSubType);
    }

    private void getOldAccountInfo(String companyId, Map<String, Object> resultMap) {
        List<BankAccountInfoDTO> bankAccountInfoDTOS = iPayAccountService.selectAccountInfoByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(bankAccountInfoDTOS) ) {
            bankAccountInfoDTOS.stream().forEach(acct -> {
                if (StringUtils.isBlank(acct.getBankBranchName())) {
                    acct.setBankBranchName(acct.getCollCompanyOpenBank());
                }
            });
        }
        FinhubLogger.info("companyId为:{}的收款账户信息为:{}",companyId,JSON.toJSONString(bankAccountInfoDTOS));
        resultMap.put("accountInfo",bankAccountInfoDTOS);
    }

    /**
     * 企业账户-账户管理
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/find/main/mgr", method = RequestMethod.GET)
    public void queryMainAcctMgr(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        AcctOverviewSimpleRespDTO simpleRespDTO = uAcctCommonService.queryAcctOverviewMgr(reqDTO);
        ResponseResultUtils.success(response, simpleRespDTO);
    }

    /**
     * 【集团版】企业账户-账户总览
     * 新账户体系 个账户信息总揽 如果商务账户授信 其他账户都是充值
     * @param request
     * @param response
     */
    @HttpService(value = "/group/overview/V5", method = RequestMethod.GET)
    public void groupCompanyOverviewV5(HttpRequest request, HttpResponse response) {
        List<CompanyAccountInfo> companyAccountInfos = JSON.parseArray(JSON.toJSONString(request.getAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS)), CompanyAccountInfo.class);
        AcctGroupOverviewV5RespDTO respDto = uAcctCommonService.queryWebGroupAcctOverviewV5(companyAccountInfos);
        ResponseResultUtils.success(response, respDto);
    }

    /**
     * 集团版-账户总览（导出调用）
     * @param request HttpRequest
     * @param response HttpResponse
     */
    @HttpService(value = "/group/overview/V5/accountList", method = RequestMethod.GET)
    public void groupAccountList(HttpRequest request, HttpResponse response) {
        List<CompanyAccountInfo> companyAccountInfos = JSON.parseArray(JSON.toJSONString(request.getAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS)), CompanyAccountInfo.class);
        ResponseResultUtils.success(response, uAcctCommonService.queryWebGroupAcctList(companyAccountInfos));
    }

    /**
     * 【集团版】企业账户-账户管理
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/find/group/main/mgr", method = RequestMethod.GET)
    public void queryGroupMainAcctMgr(HttpRequest request, HttpResponse response) {
//        UserLoginVo userLogin = getUserLogin(request);
        List<CompanyAccountInfo> companyAccountInfos = JSON.parseArray(JSON.toJSONString(request.getAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS)), CompanyAccountInfo.class);
        List<AcctGroupOverviewSimpleRespDto> respDtos = uAcctCommonService.queryGroupAcctOverviewMgr(companyAccountInfos);
        JSONObject resultData = new JSONObject();
        resultData.put("groupCompanyAcctList", respDtos);
        ResponseResultUtils.success(response, resultData);
    }

    /**
     * 企业账户-账户流水查询-主体列表
     * 新账户体系 个账户信息总揽 如果商务账户授信 其他账户都是充值
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/find/main/list", method = RequestMethod.POST)
    public void queryMainAcctList(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        List<AcctBaseMainRespDTO> acctBaseMainRespDTOS = uAcctCommonService.queryMainAcctView(reqDTO);
        AcctCompanyMainRespDTO acctCompanyMainRespDTO = AcctCompanyMainRespDTO.builder()
                .acctBaseMainRespDTOS(acctBaseMainRespDTOS)
                .build();
        ResponseResultUtils.success(response, acctCompanyMainRespDTO);
    }


    /**
     * 企业账户-账户流水查询-主体列表
     * 新账户体系 个账户信息总揽 如果商务账户授信 其他账户都是充值
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/find/company/card/list", method = RequestMethod.POST)
    public void queryMainAcctList4CompanyCard(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        AcctOverviewReqDTO reqDTO = new AcctOverviewReqDTO(companyId);
        List<AcctBaseMainRespDTO> acctBaseMainRespDTOS = uAcctCommonService.queryMainAcctView4CompanyCard(reqDTO);
        AcctCompanyMainRespDTO acctCompanyMainRespDTO = AcctCompanyMainRespDTO.builder()
                .acctBaseMainRespDTOS(acctBaseMainRespDTOS)
                .build();
        ResponseResultUtils.success(response, acctCompanyMainRespDTO);
    }


//=======================================账户资金管理=================================

    /**
     * 充值账户-单个主体下主体&余额账户信息
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/main/base/info", method = RequestMethod.POST)
    public void queryMainInfo(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO reqDTO = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AcctCompanyMainDetailRespDTO respDTO = uAcctCommonService.queryCompanyMainDetailInfo(reqDTO);
        respDTO.setBankNameDesc(BankNameEnum.getBankEnum(respDTO.getBankName()).getName());
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 充值账户-单个主体下所有账户的展示
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/main/debit/show", method = RequestMethod.POST)
    public void debitMainShow(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO reqDTO = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AcctDebitMainRespDTO respDTO = uAcctCommonDebitService.findDebitMain(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }


    /**
     * 对公账户-余额账户转账转出
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/public/debit/show", method = RequestMethod.POST)
    public void publicTransferInfo(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO reqDTO = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AcctDebitPublicRespDTO respDTO = uAcctCommonDebitService.findDebitPublic(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }


    /**
     * 查询电子回单有权限账户
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/public/electronic/acctList", method = RequestMethod.GET)
    public void publicElectronicAcctList(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        String userId = getUserId(request);

        // todo 新权限 done
        AuthObjResDTO authObjResDTO = dataAuthService.getDataAuth(AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
        if(authObjResDTO == null){
            FinhubLogger.error("查询电子回单有权限账户, 未查询到权限数据, 参数: menuCode: {}, companyId: {}, userId: {}", AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
            ResponseResultUtils.success(response, Collections.EMPTY_LIST);
            return;
        }
        if (!authObjResDTO.getAccount().getAllData() && CollectionUtils.isEmpty(authObjResDTO.getAccount().getPartDataIdList())) {
            FinhubLogger.warn("查询电子回单有权限账户, 未查询到有权限的账户");
            ResponseResultUtils.success(response, Collections.EMPTY_LIST);
            return;
        }
        List<AcctPublicSimpleInfoRespRPCDTO> accountPublics = iAcctPublicSearchService.findAccountAcctNameByCompanyId(companyId);
        if (CollectionUtils.isNotEmpty(accountPublics)) {
            List<String> publicAcctIds = authObjResDTO.getAccount().getPartDataIdList();
            // List<String> publicAcctIds = changeAccountList.stream().map(EmployeePaymentAuthReqDTO.AccountInfo::getId).collect(Collectors.toList());
            accountPublics = accountPublics.stream().filter(e -> authObjResDTO.getAccount().getAllData() || publicAcctIds.contains(e.getCompanyAccountId())).collect(Collectors.toList());
        }

        ResponseResultUtils.success(response, accountPublics);
    }

    /**
     * 对公账户-查询所有对公账户
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/public/acct/list", method = RequestMethod.GET)
    public void publicAcctList(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        String userId = getUserId(request);
        AcctPublicRespDTO respDTO = new AcctPublicRespDTO();
        // todo 新权限 done
        // List<EmployeePaymentAuthReqDTO.AccountInfo> changeAccountList = iPrivilegeService.getChangeAccountList(companyId, userId);
        AuthObjResDTO authObjResDTO = dataAuthService.getDataAuth(AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
        if(authObjResDTO == null){
            FinhubLogger.error("对公账户-查询所有对公账户, 未查询到权限数据, 参数: menuCode: {}, companyId: {}, userId: {}", AuthObjMenuCodeEnum.VIRTUAL_COMPANY_PAYMENT_ACCOUNT_FLOW, companyId, userId);
            ResponseResultUtils.success(response, respDTO);
            return;
        }
        if (!authObjResDTO.getAccount().getAllData() && CollectionUtils.isEmpty(authObjResDTO.getAccount().getPartDataIdList())) {
            FinhubLogger.warn("对公账户-查询所有对公账户, 未查询到有权限的账户");
            ResponseResultUtils.success(response, respDTO);
            return;
        }
        List<AccountPublic> accountPublics = acctPublicDechService.findAcctPublicByCompanyIdAndStatus(companyId, AccountPublicStatus.containFind(), Arrays.asList(FundAcctShowStatusEnum.SHOW.getStatus()));
        if (CollectionUtils.isNotEmpty(accountPublics)) {
            List<String> publicAcctIds = authObjResDTO.getAccount().getPartDataIdList();
            // List<String> publicAcctIds = changeAccountList.stream().map(EmployeePaymentAuthReqDTO.AccountInfo::getId).collect(Collectors.toList());
            accountPublics = accountPublics.stream().filter(e -> authObjResDTO.getAccount().getAllData() || publicAcctIds.contains(e.getCompanyAccountId())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(accountPublics)){
                makeAcctPublicRespDTO(respDTO, accountPublics);
            }
        }

        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 对公账户-查询所有对公账户,启用，可展示
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/public/acct/enable/show/list", method = RequestMethod.GET)
    public void publicAcctEnableShowList(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        AcctPublicRespDTO respDTO = new AcctPublicRespDTO();
        List<AccountPublic> accountPublics = acctPublicDechService.findAcctPublicByCompanyIdAndStatus(companyId, AccountPublicStatus.containFind(), Arrays.asList(FundAcctShowStatusEnum.SHOW.getStatus()));
        if (CollectionUtils.isNotEmpty(accountPublics)) {
            accountPublics = accountPublics.stream().filter(e -> Objects.nonNull(e.getAccountPublicStatus()) && AccountPublicStatus.isNormal(e.getAccountPublicStatus())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(accountPublics)){
                makeAcctPublicRespDTO(respDTO, accountPublics);
            }
        }
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 对公账户-查询所有对公账户,可展示
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/public/acct/show/list", method = RequestMethod.GET)
    public void publicAcctShowList(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        AcctPublicRespDTO respDTO = new AcctPublicRespDTO();
        List<AccountPublic> accountPublics = acctPublicDechService.findAcctPublicByCompanyIdAndStatus(companyId, AccountPublicStatus.containFind(), Arrays.asList(FundAcctShowStatusEnum.SHOW.getStatus()));
        if (CollectionUtils.isNotEmpty(accountPublics)) {
            makeAcctPublicRespDTO(respDTO, accountPublics);
        }
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 对公账户-查询对公账户余额
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/public/acct/balance", method = RequestMethod.POST)
    public void publicAcctBalance(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO reqDTO = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AccountPublic accountPublic = acctPublicSearchService.findByComIdAndMIdAndBank(reqDTO.getCompanyId(), reqDTO.getCompanyMainId(), reqDTO.getBankName(), reqDTO.getBankAccountNo());
        AcctPublicAcctInfoRespDTO respDTO = new AcctPublicAcctInfoRespDTO();
        respDTO.setBalance(accountPublic.getBalance());
        respDTO.setBankAccountNo(accountPublic.getBankAccountNo());
        respDTO.setBankAccountNameDesc(BankNameEnum.getBankEnum(accountPublic.getBankAccountName()).getName());
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 充值账户-余额账户转账转出
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/general/transfer", method = RequestMethod.POST)
    public void generalTransfer(HttpRequest request, HttpResponse response) {
        AcctTransferDebitReqDTO reqDTO = request.getBodyObject(AcctTransferDebitReqDTO.class);
        try {
            ValidateUtils.validate(reqDTO);
            AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonDebitService.transferOut2Others(reqDTO);
            ResponseResultUtils.success(response, optCommonRespDTO);
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 充值账户-余额账户提现
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/general/cash_withdrawal", method = RequestMethod.POST)
    public void generalCashWithdrawal(HttpRequest request, HttpResponse response) {
        AcctGeneralOptReqDTO reqDTO = request.getBodyObject(AcctGeneralOptReqDTO.class);
        try {
            ValidateUtils.validate(reqDTO);
            AcctGeneralOptRespDTO generalOptResp = uAcctGeneralService.cashWithdrawal(reqDTO);
//            uAcctGeneralService.cashWithdrawalBank(reqDTO);
            ResponseResultUtils.success(response, generalOptResp);
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 授信账户-主体
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/main/credit/show", method = RequestMethod.POST)
    public void creditMainShow(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO reqDTO = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AcctCreditMainRespDTO respDTO = uAcctCommonCreditService.findCreditMain(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 授信账户-额度转移
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/credit/transfer", method = RequestMethod.POST)
    public void transferOut(HttpRequest request, HttpResponse response) {
        AcctTransferCreditToEachReqDTO reqDTO = request.getBodyObject(AcctTransferCreditToEachReqDTO.class);
        try {
            ValidateUtils.validate(reqDTO);
            AcctCommonOptRespDTO optCommonRespDTO = uAcctCommonCreditService.adjustFromSub(reqDTO);
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】adjust 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户V4.0系统异常】recharge 参数：{}", reqDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        ResponseResultUtils.success(response, true);
    }


    //=================================Flow流水查询=================================

    /**
     * 充值账户-主体
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/main/simple/list", method = RequestMethod.POST)
    public void findMainCreditShow(HttpRequest request, HttpResponse response) {
        AcctComByAcctIdReqDTO reqDTO = request.getBodyObject(AcctComByAcctIdReqDTO.class);
        reqDTO.setCompanyId(getOperationCompanyId(request));
        reqDTO.setBetweenType(getBetweenType(request));
        reqDTO.setUserId(getUserId(request));
        AcctSubTypeMainRespDTO respDTO = uAcctCommonDebitService.findMainSimpleList(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 一般户流水(总帐户)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/general/flow", method = RequestMethod.POST)
    public void baseAccountFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        reqDTO.setCompanyId(getOperationCompanyId(request));
        reqDTO.setBetweenType(getBetweenType(request));
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctGeneralFlowRespDTO> page = accountGeneralFlowService.queryAccountGeneralFlow(reqDTO);
        ResponseResultUtils.success(response, page);
    }

    /**
     * 商务账户流水(商务)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/business/debit/flow", method = RequestMethod.POST)
    public void rechargeFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        reqDTO.setCompanyId(getOperationCompanyId(request));
        reqDTO.setBetweenType(getBetweenType(request));
        reqDTO.checkQueryTime();
        ResponsePage<AcctFlowRespDTO> flow;
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            flow = uAcctBusinessDebitFlowService.queryAccountDebitFlow(reqDTO);
        } else {
            flow = uAcctBusinessCreditFlowService.queryAccountCreditFlow(reqDTO);
        }
        makeTargetAccountName(flow);
        ResponseResultUtils.success(response, flow);
    }
    
    @HttpService(value = "/oversea/debit/flow", method = RequestMethod.POST)
    public void queryOverseaAcctFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO req = request.getBodyObject(AcctOptFlowReqDTO.class);
        req.setCompanyId(getOperationCompanyId(request));
        req.setBetweenType(getBetweenType(request));
        req.checkQueryTime();
        if (Objects.isNull(req.getStartTime())) {
            req.setStartTime(org.apache.commons.lang3.time.DateUtils.addMonths(new Date(), -1));
        }
        if (Objects.isNull(req.getEndTime())) {
        	req.setEndTime(new Date());
        }
        
        ResponsePage<AcctFlowRespDTO> flowList = acctOverseaService.queryAccountFlow(req);
        ResponseResultUtils.success(response, flowList);
    }
    
    /**
     * 场景列表
     * @param request
     * @param response
     */
    @HttpService(value = "/category/list", method = RequestMethod.GET)
    public void getCategoryCodes(HttpRequest request, HttpResponse response) {
        ResponseResultUtils.success(response, CategoryTypeMappingEnum.getCodeNames());
    }
    
    /**
     * 商务消费对手账户列表
     * @param request
     * @param response
     */
    @HttpService(value = "/collection/acct/entity/list", method = RequestMethod.GET)
    public void getCollectionAcctEntities(HttpRequest request, HttpResponse response) {
        ResponseResultUtils.success(response, CollectionAccountEntityForBizDebitEnum.getCollectionAcctEntities());
    }

    /**
     * stereo账单
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/flow/bill/{billNo}", method = RequestMethod.GET)
    public void flowRelateToBillByBillNo(HttpRequest request, HttpResponse response) {
        try {
            String billNo = request.getPathValue("billNo");
            if (StringUtils.isBlank(billNo)){
                throw new FinhubException(GlobalResponseCode.BILL_NO_NOT_EXIST.getCode(), GlobalResponseCode.BILL_NO_NOT_EXIST.getType(), GlobalResponseCode.BILL_NO_NOT_EXIST.getMsg());
            }
            Boolean isExist = iBillOpenApi.checkSummaryByBillNo(billNo);
            if (!isExist){
                throw new FinhubException(GlobalResponseCode.BILL_NO_NOT_EXIST.getCode(), GlobalResponseCode.BILL_NO_NOT_EXIST.getType(), GlobalResponseCode.BILL_NO_NOT_EXIST.getMsg());
            }
            ResponseResultUtils.success(response);
        } catch (FinPayException e) {
            FinhubLogger.error("stereo账单查询异常：{}",e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("stereo账单查询异常：{}", e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    /**
     * 个人账户流水(个人)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/individual/debit/flow", method = RequestMethod.POST)
    public void creditFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        reqDTO.setCompanyId(getOperationCompanyId(request));
        ResponsePage<AcctFlowRespDTO> flow;
        reqDTO.setBetweenType(getBetweenType(request));
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            flow = uAcctIndividualDebitFlowService.queryAccountDebitFlow(reqDTO);
        } else {
            flow = uAcctIndividualCreditFlowService.queryAccountCreditFlow(reqDTO);
        }
        ResponseResultUtils.success(response, flow);
    }

    /**
     * 员工报销账户
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/reimbursement/flow", method = RequestMethod.POST)
    public void reimbursementFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        reqDTO.setCompanyId(getOperationCompanyId(request));
        reqDTO.setBetweenType(getBetweenType(request));
        ResponsePage<AcctFlowRespDTO> flow = uAcctReimbursementService.queryReimbursementFlowPage(reqDTO);
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponseResultUtils.success(response, flow);
    }

    /**
     * 企业虚拟卡流水
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/card/flow", method = RequestMethod.POST)
    public void companyCardFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        reqDTO.setCompanyId(getOperationCompanyId(request));
        reqDTO.setBetweenType(getBetweenType(request));
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctFlowRespDTO> page = uAcctCompanyCardFlowService.queryAccountCompanyCardFlow(reqDTO);
        ResponseResultUtils.success(response, page);
    }

    /**
     *   企业对公流水
     * @param request
     * @param response
     */
    @HttpService(value = "/public/flow", method = RequestMethod.POST)
    public void acctPublicFlow(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        reqDTO.setCompanyId(getOperationCompanyId(request));
        reqDTO.setBetweenType(getBetweenType(request));
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        ResponsePage<AcctFlowRespDTO> accountCompanyCardFlow = acctPublicSearchService.queryAccountCompanyCardFlow(reqDTO);
        ResponseResultUtils.success(response, accountCompanyCardFlow);
    }


    /**
     * 红包券流水
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/redcoupon/flow", method = RequestMethod.POST)
    public void redcouponFlow(HttpRequest request, HttpResponse response) {
        AcctRedcouponFlowReqDTO dto = request.getBodyObject(AcctRedcouponFlowReqDTO.class);
        FinhubLogger.info("红包券流水 dto = {}", JsonUtils.toJson(dto));
        dto.setCompanyId(getOperationCompanyId(request));
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (dto.getStartTime() == null
                || dto.getStartTime().before(minDateOfQuery)) {
            dto.setStartTime(minDateOfQuery);
            dto.setEndTime(DateUtils.getCurrentDate());
        }
        FinhubLogger.info("红包券流水 dto2 = {}", JsonUtils.toJson(dto));
        ResponsePage<AccountRedcouponFlowVO> responsePage = acctRedcouponSearchService.queryAcctCouponFlowList(dto);
        ResponseResultUtils.success(response, responsePage);
    }
    
    @HttpService(value = "/export/excel", method = RequestMethod.POST)
    public void exportCompanyFlow(HttpRequest request, HttpResponse response) {
        BillFlowPageQuery requestParam = request.getBodyObject(BillFlowPageQuery.class);
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (requestParam.getStartTime() == null
                || requestParam.getStartTime().before(minDateOfQuery)) {
            requestParam.setStartTime(minDateOfQuery);
            requestParam.setEndTime(DateUtils.getCurrentDate());
        }
        requestParam.setDirectFromWeb(true);
        
        UserInfoVO userInfo = getUserInfo(request);
        requestParam.setUserId(userInfo.getId());
        requestParam.setUserName(userInfo.getName());
        requestParam.setCompanyId(getUserCompanyId(request));
        
        //校验参数
        requestParam.checkExportTaskParam();
        
        if (Objects.nonNull(requestParam.getTargetAcctCode())) {
            List<String> collectionAccts = acctBusinessDebitFlowService.getAccountNoByTargetAcctCode(requestParam.getTargetAcctCode());
            requestParam.setTargetAccounts(collectionAccts);
        }
        
        FinhubLogger.info("AuthAcctInnerController#exportCompanyFlow:{}", JSON.toJSONString(requestParam));
        //从大数据库获取数据
        AcctFlowExportRespDTO resp = acctCommonService.exportCompanyBillExcel(requestParam);        
        //返回数据
        ResponseResultUtils.success(response, resp);
    }

    /**
     * 流水批量导出
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/flow/export", method = RequestMethod.POST)
    public void flowExport(HttpRequest request, HttpResponse response) {
        AcctOptFlowReqDTO reqDTO = request.getBodyObject(AcctOptFlowReqDTO.class);
        reqDTO.setCompanyId(getOperationCompanyId(request));
        if (StringUtils.isBlank(reqDTO.getMergePdf())) {
            reqDTO.setMergePdf(MergePdfEnum.not_merge.getKey());
        }
        // 选择未生成/生成失败 直接提示 "可下载电子回单为空"
        if (reqDTO.getCostImageStatus() != null
                && (FundAcctCostImageStatus.isCostImageNon(reqDTO.getCostImageStatus()) || FundAcctCostImageStatus.isCostImageFaile(reqDTO.getCostImageStatus()))) {
            throw new FinhubException(GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getCode(),
                    GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getType(), GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getMsg());
        }
        // 不同银行 不用账户 的查询条件不同
        queryCondition(reqDTO);

        reqDTO.setBetweenType(getBetweenType(request));

        // 获取用户信息
        AcctUserInfoDTO acctUserInfoDTO = new AcctUserInfoDTO();
        BeanUtils.copyProperties(getUserInfo(request), acctUserInfoDTO);

        if (BankNameEnum.isLfBank(reqDTO.getBankName())) {
            LfTradeReceiptQueryReqDTO receiptQueryReqDTO = new LfTradeReceiptQueryReqDTO();
            receiptQueryReqDTO.setBankAccountNo(reqDTO.getBankAccountNo());
            receiptQueryReqDTO.setTradeTimeBegin(DateUtils.formatDate(reqDTO.getStartTime()));
            receiptQueryReqDTO.setTradeTimeEnd(DateUtils.formatDate(reqDTO.getEndTime()));
            receiptQueryReqDTO.setBankName(BankNameEnum.LFBANK.getCode());
            receiptQueryReqDTO.setAccountSubType(reqDTO.getAccountSubType());

            LfTradeReceiptRespDTO respDTO = iLfBankSearchService.queryTxnReceipt(receiptQueryReqDTO);

            AcctFlowExportRespDTO resp = uAcctCommonService.exportLfElectronicReceipt(acctUserInfoDTO, reqDTO, respDTO);
            ResponseResultUtils.success(response, resp);
        } else {
            // 查询账户流水
            List<AcctFlowRespDTO> flow = queryAccFlow(reqDTO);
            if(CollectionUtils.isEmpty(flow)){
                throw new FinhubException(GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getCode(),
                        GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getType(), GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getMsg());
            }
            // 批量导出电子回单
            AcctFlowExportRespDTO resp = uAcctCommonService.exportElectronicReceipt(acctUserInfoDTO, flow, reqDTO);
            ResponseResultUtils.success(response, resp);
        }

    }

    private void queryCondition(AcctOptFlowReqDTO reqDTO) {
        // 众邦银行所有账户 都不需要分页查询导出
        // ******** +浦发银行
        if (BankNameEnum.isZBBank(reqDTO.getBankName()) || BankNameEnum.isSpd(reqDTO.getBankName())) {
            // 时间间隔不大于31天
            checkDate(reqDTO);
            // 不需要分页查询
            reqDTO.setIfPageQuery(false);
            // 查询数量限制
            reqDTO.setNotPageQueryLimit(DOWNLOAD_RECEIPT_COUNT_LIMIT);
            // 只查询电子回单下载成功的
            if (Objects.isNull(reqDTO.getCostImageStatus())) {
                reqDTO.setCostImageStatusInDb(1);
            }
        }
        // 平安银行的对公付款账户 不需要分页查询导出
        if (BankNameEnum.isSpa(reqDTO.getBankName())
                && FundAccountSubType.isComPublicCardAccount(reqDTO.getAccountSubType())) {
            // 时间间隔不大于31天
            checkDate(reqDTO);
            // 不需要分页查询
            reqDTO.setIfPageQuery(false);
            // 查询数量限制
            reqDTO.setNotPageQueryLimit(DOWNLOAD_RECEIPT_COUNT_LIMIT);
            // 只查询电子回单下载成功的
            if (Objects.isNull(reqDTO.getCostImageStatus())) {
                reqDTO.setCostImageStatusInDb(1);
            }
        }

        if (BankNameEnum.isLfBank(reqDTO.getBankName())) {
            // 时间间隔不大于31天
            checkDate(reqDTO);
            // 不需要分页查询
            reqDTO.setIfPageQuery(false);
            // 查询数量限制
//            reqDTO.setNotPageQueryLimit(DOWNLOAD_RECEIPT_COUNT_LIMIT);
        }
    }

    private void checkDate(AcctOptFlowReqDTO req) {
        Date startTime = req.getStartTime();
        Date endTime = req.getEndTime();
        if (startTime == null || endTime == null) {
            throw new FinhubException(TIME_NOT_BLANK.getCode(), TIME_NOT_BLANK.getType(), TIME_NOT_BLANK.getMsg());
        }
        // 2022-01-01 - 2022-01-01 = 0 但是产品定义1天 所以结果+1是查询的跨度
        int interval = DateUtil.daysBetweenDay(startTime, endTime) + 1;
        if (interval > TIME_INTERVAL) {
            throw new FinhubException(TIME_INTERVAL_LESS_31.getCode(), TIME_INTERVAL_LESS_31.getType(), TIME_INTERVAL_LESS_31.getMsg());
        }
    }

    private List<AcctFlowRespDTO> queryAccFlow(AcctOptFlowReqDTO reqDTO) {
        ResponsePage<AcctFlowRespDTO> respPage;
        switch (FundAccountSubType.getEnum(reqDTO.getAccountSubType())) {
            case BUSINESS_ACCOUNT:
                // 商务消费账户
                // 充值模式
                if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                    respPage = uAcctBusinessDebitFlowService.queryAccountDebitFlow(reqDTO);
                } else {
                    // 授信模式
                    respPage = uAcctBusinessCreditFlowService.queryAccountCreditFlow(reqDTO);
                }
                break;
            case INDIVIDUAL_ACCOUNT:
                // 个人消费账户
                // 充值模式
                if (FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
                    respPage = uAcctIndividualDebitFlowService.queryAccountDebitFlow(reqDTO);
                } else {
                    // 授信模式
                    respPage = uAcctIndividualCreditFlowService.queryAccountCreditFlow(reqDTO);
                }
                break;
            case GENERAL_ACCOUNT:
                // 可支配余额账户
                ResponsePage<AcctGeneralFlowRespDTO> generalFlow = accountGeneralFlowService.queryAccountGeneralFlow(reqDTO);
                // 转换类型
                List<AcctFlowRespDTO> genFlow = generalFlow.getDataList().stream().map(e -> {
                    AcctFlowRespDTO acctFlowRespDTO = new AcctFlowRespDTO();
                    BeanUtils.copyProperties(e, acctFlowRespDTO);
                    return acctFlowRespDTO;
                }).collect(Collectors.toList());

                respPage = new ResponsePage<>();
                respPage.setTotalCount(generalFlow.getTotalCount());
                respPage.setDataList(genFlow);
                break;
            case BANK_VIRTUAL_ACCOUNT:
                // 企业虚拟卡账户
                respPage = uAcctCompanyCardFlowService.queryAccountCompanyCardFlow(reqDTO);
                break;
            case BANK_PUBLIC_ACCOUNT:
                // 对公付款账户
                respPage = acctPublicSearchService.queryAccountCompanyCardFlow(reqDTO);
                break;
            case REIMBURSEMENT_ACCOUNT:
                respPage = uAcctReimbursementService.queryReimbursementFlowPage(reqDTO);
                break;
            default:
                FinhubLogger.error("不存在的账户类型 acctSubType = {}", reqDTO.getAccountSubType());
                throw new FinhubException(GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getCode(),
                        GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getType(), GlobalResponseCode.ACC_SUB_TYPE_NOT_EXIST.getMsg());
        }
        Integer totalCount = respPage.getTotalCount();
        List<AcctFlowRespDTO> flow = respPage.getDataList();
        // 下载数量限制
        // 下载数量和下载数量限制同时不为空
        boolean a = totalCount != null && reqDTO.getNotPageQueryLimit() != null;
        // 下载数量和下载数量限制同时不为空 && 下载数量大于数量限制
        boolean b = a && (totalCount > reqDTO.getNotPageQueryLimit() || flow.size() > reqDTO.getNotPageQueryLimit());
        if (b) {
            dingDingMsgService.sendMsg("下载电子回单失败 数量超过" + DOWNLOAD_RECEIPT_COUNT_LIMIT + " totalCount : " + totalCount);
            throw new FinhubException(DOWNLOAD_RECEIPT_COUNT_OVER_LIMIT.getCode(), DOWNLOAD_RECEIPT_COUNT_OVER_LIMIT.getType(), DOWNLOAD_RECEIPT_COUNT_OVER_LIMIT.getMsg());
        }
        // 流水不存在即电子回单不存在 返回提示
        if (CollectionUtils.isEmpty(flow)) {
            throw new FinhubException(GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getCode(),
                    GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getType(), GlobalResponseCode.PAYMENT_ORDER_EXPORT_ELECTRONIC_EMPTY.getMsg());
        }
        return flow;
    }

    /**
     * 自动转账--可转账户列表
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/auto/transfer/acct", method = RequestMethod.POST)
    public void autoTransferAcct(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO reqDTO = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        AcctAutoTransferListRespDTO respDTO =  uAcctCommonDebitService.queryAutoTransferAcct(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }


    /**
     * 自动转账--保存
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/auto/transfer/save", method = RequestMethod.POST)
    public void autoTransferSave(HttpRequest request, HttpResponse response) {
        AcctTransferSaveReqDTO dto = request.getBodyObject(AcctTransferSaveReqDTO.class);
        uAcctGeneralService.defaultTransferAccount(dto);
        ResponseResultUtils.success(response);
    }

    /**
     * 账户4.0-丝滑升级
     * @param request httRequest
     * @param response httpResponse
     */
    @HttpService(value = "/v4/upgrade", method = RequestMethod.POST)
    public void upgrade(HttpRequest request, HttpResponse response) {
        AcctCompanyUpgradeReqDTO reqDTO = new AcctCompanyUpgradeReqDTO();
        try {
            reqDTO = request.getBodyObject(AcctCompanyUpgradeReqDTO.class);
            ValidateUtils.validate(reqDTO);
            AcctCompanyUpgradedRespDTO acctCompanyUpgradedRespDTO = uAcctCommonService.upgradeAcctCompany(reqDTO);
            //所有账户升级完成，如果与前置配置的权限一致，通知UC前置配置失效
            Boolean effectiveStatus = iCompanyService.queryAccountEffectiveStatus(reqDTO.getCompanyId());
            if(Objects.nonNull(effectiveStatus) && effectiveStatus) {
                List<CompanyPlatformAccountConvertDTO> companyPlatformAccountConvertDTOS = iCompanyService.queryCompanyPlatformAccountForTrans(reqDTO.getCompanyId());
                boolean canDisable = uAcctCommonService.allAuthBeforeAcctCreate(companyPlatformAccountConvertDTOS, reqDTO.getCompanyId());
                if (canDisable) {
                    iCompanyService.updateAccountInvalid(reqDTO.getCompanyId());
                }
            }
            ResponseResultUtils.success(response, acctCompanyUpgradedRespDTO);
        } catch (FinPayException e) {
            FinhubLogger.error("【资金账户4.0】v4 upgrade：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【资金账户4.0】v4 upgrade：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【资金账户4.0】v4 upgrade：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }


    /**
     * 账户4.0-丝滑升级开关
     * @param request httRequest
     * @param response httpResponse
     */
    @HttpService(value = "/upgrade/find/offon", method = RequestMethod.POST)
    public void findActUpdate4ByComId(HttpRequest request, HttpResponse response) {
        AcctComUpdate4ByComIdReqDTO reqDTO = new AcctComUpdate4ByComIdReqDTO();
        try {
            reqDTO = request.getBodyObject(AcctComUpdate4ByComIdReqDTO.class);
            ValidateUtils.validate(reqDTO);
            AcctComUpdate4RespDTO actUpdate4ByComId = uAcctComUpdateService.findActUpdate4ByComId(reqDTO);
            ResponseResultUtils.success(response,actUpdate4ByComId);
        } catch (FinPayException e) {
            FinhubLogger.error("【资金账户4.0】findActUpdate4ByComId：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【资金账户4.0】findActUpdate4ByComId：{}", JsonUtils.toJson(reqDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【资金账户4.0】findActUpdate4ByComId：{}", JsonUtils.toJson(reqDTO), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 中信交易电子回单下载
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/find/zxTradeFlowPage", method = RequestMethod.POST)
    public void zxTradeFlowPage(HttpRequest request, HttpResponse response) {
        ZxTradeFlowPageReqDTO dto = request.getBodyObject(ZxTradeFlowPageReqDTO.class);
        List<ZxTradeFlowPageRespDTO> result = uAcctCommonService.zxTradeFlowPage(dto);
        ResponseResultUtils.success(response,result);
    }

    /**
     * 浦发银行对账单下载
     * @param request
     * @param response
     */
    @HttpService(value = "/spd/trade/downLoad", method = RequestMethod.POST)
    public void spdTradeDownLoad(HttpRequest request, HttpResponse response) {
        SpdBankTradeDownLoadReqDTO dto = request.getBodyObject(SpdBankTradeDownLoadReqDTO.class);
        SpdBankTradeDownloadRespDTO spdBankTradeDown = uAcctCommonService.spdBankTradeDown(dto,(String)request.getAttribute("userId"),(String) request.getAttribute("userName"));
        ResponseResultUtils.success(response,spdBankTradeDown);
    }

    /**
     * 查询企业开启的平安充值账户
     * @param request
     * @param response
     */
    @HttpService(value = "/spa/acct/list", method = RequestMethod.POST)
    public void spaAcctList(HttpRequest request, HttpResponse response) {
        String companyId = getUserCompanyId(request);
        List<AcctGeneralSpaRespDTO> result = uAcctCommonService.spaAcctList(companyId);
        ResponseResultUtils.success(response,result);
    }



    /**
     * 余额划转-账户激活
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/acct/activite/switch", method = RequestMethod.POST)
    public void acctActiviteSwitch(HttpRequest request, HttpResponse response) {
        AcctNeedReminderReqDTO bodyObject = request.getBodyObject(AcctNeedReminderReqDTO.class);
        if(Objects.isNull(bodyObject)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        List<AcctNeedReminderRespDTO> bodyArray = bodyObject.getNeedReminder();
        if(CollectionUtils.isEmpty(bodyArray)){
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        try {
            uAcctCommonService.changeAcctActiviteStatus(bodyArray);
            //所有账户升级完成，如果与前置配置的权限一致，通知UC前置配置失效
            Boolean effectiveStatus = iCompanyService.queryAccountEffectiveStatus(bodyArray.get(0).getCompanyId());
            if (Objects.nonNull(effectiveStatus) && effectiveStatus) {
                List<CompanyPlatformAccountConvertDTO> companyPlatformAccountConvertDTOS = iCompanyService.queryCompanyPlatformAccountForTrans(bodyArray.get(0).getCompanyId());
                boolean canDisable = uAcctCommonService.allAuthBeforeAcctCreate(companyPlatformAccountConvertDTOS, bodyArray.get(0).getCompanyId());
                if (canDisable) {
                    iCompanyService.updateAccountInvalid(bodyArray.get(0).getCompanyId());
                }
            }
            ResponseResultUtils.success(response);

        }catch (Exception exception){
            FinhubLogger.error("【资金账户4.0】余额划转激活账户", JsonUtils.toJson(bodyArray));
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }



    private void makeAcctPublicRespDTO(AcctPublicRespDTO respDTO, List<AccountPublic> accountPublics) {
        List<String> companyMainIds = accountPublics.stream().filter(e -> !StringUtils.isBlank(e.getCompanyMainId())).map(AccountPublic::getCompanyMainId).collect(Collectors.toList());
        List<AcctCompanyMain> byMainIds = uAcctCompanyMainService.findByMainIds(companyMainIds);
        Map<String, AcctCompanyMain> mapByMainId = byMainIds.stream().collect(Collectors.toMap(AcctCompanyMain::getCompanyMainId, Function.identity(), (key1, key2) -> key2));
        List<AcctPublicAcctRespDTO> acctPublicAcctRespDTOS = new ArrayList<>();
        accountPublics.stream().forEach(accountPublic -> {
            AcctPublicAcctRespDTO acctPublicAcctRespDTO = new AcctPublicAcctRespDTO();
            acctPublicAcctRespDTO.setCompanyMainId(accountPublic.getCompanyMainId());
            acctPublicAcctRespDTO.setBankAccountNo(accountPublic.getBankAccountNo());
            acctPublicAcctRespDTO.setBankAccountName(accountPublic.getBankAccountName());
            acctPublicAcctRespDTO.setBankName(BankNameEnum.getBankEnum(accountPublic.getBankAccountName()).getName());
            acctPublicAcctRespDTO.setCompanyId(accountPublic.getCompanyId());
            String bankAccountName = StringUtils.isBlank(accountPublic.getBankAccountName()) ? "" : accountPublic.getBankAccountName();
            String bankAccoutNo = StringUtils.isBlank(accountPublic.getBankAccountNo()) ? "" : accountPublic.getBankAccountNo();
            //String businessName = Objects.isNull(mapByMainId.get(accountPublic.getCompanyMainId())) ? "" : mapByMainId.get(accountPublic.getCompanyMainId()).getBusinessName();
            AcctCompanyMain acctCompanyMain = mapByMainId.get(accountPublic.getCompanyMainId());
            String businessName = "";
            if(Objects.nonNull(acctCompanyMain)){
                /**
                 * ZHIFU-5070 中信账户名称优化
                 * 1.上线之后展示主体名称
                 * 2.白名单内展示主体名称
                 */

                String showMainName = uAcctCommonService.getShowMainName(BankNameEnum.getBankEnum(accountPublic.getBankAccountName()).getCode(), acctCompanyMain.getBusinessName(), accountPublic.getBankAccountNo(), acctCompanyMain);
                businessName = showMainName;
            }
            acctPublicAcctRespDTO.setDesc(BankNameShowConfig.makeBankPublicShowName(bankAccountName, bankAccoutNo, businessName));
            acctPublicAcctRespDTO.setBankBackgroundUrl(BankNameEnum.getBankEnum(accountPublic.getBankAccountName()).getBankBackground());
            acctPublicAcctRespDTO.setBalance(accountPublic.getBalance());
            acctPublicAcctRespDTO.setCompanyAccountId(accountPublic.getCompanyAccountId());
            acctPublicAcctRespDTO.setAccountId(accountPublic.getCompanyAccountId());
            acctPublicAcctRespDTO.setAccountPublicStatus(accountPublic.getAccountPublicStatus());
            acctPublicAcctRespDTOS.add(acctPublicAcctRespDTO);
        });
        respDTO.setPublicAcctRespDTOList(acctPublicAcctRespDTOS);
    }

    private void makeTargetAccountName(ResponsePage<AcctFlowRespDTO> flow) {
        // 增加对手账户名
        List<BankAcct> bankAccts = bankAcctService.queryAll();
        Map<String, String> bankAcctMap = bankAccts.stream().collect(Collectors.toMap(BankAcct::getBankAccountNo, BankAcct::getCompanyMainName, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(flow.getDataList())) {
            flow.getDataList().stream().forEach(s->{
                s.setTargetAccountName(bankAcctMap.get(s.getTargetAccount()));
                s.setTargetBankAllName(BankNameEnum.getBankEnum(s.getTargetBankName()).getName());
            });
        }
    }

    /**
     * 平安批量电子回单
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/find/spaBankBatchReceipt", method = RequestMethod.POST)
    public void spaBankBatchReceipt(HttpRequest request, HttpResponse response) {
        SpaBankBatchReceiptRespDTO result = new SpaBankBatchReceiptRespDTO();
        result.setReceiptBatchUrl(spaReceiptBatchUrl);
        result.setReceiptOperationManualUrl(spaReceiptOperationManualUrl);
        ResponseResultUtils.success(response,result);
    }

    /**
     * 查询企业充值白名单列表
     */
    @HttpService(value = "/white/query", method = RequestMethod.GET)
    public void queryWhiteList(HttpRequest request, HttpResponse response) {
        String bankAccountNo = request.getParameter("bankAccountNo");
        List<AcctRechargeWhiteListDTO> acctRechargeWhiteListDTOS = acctRechargeWhiteListService.queryWhiteListByAcctNo(bankAccountNo);

        ResponseResultUtils.success(response, acctRechargeWhiteListDTOS);
    }

    /**
     * 集团版查询 公司下账户列表
     * <AUTHOR>
     * @date 2022-07-05 18:08:12
     */
    @HttpService(value = "/find/group/list", method = RequestMethod.POST)
    public void queryGroupCompanyAcctList(HttpRequest request, HttpResponse response) {
        String companyId = getOperationCompanyId(request);
        String employeeId = getUserCompanyInfo(request).getId();
        String menuCode = request.getHeader(CoreConstant.ACCT_GROUP_MENU_CODE);
        try {
            FinhubLogger.info("集团版查询公司账户列表queryGroupCompanyAcctList companyId:{},employeeId:{},menuCode:{}", companyId, employeeId, menuCode);
            List<CompanyAccountInfo> result = (List<CompanyAccountInfo>) request.getAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS);

            // 过滤无权限公司
            result = result.stream().filter(v -> !(CompanyAccountInfo.AcctAuthEnum.NO_AUTH.equals(v.getAcctAuthEnum()) || CollectionUtils.isEmpty(v.getAccounts()))).collect(Collectors.toList());
            FinhubLogger.info("集团版查询公司账户列表queryGroupCompanyAcctList res:{}", JSON.toJSONString(result));
            ResponseResultUtils.success(response, result);
        } catch (Exception e) {
            FinhubLogger.error("集团版查询公司账户列表queryGroupCompanyAcctList error companyId:{}", e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode()));
        }
    }

    /**
     * 集团版查询有权限 公司列表
     * <AUTHOR>
     * @date 2022-07-05 18:08:12
     */
    @HttpService(value = "/find/group/companyList", method = RequestMethod.POST)
    public void queryGroupCompanyList(HttpRequest request, HttpResponse response) {
        String employeeId = getUserCompanyInfo(request).getId();
        String menuCode = request.getHeader(CoreConstant.ACCT_GROUP_MENU_CODE);
        try {
            FinhubLogger.info("集团版查询公司账户列表queryGroupCompanyAcctList companyId:{},employeeId:{},menuCode:{}", getOperationCompanyId(request), employeeId, menuCode);
            List<CompanyAccountInfo> result = (List<CompanyAccountInfo>) request.getAttribute(CoreConstant.ACCT_GROUP_COMPANY_ACCOUNT_INFOS);

            // 过滤无权限公司
            result = result.stream().filter(v -> CompanyAccountInfo.AcctAuthTypeEnum.AUTH_COMPANY.equals(v.getAcctAuthTypeEnum())).collect(Collectors.toList());
            FinhubLogger.info("集团版查询公司账户列表queryGroupCompanyList res:{}", JSON.toJSONString(result));
            ResponseResultUtils.success(response, result);
        } catch (Exception e) {
            FinhubLogger.error("集团版查询公司账户列表queryGroupCompanyAcctList error companyId:{}", e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode()));
        }
    }

    /**
     * 余额账户-提现
     */
    @HttpService(value = "/cashWithdrawal",method = RequestMethod.POST)
    public void cashWithdrawal(HttpRequest request, HttpResponse response) {
        AcctGeneralCashWithDrawalDTO bodyObject = request.getBodyObject(AcctGeneralCashWithDrawalDTO.class);
        //校验手机号，安全漏洞
        checkParams(bodyObject, request);
        AcctGeneralOptRespDTO acctGeneralOptRespDTO = acctBusinessGeneralService.bankAcctCashWithdrawal(bodyObject);
        ResponseResultUtils.success(response, acctGeneralOptRespDTO);
    }


    private void checkParams(AcctGeneralCashWithDrawalDTO acctGeneralCashWithDrawalDTO, HttpRequest request) {
        String sendMsgPhone = null;
        if(BankNameEnum.isCgb(acctGeneralCashWithDrawalDTO.getBankName()) || BankNameEnum.isLfBank(acctGeneralCashWithDrawalDTO.getBankName())){
            //如果是广发取经办人姓名手机号
            AcctCompanyMain acctCompanyMain = acctCompanyMainService.findAcctCompanyMain(acctGeneralCashWithDrawalDTO.getCompanyId(), acctGeneralCashWithDrawalDTO.getCompanyMainId(), acctGeneralCashWithDrawalDTO.getBankName());
            if(acctCompanyMain!=null){
                sendMsgPhone = acctCompanyMain.getAgentPhone();
            }
        }else {
            // 获取登录信息
            UserComInfoVO userComInfo = (UserComInfoVO) request.getAttribute(UserAttributeConstant.USER_COM_INFO);
            if(userComInfo!=null){
                sendMsgPhone = userComInfo.getUser_phone();
            }
        }
        if(StringUtils.isNotBlank(sendMsgPhone) && StringUtils.isNotBlank(acctGeneralCashWithDrawalDTO.getVerifyCodePhoneNum()) && !sendMsgPhone.equals(acctGeneralCashWithDrawalDTO.getVerifyCodePhoneNum())){
            FinhubLogger.info("AcctWebController#checkParams#提现接口手机号不一致,应:{},实:{}", sendMsgPhone, sendMsgPhone.equals(acctGeneralCashWithDrawalDTO.getVerifyCodePhoneNum()));
            throw new FinhubException(GlobalResponseCode.EXCEPTION_DATA_ERROR.getCode(), "提现手机号匹配失败");
        }
    }

    /**
     * 廊坊余额账户-提现发短信
     */
    @HttpService(value = "/cashWithdrawal/sendSms",method = RequestMethod.POST)
    public void cashWithdrawalSendSms(HttpRequest request, HttpResponse response) {
        AcctCashWithDrawalSmsDTO bodyObject = request.getBodyObject(AcctCashWithDrawalSmsDTO.class);
        String smsId = acctBusinessGeneralService.cashWithdrawalSendSms(bodyObject);
        Map<String, String> map = Maps.newHashMap();
        map.put("smsId", smsId);
        ResponseResultUtils.success(response, map);
    }

    /**
     * 授信账户-额度转移
     * 转移额度到指定账户（先调减，后调增）商务授信与个人授信互转额度
     */
    @HttpService(value = "/adjustFromSub",method = RequestMethod.POST)
    public void adjustFromSubV2(HttpRequest request, HttpResponse response) {
        String body = request.getBody();
        AcctTransferCreditToEachReqDTO bodyObject = JsonUtils.toObj(body, AcctTransferCreditToEachReqDTO.class);
        if(Objects.isNull(bodyObject.getOperationChannelType())){
            bodyObject.setOperationChannelType(OperationChannelType.WEB.getKey());
        }
        AcctCommonOptRespDTO acctCommonOptRespDTO = iAcctFundMgrService.adjustFromSub(bodyObject);
        FinhubLogger.info(">>>授信账户-额度转移充值账户接口调用结束,返回值:{}", JsonUtils.toJson(acctCommonOptRespDTO));
        ResponseResultUtils.success(response, acctCommonOptRespDTO);
    }

    /**
     * 充值账户-转回总账户
     */
    @HttpService(value = "/transferOut2General",method = RequestMethod.POST)
    public void transferOut2General(HttpRequest request, HttpResponse response) {
        String body = request.getBody();
        AcctTransferCommonReqDTO bodyObject = JsonUtils.toObj(body, AcctTransferCommonReqDTO.class);
        if(Objects.isNull(bodyObject.getOperationChannelType())){
            bodyObject.setOperationChannelType(OperationChannelType.WEB.getKey());
        }
        FinhubLogger.info("企业信息管理-账户管理-余额管理-转入可用余额账户,接口[IAcctFundMgrService#transferOut2General]-请求信息{}",JsonUtils.toJson(bodyObject));
        AcctCommonOptRespDTO acctCommonOptRespDTO = iAcctFundMgrService.transferOut2General(bodyObject);
        FinhubLogger.info("企业信息管理-账户管理-余额管理-转入可用余额账户,接口[IAcctFundMgrService#transferOut2General]-相应信息{}",JsonUtils.toJson(acctCommonOptRespDTO));
        ResponseResultUtils.success(response, acctCommonOptRespDTO);
    }

    /**
     * 充值账户-余额账户转账
     */
    @HttpService(value = "/transferOut2Others",method = RequestMethod.POST)
    public void transferOut2Others(HttpRequest request, HttpResponse response) {

        verifyIdentity(request);
        String body = request.getBody();
        AcctTransferDebitReqDTO bodyObject = JsonUtils.toObj(body, AcctTransferDebitReqDTO.class);
        if(Objects.isNull(bodyObject.getOperationChannelType())){
            bodyObject.setOperationChannelType(OperationChannelType.WEB.getKey());
        }
        FinhubLogger.info("企业信息管理-账户管理-余额管理-可用余额账户转出,接口[IAcctFundMgrService#transferOut2Others]-请求信息{}",JsonUtils.toJson(bodyObject));
        AcctCommonOptRespDTO acctCommonOptRespDTO = iAcctFundMgrService.transferOut2Others(bodyObject);
        FinhubLogger.info("企业信息管理-账户管理-余额管理-可用余额账户转出,接口[IAcctFundMgrService#transferOut2Others]-响应信息信息{}",JsonUtils.toJson(acctCommonOptRespDTO));
        ResponseResultUtils.success(response, acctCommonOptRespDTO);
    }

    /**
     * 修改短信接收人
     * @param request
     * @param response
     */
    @HttpService(value = "/update/receiver",method = RequestMethod.POST)
    public void updateReceiver(HttpRequest request,HttpResponse response){
        AcctReceiverUpdateDTO bodyObject = request.getBodyObject(AcctReceiverUpdateDTO.class);
        AcctRechargeReceiverUpdateDTO acctRechargeReceiverUpdateDTO = buildRechargeReceiverUpdateDTO(bodyObject);
        iPayAccountService.updateAcctRechargeReceiver(acctRechargeReceiverUpdateDTO);
        ResponseResultUtils.success(response,null);
    }

    private AcctRechargeReceiverUpdateDTO buildRechargeReceiverUpdateDTO(AcctReceiverUpdateDTO bodyObject) {
        AcctRechargeReceiverUpdateDTO acctRechargeReceiverUpdateDTO = new AcctRechargeReceiverUpdateDTO();
        if(Objects.nonNull(bodyObject)){
            acctRechargeReceiverUpdateDTO.setCompanyId(bodyObject.getCompanyId());
            List<AcctReceiverSmsDTO> receiverInfo = bodyObject.getReceiverInfo();
            List<RechargeSmsDTO> rechargeSmsDTOS = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(receiverInfo)){
                for (AcctReceiverSmsDTO acctReceiverSmsDTO : receiverInfo) {
                    RechargeSmsDTO rechargeSmsDTO = new RechargeSmsDTO();
                    rechargeSmsDTO.setEmployeeId(acctReceiverSmsDTO.getEmployeeId());
                    rechargeSmsDTO.setEmployeeName(acctReceiverSmsDTO.getEmployeeName());
                    rechargeSmsDTOS.add(rechargeSmsDTO);
                }
            }
            acctRechargeReceiverUpdateDTO.setRechargeSmsDTOList(rechargeSmsDTOS);
        }
        return acctRechargeReceiverUpdateDTO;
    }

    /**
     * 获取集团下公司列表 (返回所在集团所有公司)
     * <AUTHOR>
     * @date 2022-07-05 18:08:12
     */
    @HttpService(value = "/group/getCompanyList", method = RequestMethod.GET)
    public void getGroupCompanyList(HttpRequest request, HttpResponse response) {
        UserLoginVo userLogin = getUserLogin(request);
        String groupId = userLogin.getUser_info().getGroupId();
        String employeeId = userLogin.getUser_info().getId();
        try {
            List<CompanyInfoVO> companyInfoVOList = new ArrayList<>();
            if (!StringUtils.isBlank(groupId)) {
                companyInfoVOList = employeeService.getCompanyInfoByEmployeeId(employeeId);
            }
            ResponseResultUtils.success(response, companyInfoVOList);
        } catch (Exception e) {
            FinhubLogger.error("集团版查询公司列表getGroupCompanyList error groupId:" + groupId, e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode()));
        }
    }
    
    /**
     * 企业是否做过Airwallex授权
     * @param request
     * @param response
     */
    @HttpService(value = "/is/aw/auth")
    public void isAuthorizedByAirwallex(HttpRequest request, HttpResponse response) {
    	AuthRespDTO resp = companyAuthService.queryAuthResultByAirwallex(QueryAuthRequestDTO.builder().companyId(getUserCompanyId(request)).build());
    	ResponseResultUtils.success(response, resp);
    }
    
    /**
     * 企业进行Airwallex授权
     * @param request
     * @param response
     */
    @HttpService(value = "/aw/auth")
    public void authorize(HttpRequest request, HttpResponse response) {
    	String companyId = getUserCompanyId(request);
    	String authCode = request.getParameter("authCode");
    	AuthResultDTO result = companyAuthInnerService.authorize(AuthRequestDTO.builder().companyId(companyId).authCode(authCode).build());
    	ResponseResultUtils.success(response, result);
    }
    
    /**
     * 查询企业Airwallex账户详情
     * @param request
     * @param response
     */
    @HttpService(value = "/aw/acct/detail")
    public void awAcctDetail(HttpRequest request, HttpResponse response) {
    	String companyId = getUserCompanyId(request);
    	AirwallexAcctDetailRespDTO result = companyAuthService.queryAirwallexAcctDetail(QueryAuthRequestDTO.builder().companyId(companyId).build());
    	ResponseResultUtils.success(response, result);
    }

    /**
     * 查询企业信息,判断是否为试用版
     * @param request
     * @param response
     */
    @HttpService(value = "/info", method = RequestMethod.GET)
    public void info(HttpRequest request, HttpResponse response) {
        String companyId = getOperationCompanyId(request);
        Map<String,Object> resultMap = Maps.newHashMap();
        try {
            boolean isTrialAndMarketVersion = checkTrailCompany(companyId);
            if (isTrialAndMarketVersion){
                resultMap.put("rechargeAcctHide",true);
                ResponseResultUtils.success(response,resultMap);
                return;
            }
        }catch (Exception e){
            FinhubLogger.error("试用版查询UC异常",e);
        }
        resultMap.put("rechargeAcctHide",false);
        ResponseResultUtils.success(response,resultMap);
    }

    /**
     * 提前还款账户信息
     * <AUTHOR>
     * @date 2023-07-14 10:58:27
     */
    @HttpService(value = "/prepayment/info", method = RequestMethod.POST)
    public void prepaymentInfo(HttpRequest request, HttpResponse response) {
        AcctComGwByMIdBankReqDTO req = request.getBodyObject(AcctComGwByMIdBankReqDTO.class);
        FinhubLogger.info("提前还款账户信息req:{}", JSON.toJSONString(req));
        ValidateUtils.validate(req);
        AcctCreditMainRespDTO info;
        try {
            info = uAcctCommonCreditService.findPrepaymentCreditMain(req);
        }catch (Exception e){
            FinhubLogger.warn("提前还款账户信息", e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalCoreResponseCode.EXCEPTION.getCode()));
            return;
        }
        ResponseResultUtils.success(response, info);
    }

    /**
     * 提前还款账户信息
     * <AUTHOR>
     * @date 2023-07-14 10:58:27
     */
    @HttpService(value = "/prepayment", method = RequestMethod.POST)
    public void prepayment(HttpRequest request, HttpResponse response) {
        PrepaymentReq req = request.getBodyObject(PrepaymentReq.class);
        FinhubLogger.info("prepayment提前还款账户信息req:{}", JSON.toJSONString(req));
        UserLoginVo userLogin = getUserLogin(request);
        UserInfoVO userInfo = userLogin.getUser_info();
        req.setUserId(userInfo.getId());
        req.setUserName(userInfo.getName());
        req.setCompanyId(userLogin.getCompany_info().getId());
        ResultDTO<Void> res;
        boolean lock = false;
        String lockKey = CoreConstant.PREPAYMENT_PREFIX + req.getCompanyId();
        try {
            lock = redissonService.tryLock(CoreConstant.ACCT_ADJUST_CREDIT_WAIT_TIME, CoreConstant.ACCT_ADJUST_CREDIT_LOCK_TIME, TimeUnit.MILLISECONDS, lockKey);
            if (!lock) {
                FinhubLogger.error("提前还款账户信息 error adjustToSub reqDto:{}", JSON.toJSONString(req));
                throw new FinhubException(GlobalResponseCode.REDIS_GET_LOCK_ERROR.getCode(), GlobalResponseCode.REDIS_GET_LOCK_ERROR.getType(), GlobalResponseCode.REDIS_GET_LOCK_ERROR.getMsg());
            }
            res = uAcctCommonCreditService.prepaymentCredit(req);
        }catch (Exception e){
            FinhubLogger.warn("prepayment提前还款信息", e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), e.getMessage()));
            return;
        } finally {
            if (lock) {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    FinhubLogger.error("提前还款账户信息释放锁失败,lockKey：{}", lockKey, e);
                }
            }
        }
        ResponseResultUtils.success(response, res);
    }


    /**
     * 平安电子回单列表
     * <AUTHOR>
     * @date 2023-07-14 10:58:27
     */
    @HttpService(value = "/spa/receipt", method = RequestMethod.POST)
    public void spaReceipt(HttpRequest request, HttpResponse response) {
        SpaReceiptQueryReqDTO req = request.getBodyObject(SpaReceiptQueryReqDTO.class);
        FinhubLogger.info("平安电子回单查询 req:{}", JSON.toJSONString(req));
        ValidateUtils.validate(req);
        SpaReceiptQueryRespDTO info;
        try {
            info = iBankCardTrapFlowManager.queryReceipt(req);
        }catch (Exception e){
            FinhubLogger.warn("平安电子回单查询异常", e);
            ResponseResultUtils.fail(response, new FinhubException(GlobalCoreResponseCode.EXCEPTION.getCode()));
            return;
        }
        ResponseResultUtils.success(response, info);
    }

}