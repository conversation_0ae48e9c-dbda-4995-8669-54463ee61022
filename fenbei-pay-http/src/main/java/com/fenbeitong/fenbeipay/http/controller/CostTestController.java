package com.fenbeitong.fenbeipay.http.controller;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.CategoryCostEnum;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.finhub.kafka.msg.order.common.BudgetOperateInfo;
import com.fenbeitong.finhub.kafka.msg.order.common.BudgetOrderInfo;
import com.fenbeitong.finhub.kafka.msg.saturn.KafkaSaturnOrderMsg;
import com.fenbeitong.finhub.kafka.producer.IKafkaProducerPublisher;
import com.fenbeitong.saasplus.api.model.dto.finance.CostSaveResult;
import com.fenbeitong.saasplus.api.model.dto.finance.OrderCostInfoReq;
import com.fenbeitong.saasplus.api.service.finance.IOrderCostService;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/1 17:52
 * @description:
 */
@Slf4j
@HttpService("/cost/test")
public class CostTestController {

    @Autowired
    private IKafkaProducerPublisher iKafkaProducerPublisher;
    @Autowired
    private IOrderCostService iOrderCostService;

    /**
     * 编辑定时发券任务信息
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/saveCost", method = RequestMethod.GET)
    public void saveCost(HttpRequest request, HttpResponse response) {
        ResponseResultUtils.success(response, saveCost());
    }

    /**
     * 编辑定时发券任务信息
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/push", method = RequestMethod.GET)
    public void costPush(HttpRequest request, HttpResponse response) {
        String s ="{    \"status\": \"polling\",    \"billstatus\": \"got\",    \"message\": \"寄件\",    \"lastResult\": {        \"message\": \"ok\",        \"nu\": \"JD0099139136962\",        \"ischeck\": \"0\",        \"com\": \"jd\",        \"status\": \"200\",        \"data\": [{                \"time\": \"2021-12-15 20:15:14\",                \"context\": \"【苏州转运中心】 已发出 下一站 【无锡转运中心公司】\",                \"ftime\": \"2021-12-15 20:15:14\",                \"areaCode\": \"CN320500000000\",                \"areaName\": \"江苏,苏州市\",                \"status\": \"干线\",                \"location\": \"\",                \"areaCenter\": \"120.585315,31.298886\",                \"areaPinYin\": \"su zhou shi\",                \"statusCode\": \"1002\"            },            {                \"time\": \"2021-12-15 20:11:25\",                \"context\": \"【苏州转运中心公司】 已收入\",                \"ftime\": \"2021-12-15 20:11:25\",                \"areaCode\": \"CN320500000000\",                \"areaName\": \"江苏,苏州市\",                \"status\": \"干线\",                \"location\": \"\",                \"areaCenter\": \"120.585315,31.298886\",                \"areaPinYin\": \"su zhou shi\",                \"statusCode\": \"1002\"            },            {                \"time\": \"2021-12-15 19:18:27\",                \"context\": \"【江苏省无锡市锡新开发区公司】 已收入\",                \"ftime\": \"2021-12-15 19:18:27\",                \"areaCode\": \"CN320200000000\",                \"areaName\": \"江苏,无锡市\",                \"status\": \"在途\",                \"location\": \"\",                \"areaCenter\": \"120.31191,31.491169\",                \"areaPinYin\": \"wu xi shi\",                \"statusCode\": \"0\"            },            {                \"time\": \"2021-12-15 17:10:09\",                \"context\": \"【江苏省苏州市北桥公司】 已揽收\",                \"ftime\": \"2021-12-15 17:10:09\",                \"areaCode\": \"CN320507004000\",                \"areaName\": \"江苏,苏州市,相城区,北桥\",                \"status\": \"揽收\",                \"location\": \"\",                \"areaCenter\": \"120.606531,31.505825\",                \"areaPinYin\": \"bei qiao jie dao\",                \"statusCode\": \"1\"            }        ],        \"state\": \"0\",        \"condition\": \"F00\",        \"routeInfo\": {            \"from\": {                \"number\": \"CN320507004000\",                \"name\": \"江苏,苏州市,相城区,北桥\"            },            \"cur\": {                \"number\": \"CN320200000000\",                \"name\": \"江苏,无锡市\"            },            \"to\": null        },        \"isLoop\": false    }}";
        //校验权限
        int status = request.getIntParameter("status");
        String voucherTaskId = request.getParameter("voucherTaskId");
        String costId = request.getParameter("costId");
        KafkaSaturnOrderMsg msg = null;
        switch (status) {
            //释放全部占用
            case 10:
                msg = releaseCost(voucherTaskId, costId);
                break;
            //扣减
            case 20:
                msg = subCost(voucherTaskId, costId);
                break;
            //返还
            case 30:
                msg = returnCost(voucherTaskId, costId);
                break;
            default:
                log.error("不匹配");
                break;
        }
        ResponseResultUtils.success(response, msg);
    }

    /**
     * 扣减
     */
    private KafkaSaturnOrderMsg subCost(String voucherTaskId, String costId) {
        KafkaSaturnOrderMsg msg = new KafkaSaturnOrderMsg();
        msg.setCompanyId("5747fbc10f0e60e0709d8d7d");
        BudgetOperateInfo info = new BudgetOperateInfo();
        info.setCostId(costId);
        info.setCostInfoType(CategoryCostEnum.VOUCHER_COST.getCode());
        info.setRootOrderId(voucherTaskId);
        info.setCompanyPrice(new BigDecimal("1"));
        info.setOperate(20);
        msg.setBudgetOperateInfo(info);
        msg.setFbOrderId(voucherTaskId);
        log.info("【分贝券任务】预算扣减，request={}", JSONObject.toJSONString(msg));
        iKafkaProducerPublisher.publish(msg);
        return msg;
    }

    /**
     * 释放
     */
    private KafkaSaturnOrderMsg releaseCost(String voucherTaskId, String costId) {
        KafkaSaturnOrderMsg msg = new KafkaSaturnOrderMsg();
        msg.setCompanyId("5747fbc10f0e60e0709d8d7d");
        BudgetOperateInfo info = new BudgetOperateInfo();
        info.setCostId(costId);
        info.setCostInfoType(CategoryCostEnum.VOUCHER_COST.getCode());
        info.setRootOrderId(voucherTaskId);
        info.setCompanyPrice(new BigDecimal("0.5"));
        info.setOperate(10);
        msg.setBudgetOperateInfo(info);
        msg.setFbOrderId(voucherTaskId);
        log.info("【分贝券任务】预算释放，request={}", JSONObject.toJSONString(msg));
        iKafkaProducerPublisher.publish(msg);
        return msg;
    }

    /**
     * 返还
     */
    private KafkaSaturnOrderMsg returnCost(String voucherTaskId, String costId) {
        KafkaSaturnOrderMsg msg = new KafkaSaturnOrderMsg();
        msg.setCompanyId("5747fbc10f0e60e0709d8d7d");
        BudgetOperateInfo info = new BudgetOperateInfo();
        info.setCostId(costId);
        info.setCostInfoType(CategoryCostEnum.VOUCHER_COST.getCode());
        info.setRootOrderId(voucherTaskId);
        info.setCompanyPrice(new BigDecimal("0.5"));
        info.setOperate(30);
        msg.setBudgetOperateInfo(info);
        msg.setFbOrderId(voucherTaskId);

        List<BudgetOrderInfo> budgetOrderInfos = Lists.newArrayList();
        BudgetOrderInfo budgetOrderInfo = new BudgetOrderInfo();
        budgetOrderInfo.setCostId(costId);
        budgetOrderInfo.setCompanyPrice(new BigDecimal("0.5"));
        budgetOrderInfo.setOrderId(voucherTaskId);
        budgetOrderInfos.add(budgetOrderInfo);
        info.setBudgetOrderInfos(budgetOrderInfos);
        info.setCostInfoType(1);
        log.info("【分贝券任务】预算返还，request={}", JSONObject.toJSONString(msg));
        iKafkaProducerPublisher.publish(msg);
        return msg;
    }

    private CostSaveResult saveCost() {
        String json = "{\n" +
                "    \"costCategory\": {\n" +
                "      \"id\": \"10140\",\n" +
                "      \"name\": \"qewqewq\",\n" +
                "      \"formid\": \"6133174d13a9964a4d1271e2\"\n" +
                "    },\n" +
                "    \"costAttributionGroupList\": [\n" +
                "      {\n" +
                "        \"category\": 1,\n" +
                "        \"categoryName\": \"部门\",\n" +
                "        \"costAttributionList\": [\n" +
                "          {\n" +
                "            \"id\": \"5c0be90c23445f32337a9ec2\",\n" +
                "            \"name\": \"研发部\",\n" +
                "            \"price\": 1,\n" +
                "            \"weight\": 100\n" +
                "          }\n" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"category\": 3,\n" +
                "        \"categoryName\": \"测试100\",\n" +
                "        \"costAttributionList\": [\n" +
                "          {\n" +
                "            \"id\": \"632ab40f3e89c240746d789b\",\n" +
                "            \"name\": \"档案测试0000005\",\n" +
                "            \"price\": 1,\n" +
                "            \"weight\": 100\n" +
                "          }\n" +
                "        ],\n" +
                "        \"recordId\": \"604ec75427f65fae8d989b26\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"costAttributionRelationList\": []\n" +
                "  }";
        OrderCostInfoReq orderCostInfo = new OrderCostInfoReq();
        orderCostInfo.setAmount(new BigDecimal("1"));
        orderCostInfo.setOrderId(IDGen.getVoucherTaskId("5747fbc10f0e60e0709d8d7d"));
        orderCostInfo.setCategory(CategoryCostEnum.VOUCHER_COST.getCode());
        orderCostInfo.setForce(false);
        orderCostInfo.setUserId("6368bc9c5d1bf1487d90ff4c");
        orderCostInfo.setCompanyId("5747fbc10f0e60e0709d8d7d");
        orderCostInfo.setCostInfoString(json);
        log.info("分贝券发放预算占用, request：{}", JSONObject.toJSONString(orderCostInfo));
        CostSaveResult costSaveResult = iOrderCostService.asyncSaveCost(orderCostInfo);
        log.info("分贝券发放预算占用，response：{}, request：{}", JSONObject.toJSONString(costSaveResult), JSONObject.toJSONString(orderCostInfo));
        return costSaveResult;
    }
}
