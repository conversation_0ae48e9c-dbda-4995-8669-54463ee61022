package com.fenbeitong.fenbeipay.http.controller.transfer.req;

import com.fenbeitong.finhub.common.exception.FinhubException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;

/**
 * @author: guoguangxiao
 * @date: 2022-11-21 16:26:59
 * @description:
 */
@Data
public class TransferBiz2GeneralReq implements Serializable {

    /**
     * 公司Id
     */
    private String companyId;

    /**
     * 公司主体Id
     */
    private String companyMainId;

    /**
     * 银行名称
     * e.g. CGB
     */
    private String bankName;

    private String bankAccountNo;

    /**
     * 操作人Id system
     */
    private String operationUserId;
    /**
     * 操作人姓名 系统操作
     */
    private String operationUserName;

    /**
     * 转账金额
     */
    private BigDecimal operationAmount;

    /**
     * 对手银行名称
     */
    private String targetBankName;
    /**
     * 对手账户id(付款方)
     */
    private String targetBankAccountNo;

    /**
     * 账户类型
     * 2：商户消费
     * 3：补助福利
     * 5：备用金
     */
    private Integer accountSubType;

    /**
     * 转出方账户id
     */
    private String  accountId;

    /**
     * 操作渠道类型:  默认5
     */
    private Integer  operationChannelType = 5;

    private String remark;

    public void check() {
        checkEmpty(companyId, "公司Id");
        checkEmpty(companyMainId, "公司主体Id");
        checkEmpty(bankName, "银行名称");
        checkEmpty(bankAccountNo, "账户号");
        checkEmpty(operationUserId, "操作人id");
        checkEmpty(operationUserName, "操作人名称");
        if(operationAmount == null || operationAmount.compareTo(BigDecimal.ZERO)<=0){
            throw new FinhubException(1, "转账金额必须为正数，单位为分operationAmount");
        }
        checkEmpty(targetBankName, "对手银行名称");
        checkEmpty(targetBankAccountNo, "对手账户号");
        if(accountSubType==null || !Arrays.asList(2,3,5).contains(accountSubType)){
            throw new FinhubException(1, "账户类型错误accountSubType");
        }
        checkEmpty(accountId, "转出方账户id");
    }

    private void checkEmpty(String param, String paramDesc){
        if(StringUtils.isBlank(param)){
            throw new FinhubException(1, paramDesc+"不能为空:"+param);
        }
    }
}


