package com.fenbeitong.fenbeipay.http.controller.cashier;


import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierSearchMultipleRefundTradeRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierSearchMultipleTradeRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierSearchPayAmountRpcDTO;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierSearchPayStatusRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierQueryReqRPCVo;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierSearchBatchPageReqRPCVo;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.CashierSearchOrderRefundService;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * (内部系统调用的HTTP服务)收银台收款Http服务Controller
 */

@HttpService("/internal/cashier/search")
public class InnerCashierSearchOrderController {

    @Autowired
    private CashierSearchOrderService cashierSearchOrderService;


    @Autowired
    private CashierSearchOrderRefundService cashierSearchOrderRefundService;



    /**
     * 剑锋统计使用
     * 场景批量查询支付单[暂定最大500条]
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/batch/v2",method = RequestMethod.POST)
    public void searchMultipleBatch(HttpRequest request, HttpResponse response){
        CashierSearchBatchPageReqRPCVo cashierBatchQueryReqVo = request.getBodyObject(CashierSearchBatchPageReqRPCVo.class);
        cashierBatchQueryReqVo.setIgnorePageInfor(true);
        ValidateUtils.validate(cashierBatchQueryReqVo);
        long currentTimeStamp = System.currentTimeMillis();
        ResponsePage<CashierSearchMultipleTradeRPCDTO> settlement = cashierSearchOrderService.searchMultipleBatchByPage(cashierBatchQueryReqVo);
        FinhubLogger.info("【批量查询支付单】耗时->{} 参数->{}", (System.currentTimeMillis() - currentTimeStamp), JSON.toJSONString(cashierBatchQueryReqVo));
        ResponseResultUtils.success(response,settlement);
    }
    
    @HttpService(value = "/batch/v3",method = RequestMethod.POST)
    public void queryByOptimizedParam(HttpRequest request, HttpResponse response){
        CashierSearchBatchPageReqRPCVo param = request.getBodyObject(CashierSearchBatchPageReqRPCVo.class);
        List<CashierSearchMultipleTradeRPCDTO> settlements = cashierSearchOrderService.queryByOptimizedParam(param);
        ResponseResultUtils.success(response,settlements);
    }


    /**
     * 剑锋统计使用
     * 场景批量查询支付单[暂定最大500条]
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/batch/refund/v2",method = RequestMethod.POST)
    public void searchMultipleBatchRefund(HttpRequest request, HttpResponse response){
        CashierSearchBatchPageReqRPCVo cashierBatchQueryReqVo = request.getBodyObject(CashierSearchBatchPageReqRPCVo.class);
        ValidateUtils.validate(cashierBatchQueryReqVo);
        ResponsePage<CashierSearchMultipleRefundTradeRPCDTO> settlement = cashierSearchOrderRefundService.searchMultipleBatchRefundByPage(cashierBatchQueryReqVo);
        ResponseResultUtils.success(response,settlement);
    }



    /**
     *
     * 只查询收银台状态
     * @param request
     * @param response
     * @see com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayStatus
     * @return
     */
    @HttpService(value = "/pay/status/v1",method = RequestMethod.POST)
    public void searchPayStatus(HttpRequest request, HttpResponse response){
        CashierQueryReqRPCVo cashierQueryReqRPCVo = request.getBodyObject(CashierQueryReqRPCVo.class);
        ValidateUtils.validate(cashierQueryReqRPCVo);
        CashierSearchPayStatusRPCDTO rpcdto = cashierSearchOrderService.searchPayStatus(cashierQueryReqRPCVo);
        ResponseResultUtils.success(response,rpcdto);
    }

    /**
     *
     * 只查询收银台状态
     * @param request
     * @param response
     * @see com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayStatus
     * @return
     */
    @HttpService(value = "/pay/amount/v1",method = RequestMethod.POST)
    public void searchPayAmount(HttpRequest request, HttpResponse response){
        CashierQueryReqRPCVo cashierQueryReqRPCVo = request.getBodyObject(CashierQueryReqRPCVo.class);
        ValidateUtils.validate(cashierQueryReqRPCVo);
        CashierSearchPayAmountRpcDTO cashierSearchPayAmountRpcDTO = cashierSearchOrderService.searchPayAmount(cashierQueryReqRPCVo);
        ResponseResultUtils.success(response,cashierSearchPayAmountRpcDTO);
    }
}
