package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.*;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersBusinessFlowRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VoucherForStatisticsRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersFlowRPCDTO;
import com.fenbeitong.fenbeipay.api.service.external.IExternalVoucherRpcService;
import com.fenbeitong.fenbeipay.cashier.transfer.PersonTransferService;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.voucher.VoucherTaskImportStatus;
import com.fenbeitong.fenbeipay.core.enums.voucher.VoucherTaskStatus;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlow;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasks;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImport;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherCommonUtils;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherConstant;
import com.fenbeitong.fenbeipay.vouchers.dto.OperationUserInfoDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersGrantTaskCreateDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersGrantTaskDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersImportTaskDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskCreateDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskImportDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskTimingBirthdayDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskTimingCheckDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskTimingCreateDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskTimingDetailRespDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTaskTimingRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTasksRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersWithdrawTasksDTO;
import com.fenbeitong.fenbeipay.vouchers.service.VoucherHandleService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersGrantRecoveryTaskService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskHandleService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskImportService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskService;
import com.fenbeitong.fenbeipay.vouchers.task.service.VouchersTaskTimingService;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTempletService;
import com.fenbeitong.fenbeipay.vouchers.utils.ThreadPoolUtil;
import com.fenbeitong.fenbeipay.vouchers.vo.*;
import com.fenbeitong.finhub.common.constant.ToBillStatusEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyFbqRuleDTO;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 分贝券企业web管理controller
 * @ClassName: VouchersForWebController
 * @Author: zhangga
 * @CreateDate: 2018/12/11 下午6:48
 * @UpdateUser:
 * @UpdateDate: 2018/12/11 下午6:48
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Slf4j
@HttpService("/vouchers/management")
public class VouchersTaskController extends BaseController {
    @Autowired
    private VouchersGrantRecoveryTaskService vouchersGrantRecoveryTaskService;
    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    private VouchersTaskHandleService vouchersTaskHandleService;
    @Autowired
    private VouchersTaskTimingService vouchersTaskTimingService;
    @Autowired
    private VouchersTaskImportService vouchersTaskImportService;
    @Autowired
    private ICompanyService iCompanyService;
    private static final String VOUCHERS_TASK_ID = "vouchersTaskId";
    private static final String VOUCHERS_TASK_TIMING_ID = "vouchersTaskTimingId";

    @Autowired
    private VouchersTempletService vouchersTempletService;
    @Autowired
    private VoucherCommonUtils voucherCommonUtils;
    @Autowired
    private VouchersTaskService vouchersTaskService;

    @Autowired
    private VoucherHandleService voucherHandleService;

    @Autowired
    private PersonTransferService personTransferService;

    /**
     * @Description: 创建分贝券发放任务
     * @methodName: grantVouchers
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:13 PM
     **/
    @HttpService(value = "/grant/vouchers/v3", method = RequestMethod.POST)
    public void grantVouchers(HttpRequest request, HttpResponse response) {
        //Step1. 校验当前用户是否有权限操作
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);

        VouchersGrantTaskCreateDTO tasksDTO = request.getBodyObject(VouchersGrantTaskCreateDTO.class);

        //Step2. 校验开票权限
        checkInvoicePrivilege(operationUserInfoDTO, tasksDTO.getWriteInvoiceType());
        log.info("companyId:{}", operationUserInfoDTO.getCompanyId());

        //Step3. 查询企业分贝券规则
        CompanyFbqRuleDTO companyFbqRuleDTO = iCompanyService.queryCompanyFbqRule(operationUserInfoDTO.getCompanyId());
        log.info("companyFbqRuleDTO:{}", companyFbqRuleDTO);

        //Step4. 校验分贝券有效期
        tasksDTO.checkParameters(companyFbqRuleDTO);
        BeanUtils.copyProperties(operationUserInfoDTO, tasksDTO);

        //Step5. 校验是否配置预算管控
        VoucherCostStatusEnum costStatusEnum = checkCostConfig(tasksDTO.getCompanyId(), tasksDTO.getCostInfo(), tasksDTO.getDateOfExpense());
        tasksDTO.setCostStatus(costStatusEnum.getStatus());

        //Step6. 创建企业分贝券发放任务（企业管理员）
        VouchersGrantRecoveryTasks grantTask = vouchersGrantRecoveryTaskService.createVouchersGrantTask(tasksDTO);
        CompletableFuture.runAsync(() -> vouchersGrantRecoveryTaskService.addVouchersTaskDetailsGrant(grantTask), ThreadPoolUtil.getVoucherThreadExecutor());
        ResponseResultUtils.success(response, grantTask.getTaskId());
    }



    @HttpService(value = "/retry/grant/vouchers/{vouchersTaskId}/v3", method = RequestMethod.GET)
    public void retryGrantVouchers(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String vouchersTaskId = request.getPathValue("vouchersTaskId");
        if (StringUtils.isEmpty(vouchersTaskId) || VOUCHERS_TASK_ID.equals(vouchersTaskId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        boolean taskId = vouchersTaskHandleService.retryStartTask(vouchersTaskId, operationUserInfoDTO);
        ResponseResultUtils.success(response, taskId);
    }

    /**
     * @Description: 查询可撤回分贝券
     * @methodName: queryCanWithdrawalVouchersByTaskId
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 4:35 PM
     **/
    @HttpService(value = "/query/can/withdrawal/vouchers/v3", method = RequestMethod.POST)
    public void queryCanWithdrawalVouchersByTaskId(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersRequestDTO requestDTO = request.getBodyObject(VouchersRequestDTO.class);
        String companyId = operationUserInfoDTO.getCompanyId();
        String taskId = requestDTO.getTaskId();
        requestDTO.setCompanyId(companyId);
        checkTaskStatus(taskId, companyId);
        ResponsePage<VoucherForWebVO> responsePage = vouchersPersonService.selectCanWithdrawalVouchers(requestDTO);
        ResponseResultUtils.success(response, responsePage);
    }

    /**
     * @Description: 创建分贝券撤回任务
     * @methodName: withdrawVouchers
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:14 PM
     **/
    @HttpService(value = "/withdraw/vouchers/v3", method = RequestMethod.POST)
    public void withdrawVouchers(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String companyId = operationUserInfoDTO.getCompanyId();
        VouchersWithdrawTasksDTO withdrawTasksDTO = request.getBodyObject(VouchersWithdrawTasksDTO.class);
        String grantTaskId = withdrawTasksDTO.getTaskId();
        checkTaskStatus(grantTaskId, companyId);
        BeanUtils.copyProperties(operationUserInfoDTO, withdrawTasksDTO);
        CompletableFuture.runAsync(() -> createWithdrawTask(withdrawTasksDTO), ThreadPoolUtil.getVoucherThreadExecutor());
        ResponseResultUtils.success(response, "ok");
    }

    private void createWithdrawTask(VouchersWithdrawTasksDTO withdrawTasksDTO) {
        try {
            String taskId = vouchersGrantRecoveryTaskService.createVouchersWithdrawTask(withdrawTasksDTO);
            vouchersGrantRecoveryTaskService.publishAndStart(taskId);
            log.info("【分贝券撤回】【启动任务】，任务ID:{}", taskId);
        } catch (Exception e) {
            log.error("【分贝券撤回异常】参数：{}", JSON.toJSONString(withdrawTasksDTO), e);
            e.printStackTrace();
        }
    }

    /**
     * @Description: 分贝券发放任务列表查询
     * @methodName: queryGrantTaskList
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:14 PM
     **/
    @HttpService(value = "/query/grant/task/list/v3", method = RequestMethod.POST)
    public void queryGrantTaskList(HttpRequest request, HttpResponse response) {
        // 校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);

        VouchersTasksRequestDTO tasksRequestDTO = request.getBodyObject(VouchersTasksRequestDTO.class);
        tasksRequestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        ResponsePage<VouchersTaskVO> vouchersTaskList = vouchersTaskHandleService.queryGrantTaskList(tasksRequestDTO);
        ResponseResultUtils.success(response, vouchersTaskList);
    }

    /**
     * @Description: 发券任务详情
     * @methodName: queryGrantInfoByTaskId
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:15 PM
     **/
    @HttpService(value = "/query/grant/info/v3", method = RequestMethod.POST)
    public void queryGrantInfoByTaskId(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);

        VouchersRequestDTO requestDTO = request.getBodyObject(VouchersRequestDTO.class);
        String companyId = operationUserInfoDTO.getCompanyId();
        requestDTO.setCompanyId(companyId);
        ResponsePage<VoucherGrantInfoVO> responsePage = vouchersTaskHandleService.selectGrantInfo(requestDTO);
        ResponseResultUtils.success(response, responsePage);
    }


    /**
     * @Description: 查询撤回详情
     * @methodName: queryWithdrawalInfoByGrantTaskId
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 4:37 PM
     **/
    @HttpService(value = "/query/withdrawal/info/v3", method = RequestMethod.POST)
    public void queryWithdrawalInfoByGrantTaskId(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersRequestDTO requestDTO = request.getBodyObject(VouchersRequestDTO.class);
        requestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        ResponsePage<VoucherWithdrawalInfoVO> responsePage = vouchersTaskHandleService.selectWithdrawalInfo(requestDTO);
        ResponseResultUtils.success(response, responsePage);
    }


    @HttpService(value = "/add/timing/task/v3", method = RequestMethod.POST)
    public void addTimingTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersTaskTimingCreateDTO createDTO = request.getBodyObject(VouchersTaskTimingCreateDTO.class);
        log.info("operationUserInfoDTO:{}", operationUserInfoDTO);
        CompanyFbqRuleDTO companyFbqRuleDTO = iCompanyService.queryCompanyFbqRule(operationUserInfoDTO.getCompanyId());
        log.info("companyFbqRuleDTO:{}", companyFbqRuleDTO);
        createDTO.checkParams(companyFbqRuleDTO);
        BeanUtils.copyProperties(operationUserInfoDTO, createDTO);
        //公司配置校验
        VoucherCostStatusEnum voucherCostStatusEnum = checkCostConfig(operationUserInfoDTO.getCompanyId(), createDTO.getCostInfo());
        createDTO.setCostStatus(voucherCostStatusEnum.getStatus());
        vouchersTaskTimingService.addTimingTask(createDTO);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/timing/check", method = RequestMethod.POST)
    public void timingCheck(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersTaskTimingBirthdayDTO dto = request.getBodyObject(VouchersTaskTimingBirthdayDTO.class);
        log.info("operationUserInfoDTO:{}", operationUserInfoDTO);
        dto.setCompanyId(operationUserInfoDTO.getCompanyId());
        dto.setCheckAll(true);
        VouchersTaskTimingBirthdayVO vo = vouchersTaskTimingService.timingCheck(dto);
        ResponseResultUtils.success(response, vo);
    }



    @HttpService(value = "/query/timing/task/list/v3", method = RequestMethod.POST)
    public void queryTimingTaskList(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersTaskTimingRequestDTO requestDTO = request.getBodyObject(VouchersTaskTimingRequestDTO.class);
        requestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        ResponsePage<VouchersTaskTimingVO> responsePage = vouchersTaskTimingService.queryTimingTaskList(requestDTO);
        ResponseResultUtils.success(response, responsePage);
    }

    @HttpService(value = "/enable/timing/task/{vouchersTaskTimingId}/v3", method = RequestMethod.POST)
    public void enableTimingTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String vouchersTaskId = request.getPathValue("vouchersTaskTimingId");
        if (StringUtils.isEmpty(vouchersTaskId) || VOUCHERS_TASK_TIMING_ID.equals(vouchersTaskId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        log.info("【启用定时任务】:{}", vouchersTaskId);
        vouchersTaskTimingService.enableTimingTask(operationUserInfoDTO, vouchersTaskId);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/stop/timing/task/{vouchersTaskTimingId}/v3", method = RequestMethod.POST)
    public void stopTimingTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String vouchersTaskId = request.getPathValue("vouchersTaskTimingId");
        if (StringUtils.isEmpty(vouchersTaskId) || VOUCHERS_TASK_TIMING_ID.equals(vouchersTaskId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        log.info("【停用定时任务】:{}", vouchersTaskId);
        vouchersTaskTimingService.stopTimingTask(operationUserInfoDTO, vouchersTaskId);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/delete/timing/task/{vouchersTaskTimingId}/v3", method = RequestMethod.POST)
    public void deleteTimingTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String vouchersTaskId = request.getPathValue("vouchersTaskTimingId");
        if (StringUtils.isEmpty(vouchersTaskId) || VOUCHERS_TASK_TIMING_ID.equals(vouchersTaskId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        log.info("【删除定时任务】:{}", vouchersTaskId);
        vouchersTaskTimingService.deleteTimingTask(operationUserInfoDTO, vouchersTaskId);
        ResponseResultUtils.success(response);
    }

    /**
     * 导入任务及明细
     * @param request
     * @param response
     */
    @HttpService(value = "/add/import/task/v3", method = RequestMethod.POST)
    public void addImportTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String companyId = operationUserInfoDTO.getCompanyId();
        VouchersTaskImportDTO taskImportDTO = request.getBodyObject(VouchersTaskImportDTO.class);
        String filePath = taskImportDTO.getFilePath();
        String xlsx = filePath.substring(filePath.lastIndexOf(VoucherConstant.spot));
        if (!VoucherConstant.XLSX.equals(xlsx)) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_IMPORT_FILE_ERROR);
        }
        BeanUtils.copyProperties(operationUserInfoDTO, taskImportDTO);
        //检查是公司配置
        VoucherCostStatusEnum costStatusEnum = checkCostConfig(taskImportDTO.getCompanyId(), taskImportDTO.getCostInfo(), taskImportDTO.getDateOfExpense());
        taskImportDTO.setCostStatus(costStatusEnum.getStatus());

        String vouchersTaskImportId = vouchersTaskImportService.addImportTask(taskImportDTO);
        //启动使用异步程序
        CompletableFuture.runAsync(() -> vouchersTaskImportService.addImportTaskDetails(companyId, vouchersTaskImportId, filePath), ThreadPoolUtil.getVoucherThreadExecutor());
        ResponseResultUtils.success(response, vouchersTaskImportId);
    }

    @HttpService(value = "/query/import/task/completion/rate/{vouchersTaskImportId}/v3", method = RequestMethod.GET)
    public void queryImportTaskCompletionRate(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String vouchersTaskImportId = request.getPathValue("vouchersTaskImportId");
        if (StringUtils.isBlank(vouchersTaskImportId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        VouchersTaskImportVO vo = vouchersTaskImportService.queryImportTaskCompletionRate(operationUserInfoDTO.getCompanyId(), vouchersTaskImportId);
        ResponseResultUtils.success(response, vo);
    }

    @HttpService(value = "/query/import/task/{vouchersTaskImportId}/v3", method = RequestMethod.POST)
    public void queryImportTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String vouchersTaskImportId = request.getPathValue("vouchersTaskImportId");
        if (StringUtils.isBlank(vouchersTaskImportId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        PageBean pageBean = request.getBodyObject(PageBean.class);
        ResponsePage<VouchersTaskImportDetailsVO> responsePage = vouchersTaskImportService.queryImportTaskDetails(operationUserInfoDTO.getCompanyId(), vouchersTaskImportId, pageBean);
        ResponseResultUtils.success(response, responsePage);
    }

    @HttpService(value = "/start/import/task/{vouchersTaskImportId}/v3", method = RequestMethod.POST)
    public void startImportTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        //创建发放任务
        VouchersImportTaskDTO taskImportDTO =  new VouchersImportTaskDTO();
        taskImportDTO.setVouchersTaskImportId(request.getPathValue("vouchersTaskImportId"));
        taskImportDTO.setGrantType(GrantTypeEnum.IMMEDIATE_GRANT.getCode());
        executeGrantTask(operationUserInfoDTO,taskImportDTO);
        ResponseResultUtils.success(response);
    }

    /**
     * 批量发券发放分贝券执行（立即/定时）
     * @param request
     * @param response
     */
    @HttpService(value = "/start/import/task/v4", method = RequestMethod.POST)
    public void startImportScheduleTask(HttpRequest request, HttpResponse response) {
        VouchersImportTaskDTO taskImportDTO = request.getBodyObject(VouchersImportTaskDTO.class);
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        //创建发放任务
        executeGrantTask(operationUserInfoDTO,taskImportDTO);
        //响应返回
        ResponseResultUtils.success(response);
    }

    /**
     * 分贝券任务执行
     * @param userInfoDTO
     * @param taskImportDTO
     */
    private void executeGrantTask(OperationUserInfoDTO userInfoDTO,VouchersImportTaskDTO taskImportDTO){
        if (null == taskImportDTO) {
            log.error("【分贝券批量导入确认发放】参数校验异常,请求参数为空");
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_IMPORT_GRANT_FAIL);
        }
        if (Objects.nonNull(taskImportDTO.getGrantType()) && GrantTypeEnum.TIMING_GRANT.getCode() == taskImportDTO.getGrantType() && null == taskImportDTO.getTimingGrantTime()) {
            log.error("【分贝券批量导入确认发放】参数校验异常，任务详情：{}", taskImportDTO);
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_IMPORT_GRANT_FAIL);
        }
        //导入券任务ID
        String  vouchersTaskImportId  = taskImportDTO.getVouchersTaskImportId();
        //公司ID
        String  companyId  = userInfoDTO.getCompanyId();
        // 查询导入任务
        VouchersTaskImport vouchersTaskImport = vouchersTaskImportService.getVouchersTaskImportById(companyId, vouchersTaskImportId);
        Integer status = vouchersTaskImport.getStatus();
        BigDecimal estimateAmount = vouchersTaskImport.getEstimateAmount();
        Integer deductionAccountType = vouchersTaskImport.getDeductionAccountType();
        if (vouchersTaskImport == null || status != VoucherTaskImportStatus.IMPORT_SUCCESS.getValue() || BigDecimal.ZERO.compareTo(estimateAmount) >= 0) {
            log.error("【分贝券批量导入任务】发放失败，任务ID：{}，任务详情：{}", vouchersTaskImportId, vouchersTaskImport.toString());
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_IMPORT_GRANT_FAIL);
        }
        Date expireTime = null;
        // 查询分贝券模板
        VouchersTemplet vouchersTemplet = vouchersTempletService.getTempletByImportId(vouchersTaskImport.getVouchersTaskImportId());
        if(Objects.nonNull(vouchersTemplet)){
            expireTime = VoucherTermTypeEnum.getVoucherExpiryTime(vouchersTemplet.getVoucherTermType(), vouchersTemplet.getVoucherTermValidity());
        }
        // 校验个人账户余额
        vouchersTaskHandleService.checkAccountBalance(estimateAmount, companyId, deductionAccountType,expireTime);
        // 创建主任务 启动使用异步程序
        VouchersTaskCreateDTO taskCreateDTO = vouchersTaskImportService.createMainTask(userInfoDTO, vouchersTaskImport,taskImportDTO, vouchersTemplet);
        // 添加发券详情信息
        vouchersTaskImportService.addGrantDetails(vouchersTaskImport, userInfoDTO, taskCreateDTO);
        //创建发放任务
        VouchersGrantTaskDTO vouchersTaskCreateDTO = VouchersGrantTaskDTO.builder()
                .timingGrantTime(taskImportDTO.getTimingGrantTime())
                .vouchersTaskType(VoucherTaskType.BATCH_IMPORT.getValue())
                .vouchersTaskId(taskCreateDTO.getVouchersTaskId())
                .grantType(taskImportDTO.getGrantType())
                .id(vouchersTaskImport.getId())
                .companyId(companyId)
                .build();
        CompletableFuture.runAsync(() -> vouchersTaskImportService.startImportScheduleTask(vouchersTaskCreateDTO), ThreadPoolUtil.getVoucherThreadExecutor());
    }

    /**
     * 取消发放接口
     * @param request
     * @param response
     */
    @HttpService(value = "/cancel/task", method = RequestMethod.POST)
    public void cancelTask(HttpRequest request, HttpResponse response) {
        //校验权限
        checkPrivilege(request);
        VouchersGrantTaskDTO vouchersGrantTaskDTO = request.getBodyObject(VouchersGrantTaskDTO.class);
        vouchersTaskHandleService.cancelWaitGrantTask(vouchersGrantTaskDTO.getVouchersTaskId(),vouchersGrantTaskDTO.getCompanyId());
        ResponseResultUtils.success(response);
    }

    /**
     * 编辑定时发券任务信息
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/edit/timing/task/v3", method = RequestMethod.POST)
    public void editTimingTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersTaskTimingCreateDTO createDTO = request.getBodyObject(VouchersTaskTimingCreateDTO.class);
        log.info("operationUserInfoDTO:{}", operationUserInfoDTO);
        CompanyFbqRuleDTO companyFbqRuleDTO = iCompanyService.queryCompanyFbqRule(operationUserInfoDTO.getCompanyId());
        log.info("companyFbqRuleDTO:{}", companyFbqRuleDTO);
        createDTO.checkParams(companyFbqRuleDTO);
        BeanUtils.copyProperties(operationUserInfoDTO, createDTO);
        VoucherCostStatusEnum costStatusEnum = checkCostConfig(createDTO.getCompanyId(), createDTO.getCostInfo());
        createDTO.setCostStatus(costStatusEnum.getStatus());
        vouchersTaskTimingService.editTimingTask(createDTO);
        ResponseResultUtils.success(response);
    }

    /**
     * 编辑定时发券任务信息
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/query/timing/task/v3/{vouchersTaskTimingId}", method = RequestMethod.GET)
    public void queryTimingTask(HttpRequest request, HttpResponse response) {
        //校验权限
        checkPrivilege(request);
        String vouchersTaskTimingId = request.getPathValue("vouchersTaskTimingId");
        VouchersTaskTimingDetailRespDTO respDTO = vouchersTaskTimingService.queryTimingTask(vouchersTaskTimingId);
        ResponseResultUtils.success(response, respDTO);
    }


    @HttpService(value = "/check/timing/checkCalendar", method = RequestMethod.POST)
    public void checkCalendarAlready(HttpRequest request, HttpResponse response) {
        OperationUserInfoDTO userInfo = checkPrivilege(request);
        VouchersTaskTimingCheckDTO checkDTO = request.getBodyObject(VouchersTaskTimingCheckDTO.class);
        checkDTO.setCompanyId(userInfo.getCompanyId());
        ResponseResultUtils.success(response,vouchersTaskTimingService.checkCalendarAlready(checkDTO));
    }

    //TODO =============================================================================================================

    /**
     * checkTaskStatus
     *
     * @return java.lang.String
     * @Description 校验发放任务是否完成，是否超过48小时
     * @Date 下午10:27 2018/12/5
     * @Param [taskId]
     **/
    private void checkTaskStatus(String taskId, String companyId) {
        VouchersTask grantTask = vouchersTaskHandleService.getVouchersTaskById(taskId, companyId);
        if (Objects.isNull(grantTask)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (grantTask.getStatus() == VoucherTaskStatus.WAIT.getValue()) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "分贝券发放任务未完成");
        }
        //更改发放任务状态为 5-发放任务全部撤回
        if (grantTask.getStatus() == VoucherTaskStatus.SELECT_WITHDRAWAL_ALL.getValue()) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "分贝券发放任务已全部撤回，不可多次撤回");
        }
        if (grantTask.getVouchersTaskType() == VoucherTaskType.BOUNTY_EXCHANGE.getValue()) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_BOUNTY_CANNOT_WITHDRAWAL);
        }
        //已出账券任务不可撤回
        if (grantTask.getBillStatus() != null && grantTask.getBillStatus() == ToBillStatusEnum.IS_IN_BILL.getKey()) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "先票分贝券已出账，不可撤回");
        }
        //已开票券任务不可撤回
        if (grantTask.getWriteInvoiceStatus() != WriteInvoiceStatus.UN_INVOICED.getValue()) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "分贝券已申请开票，不可撤回");
        }

    }

    /**
     * 查询当前企业，是否配置预算占用
     * @param request
     * @param response
     */
    @HttpService(value = "/query/cost/config", method = RequestMethod.GET)
    public void costConfig(HttpRequest request, HttpResponse response) {
        OperationUserInfoDTO userInfo = getUserInfo(request);
        boolean config = voucherCommonUtils.queryCostConfig(userInfo.getCompanyId());
        //兼容移动端
        HashMap<String, Boolean> map = new HashMap<>();
        map.put(VoucherConstant.OPEN, config);
        ResponseResultUtils.success(response, map);
    }

    @Autowired
    private IExternalVoucherRpcService iExternalVoucherRpcService;

    /**
     * 查询分贝券交易流水
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/query/selectConsumeFlowByPage", method = RequestMethod.POST)
    public void selectConsumeFlowByPage(HttpRequest request, HttpResponse response) {
        VouchersBusinessFlowRPCDTO dto = request.getBodyObject(VouchersBusinessFlowRPCDTO.class);
        ResponsePage<VouchersFlowRPCDTO> responsePage = iExternalVoucherRpcService.selectConsumeFlowByPage(dto);
        ResponseResultUtils.success(response, responsePage);
    }

    /**
     * 已发券统计
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/query/grantRecoveryStatistics", method = RequestMethod.POST)
    public void grantRecoveryStatistics(HttpRequest request, HttpResponse response) {
        VouchersReqRPCDTO dto = request.getBodyObject(VouchersReqRPCDTO.class);
        ResponsePage<VoucherForStatisticsRPCDTO> responsePage = iExternalVoucherRpcService.grantRecoveryStatistics(dto);
        ResponseResultUtils.success(response, responsePage);
    }

    /**
     * 弹窗查询
     * 并置为已读
     * @param request
     * @param response
     */
    @HttpService(value = "/queryPopup", method = RequestMethod.POST)
    public void queryPopup(HttpRequest request, HttpResponse response) {
        String currentUserId = String.valueOf(request.getAttribute(USER_ID));
        String currentUserName = String.valueOf(request.getAttribute(USER_COMPANY_NAME));
        VoucherPopupVO voucherPopupVO = new VoucherPopupVO();
        List<VoucherPopupDetailVO> voucherPopupDetails = voucherHandleService.queryPopup(currentUserId);
        if (CollectionUtils.isNotEmpty(voucherPopupDetails)){
            String noticeContent=null;
            String backgroundUrl=null;
            VoucherPopupDetailVO popupDetailVO = voucherPopupDetails.stream().filter(vo-> VoucherSourceType.COLLEAGUE_TRANSFER.getValue() !=vo.getVoucherSource()).findFirst().orElse(null);
            if(Objects.nonNull(popupDetailVO)){
                //查询背景图片和文案
                VouchersTask vouchersTask = vouchersTaskService.getVouchersTaskByTaskId(popupDetailVO.getVouchersTaskId());
                noticeContent = vouchersTask.getNoticeContent();
                if (StringUtils.isNotBlank(noticeContent)) {
                	noticeContent = noticeContent.replaceAll(VoucherConstant.VARIATE_NAME, currentUserName);
                    noticeContent = noticeContent.replaceAll(VoucherConstant.VARIATE_VOUCHER_NAME, popupDetailVO.getVoucherName());
                }
                backgroundUrl = vouchersTask.getBackgroundUrl();
            } else {
                PersonTransferFlow personTransferFlow =  personTransferService.getPersonTransferByTargetId(String.valueOf(voucherPopupDetails.get(0).getId()));
                if(Objects.nonNull(personTransferFlow)){
                    noticeContent = personTransferFlow.getNoticeContent();
                    backgroundUrl = personTransferFlow.getBackgroundUrl();
                }
            }
            //总条数
            voucherPopupVO.setTotalCount(voucherPopupDetails.size());
            //总金额
            BigDecimal amount = voucherPopupDetails.stream().map(VoucherPopupDetailVO::getOperationAmount).reduce((x,y)->x.add(y)).get();
            voucherPopupVO.setAmount(amount);
            voucherPopupVO.setNoticeContent(noticeContent);
            voucherPopupVO.setBackgroundUrl(backgroundUrl);
            voucherPopupVO.setPopup(true);
            //置为已读
            voucherHandleService.unPopup(currentUserId);
        }
        voucherPopupVO.setList(voucherPopupDetails);
        ResponseResultUtils.success(response, voucherPopupVO);
    }
}
