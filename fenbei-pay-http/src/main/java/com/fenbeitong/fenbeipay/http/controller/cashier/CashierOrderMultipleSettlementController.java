package com.fenbeitong.fenbeipay.http.controller.cashier;


import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierMultipleTradeListRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierMultipleQueryReqRPCVo;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.COMPANY_ID;
import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.USER_ID;

/**
 * 一订单多次交易+多订单同时交易
 * 收银台收款Http服务Controller
 * RPC服务请参考fenbei-pay-api
 * @version  2020年07月09日15:50:35
 * <AUTHOR>
 * @since v4.2.0
 */

@HttpService("/cashier/pay/multiple")
public class CashierOrderMultipleSettlementController extends BaseController {

    @Autowired
    private CashierSearchOrderService cashierSearchOrderService;


    /**
     * 多次交易列表APP展示
     * @param request
     * @param response
     */
    @HttpService(value = "/trade/list/v1",method = RequestMethod.POST)
    public void  multipleTradeListV1(HttpRequest request, HttpResponse response){
        CashierMultipleQueryReqRPCVo queryReqRPCVo = request.getBodyObject(CashierMultipleQueryReqRPCVo.class);
        queryReqRPCVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        queryReqRPCVo.setCompanyId(request.getAttribute(COMPANY_ID).toString());
        CashierMultipleTradeListRPCDTO listRPCDTO = cashierSearchOrderService.queryCashierMultipleSettlementList(queryReqRPCVo);
        ResponseResultUtils.success(response,listRPCDTO);
    }

}