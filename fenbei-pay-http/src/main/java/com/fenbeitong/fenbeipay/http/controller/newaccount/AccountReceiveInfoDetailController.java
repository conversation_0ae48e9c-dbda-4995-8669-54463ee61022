package com.fenbeitong.fenbeipay.http.controller.newaccount;

import com.fenbeitong.fenbeipay.api.model.dto.na.req.EmployeeAccountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.EmployeeAccountListWithAuthResp;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.EmployeeAccountRespDTO;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.http.controller.vouchers.BaseController;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveInfoDetailService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2020/11/5
 * @Description
 */
@HttpService("/fbp/employee")
public class AccountReceiveInfoDetailController extends BaseController {


    @Autowired
    private AccountReceiveInfoDetailService accountReceiveInfoDetailService;


    @HttpService(value = "/getEmployeeAccountList",method = RequestMethod.GET)
    public void getEmployeeInfoList(HttpRequest request, HttpResponse response){
        String userId = request.getParameter("userId");
        String companyId = (String) request.getAttribute("companyId");
        String operater = (String) request.getAttribute("userId");
        String type = (String) request.getParameter("type");
        String  reimburserId = (String) request.getParameter("reimburserId");
        EmployeeAccountListWithAuthResp authResp = accountReceiveInfoDetailService.getEmployeeAccountList(companyId,userId,operater, type,reimburserId);
        ResponseResultUtils.success(response,authResp);
    }

    @HttpService(value = "/addEmployeeAccount",method = RequestMethod.POST)
    public void addEmployeeAccount(HttpRequest request, HttpResponse response){
        String body = request.getBody();
        if (ObjUtils.isBlank(body)){
            throw new FinhubException(GlobalResponseCode.EXCEPTION_DATA_ERROR.getCode(),GlobalResponseCode.EXCEPTION_DATA_ERROR.getMsg());
        }
        EmployeeAccountReqDTO reqDTO = JsonUtils.toObj(body,EmployeeAccountReqDTO.class);
        accountReceiveInfoDetailService.addEmployeeAccount(reqDTO, true);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/getEmployeeAccountDetail",method = RequestMethod.GET)
    public void getEmployeeAccountDetail(HttpRequest request, HttpResponse response){
        String accountId = request.getParameter("id");
        EmployeeAccountRespDTO result = accountReceiveInfoDetailService.getEmployeeAccountDetail(accountId);
        ResponseResultUtils.success(response,result);
    }

    @HttpService(value = "/removeEmployeeAccount",method = RequestMethod.POST)
    public void removeEmployeeAccount(HttpRequest request, HttpResponse response){
        String body = request.getBody();
        if (ObjUtils.isBlank(body)){
            throw new FinhubException(GlobalResponseCode.EXCEPTION_DATA_ERROR.getCode(),GlobalResponseCode.EXCEPTION_DATA_ERROR.getMsg());
        }
        EmployeeAccountReqDTO removeReqDTO = JsonUtils.toObj(body,EmployeeAccountReqDTO.class);
        accountReceiveInfoDetailService.removeEmployeeAccount(removeReqDTO);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/updateEmployeeAccount",method = RequestMethod.POST)
    public void updateEmployeeAccount(HttpRequest request, HttpResponse response){
        String body = request.getBody();
        if (ObjUtils.isBlank(body)){
            throw new FinhubException(GlobalResponseCode.EXCEPTION_DATA_ERROR.getCode(),GlobalResponseCode.EXCEPTION_DATA_ERROR.getMsg());
        }
        EmployeeAccountReqDTO updateReqDTO = JsonUtils.toObj(body,EmployeeAccountReqDTO.class);
        accountReceiveInfoDetailService.updateEmployeeAccount(updateReqDTO, true);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/updateAccountDefault",method = RequestMethod.POST)
    public void updateAccountDefault(HttpRequest request, HttpResponse response){
        String body = request.getBody();
        if (ObjUtils.isBlank(body)){
            throw new FinhubException(GlobalResponseCode.EXCEPTION_DATA_ERROR.getCode(),GlobalResponseCode.EXCEPTION_DATA_ERROR.getMsg());
        }
        EmployeeAccountReqDTO updateReqDTO = JsonUtils.toObj(body,EmployeeAccountReqDTO.class);
        accountReceiveInfoDetailService.updateAccountDefault(updateReqDTO);
        ResponseResultUtils.success(response);
    }

}
