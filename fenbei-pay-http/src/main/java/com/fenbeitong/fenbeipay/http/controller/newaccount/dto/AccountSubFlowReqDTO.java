package com.fenbeitong.fenbeipay.http.controller.newaccount.dto;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Description: java类作用描述
 * @ClassName: AccountSubFlowReqDTO
 * @Author: zhangga
 * @CreateDate: 2019/3/18 2:55 PM
 * @UpdateUser:
 * @UpdateDate: 2019/3/18 2:55 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class AccountSubFlowReqDTO extends PageBean {

    /**
     * 交易类型 ：1-开户;2-充值;3-提现;4-消费;51-转至商户账户;52-转至个人账户;53-转至企业账户；6-冻结;7-解冻
     */
    private Integer operationType;
    /**
     * 子账户类别  不可为空
     **/
    @NotNull
    private Integer accountSubType;

    private Integer accountModel;

    private String accountFlowId;

    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 创建时间
     */
    @DateTimeFormat
    private Date startTime;
    @DateTimeFormat
    private Date endTime;

    /**
     * 报表导出查询项目标识
     **/
    private ExportQuery exportQuery;

    public JSONObject getQuery(String companyId) {
        JSONObject queryJson = new JSONObject();
        queryJson.put("pageNo", "${page}");
        queryJson.put("pageSize", "${size}");
        queryJson.put("companyId", companyId);
        queryJson.put("accountSubType", accountSubType);
        queryJson.put("operationType", this.operationType);
        queryJson.put("accountFlowId", this.accountFlowId);
        queryJson.put("operationUserName", this.operationUserName);
        queryJson.put("startTime", this.startTime);
        queryJson.put("endTime", this.endTime);
        queryJson.put("accountModel", this.accountModel);
        return queryJson;
    }
}