package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTypeEnum;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersType;
import com.fenbeitong.fenbeipay.vouchers.dto.OperationUserInfoDTO;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTypeService;
import com.fenbeitong.fenbeipay.vouchers.vo.VouchersTypeVO;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.usercenter.api.model.enums.common.CommonAuthCodeEnums;
import com.fenbeitong.usercenter.api.model.enums.common.CommonAuthType;
import com.fenbeitong.usercenter.api.service.common.ICommonService;
import com.google.common.collect.Lists;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: java类作用描述
 * @ClassName: VouchersTypeController
 * @Author: zhangga
 * @CreateDate: 2018/12/26 下午4:25
 * @UpdateUser:
 * @UpdateDate: 2018/12/26 下午4:25
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/vouchers/management")
public class VouchersTypeController extends BaseController {

    @Autowired
    private VouchersTypeService vouchersTypeService;
    @Autowired
    private ICommonService iCommonService;

    /**
     * getVouchersTypeList
     *
     * @return void
     * @Description 获取分贝券分类列表
     * @Date 下午1:53 2018/12/27
     * @Param [request, response]
     **/
    @HttpService(value = "/get_vouchers_type_list/v2", method = RequestMethod.GET)
    public void getVouchersTypeList(HttpRequest request, HttpResponse response) {
        //TODO 校验权限
//        checkPrivilege(request);
        OperationUserInfoDTO operationUserInfo = checkPrivilegeNoMenu(request);
        Map<String, Integer> companyEmployeeAuth = iCommonService.queryCompanyEmployeeAuth(operationUserInfo.getCompanyId(), operationUserInfo.getOperationUserId(), CommonAuthType.SUPPLIER_AUTH.getKey(), Lists.newArrayList(CommonAuthCodeEnums.JD_CARD.getKey()));
        List<VouchersTypeVO> result = new ArrayList<>();
        for (VouchersType vouchersType : vouchersTypeService.getVouchersTypeList()) {
            //封装VO参数
            String typeKey = vouchersType.getTypeKey();
            if (vouchersType.getIsShow() != 1) {
                continue;
            }
            if (typeKey.equals(VoucherTypeEnum.VT_E_CARD_11.getTypeKey()) && companyEmployeeAuth != null && companyEmployeeAuth.get(CommonAuthCodeEnums.JD_CARD.getKey()) == 0) {
                continue;
            }
            VouchersTypeVO vouchersTypeVO = new VouchersTypeVO();
            vouchersTypeVO.encapsulationVO(vouchersType);
            result.add(vouchersTypeVO);
        }
        ResponseResultUtils.success(response, result);
    }

}
