package com.fenbeitong.fenbeipay.http.controller.account;

import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCommonService;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.CompanyCreditInfoDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponInfoVO;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubService;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyCreditInfoVo;
import com.fenbeitong.fenbeipay.core.model.vo.account.ZxAccountUpgradeWindowShowVo;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import static com.fenbeitong.fenbeipay.http.controller.vouchers.BaseController.COMPANY_ID;

/**
 * 账户相关接口
 * Created by xjf on 2018/8/10.
 */
@Deprecated
@HttpService("/")
public class AccountController {

    @Autowired
    private IAccountSubService iAccountSubService;

    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    private UAcctCommonService uAcctCommonService;
    /**
     * (HTTP) 获取公司信息总览
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "fbp/account/company/credit/overview", method = RequestMethod.GET)
    public void companyCreditOverview(HttpRequest request, HttpResponse response) {
        String companyId = request.getAttribute(COMPANY_ID).toString();
        CompanyCreditInfoDTO companyCreditInfoDTO = iAccountSubService.getCreditOverView(companyId);
        CompanyCreditInfoVo companyCreditInfoVo = null;
        if (ObjUtils.isNotBlank(companyCreditInfoDTO)) {
            companyCreditInfoVo = new CompanyCreditInfoVo(companyCreditInfoDTO.getInitCredit(), companyCreditInfoDTO.getAvailableCredit(),
                    companyCreditInfoDTO.getVoucherCredit(), companyCreditInfoDTO.getIndividualCredit(),companyCreditInfoDTO.getIndividualInitCredit(),
                    companyCreditInfoDTO.getCompanyCooperatingModel(),companyCreditInfoDTO.getBussinessAccountModelType(),
                    companyCreditInfoDTO.getIndividualAccountModelType());
        }
        AccountRedcouponInfoVO redcouponInfo = accountRedcouponSearchService.queryAccountCouponInfoVO(companyId);
        if (redcouponInfo != null) {
            redcouponInfo.setBalance(BigDecimalUtils.fenToYuan(redcouponInfo.getBalance()));
            companyCreditInfoVo.setRedcouponInfo(redcouponInfo);
            companyCreditInfoVo.setHasRedcoupon(true);
        }
        ResponseResultUtils.success(response, companyCreditInfoVo);
    }

    /**
     * (HTTP) 账户升级弹窗
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "fbp/account/company/upgrade/windowShow", method = RequestMethod.GET)
    public void upgradeWindowShow(HttpRequest request, HttpResponse response) {
        String companyId = request.getAttribute(COMPANY_ID).toString();
        ZxAccountUpgradeWindowShowVo windowShowVo = uAcctCommonService.upgradeWindowShow(companyId);
        ResponseResultUtils.success(response, windowShowVo);
    }

    /**
     * (HTTP) 账户升级弹窗
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "fbp/account/company/upgrade/windowConfirm", method = RequestMethod.GET)
    public void upgradeWindowConfirm(HttpRequest request, HttpResponse response) {
        String companyId = request.getAttribute(COMPANY_ID).toString();
        uAcctCommonService.upgradeWindowConfirm(companyId);
        ResponseResultUtils.success(response);
    }
}
