package com.fenbeitong.fenbeipay.http.controller.redcoupon;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.*;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponFlowPageReq;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponGrantRecordPageReq;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.RedcouponAcctFlowDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.RedcouponFlowDetailDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.RedcouponGrantRecordDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponFlowVO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponInfoVO;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponSearchService;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportDTO;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportQuery;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.service.file.BaseTemplateService;
import com.fenbeitong.fenbeipay.core.service.file.CommonJsonRespVo;
import com.fenbeitong.fenbeipay.core.service.file.StaticPageType;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcoupon;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponFlow;
import com.fenbeitong.fenbeipay.dto.redcoupon.AccountRedcouponGrantRecord;
import com.fenbeitong.fenbeipay.http.controller.vouchers.BaseController;
import com.fenbeitong.fenbeipay.redcoupon.manager.AccountRedcouponGrantRecordManager;
import com.fenbeitong.finhub.common.constant.OrderChannelEnum;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.dto.AccountRedcouponFlowReqDTO;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeSimpleInfoContract;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 红包券http查询接口
 * @ClassName: AccountRedcouponController
 * @Author: zhangga
 * @CreateDate: 2019/3/18 5:26 PM
 * @UpdateUser:
 * @UpdateDate: 2019/3/18 5:26 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/fbp/redcoupon")
public class AccountRedcouponController extends BaseController {

    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    private BaseTemplateService baseTemplateService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private IAccountRedcouponSearchService iAccountRedcouponSearchService;

    @Value("${export.task.host}")
    private String exportTaskHost;
    @Value("${export.query.host}")
    private String exportQueryHost;

    @Autowired
    private AccountRedcouponGrantRecordManager accountRedcouponGrantRecordManager;

    @HttpService(value = "/flow", method = RequestMethod.POST)
    public void queryRedCouponFlow(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String companyId = getOperationCompanyId(request);
        AccountRedcouponFlowReqDTO dto = request.getBodyObject(AccountRedcouponFlowReqDTO.class);
        dto.setCompanyId(companyId);
        dto.setOperationTypeList(RedcouponOperationType.WEB_SHOW_TYPE);
        //分贝券失效返还 分系统和人工, 查询时全都查出来
        if(null != dto.getOperationType()){
            if(RedcouponOperationType.FROZEN_VOUCHER_RECALL.getKey() == dto.getOperationType()){
                dto.setOperationTypeList(RedcouponOperationType.VOUCHER_RECALL);
                dto.setOperationType(null);
            }else if(RedcouponOperationType.STEREO_GRANT.getKey() == dto.getOperationType()){
                //红包券发放 包含 发放，增加额度
                dto.setOperationTypeList(RedcouponOperationType.STEREO_GRANT_ADD);
                dto.setOperationType(null);
            }
        }
        dto.setRedcouponType(RedcouponType.REDCOUPON.getKey());
        ResponsePage<AccountRedcouponFlow> responsePage = accountRedcouponSearchService.queryAccountCouponFlowList(dto);
        ResponsePage<AccountRedcouponFlowVO> operationFlows = new ResponsePage<>();
        List<AccountRedcouponFlowVO> dataList = new ArrayList<>();
        operationFlows.setTotalCount(responsePage.getTotalCount());
        if (ObjUtils.isNotEmpty(responsePage.getDataList())) {
            for (AccountRedcouponFlow accountRedcouponFlow : responsePage.getDataList()) {
                AccountRedcouponFlowVO vo = new AccountRedcouponFlowVO();
                BeanUtils.copyProperties(accountRedcouponFlow, vo);
                vo.setTradeDescription(vo.getTradeDescription(vo.getOperationType()));
                vo.setOperationUserName(vo.getoperationUserName(vo.getOperationType(),vo.getOperationUserName()));
                String operationValue = RedcouponOperationType.getEnum(accountRedcouponFlow.getOperationType()).getValue();
                vo.setOperationDescription(operationValue);
                vo.setOperationUserPhone(vo.getOperationUserPhone(vo.getOperationType(),vo.getOperationUserPhone()));
                dataList.add(vo);
            }
        }
        operationFlows.setDataList(dataList);
        operationFlows.setCondition(responsePage.getCondition());
        ResponseResultUtils.success(response, operationFlows);
    }

    @HttpService(value = "/instructions", method = RequestMethod.GET)
    public void queryInstructions(HttpRequest request, HttpResponse response) {
        checkPrivilegeNoMenu(request);
        try {
            String template = baseTemplateService.renderTemplate(StaticPageType.NEW_RED_COUPON_INSTRUCTIONS.getPage(), null);
            CommonJsonRespVo commonJsonRespVo = JsonUtils.toObj(template, CommonJsonRespVo.class);
            ResponseResultUtils.success(response, commonJsonRespVo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @HttpService(value = "/flow/export", method = RequestMethod.POST)
    public void exportFlow(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        AccountRedcouponFlowReqDTO dto = request.getBodyObject(AccountRedcouponFlowReqDTO.class);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        String operationUserId = request.getAttribute(USER_ID).toString();
        String operationUserName = request.getAttribute(USER_COMPANY_NAME).toString();
        ExportQuery exportQuery = dto.getExportQuery();
        if (exportQuery == null) {
            FinhubLogger.error("【导出报表】参数错误，exportQuery：{}", exportQuery.toString());
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        //判断是否超过一年
//        Date startTimeAfter1Year = DateUtil.changeDateYear(startTime, 1);
//        if (endTime.compareTo(startTimeAfter1Year) > 0) {
//            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_EXPORT_OUT_TIME);
//        }
        ExportDTO exportDTO = new ExportDTO();
        String exportQueryUrl = exportQueryHost + "/internal/redcoupon/export/redcoupon/flow";
        List<String> list = new ArrayList<>();
        EmployeeSimpleInfoContract employeeSimpleInfo = employeeService.getEmployeeSimpleInfo(operationUserId, companyId);
        if (null != employeeSimpleInfo && ObjUtils.isNotEmpty(employeeSimpleInfo.getOrg_unit_list())) {
            employeeSimpleInfo.getOrg_unit_list().forEach(orgUnitListBean -> {
                list.add(orgUnitListBean.getId());
            });
        }
        JSONObject query = dto.getQuery(companyId);
        String parentIds = StringUtils.join(list, ",");
        String taskName = "红包券消费流水导出_" + DateUtils.format(new Date(), "yyyyMMdd_HHmmss");
        exportDTO.dtoBuild(query, exportQueryUrl, taskName, operationUserId, operationUserName, parentIds, exportDTO.getRedcouponColumns(), exportQuery);
        String jsonString = JSONObject.toJSONString(exportDTO);
        String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/task", jsonString);
        FinhubLogger.info("【导出报表】返回参数：{}", postBody);
        Object taskId = JSONObject.parseObject(postBody).getJSONObject("data").get("taskId");
        ResponseResultUtils.success(response, taskId);
    }

    @HttpService(value = "/detail", method = RequestMethod.GET)
    public void redCouponDetail(HttpRequest request, HttpResponse response){
        String companyId = request.getParameter("companyId");
        if(StringUtils.isBlank(companyId)) {
             companyId = request.getAttribute(COMPANY_ID).toString();
        }
        AccountRedcoupon accountRedcoupon = accountRedcouponSearchService.queryAccountCouponInfo(companyId);
        AccountRedcouponInfoVO redCouponInfo = new AccountRedcouponInfoVO();
        if(Objects.isNull(accountRedcoupon)){
            redCouponInfo.setHasRedCoupon(false);
        }else {
            List<AccountRedcouponGrantRecord> accountRedcouponGrantRecords = accountRedcouponGrantRecordManager.queryRecordByRedAcctId(accountRedcoupon.getAccountRedcouponId());
            List<AccountRedcouponGrantRecord> collect = accountRedcouponGrantRecords.stream().filter(accountRedcouponGrantRecord -> !RedcouponScopeType.isBusinessScope(accountRedcouponGrantRecord.getUseScope())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                BigDecimal reduce = collect.stream().map(AccountRedcouponGrantRecord::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                redCouponInfo.setHasRedCoupon(true);
                redCouponInfo.setBalance(BigDecimalUtils.fenToYuan(reduce));
            }else{
                redCouponInfo.setHasRedCoupon(false);
            }
        }
        ResponseResultUtils.success(response, redCouponInfo);
    }

    @HttpService(value = "/hasRedCoupon", method = RequestMethod.GET)
    public void hasRedCoupon(HttpRequest request, HttpResponse response){
        String companyId = request.getParameter("companyId");
        if(StringUtils.isBlank(companyId)) {
             companyId = request.getAttribute(COMPANY_ID).toString();
        }
        AccountRedcoupon accountRedcoupon = accountRedcouponSearchService.queryAccountCouponInfo(companyId);
        AccountRedcouponInfoVO redCouponInfo = new AccountRedcouponInfoVO();
        if(null == accountRedcoupon){
            redCouponInfo.setHasRedCoupon(false);
        }else {
            accountRedcoupon.setBalance(BigDecimalUtils.fenToYuan(accountRedcoupon.getBalance()));
            accountRedcoupon.setGrantSum(BigDecimalUtils.fenToYuan(accountRedcoupon.getGrantSum()));
            accountRedcoupon.setRefundSum(BigDecimalUtils.fenToYuan(accountRedcoupon.getRefundSum()));
            redCouponInfo.setHasRedCoupon(true);
            BeanUtils.copyProperties(accountRedcoupon, redCouponInfo);
        }
        ResponseResultUtils.success(response, redCouponInfo);
    }

    /**
     * 红包券发放记录查询
     * @param request
     * @param response
     */
   @HttpService(value="/grant/record/list",method = RequestMethod.POST)
   public void grantRecordList(HttpRequest request, HttpResponse response){
       RedcouponGrantRecordPageReq bodyObject = request.getBodyObject(RedcouponGrantRecordPageReq.class);
       if(StringUtils.isBlank(bodyObject.getCompanyId())){
           String companyId = request.getAttribute(COMPANY_ID).toString();
           bodyObject.setCompanyId(companyId);
       }
       ResponsePage<RedcouponGrantRecordDTO> redcouponGrantRecordDTOResponsePage = iAccountRedcouponSearchService.queryRedcouponRecordPage(bodyObject);
       ResponseResultUtils.success(response, redcouponGrantRecordDTOResponsePage);
   }

    /**
     * 红包券消费流水查询
     * @param request
     * @param response
     */
    @HttpService(value="/consum/flow/list",method = RequestMethod.POST)
    public void consumFlowList(HttpRequest request, HttpResponse response){
        RedcouponFlowPageReq bodyObject = request.getBodyObject(RedcouponFlowPageReq.class);
        if(StringUtils.isBlank(bodyObject.getCompanyId())){
            String companyId = request.getAttribute(COMPANY_ID).toString();
            bodyObject.setCompanyId(companyId);
        }
        ResponsePage<RedcouponAcctFlowDTO> redcouponAcctFlowDTOResponsePage = iAccountRedcouponSearchService.queryRedcouponAcctFlowPage(bodyObject);
        if(Objects.nonNull(redcouponAcctFlowDTOResponsePage)){
            List<RedcouponAcctFlowDTO> dataList = redcouponAcctFlowDTOResponsePage.getDataList();
            if(CollectionUtils.isNotEmpty(dataList)){
                for (RedcouponAcctFlowDTO redcouponAcctFlowDTO : dataList) {
                    //撤回类型，操作人信息置为空，产品要求
                    if(redcouponAcctFlowDTO.getConsumeType().equals(RedcouponConsumType.RECALL.getKey())){
                        redcouponAcctFlowDTO.setOperationUserId(null);
                        redcouponAcctFlowDTO.setOperationUserName(null);
                    }
                    if(1==bodyObject.getAppFlag()){
                        Integer consumeType = redcouponAcctFlowDTO.getConsumeType();
                        if(consumeType.equals(RedcouponConsumType.CONSUM.getKey())||consumeType.equals(RedcouponConsumType.RECALL.getKey())){
                            redcouponAcctFlowDTO.setOperateAmt(redcouponAcctFlowDTO.getOperateAmt().negate());
                        }
                    }
                }
            }
        }
        ResponseResultUtils.success(response, redcouponAcctFlowDTOResponsePage);
    }

    /**
     * 发放记录详情
     * @param request
     * @param response
     */
    @HttpService(value="/grant/record/detail",method = RequestMethod.GET)
    public void grantRecordDetail(HttpRequest request, HttpResponse response){
        String grantFlowId = request.getParameter("grantFlowId");
        RedcouponGrantRecordDTO redcouponGrantRecordDTO = iAccountRedcouponSearchService.queryGrantRecordByRecordId(grantFlowId);
        ResponseResultUtils.success(response, redcouponGrantRecordDTO);
    }

    /**
     * 消费流水详情
     * @param request
     * @param response
     */
    @HttpService(value="/consum/flow/detail",method = RequestMethod.GET)
    public void consumFlowDetail(HttpRequest request, HttpResponse response){
        String flowId = request.getParameter("flowId");
        RedcouponFlowDetailDTO redcouponFlowDetailDTO = iAccountRedcouponSearchService.queryFlowByFlowId(flowId);
        ResponseResultUtils.success(response, redcouponFlowDetailDTO);
    }

    /**
     * 可用场景
     * @param request
     * @param response
     */
    @HttpService(value="/valid/scopeType",method = RequestMethod.GET)
    public void validScopeType(HttpRequest request, HttpResponse response){
        List<Map> mapArrayList = Lists.newArrayList();
        RedcouponScopeType[] values = RedcouponScopeType.values();
        for (RedcouponScopeType value : values) {
            HashMap<Object, Object> map = Maps.newHashMap();
            map.put("code",value.getKey());
            map.put("name",value.getDesc());
            mapArrayList.add(map);
        }
        ResponseResultUtils.success(response, mapArrayList);
    }

    /**
     * 可用渠道
     * @param request
     * @param response
     */
    @HttpService(value="/valid/channelType",method = RequestMethod.GET)
    public void validChannel(HttpRequest request, HttpResponse response){
        List<Map> mapArrayList = Lists.newArrayList();
        OrderChannelEnum[] values =OrderChannelEnum.values();
        for (OrderChannelEnum value : values) {
            HashMap<Object, Object> map = Maps.newHashMap();
            map.put("code",value.getKey());
            map.put("name",value.getValue());
            mapArrayList.add(map);
        }
        ResponseResultUtils.success(response, mapArrayList);
    }

    /**
     * 消费类型
     * @param request
     * @param response
     */
    @HttpService(value="/valid/consumType",method = RequestMethod.GET)
    public void validConsumType(HttpRequest request, HttpResponse response){
        List<Map> mapArrayList = Lists.newArrayList();
        List<RedcouponConsumType> values = RedcouponConsumType.validEnum();
        for (RedcouponConsumType value : values) {
            HashMap<Object, Object> map = Maps.newHashMap();
            map.put("code",value.getKey());
            map.put("name",value.getDesc());
            mapArrayList.add(map);
        }
        ResponseResultUtils.success(response, mapArrayList);
    }

    /**
     * 消耗可用场景
     * @param request
     * @param response
     */
    @HttpService(value="/valid/orderType",method = RequestMethod.GET)
    public void validOrderType(HttpRequest request, HttpResponse response){
        List<Map> mapArrayList = Lists.newArrayList();
        RedcouponOrderType[] values = RedcouponOrderType.values();
        for (RedcouponOrderType value : values) {
            if(!value.equals(RedcouponOrderType.UNKONW)) {
                HashMap<Object, Object> map = Maps.newHashMap();
                map.put("code", value.getKey());
                map.put("name", value.getDesc());
                mapArrayList.add(map);
            }
        }
        ResponseResultUtils.success(response, mapArrayList);
    }
}
