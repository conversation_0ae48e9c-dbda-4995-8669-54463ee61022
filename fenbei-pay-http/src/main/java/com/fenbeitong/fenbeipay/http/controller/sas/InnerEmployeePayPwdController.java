package com.fenbeitong.fenbeipay.http.controller.sas;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.model.dto.sas.req.SasSecurityVerifyReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.sas.resp.SasSecurityVerifyRespDTO;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasEmployeePayPwdManager;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasSecurityVerifyManager;
import com.fenbeitong.fenbeipay.sas.pwd.query.manager.SearchEmployeePayPwdManager;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;
/**
 * @Description: 内网接口
 * @ClassName: EmployeePayPwdController
 * @Version: 3.7.0
 */
@HttpService("/internal/sas")
public class InnerEmployeePayPwdController {

    @Autowired
    private SasEmployeePayPwdManager sasEmployeePayPwdManager;

    @Autowired
    private SearchEmployeePayPwdManager searchEmployeePayPwdManager;

    @Autowired
    private SasSecurityVerifyManager securityVerifyManager;

    @Autowired
    private RedisDao redisDao;

    /**
     * 时间程序，支付密码锁定时间到了需要开锁
     * @param request
     * @param response
     */
    @HttpService(value = "/pay/pwd/unlock",method = RequestMethod.POST)
    public void unlockPayPassword(HttpRequest request, HttpResponse response){
        sasEmployeePayPwdManager.unlockPayPassword();
    }

    /**
     * 清除缓存，是否有支付密码
     * @param request
     * @param response
     */
    @HttpService(value = "/pay/pwd/cache/delete",method = RequestMethod.GET)
    public void deletePayPwdCache(HttpRequest request, HttpResponse response){
        searchEmployeePayPwdManager.deletePayPwdCache();
    }

    /**
     * 根据员工号重置密码
     * @param request
     * @param response
     */
    @HttpService(value = "/pay/pwd/reset/{employeeId}",method = RequestMethod.GET)
    public void resetPwd(HttpRequest request, HttpResponse response){
        String employeeId = request.getPathValue("employeeId");
        Boolean b = searchEmployeePayPwdManager.resetPwd(employeeId);
        ResponseResultUtils.success(response,b);
    }
    
    /**
     * 初始化支付密码
     * @param request
     * @param response
     */
    @HttpService(value = "/pay/pwd/init",method = RequestMethod.POST)
    public void initDefaultPwd(HttpRequest request, HttpResponse response){
    	UserInfoVO userInfo = request.getBodyObject(UserInfoVO.class);
        sasEmployeePayPwdManager.initDefaultPwd4User(userInfo);
        ResponseResultUtils.success(response, Boolean.TRUE);
    }
    
    

    /**
     * 查询是否通过安全校验
     */
    @HttpService(value = "/query/security/result",method = RequestMethod.POST)
    public void queryIsSecurityVerifyPass(HttpRequest request, HttpResponse response){
        SasSecurityVerifyReqDTO reqDTO = request.getBodyObject(SasSecurityVerifyReqDTO.class);
        SasSecurityVerifyRespDTO respDTO = securityVerifyManager.queryIsSecurityVerifyPass(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }

    /**
     * 设置无需检查有没有通过安全认证
     */
    @HttpService(value = "/security/set/skip/check", method = RequestMethod.POST)
    public void getRediss(HttpRequest request, HttpResponse response) {
        JSONObject bodyObject = request.getBodyObject(JSONObject.class);
        redisDao.getRedisTemplate().opsForValue().set(bodyObject.getString("key"), bodyObject.getString("value"));
        ResponseResultUtils.success(response, "设置成功");
    }
}
