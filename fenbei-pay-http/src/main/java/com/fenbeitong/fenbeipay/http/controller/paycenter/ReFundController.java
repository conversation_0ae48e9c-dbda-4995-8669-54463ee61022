package com.fenbeitong.fenbeipay.http.controller.paycenter;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/11/16.
 */
@Deprecated
//@HttpService("internal/fbp/payment")
public class ReFundController  {
//    private static final Logger logger = LoggerFactory.getLogger(ReFundController.class);
//    @Autowired
//    private FuQianLaService fuQianLaService;
//    @Autowired
//    private EmployeeRedisService employeeRedisService;
//    @Autowired
//    private PersonPayService personPayService;
//    @Autowired
//    private RedisService redisService;
//    @Autowired
//    private CashierOrderRefundSettlementService cashierOrderRefundSettlementService;


    /**
     * 付钱拉退款
     *
     * @param request
     * @param response
     */
//    @HttpService(value = "/refund/v1", method = RequestMethod.POST)
//    public void refund(HttpRequest request, HttpResponse response) {
//        String requestBody = request.getBody();
//        ReFundReqContract reFundReqContract = JsonUtils.toObj(requestBody, ReFundReqContract.class);
//        reFundReqContract.setOrderAmount(reFundReqContract.getOrderAmount().multiply(new BigDecimal(100)));
//        String lockKey = "REFUND" + reFundReqContract.getOrderNo();
//        Long locktime = 0l;
//        RedisTemplate<Serializable, Serializable> redisTemp = employeeRedisService.getRedisTemplate();
//        logger.info("退款参数：" + requestBody);
//        Map<String, Object> retMap = new HashMap<>();
//        Map<String,String> reponsseMap=new ConcurrentHashMap<>();
//        PersonRefundRecord refundRecord=new PersonRefundRecord();
//        try {
//            locktime = RedisDistributionLock.lock(lockKey, redisTemp);
//            if (locktime > 0 || StringUtils.isEmpty(lockKey)) {
//                //校验
//                retMap = personPayService.validateMsg(reFundReqContract);
//                if (!retMap.get(PayConstant.PRFUND_STATUS).equals(PayConstant.payOrderRefundIng)) {
//                    ResponseResultUtils.success(response, retMap);
//                } else {
//                    PersonPayRecord personPayRecord = personPayService.getPayRecordList(reFundReqContract.getOrderNo());
//                    reponsseMap= FuQianLaUtil.refundPayRecord(personPayRecord.getChargeId(), reFundReqContract.getOrderAmount().intValue());
//                    logger.info("退款返回参数：" + reponsseMap.get(PayConstant.PRFUND_REPONSE));
//                    FuQianLaRefundQueryBackContract refundQueryBackContract = JsonUtils.toObj(reponsseMap.get(PayConstant.PRFUND_REPONSE), FuQianLaRefundQueryBackContract.class);
//                    FuQianLaRefundQueryBackContract.ChargeData chargeData = refundQueryBackContract.getRet_data();
//                    personPayService.updateRefunStatus(personPayRecord.getOrderId(), PayConstant.payOrderRefundIng);
//                    if (chargeData != null) {
//
//                        if (chargeData.getStatus().equals("02")) {
//                            String successKey = PayConstant.PRFUND_SUCCESS+ reFundReqContract.getOrderNo();
//                            String amountKey = PayConstant.PRFUND_TOTAL + reFundReqContract.getOrderNo();
//                            String ingKey = PayConstant.PRFUND_ING + reFundReqContract.getOrderNo();
//                            Map<String, Object> mmp = redisService.getDataFormRedis(reFundReqContract.getOrderNo());
//                            int status = CommonUtil.checkOrderStatus(Long.parseLong(mmp.get(amountKey).toString()), Long.parseLong(mmp.get(successKey).toString()) + chargeData.getRefund_amount().longValue());
//                        } else if (chargeData.getStatus().equals("01")) {
//
//                        }
//                        //更新退款表
//                        refundRecord=fuQianLaService.saveRefundRecord(reFundReqContract, refundQueryBackContract,reponsseMap);
//                        retMap.put(PayConstant.PRFUND_STATUS, PayCommonType.getEnum(chargeData.getStatus()).getValue());
//                        retMap.put(PayConstant. PRFUND_RETMSG, chargeData.getEx_msg());
//                        //异步调用新收银台退款成功
//                        PersonRefundRecord finalRefundRecord = refundRecord;
//                        CompletableFuture.runAsync(()->{
//                            //更新退款交易
//                            cashierOrderRefundSettlementService.fqlSuccessCashierRefund(personPayRecord, finalRefundRecord);
//                            //回掉
//                            cashierOrderRefundSettlementService.checkRefundStatusAndCallBiz(finalRefundRecord.getTxnId(),finalRefundRecord.getId());
//                        });
//
//                    } else {
//                        refundRecord=fuQianLaService.saveRefundRecord(reFundReqContract, refundQueryBackContract,reponsseMap);
//                        retMap.put(PayConstant.PRFUND_STATUS, PayConstant.payOrderRefundError);
//                        retMap.put(PayConstant. PRFUND_RETMSG, refundQueryBackContract.getRet_desc());
//                    }
//                    //回调结果
//                    retMap.put(PayConstant.PRFUND_REFUNDID, refundRecord.getId());
//                    ResponseResultUtils.success(response, retMap);
//                }
//            } else {
//                logger.info("退款速度频率大");
//                retMap.put(PayConstant.PRFUND_STATUS, PayConstant.payOrderRefundError);
//                retMap.put(PayConstant. PRFUND_RETMSG, "退款速度频率大");
//                ResponseResultUtils.success(response, retMap);
//            }
//        } catch (Exception e) {
//            logger.info("退款出现异常", e.getLocalizedMessage());
//            e.printStackTrace();
//            ResponseResultUtils.fail(response, new FinhubException(UcMessageCode.EXCEPTION, "退款出现异常"));
//        } finally {
//            RedisDistributionLock.unlock(lockKey, locktime, redisTemp);
//            PersonPayRefundLog refundLog=new PersonPayRefundLog();
//            refundLog.setReceiveMsg(requestBody);
//            refundLog.setReturnMsg(reponsseMap.get(PayConstant.PRFUND_REPONSE));
//            refundLog.setCjReponsMsg(JsonUtils.toJson(retMap));
//            refundLog.setFqlRequestMsg(reponsseMap.get(PayConstant.PRFUND_REQUEST));
//            fuQianLaService.savePersonPayRefundLog(refundLog, reFundReqContract,  null);
//        }
//
//    }
}
