package com.fenbeitong.fenbeipay.http.controller.cashier;


import com.fenbeitong.fenbeipay.awplus.biz.RefundBiz;
import com.fenbeitong.fenbeipay.awplus.model.dto.PersonRefundRecordRespDTO;
import com.fenbeitong.fenbeipay.awplus.model.po.RefundResponsePo;
import com.fenbeitong.fenbeipay.awplus.model.po.RefundsRequestPo;
import com.fenbeitong.fenbeipay.awplus.service.gateway.wechat.WechatRefundCommonImpl;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.core.model.vo.common.BaseListResponseVo;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.service.redis.RedissonService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaCashierRefundAllMsg;
import com.fenbeitong.finhub.kafka.producer.IKafkaProducerPublisher;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.MessageFormat;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.CASHIER_CRONTAB_HEAD;

/**
 * 收银台Http服务退款Controller
 * RPC服务请参考fenbei-pay-api
 * @version  2018年11月19日10:57:06
 * <AUTHOR>
 */

@HttpService("/internal/cashier/refund")
public class InnerCashierOrderRefundSettlementController {

    /**强制解锁时间设置*/
    private final long lockTimeRefund = 6000L;

    /**等待时间**/
    private final long waitTimeRefund = 6100L;

    @Autowired
    private CashierOrderRefundSettlementService cashierOrderRefundSettlementService;

    @Autowired
    private WechatRefundCommonImpl webChatRefundCommon;

    @Autowired
    private IKafkaProducerPublisher iKafkaProducerPublisher;

    @Autowired
    protected RedissonService redissonService;

    @Autowired
    private RefundBiz refundBiz;
    @Autowired
    private DingDingMsgService dingDingMsgService;

    /**
     * 时间程序，拉取三方退款结果，更新退款状态，并通知场景方
     * @param request
     * @param response
     */
    @HttpService(value = "/cronpullthirdrefund/v2",method = RequestMethod.POST)
    public void queryThirdPay(HttpRequest request, HttpResponse response){
        FinhubLogger.info(CASHIER_CRONTAB_HEAD+"拉取退款结果，更新退款状态，并通知场景方...Start.....");
        CompletableFuture.runAsync(() ->
         cashierOrderRefundSettlementService.cronThirdRefundResult());
        ResponseResultUtils.success(response);
    }

    /**
     * 时间程序，通知场景方完成订单，状态为已支付
     * @param request
     * @param response
     */
    @HttpService(value = "/croncallbiz4hadrefunddone/v2",method = RequestMethod.POST)
    public void cronCallbBiz4HadRefundDone(HttpRequest request, HttpResponse response){
        FinhubLogger.info(CASHIER_CRONTAB_HEAD+"通知场景方完成退单，状态为已退款...Start.....");
        CompletableFuture.runAsync(() ->
         cashierOrderRefundSettlementService.cronCallBiz4HadRefundDone());
        ResponseResultUtils.success(response);
    }

    /**
     *
     * (HTTP Internal+RPC) 场景OC系统调用，生成退款交易流水号
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/refundtrade/v2",method = RequestMethod.POST)
    public void  refundTrade(HttpRequest request, HttpResponse response){
        CashierRefundTradeReqVo cashierRefundTradeReqVo = request.getBodyObject(CashierRefundTradeReqVo.class);
        final String lockKey = MessageFormat.format(RedisKeyConstant.CASHIER_REFUND_ORDERID_KEY, cashierRefundTradeReqVo.getFbOrderId());
        try {
            if(StringUtils.isEmpty(cashierRefundTradeReqVo.getEmployeeId())){
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            //加锁
            boolean lock = redissonService.tryLock(waitTimeRefund, lockTimeRefund, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.info("加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            CashierRefundTradeRespVo cashierRefundTradeRespVo;
            try{
                cashierRefundTradeRespVo  = cashierOrderRefundSettlementService.createRefundTradeAndSaas(cashierRefundTradeReqVo);
            }catch (Exception e){
                String errorMsg = "【收银台】退款异常:场景订单号:" + cashierRefundTradeReqVo.getFbOrderId() + e.getMessage();
                if (e instanceof FinPayException && Objects.equals(GlobalResponseCode.CASHIER_REFUND_TOTAL_AMOUNT_HAD_REFUND.getCode(), ((FinPayException)e).getCode())) {
                    FinhubLogger.warn(errorMsg, e);
                } else {
                    dingDingMsgService.sendMsg(errorMsg);
                    FinhubLogger.error(errorMsg, e);
                }
                
                throw e;
            }
            ResponseResultUtils.success(response,cashierRefundTradeRespVo);
        } catch (InterruptedException e) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
        } catch (Exception e) {
            throw e;
        }finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("释放锁失败：{}", lockKey, e);
            }
        }
    }

    /**
     *
     * 内部退款(用于修订错误数据)
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/refund/error/trade/v2",method = RequestMethod.POST)
    public void  refundErrorTrade(HttpRequest request, HttpResponse response){
        CashierRefundTradeReqVo cashierRefundTradeReqVo = request.getBodyObject(CashierRefundTradeReqVo.class);
        final String lockKey = MessageFormat.format(RedisKeyConstant.CASHIER_REFUND_ERROR_ORDER_ID_KEY, cashierRefundTradeReqVo.getFbOrderId());
        try {
            if(StringUtils.isEmpty(cashierRefundTradeReqVo.getEmployeeId())){
                throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
            }
            //加锁
            boolean lock = redissonService.tryLock(waitTimeRefund, lockTimeRefund, TimeUnit.MILLISECONDS, lockKey);
            FinhubLogger.info("加锁结果:" + lock + lockKey);
            if (!lock) {
                throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
            }
            CashierRefundTradeRespVo cashierRefundTradeRespVo;
            try{
                cashierRefundTradeRespVo  = cashierOrderRefundSettlementService.createErrorRefundTradeAndSaas(cashierRefundTradeReqVo);
            }catch (Exception e){
                String  errorMsg= "【收银台】退款异常:场景订单号:"+cashierRefundTradeReqVo.getFbOrderId()+e.getMessage();
                dingDingMsgService.sendMsg(errorMsg);
                FinhubLogger.error(errorMsg,e);
                throw e;
            }
            ResponseResultUtils.success(response,cashierRefundTradeRespVo);
        } catch (InterruptedException e) {
            throw new FinPayException(GlobalResponseCode.CASHIER_PAY_SETTLE_OPERATION_OVER);
        } catch (Exception e) {
            throw e;
        }finally {
            //放锁
            try {
                redissonService.unLock(lockKey);
                FinhubLogger.info("释放锁成功：{}", lockKey);
            } catch (Exception e) {
                FinhubLogger.error("释放锁失败：{}", lockKey, e);
            }
        }
    }


    /**
     *
     * (HTTP Internal+RPC) 支付能力指定最大退款方式退款
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/refundmaxtrade/v2",method = RequestMethod.POST)
    public void  refundMaxTrade(HttpRequest request, HttpResponse response){
        CashierRefundTradeMaxWayReqVo cashierRefundTradeMaxWayReqVo = request.getBodyObject(CashierRefundTradeMaxWayReqVo.class);
        if(StringUtils.isEmpty(cashierRefundTradeMaxWayReqVo.getEmployeeId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        CashierRefundTradeRespVo cashierRefundTradeRespVo;
        try{
            cashierRefundTradeRespVo  = cashierOrderRefundSettlementService.createRefundTradeMaxWayAndSaas(cashierRefundTradeMaxWayReqVo);
        }catch (Exception e){
            String  errorMsg= "【收银台】支付能力指定最大退款方式退款 退款异常:场景订单号:"+cashierRefundTradeMaxWayReqVo.getFbOrderId()+e.getMessage();
            dingDingMsgService.sendMsg(errorMsg);
            FinhubLogger.error(errorMsg,e);
            throw e;
        }
        ResponseResultUtils.success(response,cashierRefundTradeRespVo);
    }
    /**
     * (HTTP Internal+RPC)退款详情查询
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/queryrefund/detail/v2",method = RequestMethod.POST)
    public void getRefundDetail(HttpRequest request, HttpResponse response){
        CashierRefundQueryReqVo refundQueryReqVo = request.getBodyObject(CashierRefundQueryReqVo.class);
        if(StringUtils.isEmpty(refundQueryReqVo.getEmployeeId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        List<CashierRefundQueryRespVo> refundSettlements = cashierOrderRefundSettlementService.getRefundDetail(refundQueryReqVo);
        ResponseResultUtils.success(response,refundSettlements);

    }

    /**
     * 场景批量查询支付单[暂定最大1000条]
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/batchquery/order/v3",method = RequestMethod.POST)
    public void batchQuery(HttpRequest request, HttpResponse response){
        CashierBatchQueryReqVo cashierBatchQueryReqVo = request.getBodyObject(CashierBatchQueryReqVo.class);
        List<CashierBatchRefundQueryRespVo> settlement = cashierOrderRefundSettlementService.batchQuery(cashierBatchQueryReqVo);
        ResponseResultUtils.success(response,settlement);
    }

    /**
     * 场景批量查询支付单[暂定最大1000条]
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/batchquery/refundorder/v3",method = RequestMethod.POST)
    public void batchQueryByRefundOrderIds(HttpRequest request, HttpResponse response){
        CashierBatchRefundQueryReqVo cashierBatchQueryReqVo = request.getBodyObject(CashierBatchRefundQueryReqVo.class);
        List<CashierBatchRefundQueryRespVo> settlement = cashierOrderRefundSettlementService.batchQueryByRefundOrderIds(cashierBatchQueryReqVo);
        ResponseResultUtils.success(response,settlement);
    }

    /**
     *
     * (HTTP Internal+RPC) 场景OC系统调用，生成退款交易流水号
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/refundtrade4noemployee/v3",method = RequestMethod.POST)
    public void  refundtrade4NoEmployee(HttpRequest request, HttpResponse response){
        CashierRefundTradeNoEmployeeReqVo cashierRefundTradeReqVo = request.getBodyObject(CashierRefundTradeNoEmployeeReqVo.class);
        CashierRefundTradeRespVo cashierRefundTradeRespVo = cashierOrderRefundSettlementService.createRefundTrade4SaasNoEmployee(cashierRefundTradeReqVo);
        cashierOrderRefundSettlementService.checkRefundStatusByRefundTxnIdAndCallBiz(cashierRefundTradeRespVo.getRefundTxnId());
        ResponseResultUtils.success(response,cashierRefundTradeRespVo);
    }

    /**
     * (HTTP Internal+RPC) 场景OC系统调用，生成退款交易流水号
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/batchRefundTrade/v3", method = RequestMethod.POST)
    public void batchRefundTrade(HttpRequest request, HttpResponse response) {
        CashierBatchRefundTradeReqVo cashierBatchRefundTradezReqVo = request.getBodyObject(CashierBatchRefundTradeReqVo.class);
        List<CashierRefundTradeRespVo> batchRefundTradeAndCallBiz = null;
        try {
            batchRefundTradeAndCallBiz = cashierOrderRefundSettlementService.createBatchRefundTradeAndSaas(cashierBatchRefundTradezReqVo);
            //通知退单完成
            batchRefundTradeAndCallBiz.forEach(refundTradeRespVo -> {
                cashierOrderRefundSettlementService.checkRefundStatusByRefundOrderIdAndCallBiz(cashierBatchRefundTradezReqVo.getBatchRefundOrderId());
            });
            ResponseResultUtils.success(response, BaseListResponseVo.of(batchRefundTradeAndCallBiz));
        } catch (Exception e) {
            String msgError = "【收银台】【改签批量退款】异常:场景订单号:" + JsonUtils.toJson(cashierBatchRefundTradezReqVo) + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            throw e;
        }
    }

    @HttpService(value = "/refundWeChat/v2",method = RequestMethod.POST)
    public void refundWeChat(HttpRequest request, HttpResponse response){
        CashierRefundTradeReqVo cashier = request.getBodyObject(CashierRefundTradeReqVo.class);
        RefundsRequestPo refundsRequestPo = new RefundsRequestPo();
        refundsRequestPo.setOrderId(cashier.getFbOrderId());
        refundsRequestPo.setRefundNo(cashier.getRefundOrderId());
        refundsRequestPo.setTxnId(cashier.getCashierTxnId());
        refundsRequestPo.setRefundAmount(cashier.getPersonalThirdRefundPrice());
        refundsRequestPo.setTotalFee(cashier.getTotalRefundAmount());
        RefundResponsePo refundResponsePo =  webChatRefundCommon.refund(refundsRequestPo);
        ResponseResultUtils.success(response,refundResponsePo);
    }

    /**
     * 作废退款交易-单笔(回填单)
     * 【流程：只有因公可以作废】
     *
     * @param request response
     * @return
     * @since V3.2.1
     */
    @HttpService(value = "/xetrade/v3", method = RequestMethod.POST)
    public void xeRefundTrade(HttpRequest request, HttpResponse response) {
        CashierXeRefundTradeReqVo cashierXeRefundTradeReqVo = request.getBodyObject(CashierXeRefundTradeReqVo.class);
        try {
            CashierXeRefundTradeRespVo cashierXeRefundTradeRespVo = cashierOrderRefundSettlementService.xeRefundTradeOrSass(cashierXeRefundTradeReqVo);
            ResponseResultUtils.success(response, cashierXeRefundTradeRespVo);

        } catch (FinPayException e) {
            String msgError = "【收银台】【Http回填单退款交易作废】异常:" + cashierXeRefundTradeReqVo.getRefundTxnId() + e.getMessage();
            FinhubLogger.error("【收银台】【Http回填单退款交易作废】异常:{}", JsonUtils.toJson(cashierXeRefundTradeReqVo), e);
            dingDingMsgService.sendMsg(msgError);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (Exception e) {
            String msgError = "【收银台】【Http回填单退款交易作废】异常:退款交易号:" + cashierXeRefundTradeReqVo.getRefundTxnId() + e.getMessage();
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.error(msgError, e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * 客服操作
     * (HTTP Internal+RPC) 场景OC系统调用，生成退款交易流水号
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/refundtrade4cs/v2",method = RequestMethod.POST)
    public void  refundTrade4CS(HttpRequest request, HttpResponse response){
        CashierRefundTradeReqVo cashierRefundTradeReqVo = request.getBodyObject(CashierRefundTradeReqVo.class);
        CashierRefundTradeRespVo cashierRefundTradeRespVo;
        try{
            cashierRefundTradeRespVo  = cashierOrderRefundSettlementService.createRefundTradeAndSaas4CS(cashierRefundTradeReqVo);
        }catch (Exception e){
            String  errorMsg= "【收银台】客服操作退款异常:场景订单号:"+cashierRefundTradeReqVo.getFbOrderId()+e.getMessage();
            dingDingMsgService.sendMsg(errorMsg);
            FinhubLogger.error(errorMsg,e);
            throw e;
        }
        ResponseResultUtils.success(response,cashierRefundTradeRespVo);
    }

    /**
     * for test
     * @param request
     * @param response
     */
    @HttpService(value = "/pushkafka/v1",method = RequestMethod.GET)
    public void testKafkaMessage(HttpRequest request, HttpResponse response){
        String cashierTxnId = request.getParameter("cashierTxnId");
        String orderId = request.getParameter("orderId");
        String employeeId = request.getParameter("employeeId");
        String companyId = request.getParameter("companyId");
        FinhubLogger.info("【全额退款消息】testKafkaMessage 测试kafka，发送消息开始......");
        KafkaCashierRefundAllMsg refundAllMsg  = new KafkaCashierRefundAllMsg();
        refundAllMsg.setFbOrderId(orderId);
        refundAllMsg.setEmployeeId(employeeId);
        refundAllMsg.setCompanyId(companyId);
        iKafkaProducerPublisher.publish(refundAllMsg);
        FinhubLogger.info("【全额退款消息】testKafkaMessage 测试kafka，发送消息结束......");
    }

    /**
     * for test
     * @param request
     * @param response
     */
    @HttpService(value = "/refund/lock/v1",method = RequestMethod.GET)
    public void testRefundLock(HttpRequest request, HttpResponse response){
        String orderId = request.getParameter("orderId");
        FinhubLogger.info("【测试分布式事务锁等待时间】testRefundLock 分布式事物锁开始......");
        boolean b = cashierOrderRefundSettlementService.testRefundLock(orderId);
        FinhubLogger.info("【测试分布式事务锁等待时间】testRefundLock 分布式事物锁结果："+b);
        ResponseResultUtils.success(response,b);
    }

    /**
     * 订单中心清洗历史支付流水
     * @param request
     * @param response
     */
    @HttpService(value = "record/history/query",method = RequestMethod.POST)
    public void recordHistoryQuery(HttpRequest request, HttpResponse response){
        List<String> fbOrderIds = request.getBodyArray(String.class);
        List<PersonRefundRecordRespDTO> personRefundRecordRespDTOList = refundBiz.queryRefundRecordByOrderIds(fbOrderIds);
        ResponseResultUtils.success(response,personRefundRecordRespDTOList);
    }
}
