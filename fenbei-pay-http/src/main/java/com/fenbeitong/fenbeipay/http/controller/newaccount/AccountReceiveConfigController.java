package com.fenbeitong.fenbeipay.http.controller.newaccount;

import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountReceiceConfigReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountReceiceConfigRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountTypeOperateRespDTO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.fenbeipay.na.service.AccountReceiveConfigService;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2020/11/5
 * @Description
 */
@HttpService("/fbp/account/config")
public class AccountReceiveConfigController extends BaseController {


    @Autowired
    private AccountReceiveConfigService accountReceiveConfigService;


    @HttpService(value = "/query",method = RequestMethod.GET)
    public void queryReceiveConfig(HttpRequest request, HttpResponse response){

        String companyId = (String) request.getAttribute("companyId");
        String itemCode = request.getParameter("itemCode");
        AccountReceiceConfigRespDTO respDTO = accountReceiveConfigService.queryReceiveConfig(companyId,itemCode);
        ResponseResultUtils.success(response,respDTO);
    }

    @HttpService(value = "/save",method = RequestMethod.POST)
    public void saveReceiveConfig(HttpRequest request, HttpResponse response){
        UserInfoVO userInfoVO = getUserInfo(request);
        String companyId = getUserCompanyId(request);
        AccountReceiceConfigReqDTO reqDTO = request.getBodyObject(AccountReceiceConfigReqDTO.class);

        accountReceiveConfigService.saveReceiveConfig(reqDTO,userInfoVO,companyId);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/getEmployeeAccountEnableType",method = RequestMethod.GET)
    public void getEmployeeAccountEnableType(HttpRequest request, HttpResponse response){
        String companyId = (String) request.getAttribute("companyId");
        String lang = request.getHeader("lang");
        AccountTypeOperateRespDTO respDTO = accountReceiveConfigService.queryAccountTypeOperate(companyId,lang);
        ResponseResultUtils.success(response,respDTO);
    }


}
