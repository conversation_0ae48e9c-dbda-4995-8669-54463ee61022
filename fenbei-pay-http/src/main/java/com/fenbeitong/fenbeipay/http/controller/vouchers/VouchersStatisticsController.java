package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.resp.AccountRedcouponRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponSearchService;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportDTO;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportQuery;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.utils.EnumUtils;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.vouchers.constant.VoucherCommonUtils;
import com.fenbeitong.fenbeipay.vouchers.dto.OperationUserInfoDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersFlowRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersStatisticsReqiestDTO;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersOperationFlowService;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersPersonService;
import com.fenbeitong.fenbeipay.vouchers.vo.*;
import com.fenbeitong.finhub.common.constant.SettingItemCodeTypeEnum;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeSimpleInfoContract;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

import static com.fenbeitong.finhub.common.utils.DateUtils.FORMAT_DATE_YYYYMMDD;

/**
 * @Description: 分贝券企业web管理controller
 * @ClassName: VouchersForWebController
 * @Author: zhangga
 * @CreateDate: 2018/12/11 下午6:48
 * @UpdateUser:
 * @UpdateDate: 2018/12/11 下午6:48
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/vouchers/management")
public class VouchersStatisticsController extends BaseController {

    @Autowired
    private VouchersPersonService vouchersPersonService;
    @Autowired
    private VouchersOperationFlowService vouchersOperationFlowService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private IAccountRedcouponSearchService iAccountRedcouponSearchService;

    @Value("${export.task.host}")
    private String exportTaskHost;
    @Value("${export.query.host}")
    private String exportQueryHost;

    private static final String COMSUME_FLOW_EXPORT_DESCRIPTION = "导出说明：\n1、以下数据的查询范围由导出操作人员在查询时设置；\n2、消费数据包括支付成功的订单金额与退款完成的售后单金额。";
    private static final String GRANT_RECOVERY_STATISTICS_EXPORT_DESCRIPTION = "导出说明：\n1、以下数据的查询范围由导出操作人员在查询时设置；\n2、“回收金额”是指针对查询周期内已发出的分贝券，截止到查询时间已经回收的金额。";
    private static final String VOUCHER_FLOW_EXPORT_DESCRIPTION = "新分贝券流水导出说明：\n1、以下数据的查询范围由导出操作人员在查询时设置；\n2、分贝券流水是以单张券的维度记录的记录数据，即所有的操作都是针对券的操作，持有人是指被操作券的持有人。";


    @HttpService(value = "/consume/flow/v3", method = RequestMethod.POST)
    public void selectComsumeFlow(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersFlowRequestDTO requestDTO = request.getBodyObject(VouchersFlowRequestDTO.class);
        requestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        ResponsePage<VouchersFlowVO> responsePage = vouchersOperationFlowService.selectConsumeFlowByPage(requestDTO, true);
        ResponseResultUtils.success(response, responsePage);
    }


    @HttpService(value = "/consume/flow/export/v3", method = RequestMethod.POST)
    public void consumeFlowExport(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersFlowRequestDTO requestDTO = request.getBodyObject(VouchersFlowRequestDTO.class);
        String companyId = operationUserInfoDTO.getCompanyId();
        String operationUserId = operationUserInfoDTO.getOperationUserId();
        String operationUserName = operationUserInfoDTO.getOperationUserName();
        requestDTO.setCompanyId(companyId);
        //SaaS提供接口查询权限。
        Integer hideCode = iMessageSetupService.queryCheckedByItemCode(companyId, SettingItemCodeTypeEnum.ITEM_CODE_PHONE_NUM_HIDE.getCode());
        requestDTO.setHidePhoneNo(hideCode == null || hideCode == 0);
        Date startTime = requestDTO.getStartTime();
        Date endTime = requestDTO.getEndTime();
        ExportQuery exportQuery = requestDTO.getExportQuery();
        if (startTime == null || endTime == null || exportQuery == null) {
            FinhubLogger.error("【导出报表】参数错误，startTime：{}，endTime：{}，exportQuery：{}", startTime, endTime, exportQuery.toString());
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        //TODO 判断是否超过一年
//        Date startTimeAfter1Year = DateUtil.changeDateYear(startTime, 1);
//        if (endTime.compareTo(startTimeAfter1Year) > 0) {
//            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
//        }
        ExportDTO exportDTO = new ExportDTO();
        String exportQueryUrl = exportQueryHost + "/internal/vouchers/export/vouchers/flow";
        List<String> list = new ArrayList<>();
        EmployeeSimpleInfoContract employeeSimpleInfo = employeeService.getEmployeeSimpleInfo(operationUserId, companyId);
        if (null != employeeSimpleInfo && ObjUtils.isNotEmpty(employeeSimpleInfo.getOrg_unit_list())) {
            employeeSimpleInfo.getOrg_unit_list().forEach(orgUnitListBean -> {
                list.add(orgUnitListBean.getId());
            });
        }
        JSONObject query = requestDTO.getQuery();
        String parentIds = StringUtils.join(list, ",");
        String taskName = "分贝券消费记录_" + DateUtils.format(startTime, FORMAT_DATE_YYYYMMDD) + "_" + DateUtils.format(endTime, FORMAT_DATE_YYYYMMDD);
        exportQuery.setDescription(COMSUME_FLOW_EXPORT_DESCRIPTION);
        exportDTO.dtoBuild(query, exportQueryUrl, taskName, operationUserId, operationUserName, parentIds, exportDTO.getVoucherFlowColumns(), exportQuery);
        String jsonString = JSONObject.toJSONString(exportDTO);
        String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/task", jsonString);
        if (ObjUtils.isBlank(postBody)) {
            FinhubLogger.error("【导出报表】返回参数：{}", postBody);
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_EXPORT_CREATE_FAIL);
        }
        FinhubLogger.info("【导出报表】返回参数：{}", postBody);
        Object taskId = JSONObject.parseObject(postBody).getJSONObject("data").get("taskId");
        ResponseResultUtils.success(response, taskId);
    }


    @HttpService(value = "/grant_recovery/statistics/v3", method = RequestMethod.POST)
    public void grantRecoveryStatistics(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersRequestDTO requestDTO = request.getBodyObject(VouchersRequestDTO.class);
        requestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        ResponsePage<VoucherForStatisticsVO> responsePage = vouchersPersonService.selectVouchersByPage(requestDTO);
        ResponseResultUtils.success(response, responsePage);
    }

    @HttpService(value = "/grant_recovery/statistics/export/v3", method = RequestMethod.POST)
    public void grantRecoveryStatisticsExport(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersRequestDTO requestDTO = request.getBodyObject(VouchersRequestDTO.class);
        String companyId = operationUserInfoDTO.getCompanyId();
        String operationUserId = operationUserInfoDTO.getOperationUserId();
        String operationUserName = operationUserInfoDTO.getOperationUserName();
        requestDTO.setCompanyId(companyId);
        //SaaS提供接口查询权限。
//        Integer hideCode = iMessageSetupService.queryCheckedByItemCode(companyId, SettingItemCodeTypeEnum.ITEM_CODE_PHONE_NUM_HIDE.getCode());
//        requestDTO.setHidePhoneNo(hideCode == null || hideCode == 0);
        requestDTO.setHidePhoneNo(false);
        Date startTime = requestDTO.getStartTime();
        Date endTime = requestDTO.getEndTime();
        ExportQuery exportQuery = requestDTO.getExportQuery();
        if (startTime == null || endTime == null || exportQuery == null) {
            FinhubLogger.error("【导出报表】参数错误，startTime：{}，endTime：{}，exportQuery：{}", startTime, endTime, exportQuery.toString());
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        //判断是否超过31天
        VoucherCommonUtils.checkDateMore31Day(startTime, endTime);
        //TODO 判断是否超过一年
//        Date startTimeAfter1Year = DateUtil.changeDateYear(startTime, 1);
//        if (endTime.compareTo(startTimeAfter1Year) > 0) {
//            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
//        }
        ExportDTO exportDTO = new ExportDTO();
        String exportQueryUrl = exportQueryHost + "/internal/vouchers/export/grant_recovery/statistics";
        List<String> list = new ArrayList<>();
        EmployeeSimpleInfoContract employeeSimpleInfo = employeeService.getEmployeeSimpleInfo(operationUserId, companyId);
        if (null != employeeSimpleInfo && ObjUtils.isNotEmpty(employeeSimpleInfo.getOrg_unit_list())) {
            employeeSimpleInfo.getOrg_unit_list().forEach(orgUnitListBean -> {
                list.add(orgUnitListBean.getId());
            });
        }
        JSONObject query = requestDTO.getQuery();
        String parentIds = StringUtils.join(list, ",");
        String taskName = "分贝券发放记录_" + DateUtils.format(startTime, FORMAT_DATE_YYYYMMDD) + "_" + DateUtils.format(endTime, FORMAT_DATE_YYYYMMDD);
        exportQuery.setDescription(GRANT_RECOVERY_STATISTICS_EXPORT_DESCRIPTION);
        AccountRedcouponRespRPCDTO accountRedcouponRespRPCDTO = iAccountRedcouponSearchService.queryAccountCouponInfo(companyId);
        exportDTO.dtoBuild(query, exportQueryUrl, taskName, operationUserId, operationUserName, parentIds, exportDTO.getVoucherColumns(accountRedcouponRespRPCDTO != null), exportQuery);
        String jsonString = JSONObject.toJSONString(exportDTO);
        String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/task", jsonString);
        if (ObjUtils.isBlank(postBody)) {
            FinhubLogger.error("【导出报表】返回参数：{}", postBody);
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_EXPORT_CREATE_FAIL);
        }
        FinhubLogger.info("【导出报表】返回参数：{}", postBody);
        Object taskId = JSONObject.parseObject(postBody).getJSONObject("data").get("taskId");
        ResponseResultUtils.success(response, taskId);
    }

    @HttpService(value = "/recovery/statistics/v4", method = RequestMethod.POST)
    public void recoveryStatistics(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersRequestDTO requestDTO = request.getBodyObject(VouchersRequestDTO.class);
        requestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        //时间范围限制1年
        Date startTime = requestDTO.getStartTime();
        Date endTime = requestDTO.getEndTime();
        if (startTime == null || endTime == null) {
            endTime = new Date();
            startTime = DateUtils.addYear(endTime, -1);
        }
        VoucherCommonUtils.checkDateMore366Day(startTime, endTime);
        requestDTO.setStartRecoveryTime(startTime);
        requestDTO.setEndRecoveryTime(endTime);
        requestDTO.setStartTime(null);
        requestDTO.setEndTime(null);
        ResponsePage<VoucherForStatisticsVO> responsePage = vouchersPersonService.selectRecoveryVouchersByPage(requestDTO);
        ResponseResultUtils.success(response, responsePage);
    }

    @HttpService(value = "/recovery/statistics/export/v4", method = RequestMethod.POST)
    public void recoveryStatisticsExport(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersRequestDTO requestDTO = request.getBodyObject(VouchersRequestDTO.class);
        Date startTime = requestDTO.getStartTime();
        Date endTime = requestDTO.getEndTime();
        ExportQuery exportQuery = requestDTO.getExportQuery();
        if (startTime == null || endTime == null || exportQuery == null) {
            FinhubLogger.error("【导出报表】参数错误，startTime：{}，endTime：{}，exportQuery：{}", startTime, endTime, exportQuery.toString());
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (DateUtils.addYear(startTime, 3).before(endTime)) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_RECOVERY_STATISTICS_TIME_ERROR);
        }
        String companyId = operationUserInfoDTO.getCompanyId();
        String operationUserId = operationUserInfoDTO.getOperationUserId();
        String operationUserName = operationUserInfoDTO.getOperationUserName();
        requestDTO.setCompanyId(companyId);
//        //SaaS提供接口查询权限。
//        Integer hideCode = iMessageSetupService.queryCheckedByItemCode(companyId, SettingItemCodeTypeEnum.ITEM_CODE_PHONE_NUM_HIDE.getCode());
//        requestDTO.setHidePhoneNo(hideCode == null || hideCode == 0);
        requestDTO.setHidePhoneNo(false);
        ExportDTO exportDTO = new ExportDTO();
        String exportQueryUrl = exportQueryHost + "/internal/vouchers/export/recovery/statistics";
        List<String> list = new ArrayList<>();
        EmployeeSimpleInfoContract employeeSimpleInfo = employeeService.getEmployeeSimpleInfo(operationUserId, companyId);
        if (null != employeeSimpleInfo && ObjUtils.isNotEmpty(employeeSimpleInfo.getOrg_unit_list())) {
            employeeSimpleInfo.getOrg_unit_list().forEach(orgUnitListBean -> {
                list.add(orgUnitListBean.getId());
            });
        }
        JSONObject query = requestDTO.getQuery();
        String parentIds = StringUtils.join(list, ",");
        String taskName = "分贝券回收记录_" + DateUtils.format(startTime, FORMAT_DATE_YYYYMMDD) + "_" + DateUtils.format(endTime, FORMAT_DATE_YYYYMMDD);
        exportQuery.setDescription(GRANT_RECOVERY_STATISTICS_EXPORT_DESCRIPTION);
        exportDTO.dtoBuild(query, exportQueryUrl, taskName, operationUserId, operationUserName, parentIds, exportDTO.getRecoveryVoucherColumns(), exportQuery);
        String jsonString = JSONObject.toJSONString(exportDTO);
        String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/task", jsonString);
        if (ObjUtils.isBlank(postBody)) {
            FinhubLogger.error("【导出报表】返回参数：{}", postBody);
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_EXPORT_CREATE_FAIL);
        }
        FinhubLogger.info("【导出报表】返回参数：{}", postBody);
        Object taskId = JSONObject.parseObject(postBody).getJSONObject("data").get("taskId");
        ResponseResultUtils.success(response, taskId);
    }

    /**
     * @Date: 2021/1/25 8:04 下午
     * @Description 分贝券统计：查询分贝券流水消费、回收、发放、撤回、转出、剩余总和
     **/
    @HttpService(value = "/operation_flow/statistics/total", method = RequestMethod.POST)
    public void vouchersOperationFlowStatisticsTotal(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilegeNoMenu(request);
        VouchersStatisticsReqiestDTO requestDTO = request.getBodyObject(VouchersStatisticsReqiestDTO.class);
        if (ObjUtils.isBlank(requestDTO)) {
            FinhubLogger.error("【分贝券流水统计】请求参数：{}", JSONObject.toJSONString(request));
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        String companyId = operationUserInfoDTO.getCompanyId();
        requestDTO.setCompanyId(companyId);
        VouchersFlowStatisticsVo vouchersFlowStatisticsVo = vouchersOperationFlowService.selectVouchersFlowStatisticsTotal(requestDTO);
        if (ObjUtils.isBlank(vouchersFlowStatisticsVo)) {
            FinhubLogger.warn("【分贝券流水统计】统计结果为空，请求参数：{}", JsonUtils.toJson(requestDTO));
            throw new FinPayException(GlobalResponseCode.VOUCHER_FLOW_NO_RECORD_ERROR);
        }
        ResponseResultUtils.success(response, vouchersFlowStatisticsVo);
    }

    /**
     * @Date: 2021/1/25 8:04 下午
     * @Description 分贝券统计：查询分贝券流水消费、回收、发放、撤回、转出、剩余明细
     **/
    @HttpService(value = "/operation_flow/statistics/page", method = RequestMethod.POST)
    public void vouchersOperationFlowStatisticsPage(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilegeNoMenu(request);
        VouchersStatisticsReqiestDTO requestDTO = request.getBodyObject(VouchersStatisticsReqiestDTO.class);
        if (ObjUtils.isBlank(requestDTO)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        String companyId = operationUserInfoDTO.getCompanyId();
        requestDTO.setCompanyId(companyId);
        ResponsePage<VouchersFlowStatisticsVo> vouchersFlowStatisticsVoResponsePage = vouchersOperationFlowService.selectVouchersFlowStatisticsPage(requestDTO);
        ResponseResultUtils.success(response, vouchersFlowStatisticsVoResponsePage);
    }

    /**
     * @Date: 2021/1/25 8:04 下午
     * @Description 分贝券统计导出：查询分贝券流水消费、回收、发放、撤回、转出、剩余明细
     **/
    /*@HttpService(value = "/operation_flow/statistics/export", method = RequestMethod.POST)
    public void vouchersOperationFlowStatisticsExport(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilegeNoMenu(request);
        VouchersStatisticsReqiestDTO requestDTO = request.getBodyObject(VouchersStatisticsReqiestDTO.class);
        if (ObjUtils.isBlank(requestDTO)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        String companyId = operationUserInfoDTO.getCompanyId();
        requestDTO.setCompanyId(companyId);
        ResponsePage<VouchersFlowStatisticsVo> vouchersFlowStatisticsVoResponsePage = vouchersOperationFlowService.selectVouchersFlowStatisticsPage(requestDTO);
        ResponseResultUtils.success(response, vouchersFlowStatisticsVoResponsePage.getDataList());
    }*/


    /**
     * 分贝券流水查询条件接口
     * @param request
     * @param response
     */
    @HttpService(value = "/get/voucher/flow/condition", method = RequestMethod.POST)
    public void getVoucherFlowCondition(HttpRequest request, HttpResponse response) {
        //校验权限
        checkPrivilege(request);
        Map<String,List> enumMap = new HashMap<>(4);
        enumMap.put("categoryTypeList",EnumUtils.getCategoryTypeEnumList());
        enumMap.put("voucherFlowTypeList",EnumUtils.getVoucherFlowType4Stereo());
        enumMap.put("writeInvoiceTypeList",EnumUtils.getWriteInvoiceType());
        enumMap.put("accountSubTypeList",EnumUtils.getAccountSubTypeList());
        ResponseResultUtils.success(response,enumMap);
    }


    /**
     * 分贝券流水查询接口
     * @param request
     * @param response
     */
    @HttpService(value = "/get/voucher/flow/list", method = RequestMethod.POST)
    public void getVoucherFlowList(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersOperationRequestVO requestVO = request.getBodyObject(VouchersOperationRequestVO.class);
        requestVO.setCompanyId(operationUserInfoDTO.getCompanyId());
        ResponseResultUtils.success(response, vouchersOperationFlowService.getVoucherFlowList(requestVO));
    }

    /**
     * 创建导出任务
     * @param request
     * @param response
     */
    @HttpService(value = "/task/export/create", method = RequestMethod.POST)
    public void createExportTask(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersOperationRequestVO requestVO  = request.getBodyObject(VouchersOperationRequestVO.class);
        String companyId = operationUserInfoDTO.getCompanyId();
        String operationUserId = operationUserInfoDTO.getOperationUserId();
        String operationUserName = operationUserInfoDTO.getOperationUserName();
        requestVO.setCompanyId(companyId);
        Date startTime = requestVO.getStartTime();
        Date endTime = requestVO.getEndTime();
        ExportQuery exportQuery = requestVO.getExportQuery();
        if (startTime == null || endTime == null || exportQuery == null) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        String exportQueryUrl = exportQueryHost + "/internal/vouchers/task/export/flow";
        List<String> list = new ArrayList<>();
        EmployeeSimpleInfoContract employeeSimpleInfo = employeeService.getEmployeeSimpleInfo(operationUserId, companyId);
        if (null != employeeSimpleInfo && ObjUtils.isNotEmpty(employeeSimpleInfo.getOrg_unit_list())) {
            employeeSimpleInfo.getOrg_unit_list().forEach(orgUnitListBean -> {
                list.add(orgUnitListBean.getId());
            });
        }
        //SaaS提供接口查询权限。
        Integer hideCode = iMessageSetupService.queryCheckedByItemCode(companyId, SettingItemCodeTypeEnum.ITEM_CODE_PHONE_NUM_HIDE.getCode());
        requestVO.setHidePhoneNo(hideCode == null || hideCode == 0);
        ExportDTO exportDTO = new ExportDTO();
        String parentIds = StringUtils.join(list, ",");
        String taskName = "分贝券流水记录_" + DateUtils.format(startTime, FORMAT_DATE_YYYYMMDD) + "_" + DateUtils.format(endTime, FORMAT_DATE_YYYYMMDD);
        exportQuery.setDescription(VOUCHER_FLOW_EXPORT_DESCRIPTION);
        JSONObject query = requestVO.getQuery();
        exportDTO.dtoBuild(query, exportQueryUrl, taskName, operationUserId, operationUserName, parentIds, exportDTO.getStereoVoucherFlowColumns(), exportQuery);
        String jsonString = JSONObject.toJSONString(exportDTO);
        String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/task", jsonString);
        if (ObjUtils.isBlank(postBody)) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_TASK_EXPORT_CREATE_FAIL);
        }
        Object taskId = JSONObject.parseObject(postBody).getJSONObject("data").get("taskId");
        ResponseResultUtils.success(response, taskId);
    }

}
