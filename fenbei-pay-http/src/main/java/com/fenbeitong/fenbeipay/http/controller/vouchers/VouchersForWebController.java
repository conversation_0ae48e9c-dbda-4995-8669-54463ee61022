package com.fenbeitong.fenbeipay.http.controller.vouchers;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTermTypeEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTypeEnum;
import com.fenbeitong.fenbeipay.cashier.transfer.PersonTransferService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTempletSub;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.StereoMessageCode;
import com.fenbeitong.fenbeipay.http.vo.CompanyFbqRuleVO;
import com.fenbeitong.fenbeipay.vouchers.dto.OperationUserInfoDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VoucherTermValidDayHM;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTempletDTO;
import com.fenbeitong.fenbeipay.vouchers.dto.VouchersTempletRequestDTO;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTempletService;
import com.fenbeitong.fenbeipay.vouchers.templet.service.VouchersTypeService;
import com.fenbeitong.fenbeipay.vouchers.dto.VoucherTransferConfigDTO;
import com.fenbeitong.fenbeipay.vouchers.vo.VouchersTempletSubVO;
import com.fenbeitong.fenbeipay.vouchers.vo.VouchersTempletVO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyFbqRuleDTO;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 分贝券企业web管理controller
 * @ClassName: VouchersForWebController
 * @Author: zhangga
 * @CreateDate: 2018/12/11 下午6:48
 * @UpdateUser:
 * @UpdateDate: 2018/12/11 下午6:48
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/vouchers/management")
@Slf4j
public class VouchersForWebController extends BaseController {
    @Autowired
    private VouchersTempletService vouchersTempletService;
    @Autowired
    private VouchersTypeService vouchersTypeService;
    @Autowired
    private ICompanyService iCompanyService;
    @Autowired
    private PersonTransferService personTransferService;

    private static final String VOUCHER_TEMPLET_ID = "voucherTempletId";

    /**
     * 查询企业先票分贝券和后票分贝券的最大有效期
     **/
    @HttpService(value = "/queryFbqValidDayRule", method = RequestMethod.GET)
    public void queryFbqValidDayRule(HttpRequest request, HttpResponse response) {
        String companyId =  request.getParameter("companyId");
        if (StringUtils.isEmpty(companyId)) {
            throw new FinhubException(StereoMessageCode.ILLEGAL_ARGUMENT,"企业ID不可为空");
        }
        CompanyFbqRuleDTO companyFbqRuleDTO = iCompanyService.queryCompanyFbqRule(companyId);
        CompanyFbqRuleVO companyFbqRuleVO = new CompanyFbqRuleVO(companyFbqRuleDTO);
        ResponseResultUtils.success(response, companyFbqRuleVO);
    }

    /**
     * @Description: 创建分贝券模板
     * @methodName: addVouchersTemplet
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:12 PM
     **/
    @HttpService(value = "/add/vouchers/templet/v3", method = RequestMethod.POST)
    public void addVouchersTemplet(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfo = checkPrivilege(request);
        VouchersTempletDTO vouchersTempletDTO = request.getBodyObject(VouchersTempletDTO.class);
        FinhubLogger.info("分贝券模板修改,vouchersTempletDTO:{}",JSONObject.toJSONString(vouchersTempletDTO));
        checkParams(vouchersTempletDTO, operationUserInfo.getCompanyId());
        //校验当前企业券是否可先开票
        checkInvoicePrivilege(operationUserInfo, vouchersTempletDTO.getWriteInvoiceType());
        BeanUtils.copyProperties(operationUserInfo, vouchersTempletDTO);
        //校验使用场景
        vouchersTempletDTO.checkParameters();
        String id = vouchersTempletService.createVouchersTemplet(vouchersTempletDTO);
        ResponseResultUtils.success(response, id);
    }

    private void checkParams(VouchersTempletDTO vouchersTempletDTO, String companyId) {
        log.info("vouchersTempletDTO:{}", vouchersTempletDTO);
        CompanyFbqRuleDTO companyFbqRuleDTO = iCompanyService.queryCompanyFbqRule(companyId);
        log.info("companyFbqRuleDTO:{}", companyFbqRuleDTO);
        //检查天，时，分钟是否合法
        DateUtil.checkTime(vouchersTempletDTO.getVoucherExpiryNotice(),vouchersTempletDTO.getVoucherTermType(),
                JSONObject.toJSONString(vouchersTempletDTO.getVoucherTermValidityHM()),
                vouchersTempletDTO.getWriteInvoiceType(),
                companyFbqRuleDTO);
        //检测文案和配置
        if(Integer.valueOf("1").equals(vouchersTempletDTO.getOpenEnable())){
            if (StringUtils.isEmpty(vouchersTempletDTO.getBackgroundUrl()) || StringUtils.isEmpty(vouchersTempletDTO.getNoticeContent())){
                throw new FinPayException(GlobalResponseCode.VOUCHER_BACKGROUND_NOTICE_PARAM_ERROR);
            }
            if (vouchersTempletDTO.getNoticeContent().length() > 30){
                throw new FinPayException(GlobalResponseCode.VOUCHER_NOTICE_NO_TOO_LONG);
            }
        }

    }

    /**
     * @Description: 校验分贝券模板是否在使用
     * @methodName: checkVouchersTemplet
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/22 3:05 PM
     **/
    @HttpService(value = "/check/vouchers/templet/{voucherTempletId}/v3", method = RequestMethod.GET)
    public void checkVouchersTemplet(HttpRequest request, HttpResponse response) {
        //校验权限
        checkPrivilege(request);
        String voucherTempletId = request.getPathValue("voucherTempletId");
        if (ObjUtils.isEmpty(voucherTempletId) || VOUCHER_TEMPLET_ID.equals(voucherTempletId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        //校验有效期
        List<VouchersTempletSub> templetSubs = vouchersTempletService.checkVouchersTemplet(voucherTempletId);
        List<VouchersTempletSubVO> vos = new ArrayList<>();
        templetSubs.forEach(templetSub -> {
            VouchersTempletSubVO vo = new VouchersTempletSubVO();
            BeanUtils.copyProperties(templetSub, vo);
            vos.add(vo);
        });
        ResponseResultUtils.success(response, vos);
    }

    /**
     * @Description: 删除分贝券模板
     * @methodName: removeVouchersTemplet
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:12 PM
     **/
    @HttpService(value = "/remove/vouchers/templet/{voucherTempletId}/v3", method = RequestMethod.POST)
    public void removeVouchersTemplet(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String voucherTempletId = request.getPathValue("voucherTempletId");
        if (ObjUtils.isEmpty(voucherTempletId) || VOUCHER_TEMPLET_ID.equals(voucherTempletId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        vouchersTempletService.deleteVouchersTempletById(voucherTempletId, operationUserInfoDTO);
        ResponseResultUtils.success(response);
    }

    /**
     * @Description: 修改分贝券模板
     * @methodName: editVouchersTemplet
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:12 PM
     **/
    @HttpService(value = "/edit/vouchers/templet/v3", method = RequestMethod.POST)
    public void editVouchersTemplet(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        VouchersTempletDTO vouchersTempletDTO = request.getBodyObject(VouchersTempletDTO.class);
        FinhubLogger.info("分贝券模板修改,vouchersTempletDTO:{}",JSONObject.toJSONString(vouchersTempletDTO));
        checkParams(vouchersTempletDTO, operationUserInfoDTO.getCompanyId());
        //校验当前企业券是否可先开票
        checkInvoicePrivilege(operationUserInfoDTO, vouchersTempletDTO.getWriteInvoiceType());
        BeanUtils.copyProperties(operationUserInfoDTO, vouchersTempletDTO);
        //校验有效期
        vouchersTempletDTO.checkParameters();
        boolean update = vouchersTempletService.updateVouchersTempletById(vouchersTempletDTO);
        if (!update) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_TEMPLET_EDIT_FAIL);
        }
        ResponseResultUtils.success(response);
    }

    /**
     * @Description: 列表查询分贝券模板
     * @methodName: queryVouchersTempletList
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:12 PM
     **/
    @HttpService(value = "/query/vouchers/templet/list/v3", method = RequestMethod.POST)
    public void queryVouchersTempletList(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilegeNoMenu(request);
        VouchersTempletRequestDTO vouchersTempletRequestDTO = request.getBodyObject(VouchersTempletRequestDTO.class);
        vouchersTempletRequestDTO.setCompanyId(operationUserInfoDTO.getCompanyId());
        ResponsePage<VouchersTempletVO> page = vouchersTempletService.getVouchersTempletList(vouchersTempletRequestDTO);
        ResponseResultUtils.success(response, page);
    }

    /**
     * @Description: 分贝券模板查询
     * @methodName: queryVouchersTempletById
     * @Param: [request, response]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/5/23 3:13 PM
     **/
    @HttpService(value = "/query/vouchers/templet/{voucherTempletId}/v3", method = RequestMethod.GET)
    public void queryVouchersTempletById(HttpRequest request, HttpResponse response) {
        //校验权限
        OperationUserInfoDTO operationUserInfoDTO = checkPrivilege(request);
        String voucherTempletId = request.getPathValue("voucherTempletId");
        if (ObjUtils.isBlank(voucherTempletId) || VOUCHER_TEMPLET_ID.equals(voucherTempletId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        VouchersTemplet vouchersTemplet = null;
        if (NumberUtils.isNumber(voucherTempletId)) {
            Long voucherTempletNumId = Long.valueOf(voucherTempletId);
            vouchersTemplet = vouchersTempletService.getVouchersTempletByNumId(operationUserInfoDTO.getCompanyId(), voucherTempletNumId);
        } else {
            vouchersTemplet = vouchersTempletService.getVouchersTempletById(voucherTempletId);
        }
        if (ObjUtils.isEmpty(vouchersTemplet)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        //封装VO参数
        VouchersTempletVO vouchersTempletVO = new VouchersTempletVO();
        BeanUtils.copyProperties(vouchersTemplet, vouchersTempletVO);
        VoucherTermValidDayHM voucherTermValidityHM = new VoucherTermValidDayHM();
        if(Objects.equals(vouchersTemplet.getVoucherTermType(),VoucherTermTypeEnum.TERM_OF_DAY_HOUR_MINUTE.getValue())){
            JSONObject json = JSONObject.parseObject(vouchersTemplet.getVoucherTermValidity());
            voucherTermValidityHM.setDay(json.getInteger("day"));
            voucherTermValidityHM.setHour(json.getInteger("hour"));
            voucherTermValidityHM.setMinute(json.getInteger("minute"));
        }
        if(Objects.equals(vouchersTemplet.getVoucherTermType(),VoucherTermTypeEnum.TERM_OF_DAYS.getValue())){
            voucherTermValidityHM.setDay(Integer.parseInt(vouchersTemplet.getVoucherTermValidity()));
            voucherTermValidityHM.setHour(0);
            voucherTermValidityHM.setMinute(0);
        }
        vouchersTempletVO.setVoucherTermValidityHM(voucherTermValidityHM);
        //处理分贝券分类
        List<String> voucherTypes = JSONArray.parseArray(vouchersTemplet.getVoucherTypeList(), String.class);
        List<String> distinctVoucherType = VoucherTypeEnum.getDistinctVoucherType(voucherTypes);
        List<String> voucherTypeNames = new ArrayList<>();
        if (ObjUtils.isNotEmpty(distinctVoucherType)) {
            voucherTypeNames = vouchersTypeService.getVouchersTypeNames(distinctVoucherType);
        }
        vouchersTempletVO.setVoucherTypeNames(voucherTypeNames);
        vouchersTempletVO.setVoucherTypeList(distinctVoucherType);
        ResponseResultUtils.success(response, vouchersTempletVO);
    }

    /**
     * 查询弹框背景图片
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/query/background", method = RequestMethod.GET)
    public void queryBackground(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        ResponseResultUtils.success(response, vouchersTypeService.getVouchersBackgrounds());
    }


    /**
     * 查询企业转让券配置
     * @param request
     * @param response
     */
    @HttpService(value = "/save/transfer/setting", method = RequestMethod.POST)
    public void saveTransferSetting(HttpRequest request, HttpResponse response) {
        OperationUserInfoDTO operationUserInfo = checkPrivilege(request);
        VoucherTransferConfigDTO voucherTransferConfigDTO = request.getBodyObject(VoucherTransferConfigDTO.class);
        voucherTransferConfigDTO.setCompanyId(operationUserInfo.getCompanyId());
        voucherTransferConfigDTO.setOperationUserId(operationUserInfo.getOperationUserId());
        voucherTransferConfigDTO.setOperationUserName(operationUserInfo.getOperationUserName());
        personTransferService.saveTransferSetting(voucherTransferConfigDTO);
        ResponseResultUtils.success(response);
    }
}
