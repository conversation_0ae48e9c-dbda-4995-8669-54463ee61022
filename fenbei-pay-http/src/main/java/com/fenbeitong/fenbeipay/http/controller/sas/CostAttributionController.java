package com.fenbeitong.fenbeipay.http.controller.sas;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.cashier.settlement.CashierOrderCostAttributionService;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CostAttributionListRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.CostAttributionReqVo;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020/7/7 3:15 下午
 * @Description 费用归属
 */
@HttpService("/internal")
public class CostAttributionController extends BaseController {

    @Autowired
    private CashierOrderCostAttributionService cashierOrderCostAttributionService;

    @HttpService(value = "/cost/attribution/list",method = RequestMethod.POST)
    public void costAttributionList(HttpRequest request, HttpResponse response){

        CostAttributionReqVo reqVo =  request.getBodyObject(CostAttributionReqVo.class);
        ResponsePage<CostAttributionListRespVo> data =  cashierOrderCostAttributionService.getCostAttributionList(reqVo);
        ResponseResultUtils.success(response, data);
    }
}
