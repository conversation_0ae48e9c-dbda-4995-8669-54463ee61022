package com.fenbeitong.fenbeipay.http.controller.newaccount;

import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenChangeType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwBySubAndBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponInfoVO;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.newaccount.*;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountGeneralService;
import com.fenbeitong.fenbeipay.nf.unit.service.UFundFreezenService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant.*;

/**
 * 新账户--APP端
 */
@HttpService("/fbp/app/new/account")
public class AccountAppController {

    @Autowired
    private UAccountGeneralService uAccountGeneralService;

    @Autowired
    private UFundFreezenService uFundFreezenService;

    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    private AcctCompanyGatewayService acctCompanyGatewayService;

    /**
     * 我的-账户与消费分析(新账户体系)
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/my_account_analysis", method = RequestMethod.GET)
    public void myAccountAnalysis(HttpRequest request, HttpResponse response) {
        AccountAnalysisReqVo analysisReqVo = new AccountAnalysisReqVo();
        boolean voucherStat = request.getBooleanParameter("voucherStat", false);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        analysisReqVo.setVoucherStat(voucherStat);
        analysisReqVo.setEmployeeId(request.getAttribute(USER_ID).toString());
        analysisReqVo.setCompanyId(companyId);
        //总账户
        AccountGeneralRespVo generalRespVo = new AccountGeneralRespVo();
        AccountGeneral accountGeneral = uAccountGeneralService.findByCompanyIdAndBank(analysisReqVo.getCompanyId(), BankNameEnum.FBT.getCode(), analysisReqVo.getCompanyId());
        if (ObjUtils.isEmpty(accountGeneral)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_GENERAL_NOT_EXIST);
        }
        BeanUtils.copyProperties(accountGeneral, generalRespVo);
        //子账户 激活的
        AcctComGwBySubAndBankReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwBySubAndBankReqDTO();
        acctComGwBySubAndBankReqDTO.setCompanyId(analysisReqVo.getCompanyId());
        acctComGwBySubAndBankReqDTO.setBankAccountNo(analysisReqVo.getCompanyId());
        acctComGwBySubAndBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
        acctComGwBySubAndBankReqDTO.setBankAccountNo(analysisReqVo.getCompanyId());
        List<AcctCommonBaseDTO> accountSubList = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
        if (ObjUtils.isEmpty(accountSubList)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
        }
        AccountSubRespVo businessAccountSub = new AccountSubRespVo();
        AccountSubIndividualRespVo individualAccountSub = new AccountSubIndividualRespVo();
        AccountSubRespVo enterpriseAccountSub = new AccountSubRespVo();
        for (AcctCommonBaseDTO accountsub : accountSubList) {
            if (FundAccountSubType.isBusinessAccount(accountsub.getAccountSubType())) {
                BeanUtils.copyProperties(accountsub, businessAccountSub);
                //企业账户
                BeanUtils.copyProperties(accountsub, enterpriseAccountSub);
                continue;
            }
            if (FundAccountSubType.isIndividualAccount(accountsub.getAccountSubType())) {
                BeanUtils.copyProperties(accountsub, individualAccountSub);
                if (analysisReqVo.isVoucherStat()) {
                    Date currentMonthStart = DateUtil.getTimesMonthStart();
                    Date currentMonthEnd = DateUtil.getTimesNextMonthStart();
                    BigDecimal grantAmount = uFundFreezenService.querySumFreezeBudgetFlow(analysisReqVo.getCompanyId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountsub.getAccountModel(), FreezenUseType.INDIVIDUAL_VOUCHERS, currentMonthStart, currentMonthEnd, Arrays.asList(FreezenChangeType.FREEZING.getKey()));
                    BigDecimal withdrawalAmount = uFundFreezenService.querySumFreezeBudgetFlow(analysisReqVo.getCompanyId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountsub.getAccountModel(), FreezenUseType.INDIVIDUAL_VOUCHERS, currentMonthStart, currentMonthEnd, Arrays.asList(FreezenChangeType.UNFREEZING.getKey()));
                    BigDecimal consumeAmount = uFundFreezenService.querySumFreezeBudgetFlow(analysisReqVo.getCompanyId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey(), accountsub.getAccountModel(), FreezenUseType.INDIVIDUAL_VOUCHERS, currentMonthStart, currentMonthEnd, Arrays.asList(FreezenChangeType.PAY.getKey(), FreezenChangeType.REFUND.getKey()));
                    individualAccountSub.setVoucherConsumeAmount(consumeAmount.abs());
                    individualAccountSub.setVoucherGrantAmount(grantAmount.abs());
                    individualAccountSub.setVoucherWithdrawalAmount(withdrawalAmount.abs());
                }
                continue;
            }
        }
        //增加红包券余额
        AccountRedcouponInfoVO redcouponInfoVO = accountRedcouponSearchService.queryAccountCouponInfoVO(companyId);
        if (redcouponInfoVO == null) {
            redcouponInfoVO = new AccountRedcouponInfoVO();
        }
        AccountAnalysisRespVo analysisRespVo = new AccountAnalysisRespVo(generalRespVo, businessAccountSub, individualAccountSub, enterpriseAccountSub, redcouponInfoVO);
        ResponseResultUtils.success(response, analysisRespVo);
    }


}
