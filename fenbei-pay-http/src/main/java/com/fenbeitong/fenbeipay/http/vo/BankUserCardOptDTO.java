package com.fenbeitong.fenbeipay.http.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class BankUserCardOptDTO implements Serializable {

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 操作类型 ：2-充值,3-提现,81-错花还款,82-错花还款退款
     * com.fenbeitong.finhub.common.constant.FundBankUserCardOptType
     */
    private Integer operationType;

    /**
     * 银行名称
     * @see com.fenbeitong.finhub.common.constant.BankNameEnum
     */
    private String bankName;

    /**
     * 银行虚户
     */
    private String bankAcctId;

    /**
     * 银行账户Id
     */
    private String bankAccountNo;

    /**
     * 银行交易流水号
     */
    private String bankTransNo;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal operationAmount;

    /**
     * 当前操作业务ID：消费为订单ID,转入转出为子账户类型key,提现为提现单号
     */
    private String bizNo;

    /**
     * 退款：当前操作业务ID：消费为订单ID,转入转出为子账户类型key,提现为提现单号
     */
    private String reBizNo;

    /**
     * 交易说明(内部说明)
     */
    private String operationDescription;

    /**
     * 操作人Id
     */
    private String operationUserId;
    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 对手银行名称
     * @see com.fenbeitong.finhub.common.constant.BankNameEnum
     */
    private String targetBankName;

    /**
     * 对手银行账号
     */
    private String targetBankAccountNo;


    /**
     * 打款方银行名称
     */
    private String targetBankAllName;

    /**
     * 直接链接银行：1:新帐户4.0开头 2:琥珀开户
     */
    private Integer directAcctType;



}
