package com.fenbeitong.fenbeipay.http.controller.paycenter;
/**
 * Created by mac on 18/10/14.
 */

/**
 * 付钱公共方法
 */
@Deprecated
//@HttpService("fbp/payment")
public class PayPriCommonController {
//    private static final Logger logger = LoggerFactory.getLogger(PayPriCommonController.class);
//    @Autowired
//    private FuQianLaService fuQianLaService;
//    @Autowired
//    private EmployeeRedisService employeeRedisService;
//    @Autowired
//    private CashierOrderSettlementService cashierOrderSettlementService;

    /**
     * 预下单接口
     *
     * @param request
     * @param response
     * @throws IOException
     */
//    @HttpService(value = "/order/create/v1", method = RequestMethod.POST)
//    public void create(HttpRequest request, HttpResponse response) throws IOException {
//        if (request != null) {
//            throw new FinPayException(GlobalResponseCode.APP_NEED_UPDATE);
//        }
//        String requestBody = request.getBody();
//        Object userId = request.getAttribute("userId");
//        Object companyId = request.getAttribute("companyId");
//        Object token = request.getAttribute("token");
//        logger.info("预下单签名参数：" + requestBody);
//        //拼接基本参数
//        FuQianLaPayCommonReqContract fuQianLaPayCommonReqContract = new FuQianLaPayCommonReqContract();
//        fuQianLaPayCommonReqContract = JsonUtils.toObj(requestBody, FuQianLaPayCommonReqContract.class);
//        //检查用户是否有需要支付的订单信息
//        CheckOrderPayContract checkOrderPayContract = new CheckOrderPayContract();
//        String lockKey = fuQianLaPayCommonReqContract.getPersonOrderNo();
//        Long locktime = 0l;
//        RedisTemplate<Serializable, Serializable> redisTemp = employeeRedisService.getRedisTemplate();
//        FuQianLaPayContract fuQianLaPayContract = new FuQianLaPayContract();
//        try {
//            locktime = RedisDistributionLock.lock(lockKey, redisTemp);
//            if (locktime > 0 || StringUtils.isEmpty(lockKey)) {
//                String orderRes = HttpclientUtils.getResult(ScenePreUrl.valueOf(Integer.valueOf(fuQianLaPayCommonReqContract.getOrderType())).getDesc() + "?order_id=" + fuQianLaPayCommonReqContract.getPersonOrderNo(), token.toString());
//                logger.info("预下单接口检查参数:" + orderRes);
//                ResponseResultEntity<CheckOrderPayContract> data = JsonUtils.toObj(orderRes, ResponseResultEntity.class);
//                checkOrderPayContract = JsonUtils.toObj(JsonUtils.toJson(data.getData()), CheckOrderPayContract.class);
//                if (fuQianLaPayCommonReqContract.getOrderType().equals("11") && checkOrderPayContract.getIs_over_time()) {
//                    ResponseResultUtils.fail(response, new FinhubException(checkOrderPayContract.getError_data().getError_code(), checkOrderPayContract.getError_data().getError_msg()));
//                    return;
//                }
//                //有需要支付的订单
//                if (checkOrderPayContract.getNeed_personal() && !checkOrderPayContract.getIs_personal_paied()) {
//                    fuQianLaPayContract = fuQianLaService.createOrder(fuQianLaPayCommonReqContract, userId.toString(),
//                            companyId.toString(), checkOrderPayContract, FuQianLaConstant.appid, FuQianLaConstant.notify_url, FuQianLaConstant.fuqianla_fenbeitong_env);
//                    if (fuQianLaPayContract == null) {
//
//                        fuQianLaPayContract.setNeedPay(1);
//                    } else {
//                        //检查是否有个人部分支付订单
//                        boolean checkFlag = fuQianLaService.checkPayLess(userId.toString(), fuQianLaPayCommonReqContract.getPersonOrderNo());
//                        if (checkFlag) {
//                            String clientVersion = "1.9.6";
//                            if (VersionTool.compare(fuQianLaPayCommonReqContract.getClient_version(), clientVersion) < 0 &&
//                                    org.apache.commons.lang3.StringUtils.isNotEmpty(fuQianLaPayCommonReqContract.getClient_version())) {
//                                fuQianLaPayContract.setNeedPay(2);
//
//                            } else {
//                                fuQianLaPayContract.setNeedPay(3);
//                            }
//                        } else {
//                            fuQianLaPayContract.setNeedPay(0);
//                        }
//                    }
//                    ResponseResultUtils.success(response, fuQianLaPayContract);
//                } else {
//                    fuQianLaPayContract.setNeedPay(1);
//                    ResponseResultUtils.success(response, fuQianLaPayContract);
//
//                }
//            } else {
//                logger.info("支付速度频率大");
//                fuQianLaPayContract.setNeedPay(2);
//                ResponseResultUtils.success(response, fuQianLaPayContract);
//            }
//        } catch (Exception e) {
//            logger.error("检查用户个人支付异常了:" + e.getLocalizedMessage());
//            ResponseResultUtils.fail(response, new FinhubException(UcMessageCode.EXCEPTION, "系统繁忙,请稍候再试"));
//        } finally {
//            RedisDistributionLock.unlock(lockKey, locktime, redisTemp);
//        }
//    }


    /**
     * 直接签名字符串
     *
     * @param request
     * @param response
     * @throws IOException
     */
//    @HttpService(value = "/auth/sign/v1", method = RequestMethod.POST)
//    public void sign(HttpRequest request, HttpResponse response) throws IOException {
//        if (request != null) {
//            throw new FinPayException(GlobalResponseCode.APP_NEED_UPDATE);
//        }
//        String requestBody = request.getBody();
//        Object userId = request.getAttribute("userId");
//        logger.info("下单签名参数：" + requestBody);
//        SignInfoReqContract signInfoContract = JsonUtils.toObj(requestBody, SignInfoReqContract.class);
//        //酒店验证超时时间信息等
//        if (signInfoContract.getSignInfo().indexOf("optional=11") > -1 || signInfoContract.getSignInfo().indexOf("酒店") > -1) {
//            Object token = request.getAttribute("token");
//            CheckOrderPayContract checkOrderPayContract = new CheckOrderPayContract();
//            String orderRes = HttpclientUtils.getResult(ScenePreUrl.valueOf(11).getDesc() + "?order_id=" + signInfoContract.getPersonOrderNo(), token.toString());
//            logger.info("预下单接口检查参数:" + orderRes);
//            ResponseResultEntity<CheckOrderPayContract> data = JsonUtils.toObj(orderRes, ResponseResultEntity.class);
//            checkOrderPayContract = JsonUtils.toObj(JsonUtils.toJson(data.getData()), CheckOrderPayContract.class);
//            if (checkOrderPayContract.getIs_over_time()) {
//                ResponseResultUtils.fail(response, new FinhubException(checkOrderPayContract.getError_data().getError_code(), checkOrderPayContract.getError_data().getError_msg()));
//                return;
//            }
//        }
//        logger.info("下单签名解析参数：" + signInfoContract.getSignInfo());
//        String singInfo = RsaVerifyUtil.getSignByStr(signInfoContract.getSignInfo());
//        Integer count = fuQianLaService.updateOrderStatus(signInfoContract, userId.toString());
//        JSONObject data = new JSONObject();
//        if (count == -1) {
//            //支付成功
//            data.put("needPay", 1);
//        } else if (count == -2) {
//            //部分支付＝支付失败
//            String clientVersion = "1.9.6";
//            if (VersionTool.compare(signInfoContract.getClient_version(), clientVersion) < 0 &&
//                    org.apache.commons.lang3.StringUtils.isNotEmpty(signInfoContract.getClient_version())) {
//                data.put("needPay", 2);
//                //throw new FinhubException(MessageCode.ILLEGAL_ARGUMENT, "请升级到最新版本后再进行此操作");
//            } else {
//                data.put("needPay", 3);
//            }
//        } else {
//            //未支付
//            data.put("needPay", 0);
//        }
//        data.put("signInfo", singInfo);
//        ResponseResultUtils.success(response, data);
//    }

//    @HttpService(value = "/auth/sign/map", method = RequestMethod.POST)
//    public void signMap(HttpRequest request, HttpResponse response) throws IOException {
//        if (request != null) {
//            throw new FinPayException(GlobalResponseCode.APP_NEED_UPDATE);
//        }
//        String requestBody = request.getBody();
//        logger.info("下单签名参数：" + requestBody);
//        Map<String, String> signInfo = JsonUtils.toObj(requestBody, Map.class);
//        String singInfo = RsaVerifyUtil.getSignByMap(signInfo);
//        ResponseResultUtils.success(response, singInfo);
//    }

    /**
     * 回调接口
     *
     * @param request
     * @param response
     * @throws IOException
     */
//    @HttpService(value = "/callback/v1")
//    public void callbackInfo(HttpRequest request, HttpResponse response) throws IOException {
//        String requestBody = request.getBody();
//        Object token = request.getAttribute("token");
//        logger.info("回调接口，callback=" + requestBody);
//        Map<String, Object> parameter = new HashMap<>();
//        parameter = JsonUtils.toObj(requestBody, Map.class);
//        Boolean tag = RsaVerifyUtil.verify(parameter);
//        logger.info("支付参数校验：tag=" + tag);
//        if (tag) {
//            //校验结果
//            FuQianLaPayReturnContract fuQianLaPayReturnContract = JsonUtils.toObj(requestBody, FuQianLaPayReturnContract.class);
//            Integer paySuccess = 0;
//            //记录日志
//            PersonPayRecord personPayRecord = fuQianLaService.getPersonPayRecordByOrderId(fuQianLaPayReturnContract);
//            fuQianLaService.savePersonPayCallbackLog(FuQianLaLogOperateUtil.callbackLogOperate(fuQianLaPayReturnContract, requestBody));
//
//            CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementService.selectCashierSettlementByFbOrderIdAndEmpId(personPayRecord.getFbOrderId(), personPayRecord.getEmployeeId(), OrderRootTypeEnum.ROOT_ORDER.getKey());
//            //打车与酒店兼容收银台
//            if (cashierOrderSettlement != null && (personPayRecord.getType() == OrderType.Taxi.getKey() || personPayRecord.getType() == OrderType.Hotel.getKey() || personPayRecord.getType() == 1)) {
//                //兼容收银台完成支付交易数据
//                PersonPayRecord finalPersonPayRecord = personPayRecord;
//                CompletableFuture.runAsync(() -> {
//                    cashierOrderSettlementService.fqlSuccessCashierSettlement(finalPersonPayRecord);
//                    cashierOrderSettlementService.callCashierSettlement(cashierOrderSettlement.getFbOrderId(),cashierOrderSettlement.getCashierTxnId(), finalPersonPayRecord.getEmployeeId());
//                });
//            } else {
//                //调用支付
//                JSONObject para = new JSONObject();
//                para.put("order_id", personPayRecord.getFbOrderId());
//                BigDecimal payPrice = BigDecimal.valueOf(fuQianLaPayReturnContract.getAmount()).divide(BigDecimal.valueOf(100));
//                para.put("personal_pay_price", payPrice);
//                para.put("pay_type", PersonPayType.getEnum(personPayRecord.getChannel()).getValue());
//                para.put("third_flow_id", fuQianLaPayReturnContract.getCharge_id());
//                logger.info("调用回调支付，请求参数信息：para={},personPayRecord={}", JsonUtils.toJson(para), JsonUtils.toJson(personPayRecord));
//                String orderRes = HttpclientUtils.postJSONResult(SceneFinishUrl.valueOf(Integer.valueOf(personPayRecord.getType())).getDesc(), para);
//                logger.info("调用回调支付修改返回信息：" + orderRes);
//                OrderPayFinishReturnContract orderPayFinishReturnContract = JsonUtils.toObj(orderRes, OrderPayFinishReturnContract.class);
//                paySuccess = orderPayFinishReturnContract.getData().getPay_success();
//                //回调支付状态接口
//                if (paySuccess == 0) {
//                    paySuccess = 2;//失败
//                }
//            }
//            fuQianLaService.updateOrderFinishStatus(fuQianLaPayReturnContract, paySuccess);
//            response.setResult("success");
//        } else {
//            fuQianLaService.savePersonPayCallbackLog(FuQianLaLogOperateUtil.callbackLogOperate(null, requestBody));
//            response.setResult("fail");
//        }
//    }

}
