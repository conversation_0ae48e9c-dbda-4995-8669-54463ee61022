package com.fenbeitong.fenbeipay.http.controller.bank;

import com.fenbeitong.common.utils.json.JsonUtils;
import com.fenbeitong.fenbeipay.acctdech.dto.KeepingActiveVCardDTO;
import com.fenbeitong.fenbeipay.acctdech.dto.KeepingActiveVCardResp;
import com.fenbeitong.fenbeipay.acctdech.dto.TrapStatusUpdateReqDto;
import com.fenbeitong.fenbeipay.acctdech.manager.IBankCardTrapFlowManager;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UKeepingActiveVCardService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankApplyCreditReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankApplyCreditRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.VirtualCardAccountFlowResqDTO;
import com.fenbeitong.fenbeipay.bank.base.vo.BankCardSleepDataManual;
import com.fenbeitong.fenbeipay.bank.base.vo.BankCardSleepThirdDataManual;
import com.fenbeitong.fenbeipay.acctdech.dto.BankCardTrapUpdateReqDto;
import com.fenbeitong.fenbeipay.acctdech.manager.IBankCardTrapManager;
import com.fenbeitong.fenbeipay.bank.base.vo.BankCardTrapFlowUpdate;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.ApplyCardManager;
import com.fenbeitong.fenbeipay.core.constant.personpay.EmployeeStatus;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.IDGen;
import com.fenbeitong.fenbeipay.dto.bank.BankCardCreditDistribute;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.finhub.common.utils.StringUtils;

import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * @Description:
 * @Author: liyi
 * @Date: 2022/10/31 8:17 PM
 */
@HttpService("/internal/bank/card")
public class BankCardInnerController {

    @Autowired
    private ApplyCardManager applyCardManager;

    @Autowired
    private UKeepingActiveVCardService keepingActiveVCardService;

    @Autowired
    private IBankCardTrapManager iBankCardTrapManager;

    @Autowired
    private IBankCardTrapFlowManager iBankCardTrapFlowManager;

    @Autowired
    public IBaseEmployeeExtService iBaseEmployeeExtService;

    /**
     * 平安虚拟卡 每天定时补圈存
     * @param request
     * @param response
     */
    @HttpService(value = "/timed/trap", method = RequestMethod.POST)
    public void spaTimedTrap(HttpRequest request, HttpResponse response) {
        CompletableFuture.runAsync(()->{
            applyCardManager.spaTimedTrap();
        });
        ResponseResultUtils.success(response, null);
    }

    /**
     * 平安虚拟卡圈存（个人）
     * @param request
     * @param response
     */
    @HttpService(value = "/spaTrapByEmployeeId")
    public void spaTrapByEmployeeId(HttpRequest request, HttpResponse response) {
        String employeeId = request.getParameter("employeeId");
        applyCardManager.spaTrapByEmployeeId(employeeId);
        ResponseResultUtils.success(response, null);
    }



    /**
     * 圈存流水调整
     * @param request
     * @param response
     */
    @HttpService(value = "/updateTrapFlow", method = RequestMethod.POST)
    public void updateTrapFlow(HttpRequest request, HttpResponse response) {
        BankCardTrapFlowUpdate reqDTO = request.getBodyObject(BankCardTrapFlowUpdate.class);
        iBankCardTrapFlowManager.updateTrapFlowStatus(reqDTO.getId(), reqDTO.getTrapStatus(), reqDTO.getTxnId());
        ResponseResultUtils.success(response, null);
    }


    /**
     * 虚拟卡预防休眠
     * @param request request
     * @param response response
     */
    @HttpService(value = "/prevent/sleep", method = RequestMethod.POST)
    public void preventSleep(HttpRequest request, HttpResponse response) {
        CompletableFuture.runAsync(() -> applyCardManager.preventSleep());
        ResponseResultUtils.success(response, null);
    }


    /**
     * 虚拟卡预防休眠人工维护
     * @param request request
     * @param response response
     */
    @HttpService(value = "/prevent/sleep/manual", method = RequestMethod.POST)
    public void preventSleepMannual(HttpRequest request, HttpResponse response) {
        BankCardSleepDataManual reqDTO = request.getBodyObject(BankCardSleepDataManual.class);
        boolean result = applyCardManager.updateSleepDataByManual(reqDTO);
        ResponseResultUtils.success(response, result);
    }


    /**
     * 虚拟卡预防休眠人工维护
     * @param request request
     * @param response response
     */
    @HttpService(value = "/prevent/sleep/data/init", method = RequestMethod.POST)
    public void preventSleepDataInit(HttpRequest request, HttpResponse response) {
        CompletableFuture.runAsync(() -> applyCardManager.preventSleepInitData());
        ResponseResultUtils.success(response, null);
    }


    /**
     * 虚拟卡三类户初始化
     * @param request request
     * @param response response
     */
    @HttpService(value = "/prevent/sleep/data/third/init", method = RequestMethod.POST)
    public void preventSleepDataThirdInit(HttpRequest request, HttpResponse response) {
        BankCardSleepThirdDataManual reqDTO = request.getBodyObject(BankCardSleepThirdDataManual.class);
        if (StringUtils.isAllBlank(reqDTO.getBankCardNo(),reqDTO.getThirdBankCardNo())){
            ResponseResultUtils.fail(response,new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "参数必填"));
            return;
        }
        applyCardManager.updatePreventSleepData(reqDTO.getBankCardNo(),reqDTO.getThirdBankCardNo());
        ResponseResultUtils.success(response, null);
    }


    @HttpService(value = "/prevent/sleep/keep-active-vcard", method = RequestMethod.POST)
    public void keepActive(HttpRequest request, HttpResponse response) {
        KeepingActiveVCardDTO req = new KeepingActiveVCardDTO();
        req.setBizNo(IDGen.genId("TEST"));
        req.setCardNo(request.getParameter("cardNo"));
        req.setTBankAccountNo(request.getParameter("tBankAccountNo"));
        KeepingActiveVCardResp resp = keepingActiveVCardService.keepActive(req);
        FinhubLogger.info(JsonUtils.toJsonStr(resp));
        ResponseResultUtils.success(response, resp);
    }

    @HttpService(value = "/prevent/sleep/data/bank/init")
    public void configBank(HttpRequest request, HttpResponse response) {
        String bankName = request.getParameter("bankName");
        String bankTaskStatus = request.getParameter("bankTaskStatus");
        if (StringUtils.isAllBlank(bankName,bankTaskStatus)){
            return;
        }
        applyCardManager.preventBankInit(bankName, Integer.valueOf(bankTaskStatus));
        ResponseResultUtils.success(response, null);
    }

    /**
     * 圈存记录，类型调整（补数据处理）
     * @param request
     * @param response
     */
    @HttpService(value = "/trap/update/trapType", method = RequestMethod.POST)
    public void updatetrapType(HttpRequest request, HttpResponse response) {
        BankCardTrapUpdateReqDto reqDto= request.getBodyObject(BankCardTrapUpdateReqDto.class);
        iBankCardTrapManager.updateTrapType(reqDto.getId(), reqDto.getTrapType());
        ResponseResultUtils.success(response);
    }

    /**
     * 圈存更改记录状态
     * @param request
     * @param response
     */
    @HttpService(value = "/trap/update/trapStatus", method = RequestMethod.POST)
    public void updatetrapStatus(HttpRequest request, HttpResponse response) {
        TrapStatusUpdateReqDto reqDto= request.getBodyObject(TrapStatusUpdateReqDto.class);
        iBankCardTrapManager.updateTrapStatus(reqDto.getId(), reqDto.getTrapStatus(), reqDto.getFailReason());
        ResponseResultUtils.success(response);
    }



    /**
     * 直接发额度
     * @param request
     * @param response
     */
    @HttpService(value = "/distribute/apply", method = RequestMethod.POST)
    public void distributeApply(HttpRequest request, HttpResponse response) {
        BankUserCardApplyDTO reqDto= request.getBodyObject(BankUserCardApplyDTO.class);

        BankApplyCreditReqDTO applyCreditReqDTO = new BankApplyCreditReqDTO();
        applyCreditReqDTO.setCompanyId(reqDto.getCompanyId());
        applyCreditReqDTO.setEmployeeId(reqDto.getEmployeeId());
        applyCreditReqDTO.setBankName(reqDto.getBankName());
        applyCreditReqDTO.setBankAccountNo(reqDto.getBankAccountNo());
        applyCreditReqDTO.setOperationAmount(reqDto.getOperationAmount());
        applyCreditReqDTO.setOperationDescription(reqDto.getOperationDescription());
        applyCreditReqDTO.setBizNo(reqDto.getBizNo());
        applyCreditReqDTO.setOperationUserId(reqDto.getEmployeeId());
        applyCreditReqDTO.setOperationChannel(OperationChannelType.NORMAL.getKey());

        EmployeeContract employeeContract = getEmployeeContract(applyCreditReqDTO.getEmployeeId(), applyCreditReqDTO.getCompanyId());
        applyCreditReqDTO.setOperationUserName(employeeContract.getName());

        BankCardCreditDistribute distributeOrder = new BankCardCreditDistribute();
        distributeOrder.setDistributeAmount(reqDto.getOperationAmount());
        distributeOrder.setBizNo(reqDto.getBizNo());
        distributeOrder.setBankName(reqDto.getBankName());
        distributeOrder.setDistributeOrderNo(reqDto.getBizNo());
        //虚拟卡冻结池/账户信息流操作
        BankApplyCreditRespDTO result = applyCardManager.applyCredit(applyCreditReqDTO, distributeOrder, new Date(), employeeContract);

        ResponseResultUtils.success(response,result);
    }

    /**
     * 直接发额度
     * @param request
     * @param response
     */
    @HttpService(value = "/distribute/apply/V1", method = RequestMethod.POST)
    public void distributeApplyV1(HttpRequest request, HttpResponse response) {
        BankUserCardApplyDTO reqDto= request.getBodyObject(BankUserCardApplyDTO.class);

        BankApplyCreditReqDTO applyCreditReqDTO = new BankApplyCreditReqDTO();
        applyCreditReqDTO.setCompanyId(reqDto.getCompanyId());
        applyCreditReqDTO.setRemark(reqDto.getOperationDescription());
        applyCreditReqDTO.setEmployeeId(reqDto.getEmployeeId());
        applyCreditReqDTO.setBankName(reqDto.getBankName());
        applyCreditReqDTO.setBankAccountNo(reqDto.getBankAccountNo());
        applyCreditReqDTO.setOperationAmount(reqDto.getOperationAmount());
        applyCreditReqDTO.setOperationDescription(reqDto.getOperationDescription());
        applyCreditReqDTO.setBizNo(reqDto.getBizNo());
        applyCreditReqDTO.setOperationUserId(reqDto.getEmployeeId());
        applyCreditReqDTO.setOperationChannel(OperationChannelType.NORMAL.getKey());

        EmployeeContract employeeContract = null;
        try {
            employeeContract = getUcEmployeeContract(applyCreditReqDTO.getCompanyId(), applyCreditReqDTO.getEmployeeId());
        } catch (Exception e) {
            FinhubLogger.info("获取UC用户信息异常:公司{}={}", applyCreditReqDTO.getCompanyId(), applyCreditReqDTO.getEmployeeId());
        }
        if (ObjUtils.isNotEmpty(employeeContract)) {
            applyCreditReqDTO.setOperationUserName(employeeContract.getName());
        } else {
            // uc查不到的 初始化个空对象 从入参获取一些基础信息 避免后面空指针
            employeeContract = new EmployeeContract();
            employeeContract.setName(reqDto.getOperationUserName());
            employeeContract.setEmployee_id(reqDto.getEmployeeId());
            employeeContract.setCompany_id(reqDto.getCompanyId());
            applyCreditReqDTO.setOperationUserName(reqDto.getOperationUserName());
        }

        BankCardCreditDistribute distributeOrder = new BankCardCreditDistribute();
        distributeOrder.setDistributeAmount(reqDto.getOperationAmount());
        distributeOrder.setBizNo(reqDto.getBizNo());
        distributeOrder.setBankName(reqDto.getBankName());
        distributeOrder.setDistributeOrderNo(reqDto.getBizNo());
        //虚拟卡冻结池/账户信息流操作
        VirtualCardAccountFlowResqDTO result = applyCardManager.applyCreditV1(applyCreditReqDTO, distributeOrder, new Date(), employeeContract);

        ResponseResultUtils.success(response,result);
    }

    /**
     * @Description: 根据员工id和公司获得启动的员工
     * @Param: [employeeId, companyId]
     * @return: EmployeeContract
     */
    private EmployeeContract getEmployeeContract(String employeeId, String companyId) {
        //用户信息
        EmployeeContract employeeContract = getUcEmployeeContract(companyId, employeeId);
        if (ObjUtils.isEmpty(employeeContract) || ObjUtils.isEmpty(employeeContract.getCompany_id()) ||
                !EmployeeStatus.isEnable(employeeContract.getStatus())) {
            throw new FinPayException(GlobalResponseCode.BANK_CARD_ACCOUNT_INFO_ERROR);
        }
        return employeeContract;
    }

    /**
     * @Description: 掉UC接口
     * @Param: [companyId, employeeId]
     * @return: EmployeeContract
     */
    private EmployeeContract getUcEmployeeContract(String companyId, String employeeId) {
        try {
            return iBaseEmployeeExtService.queryEmployeeInfo(employeeId,
                    companyId);
        } catch (Exception e) {
            FinhubLogger.info("获取UC用户信息异常:公司{}={}", companyId, employeeId);
            throw new FinPayException(GlobalResponseCode.EXCEPTION);
        }
    }
}
