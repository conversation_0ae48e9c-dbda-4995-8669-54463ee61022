package com.fenbeitong.fenbeipay.http;

import com.fenbeitong.eventbus.util.EventBusBoot;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.server.HttpServer;

public class HttpBootstrap {

    public static void main(String[] args) {
        new EventBusBoot().useKafkaProducer("/props/producer.properties");
        //支付中并不接受EVENT消息
        //new EventBusBoot().useKafkaConsumer("/props/consumer.properties");
        int port = 9081;
        if (args != null && args.length > 0) {
            port = ObjUtils.toInteger(args[0], port);
        }
        new HttpServer(port).start();
    }

}
