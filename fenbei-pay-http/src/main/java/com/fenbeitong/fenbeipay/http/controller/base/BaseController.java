package com.fenbeitong.fenbeipay.http.controller.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserLoginVo;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.entity.SwiftHashMap;
import com.luastar.swift.http.server.HttpRequest;
import io.netty.handler.codec.http.HttpMethod;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR> wh
 * @date : 2019-11-09 14:12
 */
public class BaseController {

    /**
     * 获取用户登陆
     * @param request
     * @return
     */
    protected UserLoginVo getUserLogin(HttpRequest request) {
        UserLoginVo user = (UserLoginVo)request.getAttribute(PayConstant.REQ_USER);
        if(user == null) {
            throw new FinPayException(GlobalResponseCode.RemoteService401);
        }
        return user;
    }

    /**
     * 获取用户Token
     * @param request
     * @return
     */
    protected String getToken(HttpRequest request) {
        return request.getHeader(PayConstant.KEY_NAME_TOKEN);
    }

    /**
     * 获取客户端版本号
     * @param request
     * @return
     */
    protected String getAppVersion(HttpRequest request) {
        return request.getHeader(PayConstant.CLIENT_VERSION);
    }

    /**
     * 获取客户端类型
     * @param request
     * @return
     */
    protected String getClientType(HttpRequest request) {
        String clientType =  request.getHeader(PayConstant.CLIENT_TYPE);
        if (StringUtils.isBlank(clientType)) {
            clientType = Objects.nonNull(request.getBodyMap()) ? request.getBodyMap().getString("client_type") : "";
        }
        return clientType;
    }

    /**
     * 获取用户信息
     * @param request
     * @return
     */
    protected UserInfoVO getUserInfo(HttpRequest request) {
        UserLoginVo userLogin = getUserLogin(request);
        if(userLogin.getUser_info() == null) {
            throw new FinPayException(GlobalResponseCode.RemoteService401);
        }
        return userLogin.getUser_info();
    }
    
    /**
     * 获取用户ID
     * @param request
     * @return
     */
    protected String getUserId(HttpRequest request) {
        return getUserInfo(request).getId();
    }

    /**
     * 获取用户企业ID
     * @param request
     * @return
     */
    protected String getUserCompanyId(HttpRequest request) {
        CompanyInfoVO userCompanyInfo = getUserCompanyInfo(request);
        if(userCompanyInfo != null) {
            return userCompanyInfo.getId();
        }
        return null;
    }

    /**
     * 获取用户企业信息
     * @param request
     * @return
     */
    protected CompanyInfoVO getUserCompanyInfo(HttpRequest request) {
        UserLoginVo userLogin = getUserLogin(request);
        return userLogin.getCompany_info();
    }

    /**
     * 获取员工端/管理端区分标记
     * @param request
     * @return
     */
    protected Integer getBetweenType(HttpRequest request) {
        String betweenType = request.getHeader(PayConstant.BETWEEN_TYPE);
        FinhubLogger.info("【获取员工端管理端区分标记】betweenType={}", betweenType);

        //1:兼容以前逻辑,默认是员工端(流水列表暂无员工端),
        if(StringUtils.isBlank(betweenType)){
            return PayConstant.BETWEEN_TYPE_EMPLOYEE;
        }

        //2：校验
        if(!Objects.equals(PayConstant.BETWEEN_TYPE_EMPLOYEE + StringUtils.EMPTY,betweenType)
                && !Objects.equals(PayConstant.BETWEEN_TYPE_MANAGE + StringUtils.EMPTY,betweenType)){
            throw new FinPayException(GlobalResponseCode.BETWEEN_TYPE_PARAM_ERROR);
        }
        return new Integer(betweenType);
    }

    /**
     * 获取需要操作的公司id
     * <AUTHOR>
     * @date 2022-07-05 18:47:01
     */
    protected String getOperationCompanyId(HttpRequest request) {
        UserLoginVo userLogin = getUserLogin(request);
        // 集团版标识
        if (userLogin.getUser_info().isGroupLogin()) {
            if (HttpMethod.GET.name().equalsIgnoreCase(request.getMethod())) {
                return request.getParameter(CoreConstant.ACCT_GROUP_COMPANY_ID);
            }
            if (HttpMethod.POST.name().equalsIgnoreCase(request.getMethod())) {
                SwiftHashMap<String, Object> bodyMap = request.getBodyMap();
                if (bodyMap == null) {
                    return null;
                }
                return bodyMap.getString(CoreConstant.ACCT_GROUP_COMPANY_ID);
            }
        }

        // 非集团版
        CompanyInfoVO userCompanyInfo = userLogin.getCompany_info();
        if(userCompanyInfo != null) {
            return userCompanyInfo.getId();
        }
        return null;
    }

    protected String verifyCompanyId(HttpRequest request){
        CompanyInfoVO userCompanyInfo = getUserCompanyInfo(request);
        if(userCompanyInfo == null || StringUtils.isBlank(userCompanyInfo.getId())){
            throw new FinPayException(GlobalResponseCode.RemoteService401);
        }
        return userCompanyInfo.getId();
    }

    /**
     * @Description: header中的token与body中的companyid进行比对，JSON请求时校验
     * @Author: guogx
     * @Date: 2022/8/18 下午2:29
     */
    protected void verifyIdentity(HttpRequest request){
        String companyIdOfToken = verifyCompanyId(request);
        String requestJsonStr = request.getBody();
        JSONObject requestJsonObj = JSON.parseObject(requestJsonStr);
        String companyIdOfParam = requestJsonObj.getString("companyId");
        if(isCommonAcct(request) && !companyIdOfToken.equals(companyIdOfParam)){
            throw new FinPayException(GlobalResponseCode.TOKEN_EXPIRE);
        }
    }

    /**
     * @Description: 集团版
     * @Author: guogx
     * @Date: 2022/8/9 下午4:37
     */
    protected boolean isGroupAcct(HttpRequest request){
        String menuCode = request.getHeader(CoreConstant.ACCT_GROUP_MENU_CODE);
        return StringUtils.isNotBlank(menuCode) && menuCode.startsWith(CoreConstant.ACCT_GROUP_MENU_CODE_SUFFIX);
    }

    /**
     * @Description: 通用版，非集团版
     * @Author: guogx
     * @Date: 2022/8/9 下午4:37
     */
    protected boolean isCommonAcct(HttpRequest request){
        return !isGroupAcct(request);
    }
}
