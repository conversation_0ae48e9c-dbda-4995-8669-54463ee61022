package com.fenbeitong.fenbeipay.http.controller.newaccount;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessDebitFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctIndividualCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctIndividualDebitFlowService;
import com.fenbeitong.fenbeipay.acctdech.unit.service.*;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.AcctFlowReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwBySubAndBankReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountInstructionRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.na.*;
import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportDTO;
import com.fenbeitong.fenbeipay.core.common.base.export.ExportQuery;
import com.fenbeitong.fenbeipay.core.enums.account.AccountFlowTitleType;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.employee.EmployeeService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessCredit;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessDebit;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctIndividualCredit;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctIndividualDebit;
import com.fenbeitong.fenbeipay.dto.na.AccountSub;
import com.fenbeitong.fenbeipay.http.controller.newaccount.dto.AccountInstructionReqDTO;
import com.fenbeitong.fenbeipay.http.controller.newaccount.dto.AccountSubFlowReqDTO;
import com.fenbeitong.fenbeipay.http.controller.vouchers.BaseController;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeSimpleInfoContract;
import com.google.common.collect.Lists;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.fenbeitong.fenbeipay.core.constant.core.CoreConstant.FBT;


/**
 * @Description: java类作用描述
 * @ClassName: AccountSubController
 * @Author: zhangga
 * @CreateDate: 2019/3/18 5:26 PM
 * @UpdateUser:
 * @UpdateDate: 2019/3/18 5:26 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@HttpService("/fbp/na/account_sub")
public class AccountSubController extends BaseController {

    private static final String ACCOUNT_SUB_TYPE = "{accountSubType}";


    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;

    @Autowired
    private UAcctBusinessCreditService uAcctBusinessCreditService;

    @Autowired
    private UAcctBusinessDebitService uAcctBusinessDebitService;

    @Autowired
    private UAcctIndividualCreditService uAcctIndividualCreditService;

    @Autowired
    private UAcctIndividualDebitService uAcctIndividualDebitService;

    @Autowired
    private AcctCompanyGatewayService acctCompanyGatewayService;

    @Autowired
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;

    @Autowired
    private AcctBusinessDebitFlowService acctBusinessDebitFlowService;

    @Autowired
    private AcctIndividualCreditFlowService acctIndividualCreditFlowService;

    @Autowired
    private AcctIndividualDebitFlowService acctIndividualDebitFlowService;

    @Value("${export.task.host}")
    private String exportTaskHost;
    @Value("${export.query.host}")
    private String exportQueryHost;


    @HttpService(value = "/{accountSubType}/info", method = RequestMethod.GET)
    public void accountSubInfo(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        String value = request.getPathValue("accountSubType");
        if (ObjUtils.isEmpty(value) || ACCOUNT_SUB_TYPE.equals(value)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        int accountSubType = Integer.parseInt(value);
        AcctComGwBySubAndBankReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwBySubAndBankReqDTO();
        acctComGwBySubAndBankReqDTO.setBankAccountNo(companyId);
        acctComGwBySubAndBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
        acctComGwBySubAndBankReqDTO.setCompanyId(companyId);
        acctComGwBySubAndBankReqDTO.setFundAccountType(accountSubType);
        List<AcctCommonBaseDTO> accountSubs = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
        if (CollectionUtils.isEmpty(accountSubs)) {
            throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
        }

        AcctCommonBaseDTO acctCommonBaseDTO = accountSubs.get(0);
        AccountSubInfoVO accountSubInfoVO = new AccountSubInfoVO();
        List<AccountFlowTitleVO> titleVOS = extracted(accountSubType, acctCommonBaseDTO);
        if (FundAccountSubType.isBusinessAccount(accountSubType) &&
                FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())) {
            List<AccountFlowVO> voList = acctBusinessDebitFlowService.getByCountIdPageBeanVO(acctCommonBaseDTO.getAccountId(), new PageBean(1, 6));
            if (CollectionUtils.isNotEmpty(voList)) {
                List<AccountSubFlowVO> targetSubFlowVo = new ArrayList<>(voList.size());
                voList.stream().forEach(e -> {
                    AccountSubFlowVO subFlowVO = new AccountSubFlowVO();
                    BeanUtils.copyProperties(e, subFlowVO);
                    targetSubFlowVo.add(subFlowVO);
                });
                accountSubInfoVO.setAccountSubFlowList(targetSubFlowVo);
                BeanUtils.copyProperties(acctCommonBaseDTO, accountSubInfoVO);
                accountSubInfoVO.setFlowTitleList(titleVOS);
                AccountRedcouponInfoVO redcouponInfoVO = accountRedcouponSearchService.queryAccountCouponInfoVO(companyId);
                if (redcouponInfoVO != null) {
                    accountSubInfoVO.setAccountRedcouponInfo(redcouponInfoVO);
                    accountSubInfoVO.setHasRedcoupon(true);
                }
            }
        } else if (FundAccountSubType.isBusinessAccount(accountSubType) &&
                FundAccountModelType.isCredit(acctCommonBaseDTO.getAccountModel())) {
            List<AccountFlowVO> voList = acctBusinessCreditFlowService.getByCountIdPageBeanVO(acctCommonBaseDTO.getAccountId(), new PageBean(1, 6));
            if (CollectionUtils.isNotEmpty(voList)) {
                List<AccountSubFlowVO> targetSubFlowVo = new ArrayList<>(voList.size());
                voList.stream().forEach(e -> {
                    AccountSubFlowVO subFlowVO = new AccountSubFlowVO();
                    BeanUtils.copyProperties(e, subFlowVO);
                    targetSubFlowVo.add(subFlowVO);
                });
                accountSubInfoVO.setAccountSubFlowList(targetSubFlowVo);
                BeanUtils.copyProperties(acctCommonBaseDTO, accountSubInfoVO);
                accountSubInfoVO.setFlowTitleList(titleVOS);
                AccountRedcouponInfoVO redcouponInfoVO = accountRedcouponSearchService.queryAccountCouponInfoVO(companyId);
                if (redcouponInfoVO != null) {
                    accountSubInfoVO.setAccountRedcouponInfo(redcouponInfoVO);
                    accountSubInfoVO.setHasRedcoupon(true);
                }
            }
        } else if (FundAccountSubType.isIndividualAccount(accountSubType) &&
                FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())) {
            List<AccountFlowVO> voList = acctIndividualDebitFlowService.getByCountIdPageBeanVO(acctCommonBaseDTO.getAccountId(), new PageBean(1, 6));
            if (CollectionUtils.isNotEmpty(voList)) {
                List<AccountSubFlowVO> targetSubFlowVo = new ArrayList<>(voList.size());
                voList.stream().forEach(e -> {
                    AccountSubFlowVO subFlowVO = new AccountSubFlowVO();
                    BeanUtils.copyProperties(e, subFlowVO);
                    targetSubFlowVo.add(subFlowVO);
                });
                accountSubInfoVO.setAccountSubFlowList(targetSubFlowVo);
                BeanUtils.copyProperties(acctCommonBaseDTO, accountSubInfoVO);
                accountSubInfoVO.setFlowTitleList(titleVOS);
            }
        } else if (FundAccountSubType.isIndividualAccount(accountSubType) &&
                FundAccountModelType.isCredit(acctCommonBaseDTO.getAccountModel())) {
            List<AccountFlowVO> voList = acctIndividualCreditFlowService.getByCountIdPageBeanVO(acctCommonBaseDTO.getAccountId(), new PageBean(1, 6));
            if (CollectionUtils.isNotEmpty(voList)) {
                List<AccountSubFlowVO> targetSubFlowVo = new ArrayList<>(voList.size());
                voList.stream().forEach(e -> {
                    AccountSubFlowVO subFlowVO = new AccountSubFlowVO();
                    BeanUtils.copyProperties(e, subFlowVO);
                    targetSubFlowVo.add(subFlowVO);
                });
                accountSubInfoVO.setAccountSubFlowList(targetSubFlowVo);
                BeanUtils.copyProperties(acctCommonBaseDTO, accountSubInfoVO);
                accountSubInfoVO.setFlowTitleList(titleVOS);
            }
        }
        ResponseResultUtils.success(response, accountSubInfoVO);
    }

    private List<AccountFlowTitleVO> extracted(int accountSubType, AcctCommonBaseDTO acctCommonBaseDTO) {
        List<AccountFlowTitleVO> titleTypes = new ArrayList<>();
        AccountFlowTitleType type = AccountFlowTitleType.ofType(accountSubType, acctCommonBaseDTO.getAccountModel());
        AccountFlowTitleVO vo = new AccountFlowTitleVO();
        vo.setAccountSubType(type.getAccountSubType());
        vo.setAccountModel(type.getAccountModel());
        vo.setTitleKey(type.getKey());
        vo.setTitleDesc(type.getValue());
        titleTypes.add(vo);
        return titleTypes;
    }

    @HttpService(value = "/{accountSubType}/balance", method = RequestMethod.GET)
    public void queryBalance(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String value = request.getPathValue("accountSubType");
        Integer accountModel = request.getIntParameter("account_model");
        if (ObjUtils.isEmpty(value) || ACCOUNT_SUB_TYPE.equals(value)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        int accountSubType = Integer.parseInt(value);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        BigDecimal balance = BigDecimal.ZERO;
        AccountSub accountSub = null;
        if (ObjUtils.isNotEmpty(accountModel)) {
//            accountSub = uAccountSubService.findByCompanyIdAndAccountSubType(companyId, accountSubType, accountModel);
            if (FundAccountSubType.isBusinessAccount(accountSubType) &&
                    FundAccountModelType.isRecharge(accountModel)) {
                AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                if (Objects.isNull(acctBusinessDebit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                balance = acctBusinessDebit.getBalance();
            } else if (FundAccountSubType.isBusinessAccount(accountSubType) &&
                    FundAccountModelType.isCredit(accountModel)) {
                AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                if (Objects.isNull(acctBusinessCredit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                balance = acctBusinessCredit.getBalance();
            } else if (FundAccountSubType.isIndividualAccount(accountSubType) &&
                    FundAccountModelType.isRecharge(accountModel)) {
                AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                if (Objects.isNull(acctIndividualDebit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                balance = acctIndividualDebit.getBalance();
            } else if (FundAccountSubType.isIndividualAccount(accountSubType) &&
                    FundAccountModelType.isCredit(accountModel)) {
                AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                if (Objects.isNull(acctIndividualCredit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                balance = acctIndividualCredit.getBalance();
            }
        } else {
            AcctComGwBySubAndBankReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwBySubAndBankReqDTO();
            acctComGwBySubAndBankReqDTO.setBankAccountNo(companyId);
            acctComGwBySubAndBankReqDTO.setCompanyId(companyId);
            acctComGwBySubAndBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
            acctComGwBySubAndBankReqDTO.setFundAccountType(accountSubType);
            List<AcctCommonBaseDTO> actCommonBySubAndBank = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
            if (CollectionUtils.isEmpty(actCommonBySubAndBank)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
            }
            balance = actCommonBySubAndBank.get(0).getBalance();
        }
        ResponseResultUtils.success(response, balance);
    }


    @HttpService(value = "/{accountSubType}/balance/min", method = RequestMethod.GET)
    public void queryMinBalance(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String value = request.getPathValue("accountSubType");
        Integer accountModel = request.getIntParameter("account_model");
        if (ObjUtils.isEmpty(value) || ACCOUNT_SUB_TYPE.equals(value)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        int accountSubType = Integer.parseInt(value);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        BigDecimal balance = BigDecimal.ZERO;
        if (ObjUtils.isNotEmpty(accountModel)) {
            if (FundAccountSubType.isBusinessAccount(accountSubType) &&
                    FundAccountModelType.isRecharge(accountModel)) {
                AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                if (Objects.isNull(acctBusinessDebit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                balance = acctBusinessDebit.getBalance();
            } else if (FundAccountSubType.isBusinessAccount(accountSubType) &&
                    FundAccountModelType.isCredit(accountModel)) {
                AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                if (Objects.isNull(acctBusinessCredit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                balance = acctBusinessCredit.getBalance().compareTo(acctBusinessCredit.getInitCredit()) > 0 ? acctBusinessCredit.getInitCredit() : acctBusinessCredit.getBalance();
            } else if (FundAccountSubType.isIndividualAccount(accountSubType) &&
                    FundAccountModelType.isRecharge(accountModel)) {
                AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                if (Objects.isNull(acctIndividualDebit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                balance = acctIndividualDebit.getBalance();
            } else if (FundAccountSubType.isIndividualAccount(accountSubType) &&
                    FundAccountModelType.isCredit(accountModel)) {
                AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
                if (Objects.isNull(acctIndividualCredit)) {
                    throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
                }
                balance = acctIndividualCredit.getBalance().compareTo(acctIndividualCredit.getInitCredit()) > 0 ? acctIndividualCredit.getInitCredit() : acctIndividualCredit.getBalance();
            }
        } else {
            AcctComGwBySubAndBankReqDTO acctComGwBySubAndBankReqDTO = new AcctComGwBySubAndBankReqDTO();
            acctComGwBySubAndBankReqDTO.setBankAccountNo(companyId);
            acctComGwBySubAndBankReqDTO.setCompanyId(companyId);
            acctComGwBySubAndBankReqDTO.setBankName(BankNameEnum.FBT.getCode());
            acctComGwBySubAndBankReqDTO.setFundAccountType(accountSubType);
            List<AcctCommonBaseDTO> actCommonBySubAndBank = acctCompanyGatewayService.findActCommonBySubAndBank(acctComGwBySubAndBankReqDTO);
            if (CollectionUtils.isEmpty(actCommonBySubAndBank)) {
                throw new FinPayException(GlobalResponseCode.ACCOUNT_SUB_NOT_EXIST);
            }
            AcctCommonBaseDTO acctCommonBaseDTO = actCommonBySubAndBank.get(0);
            if (FundAccountModelType.isRecharge(acctCommonBaseDTO.getAccountModel())) {
                balance = acctCommonBaseDTO.getBalance();
            } else {
                //授信模式，取最小金额
                balance = acctCommonBaseDTO.getBalance().compareTo(acctCommonBaseDTO.getInitCredit()) > 0 ? acctCommonBaseDTO.getInitCredit() : acctCommonBaseDTO.getBalance();
            }
        }
        ResponseResultUtils.success(response, balance);
    }


    @HttpService(value = "/{accountSubType}/flow", method = RequestMethod.POST)
    public void queryFlow(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        AccountSubFlowReqDTO dto = request.getBodyObject(AccountSubFlowReqDTO.class);
        PageBean pageBean = new PageBean();
        AcctFlowReqDTO requestParam = new AcctFlowReqDTO();
        pageBean.setPageNo(dto.getPageNo());
        pageBean.setPageSize(dto.getPageSize());
        BeanUtils.copyProperties(dto, requestParam);
        requestParam.setCompanyId(companyId);
        if (FBT.equals(requestParam.getOperationUserName())) {
            requestParam.setOperationChannelType(OperationChannelType.STEREO.getKey());
            requestParam.setOperationUserName(null);
        }
        ResponsePage<AccountFlowVO> operationFlows = new ResponsePage<>();
        if (ObjUtils.isNotEmpty(requestParam.getAccountModel())) {
            if (FundAccountSubType.isBusinessAccount(requestParam.getAccountSubType()) &&
                    FundAccountModelType.isRecharge(requestParam.getAccountModel())) {
                operationFlows = acctBusinessDebitFlowService.queryPageVo(requestParam, pageBean);
                if(CollectionUtils.isNotEmpty(operationFlows.getDataList())){
                    operationFlows.getDataList().stream().forEach(e->{
                        if(!(Objects.nonNull(e.getOperationType()) && (e.getOperationType() == 41 || e.getOperationType() == 42))) {
                            e.setBizNo("");
                        }
                    });
                }
            } else if (FundAccountSubType.isBusinessAccount(requestParam.getAccountSubType()) &&
                    FundAccountModelType.isCredit(requestParam.getAccountModel())) {
                operationFlows = acctBusinessCreditFlowService.queryPageVo(requestParam, pageBean);
                if(CollectionUtils.isNotEmpty(operationFlows.getDataList())){
                    operationFlows.getDataList().stream().forEach(e->{
                        if(!(Objects.nonNull(e.getOperationType()) && (e.getOperationType() == 41 || e.getOperationType() == 42))) {
                            e.setBizNo("");
                        }
                    });
                }
            } else if (FundAccountSubType.isIndividualAccount(requestParam.getAccountSubType()) &&
                    FundAccountModelType.isRecharge(requestParam.getAccountModel())) {
                operationFlows = acctIndividualDebitFlowService.queryPageVo(requestParam, pageBean);
                if(CollectionUtils.isNotEmpty(operationFlows.getDataList())){
                    operationFlows.getDataList().stream().forEach(e->{
                        e.setBizNo("");
                    });
                }
            } else if (FundAccountSubType.isIndividualAccount(requestParam.getAccountSubType()) &&
                    FundAccountModelType.isCredit(requestParam.getAccountModel())) {
                operationFlows = acctIndividualCreditFlowService.queryPageVo(requestParam, pageBean);
                if(CollectionUtils.isNotEmpty(operationFlows.getDataList())){
                    operationFlows.getDataList().stream().forEach(e->{
                        e.setBizNo("");
                    });
                }
            }
        } else {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if(CollectionUtils.isNotEmpty(operationFlows.getDataList())){
            operationFlows.getDataList().stream().forEach(e->{
                if(Objects.nonNull(e.getOrderType())){
                    CategoryTypeEnum categoryTypeEnum = CategoryTypeEnum.valueOf(e.getOrderType());
                    e.setOrderTypeName(Objects.nonNull(categoryTypeEnum)?categoryTypeEnum.getName():"");
                }
            });
        }
        ResponseResultUtils.success(response, operationFlows);
    }

    @HttpService(value = "/flow/export", method = RequestMethod.POST)
    public void exportBusinessFlow(HttpRequest request, HttpResponse response) {
        checkPrivilege(request);
        AccountSubFlowReqDTO dto = request.getBodyObject(AccountSubFlowReqDTO.class);
        String companyId = request.getAttribute(COMPANY_ID).toString();
        String operationUserId = request.getAttribute(USER_ID).toString();
        String operationUserName = request.getAttribute(USER_COMPANY_NAME).toString();
        ExportQuery exportQuery = dto.getExportQuery();
        if (exportQuery == null) {
            FinhubLogger.error("【导出报表】参数错误，exportQuery：{}", exportQuery.toString());
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        ExportDTO exportDTO = new ExportDTO();
        String exportQueryUrl = exportQueryHost + "/internal/new/account/export/sub/flow";
        List<String> list = new ArrayList<>();
        EmployeeSimpleInfoContract employeeSimpleInfo = employeeService.getEmployeeSimpleInfo(operationUserId, companyId);
        if (null != employeeSimpleInfo && ObjUtils.isNotEmpty(employeeSimpleInfo.getOrg_unit_list())) {
            employeeSimpleInfo.getOrg_unit_list().forEach(orgUnitListBean -> {
                list.add(orgUnitListBean.getId());
            });
        }
        String parentIds = StringUtils.join(list, ",");
        JSONObject query = dto.getQuery(companyId);
        String taskName = FundAccountSubType.getEnum(dto.getAccountSubType()).getValue() + "流水导出_" + DateUtils.format(new Date(), "yyyyMMdd_HHmmss");
        exportDTO.dtoBuild(query, exportQueryUrl, taskName, operationUserId, operationUserName, parentIds, exportDTO.getAccountColumns(), exportQuery);
        String jsonString = JSONObject.toJSONString(exportDTO);
        String postBody = HttpClientUtils.postBody(exportTaskHost + "/internal/export/task", jsonString);
        FinhubLogger.info("【导出报表】返回参数：{}", postBody);
        Object taskId = JSONObject.parseObject(postBody).getJSONObject("data").get("taskId");
        ResponseResultUtils.success(response, taskId);
    }

    @HttpService(value = "/account/instruction", method = RequestMethod.POST)
    public void getAccountInstruction(HttpRequest request, HttpResponse response) {

        AccountInstructionReqDTO reqDTO = request.getBodyObject(AccountInstructionReqDTO.class);
        if (ObjUtils.isNull(reqDTO.getAccountType()) || ObjUtils.isNull(reqDTO.getAccountModel())) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountInstructionRespDTO respDTO = null;
        if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountType()) &&
                FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(reqDTO.getCompanyId(), BankNameEnum.FBT.getCode(), reqDTO.getCompanyId());
            if (ObjUtils.isNull(acctBusinessDebit) || FundAcctActStatusEnum.UN_ACTIVATE.getStatus() == acctBusinessDebit.getActiveStatus()) {
                respDTO = null;
            }else {
                respDTO = acctBusinessDebitFlowService.getAccountInstruction(acctBusinessDebit.getAccountId());
            }
        } else if (FundAccountSubType.isBusinessAccount(reqDTO.getAccountType()) &&
                FundAccountModelType.isCredit(reqDTO.getAccountModel())) {
            AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(reqDTO.getCompanyId(), BankNameEnum.FBT.getCode(), reqDTO.getCompanyId());
            if (ObjUtils.isNull(acctBusinessCredit) || FundAcctActStatusEnum.UN_ACTIVATE.getStatus() == acctBusinessCredit.getActiveStatus()) {
                respDTO = null;
            }else {
                respDTO = acctBusinessCreditFlowService.getAccountInstruction(acctBusinessCredit.getAccountId());
            }
        } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountType()) &&
                FundAccountModelType.isRecharge(reqDTO.getAccountModel())) {
            AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(reqDTO.getCompanyId(), BankNameEnum.FBT.getCode(), reqDTO.getCompanyId());
            if (ObjUtils.isNull(acctIndividualDebit) || FundAcctActStatusEnum.UN_ACTIVATE.getStatus() == acctIndividualDebit.getActiveStatus()) {
                respDTO = null;
            }else {
                respDTO = acctIndividualDebitFlowService.getAccountInstruction(acctIndividualDebit.getAccountId());
            }
        } else if (FundAccountSubType.isIndividualAccount(reqDTO.getAccountType()) &&
                FundAccountModelType.isCredit(reqDTO.getAccountModel())) {
            AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(reqDTO.getCompanyId(), BankNameEnum.FBT.getCode(), reqDTO.getCompanyId());
            if (ObjUtils.isNull(acctIndividualCredit) || FundAcctActStatusEnum.UN_ACTIVATE.getStatus() == acctIndividualCredit.getActiveStatus()) {
                respDTO = null;
            }else {
                respDTO = acctIndividualCreditFlowService.getAccountInstruction(acctIndividualCredit.getAccountId());
            }
        }
        ResponseResultUtils.success(response, respDTO);

    }

    @HttpService(value = "/flowTitle", method = RequestMethod.GET)
    public void getAccountFlowTitle(HttpRequest request, HttpResponse response) {
        String companyId = request.getAttribute(COMPANY_ID).toString();
        List<AccountFlowTitleVO> titleVOS = Lists.newArrayList();
        AcctBusinessDebit acctBusinessDebit = uAcctBusinessDebitService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
        if (Objects.nonNull(acctBusinessDebit)) {
            List<AccountFlowTitleVO> accountFlowTitleByAccountModel = acctBusinessDebitFlowService.getAccountFlowTitleByAccountModel(acctBusinessDebit.getAccountId(), FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            if (CollectionUtils.isNotEmpty(accountFlowTitleByAccountModel)) {
                titleVOS.addAll(accountFlowTitleByAccountModel);
            }
        }

        AcctBusinessCredit acctBusinessCredit = uAcctBusinessCreditService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
        if (Objects.nonNull(acctBusinessCredit)) {
            List<AccountFlowTitleVO> accountFlowTitleByAccountModel = acctBusinessCreditFlowService.getAccountFlowTitleByAccountModel(acctBusinessCredit.getAccountId(), FundAccountSubType.BUSINESS_ACCOUNT.getKey());
            if (CollectionUtils.isNotEmpty(accountFlowTitleByAccountModel)) {
                titleVOS.addAll(accountFlowTitleByAccountModel);
            }
        }
        AcctIndividualCredit acctIndividualCredit = uAcctIndividualCreditService.findByCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
        if (Objects.nonNull(acctIndividualCredit)) {
            List<AccountFlowTitleVO> accountFlowTitleByAccountModel = acctIndividualCreditFlowService.getAccountFlowTitleByAccountModel(acctIndividualCredit.getAccountId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            if (CollectionUtils.isNotEmpty(accountFlowTitleByAccountModel)) {
                titleVOS.addAll(accountFlowTitleByAccountModel);
            }
        }
        AcctIndividualDebit acctIndividualDebit = uAcctIndividualDebitService.findCompanyIdAndBank(companyId, BankNameEnum.FBT.getCode(), companyId);
        if (Objects.nonNull(acctIndividualDebit)) {
            List<AccountFlowTitleVO> accountFlowTitleByAccountModel = acctIndividualDebitFlowService.getAccountFlowTitleByAccountModel(acctIndividualDebit.getAccountId(), FundAccountSubType.INDIVIDUAL_ACCOUNT.getKey());
            if (CollectionUtils.isNotEmpty(accountFlowTitleByAccountModel)) {
                titleVOS.addAll(accountFlowTitleByAccountModel);
            }
        }
        AccountFlowTitleVO vo = accountRedcouponSearchService.queryRedcouponFlowTitle(companyId);
        if (!ObjUtils.isNull(vo)) {
            titleVOS.add(vo);
        }
        ResponseResultUtils.success(response, titleVOS);
    }

}
