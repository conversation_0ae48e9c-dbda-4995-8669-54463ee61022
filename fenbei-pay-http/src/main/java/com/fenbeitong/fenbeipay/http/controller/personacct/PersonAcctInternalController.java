package com.fenbeitong.fenbeipay.http.controller.personacct;

import com.fenbeitong.fenbeipay.api.model.dto.personaccount.req.FbbRecallStereoReq;
import com.fenbeitong.fenbeipay.api.model.dto.personaccount.resp.FbbRecallStereoResp;
import com.fenbeitong.fenbeipay.api.service.personaccount.IPersonAccountService;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-09-05 上午10:43
 */
@HttpService("internal/person/acct")
public class PersonAcctInternalController {

    @Autowired
    private IPersonAccountService iPersonAccountService;

    /**
     * 付钱拉退款
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/recall", method = RequestMethod.POST)
    public void refund(HttpRequest request, HttpResponse response) {
        String requestBody = request.getBody();
        FbbRecallStereoReq recallStereoReq = JsonUtils.toObj(requestBody, FbbRecallStereoReq.class);
        FbbRecallStereoResp fbbRecallStereoResp = iPersonAccountService.recallFbbStereo(recallStereoReq);
        ResponseResultUtils.success(response, fbbRecallStereoResp);
    }
}
