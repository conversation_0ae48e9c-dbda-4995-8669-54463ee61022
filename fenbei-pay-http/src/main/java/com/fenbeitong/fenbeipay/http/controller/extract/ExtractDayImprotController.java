package com.fenbeitong.fenbeipay.http.controller.extract;

import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayShowPageReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.ExtractDayUpdateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.ExtractDayShowDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.ExtractDayShowPageDTO;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractDayService;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.http.controller.base.BaseController;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * 导出用
 */
@HttpService("/internal/fbp/web/extractDay")
public class ExtractDayImprotController extends BaseController {


    @Autowired
    private IAcctExtractDayService iAcctExtractDayService;

    /**
     * 日终余额列表查询（直接查数据库，有分页信息，导出用）
     * @param request
     * @param response
     */
    @HttpService(value = "/getListPage", method = RequestMethod.POST)
    public void getListPage(HttpRequest request, HttpResponse response) {
        ExtractDayShowPageReqDTO queryReq = request.getBodyObject(ExtractDayShowPageReqDTO.class);

        /**
         * 导出时，增加时间范围限制
         *
         */
        if(StringUtils.isNotBlank(queryReq.getStartTime()) && StringUtils.isNotBlank(queryReq.getEndTime())){
            Date startTimeDate = DateUtils.parseDate(queryReq.getStartTime());
            Date endTimeDate = DateUtils.parseDate(queryReq.getEndTime());
            int count = DateUtil.daysBetweenDay(startTimeDate, endTimeDate) + 1; // 总条数等于日期间隔+1
            if(count > 180 || endTimeDate.before(startTimeDate)){
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), "日终余额导出，不支持大于180天");
            }
        }

        ResponsePage<ExtractDayShowPageDTO> resp = iAcctExtractDayService.queryPage(queryReq);
        FinhubLogger.info("日切余额单日web导出，返回1{}", JsonUtils.toJson(resp));
        List<ExtractDayShowDTO> list = Lists.newArrayList();
        for (ExtractDayShowPageDTO one: resp.getDataList()) {
            ExtractDayShowDTO extractDayShowDTO = new ExtractDayShowDTO();
            BeanUtils.copyProperties(one, extractDayShowDTO);
            list.add(extractDayShowDTO);
        }
        // 导出不能返回分页信息，需要返回单个列表
        ResponseResultUtils.success(response, list);
    }

    /**
     * 虚拟卡下线 修复对账数据用
     *
     * @param request  request
     * @param response response
     */
    @HttpService(value = "/updateById", method = RequestMethod.POST)
    public void update(HttpRequest request, HttpResponse response) {
        ExtractDayUpdateReqDTO updateReq = request.getBodyObject(ExtractDayUpdateReqDTO.class);
        iAcctExtractDayService.updateById(updateReq);
        ResponseResultUtils.success(response);
    }

}
