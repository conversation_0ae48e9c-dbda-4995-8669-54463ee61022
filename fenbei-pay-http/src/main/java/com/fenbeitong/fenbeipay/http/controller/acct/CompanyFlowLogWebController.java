package com.fenbeitong.fenbeipay.http.controller.acct;

import java.util.*;

import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.finhub.common.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctBusinessDebitFlowService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.CategoryTypeMappingEnum;
import com.fenbeitong.fenbeipay.core.model.dto.AccountBillFlowReqDTO;
import com.fenbeitong.fenbeipay.core.model.dto.BillFlowPageQuery;
import com.fenbeitong.fenbeipay.core.service.bigdata.AccountBillFlowService;
import com.fenbeitong.fenbeipay.core.utils.StereoMessageCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

/**
 * web 端服务接口
 * 1. 财务 - 企业账户 - 账户流水
 */
@HttpService("/fbp/web/flow/log")
public class CompanyFlowLogWebController extends AcctAbstractController {
	
    public static final String COMPANY_ID = "companyId";
    
    public static final String BANK_NAME = "bankName";
    
    public static final String BANK_ACCOUNT_NO = "bankAccountNo";

    @Autowired
    private AccountBillFlowService accountBillFlowService;
    
    @Autowired
    private UAcctBusinessDebitFlowService acctBusinessDebitFlowService;
    
    /**
     * 财务 - 企业账户 - 账户流水 - 商务消费账户
     * 查询参数校验
     *
     * @param reqDTO
     */
    private BillFlowPageQuery checkAndBuildBillFlowSearch(AccountBillFlowReqDTO reqDTO) {
        BillFlowPageQuery query = new BillFlowPageQuery();
        query.setStereo(false);
        query.setBankName(reqDTO.getBankName());
        query.setBankAccountNo(reqDTO.getBankAccountNo());
        // 账户类型
        if (ObjUtils.isNull(reqDTO.getAccountModel())) {
            throw new FinhubException(StereoMessageCode.NO_DATA, "账户类型为空");
        }
        query.setAccountModel(reqDTO.getAccountModel());

        if (Objects.nonNull(reqDTO.getStartTime()) && Objects.nonNull(reqDTO.getEndTime())) {
            query.setStartTime(reqDTO.getStartTime());
            query.setEndTime(reqDTO.getEndTime());
        }

        // 交易编码
        query.setAccountFlowId(reqDTO.getAccountFlowId());

        // 交易类型
        if (Objects.nonNull(reqDTO.getTradeType())) {
            query.setTradeTypes(Lists.newArrayList(reqDTO.getTradeType()));
        }

        // 业务类型
        if (Objects.nonNull(reqDTO.getOperationType())) {
            query.setOperationTypes(Lists.newArrayList(reqDTO.getOperationType()));
        }
        
        if (Objects.nonNull(reqDTO.getOrderType())) {
        	query.setOrderTypes(CategoryTypeMappingEnum.mapCategoryType(reqDTO.getOrderType()));
        }
        
        if (Objects.nonNull(reqDTO.getTargetAcctCode())) {
        	List<String> collectionAccts = acctBusinessDebitFlowService.getAccountNoByTargetAcctCode(reqDTO.getTargetAcctCode());
        	query.setTargetAccounts(collectionAccts);
        }

        // 账单编号
        if (Objects.nonNull(reqDTO.getBillNo())) {
            query.setBillNo(reqDTO.getBillNo());
        }

        //操作人
        query.setOperationUserName(reqDTO.getOperationUserName());

        //操作人企业
        query.setOperationUserCompanyId(reqDTO.getOperationUserCompanyId());

        // 银行流水号
        query.setBankTransNo(reqDTO.getBankTransNo());

        // 请求银行订单号
        query.setSyncBankTransNo(reqDTO.getSyncBankTransNo());

        // 电子回电状态
        query.setReceiptStatus(reqDTO.getCostImageStatus());

        query.setPageIndex(reqDTO.getPageNo());

        query.setPageSize(reqDTO.getPageSize());

        return query;
    }


    /**
     * 商务消费账户 - 列表页
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/list/billFlow", method = RequestMethod.POST)
    public void listBillFlow(HttpRequest request, HttpResponse response) {
        AccountBillFlowReqDTO reqDTO = request.getBodyObject(AccountBillFlowReqDTO.class);
        FinhubLogger.info("商务消费账户流水查询参数->{}", JSON.toJSONString(reqDTO));
        Date minDateOfQuery = DateUtil.convertDate("2021-01-01 00:00:00");
        if (reqDTO.getStartTime() == null
                || reqDTO.getStartTime().before(minDateOfQuery)) {
            reqDTO.setStartTime(minDateOfQuery);
            reqDTO.setEndTime(DateUtils.getCurrentDate());
        }
        BillFlowPageQuery query = checkAndBuildBillFlowSearch(reqDTO);
        // 企业不能为空
        String companyId = getOperationCompanyId(request);
        Map<String, Object> resultMap = Maps.newHashMap();
        
        resultMap.put("code", 0);
        resultMap.put("msg", "success");
        Map<String, Object> dataMap = new HashMap<>();
        resultMap.put("data", dataMap);
        if (StringUtils.isBlank(companyId)) {
        	dataMap.put("totalCount", 0);
            dataMap.put("dataList", Collections.emptyList());
        } else {
        	query.setCompanyId(companyId);
        	Map<String, Object> queryResult = accountBillFlowService.queryBillFlowForWeb(query);
            dataMap.put("totalCount", ObjUtils.toInteger(queryResult.get("count")));
            dataMap.put("dataList", queryResult.get("data"));
            
        }
        response.setResult(JsonUtils.toJson(resultMap));
    }

}
