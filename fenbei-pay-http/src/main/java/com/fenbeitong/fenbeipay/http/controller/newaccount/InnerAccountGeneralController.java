package com.fenbeitong.fenbeipay.http.controller.newaccount;


import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountGeneralOperationRPCDTO;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneralFlow;
import com.fenbeitong.fenbeipay.na.unit.service.UAccountGeneralService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinAccountNoEnoughException;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * (内部系统调用的HTTP服务)收银台收款Http服务Controller
 */

@HttpService("/internal/new/account/general")
public class InnerAccountGeneralController {

    @Autowired
    private UAccountGeneralService uAccountGeneralService;
    @Autowired
    private DingDingMsgService dingDingMsgService;


    /**
     * (HTTP Internal+RPC)创建支付交易流水号(也称为：预支付)
     * 场景OC系统调用，生成支付交易流水号，如果因公支付，企业支付部分需要提前扣除，也在此扣除
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/cash_withdrawal/v1",method = RequestMethod.POST)
    public void  cashWithdrawal(HttpRequest request, HttpResponse response){
        AccountGeneralOperationRPCDTO cashWithdrawalCDTO= request.getBodyObject(AccountGeneralOperationRPCDTO.class);
        FinhubLogger.info("【新账户系统，提现】cashWithdrawalCDTO 参数：{}", cashWithdrawalCDTO);
        if(StringUtils.isEmpty(cashWithdrawalCDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        boolean cashWithdrawal=false;
        try {
            //校验字段
            ValidateUtils.validate(cashWithdrawalCDTO);
            //TODO FBT授信如何还款，余额账户是否需要跟充值分开（同一个主体同一个银行）
            cashWithdrawalCDTO.setBankName(BankNameEnum.FBT.getCode());
            cashWithdrawalCDTO.setBankNo(cashWithdrawalCDTO.getCompanyId());
            cashWithdrawal = uAccountGeneralService.cashWithdrawal(cashWithdrawalCDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】confirmationCashWithdrawal 参数：{}账户余额不足", cashWithdrawalCDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户系统异常】confirmationCashWithdrawal 参数：{}", cashWithdrawalCDTO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】confirmationCashWithdrawal 参数：{}", cashWithdrawalCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】confirmationCashWithdrawal 参数：{}", cashWithdrawalCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        ResponseResultUtils.success(response,cashWithdrawal);
    }


    /**
     * (HTTP Internal+RPC)创建支付交易流水号(也称为：预支付)
     * 场景OC系统调用，生成支付交易流水号，如果因公支付，企业支付部分需要提前扣除，也在此扣除
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/cash_withdrawal/v2",method = RequestMethod.POST)
    public void  cashWithdrawalV2(HttpRequest request, HttpResponse response){
        AccountGeneralOperationRPCDTO cashWithdrawalCDTO= request.getBodyObject(AccountGeneralOperationRPCDTO.class);
        FinhubLogger.info("【新账户系统，提现】cashWithdrawalCDTO 参数：{}", cashWithdrawalCDTO);
        if(StringUtils.isEmpty(cashWithdrawalCDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if(ObjUtils.isNull(cashWithdrawalCDTO.getBankName())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if(ObjUtils.isNull(cashWithdrawalCDTO.getBankNo())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        boolean cashWithdrawal=false;
        try {
            //校验字段
            ValidateUtils.validate(cashWithdrawalCDTO);
            cashWithdrawal = uAccountGeneralService.cashWithdrawal(cashWithdrawalCDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】confirmationCashWithdrawal 参数：{}账户余额不足", cashWithdrawalCDTO.toString(), accountEx);
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户系统异常】confirmationCashWithdrawal 参数：{}", cashWithdrawalCDTO.toString(), e);
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】confirmationCashWithdrawal 参数：{}", cashWithdrawalCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】confirmationCashWithdrawal 参数：{}", cashWithdrawalCDTO.toString(), e);
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        ResponseResultUtils.success(response,cashWithdrawal);
    }


    /**
     * (HTTP Internal+RPC)创建支付交易流水号(也称为：预支付)
     * 场景OC系统调用，生成支付交易流水号，如果因公支付，企业支付部分需要提前扣除，也在此扣除
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/account_recharge/v1",method = RequestMethod.POST)
    public void  accountRecharge(HttpRequest request, HttpResponse response){
        AccountGeneralOperationRPCDTO accountRechargeDTO= request.getBodyObject(AccountGeneralOperationRPCDTO.class);
        FinhubLogger.info("【新账户系统，主账户充值】cashWithdrawalCDTO 参数：{}", accountRechargeDTO);
        if(StringUtils.isEmpty(accountRechargeDTO.getCompanyId())){
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        AccountGeneralFlow flow;
        try {
            //校验字段
            ValidateUtils.validate(accountRechargeDTO);
            //TODO FBT授信如何还款，余额账户是否需要跟充值分开（同一个主体同一个银行）
            accountRechargeDTO.setBankNo(accountRechargeDTO.getCompanyId());
            accountRechargeDTO.setBankName(BankNameEnum.FBT.getCode());
            flow = uAccountGeneralService.accountRecharge(accountRechargeDTO);
        } catch (FinAccountNoEnoughException accountEx) {
            FinhubLogger.warn("【新账户系统异常】accountRecharge 参数：{}账户余额不足", accountRechargeDTO.toString(), accountEx);
            dingDingMsgService.sendMsg("【新账户系统，主账户充值】充值异常:" + accountEx.getMessage() + "  accountRechargeDTO 参数：" + JsonUtils.toJson(accountRechargeDTO));
            throw accountEx;
        } catch (FinPayException e) {
            FinhubLogger.error("【新账户系统异常】accountRecharge 参数：{}", accountRechargeDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户充值】充值异常:" + e.getMessage() + "  accountRechargeDTO 参数：" + JsonUtils.toJson(accountRechargeDTO));
            throw new FinhubException(e.getCode(), e.getType(), e.getMessage());
        } catch (ValidateException e) {
            FinhubLogger.error("【新账户系统异常】accountRecharge 参数：{}", accountRechargeDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户充值】充值异常:" + e.getMessage() + "  accountRechargeDTO 参数：" + JsonUtils.toJson(accountRechargeDTO));
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), e.getMessage());
        } catch (Exception e) {
            FinhubLogger.error("【新账户系统异常】accountRecharge 参数：{}", accountRechargeDTO.toString(), e);
            dingDingMsgService.sendMsg("【新账户系统，主账户充值】充值异常:" + e.getMessage() + "  accountRechargeDTO 参数：" + JsonUtils.toJson(accountRechargeDTO));
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
        ResponseResultUtils.success(response,flow);
    }



}
