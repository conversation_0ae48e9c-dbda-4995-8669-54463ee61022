package com.fenbeitong.fenbeipay.http.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.acctperson.api.model.dto.bankUserCard.req.BankUserCardOptReqDTO;
import com.fenbeitong.bank.api.utils.CopyUtils;
import com.fenbeitong.dech.api.model.dto.BankTradeRespDto;
import com.fenbeitong.fenbeipay.acctdech.service.*;
import com.fenbeitong.fenbeipay.acctdech.unit.service.UAcctCompanyMainService;
import com.fenbeitong.fenbeipay.acctpublic.service.AcctPublicDechService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.acctdech.AcctCompanyBindCardStatusEnum;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AccountVirtualFlowRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctFlowStereoPageServiceResqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOverviewRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AllAcctByCompanyModelRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.AcctRechargeWhiteListDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctdech.req.AcctRechargeWhiteReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.dech.req.AcctPublicCreateReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.cgb.CgbDirectVirtualCardSolveTrapReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.*;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierChangePriceTradeRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctBankCheckProcessReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctExtractDaySearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.req.AcctExtractMonthSearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctExtractDaySearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.extract.resp.AcctExtractMonthSearchRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.*;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountGeneralRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountStatisticalCreditDTO;
import com.fenbeitong.fenbeipay.api.model.dto.personaccount.req.FbbTasksListReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.personaccount.resp.FbbGrantTasksExportRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.AccountRedcouponSearchReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.redcoupon.req.RedcouponGrantFlowReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.VouchersFlowReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.vouchers.resp.VouchersFlow4ExportRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.bank.SpaBankAcctLimitReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.api.service.acct.flow.IAcctFlowService;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService;
import com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService;
import com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctCompanyCardService;
import com.fenbeitong.fenbeipay.api.service.acctdech.IAcctRechargeWhiteListService;
import com.fenbeitong.fenbeipay.api.service.acctdech.IBankSpaTradeService;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchService;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicService;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicTradeService;
import com.fenbeitong.fenbeipay.api.service.bank.*;
import com.fenbeitong.fenbeipay.api.service.bank.cgb.IBankCgbAccountService;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderRefundSettlementService;
import com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementService;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctBankCheckService;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractDayService;
import com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractMonthService;
import com.fenbeitong.fenbeipay.api.service.na.IAccountGeneralService;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSearchService;
import com.fenbeitong.fenbeipay.api.service.personaccount.IPersonAccountService;
import com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponSearchService;
import com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService;
import com.fenbeitong.fenbeipay.api.util.BankNameShowConfig;
import com.fenbeitong.fenbeipay.awplus.biz.PayBiz;
import com.fenbeitong.fenbeipay.awplus.biz.RefundBiz;
import com.fenbeitong.fenbeipay.awplus.model.dto.PrePayRequestDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.QueryRefundRequestDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.RefundRequestDTO;
import com.fenbeitong.fenbeipay.awplus.model.dto.RefundResponseDto;
import com.fenbeitong.fenbeipay.bank.base.dto.SaasBugetDto;
import com.fenbeitong.fenbeipay.bank.base.manager.impl.BankCardManagerImpl;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.ApplyCardManager;
import com.fenbeitong.fenbeipay.bank.company.enroll.manager.CreditApplyManager;
import com.fenbeitong.fenbeipay.bank.company.service.trade.BankCardFundFlowService;
import com.fenbeitong.fenbeipay.cashier.manager.CashierBankCardPayRefundManager;
import com.fenbeitong.fenbeipay.core.constant.core.BankCgbConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.personpay.PayChannel;
import com.fenbeitong.fenbeipay.core.model.vo.acctpublic.*;
import com.fenbeitong.fenbeipay.core.service.rocket.RocketMQProducer;
import com.fenbeitong.fenbeipay.core.utils.BankConfigUtil;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyBindCard;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctCompanyMain;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcct;
import com.fenbeitong.fenbeipay.dto.acctdech.BankAcctFlow;
import com.fenbeitong.fenbeipay.dto.bank.BankCard;
import com.fenbeitong.fenbeipay.dto.na.AccountGeneral;
import com.fenbeitong.fenbeipay.http.service.group.GroupService;
import com.fenbeitong.fenbeipay.http.vo.BankUserCardOptDTO;
import com.fenbeitong.fenbeipay.na.db.mapper.AccountGeneralMapper;
import com.fenbeitong.fenbeipay.na.service.AccountGeneralService;
import com.fenbeitong.fenbeipay.rpc.service.bank.IBankCardCreditApplyServiceImpl;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.FundPlatAcctOptType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;

import javax.crypto.Cipher;
import java.math.BigDecimal;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 账户相关接口.
 *
 * Created by xjf on 2018/8/10.
 * @modify renfj on 2018/12/17
 */
@HttpService("/internal/test")
@Slf4j
public class TestController {
    @Autowired
    private AcctPublicDechService acctPublicDechService;

    @Autowired
    private IBankAccountService iBankAccountService;
    @Autowired
    private IBankPettySearchService iBankPettySearchService;

    @Autowired
    private IBankCardSearchService iBankCardSearchService;

    @Autowired
    private IBankOrderPayService iBankOrderPayService;

    @Autowired
    private IBankOrderRefundService iBankOrderRefundService;

    @Autowired
    private IAcctPublicService iAcctPublicService;

    @Autowired
    private IAcctPublicSearchService iAcctPublicSearchService;

    @Autowired
    private IAcctPublicTradeService iAcctPublicTradeService;

    @Autowired
    private ICashierOrderSettlementService iCashierOrderSettlementService;

    @Autowired
    private ICashierOrderRefundSettlementService iCashierOrderRefundSettlementService;

    @Autowired
    private IVouchersPersonService iVouchersPersonService;

    @Autowired
    private RefundBiz refundBiz;

    @Autowired
    private PayBiz payBiz;

    @Autowired
    private IAccountSearchService iAccountSearchService;

    @Autowired
    private IAcctExtractDayService iAcctExtractDayService;

    @Autowired
    private IAcctExtractMonthService iAcctExtractMonthService;

    @Autowired
    private IAcctBankCheckService iAcctBankCheckService;

    @Autowired
    private IPersonAccountService iPersonAccountService;

    @Autowired
    private IAcctMgrService iAcctMgrService;

    @Autowired
    private IAcctFlowService iAcctFlowService;

    @Autowired
    private IAccountGeneralService iAccountGeneralService;

    @Autowired
    private IAcctFundMgrService iAcctFundMgrService;

    @Autowired
    private IAccountRedcouponSearchService iAccountRedcouponSearchService;

    @Autowired
    public CreditApplyManager creditApplyManager;

    @Autowired
    private IBankCardCreditApplyService iBankCardCreditApplyService;

    @Autowired
    public IBankCardCreditApplyServiceImpl bankCardCreditApplyService;

    @Autowired
    private IMessageSetupService messageSetupService;

    @Autowired
    private IAcctRechargeWhiteListService iAcctRechargeWhiteListService;

    @Autowired
    private GroupService groupService;

    @Autowired
    private AcctCompanyBindCardService acctCompanyBindCardService;
    @Autowired
    private UAcctCompanyMainService uAcctCompanyMainService;
    @Autowired
    private AccountGeneralMapper accountGeneralMapper;
    @Autowired
    private BankAcctFlowService bankAcctFlowService;
    @Autowired
    private AccountGeneralService accountGeneralService;
    @Autowired
    private RocketMQProducer rocketMQProducer;

    @Value("${rocketmq.producer.stereo.qb.sync.topic}")
    private String topic;

    @Autowired
    private IBankCgbAccountService iBankCgbAccountService;
    @Autowired
    private RedisTemplate redisTemplate;
    private final Integer TIME_OUT_TEN = 30;
    @Autowired
    private BankCardManagerImpl bankCardManager;

    @Autowired
    private IBankSpaTradeService iBankSpaTradeService;
    /**
     * 广发停服redis开关
     */
    @HttpService(value = "/switch", method = RequestMethod.POST)
    public void sendAccountInfoUpdateMsg(HttpRequest request, HttpResponse response) {
        /**
         * ON 广发服务开启，OFF广发停服
         */
        String menu = request.getParameter("menu");
        String req = StringUtils.isBlank(menu) ? BankCgbConstant.ON : menu;
        String redisKey = MessageFormat.format(BankCgbConstant.BANK_STOP_SERVER_REDIS_KEY, BankNameEnum.CGB.getCode());
        redisTemplate.opsForValue().set(redisKey,req,TIME_OUT_TEN, TimeUnit.DAYS);

        String onOff = (String) redisTemplate.opsForValue().get(redisKey);
        FinhubLogger.info("广发服务redis开关：result={}",onOff);
        ResponseResultUtils.success(response, onOff);
    }


    @HttpService(value = "/cgb/stop/server",method = RequestMethod.GET)
    public void stopServer(HttpRequest request,HttpResponse response) throws InterruptedException {
        String redisKey = MessageFormat.format(BankCgbConstant.BANK_STOP_SERVER_REDIS_KEY, BankNameEnum.CGB.getCode());
        String redisValue = (String)redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(redisValue)) {
            if (redisValue.equals(BankCgbConstant.OFF)) {
                GlobalResponseCode e = GlobalResponseCode.BANK_CGB_STOP_SERVER;
                throw new FinhubException(e.getCode(), e.getType(), e.getMsg());
            }
        }
    }
    @Autowired
    private BankCardFundFlowService bankCardFundFlowService ;

    /**
     * 【集团版】清洗数据接口（补充流水中操作人企业信息）
     * @param request
     * @param response
     */
    @HttpService(value = "/group/cleanData",method = RequestMethod.GET)
    public void groupCleanData(HttpRequest request,HttpResponse response) throws InterruptedException {
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        groupService.cleanData(startTime, endTime);
    }

    @HttpService(value = "/group/stopCleanData",method = RequestMethod.GET)
    public void groupStopCleanData(HttpRequest request,HttpResponse response) {
        groupService.stopCleanData();
    }

    @HttpService(value = "/group/startCleanData",method = RequestMethod.GET)
    public void groupStartCleanData(HttpRequest request,HttpResponse response) {
        groupService.startCleanData();
    }


    @HttpService(value = "/552",method = RequestMethod.POST)
    public void round(HttpRequest request,HttpResponse response){
        String companyId = request.getParameter("companyId");
        String employeeId = request.getParameter("employeeId");
        String applyId = request.getParameter("applyId");
        String bigDecimal = request.getParameter("operationAmount");
        BigDecimal operationAmount = new BigDecimal(bigDecimal);
//        iBankCardModifyService.updateCardBalanceAndGrantSumByApplyId(companyId,employeeId,applyId,operationAmount);
    }

    @HttpService(value = "/553",method = RequestMethod.POST)
    public void retryCgb(HttpRequest request,HttpResponse response){
        CgbDirectVirtualCardSolveTrapReqDTO bodyObject = request.getBodyObject(CgbDirectVirtualCardSolveTrapReqDTO.class);
        iBankCgbAccountService.solveTrap(bodyObject);
        System.out.println(111);
    }

    @HttpService(value = "/554",method = RequestMethod.POST)
    public void batchDistributeCredit(HttpRequest request,HttpResponse response){
        BankApplyCreditReqDTO bodyObject = request.getBodyObject(BankApplyCreditReqDTO.class);
        BankCardBatchDistributeRespDTO bankCardBatchDistributeRespDTO = iBankAccountService.batchDistributeCredit(bodyObject);
        System.out.println(JSON.toJSONString(bankCardBatchDistributeRespDTO));
    }




    @HttpService(value = "/445", method = RequestMethod.POST)
    public void costAttribute(HttpRequest request, HttpResponse response) {
        String companyId = request.getParameter("companyId");
        String employeeId = request.getParameter("employeeId");
        String bankName = request.getParameter("bankName");
        BankSeraechCostAttributionResqDTO bankSeraechCostAttributionResqDTO = iBankCardSearchService.searchCostAttribution(companyId, employeeId, bankName);
        ResponseResultUtils.success(response, bankSeraechCostAttributionResqDTO);
    }

    @Autowired
    private ApplyCardManager applyCardManager;

    @HttpService(value = "/canApply", method = RequestMethod.GET)  //王彭
    public void testCanApply(HttpRequest request, HttpResponse response) {

        List<String> userIds = iBankCardSearchService.getCardUsersByCompanyId("5747fbc10f0e60e0709d8d7d");
        System.out.println(userIds.toString());

        ResponseResultUtils.success(response, "ok");
    }

    @HttpService(value = "/dealSpa", method = RequestMethod.POST)
    public void dealSpaBankReturnAmount(HttpRequest request, HttpResponse response) {
        RechargeCardAcctCreditReqDTO bodyObject = request.getBodyObject(RechargeCardAcctCreditReqDTO.class);
        ResponseResultUtils.success(response,applyCardManager.rechargeCardAcctBalance4SpaBank(bodyObject));
    }


    @Autowired
    private AcctReimbursementCountService acctReimbursementCountService;

    @Autowired
    public IAcctCompanyCardService iAcctCompanyCardService;

    @HttpService(value = "/testFrozen", method = RequestMethod.POST)
    public void testFrozen(HttpRequest request, HttpResponse response) {
        AccountCardApplyAmountReqDTO reqDTO = request.getBodyObject(AccountCardApplyAmountReqDTO.class);
        iAcctCompanyCardService.frozen(reqDTO);
        ResponseResultUtils.success(response, "success");
    }

    @HttpService(value = "/createApplyOrderFlow", method = RequestMethod.POST)
    public void createApplyOrderFlow(HttpRequest request, HttpResponse response) {
        BankApplyCreditReqDTO reqDTO = request.getBodyObject(BankApplyCreditReqDTO.class);
        BankApplyCreditRespDTO respDTO = iBankAccountService.applyCredit(reqDTO);
        ResponseResultUtils.success(response, respDTO);
    }
    @HttpService(value = "/getBankCardCreditApplyByEmployeeId", method = RequestMethod.POST)
    public void getBankCardCreditApplyByEmployeeId(HttpRequest request, HttpResponse response) {
        BankSearchApplyCreditReqDTO reqdto = request.getBodyObject(BankSearchApplyCreditReqDTO.class);
        IBankCardCreditApplyService iBankCardCreditApplyService = this.iBankCardCreditApplyService;
        iBankCardCreditApplyService.getBankCardCreditApplyByParam(reqdto);
        ResponseResultUtils.success(response, iBankCardCreditApplyService);
    }
    @HttpService(value = "/3333", method = RequestMethod.POST)
    public void findAccountCouponGrantList(HttpRequest request, HttpResponse response) {
        RedcouponGrantFlowReqDTO accountSubVo = request.getBodyObject(RedcouponGrantFlowReqDTO.class);
        iAccountRedcouponSearchService.findAccountCouponGrantList(accountSubVo);
    }

    @HttpService(value = "/4444", method = RequestMethod.POST)
    public void exportAccountCouponGrantList(HttpRequest request, HttpResponse response) {
        AccountRedcouponSearchReqRPCDTO accountSubVo = request.getBodyObject(AccountRedcouponSearchReqRPCDTO.class);
        iAccountRedcouponSearchService.exportAccountCouponGrantList(accountSubVo);
    }


    @HttpService(value = "/createAcctPublic", method = RequestMethod.POST)
    public void createAcctPublic(HttpRequest request, HttpResponse response) {
        AcctPublicCreateReqDTO accountSubVo = request.getBodyObject(AcctPublicCreateReqDTO.class);
        acctPublicDechService.createAcctPublic(accountSubVo);
    }

    @HttpService(value = "/changeCompanyModel", method = RequestMethod.POST)
    public void changeCompanyModel(HttpRequest request, HttpResponse response) {
        AccountChangeCompanyModelReqDTO accountSubVo = request.getBodyObject(AccountChangeCompanyModelReqDTO.class);
        iAccountGeneralService.changeCompanyModel(accountSubVo);
    }

    @HttpService(value = "/acctFlowSearchStereoPage", method = RequestMethod.POST)
    public void acctFlowSearchStereoPage(HttpRequest request, HttpResponse response) {
        AcctFlowStereoPageServiceReqDTO accountSubVo = request.getBodyObject(AcctFlowStereoPageServiceReqDTO.class);
        ResponsePage<AcctFlowStereoPageServiceResqDTO> responsePage = iAcctFlowService.acctFlowSearchStereoPage(accountSubVo);
    }

    @HttpService(value = "/queryAccountPageFlow", method = RequestMethod.POST)
    public void queryAccountPageFlow(HttpRequest request, HttpResponse response) {
        AccountVirtualFlowReqRPCDTO accountSubVo = request.getBodyObject(AccountVirtualFlowReqRPCDTO.class);
        ResponsePage<AccountVirtualFlowRespDTO> responsePage = iAcctFlowService.queryAccountPageFlow(accountSubVo);
        ResponseResultUtils.success(response, responsePage);
    }

    @HttpService(value = "/queryAccountDetailFlow", method = RequestMethod.POST)
    public void queryAccountDetailFlow(HttpRequest request, HttpResponse response) {
        String accountFlowId = request.getParameter("accountFlowId");
        AccountVirtualFlowReqRPCDTO accountSubVo = request.getBodyObject(AccountVirtualFlowReqRPCDTO.class);
        AccountVirtualFlowRespDTO responsePage = iAcctFlowService.queryAccountDetailFlow(accountFlowId);
        ResponseResultUtils.success(response, responsePage);
    }

    @HttpService(value = "/queryAccountBigDataPageFlow", method = RequestMethod.POST)
    public void queryAccountBigDataPageFlow(HttpRequest request, HttpResponse response) {
        AccountVirtualFlowReqRPCDTO accountSubVo = request.getBodyObject(AccountVirtualFlowReqRPCDTO.class);
        ResponsePage<AccountVirtualFlowRespDTO> responsePage = iAcctFlowService.queryAccountBigDataPageFlow(accountSubVo);
        ResponseResultUtils.success(response, responsePage);
    }

    @HttpService(value = "/queryAccountBigDataDetailFlow", method = RequestMethod.POST)
    public void queryAccountBigDataDetailFlow(HttpRequest request, HttpResponse response) {
        String accountFlowId = request.getParameter("accountFlowId");
        AccountVirtualFlowReqRPCDTO accountSubVo = request.getBodyObject(AccountVirtualFlowReqRPCDTO.class);
        AccountVirtualFlowRespDTO responsePage = iAcctFlowService.queryAccountBigDataDetailFlow(accountFlowId);
        ResponseResultUtils.success(response, responsePage);
    }


    @HttpService(value = "/acctFlowSearchByStereoBill", method = RequestMethod.POST)
    public void acctFlowSearchByStereoBill(HttpRequest request, HttpResponse response) {
        AcctFlow4StereoBillReqDTO acctFlow4StereoBillReqDTO = request.getBodyObject(AcctFlow4StereoBillReqDTO.class);
        ResponseResultUtils.success(response,  iAcctFlowService.acctFlowSearchByStereoBill(acctFlow4StereoBillReqDTO));
    }



    @HttpService(value = "/44441", method = RequestMethod.POST)
    public void cutPettyCardModel(HttpRequest request, HttpResponse response) {
        BankCardCutPettyModelReqDTO accountSubVo = request.getBodyObject(BankCardCutPettyModelReqDTO.class);
         iBankAccountService.cutPettyCardModel(accountSubVo);
    }

    @HttpService(value = "/ooo", method = RequestMethod.POST)
    public void grantTaskExport(HttpRequest request, HttpResponse response) {
        FbbTasksListReqRPCDTO accountSubVo = request.getBodyObject(FbbTasksListReqRPCDTO.class);
        List<FbbGrantTasksExportRespRPCDTO> fbbGrantTasksExportRespRPCDTOS = iPersonAccountService.grantTaskExport(accountSubVo);
        ResponseResultUtils.success(response, fbbGrantTasksExportRespRPCDTOS);
    }

    @HttpService(value = "/queryBizNoByPettyIds", method = RequestMethod.POST)
    public void queryBizNoByPettyIds(HttpRequest request, HttpResponse response) {
        List<String> bodyObject = request.getBodyObject(List.class);
        iBankPettySearchService.queryBizNoByPettyIds(bodyObject);
    }

    @HttpService(value = "/777771", method = RequestMethod.POST)
    public void accountStatisticalCredit2(HttpRequest request, HttpResponse response) {
        AccountOneStatisticsAmountDTO accountStatisticsAmountDTO = request.getBodyObject(AccountOneStatisticsAmountDTO.class);
        List<AccountStatisticalCreditDTO> accountStatisticalCreditDTOS = iAccountSearchService.accountOneStatisticalCredit(accountStatisticsAmountDTO);
        ResponseResultUtils.success(response, accountStatisticalCreditDTOS);
    }

    @HttpService(value = "/payBiz")
    public void PayBiz1(HttpRequest request, HttpResponse response) {
        PrePayRequestDTO payRequestDTO = PrePayRequestDTO.builder()
                .payChannel(PayChannel.PAY_ALI_APP)
                .orderId("tdi"+System.currentTimeMillis())
                .accountId("ddfddd")
                .accountType(1)
                .amount(new BigDecimal("1"))
                .chargeId("tdc"+System.currentTimeMillis())
                .subject("wwwww")
                .body("dddd")
                .deadLineTime(DateUtils.addHour(new Date(),1))
                .fbOrderId("tct"+System.currentTimeMillis())
                .employeeId("**************")
                .orderType(11)
                .build();
        Object url= payBiz.genPrePayInfo(payRequestDTO);
        ResponseResultUtils.success(response, url);
        //ResponseResultUtils.redirect(response, null,String.valueOf(url));
    }

    @HttpService(value = "/44442", method = RequestMethod.POST)
    public void applyCredit(HttpRequest request, HttpResponse response) {
        BankApplyCreditReqDTO bankApplyCreditReqDTO = request.getBodyObject(BankApplyCreditReqDTO.class);
        iBankAccountService.applyCredit(bankApplyCreditReqDTO);
    }

    @HttpService(value = "/44443", method = RequestMethod.POST)
    public void synOrderReimbursed(HttpRequest request, HttpResponse response) {
        BankReimbursedReqDTO bankApplyCreditReqDTO = request.getBodyObject(BankReimbursedReqDTO.class);
        iBankOrderPayService.synOrderReimbursed(bankApplyCreditReqDTO);
    }

    @HttpService(value = "/44445", method = RequestMethod.POST)
    public void cutCompanyNormalCardModel(HttpRequest request, HttpResponse response) {
        CompanyCutNormalModelReqDTO companyCutNormalModelReqDTO = request.getBodyObject(CompanyCutNormalModelReqDTO.class);
        iBankAccountService.cutCompanyNormalCardModel(companyCutNormalModelReqDTO);
    }

    @HttpService(value = "/8000", method = RequestMethod.POST)
    public void refund(HttpRequest request, HttpResponse response) {
        RefundRequestDTO accountSubVo = request.getBodyObject(RefundRequestDTO.class);
        RefundResponseDto refundResponseDto = refundBiz.refund(accountSubVo);
        ResponseResultUtils.success(response, refundResponseDto);

    }

    @HttpService(value = "/8001", method = RequestMethod.POST)
    public void queryRefundRecordList(HttpRequest request, HttpResponse response) {
        QueryRefundRequestDTO accountSubVo = request.getBodyObject(QueryRefundRequestDTO.class);
        List<RefundResponseDto> dd = refundBiz.queryRefundRecordList(accountSubVo);
        ResponseResultUtils.success(response, dd);

    }

    /**
     * 查询账户信息
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/1", method = RequestMethod.POST)
    public void queryAccountInfo(HttpRequest request, HttpResponse response) {
        BankCreateAccountReqDTO accountSubVo = request.getBodyObject(BankCreateAccountReqDTO.class);
        BankCreateAccountRespDTO bankAccount = iBankAccountService.createBankAccount(accountSubVo);
        ResponseResultUtils.success(response, bankAccount);

    }

    @HttpService(value = "/111111", method = RequestMethod.POST)
    public void queryByCompanyAcctIdAndBankAcctName(HttpRequest request, HttpResponse response) {
        AcctPublicDetailRespRPCDTO acctPublicDetailRespRPCDTO = iAcctPublicSearchService.queryByCompanyAcctIdAndBankAcctName("5747fbc10f0e60e0709d8d7d", BankNameEnum.ZBBANK);
        ResponseResultUtils.success(response, acctPublicDetailRespRPCDTO);

    }

    /**
     * 查询账户信息
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/11112", method = RequestMethod.POST)
    public void queryAccountInfo112(HttpRequest request, HttpResponse response) {
        VouchersFlowReqRPCDTO vouchersFlowReqRPCDTO = request.getBodyObject(VouchersFlowReqRPCDTO.class);
        VouchersFlow4ExportRespRPCDTO vouchersFlow4ExportRespRPCDTO = iVouchersPersonService.
                queryVoucherFlow4Export(vouchersFlowReqRPCDTO);
        ResponseResultUtils.success(response, vouchersFlow4ExportRespRPCDTO);

    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/2", method = RequestMethod.POST)
    public void searchBankCard(HttpRequest request, HttpResponse response) {
        BankApplyCreditReqDTO accountSubVo = request.getBodyObject(BankApplyCreditReqDTO.class);
        BankApplyCreditRespDTO bankApplyCreditRespDTO = iBankAccountService.applyCredit(accountSubVo);
        ResponseResultUtils.success(response, bankApplyCreditRespDTO);
    }

    @HttpService(value = "/1234", method = RequestMethod.POST)
    public void addFlow(HttpRequest request, HttpResponse response) {
        //BankCardBaseReqDTO accountSubVo = request.getBodyObject(BankCardBaseReqDTO.class);
        //if(accountSubVo.getRemark()!=null){
        //    try {
        //        String decrypt = this.decrypt(accountSubVo.getRemark());
        //        accountSubVo.setRemark(decrypt);
        //        //iBankAccountService.handleBalance(accountSubVo);
        //    } catch (Exception e) {
        //        log.info("解密失败",e);
        //        ResponseResultUtils.fail(response,  new FinhubException(401, "不存在的编码"));
        //    }
        //}
        ResponseResultUtils.success(response);
    }

    private  String decrypt(String  encryptedStr) throws Exception {
        String privateKeyStr = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCgYsZelywQCoE6FzBI60kfR/EQ/OLki2CDG39tn5Lp3/GpGWFIhtx58lSiso9Db9GuJ3T2iE/u1LjEr1MDrbf4MjxmoB6PH6EQyibA3ln/njZHzemjLpq7YyYy1S1/CsHL+Wmj+LJDltV477A0aa8ttB7xnSOCA8uG5mg5d23/JGq/YffxmI8lVY274jLc1EzZjhMeXQF0kf7EBGJT7njRIg7AADctiSWJ3tIsI79q/1HrRxcddfWjF2OGMiqQ1DNNjYV+HVu/hgZVpzNpEjzYdXrRE/7xWIoHqtkQi0siCl2X8UvAXGCn1gpn1UWRFty9GT2TVCUVClOjkCWKGFiDAgMBAAECggEAazE49VVB3MK4lbOT0Nh3+ZEie8EYf7jrWxTQuNqS3P6QS/0reMaxqksc59RBTcf5MtUqJe76xVuk1cd7yxSaxSYGGmzfn7Z69na1Kegx7Fa4XosvoKFwRtdAW28AxkzRM1tRkIaR1fyEVhyT/qO3sesVV1Q3vBh/OXzaobXBbWi9ET58RIT/E7/v1Xem/r77+jPLS8TqpvC4j9250a9x4UyrTm39uIgd+HgyAR1FCRdXDP2LbAZ7PPDBr7rHJLyx8cojVFB5C2qLaOQrrdlwpjaUKIdQUzS4gnIyb3oK6UwDy4uJX0x8ek440I6+Mf/pfGqPUTRwl5AddvWfweNM6QKBgQDzOgGfvINmJIQwipXvVS7pj9hbju0rpdJBUoGG8jjiLUyC2z+kYse8BWF4k6m6J/ehYuYRKeMg8KnSYfsfcCHuPwV3ig0dKc4R04FfMu/3RQ1RbxtEf6ybYP2rt9tjRyW9bZ2ADk1FDt7pTVngnHlH+vEnH/sDOROeRwbG3Q6ujwKBgQCozwnk1CprXUwOf5DvcOkd6sburvSj0ce4h86C8cQP701u+svgQmPQRi3iQilpJmmggT69/rzCMkFvuxXPzUbw67JzXdKrWpFPwIGk/6BU1RBflEYANUpmIa8YSMr2x7wfNlqF+GHbb8G4c3gHgGsKNDzEezFs1X1VSYkFU4hwzQKBgQCe8XocM6m+dwB3ZCkE5KqqcJjXhDgSvOMAUvVYBTQNAcMtBC+7sd2KTg3bKMvSXcniCeoEr1XAseSYGfVsHu/dbAubiXh2ZVpHpeq2FmhgMxy36Z5tDe3y/9flfll8sALGnjo9c5slFMmwth6UvEw5MJCkm66B9auPVCTmkZDFmwKBgG43r/gOZxvusIgO1mzfxfVYo0aYSPblM4fSdA2fGSpmTqKVcaQc9Nq515+RH0SeZfz8JiEbP9dzxLHU7yrcluMaoUseCT0ERzhRMtSuatq4upYNU58zLD5+47nxXUO/qZ6feH9ca1hBIbTy/322ZS0maj7HSMAMrGaROruVjNJJAoGAA7wEPw14i1HPhwW37OFT4KSFViGE9EEg00S1RCtdJh5zw6tA/X2mSC00VSvsEkVDn0EXZcHW+UyOUnF6IZu2eKq2WlnN36mjebrrb5mfUyDnAgn2ck3EXJOEj7VPCOkHayrEew9iB6/PwGwbqWun8GOie/X+gc0n9TrNJtQy/uI=";
        byte[] keyBytes1 = Base64.getDecoder().decode(privateKeyStr);
        PKCS8EncodedKeySpec keySpec1 = new PKCS8EncodedKeySpec(keyBytes1);
        PrivateKey privateKey =  KeyFactory.getInstance("RSA").generatePrivate(keySpec1);
        Cipher decryptCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        decryptCipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedBytes = decryptCipher.doFinal(Base64.getDecoder().decode(encryptedStr));
        return  new String(decryptedBytes);

    }

    @HttpService(value = "/addReturnFlow", method = RequestMethod.POST)
    public void addReturnFlow(HttpRequest request, HttpResponse response) {
        BankCardFlowBaseReqDTO accountSubVo = request.getBodyObject(BankCardFlowBaseReqDTO.class);
        VirtualCardAccountFlowResqDTO res = iBankAccountService.addReturnFlow(accountSubVo);
        ResponseResultUtils.success(response, res);
    }

    @HttpService(value = "/addApplyFlow", method = RequestMethod.POST)
    public void addApplyFlow(HttpRequest request, HttpResponse response) {
        BankCardFlowBaseReqDTO accountSubVo = request.getBodyObject(BankCardFlowBaseReqDTO.class);
        VirtualCardAccountFlowResqDTO res = iBankAccountService.addApplyFlow(accountSubVo);
        ResponseResultUtils.success(response, res);
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/2111", method = RequestMethod.POST)
    public void rechargeCardAcctCredit(HttpRequest request, HttpResponse response) {
        RechargeCardAcctCreditReqDTO accountSubVo = request.getBodyObject(RechargeCardAcctCreditReqDTO.class);
        BankCardAcctRechargeCreditRespDTO bankApplyCreditRespDTO = iBankAccountService.rechargeCardAcctCredit(accountSubVo);
        ResponseResultUtils.success(response, bankApplyCreditRespDTO);
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/3", method = RequestMethod.POST)
    public void bankSearchCardReqDTO(HttpRequest request, HttpResponse response) {
        BankSearchCardReqDTO accountSubVo = request.getBodyObject(BankSearchCardReqDTO.class);
        BankSearchCardDetailRespDTO searchBankCardDetailDTO = iBankCardSearchService.searchBankCard(accountSubVo);
        ResponseResultUtils.success(response, searchBankCardDetailDTO);
    }
    @HttpService(value = "/401a", method = RequestMethod.GET)
    public void queryByBankAccountNo(HttpRequest request, HttpResponse response) {
        String bankAccountNo = request.getParameter("bankAccountNo");
        BankSearchCardDetailRespDTO searchBankCardDetailDTO = iBankCardSearchService.queryByBankAccountNo(bankAccountNo);
        System.out.println(JSON.toJSONString(searchBankCardDetailDTO));
        ResponseResultUtils.success(response, searchBankCardDetailDTO);
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/30", method = RequestMethod.POST)
    public void searchCostAttribution(HttpRequest request, HttpResponse response) {
        try {
            BankSeraechCostAttributionResqDTO attributionResqDTO = iBankCardSearchService.searchCostAttribution("5c107d8523445f330630d1d4", "5d6e203123445f2fde0377f6","XWBANK");
            ResponseResultUtils.success(response, attributionResqDTO);
        } catch (Exception e) {
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/311", method = RequestMethod.POST)
    public void queryByApplyTransNo(HttpRequest request, HttpResponse response) {
        try {
            String subId = request.getParameter("subId");
            BankPettySubDetailRespDTO bankPettySubDetailRespDTO = iBankCardSearchService.queryBySubId(subId);
            BankRefundCreditDetailRespDTO bankRefundCreditDetailRespDTO = iBankCardSearchService.queryByApplyTransNo("5c126b8a23445f41dcdf5f97", "6131bdde1e4bfb784c0aab31","APA202112071513558599147");
            ResponseResultUtils.success(response, bankRefundCreditDetailRespDTO);
        } catch (Exception e) {
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/40", method = RequestMethod.POST)
    public void searchApplyCredit(HttpRequest request, HttpResponse response) {
        try {
            BankSearchApplyCreditReqDTO accountSubVo = request.getBodyObject(BankSearchApplyCreditReqDTO.class);
            Map<Object, List<BankSearchApplyCreditResqDTO>> objectListMap = iBankCardSearchService.searchApplyCredit(accountSubVo);
            ResponseResultUtils.success(response, objectListMap);
        } catch (Exception e) {
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(),
                    GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());
        }
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/31", method = RequestMethod.POST)
    public void searchBankCardPage(HttpRequest request, HttpResponse response) {
        BankCardSearchReqDTO accountSubVo = request.getBodyObject(BankCardSearchReqDTO.class);
        ResponsePage<BankCardSearchResqDTO> bankCardSearchResqDTOResponsePage = iBankCardSearchService.searchBankCardPage(accountSubVo);
        ResponseResultUtils.success(response, bankCardSearchResqDTOResponsePage);
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/32", method = RequestMethod.POST)
    public void searchBankCardApplyPage(HttpRequest request, HttpResponse response) {
        BankCardApplySearchReqDTO accountSubVo = request.getBodyObject(BankCardApplySearchReqDTO.class);
        ResponsePage<BankCardApplySearchResqDTO> page = iBankCardSearchService.searchBankCardApplyPage(accountSubVo);
        ResponseResultUtils.success(response, page);
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/9999", method = RequestMethod.POST)
    public void exportWebBankCardFlowPage(HttpRequest request, HttpResponse response) {
        BankCardSearchReqDTO accountSubVo = request.getBodyObject(BankCardSearchReqDTO.class);
        List<ExportBankCardFlowResqDTO> page = iBankCardSearchService.exportWebBankCardFlowPage(accountSubVo);
        ResponseResultUtils.success(response, page);
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/33", method = RequestMethod.POST)
    public void queryBankCardDetail(HttpRequest request, HttpResponse response) {
        BankCardDetailRespDTO b = iBankCardSearchService.queryBankCardDetail("5747fbc10f0e60e0709d8d7d", "BCA201909231423221790412");
        ResponseResultUtils.success(response, b);
    }

    @HttpService(value = "/********/{companyId}", method = RequestMethod.GET)
    public void findBalanceEmployeeByCompanyId(HttpRequest request, HttpResponse response) {
        String companyId = request.getPathValue("companyId");
        List<String> balanceEmployeeByCompanyId = iBankCardSearchService.findBalanceEmployeeByCompanyId(companyId);
        ResponseResultUtils.success(response, balanceEmployeeByCompanyId);
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/34", method = RequestMethod.POST)
    public void queryBankBalance(HttpRequest request, HttpResponse response) {
        Map<String, Boolean> deleteEmployeeByBank = iBankCardSearchService.isDeleteEmployeeByBank("5747fbc10f0e60e0709d8d7d", "5a2752f123445f3483c70354");
        ResponseResultUtils.success(response, deleteEmployeeByBank);
    }

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/3411/{companyId}/{bankName}", method = RequestMethod.GET)
    public void companyEmployeeBankCredit(HttpRequest request, HttpResponse response) {
        String companyId = request.getPathValue("companyId");
        String bankName = request.getPathValue("bankName");
        Map<String, Boolean> deleteEmployeeByBank = iBankCardSearchService.companyEmployeeBankCredit(companyId, BankNameEnum.getBankEnum(bankName));
        ResponseResultUtils.success(response, deleteEmployeeByBank);
    }

    /**
     * (HTTP Internal) 解绑
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/4", method = RequestMethod.POST)
    public void unBindAccount(HttpRequest request, HttpResponse response) {
        BankUnbindAccountReqDTO accountSubVo = request.getBodyObject(BankUnbindAccountReqDTO.class);
        BankUnbindAccountRespDTO bankUnbindAccountRespDTO = iBankAccountService.unBindAccount(accountSubVo);
        ResponseResultUtils.success(response, bankUnbindAccountRespDTO);
    }


    /**
     * (HTTP Internal) 绑定
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/5", method = RequestMethod.POST)
    public void bindAccount(HttpRequest request, HttpResponse response) {
        BankBindAccountReqDTO accountSubVo = request.getBodyObject(BankBindAccountReqDTO.class);
        BankBindAccountRespDTO bankBindAccountRespDTO = iBankAccountService.bindAccount(accountSubVo);
        ResponseResultUtils.success(response, bankBindAccountRespDTO);
    }

    /**
     * (HTTP Internal) 还额
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/7", method = RequestMethod.POST)
    public void refundCredit(HttpRequest request, HttpResponse response) {
        BankRefundCreditReqDTO accountSubVo = request.getBodyObject(BankRefundCreditReqDTO.class);
        BankRefundCreditRespDTO bankRefundCreditRespDTO = iBankAccountService.refundCredit(accountSubVo);
        ResponseResultUtils.success(response, bankRefundCreditRespDTO);
    }

    /**
     * (HTTP Internal) 还额
     */
    @HttpService(value = "/7111", method = RequestMethod.POST)
    public void withdrawalCardAcctCredit(HttpRequest request, HttpResponse response) {
        BankCardAcctWithdrawalCreditReqDTO accountSubVo = request.getBodyObject(BankCardAcctWithdrawalCreditReqDTO.class);
        BankCardAcctWithdrawalCreditRespDTO bankRefundCreditRespDTO = iBankAccountService.withdrawalCardAcctCredit(accountSubVo);
        ResponseResultUtils.success(response, bankRefundCreditRespDTO);
    }

    /**
     * (HTTP Internal) 分贝通虚拟卡禁用
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/711", method = RequestMethod.POST)
    public void disableBankCardAccount(HttpRequest request, HttpResponse response) {
        BankDisableBankCardReqDTO accountSubVo = request.getBodyObject(BankDisableBankCardReqDTO.class);
        BankDisableAccountRespDTO bankDisableAccountRespDTO = iBankAccountService.disableBankCardAccount(accountSubVo);
        ResponseResultUtils.success(response, bankDisableAccountRespDTO);
    }

    /**
     * (HTTP Internal) 分贝通虚拟卡禁用
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/712", method = RequestMethod.POST)
    public void enableBankCardAccount(HttpRequest request, HttpResponse response) {
        BankEnableAccountReqDTO accountSubVo = request.getBodyObject(BankEnableAccountReqDTO.class);
        BankEnableAccountRespDTO bankEnableAccountRespDTO = iBankAccountService.enableBankCardAccount(accountSubVo);
        ResponseResultUtils.success(response, bankEnableAccountRespDTO);
    }

    /**
     * (HTTP Internal) 支付
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/8", method = RequestMethod.POST)
    public void payTrade(HttpRequest request, HttpResponse response) {
        BankPayTradeReqDTO accountSubVo = request.getBodyObject(BankPayTradeReqDTO.class);
        BankPayTradeRespDTO bankPayTradeRespDTO = iBankOrderPayService.payTrade(accountSubVo);
        ResponseResultUtils.success(response, bankPayTradeRespDTO);
    }

    /**
     * (HTTP Internal) 退款
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/9", method = RequestMethod.POST)
    public void refundTrade(HttpRequest request, HttpResponse response) {
        BankRefundTradeReqDTO accountSubVo = request.getBodyObject(BankRefundTradeReqDTO.class);
        BankRefundTradeRespDTO bankRefundTradeRespDTO = iBankOrderRefundService.refundTrade(accountSubVo);
        ResponseResultUtils.success(response, bankRefundTradeRespDTO);
    }

    /////________________________________对公账户测试
    @HttpService(value = "/200", method = RequestMethod.POST)
    private void createAccountPublic(HttpRequest request, HttpResponse response) {
        AddAcctPublicReqRPCDTO createAccountPublicReqDTO = request.getBodyObject(AddAcctPublicReqRPCDTO.class);
        AddAcctPublicRespRPCDTO addAcctPublicRespRPCDTO = iAcctPublicService.addAccountPublic(createAccountPublicReqDTO);
        ResponseResultUtils.success(response, addAcctPublicRespRPCDTO);
    }

    @HttpService(value = "/210", method = RequestMethod.POST)
    private void synExamineFailBankAcct(HttpRequest request, HttpResponse response) {
        SynAcctPublicReqRPCDTO createAccountPublicReqDTO = request.getBodyObject(SynAcctPublicReqRPCDTO.class);
         iAcctPublicService.synExamineFailBankAcct(createAccountPublicReqDTO);
    }

    @HttpService(value = "/201", method = RequestMethod.POST)
    private void synAuthBankAccountNo(HttpRequest request, HttpResponse response) {
        SynAcctPublicReqVo accountPublicBaseReqVo = request.getBodyObject(SynAcctPublicReqVo.class);
        SynAcctPublicReqRPCDTO synAccountPublicReqDTO=new SynAcctPublicReqRPCDTO();
        BeanUtils.copyProperties(accountPublicBaseReqVo, synAccountPublicReqDTO);
        synAccountPublicReqDTO.setBankAccountName(BankNameEnum.getBankEnum(accountPublicBaseReqVo.getBankAccountName()));
         iAcctPublicService.synAuthBankAccountNo(synAccountPublicReqDTO);
    }

    @HttpService(value = "/202", method = RequestMethod.POST)
    private void synFailAuthBankAccount(HttpRequest request, HttpResponse response) {
        SynAcctPublicReqVo accountPublicBaseReqVo = request.getBodyObject(SynAcctPublicReqVo.class);
        SynAcctPublicReqRPCDTO synAccountPublicReqDTO=new SynAcctPublicReqRPCDTO();
        BeanUtils.copyProperties(accountPublicBaseReqVo, synAccountPublicReqDTO);
        synAccountPublicReqDTO.setBankAccountName(BankNameEnum.getBankEnum(accountPublicBaseReqVo.getBankAccountName()));
        iAcctPublicService.synFailAuthBankAccount(synAccountPublicReqDTO);
    }

    @HttpService(value = "/203", method = RequestMethod.POST)
    private void bindAccountPublic(HttpRequest request, HttpResponse response) {
        BindAcctPublicReqVo bindAccountPublicReqVo = request.getBodyObject(BindAcctPublicReqVo.class);
        BindAcctPublicReqRPCDTO bindAccountPublicReqDTO = new BindAcctPublicReqRPCDTO();
        BeanUtils.copyProperties(bindAccountPublicReqVo, bindAccountPublicReqDTO);
        bindAccountPublicReqDTO.setBankAccountName(BankNameEnum.getBankEnum(bindAccountPublicReqVo.getBankAccountName()));
        BindAcctPublicRespRPCDTO bindAccountPublicRespDTO1 = iAcctPublicService.bindAccountPublic(bindAccountPublicReqDTO);
        ResponseResultUtils.success(response, bindAccountPublicRespDTO1);
    }

    @HttpService(value = "/211/{companyId}/{bankAccountNo}", method = RequestMethod.GET)
    private void queryByCompanyAndBankNo(HttpRequest request, HttpResponse response) {
        String companyId = request.getPathValue("companyId");
        String bankAccountNo = request.getPathValue("bankAccountNo");
        AcctPublicInfoRespRPCDTO accountPublicInfoRespDTO = iAcctPublicSearchService.queryByCompanyAndBankNo(companyId, BankNameEnum.ZBBANK, bankAccountNo);
        ResponseResultUtils.success(response, accountPublicInfoRespDTO);
    }

    @HttpService(value = "/300", method = RequestMethod.POST)
    private void accountPublicRecharge(HttpRequest request, HttpResponse response) {
        AcctPublicRechargeReqRPCVo accountPublicRechargeReqRPCVo = request.getBodyObject(AcctPublicRechargeReqRPCVo.class);
        AcctPublicRechargeReqRPCDTO accountPublicRechargeReqRPCDTO=new AcctPublicRechargeReqRPCDTO();
        BeanUtils.copyProperties(accountPublicRechargeReqRPCVo, accountPublicRechargeReqRPCDTO);
        accountPublicRechargeReqRPCDTO.setBankAccountName(BankNameEnum.getBankEnum(accountPublicRechargeReqRPCVo.getBankAccountName()));
        AcctPublicRechargeRespRPCDTO acctPublicRechargeRespRPCDTO = iAcctPublicTradeService.acctPublicCashRecharge(accountPublicRechargeReqRPCDTO);
        ResponseResultUtils.success(response, acctPublicRechargeRespRPCDTO);
    }

    @HttpService(value = "/301", method = RequestMethod.POST)
    private void acctPublicCashWithdrawal(HttpRequest request, HttpResponse response) {
        AcctPublicCashWithdReqVo cashWithdReqVo = request.getBodyObject(AcctPublicCashWithdReqVo.class);
        AcctPublicCashWithdReqRPCDTO cashWithdReqRPCDTO=new AcctPublicCashWithdReqRPCDTO();
        BeanUtils.copyProperties(cashWithdReqVo, cashWithdReqRPCDTO);
        cashWithdReqRPCDTO.setBankAccountName(BankNameEnum.getBankEnum(cashWithdReqVo.getBankAccountName()));
        AcctPublicCashWithdRespRPCDTO cashWithdRespDTO = iAcctPublicTradeService.acctPublicCashWithdrawal(cashWithdReqRPCDTO);
        ResponseResultUtils.success(response, cashWithdRespDTO);
    }

    @HttpService(value = "/301-1", method = RequestMethod.POST)
    private void acctPublicCancelWithdrawal(HttpRequest request, HttpResponse response) {
        AcctPublicCancelWithdrawalRPCVo cancelConsumeReqVo = request.getBodyObject(AcctPublicCancelWithdrawalRPCVo.class);
        AcctPublicCancelWithdrawalRPCDTO cancelWithdrawalRPCDTO=new AcctPublicCancelWithdrawalRPCDTO();
        BeanUtils.copyProperties(cancelConsumeReqVo, cancelWithdrawalRPCDTO);
        cancelWithdrawalRPCDTO.setBankAccountName(BankNameEnum.getBankEnum(cancelConsumeReqVo.getBankAccountName()));
        AcctPublicCashWithdRespRPCDTO acctPublicCancelWithdrawal = iAcctPublicTradeService.acctPublicCancelWithdrawal(cancelWithdrawalRPCDTO);
        ResponseResultUtils.success(response, acctPublicCancelWithdrawal);
    }

    @HttpService(value = "/302", method = RequestMethod.POST)
    private void acctPublicConsume(HttpRequest request, HttpResponse response) {
        AcctPublicConsumeReqVo cashWithdReqVo = request.getBodyObject(AcctPublicConsumeReqVo.class);
        AcctPublicConsumeReqRPCDTO consumeReqRPCDTO=new AcctPublicConsumeReqRPCDTO();
        BeanUtils.copyProperties(cashWithdReqVo, consumeReqRPCDTO);
        consumeReqRPCDTO.setBankAccountName(BankNameEnum.getBankEnum(cashWithdReqVo.getBankAccountName()));
        AcctPublicConsumeRespRPCDTO acctPublicConsumeRespRPCDTO = iAcctPublicTradeService.acctPublicConsume(consumeReqRPCDTO);
        ResponseResultUtils.success(response, acctPublicConsumeRespRPCDTO);
    }

    @HttpService(value = "/303", method = RequestMethod.POST)
    private void acctPublicCancelConsume(HttpRequest request, HttpResponse response) {
        AcctPublicCancelConsumeReqVo consumeReqRPCVo = request.getBodyObject(AcctPublicCancelConsumeReqVo.class);
        AcctPublicCancelConsumeReqRPCDTO cancelConsumeReqRPCDTO=new AcctPublicCancelConsumeReqRPCDTO();
        BeanUtils.copyProperties(consumeReqRPCVo, cancelConsumeReqRPCDTO);
        cancelConsumeReqRPCDTO.setBankAccountName(BankNameEnum.getBankEnum(consumeReqRPCVo.getBankAccountName()));
        AcctPublicCancelConsumeRespRPCDTO acctPublicConsumeRespRPCDTO = iAcctPublicTradeService.acctPublicCancelConsume(cancelConsumeReqRPCDTO);
        ResponseResultUtils.success(response, acctPublicConsumeRespRPCDTO);
    }

    @HttpService(value = "/400", method = RequestMethod.POST)
    private void searchAcctPublicFlowPage(HttpRequest request, HttpResponse response) {
        AcctPublicFlowFindRepRPCDTO consumeReqRPCVo = request.getBodyObject(AcctPublicFlowFindRepRPCDTO.class);
        ResponsePage<AcctPublicFlowFindRespRPCDTO> acctPublicFlowPage = iAcctPublicSearchService.searchAcctPublicFlowPage(consumeReqRPCVo);
        ResponseResultUtils.success(response, acctPublicFlowPage);
    }

    @HttpService(value = "/401/{accountFlowId}", method = RequestMethod.GET)
    private void searchAcctPublicFlowDetail(HttpRequest request, HttpResponse response) {
        String accountFlowId = request.getPathValue("accountFlowId");
        AcctPublicFlowDetailRespRPCDTO flowDetailRespRPCDTO = iAcctPublicSearchService.searchAcctPublicFlowDetail(accountFlowId);
        ResponseResultUtils.success(response, flowDetailRespRPCDTO);
    }

    @HttpService(value = "/402", method = RequestMethod.POST)
    private void queryAcctPublicFlowAmount(HttpRequest request, HttpResponse response) {
        AcctPublicAmountPageReqRPCDTO amountPageReqRPCDTO = request.getBodyObject(AcctPublicAmountPageReqRPCDTO.class);
        iAcctPublicSearchService.queryAcctPublicFlowAmount(amountPageReqRPCDTO);
    }

    @HttpService(value = "/501", method = RequestMethod.POST)
    private void searchAcctPublicPage(HttpRequest request, HttpResponse response) {
        AcctPublicPageReqRPCDTO consumeReqRPCVo = request.getBodyObject(AcctPublicPageReqRPCDTO.class);
        ResponsePage<AcctPublicListRespRPCDTO> acctPublicListRespRPCDTOResponsePage = iAcctPublicSearchService.searchAcctPublicPage(consumeReqRPCVo);
        ResponseResultUtils.success(response, acctPublicListRespRPCDTOResponsePage);
    }

    @HttpService(value = "/503", method = RequestMethod.POST)
    private void getCompanyName(HttpRequest request, HttpResponse response) {
        List<String> companyName = iAcctPublicSearchService.getCompanyName();
        ResponseResultUtils.success(response, companyName);
    }

    @HttpService(value = "/504", method = RequestMethod.POST)
    private void searchAcctPubli(HttpRequest request, HttpResponse response) {
        AcctPublicFlowReqRPCDTO acctPublicPageReqRPCDTO = request.getBodyObject(AcctPublicFlowReqRPCDTO.class);
        ResponsePage<AcctPublicFlowListRespRPCDTO> companyName = iAcctPublicSearchService.queryAcctPublicFlowPage(acctPublicPageReqRPCDTO);
        ResponseResultUtils.success(response, companyName);
    }

    @HttpService(value = "/5051", method = RequestMethod.POST)
    private void exportAcctPublicFlowPage(HttpRequest request, HttpResponse response) {
        AcctPublicFlowQueryReqRPCDTO acctPublicPageReqRPCDTO = request.getBodyObject(AcctPublicFlowQueryReqRPCDTO.class);
        ResponsePage<ExportAcctPublicFlowRespRPCDTO> exportAcctPublicFlowRespRPCDTOResponsePage = iAcctPublicSearchService.exportAcctPublicFlowPage(acctPublicPageReqRPCDTO);
        ResponseResultUtils.success(response, exportAcctPublicFlowRespRPCDTOResponsePage);
    }

    @HttpService(value = "/510", method = RequestMethod.POST)
    private void disableAcctPublic(HttpRequest request, HttpResponse response) {
        UpdateAcctPublicReqRPCDTO updateAcctPublicReqRPCDTO = request.getBodyObject(UpdateAcctPublicReqRPCDTO.class);
        iAcctPublicService.disableAcctPublic(updateAcctPublicReqRPCDTO);
    }

    @HttpService(value = "/511", method = RequestMethod.POST)
    private void enableAcctPublic(HttpRequest request, HttpResponse response) {
        UpdateAcctPublicReqRPCDTO updateAcctPublicReqRPCDTO = request.getBodyObject(UpdateAcctPublicReqRPCDTO.class);
        iAcctPublicService.enableAcctPublic(updateAcctPublicReqRPCDTO);
    }

    @HttpService(value = "/611", method = RequestMethod.POST)
    private void synPicAcctPublic(HttpRequest request, HttpResponse response) {
        SynPicAcctPublicReqRPCDTO picAcctPublicReqRPCDTO = request.getBodyObject(SynPicAcctPublicReqRPCDTO.class);
        iAcctPublicService.synPicAcctPublic(picAcctPublicReqRPCDTO);
    }

    //___________________支付——————————————————————————

    @HttpService(value = "/600", method = RequestMethod.POST)
    private void createAcctPublicPayTrade(HttpRequest request, HttpResponse response) {
        CashierAcctPublicPayTradeRPCVo createPayTradePPCVo = request.getBodyObject(CashierAcctPublicPayTradeRPCVo.class);
        iCashierOrderSettlementService.createAcctPublicPayTrade(createPayTradePPCVo);
    }

    @HttpService(value = "/601", method = RequestMethod.POST)
    private void refundAcctPublicTrade(HttpRequest request, HttpResponse response) {
        CashierAcctPublicRefundTradeRPCVo createPayTradePPCVo = request.getBodyObject(CashierAcctPublicRefundTradeRPCVo.class);
        iCashierOrderRefundSettlementService.refundAcctPublicTrade(createPayTradePPCVo);
    }

    /**
     * 【因私】最大化支付
     * @param request httpRequest
     * @param response httpResponse
     */
    @HttpService(value = "/602", method = RequestMethod.POST)
    private void createAndAutoPayOrderTrade(HttpRequest request, HttpResponse response) {
        CashierCreateAutoPayTradeRpcVO cashierCreateAutoPayTradeRpcVO = request.getBodyObject(CashierCreateAutoPayTradeRpcVO.class);
        iCashierOrderSettlementService.createAndAutoPayOrderTrade(cashierCreateAutoPayTradeRpcVO);
    }

    //stereo 查询

    /**
     * (HTTP Internal) 获取公司信息总览
     *
     * @param request
     * @param response
     * @return
     */
    @HttpService(value = "/99998", method = RequestMethod.POST)
    public void stereoQueryBankCardList(HttpRequest request, HttpResponse response) {
        BankCardForStereoReqDTO accountSubVo = request.getBodyObject(BankCardForStereoReqDTO.class);
        ResponsePage<BankCardForStereoRespDTO> bankCardForStereoRespDTOResponsePage = iBankCardSearchService.stereoQueryBankCardList(accountSubVo);
        ResponseResultUtils.success(response, bankCardForStereoRespDTOResponsePage);
    }

    @HttpService(value = "/99981", method = RequestMethod.POST)
    public void exportStereoBankCardFlowPage(HttpRequest request, HttpResponse response) {
        BankCardForStereoReqDTO accountSubVo = request.getBodyObject(BankCardForStereoReqDTO.class);
        List<BankExportFlowForStereoRespDTO> bankExportFlowForStereoRespDTOS = iBankCardSearchService.exportStereoBankCardFlowPage(accountSubVo);
        ResponseResultUtils.success(response, bankExportFlowForStereoRespDTOS);
    }

    @HttpService(value = "/99982", method = RequestMethod.POST)
    public void bankCardFlowForStereoRespDTOResponsePage(HttpRequest request, HttpResponse response) {
        BankCardOperationForStereoReqDTO accountSubVo = request.getBodyObject(BankCardOperationForStereoReqDTO.class);
        ResponsePage<BankCardFlowForStereoRespDTO> bankCardFlowForStereoRespDTOResponsePage = iBankCardSearchService.stereoQueryBankCardFlow(accountSubVo);
        ResponseResultUtils.success(response, bankCardFlowForStereoRespDTOResponsePage);
    }

    @HttpService(value = "/99983", method = RequestMethod.POST)
    public void stereoStatisticsBankCard(HttpRequest request, HttpResponse response) {
        BankCardStatisticsForStereoReqDTO accountSubVo = request.getBodyObject(BankCardStatisticsForStereoReqDTO.class);
        ResponsePage<BankCardStatisticsForStereoRespDTO> bankCardStatisticsForStereoRespDTOResponsePage = iBankCardSearchService.stereoStatisticsBankCard(accountSubVo);
        ResponseResultUtils.success(response, bankCardStatisticsForStereoRespDTOResponsePage);
    }

    @HttpService(value = "/test1", method = RequestMethod.POST)
    public void bankCardStairUnitName(HttpRequest request, HttpResponse response) {
        BankCardStatisticsForStereoReqDTO accountSubVo = request.getBodyObject(BankCardStatisticsForStereoReqDTO.class);
        iBankCardSearchService.bankCardStairUnitName(accountSubVo);
    }

    @HttpService(value = "/testExtractDay", method = RequestMethod.POST)
    public void testExtractDay(HttpRequest request, HttpResponse response) {}

    @HttpService(value = "/taskTest", method = RequestMethod.POST)
    public void taskTest(HttpRequest request, HttpResponse response) {
        FinhubLogger.info("taskTesttaskTesttaskTesttaskTest");
    }

    @HttpService(value = "/enable/sub/Acct", method = RequestMethod.POST)
    public void enableSubAccount(HttpRequest request, HttpResponse response) {
        AcctActiveSubReqDTO accountSubVo = request.getBodyObject(AcctActiveSubReqDTO.class);
        iAcctMgrService.enableSubAccount(accountSubVo);
    }

    @HttpService(value = "/disable/sub/Acct", method = RequestMethod.POST)
    public void disableSubAccount(HttpRequest request, HttpResponse response) {
        AcctActiveSubReqDTO accountSubVo = request.getBodyObject(AcctActiveSubReqDTO.class);
        iAcctMgrService.disableSubAccount(accountSubVo);
    }

    @HttpService(value = "/show/sub/Acct", method = RequestMethod.POST)
    public void AcctUpdateCommonReqDTO(HttpRequest request, HttpResponse response) {
        AcctUpdateCommonReqDTO accountSubVo = request.getBodyObject(AcctUpdateCommonReqDTO.class);
        iAcctMgrService.showAccountSub(accountSubVo);
    }

    @HttpService(value = "/activate/sub/Acct", method = RequestMethod.POST)
    public void activateAcctSub(HttpRequest request, HttpResponse response) {
        AcctUpdateActvateReqDTO accountSubVo = request.getBodyObject(AcctUpdateActvateReqDTO.class);
        iAcctMgrService.activateAcctSub(accountSubVo.getAcctUpdateCommonReqDTOS());
    }

    @HttpService(value = "/{accountId}", method = RequestMethod.GET)
    public void isActivateAcctSubByAcctId(HttpRequest request, HttpResponse response) {
        String accountId = request.getPathValue("accountId");
        iAcctMgrService.isActivateAcctSubByAcctId(accountId);
    }

    @HttpService(value = "/queryAcctOverview", method = RequestMethod.POST)
    public void queryAcctOverview(HttpRequest request, HttpResponse response) {
        AcctOverviewReqDTO acctOverviewReqDTO =  request.getBodyObject(AcctOverviewReqDTO.class);
        AcctOverviewRespDTO acctOverviewRespDTO = iAcctMgrService.queryAcctOverview(acctOverviewReqDTO);
        ResponseResultUtils.success(response,acctOverviewRespDTO);
    }

    @HttpService(value = "/getAllAcctByCompanyModel", method = RequestMethod.POST)
    public void getAllAcctByCompanyModel(HttpRequest request, HttpResponse response) {
        AcctByCompanyModelReqDTO acctOverviewReqDTO = request.getBodyObject(AcctByCompanyModelReqDTO.class);
        AllAcctByCompanyModelRespDTO acctOverviewRespDTO = iAcctMgrService.getAllAcctByCompanyModel(acctOverviewReqDTO);
        ResponseResultUtils.success(response,acctOverviewRespDTO);
    }

    @HttpService(value = "/testExtract", method = RequestMethod.POST)
    public void testExtract(HttpRequest request, HttpResponse response) {
        AcctExtractDaySearchReqRPCDTO queryReq = request.getBodyObject(AcctExtractDaySearchReqRPCDTO.class);
        ResponsePage<AcctExtractDaySearchRespRPCDTO> page = iAcctExtractDayService.queryByPage(queryReq);
        ResponseResultUtils.success(response, page);
    }

    @HttpService(value = "/testExtractMonth", method = RequestMethod.POST)
    public void testExtractMonth(HttpRequest request, HttpResponse response) {
        AcctExtractMonthSearchReqRPCDTO queryReq = request.getBodyObject(AcctExtractMonthSearchReqRPCDTO.class);
        ResponsePage<AcctExtractMonthSearchRespRPCDTO> page = iAcctExtractMonthService.queryByPage(queryReq);
        ResponseResultUtils.success(response, page);
    }

    @HttpService(value = "/testExtractRemark", method = RequestMethod.POST)
    public void testExtractRemark(HttpRequest request, HttpResponse response) {
        AcctBankCheckProcessReqDTO queryReq = request.getBodyObject(AcctBankCheckProcessReqDTO.class);
        iAcctBankCheckService.extractProcess(queryReq);
        ResponseResultUtils.success(response, null);
    }

    @HttpService(value = "/recharge", method = RequestMethod.POST)
    public void testRecharge(HttpRequest request, HttpResponse response) {
        AcctBankRechargeOptReqDTO acctBankRechargeOptReqDTO = request.getBodyObject(AcctBankRechargeOptReqDTO.class);
        iAcctFundMgrService.rechargeByBank(acctBankRechargeOptReqDTO);
        ResponseResultUtils.success(response, null);
    }

    @HttpService(value = "/cashWithdrawal", method = RequestMethod.POST)
    public void testCashWithdrawal(HttpRequest request, HttpResponse response) {
        AcctGeneralOptReqDTO acctGeneralOptReqDTO = request.getBodyObject(AcctGeneralOptReqDTO.class);
        iAcctFundMgrService.cashWithdrawal(acctGeneralOptReqDTO);
        ResponseResultUtils.success(response, null);
    }

    @HttpService(value = "/changePrice", method = RequestMethod.POST)
    public void testChangePrice(HttpRequest request, HttpResponse response) {
        CashierChangePriceTradeRPCVo requestVo = request.getBodyObject(CashierChangePriceTradeRPCVo.class);
        CashierChangePriceTradeRespDTO respDTO = iCashierOrderSettlementService.changePriceOrderTrade(requestVo);
        ResponseResultUtils.success(response, respDTO);
    }


    @HttpService(value = "/savePersonAccount/{employeeId}", method = RequestMethod.GET)
    public void savePersonAccount(HttpRequest request, HttpResponse response) {
        String employeeId = request.getPathValue("employeeId");
        int count = iPersonAccountService.savePersonAccount(employeeId);
        ResponseResultUtils.success(response, count);
    }

    /**
      * 不要用：风险
      */
    @HttpService(value = "/delPersonAccount/{accountId}", method = RequestMethod.GET)
    public void delPersonAccount(HttpRequest request, HttpResponse response) {
        String accountId = request.getPathValue("accountId");
        int count = iPersonAccountService.delPersonAccount(accountId);
        ResponseResultUtils.success(response, count);
    }
    @HttpService(value = "1221", method = RequestMethod.POST)
    public void roundPetty(HttpRequest request, HttpResponse response) {
        BankRefundTradeReqDTO accountSubVo = request.getBodyObject(BankRefundTradeReqDTO.class);
        creditApplyManager.roundPettyRefund(accountSubVo);
    }
    /**
     * 不要用：风险
     */
    @HttpService(value = "/kevin", method = RequestMethod.GET)
    public void kevin(HttpRequest request, HttpResponse response) {
        SaasBugetDto build = SaasBugetDto.builder()
                .companyId("5b9b767c23445f5a69d5f665")
                .employeeId("5ba1f0c423445f7a42a2d11f")
                .orderAmount(new BigDecimal("1"))
                .bizNo("123")
                .orderType(CategoryTypeEnum.BANK_INDIVIDUAL.getCode())
                //.pettyId("BYJ202112171617026834029")
                .transNo("********")
                .bankApplyCreditType(1)
                .build();
        creditApplyManager.addRefundCreditOrderNew(build);
    }

    private boolean getGlobalBudgetFlag(String companyId){
        Integer switchFlag = messageSetupService.queryCheckedByItemCode(companyId, "quota_relation_apply_switch");
        if (ObjectUtils.isEmpty(switchFlag)){
            return false;
        }
        return switchFlag.equals(NumberUtils.INTEGER_ONE);
    }

    @HttpService(value = "/white/1", method = RequestMethod.POST)
    public void testCreateWhite(HttpRequest request, HttpResponse response) {
        AcctRechargeWhiteListDTO acctRechargeWhiteListDTO = request.getBodyObject(AcctRechargeWhiteListDTO.class);
        iAcctRechargeWhiteListService.createBankWhite(acctRechargeWhiteListDTO);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/white/2", method = RequestMethod.POST)
    public void testQueryWhite(HttpRequest request, HttpResponse response) {
        AcctRechargeWhiteReqDTO acctRechargeWhiteReqDTO = request.getBodyObject(AcctRechargeWhiteReqDTO.class);
        List<AcctRechargeWhiteListDTO> acctRechargeWhiteListDTOS = iAcctRechargeWhiteListService.queryWhiteListByPage(acctRechargeWhiteReqDTO);
        Integer count = iAcctRechargeWhiteListService.queryWhiteListCount(acctRechargeWhiteReqDTO);
        JSONObject result = new JSONObject();
        result.put("data", acctRechargeWhiteListDTOS);
        result.put("count", count);
        ResponseResultUtils.success(response, result);
    }

    @HttpService(value = "/white/delete/{id}", method = RequestMethod.POST)
    public void testDeleteWhite(HttpRequest request, HttpResponse response) {
        String id = request.getPathValue("id");
        iAcctRechargeWhiteListService.deleteBankWhite(Integer.valueOf(id));
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/white/3", method = RequestMethod.POST)
    public void testQueryRechargeCompanyName(HttpRequest request, HttpResponse response) {
        String keyword = request.getParameter("keyword");
        List<String> strings = iAcctRechargeWhiteListService.queryRechargeCompanyName(keyword);
        ResponseResultUtils.success(response, strings);
    }

    @HttpService(value = "/queryAllAcctByCompanyId", method = RequestMethod.POST)
    public void queryAllAcctByCompanyId(HttpRequest request, HttpResponse response) {
        String companyId = request.getParameter("companyId");
        List<AccountGeneralRespDTO> respDTOS = iAccountGeneralService.queryAllAcctByCompanyId(companyId);
        List<JSONObject> resultList = new ArrayList<>();
        respDTOS.stream().forEach(acctGeneral -> {
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(acctGeneral));

            String acctMainShowName = acctGeneral.getCompanyMainName() + " "
                    + BankNameShowConfig.makeBankMainShowName(acctGeneral.getBankName(), acctGeneral.getBankAccountNo(), acctGeneral.getAccountModel());
            jsonObject.put("accountMainShowName", acctMainShowName);
            resultList.add(jsonObject);
        });
        ResponseResultUtils.success(response, resultList);
    }

    @HttpService(value = "/virtual/card/cgb/bind/account/{bankname}/{bankaccountno}", method = RequestMethod.POST)
    public void cgbBindAccount(HttpRequest request, HttpResponse response) {
        String bankName = request.getPathValue("bankname");
        String bankAccountNo = request.getPathValue("bankaccountno");
        applyCardManager.bindAccountToCompanyBankAccount(bankName,bankAccountNo);
        ResponseResultUtils.success(response);
    }

    /**
     * 虚拟卡和企业银行账户绑定
     * @param request
     * @param response
     */
    @HttpService(value = "/virtual/card/cgb/unbind/account/{bankname}/{bankaccountno}", method = RequestMethod.POST)
    public void cgbUnbindAccount(HttpRequest request, HttpResponse response) {
        String bankName = request.getPathValue("bankname");
        String bankAccountNo = request.getPathValue("bankaccountno");
        applyCardManager.unbindAccountToCompanyBankAccount(bankName,bankAccountNo);
        ResponseResultUtils.success(response);
    }

    @HttpService(value = "/reimbursement/consume", method = RequestMethod.POST)
    private void createAndPayTrade4Reimbursement(HttpRequest request, HttpResponse response) {
        CashierPayTrade4ReimbursementRPCVO cashierPayTrade4ReimbursementRPCVO = request.getBodyObject(CashierPayTrade4ReimbursementRPCVO.class);
        iCashierOrderSettlementService.createAndPayTrade4Reimbursement(cashierPayTrade4ReimbursementRPCVO);
    }

    @HttpService(value = "/reimbursement/count/{bankaccountno}", method = RequestMethod.POST)
    private void reimbursementCount(HttpRequest request, HttpResponse response) {
        String bankAccountNo = request.getPathValue("bankaccountno");
        acctReimbursementCountService.subtractCount(bankAccountNo,"CITIC");
    }

    @HttpService(value = "/reimbursement/refund", method = RequestMethod.POST)
    private void refund4Reimbursement(HttpRequest request, HttpResponse response) {
        CashierRefundTrade4ReimbursementRPCVO cashierRefundTrade4ReimbursementRPCVO = request.getBodyObject(CashierRefundTrade4ReimbursementRPCVO.class);
        iCashierOrderRefundSettlementService.refund4Reimbursement(cashierRefundTrade4ReimbursementRPCVO);
    }

    /**
     * 清洗数据
     */
    /**
     * 将平安企业开户信息迁移到AcctCompanyBindCard表中 <平安银行支持绑定多张卡，AcctCompanyBindCard存储多个关系>
     */
    @HttpService(value = "/clean/companyMain/bindCard")
    public void cleanCompanyMainToBindCard(HttpRequest request, HttpResponse response) {
        Map<String, Object> resultMap = Maps.newHashMap();
        // 1、查询余额账户为平安银行的记录
        Example example = new Example(AccountGeneral.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bankName", BankNameEnum.SPABANK.getCode());
        List<AccountGeneral> accountGenerals = accountGeneralMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(accountGenerals)) {
            ResponseResultUtils.success(response, "暂无平安银行开户的余额账户");
        }
        Set<String> mainSet = accountGenerals.stream().map(AccountGeneral::getCompanyMainId).collect(Collectors.toSet());
        List<AcctCompanyMain> mainList = uAcctCompanyMainService.findByMainIds(mainSet);
        if (CollectionUtils.isEmpty(mainList)) {
            ResponseResultUtils.success(response, "companyMainId集合为空");
            return;
        }

        List<String> existAcctCompanyMainList = Lists.newArrayList();
        List<String> saveAcctCompanyMainList = Lists.newArrayList();
        for (AcctCompanyMain acctCompanyMain : mainList) {
            List<AcctCompanyBindCard> acctCompanyBindCards = acctCompanyBindCardService.queryByCompanyMainId(acctCompanyMain.getCompanyMainId());
            if (CollectionUtils.isEmpty(acctCompanyBindCards)) {
                AcctCompanyBindCard acctCompanyBindCard = new AcctCompanyBindCard();
                acctCompanyBindCard.setCompanyId(acctCompanyMain.getCompanyId());
                acctCompanyBindCard.setCompanyName(acctCompanyMain.getCompanyName());
                acctCompanyBindCard.setCompanyMainId(acctCompanyMain.getCompanyMainId());
                acctCompanyBindCard.setBankCode(acctCompanyMain.getBankCode());
                acctCompanyBindCard.setBankCardName(acctCompanyMain.getBankCardName());
                acctCompanyBindCard.setBankBrnNo(acctCompanyMain.getBankBrnNo());
                acctCompanyBindCard.setBankBatchName(acctCompanyMain.getBankBatchName());
                acctCompanyBindCard.setBankCardNo(acctCompanyMain.getBankCardNo());
                acctCompanyBindCard.setBankProvince(acctCompanyMain.getBankProvince());
                acctCompanyBindCard.setBankCity(acctCompanyMain.getBankCity());
                acctCompanyBindCard.setBusinessName(acctCompanyMain.getBusinessName());
                acctCompanyBindCard.setStatus(AcctCompanyBindCardStatusEnum.BIND.getCode());
                acctCompanyBindCardService.saveAcctCompanyBindCard(acctCompanyBindCard);
                saveAcctCompanyMainList.add(acctCompanyMain.getCompanyMainId());
                FinhubLogger.warn("cleanCompanyMainToBindCard清洗数据，写入， companyMainId {}", acctCompanyMain.getCompanyMainId());
            } else {
                existAcctCompanyMainList.add(acctCompanyMain.getCompanyMainId());
                FinhubLogger.warn("cleanCompanyMainToBindCard清洗数据，已存在， companyMainId {}", acctCompanyMain.getCompanyMainId());
            }
        }
        resultMap.put("existAcctCompanyMainList", existAcctCompanyMainList);
        resultMap.put("saveAcctCompanyMainList", saveAcctCompanyMainList);
        ResponseResultUtils.success(response, resultMap);
    }

    /**
     * 维护历businessName字段
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/cleanCompanyMainToBindCardByBusinessName")
    private void cleanCompanyMainToBindCardByBusinessName(HttpRequest request, HttpResponse response) {
        Map<String, List<Long>> resultMap = acctCompanyBindCardService.cleanCompanyMainToBindCardByBusinessName();
        ResponseResultUtils.success(response, resultMap);
    }

    @Autowired
    private BankAcctService bankAcctService;
    /**
     * 清洗平台账户流水对手账户名称
     *
     * @param request
     * @param response
     */
    @HttpService(value = "/cleanBankAcctFlowTargetBankAccountName", method = RequestMethod.GET)
    private void cleanBankAcctFlowTargetBankAccountName(HttpRequest request, HttpResponse response) {
        Map<String, Object> resultMap = Maps.newHashMap();
        // 对手方展示成fbt,查询余额账户为空
        List<Long> fbtAccountGeneralEmptyList = Lists.newArrayList();
        // 根据主体ID展示对手账户，未获取到主体信息， (只打印挂账91、挂账认款92、挂账退款93、挂账认款失败94、认款撤销95)
        List<Long> acctCompanyMainEmptyList = Lists.newArrayList();
        // 根据主体ID展示对手账户，未获取到主体信息， (排除挂账91、挂账认款92、挂账退款93、挂账认款失败94、认款撤销95)
        List<Long> acctCompanyMainEmptyRemoveList = Lists.newArrayList();
        // 是FBT但是对手账户号未空
        List<Long> fbtTargetBankAccountNoEmptyList = Lists.newArrayList();
        List<Integer> opTypeList = Arrays.asList(FundPlatAcctOptType.CREDIT_CHARGE.getKey(), FundPlatAcctOptType.CREDIT_SUBSCRIP.getKey(),
                FundPlatAcctOptType.CREDIT_REFUND.getKey(), FundPlatAcctOptType.CREDIT_SUBSCRIP_FAIL.getKey(), FundPlatAcctOptType.CREDIT_SUBSCRIP_CANCEL.getKey());
        Long minId = Long.parseLong(request.getParameter("minId"));
        Long maxId = Long.parseLong(request.getParameter("maxId"));
        List<BankAcctFlow> bankAcctFlowList = bankAcctFlowService.queryByRangeId(minId, maxId);
        if (CollectionUtils.isEmpty(bankAcctFlowList)) {
            ResponseResultUtils.success(response, "没查询流水数据--输入的范围区间不对， 请调整后重新再试！！！");
            return;
        }

        Map<String, String> companyMainMap = new HashMap<>();
        List<String> companyMainIds = bankAcctFlowList.stream().filter(x -> ObjUtils.isNotEmpty(x.getTargetCompanyMainId()))
                .map(BankAcctFlow::getTargetCompanyMainId).collect(Collectors.toList());
        if (ObjUtils.isNotEmpty(companyMainIds)) {
            List<AcctCompanyMain> acctCompanyMains = uAcctCompanyMainService.findByMainIds(companyMainIds);
            if(CollectionUtils.isNotEmpty(acctCompanyMains)){
                companyMainMap = acctCompanyMains.stream().collect(Collectors.toMap(AcctCompanyMain::getCompanyMainId, AcctCompanyMain::getBusinessName, (v1, v2) -> v1));
            }
        }

        for (BankAcctFlow flow : bankAcctFlowList) {
            BankAcctFlow newFlow = new BankAcctFlow();
            newFlow.setId(flow.getId());

            if (ObjUtils.isNotEmpty(flow.getTargetBankAccountName())) {
               continue;
            }
            // FBT
            if (ObjUtils.isEmpty(newFlow.getTargetBankAccountName()) && BankNameEnum.isFbt(flow.getTargetBankName()) && ObjUtils.isNotEmpty(flow.getTargetBankAccountNo())) {
                AccountGeneral accountGeneral = accountGeneralService.findByBank(BankNameEnum.FBT.getCode(), flow.getTargetBankAccountNo());
                if (ObjUtils.isEmpty(accountGeneral)) {
                    // 记录数据
                    fbtAccountGeneralEmptyList.add(flow.getId());
                } else {
                    newFlow.setTargetBankAccountName(accountGeneral.getCompanyMainName());
                }
            }
            // 一般户提现
            if (ObjUtils.isEmpty(newFlow.getTargetBankAccountName()) &&  Objects.equals(flow.getOperationType(), FundPlatAcctOptType.WITHDRAWAL.getKey())) {
                BankAcct bankAcct = bankAcctService.queryByAcctIdAndName(flow.getBankAcctId(), flow.getTargetBankName());
                if (ObjUtils.isNotEmpty(bankAcct)) {
                    newFlow.setTargetBankAccountName(bankAcct.getCompanyMainName());
                } else {
                    FinhubLogger.warn("cleanBankAcctFlowTargetBankAccountName bankAcct is null flow {}", JsonUtils.toJson(flow));
                }
            }
            // 主体ID
            if ( ObjUtils.isEmpty(newFlow.getTargetBankAccountName()) && ObjUtils.isNotEmpty(flow.getTargetCompanyMainId())) {
                if (!companyMainMap.containsKey(flow.getTargetCompanyMainId())) {
                    // 记录数据
                    if (opTypeList.contains(flow.getOperationType())) {
                        acctCompanyMainEmptyList.add(flow.getId());
                    } else {
                        acctCompanyMainEmptyRemoveList.add(flow.getId());
                    }
                } else {
                    newFlow.setTargetBankAccountName(companyMainMap.get(flow.getTargetCompanyMainId()));
                }
            }

            // 默认
            if (ObjUtils.isEmpty(newFlow.getTargetBankAccountName())) {
                newFlow.setTargetBankAccountName(flow.getTargetBankName());

                // 记录下可能是异常数据
                if (BankNameEnum.isFbt(flow.getTargetBankName()) && ObjUtils.isEmpty(flow.getTargetBankAccountNo())) {
                    fbtTargetBankAccountNoEmptyList.add(flow.getId());
                }
            }
            // 更新
            bankAcctFlowService.updateByIdSelective(newFlow);
        }

        resultMap.put("fbtAccountGeneralEmptyList 对手方展示成fbt,查询余额账户为空", fbtAccountGeneralEmptyList);
        resultMap.put("acctCompanyMainEmptyList 根据主体ID展示对手账户，未获取到主体信息(挂账)", acctCompanyMainEmptyList);
        resultMap.put("acctCompanyMainEmptyRemoveList 根据主体ID展示对手账户，未获取到主体信息(非挂账)", acctCompanyMainEmptyRemoveList);
        resultMap.put("fbtTargetBankAccountNoEmptyList 是FBT但是对手账户号未空", fbtTargetBankAccountNoEmptyList);
        ResponseResultUtils.success(response, resultMap);

    }

    @HttpService(value = "/changeCompanyName", method = RequestMethod.POST)
    private void changeCompanyName(HttpRequest request, HttpResponse response) {
        AccountChangeNameReqDTO accountChangeNameReqDTO = request.getBodyObject(AccountChangeNameReqDTO.class);
        boolean b = iAccountGeneralService.changeCompanyName(accountChangeNameReqDTO);
        System.out.println(b);
    }

    @HttpService(value = "/changeAccountMainType", method = RequestMethod.POST)
    private void changeAccountMainType(HttpRequest request, HttpResponse response) {
        Map<String, String> map = request.getBodyObject(Map.class);

        String companyId = map.get("companyId");
        String bankName = map.get("bankName");
        String bankAccountNo = map.get("bankaccountno");
        boolean b = iAcctMgrService.changeAccountMainType(companyId, bankName, bankAccountNo);
        System.out.println(b);
    }

    @HttpService(value = "/rocket/test", method = RequestMethod.GET)
    public void rocketSendMq(HttpRequest request, HttpResponse response) throws Exception {
        Message message = new Message(topic,"aaaaa".getBytes("utf-8"));
        SendResult result = rocketMQProducer.getDefaultMQProducer().send(message);
        log.info(JsonUtils.toJson(result));
    }

    @HttpService(value = "/dataCleanCreditDisTributeInfo", method = RequestMethod.POST)
    public void dataCleanCreditDisTributeInfo(HttpRequest request, HttpResponse response) {
        Map<String, String> map = request.getBodyObject(Map.class);
        String id = map.get("id");
        String applyTransNo = map.get("applyTransNo");
        iBankCardCreditApplyService.dataCleanCreditDisTributeInfo(id,applyTransNo);
        ResponseResultUtils.success(response, "success");
    }

    /**
     * 从apply同步字段到distribute
     * @param request
     * @param response
     */
    @HttpService(value = "/clean/distributeInfo", method = RequestMethod.POST)
    public void updateCreditDisTributeInfo(HttpRequest request, HttpResponse response) {
        Map<String, String> map = request.getBodyObject(Map.class);
        String id = map.get("id");
        String applyTransNo = map.get("applyTransNo");
        iBankCardCreditApplyService.updateCreditDisTributeInfo(id,applyTransNo);
        ResponseResultUtils.success(response, "success");
    }

    /**平安退回额度手动触发*/
    @HttpService(value = "/spaRefundCredit", method = RequestMethod.GET)
    public void spaRefundCredit(HttpRequest request, HttpResponse response) throws Exception {
        AccountCardRefundAmountReqDTO accountCardRefundAmountReqDTO = request.getBodyObject(AccountCardRefundAmountReqDTO.class);
        BankCard bankCard = bankCardManager.selectUsingBankCard(accountCardRefundAmountReqDTO.getCompanyId(),accountCardRefundAmountReqDTO.getOperationUserId(),accountCardRefundAmountReqDTO.getBankName());
        BankTradeRespDto bankTradeRespDto = bankCardFundFlowService.refundCredit(accountCardRefundAmountReqDTO ,accountCardRefundAmountReqDTO.getBizNo(),bankCard);
        log.info("spaRefundCredit"+JsonUtils.toJson(bankTradeRespDto));
    }

    @Autowired
    private CashierBankCardPayRefundManager cashierBankCardPayRefundManager ;
    /**平安手动退款*/
    @HttpService(value = "/refund2Card", method = RequestMethod.POST)
    public void refund2Card(HttpRequest request, HttpResponse response) throws Exception {
        BankUserCardOptDTO bankUserCardOptDTO = request.getBodyObject(BankUserCardOptDTO.class);
        BankUserCardOptReqDTO bankUserCardOptReqDTO = CopyUtils.convert(bankUserCardOptDTO,BankUserCardOptReqDTO.class);
        cashierBankCardPayRefundManager.refundBankCardAndPayTest(bankUserCardOptReqDTO);
    }


    @HttpService(value = "/zbswitch", method = RequestMethod.POST)
    public void zbswitch(HttpRequest request, HttpResponse response) {
        try {
            Map<String, String> map = request.getBodyObject(Map.class);
            if (map.containsKey(BankConfigUtil.ZBBANK_SWTICH)) {
                if ("true".equalsIgnoreCase(map.get(BankConfigUtil.ZBBANK_SWTICH))) {
                    this.redisTemplate.opsForValue().set(BankConfigUtil.ZBBANK_SWTICH, "1");
                    FinhubLogger.error("fenbei-pay 众邦切换开关变更：{} = true", BankConfigUtil.ZBBANK_SWTICH);
                } else if ("false".equalsIgnoreCase(map.get(BankConfigUtil.ZBBANK_SWTICH))) {
                    this.redisTemplate.delete(BankConfigUtil.ZBBANK_SWTICH);
                    FinhubLogger.error("fenbei-pay 众邦切换开关变更：{} = false", BankConfigUtil.ZBBANK_SWTICH);
                }
            }
            String addKey = BankConfigUtil.ZBBANK_SWTICH_COMPANY + "_ADD";
            if (map.containsKey(addKey) && !StringUtils.isBlank(map.get(addKey))) {
                String companyId = map.get(addKey);
                this.redisTemplate.opsForHash().put(BankConfigUtil.ZBBANK_SWTICH_COMPANY, companyId, "1");
                FinhubLogger.error("fenbei-pay 众邦切换公司开关变更：{} = true", companyId);
            }
            String removeKey = BankConfigUtil.ZBBANK_SWTICH_COMPANY + "_REMOVE";
            if (map.containsKey(removeKey) && !StringUtils.isBlank(map.get(removeKey))) {
                String companyId = map.get(removeKey);
                this.redisTemplate.opsForHash().delete(BankConfigUtil.ZBBANK_SWTICH_COMPANY, companyId);
                FinhubLogger.error("fenbei-pay 众邦切换公司开关变更：{} = false", companyId);
            }
            String oppoAddKey = BankConfigUtil.ZBBANK_SWTICH_OPPO_COMPANY + "_ADD";
            if (map.containsKey(oppoAddKey) && !StringUtils.isBlank(map.get(oppoAddKey))) {
                String companyId = map.get(oppoAddKey);
                this.redisTemplate.opsForHash().put(BankConfigUtil.ZBBANK_SWTICH_OPPO_COMPANY, companyId, "1");
                FinhubLogger.error("fenbei-pay 众邦切换反向公司开关变更：{} = true", companyId);
            }
            String oppoRemoveKey = BankConfigUtil.ZBBANK_SWTICH_OPPO_COMPANY + "_REMOVE";
            if (map.containsKey(oppoRemoveKey) && !StringUtils.isBlank(map.get(oppoRemoveKey))) {
                String companyId = map.get(oppoRemoveKey);
                this.redisTemplate.opsForHash().delete(BankConfigUtil.ZBBANK_SWTICH_OPPO_COMPANY, companyId);
                FinhubLogger.error("fenbei-pay 众邦切换反向公司开关变更：{} = false", companyId);
            }
            ResponseResultUtils.success(response, "success");
        } catch (Exception e) {
            FinhubLogger.error("fenbei-pay 众邦切换开关接口调用异常：{}，errormsg：{}", request.getBody(), e.getMessage(), e);
        }
    }

    @HttpService(value = "/zbswitch/query", method = RequestMethod.GET)
    public void zbswitchQuery(HttpRequest request, HttpResponse response) {
        try {
            Map<String, Object> map = Maps.newHashMap();
            map.put(BankConfigUtil.ZBBANK_SWTICH, String.valueOf(this.redisTemplate.hasKey(BankConfigUtil.ZBBANK_SWTICH)));
            map.put(BankConfigUtil.ZBBANK_SWTICH_COMPANY, this.redisTemplate.opsForHash().keys(BankConfigUtil.ZBBANK_SWTICH_COMPANY));
            map.put(BankConfigUtil.ZBBANK_SWTICH_OPPO_COMPANY, this.redisTemplate.opsForHash().keys(BankConfigUtil.ZBBANK_SWTICH_OPPO_COMPANY));
            ResponseResultUtils.success(response, map);
        } catch (Exception e) {
            FinhubLogger.error("fenbei-pay 众邦切换开关查询接口调用异常，errormsg：{}", e.getMessage(), e);
        }
    }


    /**
     * 清洗数据
     * 将中信企业开户信息迁移到AcctCompanyBindCard表中 <中信银行支持绑定多张卡，AcctCompanyBindCard存储多个关系>
     */
    @HttpService(value = "/citic/bindCard")
    public void cleanCompanyMainToBindCard4Citic(HttpRequest request, HttpResponse response) {
        Map<String, String> map = request.getBodyObject(Map.class);
        if (map.containsKey("companyMainId")){
            String companyMainId = map.get("companyMainId");
            List<AcctCompanyBindCard> acctCompanyBindCards = acctCompanyBindCardService.queryByCompanyMainId(companyMainId);
            if (CollectionUtils.isEmpty(acctCompanyBindCards)) {
                AcctCompanyBindCard acctCompanyBindCard = new AcctCompanyBindCard();
                acctCompanyBindCard.setCompanyId(map.get("companyId"));
                acctCompanyBindCard.setCompanyName(map.get("companyName"));
                acctCompanyBindCard.setCompanyMainId(map.get("companyMainId"));
                acctCompanyBindCard.setBankCode(map.get("bankCode"));
                acctCompanyBindCard.setBankCardName(map.get("bankCardName"));
                acctCompanyBindCard.setBankBrnNo(map.get("bankBrnNo"));
                acctCompanyBindCard.setBankBatchName(map.get("bankBatchName"));
                acctCompanyBindCard.setBankCardNo(map.get("bankCardNo"));
                acctCompanyBindCard.setBankProvince(map.get("bankProvince"));
                acctCompanyBindCard.setBankCity(map.get("bankCity"));
                acctCompanyBindCard.setBusinessName(map.get("businessName"));
                acctCompanyBindCard.setStatus(AcctCompanyBindCardStatusEnum.BIND.getCode());
                Boolean saveFlag = acctCompanyBindCardService.saveAcctCompanyBindCard(acctCompanyBindCard);
                ResponseResultUtils.success(response, saveFlag);
            }
        }else {
            ResponseResultUtils.success(response, false);
        }
    }

    @HttpService(value = "/airwallex/switch",method = RequestMethod.GET)
    public void airwallexSwitch(HttpRequest request,HttpResponse response) {
        String redisValue = "AIRWALLEX:CASHIER";
        String req = BankCgbConstant.ON;
        redisTemplate.opsForValue().set(redisValue,req,TIME_OUT_TEN, TimeUnit.DAYS);

        String onOff = (String) redisTemplate.opsForValue().get(redisValue);
        FinhubLogger.info("airwallex redis开关：result={}",onOff);
        ResponseResultUtils.success(response, onOff);
    }

    @HttpService(value = "/spa/limit",method = RequestMethod.POST)
    public void spaLimit(HttpRequest request,HttpResponse response){
        SpaBankAcctLimitReqVO spaBankAcctLimitReqVO = request.getBodyObject(SpaBankAcctLimitReqVO.class);
        iBankSpaTradeService.syncTrade(spaBankAcctLimitReqVO);
    }
}