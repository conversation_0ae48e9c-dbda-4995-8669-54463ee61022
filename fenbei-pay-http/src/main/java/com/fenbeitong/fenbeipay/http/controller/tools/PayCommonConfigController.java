package com.fenbeitong.fenbeipay.http.controller.tools;

import org.springframework.beans.factory.annotation.Autowired;

import com.fenbeitong.fenbeipay.awplus.config.PayCommonConfigBiz;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.config.PayCommonConfig;
import com.fenbeitong.finhub.common.utils.ResponseResultUtils;
import com.luastar.swift.http.route.RequestMethod;
import com.luastar.swift.http.server.HttpRequest;
import com.luastar.swift.http.server.HttpResponse;
import com.luastar.swift.http.server.HttpService;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2023-03-13 04:31:56 
*/
@HttpService("/internal/config")
public class PayCommonConfigController {

	@Autowired
	private PayCommonConfigBiz payCommonConfigBiz;
	
	@HttpService(value = "/save/config", method = RequestMethod.POST)
	public void saveConfig(HttpRequest request, HttpResponse response) {
		PayCommonConfig config = request.getBodyObject(PayCommonConfig.class);
		boolean done = payCommonConfigBiz.saveConfig(config);
		ResponseResultUtils.success(response, done);
	}
	
	@HttpService(value = "/query/config")
	public void queryConfig(HttpRequest request, HttpResponse response) {
		String key = request.getParameter("key");
		PayCommonConfig config = payCommonConfigBiz.queryConfig(key);
		ResponseResultUtils.success(response, config);
	}
	
	@HttpService(value = "/enable/config")
	public void enableConfig(HttpRequest request, HttpResponse response) {
		String key = request.getParameter("key");
		PayCommonConfig config = payCommonConfigBiz.queryConfig(key);
		ResponseResultUtils.success(response, config);
	}
	
	@HttpService(value = "/disable/config")
	public void disableConfig(HttpRequest request, HttpResponse response) {
		String key = request.getParameter("key");
		PayCommonConfig config = payCommonConfigBiz.queryConfig(key);
		ResponseResultUtils.success(response, config);
	}
}
