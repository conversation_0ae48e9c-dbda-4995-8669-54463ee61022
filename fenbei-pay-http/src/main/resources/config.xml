<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <properties resource="classpath:props/swift.properties"/>
    <properties resource="classpath:props/db.properties"/>
    <properties resource="classpath:props/api.properties"/>
    <!--<properties resource="classpath:props/kms.properties"/>-->
    <properties resource="classpath*:props/message.properties"/>
    <properties resource="classpath*:props/logstash.properties"/>
    <properties resource="classpath*:props/rocketmq.properties"/>
    <properties resource="classpath:i18n/messages.properties"/>
    <properties resource="classpath:i18n/messages_en_US.properties"/>
    <properties resource="classpath:i18n/messages_zh_CN.properties"/>
</configuration>