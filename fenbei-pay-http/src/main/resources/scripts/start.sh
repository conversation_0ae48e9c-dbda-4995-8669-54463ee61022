#!/bin/bash

#JMX_PORT=`expr $1 + 500`
JAVA_OPTS="-server -Xms${fbp.http.java.opts.xms}m -Xmx${fbp.http.java.opts.xmx}m -Xmn${fbp.http.java.opts.xmn}m -XX:MaxPermSize=${fbp.http.java.opts.mps}m \
-Xss1m -XX:+UseConcMarkSweepGC \
-XX:+UseParNewGC -XX:CMSFullGCsBeforeCompaction=5 \
-XX:+UseCMSCompactAtFullCollection \
-XX:+PrintGC -Xloggc:/data/logs/${APP}/gc_$1.log"
#-Djava.rmi.server.hostname=************** \
#-Dcom.sun.management.jmxremote \
#-Dcom.sun.management.jmxremote.port=$JMX_PORT \
#-Dcom.sun.management.jmxremote.authenticate=false \
#-Dcom.sun.management.jmxremote.ssl=false"

SCRIPT_HOME=$(dirname $(readlink -f $0))
PROJECT_HOME=$(dirname ${SCRIPT_HOME})

pid=`ps -eo pid,args | grep $1 | grep java | grep -v grep | awk '{print $1}'`

if [ -n "$pid" ]
then
    kill -3 ${pid}
    kill ${pid} && sleep 3
    if [  -n "`ps -eo pid | grep $pid`" ]
    then
        kill -9 ${pid}
    fi
    echo "-----kill pid: ${pid}-----"
fi

echo "-----project start at $1-----"
java -Dport=$1 ${JAVA_OPTS} -Duser.language=zh -Duser.region=CN -cp ${PROJECT_HOME}/conf:${PROJECT_HOME}/lib/* com.fenbeitong.fenbeipay.http.HttpBootstrap $1 > /dev/null 2>&1 &