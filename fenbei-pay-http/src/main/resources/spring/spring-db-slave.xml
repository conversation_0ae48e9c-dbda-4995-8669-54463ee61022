<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd"
>

    <import resource="classpath*:spring/spring-kms.xml"/>

    <!-- fenbei-pay slave 数据源 -->
    <bean id="slaveDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="driverClassName" value="${db.slave.fenbeipay.driver}"/>
        <property name="url" value="${db.slave.fenbeipay.url}"/>
        <property name="username" value="${db.slave.fenbeipay.username}"/>
        <property name="password" value="${db.slave.fenbeipay.password}"/>
        <property name="minIdle" value="${db.slave.fenbeipay.minIdle}"/>
        <property name="maxActive" value="${db.slave.fenbeipay.maxActive}"/>
        <property name="maxWait" value="${db.slave.fenbeipay.maxWait}"/>
        <!-- 超过时间限制是否回收 -->
        <property name="removeAbandoned" value="${db.slave.fenbeipay.removeAbandoned}"/>
        <!-- 超过时间限制多长； -->
        <property name="removeAbandonedTimeout" value="${db.slave.fenbeipay.removeAbandonedTimeout}"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${db.slave.fenbeipay.timeBetweenEvictionRunsMillis}"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${db.slave.fenbeipay.minEvictableIdleTimeMillis}"/>
        <!-- 用来检测连接是否有效的sql，要求是一个查询语句-->
        <property name="validationQuery" value="${db.slave.fenbeipay.validationQuery}"/>
        <!-- 申请连接的时候检测 -->
        <property name="testWhileIdle" value="${db.slave.fenbeipay.testWhileIdle}"/>
        <!-- 申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能 -->
        <property name="testOnBorrow" value="${db.slave.fenbeipay.testOnBorrow}"/>
        <!-- 归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能  -->
        <property name="testOnReturn" value="${db.slave.fenbeipay.testOnReturn}"/>
    </bean>


    <!-- fenbei-pay 事务管理器 -->
    <!--<bean id="fenbeipayTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">-->
        <!--<property name="dataSource" ref="fenbeipayMasterDataSource"/>-->
        <!--<qualifier value="fenbeipay"/>-->
    <!--</bean>-->

    <!--  fenbei-pay MyBatis配置 -->
    <bean id="slaveSqlSessionFactory" class="org.mybatis.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="slaveDataSource"/>
        <!--指定mybatis-config文件-->
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
        <!-- 自动扫描entity目录, 省掉mybatis-config.xml里的手工配置 -->
        <property name="typeAliasesPackage" value="com.fenbeitong.fenbeipay.dto.redcoupon"/>
        <!-- 指定mapper.xml文件位置 -->
        <property name="mapperLocations" value="classpath:mapper/search/*.xml"/>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <props>
                            <!--方言：-->
                            <prop key="helperDialect">mysql</prop>
                        </props>
                    </property>
                </bean>
            </array>
        </property>
    </bean>

    <!-- 扫描basePackage下所有接口-->
    <bean id="slaveMapperScannerConfigurer" class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.fenbeitong.pay.search.db.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="slaveSqlSessionFactory"/>
    </bean>

    <!-- 使用注解定义事务 -->
    <!--<tx:annotation-driven transaction-manager="fenbeipayTransactionManager" proxy-target-class="true"/>-->


    <!-- 使用cglib实现aop动态代理 -->
    <!--<aop:aspectj-autoproxy proxy-target-class="true"/>-->

</beans>