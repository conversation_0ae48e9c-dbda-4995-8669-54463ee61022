<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd"
>

    <!--<import resource="classpath*:spring/spring-kms.xml"/>-->

    <!-- fenbei-pay slave 数据源 -->
    <bean id="psqlDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="driverClassName" value="${db.fenbeitong1.driver}"/>
        <property name="url" value="${db.fenbeitong1.url}"/>
        <property name="username" value="${db.fenbeitong1.username}"/>
        <property name="password" value="${db.fenbeitong1.password}"/>
        <!--下方属性属于 druid ，也用 slave 的配置-->
        <!-- 初始化数量 -->
        <property name="initialSize" value="${db.slave.fenbeipay.initialSize}"/>
        <!--最大活跃数-->
        <property name="maxActive" value="${db.fenbeitong1.maxActive}"/>
        <!--最大连接等待超时时间-->
        <property name="maxWait" value="${db.slave.fenbeipay.maxWait}"/>
        <!-- 超过时间限制是否回收 -->
        <property name="removeAbandoned" value="${db.slave.fenbeipay.removeAbandoned}"/>
        <!-- 超过时间限制多长； -->
        <property name="removeAbandonedTimeout" value="${db.slave.fenbeipay.removeAbandonedTimeout}"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${db.slave.fenbeipay.timeBetweenEvictionRunsMillis}"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${db.slave.fenbeipay.minEvictableIdleTimeMillis}"/>
        <!-- 用来检测连接是否有效的sql，要求是一个查询语句-->
        <property name="validationQuery" value="${db.slave.fenbeipay.validationQuery}"/>
        <!-- 申请连接的时候检测 -->
        <property name="testWhileIdle" value="${db.slave.fenbeipay.testWhileIdle}"/>
        <!-- 申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能 -->
        <property name="testOnBorrow" value="${db.slave.fenbeipay.testOnBorrow}"/>
        <!-- 归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能  -->
        <property name="testOnReturn" value="${db.slave.fenbeipay.testOnReturn}"/>
    </bean>

    <!--  fenbei-pay MyBatis配置 -->
    <bean id="pSqlSessionFactory" class="org.mybatis.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="psqlDataSource"/>
        <!--指定mybatis-config文件-->
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
        <!-- 自动扫描entity目录, 省掉mybatis-config.xml里的手工配置 -->
        <property name="typeAliasesPackage" value="com.fenbeitong.fenbeipay.dto.flow"/>
        <!-- 指定mapper.xml文件位置 -->
        <property name="mapperLocations" value="classpath:mapper/searchflow/*.xml"/>
    </bean>

    <!-- 扫描basePackage下所有接口-->
    <bean id="psqlMapperScannerConfigurer" class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.fenbeitong.pay.search.dbf.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="pSqlSessionFactory"/>
    </bean>

</beans>