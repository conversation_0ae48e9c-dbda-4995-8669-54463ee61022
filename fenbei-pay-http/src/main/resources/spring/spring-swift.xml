<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.2.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.2.xsd"
       default-lazy-init="true">

    <description>swift配置</description>

    <!-- HttpService服务发现 -->
    <context:component-scan base-package="com.fenbeitong.fenbeipay" use-default-filters="false">
        <context:include-filter type="annotation" expression="com.luastar.swift.http.server.HttpService"/>
    </context:component-scan>


    <context:annotation-config/>

    <!-- http 路由 -->
    <bean class="com.luastar.swift.http.route.HttpHandlerMapping">
        <property name="exceptionHandler">
            <bean class="com.fenbeitong.finhub.common.exception.DefaultHttpExceptionHandler"/>
        </property>
    </bean>

    <!-- 通用token拦截器-->
    <bean id="userCenterAuthInterceptor" class="com.luastar.swift.http.route.MappedInterceptor">
        <property name="excludePatterns">
            <list>
                <value>/internal/**/*</value>
                <value>/cashier/pay/callback/v2/*</value>
                <value>/fbp/payment/callback/v1</value>
                <value>/fbp/payment/refund/queryStatus/v1</value>
                <value>/fbp/payment/fbp/updateNoticeStatus/v1</value>
                <value>/fbp/payment/refund/schedule/v1</value>

                <value>/fbp/test/payBiz</value>
                <!--<value>/pay/**</value>-->
                <!--<value>/vouchers/management/execute_vouchers_grant_or_recovery_task/v2</value>
                <value>/vouchers/management/vouchers_writeoff_task/v2</value>-->
                <!-- 对账跳过登陆               -->
                <value>/fbp/account/check</value>
                <value>/fbp/account/lfbank/check</value>
                <value>/fbp/account/lianlian/check</value>
                <!-- 用户换新设备登录时校验支付密码，跳过登录 -->
                <value>/sas/login/confirm/pay/pwd/v4</value>
            </list>
        </property>
        <property name="interceptor">
            <bean class="com.fenbeitong.finhub.auth.interceptor.UserCenterAuthInterceptor"/>
        </property>
    </bean>

    <!-- 外部拦截器，拦截web和APP中请求过来的额URL-->
    <bean id="appLoginInterceptor" class="com.luastar.swift.http.route.MappedInterceptor">
        <property name="excludePatterns">
            <list>
                <value>/internal/**/*</value>
                <value>/cashier/pay/callback/v2/*</value>
                <value>/fbp/payment/callback/v1</value>
                <value>/fbp/payment/refund/queryStatus/v1</value>
                <value>/fbp/payment/refund/schedule/v1</value>

                <value>/fbp/test/payBiz</value>
                <!--<value>/pay/**</value>-->
                <!--<value>/vouchers/management/execute_vouchers_grant_or_recovery_task/v2</value>
                <value>/vouchers/management/vouchers_writeoff_task/v2</value>-->
                <!-- 对账跳过登陆               -->
                <value>/fbp/account/check</value>
                <value>/fbp/account/lfbank/check</value>
                <value>/fbp/account/lianlian/check</value>
                <!-- 用户换新设备登录时校验支付密码，跳过登录 -->
                <value>/sas/login/confirm/pay/pwd/v4</value>
            </list>
        </property>
        <property name="interceptor">
                <bean class="com.fenbeitong.fenbeipay.http.interceptor.AppLoginInterceptor"/>
        </property>
    </bean>

    <!-- 外部拦截器，拦截集团版url 鉴权-->
    <bean id="groupWebInterceptor" class="com.luastar.swift.http.route.MappedInterceptor">
        <property name="interceptor">
            <bean class="com.fenbeitong.fenbeipay.http.interceptor.GroupWebInterceptor"/>
        </property>

    </bean>

    <!-- 用户权限切面 -->
    <bean id="userAccessAspect" class="com.fenbeitong.finhub.common.auth.aop.UserAccessAspect"/>

    <!--  多语言拦截器  -->
    <bean id="localeInterceptor" class="com.luastar.swift.http.route.MappedInterceptor">
        <property name="includePatterns">
            <list>
                <value>/sas/**/*</value>
            </list>
        </property>
        <property name="interceptor">
            <bean class="com.luastar.swift.i18n.interceptor.LocaleInterceptor" />
        </property>
    </bean>


    <!-- 普通消息 生产者 -->
    <bean id="rocketMQProducer" class="com.fenbeitong.fenbeipay.core.service.rocket.RocketMQProducer" init-method="init" destroy-method="destroy" scope="singleton">
        <property name="namesrvAddr" value="${rocketmq.nameserAddr}" />
        <property name="producerGroup" value="${rocketmq.producer.group}" />
        <property name="sendTimeOut" value="${rocketmq.producer.sendMsgTimeout}" />
        <property name="retryTimes" value="${rocketmq.producer.retryTimesWhenSendFailed}" />
    </bean>

    <!-- properties配置文件 -->
    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:props/db.properties</value>
                <value>classpath:props/redis.properties</value>
                <value>classpath:props/mongo.properties</value>
                <value>classpath:props/api.properties</value>
                <value>classpath:props/consumer.properties</value>
                <value>classpath:props/producer.properties</value>
                <value>classpath:props/logstash.properties</value>
                <value>classpath:props/kms.properties</value>
<!--                <value>classpath:props/oss.properties</value>-->
                <value>classpath:props/rocketmq.properties</value>
            </list>
        </property>
    </bean>

    <import resource="classpath:spring/spring-fenbeipay.xml"/>
    <import resource="classpath:dubbo/dubbo-application.xml"/>
    <import resource="classpath:spring/spring-finhub-oss.xml"/>
</beans>