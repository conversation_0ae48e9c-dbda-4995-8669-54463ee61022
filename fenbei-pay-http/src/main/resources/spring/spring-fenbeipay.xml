<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.2.xsd
        http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd"
       default-lazy-init="true">

    <description>fenbeipay-http配置</description>


    <cache:annotation-driven/>

    <!-- 普通服务发现 -->
    <context:component-scan base-package="com.fenbeitong"/>

    <import resource="classpath*:spring/spring-core.xml"/>
    <import resource="classpath*:spring/spring-bigdata.xml"/>
    <import resource="classpath*:spring/spring-redis.xml"/>
    <import resource="classpath*:spring/spring-redisson.xml"/>
    <import resource="classpath*:spring/spring-data-mongo.xml"/>
    <import resource="classpath*:spring/spring-db.xml"/>
    <import resource="classpath*:spring/spring-db-slave.xml"/>
    <import resource="classpath*:spring/spring-db-psql.xml"/>

</beans>