<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.2.xsd

        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.2.xsd"
>

    <!-- <import resource="classpath*:spring/spring-kms.xml"/>-->

    <!-- fenbei-pay Master数据源 -->
    <bean id="fenbeipayMasterDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="driverClassName" value="${db.master.fenbeipay.driver}"/>
        <property name="url" value="${db.master.fenbeipay.url}"/>
        <property name="username" value="${db.master.fenbeipay.username}"/>
        <property name="password" value="${db.master.fenbeipay.password}"/>
        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="${db.master.fenbeipay.initialSize}"/>
        <property name="minIdle" value="${db.master.fenbeipay.minIdle}"/>
        <property name="maxActive" value="${db.master.fenbeipay.maxActive}"/>
        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="${db.master.fenbeipay.maxWait}"/>
        <!-- 超过时间限制是否回收 -->
        <property name="removeAbandoned" value="${db.master.fenbeipay.removeAbandoned}"/>
        <!-- 超过时间限制多长； -->
        <property name="removeAbandonedTimeout" value="${db.master.fenbeipay.removeAbandonedTimeout}"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${db.master.fenbeipay.timeBetweenEvictionRunsMillis}"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${db.master.fenbeipay.minEvictableIdleTimeMillis}"/>
        <!-- 用来检测连接是否有效的sql，要求是一个查询语句-->
        <property name="validationQuery" value="${db.master.fenbeipay.validationQuery}"/>
        <!-- 申请连接的时候检测 -->
        <property name="testWhileIdle" value="${db.master.fenbeipay.testWhileIdle}"/>
        <!-- 申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能 -->
        <property name="testOnBorrow" value="${db.master.fenbeipay.testOnBorrow}"/>
        <!-- 归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能  -->
        <property name="testOnReturn" value="${db.master.fenbeipay.testOnReturn}"/>

        <property name="proxyFilters">
            <list>
                <ref bean="wall-filter"/>
            </list>
        </property>
        <!--监控-->
        <!-- 配置监控统计拦截的filters，去掉后监控界面sql无法统计 -->
        <property name="filters" value="stat,wall,log4j"/>

        <!--
        如果配置了proxyFilters，此配置可以不配置
        druid.stat.mergeSql=true 合并执行的相同sql，避免因为参数不同而统计多条sql语句
        druid.stat.slowSqlMillis=10000 用来配置SQL慢的标准，执行时间超过slowSqlMillis的就是慢  -->
        <property name="connectionProperties" value="druid.stat.mergeSql=true;druid.stat.slowSqlMillis=10000"/>

        <!-- 监控统计拦截的filters -->
        <!-- 并在filters属性中配置了log4j
        <property name="proxyFilters">
            <list>
                <ref bean="stat-filter" />
                <ref bean="log-filter" />
            </list>
        </property>-->
    </bean>

    <!-- 从库数据源 -->
    <bean id="fenbeipaySlaveDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="driverClassName" value="${ds.slave.fenbeipay.driver}"/>
        <property name="url" value="${ds.slave.fenbeipay.url}"/>
        <property name="username" value="${ds.slave.fenbeipay.username}"/>
        <property name="password" value="${ds.slave.fenbeipay.password}"/>
        <property name="minIdle" value="${db.slave.fenbeipay.minIdle}"/>
        <property name="maxActive" value="${db.slave.fenbeipay.maxActive}"/>
        <property name="maxWait" value="${db.slave.fenbeipay.maxWait}"/>
        <!-- 超过时间限制是否回收 -->
        <property name="removeAbandoned" value="${db.slave.fenbeipay.removeAbandoned}"/>
        <!-- 超过时间限制多长； -->
        <property name="removeAbandonedTimeout" value="${db.slave.fenbeipay.removeAbandonedTimeout}"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${db.slave.fenbeipay.timeBetweenEvictionRunsMillis}"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${db.slave.fenbeipay.minEvictableIdleTimeMillis}"/>
        <!-- 用来检测连接是否有效的sql，要求是一个查询语句-->
        <property name="validationQuery" value="${db.slave.fenbeipay.validationQuery}"/>
        <!-- 申请连接的时候检测 -->
        <property name="testWhileIdle" value="${db.slave.fenbeipay.testWhileIdle}"/>
        <!-- 申请连接时执行validationQuery检测连接是否有效，配置为true会降低性能 -->
        <property name="testOnBorrow" value="${db.slave.fenbeipay.testOnBorrow}"/>
        <!-- 归还连接时执行validationQuery检测连接是否有效，配置为true会降低性能  -->
        <property name="testOnReturn" value="${db.slave.fenbeipay.testOnReturn}"/>
    </bean>

    <!-- 动态数据源 -->
    <bean id="dynamicDataSource" class="com.fenbeitong.fenbeipay.core.datasource.DynamicDataSource">
        <property name="targetDataSources">
            <map>
                <entry key="slave" value-ref="fenbeipaySlaveDataSource"></entry>
            </map>
        </property>
        <property name="defaultTargetDataSource" ref="fenbeipayMasterDataSource"></property>
    </bean>

    <bean id="wall-filter" class="com.alibaba.druid.wall.WallFilter">
        <property name="dbType" value="mysql"/>
        <property name="config" ref="wall-config"/>
    </bean>

    <bean id="wall-config" class="com.alibaba.druid.wall.WallConfig">
        <!-- 批量sql -->
        <property name="multiStatementAllow" value="true"/>
    </bean>
    <!-- fenbei-pay 事务管理器 -->
    <bean id="fenbeipayTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dynamicDataSource"/>
        <qualifier value="fenbeipay"/>
    </bean>

    <!--  fenbei-pay MyBatis配置 -->
    <bean id="fenbeipaySqlSessionFactory" class="org.mybatis.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="dynamicDataSource"/>
        <!--指定mybatis-config文件-->
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
        <!-- 自动扫描entity目录, 省掉mybatis-config.xml里的手工配置 -->
        <property name="typeAliasesPackage" value="com.fenbeitong.fenbeipay.dto.na,
                                                   com.fenbeitong.fenbeipay.dto.nf,
                                                   com.fenbeitong.fenbeipay.dto.bank,
                                                   com.fenbeitong.fenbeipay.dto.sas,
                                                   com.fenbeitong.fenbeipay.dto.acctdech,
                                                   com.fenbeitong.fenbeipay.dto.gw,
                                                   com.fenbeitong.fenbeipay.core.model.po.fenbeitong,
                                                   com.fenbeitong.fenbeipay.dto.accountpublic,
                                                   com.fenbeitong.fenbeipay.dto.extract"/>
        <!-- 指定mapper.xml文件位置 -->
        <property name="mapperLocations" value="classpath*:mapper/fenbeitong/mysql/**/*.xml"/>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <props>
                            <!--方言：-->
                            <prop key="helperDialect">mysql</prop>
                        </props>
                    </property>
                </bean>
            </array>
        </property>
    </bean>

    <!-- 扫描basePackage下所有接口-->
    <bean id="fenbeipayMapperScannerConfigurer" class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.fenbeitong.fenbeipay.na.db.mapper,
                                            com.fenbeitong.fenbeipay.nf.db.mapper,
                                            com.fenbeitong.fenbeipay.redcoupon.db.mapper,
                                            com.fenbeitong.fenbeipay.bank.base.db.mapper,
                                            com.fenbeitong.fenbeipay.sas.pwd.base.db.mapper,
                                            com.fenbeitong.fenbeipay.acctdech.db.mapper,
                                            com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql,
                                            com.fenbeitong.fenbeipay.gw.db.mapper,
                                            com.fenbeitong.fenbeipay.acctpublic.base.db.mapper,
                                            com.fenbeitong.fenbeipay.extract.db.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="fenbeipaySqlSessionFactory"/>
    </bean>

    <!-- 使用注解定义事务 -->
    <tx:annotation-driven transaction-manager="fenbeipayTransactionManager" proxy-target-class="true"/>


    <!-- 使用cglib实现aop动态代理 -->
    <aop:aspectj-autoproxy proxy-target-class="true" expose-proxy="true"/>

</beans>