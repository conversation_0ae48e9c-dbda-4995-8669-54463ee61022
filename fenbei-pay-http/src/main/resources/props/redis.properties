#å°å
redis.hostName=${redis.hostName}
#ç«¯å£å·
redis.port=${redis.port}
#å¯ç 
redis.password=${redis.password}
#æ°æ®åº0-15
redis.database=${redis.database}

redis.address=${redis.address}

#æå¤§åéçå¯¹è±¡æ°
redis.pool.maxTotal=2000
#æå¤§è½å¤ä¿æidelç¶æçå¯¹è±¡æ°
redis.pool.maxIdle=1000
#è¶æ¶æ¶é´
redis.pool.maxWaitMillis=30000
#å¤é¿æ¶é´æ£æ¥ä¸æ¬¡è¿æ¥æ± ä¸­ç©ºé²çè¿æ¥
redis.pool.timeBetweenEvictionRunsMillis=30000
#ç©ºé²è¿æ¥å¤é¿æ¶é´åä¼è¢«æ¶å
redis.pool.minEvictableIdleTimeMillis=30000
#å½è°ç¨borrow Objectæ¹æ³æ¶ï¼æ¯å¦è¿è¡æææ§æ£æ¥
redis.pool.testOnBorrow=true

#ç¼ç æ ¼å¼
redis.encode=utf-8
#ç¼å­è¿ææ¶é´(ç§)  1000*60*60*24*7 ä¸å¤©
redis.expire=604800000
#æ¯å¦å¼å¯Redisæå¡åºç¨
redis.unlock=false
redis.fbb.task.grant.top=${redis.fbb.task.grant.top}
redis.fbb.task.recall.top=${redis.fbb.task.recall.top}
redis.voucher.task.grant.top=${redis.voucher.task.grant.top}
redis.voucher.task.recovery.top=${redis.voucher.task.recovery.top}
redis.voucher.task.task.top=${redis.voucher.task.task.top}
redis.voucher.task.details.top=${redis.voucher.task.details.top}
