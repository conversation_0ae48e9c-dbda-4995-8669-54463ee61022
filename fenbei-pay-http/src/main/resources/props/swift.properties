#swfitéç½®æä»¶è·¯å¾
swift.config.location=classpath:spring/spring-swift.xml
#è¶æ¶æ¶é´
swift.timeout=600
#æå¤§åå®¹é¿åº¦(1000MB)
swift.maxContentLength=104857600
#å®éåå®¹é¿åº¦ï¼è¿åresponseï¼
swift.maxLogLength=1048576
#swfitéç½®æä»¶è·¯å¾
#ååçº¿ç¨æ°ï¼å¦æåªæä¸ä¸ªç«¯å£ï¼å»ºè®®è®¾ç½®æ1
swift.bossThreads=1
#å·¥ä½çº¿ç¨æ°ï¼å¦æè®¾ç½®æ0ï¼åä¸ºcpuä¸ªæ° * 2
swift.workerThreads=16

#ä¸å¡çº¿ç¨æ° è¿ä¸ªè®¾ç½®ä¸å¤ç´æ¥ å ä¸ºè¿ä¸æ¯å®éçº¿ç¨æ° Swiftè¿åäºè®¡ç® è¿æ¯æå°å¼ï¼æå¤§å¼ä¸º æå°çº¿ç¨æ° * 4
swift.businessThreads=32
