##############################
# db fenbeitong
##############################
db.fenbeitong.driver=org.postgresql.Driver
db.fenbeitong.url=${db.fenbeitong.url}
db.fenbeitong.username=${db.fenbeitong.username}
db.fenbeitong.password=${db.fenbeitong.password}
db.fenbeitong.connectionTimeout=30000
db.fenbeitong.idleTimeout=600000
db.fenbeitong.maxLifetime=1800000
db.fenbeitong.minimumIdle=20
db.fenbeitong.maximumPoolSize=100

##############################
# db fenbeitong for flow export
db.fenbeitong1.onOff=${db.fenbeitong1.onOff}
##############################
db.fenbeitong1.driver=${db.fenbeitong1.driver}
db.fenbeitong1.url=${db.fenbeitong1.url}
db.fenbeitong1.username=${db.fenbeitong1.username}
db.fenbeitong1.password=${db.fenbeitong1.password}
db.fenbeitong1.maxActive=5


##############################
# db master fenbei-pay
##############################
db.master.fenbeipay.driver=com.mysql.jdbc.Driver
db.master.fenbeipay.url=${db.master.fenbeipay.url}
db.master.fenbeipay.username=${db.master.fenbeipay.username}
db.master.fenbeipay.password=${db.master.fenbeipay.password}
db.master.fenbeipay.initialSize=1
db.master.fenbeipay.minIdle=1
db.master.fenbeipay.maxActive=50
db.master.fenbeipay.maxWait=60000
db.master.fenbeipay.removeAbandoned=true
db.master.fenbeipay.removeAbandonedTimeout=180
db.master.fenbeipay.timeBetweenEvictionRunsMillis=60000
db.master.fenbeipay.minEvictableIdleTimeMillis=300000
db.master.fenbeipay.validationQuery=SELECT 1
db.master.fenbeipay.testWhileIdle=true
db.master.fenbeipay.testOnBorrow=false
db.master.fenbeipay.testOnReturn=false

db.billflow.driverClassName=org.postgresql.Driver
db.billflow.jdbcUrl=${db.billflow.jdbcUrl}
db.billflow.username=${db.billflow.username}
db.billflow.password=${db.billflow.password}
db.billflow.connectionTimeout=${db.billflow.connectionTimeout}
db.billflow.idleTimeout=${db.billflow.idleTimeout}
db.billflow.maxLifetime=${db.billflow.maxLifetime}
db.billflow.minimumIdle=${db.billflow.minimumIdle}
db.billflow.maximumPoolSize=${db.billflow.maximumPoolSize}


##############################
# db slave fenbei-pay
##############################initialSize
db.slave.fenbeipay.driver=com.mysql.jdbc.Driver
db.slave.fenbeipay.url=${db.slave.fenbeipay.url}
db.slave.fenbeipay.username=${db.slave.fenbeipay.username}
db.slave.fenbeipay.password=${db.slave.fenbeipay.password}
db.slave.fenbeipay.initialSize=1
db.slave.fenbeipay.minIdle=1
db.slave.fenbeipay.maxActive=50
db.slave.fenbeipay.maxWait=60000
db.slave.fenbeipay.removeAbandoned=true
db.slave.fenbeipay.removeAbandonedTimeout=180
db.slave.fenbeipay.timeBetweenEvictionRunsMillis=60000
db.slave.fenbeipay.minEvictableIdleTimeMillis=300000
db.slave.fenbeipay.validationQuery=SELECT 1
db.slave.fenbeipay.testWhileIdle=true
db.slave.fenbeipay.testOnBorrow=false
db.slave.fenbeipay.testOnReturn=false

## slave?????
ds.slave.fenbeipay.url=${ds.slave.fenbeipay.url}
ds.slave.fenbeipay.username=${ds.slave.fenbeipay.username}
ds.slave.fenbeipay.password=${ds.slave.fenbeipay.password}
ds.slave.fenbeipay.driver=com.mysql.jdbc.Driver

