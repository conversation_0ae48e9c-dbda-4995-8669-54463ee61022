#kafka æ¶è´¹èéç½®
########################################
# æ ¹æ®éè¦ä¿®æ¹èªå·± local éç½®æä»¶ç groupId #
########################################
bootstrap.servers=${bootstrap.servers}
key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
value.deserializer=org.apache.kafka.common.serialization.StringDeserializer
group.id=${bootstrap.group.id}
pool.timeout=30000
consumer.concurrency=2
