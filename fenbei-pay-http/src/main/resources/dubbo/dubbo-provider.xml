<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
    http://code.alibabatech.com/schema/dubbo
    http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 声明需要暴露的服务接口 -->
    <!--个人账户-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.personaccount.IPersonAccountService"  retries="0"
                   ref="iPersonAccountService" owner="fengjie.ren"/>
    <!--公司账户 stop by renfj 2019年03月30日17:03:15
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.account.IAccountService"
                   ref="iAccountService" owner="fengjie.ren"/>-->
    <!--收银台-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderRefundSettlementService" retries="0"
                   ref="iCashierOrderRefundSettlementService" owner="fengjie.ren"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementService" retries="0"
                   ref="iCashierOrderSettlementService" owner="fengjie.ren"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderMultipleSettlementService"
                   retries="0"
                   ref="iCashierOrderMultipleSettlementService" owner="fengjie.ren"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierSearchOrderService" retries="0"
                   ref="iCashierSearchOrderService" owner="fengjie.ren"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierSearchOrderRefundService" retries="0"
                   ref="iCashierSearchOrderRefundService" owner="fengjie.ren"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierThirdPayBillService" retries="0"
                   ref="iCashierThirdPayBillService" owner="hl"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.PaymentAgentService" retries="0"
                   ref="paymentAgentService" owner="ztz"/>

    <!--公司主账户-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountGeneralService"
                   ref="iAccountGeneralService" owner="fengjie.ren"/>
    <!--公司子账户-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountSubService"
                   ref="iAccountSubService" owner="fengjie.ren"/>

    <!--公司主账户流水-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountGeneralFlowService"
                   ref="iAccountGeneralFlowService" owner="fengjie.ren"/>
    <!--公司子账户流水-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountSubFlowService"
                   ref="iAccountSubFlowService" owner="fengjie.ren"/>

    <!--个人分贝券-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.voucher.IVouchersPersonService" retries="0"
                   ref="iVouchersPersonService" owner="ga.zhang"/>
    <!--分贝券模板-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTempletService" retries="0"
                   ref="iVouchersTempletService" owner="ga.zhang"/>
    <!--分贝券任务-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTaskService" retries="0"
                   ref="iVouchersTaskService" owner="ga.zhang"/>
    <!--分贝券对接任务-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.external.IExternalVoucherRpcService" retries="0"
                   ref="iExternalVoucherRpcService" owner="huixiang.yu"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankAccountService" retries="0"
                   ref="iBankAccountService" owner="wh"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService" retries="0"
                   ref="iBankCardSearchService" owner="wh"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankCardTrapRpcService" retries="0"
                   ref="iBankCardTrapRpcService" owner="wh"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankCardModifyService" retries="0"
                   ref="iBankCardModifyService" owner="wh"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankOrderPayService" retries="0"
                   ref="iBankOrderPayService" owner="wh"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankOrderRefundService" retries="0"
                   ref="iBankOrderRefundService" owner="wh"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankAcctLimitService" retries="0"
                   ref="iBankAcctLimitService"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchService" retries="0"
                   ref="iAcctPublicSearchService" owner="wh"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicService" retries="0"
                   ref="iAcctPublicService" owner="wh"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicTradeService" retries="0"
                   ref="iAcctPublicTradeService" owner="wh"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponSearchService" retries="0"
                   ref="iAccountRedcouponSearchService" owner="ga.zhang"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.redcoupon.IAccountRedcouponService" retries="0"
                   ref="iAccountRedcouponService" owner="fengjie.ren"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankPettySearchService" retries="0"
                   ref="iBankPettySearchService" owner="ga.zhang"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountSearchService" retries="0"
                   ref="iAccountSearchService" owner="wh"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountReceiveService" retries="0"
                   ref="iAccountReceiveService" owner="zgl"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountSpaInnerService" retries="0"
                   ref="iAccountSpaInnerService" owner="hl" />

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountComponentService" retries="0"
                   ref="iAccountComponentService" owner="mc" />

    <!--支付网关-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.gateway.IAcctCompanyGatewayService" retries="0"
                   ref="iAcctCompanyGatewayService" owner="renfj"/>

    <!--升级开关-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctComUpdateService" retries="0"
                   ref="iAcctComUpdateService" owner="renfj"/>

    <!--企业切换白名单-->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctCompanySwitchService" retries="0"
                   ref="iAcctCompanySwitchService" owner="wh"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.flow.IAcctFlowService" retries="0"
                   ref="iAcctFlowService" owner="wh"/>

    <!--账户管理类操作、开户权限 -->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctMgrService" retries="0"
                   ref="iAcctMgrService" owner="zxw"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctModifyService" retries="0"
                   ref="iAcctModifyService" owner="zxw"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctFundMgrService" retries="0"
                   ref="iAcctFundMgrService" owner="renfj"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctAppPayService" retries="0"
                   ref="iAcctAppPayService" owner="zxw"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctCompanyCardService" retries="0"
                   ref="iAcctCompanyCardService" owner="renfj"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.trade.IAcctGuaranteeService" retries="0"
                   ref="iAcctGuaranteeService" owner="renfj"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.IAcctReimbursementMgrService" retries="0"
                   ref="iAcctReimbursementMgrService"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchDechService" retries="0"
                   ref="iAcctPublicSearchDechService" owner="zxw"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.GroupAcctTransferService" retries="0"
                   ref="groupAcctTransferService" owner="ztz"/>
                   
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.trade.AcctOverseaCardService" retries="0"
                   ref="acctOverseaCardService" owner="ztz"/> 

    <!--企业账户对账 -->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractDayService" retries="0"
                   ref="iAcctExtractDayService" owner="zxw"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.extract.IAcctExtractMonthService" retries="0"
                   ref="iAcctExtractMonthService" owner="zxw"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.extract.IAcctBankCheckService" retries="0"
                   ref="iAcctBankCheckService" owner="zxw"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.extract.AccountCheckService" retries="0"
                   ref="accountCheckService" owner="ztz"/>

    <!--银行账户管理 -->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.IBankAcctService" retries="0"
                   ref="iBankAcctService" owner="zxw"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.AccountCompanyService" retries="0"
                   ref="accountCompanyService" owner="tongzheng.zhang"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.auth.CompanyAuthService" retries="0"
                   ref="companyAuthService" owner="tongzheng.zhang"/>

    <!--银行账户管理 -->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.saturn.IAutoAcctCheckingQueryService" retries="0"
                   ref="iAutoAcctCheckingQueryServiceImpl" owner="zxw"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.differ.IBankDifferHanderRecordService" retries="0"
                   ref="iBankDifferHanderRecordService" owner="hl"/>


    <!--虚拟卡额度申请单 -->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankCardCreditApplyService" retries="0"
                   ref="iBankCardCreditApplyService" owner="zhaozhao.zhang"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.mgr.ISpaInnerAcctService" retries="0"
                   ref="iSpaInnerAcctService" owner="hl"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.sas.IPayPwdService" retries="0"
                   ref="iPayPwdService" owner="zyc"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acctdech.IAcctRechargeWhiteListService" retries="0"
                   ref="iAcctRechargeWhiteListService" owner="liyi"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.cgb.IBankCgbAccountService" retries="0"
                   ref="iBankCgbAccountService" owner="hl"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acct.search.IAcctInfoSearchService" retries="0"
                   ref="iAcctInfoSearchService" owner="hl"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankCardApplySearchService" retries="0"
                   ref="iBankCardApplySearchService" owner="dj"/>

    <!--员工收款账户明细 -->
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.na.IAccountDetailService"
                   ref="iAccountDetailService" owner="zhc"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acctdech.IAcctGeneralService" retries="0"
                   ref="iAcctGeneralService" owner="ggx"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.extract.IPersonAcctBankCheckRPCService" retries="0"
                   ref="iPersonAcctBankCheckRPCService" owner="ggx"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.extract.IAcctVcardDiffProcessService" retries="0"
                   ref="iAcctVcardDiffProcessService" owner="wcj"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.extract.IPersonAcctExtractDayRPCService" retries="0"
                   ref="iPersonAcctExtractDayRPCService" owner="ggx"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acctdech.IAcctCompanyBindCardService" retries="0"
                   ref="iAcctCompanyBindCardService" owner="hyq"/>

    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementV2Service" retries="0"
                   ref="iCashierOrderSettlementV2Service" owner="hl"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.newcount.IReceiveAccountService" retries="0"
                   ref="iReceiveAccountService" owner="adl"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementLimitRequestService" retries="0"
                   ref="iCashierOrderSettlementLimitRequestService" owner="ljw"/>


    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchV2Service" retries="0"
                   ref="iBankCardSearchV2Service" owner="hl"/>
    <dubbo:service interface="com.fenbeitong.fenbeipay.api.service.acctdech.IBankSpaTradeService" retries="0"
                   ref="iBankSpaTradeService" owner="hl"/>
</beans>
