<?xml version="1.0" encoding="UTF-8" ?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
    <!-- 消费方应用名，用于计算依赖关系，不是匹配条件，不要与提供方一样 -->
    <!-- 提供方应用信息，用于计算依赖关系 qos是Dubbo的在线运维命令-->
    <!--<dubbo:protocol accesslog="true" />-->
    <dubbo:application name="cashier_dubbo" logger="slf4j">
        <dubbo:parameter key="qos.enable" value="false"/>
        <dubbo:parameter key="qos.accept.foreign.ip" value="false"/>
        <dubbo:parameter key="qos.port" value="33333"/>
    </dubbo:application>
    <!-- 使用zookeeper注册中心暴露服务地址 -->
    <dubbo:registry protocol="zookeeper" address="${zookeeper.host}"/>

    <!--<dubbo:protocol name="dubbo" port="${dubbo.port}"/>-->
    <!--<dubbo:provider delay="-1" timeout="${dubbo.timeout}" filter="providerExceptionFilter,-exception"  retries="0"/>-->
    <!-- 用dubbo port=-1 随机可用端口 -->
    <dubbo:provider delay="-1" timeout="${dubbo.timeout}" retries="0" port="-1" filter="finhubRpcProviderFilter,-exception"/>
    <dubbo:consumer timeout="${dubbo.timeout}" retries="0" check="false" filter="finhubRpcConsumerFilter" />

    <import resource="classpath*:dubbo/dubbo-provider.xml"/>
    <import resource="classpath*:dubbo/dubbo-consumer.xml"/>
    <import resource="classpath*:application-meta-sdk.xml"/>

</beans>
