<?xml version="1.0" encoding="UTF-8" ?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!--user-center RPC，如果增加了UC的RPC服务，请在fenbei-pay-rec的dubbo-consumer.xml中也对应增加-->
    <!--<dubbo:reference interface="com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService"
                     id="iBaseEmployeeExtService" check="false"/>-->
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.virtualCard.IVirtualCardConfigRpcService"
                     id="iVirtualCardConfigRpcService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService"
                     id="IPrivilegeService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.common.ICommonService"
                     id="iCommonService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.ICompanyService"
                     id="iCompanyService" check="false"/>
                     
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.group.IGroupManagementService"
    				id="groupManagementService" check="false"/>
                     
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.orgunit.IOrgUnitService"
                     id="iOrgUnitService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.log.IOperateLogService"
                     id="iOperateLogService" check="false"/>

    <dubbo:reference id="iRPrivilegeService"
                     interface="com.fenbeitong.usercenter.api.service.privilege.IRPrivilegeService" timeout="5000" check="false" />

    <dubbo:reference id="rpcCompanyCalendarService"
                     interface="com.fenbeitong.usercenter.api.service.calendar.RpcCompanyCalendarService" timeout="5000" check="false" />

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService"
                     id="iMessageSetupService" check="false"/>



    <!--fenbei-pay 即作为Provider有作为Consumer,id不能与Spring 注解中的一样，否则Duplicate spring bean id-->
    <dubbo:reference interface="com.fenbeitong.fenbeipay.api.service.cashier.ICashierOrderSettlementService"
                     id="iCashierOrderSettlementServiceConsumer" check="false"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.ICompanyRuleService"
                     id="iCompanyRuleService" check="false"/>
    <!--验证码-->
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.captcha.ICaptchaService"
                     id="iCaptchaService" check="false"/>


    <!--fenbei-activity 异步通知，超时时间可以长-->
    <dubbo:reference interface="com.fenbeitong.activity.api.service.fbb.service.lottery.IFbbLotteryActivityService"
                     timeout="6000"
                     id="iFbbLotteryActivityService" check="false"/>

    <!--调用银行 异步通知，超时时间可以长-->
    <dubbo:reference interface="com.fenbeitong.dech.api.service.IBankTradeService"
                     id="iBankTradeService" check="false"/>
                     
    <dubbo:reference interface="com.fenbeitong.dech.api.service.airwallex.AirwallexAuthPayService"
                     id="airwallexAuthPayService" check="false"/>
                     
    <!--调用银行 查询账户余额-->
    <dubbo:reference interface="com.fenbeitong.dech.api.service.IBankSearchService"
                     id="iBankSearchService" check="false"/>
    <!--调用中信银行-->
    <dubbo:reference interface="com.fenbeitong.dech.api.service.IZxBankAccountService"
                     id="iZxBankAccountService" check="false"/>
    <!--调用中信银行-->
    <dubbo:reference interface="com.fenbeitong.acctperson.api.service.search.IBankUserCardSearchService"
                     id="iBankUserCardSearchService" check="false"/>
    <!--调用中信银行-->
    <dubbo:reference interface="com.fenbeitong.acctperson.api.service.trade.IBankUserCardTradeService"
                     id="iBankUserCardTradeService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.acctperson.api.service.search.IReimbursePersonCardSearchService"
                     id="iReimbursePersonCardSearchService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.fenbei.settlement.external.api.api.bill.IBillOpenApi" id="iBillOpenApi" check="false"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.employee.IREmployeeService" id="iREmployeeService"
                     check="false"/>

    <dubbo:reference interface="com.fenbeitong.config.api.invoice.InvoiceConfigService" id="invoiceConfigService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.config.api.config.SysConfigItemCompanyService" id="sysConfigItemCompanyService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.virtualCard.TbBankOrderRpcService"
                     id="tbBankOrderRpcService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.budget.IVirtualCardBudgetService"
                     id="iVirtualCardBudgetService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.dech.api.service.IBankTradeFlowSearchService"
                     id="iBankTradeFlowSearchService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.finance.IOrderCostService"
                     id="iOrderCostService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.dech.api.service.ICgbVirtualCardService"
                     id="iCgbVirtualCardService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.dech.api.service.ICgbAccountService"
                     id="iCgbAccountService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.dech.api.service.ISpaBankSearchService"
                     id="iSpaBankSearchService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.finance.IFinanceCostService"
                     id="iFinanceCostService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.apply.IApplyOrderService"
                     id="iApplyOrderService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.harmony.third.examine.api.ITencentFaceRecognitionService"
                     id="iTencentFaceRecognitionService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.bank.api.service.IBankInterBankCodeService"
                     id="iBankInterBankCodeService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.bank.api.service.IBankPublicPaymentService"
                     id="iBankPublicPaymentService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.dech.api.service.IZbBankAccountService"
                     id="iZbBankAccountService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.dech.api.service.ISpdSearchService"
                     id="iSpdSearchService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.bank.api.service.IBankPaymentService" id="iBankPaymentService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.cost.IFinanceCostInfoDetailRpcService"
                     id="iFinanceCostInfoDetailRpcService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.group.IGroupPrivilegeService" id="iGroupPrivilegeService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.IRCompanyService" id="iRCompanyService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.bank.ent.api.account.BankEntAccountInfoService" id="bankEntAccountInfoService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.bank.ent.api.trade.BankEntAccountTradeService"
                     id="iBankEntAccountTradeService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.bank.ent.api.account.BankEntAccountInfoService"
                     id="iBankEntAccountInfoService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.bank.api.service.IEntPayBackRpcService"
                     id="iEntPayBackRpcService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.stereo.api.service.pay.IPayAccountService" id="iPayAccountService" check="false"/>

    <dubbo:reference interface="com.fenbei.paycore.api.pay.PaymentFacade" id="paymentFacade" check="false"/>

    <dubbo:reference interface="com.fenbei.paycore.api.pay.QueryPaymentOrderFacade" id="queryPaymentOrderFacade" check="false"/>

    <dubbo:reference interface="com.fenbeitong.dech.api.service.IBankCompanyAccountService" id="iBankCompanyAccountService" check="false"/>



    <dubbo:reference interface="com.fenbei.salary.api.pay.IPayBackRpcService" id="iPayBackRpcService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.harmony.captcha.api.service.ICaptchaService" id="captchaService"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.auth.ITwoFactorAuthService" id="iTwoFactorAuthService"/>

    <dubbo:reference interface="com.fenbeitong.dech.api.service.ILfBankSearchService"
                     id="iLfBankSearchService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saas.card.api.control.api.channel.pay.IVirtualCardAccountSwithCallerPayApi" id="iVirtualCardAccountSwithCallerPayApi" check="false"/>


    <dubbo:reference interface="com.fenbeitong.bank.api.service.IBankPublicAccountInfoService"
                     id="iBankPublicAccountInfoService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.dech.api.service.IBankCardTradeService"
                     id="iBankCardTradeService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.group.IGroupEmployeeService" id="iGroupEmployeeService" check="false"/>

    <dubbo:reference interface="com.fenbei.material.api.bank.BankDataFacade"
                     id="bankDataFacade" check="false"/>
    <dubbo:reference interface="com.fenbeitong.finance.api.bf.IAccountSubjectService" id="iAccountSubjectService" check="false"/>
    <dubbo:reference interface="com.fenbei.pay.risk.api.cashier.CashierOrderSettlementThirdFacade" id="cashierOrderSettlementThirdFacade" check="false"/>
    <dubbo:reference interface="com.fenbeitong.sass.budget.service.api.dubbo.BudgetCompanyBusinessConfigService" id="budgetCompanyBusinessConfigService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.fxpay.api.interfaces.ICompanyAcctService" id="iCompanyAcctService" check="false"/>
    <dubbo:reference interface="com.fenbei.fx.card.api.card.ICardService" id="iCardService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.acctperson.api.service.trade.IReimbursePersonCardService"
                     id="iReimbursePersonCardService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.fxpay.api.interfaces.FxCompanyAcctFacade" id="fxCompanyAcctFacade" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saas.api.service.message.setting.IMessageSettingService" id="iMessageSettingService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.ICompanyCooperatingModelService" id="iCompanyCooperatingModelService"  check="false"/>
    <dubbo:reference interface="com.fenbeitong.dech.api.service.spabank.ISpaBankPersonAcctService"
                     id="iSpaBankPersonAcctService"  check="false"/>
    <dubbo:reference interface="com.fenbeitong.dech.api.service.IBankCardBinService" id="iBankCardBinService"  check="false"/>
    <dubbo:reference interface="com.fenbeitong.dech.api.service.lianlian.ILianLianAccountService" id="iLianLianAccountService" check="false"/>
</beans>