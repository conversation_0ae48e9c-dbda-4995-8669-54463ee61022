<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <!--属性定义-->
    <property name="LOG_NAME" value="${logback.log.path}" />
    <property name="LOG_PORT" value="${port:-80}" />
    <property name="LOG_KEEP_DAY" value="5" />

    <property name="APPNAME" value="${log.name}" />
    <property name="LOGSTASH_DESTINATION" value="${log.logstash.destination}" />

    <!--控制台输出-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}][%thread][%level][%X{requestId}] %logger{50}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <!--文件输出-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">　　　　　　　　　　　　
        <file>${LOG_NAME}-${LOG_PORT}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">　　　　　　　　　　　　
            <fileNamePattern>${LOG_NAME}-${LOG_PORT}-%d{yyyy-MM-dd}.zip</fileNamePattern>　　　　　　　　　　　　　
            <maxHistory>${LOG_KEEP_DAY}</maxHistory>　　　　　　　　　　　
        </rollingPolicy>　　　　　
        <encoder>　　　　　　　　　　　
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}][%thread][%level][%X{requestId}] %logger{50}:%line - %msg%n</pattern>　　　　　　　　　　　
        </encoder>　　
    </appender>
    <!--远程日志输出-->
    <appender name="logstash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>${LOGSTASH_DESTINATION}</destination>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <customFields>{"appname":"${APPNAME}"}</customFields>
        </encoder>
    </appender>

    <!--主日志级别-->
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
        <appender-ref ref="logstash"/>
    </root>
    <!--包日志级别-->
    <logger name="com.ibatis" level="debug"/>
    <logger name="java.sql" level="debug"/>
    
    <logger name="com.luastar.swift.http" level="warn" additivity="false"/>
    
    <logger name="com.luastar.swift.base.net" level="warn" additivity="false"/>
    
    <logger name="org.mongodb.driver.connection" level="warn" additivity="false"/>
    
    <logger name="com.fenbeitong.finhub.auth.interceptor" level="warn" additivity="false"/>
    
    <logger name="com.fenbeitong.finhub.auth.core.auth" level="warn" additivity="false"/>
    
    <logger name="com.fenbeitong.finhub.auth.core.convert" level="error" additivity="false"/>
    <!-- 
    <logger name="com.fenbeitong.finhub.dubbo.filter" level="warn" additivity="false"/> -->
    
    <logger name="io.netty.handler.logging" level="warn" additivity="false"/>

</configuration>
