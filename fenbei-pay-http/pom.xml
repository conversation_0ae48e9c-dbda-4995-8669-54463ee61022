<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fenbei-pay</artifactId>
        <groupId>com.fenbeitong</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fenbei-pay-http</artifactId>

    <dependencies>
        <!--收银台-->
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-cashier</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-sas</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-redcoupon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swift-http</artifactId>
                    <groupId>com.luastar</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-auth</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swift-http</artifactId>
                    <groupId>com.luastar</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.luastar</groupId>
            <artifactId>swift-http</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>bson</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>

		<dependency>
			<groupId>com.fenbeitong</groupId>
			<artifactId>fenbei-pay-common-api</artifactId>
			<version>1.0.0${current.version}</version>
		</dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>org.glassfish.web</groupId>
            <artifactId>javax.el</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-acctpublic</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-acctdech</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-webmvc</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-expression</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-search</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>saas-plus-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-extract</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-settlement-external-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>harmony-third-examine-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>stereo-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fenbeitong</groupId>
                    <artifactId>finhub-task</artifactId>
                </exclusion>
                <exclusion>
                	<groupId>com.finhub.framework</groupId>
                	<artifactId>finhub-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>stereo-data-middleplatform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong.fxpay</groupId>
            <artifactId>fenbei-fx-pay-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}-${profile.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <!-- 过滤后缀为pem、pfx的证书文件 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <filters>
            <!-- 使用不同环境的属性文件作为替换源，少量属性时可直接在profile/properties中定义 -->
            <filter>../filters/filter-${profile.name}.properties</filter>
        </filters>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>spring/*</include>
                    <include>template/*</include>
                    <include>*.xml</include>
                    <include>META-INF/dubbo/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>props/*</include>
                    <include>logback.xml</include>
                    <include>dubbo/*</include>
                    <include>scripts/*</include>
                    <include>cert/**/*</include>
                    <include>i18n/*</include>
                </includes>
                <!-- 需要对上述文件进行${}变量替换 -->
                <filtering>true</filtering>
            </resource>

        </resources>
    </build>
</project>
