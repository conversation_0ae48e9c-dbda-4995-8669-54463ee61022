<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.sas.pwd.base.db.mapper.SasEmployeePasswordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.dto.sas.SasEmployeePassword">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
    <result column="employee_pwd" jdbcType="VARCHAR" property="employeePwd" />
    <result column="sign_factor" jdbcType="VARCHAR" property="signFactor" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="pwd_status" jdbcType="INTEGER" property="pwdStatus" />
    <result column="lock_open_time" jdbcType="TIMESTAMP" property="lockOpenTime" />
    <result column="fail_num" jdbcType="INTEGER" property="failNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>