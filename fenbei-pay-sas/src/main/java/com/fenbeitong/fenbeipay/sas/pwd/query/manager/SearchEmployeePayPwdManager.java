package com.fenbeitong.fenbeipay.sas.pwd.query.manager;

import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryPayPwdRespVo;

/**
 * @Description: 执行用户支付密码业务
 * @Author: wh
 * @Date: 2019/11/9 11:42 AM
 */
public interface SearchEmployeePayPwdManager {
    /**
      * @Description: 查询支付支付密码是否存在
      * @Param: [employeeId]
      * @return: com.fenbeitong.fenbeipay.core.model.vo.sas.QueryPayPwdRespVo
      */
    QueryPayPwdRespVo queryExistPayPassword(String employeeId);

    /**
     * @Description: 删除缓存里存在支付密码
     */
    void deletePayPwdCache();

    /**
     * 根据员工号重置密码
     * @param employeeId
     * @return
     */
    Boolean resetPwd(String employeeId);
}
