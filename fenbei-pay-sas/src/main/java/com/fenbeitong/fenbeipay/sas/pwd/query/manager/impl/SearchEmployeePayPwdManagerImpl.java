package com.fenbeitong.fenbeipay.sas.pwd.query.manager.impl;

import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryPayPwdRespVo;
import com.fenbeitong.fenbeipay.core.utils.SecurityUtils;
import com.fenbeitong.fenbeipay.dto.sas.SasEmployeePassword;
import com.fenbeitong.fenbeipay.sas.pwd.base.db.mapper.SasEmployeePasswordMapper;
import com.fenbeitong.fenbeipay.sas.pwd.base.manager.BaseSasPayManagerImpl;
import com.fenbeitong.fenbeipay.sas.pwd.query.manager.SearchEmployeePayPwdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Description: 查询支付密码业务
 * @Author: wh
 * @Date: 2019/11/9 11:42 AM
 */
@Service
public class SearchEmployeePayPwdManagerImpl extends BaseSasPayManagerImpl implements SearchEmployeePayPwdManager {
    @Autowired
    private RedisDao redisDao;

    @Autowired
    public SasEmployeePasswordMapper sasEmployeePasswordMapper;

    @Override
    @Cacheable(value = "pay_pwd", key = "'employeeId:' + #p0", unless = "#result.isExistPwd eq false")
    public QueryPayPwdRespVo queryExistPayPassword(String employeeId) {
        SasEmployeePassword sasEmployeePassword = getSasEmployeePassword(employeeId);
        return Objects.isNull(sasEmployeePassword) ? QueryPayPwdRespVo.of(false) : QueryPayPwdRespVo.of(true);
    }

    @Override
    public void deletePayPwdCache() {
        redisDao.deleteBatchKey("fenbei-pay:pay_pwd:employeeId:*");
    }

    @Override
    public Boolean resetPwd(String employeeId) {
        SasEmployeePassword sasEmployeePassword = getSasEmployeePassword(employeeId);
        if(Objects.isNull(sasEmployeePassword)){
            return false;
        }
        String employeePwd = SecurityUtils.SHA512("gVOdpSE/2n8b5UiAKbEQDA==" + sasEmployeePassword.getSignFactor());
        sasEmployeePasswordMapper.resetPayPassword(employeeId,employeePwd);
        //删除缓存
        redisDao.deleteKey("fenbei-pay:pay_pwd:employeeId:"+employeeId);
        return true;
    }
}
