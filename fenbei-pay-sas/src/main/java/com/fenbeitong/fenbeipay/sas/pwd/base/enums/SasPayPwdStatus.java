package com.fenbeitong.fenbeipay.sas.pwd.base.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * sas支付密码状态
 */
public enum SasPayPwdStatus {

    NORMAL(1, "正常"),
    LOCK(2, "锁定");
    private int key;
    private String value;

    SasPayPwdStatus(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static SasPayPwdStatus getEnum(Integer key) {
        if (key == null) {
            return null;
        }
        for (SasPayPwdStatus item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return null;
    }

    public static SasPayPwdStatus getEnum(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        for (SasPayPwdStatus item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }


}
