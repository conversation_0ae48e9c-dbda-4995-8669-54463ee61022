package com.fenbeitong.fenbeipay.sas.pwd.handle.manager.impl;

import cn.hutool.core.util.IdUtil;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.config.api.config.SysConfigItemCompanyService;
import com.fenbeitong.fenbeipay.api.constant.enums.sas.SecureVerifyModeEnum;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.sas.ConfirmPayPwdRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.PayPasswordReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.UpdatePayPasswordReqVo;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.fenbeipay.core.utils.RandomUtil;
import com.fenbeitong.fenbeipay.core.utils.SecurityUtils;
import com.fenbeitong.fenbeipay.dto.sas.SasEmployeePassword;
import com.fenbeitong.fenbeipay.sas.pwd.base.conver.SasEmployeePasswordConver;
import com.fenbeitong.fenbeipay.sas.pwd.base.enums.SasPayFaceRecognitionStatus;
import com.fenbeitong.fenbeipay.sas.pwd.base.manager.BaseSasPayManagerImpl;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasEmployeePayPwdManager;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.harmony.third.examine.api.ITencentFaceRecognitionService;
import com.fenbeitong.harmony.third.examine.api.request.TencentFaceQueryFaceRecordReq;
import com.fenbeitong.harmony.third.examine.api.response.TencentFaceQueryFaceRecordResponse;
import com.fenbeitong.usercenter.api.model.dto.operater.OperateLogFaceDTO;
import com.fenbeitong.usercenter.api.service.log.IOperateLogService;

import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ValidateUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * @Description: 执行用户支付密码业务
 * @Author: wh
 * @Date: 2019/11/9 11:42 AM
 */
@Service
public class SasEmployeeSasPayManagerImpl extends BaseSasPayManagerImpl implements SasEmployeePayPwdManager {

    @Autowired
    private SysConfigItemCompanyService sysConfigItemCompanyService;
    @Autowired
    private ITencentFaceRecognitionService iTencentFaceRecognitionService;
    @Autowired
    private IOperateLogService iOperateLogService;

    //密码默认的失败次数
    private final int DEFAULT_FAIL_NUM = 5;
    //需要锁定的时间目前3小时
    private final int OPEN_LOCK_TIME = 3;

    //失败在DB最后一次数
    private final int DB_END_FAIL_NUM = 0;
    //配置组
    private final String GROUP_CODE = "forget_pay_pwd";
    //配置项
    private final String ITEM_CODE = "pay_pwd_face_recognition";
    //人脸对比通过分数,大于等于79通过
    private final int PASS_SCORE = 79;
    //webapp
//    private final String CLIENT_TYPE_WEBAPP = "webapp";


    @Override
    public void createPayPassword(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo) {
        SasEmployeePassword password = getSasEmployeePassword(userInfo.getId());
        if (Objects.nonNull(password)){
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_EXIST_ERROR);
        }
        //生成盐
        String signFactor = RandomUtil.getStrByLength(6);
        //生成密码
        String employeePwd = SecurityUtils.SHA512(payPasswordReqVo.getEmployeePwd() + signFactor);

        SasEmployeePassword sasEmployeePassword = new SasEmployeePassword();

        SasEmployeePasswordConver.toCreatePayPasswordReq(userInfo, signFactor, employeePwd,
                DEFAULT_FAIL_NUM, sasEmployeePassword);

        if (sasEmployeePasswordMapper.insert(sasEmployeePassword) != 1) {
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_CREATE_ERROR);
        }
    }
    
    @Override
    public void initDefaultPwd4User(UserInfoVO userInfo) {
    	SasEmployeePassword sasEmployeePassword = getSasEmployeePassword(userInfo.getId());
        if(Objects.nonNull(sasEmployeePassword)){
            return;
        }
        
    	PayPasswordReqVo defaultPwd = PayPasswordReqVo.builder().employeePwd("gVOdpSE/2n8b5UiAKbEQDA==").build();
    	createPayPassword(defaultPwd, userInfo);
    }

    @Override
    public void updatePayPassword(UpdatePayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo) {
        SasEmployeePassword sasEmployeePassword = getSasEmployeePassword(userInfo.getId());
        if (Objects.isNull(sasEmployeePassword)) {
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }
        if (Objects.nonNull(sasEmployeePassword.getLockOpenTime())
                && new Date().before(sasEmployeePassword.getLockOpenTime())) {
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_LOCK);
        }
        //比较老密码
        ConfirmPayPwdRespVo confirmPayPwdRespVo = verifySynPayPassword(sasEmployeePassword, payPasswordReqVo.getEmployeePwd());
        if (!confirmPayPwdRespVo.isCorrectConfirm) {
            throw new FinhubException(GlobalResponseCode.SAS_PAY_PASSWORD_UPDATE_OLD_ERROR.getCode(),confirmPayPwdRespVo.getShowTips());
        }
        //生成密码
        String employeeNewPwd = SecurityUtils.SHA512(payPasswordReqVo.getEmployeeNewPwd() + sasEmployeePassword.getSignFactor());
        if (Objects.equals(sasEmployeePassword.getEmployeePwd(), employeeNewPwd)) {
            //新密码和原密码一样错误提示
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_UPDATE_IDENTICAL_ERROR);
        }
        if (sasEmployeePasswordMapper.updatePayPassword(sasEmployeePassword.getEmployeeId(), employeeNewPwd) != 1) {
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_UPDATE_ERROR);
        }
    }

    @Override
    public ConfirmPayPwdRespVo confirmPayPassword(PayPasswordReqVo payPasswordReqVo, String employeeId) {
        ValidateUtils.validate(payPasswordReqVo);
        SasEmployeePassword sasEmployeePassword = getSasEmployeePassword(employeeId);
        if (Objects.isNull(sasEmployeePassword)) {
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }
        if (Objects.nonNull(sasEmployeePassword.getLockOpenTime())
                && new Date().before(sasEmployeePassword.getLockOpenTime())) {
            return ConfirmPayPwdRespVo.ofFail(DB_END_FAIL_NUM);
        }
        //支付密码：验证同步失败次数
        return verifySynPayPassword(sasEmployeePassword, payPasswordReqVo.getEmployeePwd());
    }

    @Override
    public ConfirmPayPwdRespVo confirmPayPasswordV4(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo) {
        ConfirmPayPwdRespVo confirmPayPwdRespVo = confirmPayPassword(payPasswordReqVo, userInfo.getId());
        if (confirmPayPwdRespVo.getIsCorrectConfirm()) {
            String sasBusinessId = payPasswordReqVo.getBusinessId();
            if (StringUtils.isEmpty(sasBusinessId)) {
                sasBusinessId = "SAS" + IdUtil.objectId();
            }
            redisDao.getValueOperations().set(PWD_SECURE_VERIFY_PREX + userInfo.getId() + sasBusinessId, VERIFY_PASS_VALUE, SECURE_VERIFY_TIMEOUT, TimeUnit.SECONDS);
            confirmPayPwdRespVo.setSasBusinessId(sasBusinessId);
            if (SecureVerifyModeEnum.PWD_CAPTCHA_VERIFY_MODE.getCode().equals(payPasswordReqVo.getVerifyMode())) {
                confirmPayPwdRespVo.setSmsMsg(userInfo.getPhone());
            }
        }
        return confirmPayPwdRespVo;
    }

    @Override
    public ConfirmPayPwdRespVo confirmPayPasswordBusinessIdV4(PayPasswordReqVo payPasswordReqVo, List<String> employeeIdList) {
        List<SasEmployeePassword> sasEmployeesPasswords = getSasEmployeesPassword(employeeIdList);
        if (CollectionUtils.isEmpty(sasEmployeesPasswords)) {
            FinhubLogger.info("confirmPayPasswordBusinessIdV4 未查询到密码信息,employeeIdList:{}", JSON.toJSONString(employeeIdList));
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }
        ConfirmPayPwdRespVo confirmPayPwdRespVo = new ConfirmPayPwdRespVo();
        for (SasEmployeePassword sasEmployeesPassword : sasEmployeesPasswords) {
            UserInfoVO userInfo = new UserInfoVO();
            userInfo.setId(sasEmployeesPassword.getEmployeeId());
            confirmPayPwdRespVo = confirmPayPassword(payPasswordReqVo, userInfo.getId());
            if (confirmPayPwdRespVo.getIsCorrectConfirm()) {
                String sasBusinessId = payPasswordReqVo.getBusinessId();
                if (StringUtils.isEmpty(sasBusinessId)) {
                    sasBusinessId = "SAS" + IdUtil.objectId();
                }
                redisDao.getValueOperations().set(PWD_SECURE_VERIFY_PREX + sasBusinessId, VERIFY_PASS_VALUE, SECURE_VERIFY_TIMEOUT, TimeUnit.SECONDS);
                confirmPayPwdRespVo.setSasBusinessId(sasBusinessId);
                break;
            }
        }
        return confirmPayPwdRespVo;
    }


    @Override
    public void resetPayPassword(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo, CompanyInfoVO userCompanyInfo, String clientType) {
        checkFaceRecognition(payPasswordReqVo, userInfo, userCompanyInfo, clientType);

        SasEmployeePassword sasEmployeePassword = getSasEmployeePassword(userInfo.getId());
        if (Objects.isNull(sasEmployeePassword)) {
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }
        //生成密码
        String employeePwd = SecurityUtils.SHA512(payPasswordReqVo.getEmployeePwd() + sasEmployeePassword.getSignFactor());
        if (sasEmployeePasswordMapper.resetPayPassword(sasEmployeePassword.getEmployeeId(), employeePwd) != 1) {
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_RESET_ERROR);
        }
    }

    private void checkFaceRecognition(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo, CompanyInfoVO userCompanyInfo, String clientType) {
        if(Objects.isNull(userCompanyInfo) || StringUtils.isBlank(clientType)){
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }

        //0.查询企业是否开通人脸识别
        if (checkCompanyFaceConfig(payPasswordReqVo, userCompanyInfo)) return;
        //1.查询userId和orderNo关联关系
        checkRelUserIdAndOrderNo(payPasswordReqVo, userInfo);
        //2.人脸识别结果校验
        checkFaceRecognition(payPasswordReqVo, userInfo);
    }

    private void checkFaceRecognition(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo) {
        try{
            String nonceStr = UUID.randomUUID().toString();
            nonceStr = nonceStr.replaceAll("-", "");
            //人脸识别验证
            faceRecognitionService(payPasswordReqVo, nonceStr);
        }catch(FinhubException e){
            FinhubLogger.error("查询人脸识别结果异常:参数{}, {}", JsonUtils.toJson(userInfo), e);
            throw new FinPayException(GlobalResponseCode.SAS_PAY_FACE_RECOGNITION_EXCEPTION);
        }
    }

    private boolean checkCompanyFaceConfig(PayPasswordReqVo payPasswordReqVo, CompanyInfoVO userCompanyInfo) {
        try{
            int companyIntConfig = sysConfigItemCompanyService.getCompanyIntConfig(userCompanyInfo.getId(), GROUP_CODE, ITEM_CODE);
            FinhubLogger.info("人脸识别开关配置： config={}", companyIntConfig);
            if(companyIntConfig == SasPayFaceRecognitionStatus.OFF.getKey()) return true;
        }catch(FinhubException e){
            FinhubLogger.error("查询人脸识别开关配置异常:参数{}, {}", JsonUtils.toJson(userCompanyInfo), e);
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }

        if(StringUtils.isEmpty(payPasswordReqVo.getOrderNo())){
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_VERSION_UPGRADE, true);
        }
        return false;
    }

    private void checkRelUserIdAndOrderNo(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo) {
        try{
            FinhubLogger.info("查询userId和orderNo关联关系请求参数： req={}, userInfo={}", JsonUtils.toJson(payPasswordReqVo), JsonUtils.toJson(userInfo));
            OperateLogFaceDTO operateLogFace = iOperateLogService.getOperateLogFace(userInfo.getId(), payPasswordReqVo.getOrderNo());
            FinhubLogger.info("查询userId和orderNo关联关系返回结果： resp={}", JsonUtils.toJson(operateLogFace));
            if(Objects.isNull(operateLogFace)){
                FinhubLogger.error("未查到userId和orderNo关联关系信息： req={}, userId={}", JsonUtils.toJson(payPasswordReqVo), userInfo.getId());
                throw new FinPayException(GlobalResponseCode.SAS_PAY_FACE_RECOGNITION_FAIL);
            }
        }catch(FinhubException e){
            FinhubLogger.error("查询userId和orderNo关联关系异常:req={}, userId={}, {}", JsonUtils.toJson(payPasswordReqVo), userInfo.getId(), e);
            throw new FinPayException(GlobalResponseCode.EXCEPTION_DATA_ERROR);
        }
    }

    private void faceRecognitionService(PayPasswordReqVo payPasswordReqVo, String nonceStr) {
        TencentFaceQueryFaceRecordReq req = new TencentFaceQueryFaceRecordReq(nonceStr, payPasswordReqVo.getOrderNo());
        FinhubLogger.info("查询人脸识别结果请求参数： req={}", JsonUtils.toJson(req));
        TencentFaceQueryFaceRecordResponse resp = iTencentFaceRecognitionService.queryfacerecord(req);
        FinhubLogger.info("查询人脸识别结果返回结果： resp={}", JsonUtils.toJson(resp));
        if(!(Objects.nonNull(resp) && StringUtils.isNotBlank(resp.getSimilarity()) && Double.parseDouble(resp.getSimilarity()) >= PASS_SCORE)){
            FinhubLogger.error("人脸识别核验异常:参数{}", JsonUtils.toJson(resp));
            throw new FinPayException(GlobalResponseCode.SAS_PAY_FACE_RECOGNITION_FAIL);
        }
    }

    @Override
    public void unlockPayPassword() {
        List<SasEmployeePassword>  sasEmployeePasswordList= Lists.newArrayList();
        try {
            sasEmployeePasswordList=queryUnlockPayPassword();
            if (CollectionUtils.isEmpty(sasEmployeePasswordList)){
                return;
            }
            sasEmployeePasswordList.forEach(sasEmployeePassword -> {
                 sasEmployeePasswordMapper.unlockPayPassword(sasEmployeePassword.getId());
            });
        } catch (Exception e) {
            FinhubLogger.error("打开支付密码锁定时间:参数{}", JsonUtils.toJson(sasEmployeePasswordList), e);
        }
    }

    @Override
    public boolean queryIsVerifyPwdPass(String businessId, String employeeId) {
        if (StringUtils.isNotBlank(businessId)) {
            String verifyResult;
            if (StringUtils.isBlank(employeeId)) {
                verifyResult = redisDao.getValueOperations().get(PWD_SECURE_VERIFY_PREX + businessId);
            } else {
                verifyResult = redisDao.getValueOperations().get(PWD_SECURE_VERIFY_PREX+ employeeId + businessId);
            }
            if (VERIFY_PASS_VALUE.equals(verifyResult)) {
                return true;
            }
        }
        return false;
    }


    //——————————————————————————private——————————————————————————


    /**
     * @Description: 支付密码：验证同步失败次数
     * @Param: [sasEmployeePassword, verifyEmployeePwd]
     * @return: com.fenbeitong.fenbeipay.core.model.vo.sas.ConfirmPayPwdRespVo
     */
    private ConfirmPayPwdRespVo verifySynPayPassword(SasEmployeePassword sasEmployeePassword, String verifyEmployeePwd) {
        //生成密码
        String employeePwd = SecurityUtils.SHA512(verifyEmployeePwd + sasEmployeePassword.getSignFactor());
        if (Objects.equals(sasEmployeePassword.getEmployeePwd(), employeePwd)) {
            //数据都设置正常
            handleCorrectPwd(sasEmployeePassword);
            return ConfirmPayPwdRespVo.of();
        }
        //密码不相同:执行数据处理
        handleFailPwd(sasEmployeePassword);

        return ConfirmPayPwdRespVo.ofFail(sasEmployeePassword.getFailNum());
    }

    /**
     * @Description: 支付密码记录密码错误
     * @Param: [sasEmployeePassword]
     */
    private void handleFailPwd(SasEmployeePassword sasEmployeePassword) {
        //需要锁定账户3小时
        if (DB_END_FAIL_NUM == sasEmployeePassword.getFailNum()) {
            Date lockTime = DateUtil.changeDateHour(new Date(), OPEN_LOCK_TIME);
            int lockNum = sasEmployeePasswordMapper.lockPayPwdFailNum(sasEmployeePassword.getEmployeeId(), lockTime);
            if (lockNum != 1) {
                throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_LOCK_ERROR);
            }
            return;
        }
        //减少重试支付密码次数
        int failNum = sasEmployeePasswordMapper.reducePayPwdFailNum(sasEmployeePassword.getEmployeeId());
        if (failNum != 1) {
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_ADD_FAILNUM_ERROR);
        }
    }

    /**
     * @Description: 支付密码记录密码正确
     * @Param: [sasEmployeePassword]
     */
    private void handleCorrectPwd(SasEmployeePassword sasEmployeePassword) {
        //正常数据
        if (DEFAULT_FAIL_NUM == sasEmployeePassword.getFailNum()) {
            return;
        }
        int normalPwdNum = sasEmployeePasswordMapper.updateNormalPwdNum(sasEmployeePassword.getEmployeeId());
        if (normalPwdNum != 1) {
            throw new FinPayException(GlobalResponseCode.SAS_PAY_PASSWORD_UPDATE_NORMALNUM_ERROR);
        }
    }
}