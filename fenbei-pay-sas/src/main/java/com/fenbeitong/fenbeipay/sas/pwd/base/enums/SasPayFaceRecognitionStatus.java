package com.fenbeitong.fenbeipay.sas.pwd.base.enums;

import org.apache.commons.lang3.StringUtils;

public enum SasPayFaceRecognitionStatus {

    OFF(0, "关闭人脸识别配置"),
    ON(1, "开启人脸识别配置");
    private int key;
    private String value;

    SasPayFaceRecognitionStatus(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static SasPayFaceRecognitionStatus getEnum(Integer key) {
        if (key == null) {
            return null;
        }
        for (SasPayFaceRecognitionStatus item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return null;
    }

    public static SasPayFaceRecognitionStatus getEnum(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        for (SasPayFaceRecognitionStatus item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
