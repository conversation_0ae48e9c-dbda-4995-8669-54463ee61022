package com.fenbeitong.fenbeipay.sas.pwd.handle.manager;

import com.fenbeitong.fenbeipay.api.model.dto.sas.req.SasSecurityVerifyReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.sas.resp.SasSecurityVerifyRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.sas.SasSecurityVerifyModeReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.sas.SasSecurityVerifyModeRespVO;

/**
 * @Description: 安全认证Manager
 * @Author: liyi
 * @Date: 2022/12/2 2:21 PM
 */
public interface SasSecurityVerifyManager {
    /**
     * 获取安全认证方式
     */
    SasSecurityVerifyModeRespVO getSecurityVerifyMode(SasSecurityVerifyModeReqVO reqVO, String companyId, String employeeId);

    /**
     * 查询是否通过安全校验
     */
    SasSecurityVerifyRespDTO queryIsSecurityVerifyPass(SasSecurityVerifyReqDTO securityVerifyReqDTO);
}
