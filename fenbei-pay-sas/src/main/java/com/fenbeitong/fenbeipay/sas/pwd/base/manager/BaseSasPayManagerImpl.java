package com.fenbeitong.fenbeipay.sas.pwd.base.manager;

import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.dto.sas.SasEmployeePassword;
import com.fenbeitong.fenbeipay.sas.pwd.base.db.mapper.SasEmployeePasswordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * @Title: PayPwdManagerImpl
 * @ProjectName fenbei-pay
 */
@Service
public class BaseSasPayManagerImpl {

    @Autowired
    public SasEmployeePasswordMapper sasEmployeePasswordMapper;

    @Autowired
    protected RedisDao redisDao;

    //支付密码安全认证验证结果前缀
    protected final String PWD_SECURE_VERIFY_PREX = "PWD_SECURE_VERIFY_";
    //验证码安全认证验证结果前缀
    protected final String CAPTCHA_SECURE_VERIFY_PREX = "CAPTCHA_SECURE_VERIFY_";
    //验证通过
    protected final String VERIFY_PASS_VALUE = "1";
    //安全认证验证结果缓存时间
    protected final int SECURE_VERIFY_TIMEOUT = 15 * 60;

    /**
      * @Description: 根据用户id获取支付密码
      * @Param: [employeeId]
      * @return: com.fenbeitong.fenbeipay.dto.sas.SasEmployeePassword
      */
    public SasEmployeePassword getSasEmployeePassword(String employeeId){
        Example example=new  Example(SasEmployeePassword.class);
        example.createCriteria().andEqualTo("employeeId",employeeId);
        return sasEmployeePasswordMapper.selectOneByExample(example);
    }

    public List<SasEmployeePassword> getSasEmployeesPassword(List<String> employeeIds){
        Example example=new  Example(SasEmployeePassword.class);
        example.createCriteria().andIn("employeeId", employeeIds);
        return sasEmployeePasswordMapper.selectByExample(example);
    }
    /**
     * @Description: 查询当前时间等等于或小于锁定时间
     * @return: List<SasEmployeePassword>
     */
    public List<SasEmployeePassword> queryUnlockPayPassword() {
        Example example=new  Example(SasEmployeePassword.class);
        example.createCriteria().andLessThanOrEqualTo("lockOpenTime",new Date());
        return sasEmployeePasswordMapper.selectByExample(example);
    }
}
