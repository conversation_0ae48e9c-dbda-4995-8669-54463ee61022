package com.fenbeitong.fenbeipay.sas.pwd.handle.manager;

import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.sas.ConfirmPayPwdRespVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.PayPasswordReqVo;
import com.fenbeitong.fenbeipay.core.model.vo.sas.UpdatePayPasswordReqVo;

import java.util.List;

/**
 * @Description: 执行用户支付密码业务
 * @Author: wh
 * @Date: 2019/11/9 11:42 AM
 */
public interface SasEmployeePayPwdManager {
    /**
      * @Description: 设置密码
      * @Param: [payPasswordReqVo, userInfo]
      * @Author: wh
      * @Date: 2019/11/9 12:15 PM
      */
    void createPayPassword(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo);
    
    /**
     * 设置默认支付密码，类似重置
     * @param userInfo
     */
    void initDefaultPwd4User(UserInfoVO userInfo);

    /**
      * @Description: 修改支付密码
      * @Param: [updatePayPasswordReqVo, userInfo]
      * @Date: 2019/11/9 5:17 PM
      */
    void updatePayPassword(UpdatePayPasswordReqVo updatePayPasswordReqVo, UserInfoVO userInfo);

    /**
      * @Description: 确认支付密码
      * @Param: [payPasswordReqVo, employeeId]
      * @return: ConfirmPayPwdRespVo
      * @Author: wh
      * @Date: 2019/11/11 10:26 AM
      */
    ConfirmPayPwdRespVo confirmPayPassword(PayPasswordReqVo payPasswordReqVo,String employeeId);

    /**
     * @Description: 确认支付密码V4版-缓存验证结果
     * @Param: [payPasswordReqVo, employeeId]
     * @return: ConfirmPayPwdRespVo
     * @Author: wh
     * @Date: 2019/11/11 10:26 AM
     */
    ConfirmPayPwdRespVo confirmPayPasswordV4(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo);

    ConfirmPayPwdRespVo confirmPayPasswordBusinessIdV4(PayPasswordReqVo payPasswordReqVo, List<String> employeeIdList);

    /**
      * @Description: 重置(找回)支付密码
      * @Param: [payPasswordReqVo, userInfo]
      */
    void resetPayPassword(PayPasswordReqVo payPasswordReqVo, UserInfoVO userInfo,
        CompanyInfoVO userCompanyInfo, String clientType);

    /**
      * @Description: 打开支付密码锁定时间
      */
    void unlockPayPassword();

    boolean queryIsVerifyPwdPass(String businessId, String employeeId);

}
