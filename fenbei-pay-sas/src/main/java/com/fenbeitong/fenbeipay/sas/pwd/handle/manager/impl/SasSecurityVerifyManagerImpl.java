package com.fenbeitong.fenbeipay.sas.pwd.handle.manager.impl;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.BizPayType;
import com.fenbeitong.fenbeipay.api.constant.enums.sas.SasPasswordVerifyModeEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.sas.SasSmsVerifyModeEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.sas.SecurityBusinessCaseEnum;
import com.fenbeitong.fenbeipay.api.model.dto.sas.req.SasSecurityVerifyReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.sas.resp.SasSecurityVerifyRespDTO;
import com.fenbeitong.fenbeipay.api.model.vo.sas.SasSecurityVerifyModeReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.sas.SasSecurityVerifyModeRespVO;
import com.fenbeitong.fenbeipay.core.constant.paycenter.PayCenterConstant;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.fenbeipay.core.enums.auth.AuthCode;
import com.fenbeitong.fenbeipay.core.model.vo.sas.QueryPayPwdRespVo;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasEmployeePayPwdManager;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasPayCaptchaManager;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasSecurityVerifyManager;
import com.fenbeitong.fenbeipay.sas.pwd.query.manager.SearchEmployeePayPwdManager;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.enums.common.CommonAuthCodeEnums;
import com.fenbeitong.usercenter.api.service.common.ICommonService;
import com.luastar.swift.base.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Author: liyi
 * @Date: 2022/12/2 2:22 PM
 */
@Component
public class SasSecurityVerifyManagerImpl implements SasSecurityVerifyManager {
    @Autowired
    private ICommonService iCommonService;

    @Autowired
    private SearchEmployeePayPwdManager searchEmployeePayPwdManager;

    @Autowired
    private DingDingMsgService dingDingMsgService;

    @Autowired
    private SasEmployeePayPwdManager sasEmployeePayPwdManager;

    @Autowired
    private SasPayCaptchaManager sasPayCaptchaManager;

    @Autowired
    private RedisDao redisDao;

    //短信校验金额阈值
    private static final BigDecimal smsVerifyThreshold = new BigDecimal(100000);

    private static final String SECURITY_VERIFY_SWITCH = "SECURITY_VERIFY_SWITCH";

    @Override
    public SasSecurityVerifyModeRespVO getSecurityVerifyMode(SasSecurityVerifyModeReqVO reqVO, String companyId, String employeeId) {
        String businessCase = reqVO.getBusinessCase();
        String bizPayType = reqVO.getBizPayType();
        Boolean isGoSettingPassword = Boolean.FALSE;
        Boolean isVerifyPassword = Boolean.FALSE;
        Boolean isVerifySms = Boolean.FALSE;
        SasSecurityVerifyModeRespVO respVO = new SasSecurityVerifyModeRespVO();
        SecurityBusinessCaseEnum businessCaseEnum = SecurityBusinessCaseEnum.getBusinessCaseEnumByCode(businessCase);
        if (businessCaseEnum == null) {
            respVO.setIsGoSettingPassword(false);
            respVO.setIsVerifyPassword(false);
            respVO.setIsVerifySms(false);
            return respVO;
        }

        SasPasswordVerifyModeEnum passwordVerifyMode = getPasswordVerifyMode(businessCaseEnum, bizPayType);
        if (SasPasswordVerifyModeEnum.COMPANY_CONFIG.equals(passwordVerifyMode)) {
            Map<String, Integer> companyEmployeeAuth = iCommonService.queryCompanyEmployeeAuth(companyId, employeeId,
                    CommonAuthCodeEnums.PAYMENT_CODE.getCommonAuthType().getKey(), AuthCode.pwdPayCode());
            FinhubLogger.info("验证支付密码权限返回结构{},{},{}", companyId, employeeId, JsonUtils.toJson(companyEmployeeAuth));
            //返回map对象 1:开启支付密码，需要验证支付密码，如果没有支付密码就需要设置 0:不开启支付密码，不需要验证支付密码
            if (PayCenterConstant.PWD_PAY_EXIST_AUTH == companyEmployeeAuth.get(CommonAuthCodeEnums.PAYMENT_CODE.getKey())) {
                QueryPayPwdRespVo queryPayPwdRespVo = searchEmployeePayPwdManager.queryExistPayPassword(employeeId);
                if (queryPayPwdRespVo.isExistPwd) {
                    isVerifyPassword = Boolean.TRUE;
                } else {
                    isGoSettingPassword = Boolean.TRUE;
                }
            }
        }
        if (SasPasswordVerifyModeEnum.VERIFY_PASSWORD_AND_COMPANY_CONFIG.equals(passwordVerifyMode)) {
            QueryPayPwdRespVo queryPayPwdRespVo = searchEmployeePayPwdManager.queryExistPayPassword(employeeId);
            if (queryPayPwdRespVo.isExistPwd) {
                isVerifyPassword = Boolean.TRUE;
            } else if (!StringUtils.isEmpty(companyId)){
                //如果没有设置支付密码，看企业配置
                Map<String, Integer> companyEmployeeAuth = iCommonService.queryCompanyEmployeeAuth(companyId, employeeId,
                        CommonAuthCodeEnums.PAYMENT_CODE.getCommonAuthType().getKey(), AuthCode.pwdPayCode());
                FinhubLogger.info("验证支付密码权限返回结构{},{},{}", companyId, employeeId, JsonUtils.toJson(companyEmployeeAuth));
                //返回map对象 1:开启支付密码，需要验证支付密码，如果没有支付密码就需要设置 0:不开启支付密码，不需要验证支付密码
                if (PayCenterConstant.PWD_PAY_EXIST_AUTH == companyEmployeeAuth.get(CommonAuthCodeEnums.PAYMENT_CODE.getKey())) {
                    isGoSettingPassword = Boolean.TRUE;
                }
            } else {
                isGoSettingPassword = Boolean.TRUE;
            }
        }

        if (SasSmsVerifyModeEnum.VERIFY_SMS.equals(businessCaseEnum.getSmsVerifyMode())) {
            isVerifySms = true;
        } else if (SasSmsVerifyModeEnum.AS_APPROPRIATE_VERIFY.equals(businessCaseEnum.getSmsVerifyMode())) {
            //云闪付 大额交易校验短信验证码
            if (SecurityBusinessCaseEnum.UNION_PAY.getCode().equals(businessCase)) {
                BigDecimal amount = new BigDecimal(reqVO.getAmount());
                if (smsVerifyThreshold.compareTo(amount) <= 0) {
                    isVerifySms = Boolean.TRUE;
                }
            }
        }

        respVO.setIsGoSettingPassword(isGoSettingPassword);
        respVO.setIsVerifyPassword(isVerifyPassword);
        respVO.setIsVerifySms(isVerifySms);
        return respVO;
    }

    @Override
    public SasSecurityVerifyRespDTO queryIsSecurityVerifyPass(SasSecurityVerifyReqDTO securityVerifyReqDTO) {
        FinhubLogger.info("【安全认证模块】判断是否通过安全认证，reqVo: {}", JSON.toJSONString(securityVerifyReqDTO));
        SasSecurityVerifyRespDTO respDTO = new SasSecurityVerifyRespDTO();
        try {
            //检查是否通过安全认证 开关
            Boolean isCheckSecurityVerify = Boolean.TRUE;
            Object securityVerifySwitch = redisDao.getRedisTemplate().opsForValue().get(SECURITY_VERIFY_SWITCH);
            //1: 不校验 直接通过
            if ("1".equals(securityVerifySwitch)) {
                isCheckSecurityVerify = Boolean.FALSE;

            }

            String businessId = securityVerifyReqDTO.getSasBusinessId();
            String employeeId = securityVerifyReqDTO.getEmployeeId();
            String companyId = securityVerifyReqDTO.getCompanyId();

            boolean isPwdVerifyPass = sasEmployeePayPwdManager.queryIsVerifyPwdPass(businessId, employeeId);
            boolean isSmsVerifyPass = sasPayCaptchaManager.queryIsVerifyCaptchaPass(businessId, employeeId);

            //获取安全认证模式
            SasSecurityVerifyModeReqVO securityVerifyModeReqVO = new SasSecurityVerifyModeReqVO(securityVerifyReqDTO.getBusinessCase(), securityVerifyReqDTO.getBizPayType(), securityVerifyReqDTO.getAmount());
            SasSecurityVerifyModeRespVO securityVerifyMode = getSecurityVerifyMode(securityVerifyModeReqVO, companyId, employeeId);

            Boolean isSecurityVerifyPass = Boolean.TRUE;
            if (securityVerifyMode.getIsGoSettingPassword()) {
                isSecurityVerifyPass = Boolean.FALSE;
                dingDingMsgService.sendMsg("用户未设置支付密码进行支付，reqDTO: " + JSON.toJSONString(securityVerifyReqDTO));
            }
            if (securityVerifyMode.getIsVerifyPassword() && !isPwdVerifyPass) {
                isSecurityVerifyPass = Boolean.FALSE;
                dingDingMsgService.sendMsg("用户未完成支付密码校验进行支付，reqDTO: " + JSON.toJSONString(securityVerifyReqDTO));
            }
            if (securityVerifyMode.getIsVerifySms() && !isSmsVerifyPass) {
                isSecurityVerifyPass = Boolean.FALSE;
                dingDingMsgService.sendMsg("用户未完成短信验证码校验进行支付，reqDTO: " + JSON.toJSONString(securityVerifyReqDTO));
            }

            respDTO.setIsSecurityVerifyPass(isCheckSecurityVerify ? isSecurityVerifyPass : Boolean.TRUE );
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("【安全认证模块】查询支付单是否通过安全校验报错", e);
            respDTO.setIsSecurityVerifyPass(Boolean.TRUE);
            return respDTO;
        }
    }

    /**
     * 获取支付密码校验模式
     */
    SasPasswordVerifyModeEnum getPasswordVerifyMode(SecurityBusinessCaseEnum businessCase, String bizPayType) {
        SasPasswordVerifyModeEnum passwordVerifyMode = businessCase.getPasswordVerifyMode();
        if (!SasPasswordVerifyModeEnum.AS_APPROPRIATE_VERIFY.equals(passwordVerifyMode)) {
            return passwordVerifyMode;
        }
        //收银台场景 看支付方式 确定支付密码验证模式
        if (SecurityBusinessCaseEnum.CASHIER.equals(businessCase)) {
            if (StringUtils.isEmpty(bizPayType)) {
                return SasPasswordVerifyModeEnum.DEFAULT_NO_VERIFY;
            }
            //分贝币支付、分贝券支付、虚拟卡个人资金支付、绑定银行卡支付  要验证支付密码
            if (bizPayType.contains(BizPayType.FBB.name()) || bizPayType.contains(BizPayType.FBQ.name()) ||
                    bizPayType.contains(BizPayType.BANK_CARD.name()) || bizPayType.contains(BizPayType.BIND_BANK_CARD.name())) {
                return SasPasswordVerifyModeEnum.VERIFY_PASSWORD_AND_COMPANY_CONFIG;
            }

            //企业支付、红包券支付 看企业配置
            if (bizPayType.contains(BizPayType.COMPANY.name()) || bizPayType.contains(BizPayType.REDCOUPON.name())) {
                return SasPasswordVerifyModeEnum.COMPANY_CONFIG;
            }
        }

        return SasPasswordVerifyModeEnum.DEFAULT_NO_VERIFY;
    }
}
