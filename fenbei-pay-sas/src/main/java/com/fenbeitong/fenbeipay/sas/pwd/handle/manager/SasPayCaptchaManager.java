package com.fenbeitong.fenbeipay.sas.pwd.handle.manager;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.model.vo.sas.CaptchaVerifyReqVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;

/**
 * @Description: 短信验证码安全认证Manager
 * @Author: liyi
 * @Date: 2022/8/17 下午2:24
 */
public interface SasPayCaptchaManager {
    /**
     * 发送短信验证码
     * @param phone
     * @param payBalance
     * @param businessCase
     * @return
     */
    JSONObject sendCaptcha(String phone, String payBalance, String businessCase);

    /**
     * 验证短信验证码
     * @param reqVO
     * @param userInfo
     * @return
     */
    JSONObject verifyCaptcha(CaptchaVerifyReqVO reqVO, UserInfoVO userInfo);

    /**
     * 查询是否短信验证码验证通过
     * @param businessId
     * @param employeeId
     * @return
     */
    boolean queryIsVerifyCaptchaPass(String businessId, String employeeId);
}
