package com.fenbeitong.fenbeipay.sas.pwd.handle.manager.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.constant.enums.sas.SecurityBusinessCaseEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.sas.SmsTemplateEnum;
import com.fenbeitong.fenbeipay.api.model.vo.sas.CaptchaVerifyReqVO;
import com.fenbeitong.fenbeipay.api.util.CaptchaUtil;
import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.sas.pwd.base.manager.BaseSasPayManagerImpl;
import com.fenbeitong.fenbeipay.sas.pwd.handle.manager.SasPayCaptchaManager;
import com.fenbeitong.harmony.captcha.api.dto.CaptchaSendResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.fenbeitong.harmony.captcha.api.service.ICaptchaService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 短信验证码安全认证Manager
 * @Author: liyi
 * @Date: 2022/8/17 下午3:45
 */
@Service
public class SasPayCaptchaManangerImpl extends BaseSasPayManagerImpl implements SasPayCaptchaManager {
    @Autowired
    private ICaptchaService iCaptchaService;


    @Override
    public JSONObject sendCaptcha(String phone, String payBalance, String businessCase) {
        String captcha = CaptchaUtil.createCaptcha(4);
        Map<String, String> smsParams = new HashMap<>();
        smsParams.put("var1", captcha);
        SecurityBusinessCaseEnum businessCaseEnum = SecurityBusinessCaseEnum.getBusinessCaseEnumByCode(businessCase);
        CaptchaSendResult captchaSendResult;
        if (businessCaseEnum == null) {
            smsParams.put("var2", payBalance);
            captchaSendResult =
                    iCaptchaService.sendCaptcha(phone, SmsTemplateEnum.SAS_SMS_VERIFY.getId(), smsParams, captcha, 5, TimeUnit.MINUTES);
        } else {
            smsParams.put("var2", businessCaseEnum.getSmsShowName());
            smsParams.put("var3", payBalance);
            captchaSendResult =
                    iCaptchaService.sendCaptcha(phone, SmsTemplateEnum.SAS_SMS_CASE_VERIFY.getId(), smsParams, captcha, 5, TimeUnit.MINUTES);
        }

        JSONObject result = new JSONObject();
        result.put("sequenceNo", captchaSendResult.getSequenceNo());

        return result;
    }

    @Override
    public JSONObject verifyCaptcha(CaptchaVerifyReqVO reqVO, UserInfoVO userInfo) {
        Boolean isValid = iCaptchaService.verifyCaptcha(reqVO.getSequenceNo(), reqVO.getCaptcha());
        JSONObject result = new JSONObject();
        result.put("isValid", isValid);
        if (isValid) {
            String sasBusinessId = reqVO.getBusinessId();
            if (StringUtils.isEmpty(sasBusinessId)) {
                sasBusinessId = "SAS" + IdUtil.objectId();
            }
            redisDao.getValueOperations().set(CAPTCHA_SECURE_VERIFY_PREX + userInfo.getId() + sasBusinessId, VERIFY_PASS_VALUE, SECURE_VERIFY_TIMEOUT, TimeUnit.SECONDS);
            result.put("sasBusinessId", sasBusinessId);
        }
        return result;
    }

    @Override
    public boolean queryIsVerifyCaptchaPass(String businessId, String employeeId) {
        if (StringUtils.isNotBlank(businessId)) {
            String verifyResult = redisDao.getValueOperations().get(CAPTCHA_SECURE_VERIFY_PREX + employeeId + businessId);
            if (VERIFY_PASS_VALUE.equals(verifyResult)) {
                return true;
            }
        }
        return false;
    }
}
