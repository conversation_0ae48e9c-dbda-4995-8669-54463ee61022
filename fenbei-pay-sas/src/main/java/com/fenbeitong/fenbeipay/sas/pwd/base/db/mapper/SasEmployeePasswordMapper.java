package com.fenbeitong.fenbeipay.sas.pwd.base.db.mapper;

import com.fenbeitong.fenbeipay.core.db.base.SuperMapper;
import com.fenbeitong.fenbeipay.dto.sas.SasEmployeePassword;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Date;

public interface SasEmployeePasswordMapper extends SuperMapper<SasEmployeePassword> {

    /**
     * @Description: 密码输入失败：锁定次数
     * @Param: [employeeId]
     */
    @Update("update tb_sas_employee_password set fail_num = fail_num - 1, " +
            "update_time = now() " +
            "where employee_id = #{employeeId}")
    int reducePayPwdFailNum(@Param("employeeId") String employeeId);

    /**
     * @Description: 密码输入失败：锁定密码
     * @Param: [employeeId, lockOpenTime]
     */
    @Update("update tb_sas_employee_password set pwd_status= 2, " +
            "update_time = now(),lock_open_time = #{lockOpenTime} " +
            "where employee_id = #{employeeId}")
    int lockPayPwdFailNum(@Param("employeeId")  String employeeId,@Param("lockOpenTime") Date lockOpenTime );
    /**
     * @Description: 密码改为正常次数
     * @Param: [employeeId, employeePwd]
     */
    @Update("update tb_sas_employee_password set fail_num = 5, " +
            "update_time = now()" +
            "where employee_id = #{employeeId}")
    int updateNormalPwdNum(@Param("employeeId") String employeeId);

    /**
     * @Description: 修改密码恢 复数据正常
     * @Param: [employeeId, employeePwd]
     */
    @Update("update tb_sas_employee_password set fail_num = 5," +
            "update_time = now() ,employee_pwd = #{employeePwd}" +
            "where employee_id = #{employeeId}")
    int updatePayPassword(@Param("employeeId") String employeeId,@Param("employeePwd") String employeePwd);
    /**
     * @Description: 重置支付密码
     * @Param: [employeeId, employeePwd]
     */
    @Update("update tb_sas_employee_password set fail_num = 5, pwd_status= 1, lock_open_time = null , " +
            "update_time = now() ,employee_pwd = #{employeePwd}" +
            "where employee_id = #{employeeId}")
    int resetPayPassword(@Param("employeeId") String employeeId,@Param("employeePwd") String employeePwd);
    /**
     * @Description: 开锁设置正常次数
     * @Param: [id]
     */
    @Update("update tb_sas_employee_password set fail_num = 5,lock_open_time = null, pwd_status= 1, " +
            "update_time = now()" +
            "where id = #{id}")
    int unlockPayPassword(@Param("id") Long id);


}