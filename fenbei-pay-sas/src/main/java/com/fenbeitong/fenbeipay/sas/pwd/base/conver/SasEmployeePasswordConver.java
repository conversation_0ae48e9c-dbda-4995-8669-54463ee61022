package com.fenbeitong.fenbeipay.sas.pwd.base.conver;

import com.fenbeitong.fenbeipay.core.model.vo.account.UserInfoVO;
import com.fenbeitong.fenbeipay.dto.sas.SasEmployeePassword;
import com.fenbeitong.fenbeipay.sas.pwd.base.enums.SasPayPwdStatus;

import java.util.Date;

/**
 * @Title: SasEmployeePasswordConver
 * @ProjectName fenbei-pay
 * @Description: 支付密码转换类
 */
public class SasEmployeePasswordConver {

    /**
      * @Description: 设置密码参数
      * @Param: [userInfo, signFactor, employeePwd, sasEmployeePassword]
      * @Author: wh
      */
    public static void toCreatePayPasswordReq(UserInfoVO userInfo, String signFactor, String employeePwd,
                                              int DEFAULT_FAIL_NUM, SasEmployeePassword sasEmployeePassword) {
        Date date = new Date();
        sasEmployeePassword.setEmployeeId(userInfo.getId());
        sasEmployeePassword.setEmployeeName(userInfo.getName());
        sasEmployeePassword.setEmployeePhone(userInfo.getPhone());
        sasEmployeePassword.setEmployeePwd(employeePwd);
        sasEmployeePassword.setSignFactor(signFactor);
        sasEmployeePassword.setUserType(1);
        sasEmployeePassword.setFailNum(DEFAULT_FAIL_NUM);
        sasEmployeePassword.setCreateTime(date);
        sasEmployeePassword.setUpdateTime(date);
        sasEmployeePassword.setPwdStatus(SasPayPwdStatus.NORMAL.getKey());
    }
}
