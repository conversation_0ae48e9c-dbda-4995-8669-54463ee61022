<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fenbei-pay</artifactId>
        <groupId>com.fenbeitong</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>安全验证服务</description>
    <artifactId>fenbei-pay-sas</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-dto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.config</groupId>
            <artifactId>fenbei-config-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>harmony-third-examine-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>harmony-captcha-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.luastar</groupId>
            <artifactId>swift-i18n</artifactId>
        </dependency>
    </dependencies>

</project>