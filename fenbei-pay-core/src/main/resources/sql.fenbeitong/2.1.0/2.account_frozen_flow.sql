CREATE TABLE "public"."account_frozen_flow" (
	"id" varchar NOT NULL COLLATE "default",
	"account_id" int4 NOT NULL,
	"amount" numeric(21,4) NOT NULL DEFAULT 0,
	"purchase_amount" numeric(21,4) DEFAULT 0,
	"order_type" int4,
	"order_id" varchar COLLATE "default",
	"balance" numeric(21,4),
	"comment" varchar COLLATE "default",
	"operator_id" varchar COLLATE "default",
	"operator_name" varchar COLLATE "default",
	"employee_id" varchar COLLATE "default",
	"operator_type" int4 DEFAULT 1,
	"customer_service_id" varchar COLLATE "default",
	"customer_service_name" varchar COLLATE "default",
	"state" int4 DEFAULT 1,
	"create_time" timestamp(6) NULL,
	"update_time" timestamp(6) NULL
)
WITH (OIDS=FALSE);
ALTER TABLE "public"."account_frozen_flow" OWNER TO "postgres";

COMMENT ON TABLE "public"."account_frozen_flow" IS '账户冻结记录';
COMMENT ON COLUMN "public"."account_frozen_flow"."account_id" IS '公司账户id';
COMMENT ON COLUMN "public"."account_frozen_flow"."amount" IS '金额（元）';
COMMENT ON COLUMN "public"."account_frozen_flow"."purchase_amount" IS '采购价格';
COMMENT ON COLUMN "public"."account_frozen_flow"."order_type" IS '订单类型：3、用车，7、国内机票，11、酒店，15、火车，20、采购，30、用餐，40、国际机票，100、用车保险，101、国内机票保险，102、酒店保险，103、火车保险';
COMMENT ON COLUMN "public"."account_frozen_flow"."order_id" IS '公司订单id（消费）';
COMMENT ON COLUMN "public"."account_frozen_flow"."balance" IS '账户余额';
COMMENT ON COLUMN "public"."account_frozen_flow"."comment" IS '备注';
COMMENT ON COLUMN "public"."account_frozen_flow"."operator_id" IS '操作人id';
COMMENT ON COLUMN "public"."account_frozen_flow"."operator_name" IS '操作人姓名';
COMMENT ON COLUMN "public"."account_frozen_flow"."employee_id" IS '下单人id';
COMMENT ON COLUMN "public"."account_frozen_flow"."operator_type" IS '操作类型：1:普通下单 2:回填';
COMMENT ON COLUMN "public"."account_frozen_flow"."customer_service_id" IS '客服id';
COMMENT ON COLUMN "public"."account_frozen_flow"."customer_service_name" IS '客服姓名';
COMMENT ON COLUMN "public"."account_frozen_flow"."state" IS '冻结状态：1:冻结 2:解冻';

ALTER TABLE "public"."account_frozen_flow" ADD PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE;

