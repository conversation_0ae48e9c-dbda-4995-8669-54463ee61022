/*
 Navicat Premium Data Transfer

 Source Server         : ucenter
 Source Server Type    : PostgreSQL
 Source Server Version : 90405
 Source Host           : **********:5432
 Source Catalog        : fenbeitong
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90405
 File Encoding         : 65001

 Date: 23/10/2018 10:43:04
*/


-- ----------------------------
-- Table structure for person_refund_record
-- ----------------------------
DROP TABLE IF EXISTS "person_refund_record";
CREATE TABLE "person_refund_record" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL,
  "order_id" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "txn_id" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "refund_no" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "refund_amount" int4 DEFAULT NULL,
  "refund_reason" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "refund_status" int4 DEFAULT NULL,
  "receive_time" timestamp(6) DEFAULT NULL::timestamp without time zone,
  "create_time" timestamp(6) DEFAULT NULL::timestamp without time zone,
  "update_time" timestamp(6) DEFAULT NULL::timestamp without time zone,
  "complete_time" timestamp(6) DEFAULT NULL::timestamp without time zone,
  "ex_code" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "ex_msg" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "channel" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "notifyurl" varchar COLLATE "pg_catalog"."default" DEFAULT NULL
)
;
ALTER TABLE "person_refund_record" OWNER TO "postgres";
COMMENT ON COLUMN "person_refund_record"."order_id" IS '订单号';
COMMENT ON COLUMN "person_refund_record"."txn_id" IS '付钱拉系统订单号';
COMMENT ON COLUMN "person_refund_record"."refund_no" IS '退款单号';
COMMENT ON COLUMN "person_refund_record"."refund_amount" IS '退款金额';
COMMENT ON COLUMN "person_refund_record"."refund_reason" IS '退款原因';
COMMENT ON COLUMN "person_refund_record"."refund_status" IS '7,退款处理中，8退款成功，9退款失败';
COMMENT ON COLUMN "person_refund_record"."receive_time" IS '退款接收时间';
COMMENT ON COLUMN "person_refund_record"."create_time" IS '创建时间';
COMMENT ON COLUMN "person_refund_record"."update_time" IS '更新时间';
COMMENT ON COLUMN "person_refund_record"."complete_time" IS '退款完成时间';
COMMENT ON COLUMN "person_refund_record"."ex_code" IS '错误码';
COMMENT ON COLUMN "person_refund_record"."ex_msg" IS '错误描述';
COMMENT ON COLUMN "person_refund_record"."channel" IS '交易渠道';
COMMENT ON COLUMN "person_refund_record"."notifyurl" IS '退款回调接口';

-- ----------------------------
-- Primary Key structure for table person_refund_record
-- ----------------------------
ALTER TABLE "person_refund_record" ADD CONSTRAINT "person_refund_record_pkey" PRIMARY KEY ("id");
ALTER TABLE person_refund_record ADD COLUMN refund_order_status  VARCHAR;
ALTER TABLE person_pay_record ADD COLUMN refund_order_status  int ;
ALTER TABLE person_refund_record ADD COLUMN retry_num  int;
ALTER TABLE person_refund_record ADD COLUMN next_retry_time  TIMESTAMP;
comment on COLUMN "public"."person_refund_record"."next_retry_time" is '下次重试时间';
comment on COLUMN "public"."person_pay_record"."refund_order_status" is '退款状态7退款进行中，8退款成功，9退款失败';
comment on COLUMN "public"."person_refund_record"."retry_num" is '退款重试次数';