/*
 Navicat Premium Data Transfer

 Source Server         : ucenter
 Source Server Type    : PostgreSQL
 Source Server Version : 90405
 Source Host           : **********:5432
 Source Catalog        : fenbeitong
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90405
 File Encoding         : 65001

 Date: 19/10/2018 18:03:23
*/


-- ----------------------------
-- Table structure for person_pay_refund_log
-- ----------------------------
DROP TABLE IF EXISTS "person_pay_refund_log";
CREATE TABLE "person_pay_refund_log" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL,
  "order_no" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "channel" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "amount" int4 DEFAULT NULL,
  "notify_url" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "receive_time" timestamp(6) DEFAULT NULL,
  "complete_time" timestamp(6) DEFAULT NULL,
  "receive_msg" varchar COLLATE "pg_catalog"."default" DEFAULT NULL,
  "return_msg" varchar COLLATE "pg_catalog"."default" DEFAULT NULL
)
;
ALTER TABLE "person_pay_refund_log" OWNER TO "postgres";

-- ----------------------------
-- Primary Key structure for table person_pay_refund_log
-- ----------------------------
ALTER TABLE "person_pay_refund_log" ADD CONSTRAINT "person_pay_refund_log_pkey" PRIMARY KEY ("id");
ALTER TABLE person_pay_refund_log ADD COLUMN fql_request_msg  varchar;
ALTER TABLE person_pay_refund_log ADD COLUMN cj_repons_msg  varchar;
comment on COLUMN "public"."person_pay_refund_log"."fql_request_msg" is '付钱啦请求日志';
comment on COLUMN "public"."person_pay_refund_log"."cj_repons_msg" is '场景返回日志';
