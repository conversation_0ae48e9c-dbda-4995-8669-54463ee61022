<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.na.db.mapper.TbAccountReceiveInfoDetailsMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.TbAccountReceiveInfoDetails">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="bank_id" jdbcType="VARCHAR" property="bankId" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="sub_bank_id" jdbcType="VARCHAR" property="subBankId" />
    <result column="sub_bank_name" jdbcType="VARCHAR" property="subBankName" />
    <result column="union_pay_no" jdbcType="VARCHAR" property="unionPayNo" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
    <result column="delete_tag" jdbcType="BIT" property="deleteTag" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="third_id" jdbcType="VARCHAR" property="thirdId" />
  </resultMap>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into tb_account_receive_info_details(id,third_id,company_id,user_id,account_name,account_no,type,bank_id,bank_name,sub_bank_id,sub_bank_name,union_pay_no,is_default,state)
    values
    <foreach collection = "accountReceiveInfoDetails" item= "accountReceiveInfoDetail" index = "index" separator = ",">
      (
      #{accountReceiveInfoDetail.id},{accountReceiveInfoDetail.thirdId},
      #{accountReceiveInfoDetail.companyId},#{accountReceiveInfoDetail.userId},#{accountReceiveInfoDetail.accountName},
      #{accountReceiveInfoDetail.accountNo},#{accountReceiveInfoDetail.type},#{accountReceiveInfoDetail.bankId},
      #{accountReceiveInfoDetail.bankName},#{accountReceiveInfoDetail.subBankId},#{accountReceiveInfoDetail.subBankName},
      #{accountReceiveInfoDetail.unionPayNo},#{accountReceiveInfoDetail.isDefault},
      #{accountReceiveInfoDetail.state}
      )
    </foreach>
  </insert>
<!--  <update id="batchUpdate" parameterType="java.util.List">-->
<!--    <foreach collection = "accountReceiveInfoDetails" item= "accountReceiveInfoDetail" index = "index" separator = ",">-->
<!--    update tb_account_receive_info_details-->
<!--    <set>-->
<!--        <if test="accountReceiveInfoDetail.userId != null">-->
<!--            user_id =#{accountReceiveInfoDetail.userId,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.thirdId != null">-->
<!--            third_id =#{accountReceiveInfoDetail.thirdId,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.companyId != null">-->
<!--            company_id = #{accountReceiveInfoDetail.companyId,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.accountName != null">-->
<!--            account_name = #{accountReceiveInfoDetail.accountName,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.accountNo != null">-->
<!--            account_no = #{accountReceiveInfoDetail.accountNo,jdbcType=INTEGER},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.type != null">-->
<!--            type = #{accountReceiveInfoDetail.type,jdbcType=INTEGER},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.bankId != null">-->
<!--            bank_id = #{accountReceiveInfoDetail.bankId,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.bankName != null">-->
<!--            bank_name = #{accountReceiveInfoDetail.bankName,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.subBankId != null">-->
<!--            sub_bank_id = #{accountReceiveInfoDetail.subBankId,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.subBankName != null">-->
<!--            sub_bank_name = #{accountReceiveInfoDetail.subBankName,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.unionPayNo != null">-->
<!--            union_pay_no = #{accountReceiveInfoDetail.unionPayNo,jdbcType=VARCHAR},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.isDefault != null">-->
<!--            is_default = #{accountReceiveInfoDetail.isDefault,jdbcType=INTEGER},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.isDefault != null">-->
<!--            delete_tag = #{accountReceiveInfoDetail.deleteTag,jdbcType=INTEGER},-->
<!--        </if>-->
<!--        <if test="accountReceiveInfoDetail.state != null">-->
<!--            state = #{accountReceiveInfoDetail.state,jdbcType=INTEGER},-->
<!--        </if>-->
<!--    </set>-->
<!--    where id = #{accountReceiveInfoDetail.id,jdbcType=VARCHAR}-->
<!--    </foreach>-->
<!--  </update>-->
</mapper>