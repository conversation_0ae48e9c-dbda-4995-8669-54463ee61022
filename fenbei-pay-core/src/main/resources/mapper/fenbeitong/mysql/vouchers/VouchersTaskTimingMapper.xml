<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskTimingMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskTiming">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vouchers_task_timing_id" jdbcType="VARCHAR" property="vouchersTaskTimingId" />
    <result column="vouchers_task_timing_name" jdbcType="VARCHAR" property="vouchersTaskTimingName" />
    <result column="grant_status" jdbcType="SMALLINT" property="grantStatus" />
    <result column="task_status" jdbcType="SMALLINT" property="taskStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="grant_period_type" jdbcType="SMALLINT" property="grantPeriodType" />
    <result column="grant_period_start" jdbcType="TIMESTAMP" property="grantPeriodStart" />
    <result column="grant_period_end" jdbcType="TIMESTAMP" property="grantPeriodEnd" />
    <result column="grant_date" jdbcType="TIMESTAMP" property="grantDate" />
    <result column="grant_time" jdbcType="SMALLINT" property="grantTime" />
    <result column="grant_frequency" jdbcType="VARCHAR" property="grantFrequency" />
    <result column="grant_target_type" jdbcType="SMALLINT" property="grantTargetType" />
    <result column="grant_target" jdbcType="VARCHAR" property="grantTarget" />
    <result column="notice_type" jdbcType="SMALLINT" property="noticeType" />
    <result column="notice_email" jdbcType="VARCHAR" property="noticeEmail" />
    <result column="notice_phone" jdbcType="VARCHAR" property="noticePhone" />
    <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
    <result column="voucher_denomination" jdbcType="DECIMAL" property="voucherDenomination" />
    <result column="voucher_type_list" jdbcType="VARCHAR" property="voucherTypeList" />
    <result column="voucher_term_type" jdbcType="SMALLINT" property="voucherTermType" />
    <result column="voucher_term_validity" jdbcType="VARCHAR" property="voucherTermValidity" />
    <result column="voucher_expiry_notice" jdbcType="INTEGER" property="voucherExpiryNotice" />
    <result column="can_transfer" jdbcType="INTEGER" property="canTransfer" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="task_desc" jdbcType="VARCHAR" property="taskDesc" />
    <result column="voucher_desc" jdbcType="VARCHAR" property="voucherDesc" />
    <result column="cost_info" jdbcType="VARCHAR" property="costInfo" />
    <result column="date_of_expense" jdbcType="VARCHAR" property="dateOfExpense" />
    <result column="open_enable" jdbcType="INTEGER" property="openEnable" />
    <result column="background_url" jdbcType="VARCHAR" property="backgroundUrl" />
    <result column="background_name" jdbcType="VARBINARY" property="backgroundName" />
    <result column="notice_content" jdbcType="VARCHAR" property="noticeContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, vouchers_task_timing_id, vouchers_task_timing_name, grant_status, task_status,
    create_time, update_time, grant_period_type, grant_period_start, grant_period_end,
    grant_date, grant_time, grant_frequency, grant_target_type, grant_target, notice_type, notice_email, notice_phone,
    voucher_name, voucher_denomination, voucher_type_list,voucher_term_type, voucher_term_validity, voucher_expiry_notice, can_transfer,
    company_id, operation_user_id, task_desc,voucher_desc,cost_info,date_of_expense, open_enable , background_url,background_name, notice_content
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskTimingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_task_timing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from vouchers_task_timing
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_task_timing
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskTimingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_task_timing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskTiming">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_task_timing (id, vouchers_task_timing_id, vouchers_task_timing_name,
      grant_status, task_status, create_time,
      update_time, grant_period_type, grant_period_start,
      grant_period_end, grant_time, grant_target_type,
      grant_target, notice_type, notice_email,
      notice_phone, voucher_name, voucher_denomination,
      voucher_type_list, voucher_term_validity, voucher_expiry_notice, voucher_term_type, can_transfer,
      company_id, operation_user_id, task_desc,voucher_desc
      )
    values (#{id,jdbcType=BIGINT}, #{vouchersTaskTimingId,jdbcType=VARCHAR}, #{vouchersTaskTimingName,jdbcType=VARCHAR},
      #{grantStatus,jdbcType=SMALLINT}, #{taskStatus,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{grantPeriodType,jdbcType=SMALLINT}, #{grantPeriodStart,jdbcType=TIMESTAMP},
      #{grantPeriodEnd,jdbcType=TIMESTAMP}, #{grantTime,jdbcType=SMALLINT}, #{grantTargetType,jdbcType=SMALLINT},
      #{grantTarget,jdbcType=VARCHAR}, #{noticeType,jdbcType=SMALLINT}, #{noticeEmail,jdbcType=VARCHAR},
      #{noticePhone,jdbcType=VARCHAR}, #{voucherName,jdbcType=VARCHAR}, #{voucherDenomination,jdbcType=DECIMAL},
      #{voucherTypeList,jdbcType=VARCHAR}, #{voucherTermValidity,jdbcType=VARCHAR}, #{voucherExpiryNotice,jdbcType=INTEGER}, #{voucherTermType,jdbcType=SMALLINT}, #{canTransfer,jdbcType=INTEGER},
      #{companyId,jdbcType=VARCHAR}, #{operationUserId,jdbcType=VARCHAR}, #{taskDesc,jdbcType=VARCHAR},#{voucherDesc,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskTiming">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_task_timing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vouchersTaskTimingId != null">
        vouchers_task_timing_id,
      </if>
      <if test="vouchersTaskTimingName != null">
        vouchers_task_timing_name,
      </if>
      <if test="grantStatus != null">
        grant_status,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="grantPeriodType != null">
        grant_period_type,
      </if>
      <if test="grantPeriodStart != null">
        grant_period_start,
      </if>
      <if test="grantPeriodEnd != null">
        grant_period_end,
      </if>
      <if test="grantTime != null">
        grant_time,
      </if>
      <if test="grantFrequency != null">
        grant_frequency,
      </if>
      <if test="grantTargetType != null">
        grant_target_type,
      </if>
      <if test="grantTarget != null">
        grant_target,
      </if>
      <if test="noticeType != null">
        notice_type,
      </if>
      <if test="noticeEmail != null">
        notice_email,
      </if>
      <if test="noticePhone != null">
        notice_phone,
      </if>
      <if test="voucherName != null">
        voucher_name,
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination,
      </if>
      <if test="voucherTypeList != null">
        voucher_type_list,
      </if>
      <if test="voucherTermValidity != null">
        voucher_term_validity,
      </if>
      <if test="voucherExpiryNotice != null">
        voucher_expiry_notice,
      </if>
      <if test="voucherTermType != null">
        voucher_term_type,
      </if>
      <if test="canTransfer != null">
        can_transfer,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="taskDesc != null">
        task_desc,
      </if>
      <if test="voucherDesc != null">
        voucher_desc,
      </if>
      <if test="costInfo != null and costInfo !='' ">
        cost_info,
      </if>
      <if test="dateOfExpense != null and dateOfExpense !='' ">
        date_of_expense,
      </if>
      <if test="openEnable != null">
        open_enable,
      </if>
      <if test="backgroundUrl != null">
        background_url,
      </if>
      <if test="backgroundName != null">
        background_name,
      </if>
      <if test="noticeContent != null">
        notice_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vouchersTaskTimingId != null">
        #{vouchersTaskTimingId,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskTimingName != null">
        #{vouchersTaskTimingName,jdbcType=VARCHAR},
      </if>
      <if test="grantStatus != null">
        #{grantStatus,jdbcType=SMALLINT},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grantPeriodType != null">
        #{grantPeriodType,jdbcType=SMALLINT},
      </if>
      <if test="grantPeriodStart != null">
        #{grantPeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="grantPeriodEnd != null">
        #{grantPeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="grantTime != null">
        #{grantTime,jdbcType=SMALLINT},
      </if>
      <if test="grantFrequency != null">
        #{grantFrequency,jdbcType=VARCHAR},
      </if>
      <if test="grantTargetType != null">
        #{grantTargetType,jdbcType=SMALLINT},
      </if>
      <if test="grantTarget != null">
        #{grantTarget,jdbcType=VARCHAR},
      </if>
      <if test="noticeType != null">
        #{noticeType,jdbcType=SMALLINT},
      </if>
      <if test="noticeEmail != null">
        #{noticeEmail,jdbcType=VARCHAR},
      </if>
      <if test="noticePhone != null">
        #{noticePhone,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="voucherTypeList != null">
        #{voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="voucherTermValidity != null">
        #{voucherTermValidity,jdbcType=VARCHAR},
      </if>
      <if test="voucherExpiryNotice != null">
        #{voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="voucherTermType != null">
        #{voucherTermType,jdbcType=SMALLINT},
      </if>
      <if test="canTransfer != null">
        #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null">
        #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="voucherDesc != null">
        #{voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="costInfo != null">
        #{costInfo,jdbcType=VARCHAR},
      </if>
      <if test="dateOfExpense != null">
        #{dateOfExpense,jdbcType=VARCHAR},
      </if>
      <if test="openEnable != null">
        #{openEnable,jdbcType=INTEGER},
      </if>
      <if test="backgroundUrl != null">
        #{backgroundUrl,jdbcType=INTEGER},
      </if>
      <if test="backgroundName != null">
        #{backgroundName,jdbcType=INTEGER},
      </if>
      <if test="noticeContent != null">
        #{noticeContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskTimingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from vouchers_task_timing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_timing
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vouchersTaskTimingId != null">
        vouchers_task_timing_id = #{record.vouchersTaskTimingId,jdbcType=VARCHAR},
      </if>
      <if test="record.vouchersTaskTimingName != null">
        vouchers_task_timing_name = #{record.vouchersTaskTimingName,jdbcType=VARCHAR},
      </if>
      <if test="record.grantStatus != null">
        grant_status = #{record.grantStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.grantPeriodType != null">
        grant_period_type = #{record.grantPeriodType,jdbcType=SMALLINT},
      </if>
      <if test="record.grantPeriodStart != null">
        grant_period_start = #{record.grantPeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.grantPeriodEnd != null">
        grant_period_end = #{record.grantPeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.grantTime != null">
        grant_time = #{record.grantTime,jdbcType=SMALLINT},
      </if>
      <if test="record.grantFrequency != null">
        grant_frequency = #{record.grantFrequency,jdbcType=VARCHAR},
      </if>
      <if test="record.grantTargetType != null">
        grant_target_type = #{record.grantTargetType,jdbcType=SMALLINT},
      </if>
      <if test="record.grantTarget != null">
        grant_target = #{record.grantTarget,jdbcType=VARCHAR},
      </if>
      <if test="record.noticeType != null">
        notice_type = #{record.noticeType,jdbcType=SMALLINT},
      </if>
      <if test="record.noticeEmail != null">
        notice_email = #{record.noticeEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.noticePhone != null">
        notice_phone = #{record.noticePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherName != null">
        voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherDenomination != null">
        voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherTypeList != null">
        voucher_type_list = #{record.voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherTermValidity != null">
        voucher_term_validity = #{record.voucherTermValidity,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherExpiryNotice != null">
        voucher_expiry_notice = #{record.voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="record.voucherTermType != null">
        voucher_term_type = #{record.voucherTermType,jdbcType=SMALLINT},
      </if>
      <if test="record.canTransfer != null">
        can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserId != null">
        operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.taskDesc != null">
        task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherDesc != null">
        voucher_desc = #{record.voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.costInfo != null and record.costInfo !='' ">
        cost_info = #{record.costInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.dateOfExpense != null and record.dateOfExpense !='' ">
        date_of_expense = #{record.dateOfExpense,jdbcType=VARCHAR},
      </if>
      <if test="record.openEnable != null">
        open_enable = #{record.openEnable,jdbcType=INTEGER},
      </if>
      <if test="record.backgroundUrl != null">
        background_url = #{record.backgroundUrl,jdbcType=INTEGER},
      </if>
      <if test="record.backgroundName != null">
        background_name = #{record.backgroundName,jdbcType=INTEGER},
      </if>
      <if test="record.noticeContent != null">
        notice_content = #{record.noticeContent,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_timing
    set id = #{record.id,jdbcType=BIGINT},
      vouchers_task_timing_id = #{record.vouchersTaskTimingId,jdbcType=VARCHAR},
      vouchers_task_timing_name = #{record.vouchersTaskTimingName,jdbcType=VARCHAR},
      grant_status = #{record.grantStatus,jdbcType=SMALLINT},
      task_status = #{record.taskStatus,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      grant_period_type = #{record.grantPeriodType,jdbcType=SMALLINT},
      grant_period_start = #{record.grantPeriodStart,jdbcType=TIMESTAMP},
      grant_period_end = #{record.grantPeriodEnd,jdbcType=TIMESTAMP},
      grant_time = #{record.grantTime,jdbcType=SMALLINT},
      grant_target_type = #{record.grantTargetType,jdbcType=SMALLINT},
      grant_target = #{record.grantTarget,jdbcType=VARCHAR},
      notice_type = #{record.noticeType,jdbcType=SMALLINT},
      notice_email = #{record.noticeEmail,jdbcType=VARCHAR},
      notice_phone = #{record.noticePhone,jdbcType=VARCHAR},
      voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      voucher_type_list = #{record.voucherTypeList,jdbcType=VARCHAR},
      voucher_term_validity = #{record.voucherTermValidity,jdbcType=VARCHAR},
      voucher_expiry_notice = #{record.voucherExpiryNotice,jdbcType=INTEGER},
      voucher_term_type = #{record.voucherTermType,jdbcType=SMALLINT},
      can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      voucher_desc = #{record.voucherDesc,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskTiming">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_timing
    <set>
      <if test="vouchersTaskTimingId != null">
        vouchers_task_timing_id = #{vouchersTaskTimingId,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskTimingName != null">
        vouchers_task_timing_name = #{vouchersTaskTimingName,jdbcType=VARCHAR},
      </if>
      <if test="grantStatus != null">
        grant_status = #{grantStatus,jdbcType=SMALLINT},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grantPeriodType != null">
        grant_period_type = #{grantPeriodType,jdbcType=SMALLINT},
      </if>
      <if test="grantPeriodStart != null">
        grant_period_start = #{grantPeriodStart,jdbcType=TIMESTAMP},
      </if>
      <if test="grantPeriodEnd != null">
        grant_period_end = #{grantPeriodEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="grantDate != null">
        grant_date = #{grantDate,jdbcType=TIMESTAMP},
      </if>
      <if test="grantTime != null">
        grant_time = #{grantTime,jdbcType=SMALLINT},
      </if>
      <if test="grantFrequency != null">
        grant_frequency = #{grantFrequency,jdbcType=VARCHAR},
      </if>
      <if test="grantTargetType != null">
        grant_target_type = #{grantTargetType,jdbcType=SMALLINT},
      </if>
      <if test="grantTarget != null">
        grant_target = #{grantTarget,jdbcType=VARCHAR},
      </if>
      <if test="noticeType != null">
        notice_type = #{noticeType,jdbcType=SMALLINT},
      </if>
      <if test="noticeEmail != null">
        notice_email = #{noticeEmail,jdbcType=VARCHAR},
      </if>
      <if test="noticePhone != null">
        notice_phone = #{noticePhone,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        voucher_name = #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="voucherTypeList != null">
        voucher_type_list = #{voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="voucherTermValidity != null">
        voucher_term_validity = #{voucherTermValidity,jdbcType=VARCHAR},
      </if>
      <if test="voucherTermValidity != null">
        voucher_expiry_notice = #{voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="voucherTermType != null">
        voucher_term_type = #{voucherTermType,jdbcType=SMALLINT},
      </if>
      <if test="canTransfer != null">
        can_transfer = #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null">
        task_desc = #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="voucherDesc != null">
        voucher_desc = #{voucherDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskTiming">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_timing
    set vouchers_task_timing_id = #{vouchersTaskTimingId,jdbcType=VARCHAR},
      vouchers_task_timing_name = #{vouchersTaskTimingName,jdbcType=VARCHAR},
      grant_status = #{grantStatus,jdbcType=SMALLINT},
      task_status = #{taskStatus,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      grant_period_type = #{grantPeriodType,jdbcType=SMALLINT},
      grant_period_start = #{grantPeriodStart,jdbcType=TIMESTAMP},
      grant_period_end = #{grantPeriodEnd,jdbcType=TIMESTAMP},
      grant_time = #{grantTime,jdbcType=SMALLINT},
      grant_target_type = #{grantTargetType,jdbcType=SMALLINT},
      grant_target = #{grantTarget,jdbcType=VARCHAR},
      notice_type = #{noticeType,jdbcType=SMALLINT},
      notice_email = #{noticeEmail,jdbcType=VARCHAR},
      notice_phone = #{noticePhone,jdbcType=VARCHAR},
      voucher_name = #{voucherName,jdbcType=VARCHAR},
      voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      voucher_type_list = #{voucherTypeList,jdbcType=VARCHAR},
      voucher_term_validity = #{voucherTermValidity,jdbcType=VARCHAR},
      voucher_expiry_notice = #{voucherExpiryNotice,jdbcType=INTEGER},
      voucher_term_type = #{voucherTermType,jdbcType=SMALLINT},
      can_transfer = #{canTransfer,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=VARCHAR},
      operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      voucher_desc = #{voucherDesc,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
