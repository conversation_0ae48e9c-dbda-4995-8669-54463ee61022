<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersOperationFlowMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="voucher_id" jdbcType="VARCHAR" property="voucherId" />
    <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="marks" jdbcType="VARCHAR" property="marks" />
    <result column="business_type" jdbcType="SMALLINT" property="businessType" />
    <result column="business_sub_type" jdbcType="SMALLINT" property="businessSubType" />
    <result column="account_type" jdbcType="SMALLINT" property="accountType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="order_snapshot" jdbcType="VARCHAR" property="orderSnapshot" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
    <result column="employee_department" jdbcType="VARCHAR" property="employeeDepartment" />
    <result column="employee_department_id" jdbcType="VARCHAR" property="employeeDepartmentId" />
    <result column="employee_department_full" jdbcType="VARCHAR" property="employeeDepartmentFull" />
    <result column="write_invoice_type" jdbcType="INTEGER" property="writeInvoiceType" />
    <result column="write_invoice_status" jdbcType="INTEGER" property="writeInvoiceStatus" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
    <result column="account_sub_flow_id" jdbcType="VARCHAR" property="accountSubFlowId" />
    <result column="cost_attribution_sign" jdbcType="INTEGER" property="costAttributionSign" />
    <result column="voucher_grant_time" jdbcType="TIMESTAMP" property="voucherGrantTime" />
    <result column="voucher_source" jdbcType="INTEGER" property="voucherSource" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
    <result column="original_voucher_id" jdbcType="VARCHAR" property="originalVoucherId" />
    <result column="parent_voucher_id" jdbcType="VARCHAR" property="parentVoucherId" />
    <result column="redcoupon_grant_record_id" jdbcType="VARCHAR" property="redcouponGrantRecordId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, voucher_id, voucher_name, order_no, amount, balance, type, marks, business_type, 
    business_sub_type, account_type, create_time, order_snapshot, fb_order_id, biz_no, 
    refund_order_id, company_id, employee_id, employee_name, employee_phone, employee_department, 
    employee_department_id, employee_department_full, write_invoice_type, write_invoice_status, 
    bill_status, deduction_account_type, account_model, account_sub_id, account_sub_flow_id, 
    cost_attribution_sign, voucher_grant_time, voucher_source, bank_name, bank_account_no, 
    is_ding, original_voucher_id, parent_voucher_id,redcoupon_grant_record_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlowExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_operation_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from vouchers_operation_flow
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_operation_flow
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlowExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_operation_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_operation_flow (id, voucher_id, voucher_name, 
      order_no, amount, balance, 
      type, marks, business_type, 
      business_sub_type, account_type, create_time, 
      order_snapshot, fb_order_id, biz_no, 
      refund_order_id, company_id, employee_id, 
      employee_name, employee_phone, employee_department, 
      employee_department_id, employee_department_full, 
      write_invoice_type, write_invoice_status, bill_status, 
      deduction_account_type, account_model, account_sub_id, 
      account_sub_flow_id, cost_attribution_sign, 
      voucher_grant_time, voucher_source, bank_name, 
      bank_account_no, is_ding, original_voucher_id, 
      parent_voucher_id,redcoupon_grant_record_id)
    values (#{id,jdbcType=VARCHAR}, #{voucherId,jdbcType=VARCHAR}, #{voucherName,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, #{balance,jdbcType=DECIMAL}, 
      #{type,jdbcType=INTEGER}, #{marks,jdbcType=VARCHAR}, #{businessType,jdbcType=SMALLINT}, 
      #{businessSubType,jdbcType=SMALLINT}, #{accountType,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{orderSnapshot,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{bizNo,jdbcType=VARCHAR}, 
      #{refundOrderId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, 
      #{employeeName,jdbcType=VARCHAR}, #{employeePhone,jdbcType=VARCHAR}, #{employeeDepartment,jdbcType=VARCHAR}, 
      #{employeeDepartmentId,jdbcType=VARCHAR}, #{employeeDepartmentFull,jdbcType=VARCHAR}, 
      #{writeInvoiceType,jdbcType=INTEGER}, #{writeInvoiceStatus,jdbcType=INTEGER}, #{billStatus,jdbcType=INTEGER}, 
      #{deductionAccountType,jdbcType=INTEGER}, #{accountModel,jdbcType=INTEGER}, #{accountSubId,jdbcType=VARCHAR}, 
      #{accountSubFlowId,jdbcType=VARCHAR}, #{costAttributionSign,jdbcType=INTEGER}, 
      #{voucherGrantTime,jdbcType=TIMESTAMP}, #{voucherSource,jdbcType=INTEGER}, #{bankName,jdbcType=VARCHAR}, 
      #{bankAccountNo,jdbcType=VARCHAR}, #{isDing,jdbcType=TINYINT}, #{originalVoucherId,jdbcType=VARCHAR}, 
      #{parentVoucherId,jdbcType=VARCHAR}, #{redcouponGrantRecordId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_operation_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="voucherName != null">
        voucher_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="balance != null">
        balance,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="marks != null">
        marks,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="businessSubType != null">
        business_sub_type,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="orderSnapshot != null">
        order_snapshot,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="refundOrderId != null">
        refund_order_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="employeePhone != null">
        employee_phone,
      </if>
      <if test="employeeDepartment != null">
        employee_department,
      </if>
      <if test="employeeDepartmentId != null">
        employee_department_id,
      </if>
      <if test="employeeDepartmentFull != null">
        employee_department_full,
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type,
      </if>
      <if test="writeInvoiceStatus != null">
        write_invoice_status,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type,
      </if>
      <if test="accountModel != null">
        account_model,
      </if>
      <if test="accountSubId != null">
        account_sub_id,
      </if>
      <if test="accountSubFlowId != null">
        account_sub_flow_id,
      </if>
      <if test="costAttributionSign != null">
        cost_attribution_sign,
      </if>
      <if test="voucherGrantTime != null">
        voucher_grant_time,
      </if>
      <if test="voucherSource != null">
        voucher_source,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="originalVoucherId != null">
        original_voucher_id,
      </if>
      <if test="parentVoucherId != null">
        parent_voucher_id,
      </if>
      <if test="redcouponGrantRecordId != null">
        redcoupon_grant_record_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        #{voucherId,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="marks != null">
        #{marks,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=SMALLINT},
      </if>
      <if test="businessSubType != null">
        #{businessSubType,jdbcType=SMALLINT},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSnapshot != null">
        #{orderSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderId != null">
        #{refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartment != null">
        #{employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartmentId != null">
        #{employeeDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartmentFull != null">
        #{employeeDepartmentFull,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="writeInvoiceStatus != null">
        #{writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="deductionAccountType != null">
        #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="accountSubFlowId != null">
        #{accountSubFlowId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionSign != null">
        #{costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="voucherGrantTime != null">
        #{voucherGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherSource != null">
        #{voucherSource,jdbcType=INTEGER},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
      <if test="originalVoucherId != null">
        #{originalVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="parentVoucherId != null">
        #{parentVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="redcouponGrantRecordId != null">
        #{redcouponGrantRecordId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlowExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from vouchers_operation_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_operation_flow
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherId != null">
        voucher_id = #{record.voucherId,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherName != null">
        voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.balance != null">
        balance = #{record.balance,jdbcType=DECIMAL},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.marks != null">
        marks = #{record.marks,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=SMALLINT},
      </if>
      <if test="record.businessSubType != null">
        business_sub_type = #{record.businessSubType,jdbcType=SMALLINT},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderSnapshot != null">
        order_snapshot = #{record.orderSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.refundOrderId != null">
        refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeName != null">
        employee_name = #{record.employeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeePhone != null">
        employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeDepartment != null">
        employee_department = #{record.employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeDepartmentId != null">
        employee_department_id = #{record.employeeDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeDepartmentFull != null">
        employee_department_full = #{record.employeeDepartmentFull,jdbcType=VARCHAR},
      </if>
      <if test="record.writeInvoiceType != null">
        write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.writeInvoiceStatus != null">
        write_invoice_status = #{record.writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="record.billStatus != null">
        bill_status = #{record.billStatus,jdbcType=INTEGER},
      </if>
      <if test="record.deductionAccountType != null">
        deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.accountModel != null">
        account_model = #{record.accountModel,jdbcType=INTEGER},
      </if>
      <if test="record.accountSubId != null">
        account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountSubFlowId != null">
        account_sub_flow_id = #{record.accountSubFlowId,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionSign != null">
        cost_attribution_sign = #{record.costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="record.voucherGrantTime != null">
        voucher_grant_time = #{record.voucherGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherSource != null">
        voucher_source = #{record.voucherSource,jdbcType=INTEGER},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
      <if test="record.originalVoucherId != null">
        original_voucher_id = #{record.originalVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="record.parentVoucherId != null">
        parent_voucher_id = #{record.parentVoucherId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_operation_flow
    set id = #{record.id,jdbcType=VARCHAR},
      voucher_id = #{record.voucherId,jdbcType=VARCHAR},
      voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DECIMAL},
      balance = #{record.balance,jdbcType=DECIMAL},
      type = #{record.type,jdbcType=INTEGER},
      marks = #{record.marks,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=SMALLINT},
      business_sub_type = #{record.businessSubType,jdbcType=SMALLINT},
      account_type = #{record.accountType,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      order_snapshot = #{record.orderSnapshot,jdbcType=VARCHAR},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      employee_name = #{record.employeeName,jdbcType=VARCHAR},
      employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      employee_department = #{record.employeeDepartment,jdbcType=VARCHAR},
      employee_department_id = #{record.employeeDepartmentId,jdbcType=VARCHAR},
      employee_department_full = #{record.employeeDepartmentFull,jdbcType=VARCHAR},
      write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      write_invoice_status = #{record.writeInvoiceStatus,jdbcType=INTEGER},
      bill_status = #{record.billStatus,jdbcType=INTEGER},
      deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      account_model = #{record.accountModel,jdbcType=INTEGER},
      account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      account_sub_flow_id = #{record.accountSubFlowId,jdbcType=VARCHAR},
      cost_attribution_sign = #{record.costAttributionSign,jdbcType=INTEGER},
      voucher_grant_time = #{record.voucherGrantTime,jdbcType=TIMESTAMP},
      voucher_source = #{record.voucherSource,jdbcType=INTEGER},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      is_ding = #{record.isDing,jdbcType=TINYINT},
      original_voucher_id = #{record.originalVoucherId,jdbcType=VARCHAR},
      parent_voucher_id = #{record.parentVoucherId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_operation_flow
    <set>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        voucher_name = #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="marks != null">
        marks = #{marks,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=SMALLINT},
      </if>
      <if test="businessSubType != null">
        business_sub_type = #{businessSubType,jdbcType=SMALLINT},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSnapshot != null">
        order_snapshot = #{orderSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderId != null">
        refund_order_id = #{refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        employee_phone = #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartment != null">
        employee_department = #{employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartmentId != null">
        employee_department_id = #{employeeDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartmentFull != null">
        employee_department_full = #{employeeDepartmentFull,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="writeInvoiceStatus != null">
        write_invoice_status = #{writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        account_model = #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="accountSubFlowId != null">
        account_sub_flow_id = #{accountSubFlowId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionSign != null">
        cost_attribution_sign = #{costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="voucherGrantTime != null">
        voucher_grant_time = #{voucherGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherSource != null">
        voucher_source = #{voucherSource,jdbcType=INTEGER},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
      <if test="originalVoucherId != null">
        original_voucher_id = #{originalVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="parentVoucherId != null">
        parent_voucher_id = #{parentVoucherId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_operation_flow
    set voucher_id = #{voucherId,jdbcType=VARCHAR},
      voucher_name = #{voucherName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      balance = #{balance,jdbcType=DECIMAL},
      type = #{type,jdbcType=INTEGER},
      marks = #{marks,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=SMALLINT},
      business_sub_type = #{businessSubType,jdbcType=SMALLINT},
      account_type = #{accountType,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      order_snapshot = #{orderSnapshot,jdbcType=VARCHAR},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      refund_order_id = #{refundOrderId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      employee_phone = #{employeePhone,jdbcType=VARCHAR},
      employee_department = #{employeeDepartment,jdbcType=VARCHAR},
      employee_department_id = #{employeeDepartmentId,jdbcType=VARCHAR},
      employee_department_full = #{employeeDepartmentFull,jdbcType=VARCHAR},
      write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      write_invoice_status = #{writeInvoiceStatus,jdbcType=INTEGER},
      bill_status = #{billStatus,jdbcType=INTEGER},
      deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      account_model = #{accountModel,jdbcType=INTEGER},
      account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      account_sub_flow_id = #{accountSubFlowId,jdbcType=VARCHAR},
      cost_attribution_sign = #{costAttributionSign,jdbcType=INTEGER},
      voucher_grant_time = #{voucherGrantTime,jdbcType=TIMESTAMP},
      voucher_source = #{voucherSource,jdbcType=INTEGER},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      is_ding = #{isDing,jdbcType=TINYINT},
      original_voucher_id = #{originalVoucherId,jdbcType=VARCHAR},
      parent_voucher_id = #{parentVoucherId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="queryIdsByDate" resultType="java.lang.String">
    select id from vouchers_operation_flow
    where create_time > #{startDate} and create_time &lt;= #{endDate}
  </select>

  <update id="updateTimeById" parameterType="java.lang.String">
    update vouchers_operation_flow set update_time = create_time where id=#{id}
  </update>
</mapper>