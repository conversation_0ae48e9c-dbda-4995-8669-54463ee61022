<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskImportDetailsMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetails">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vouchers_task_import_id" jdbcType="VARCHAR" property="vouchersTaskImportId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_status" jdbcType="INTEGER" property="employeeStatus" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
    <result column="employee_department" jdbcType="VARCHAR" property="employeeDepartment" />
    <result column="voucher_templet_id" jdbcType="BIGINT" property="voucherTempletId" />
    <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
    <result column="voucher_denomination" jdbcType="DECIMAL" property="voucherDenomination" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="row_number" jdbcType="INTEGER" property="rowNumber" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="error_details" jdbcType="VARCHAR" property="errorDetails" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, vouchers_task_import_id, employee_id, employee_status, employee_name, employee_phone,
    employee_department, voucher_templet_id, voucher_name, voucher_denomination, create_time,
    update_time, row_number, status, error_details, is_ding
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetailsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_task_import_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from vouchers_task_import_details
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_task_import_details
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetailsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_task_import_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetails" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_task_import_details (vouchers_task_import_id, employee_id,
      employee_status, employee_name, employee_phone,
      employee_department, voucher_templet_id, voucher_name,
      voucher_denomination, create_time, update_time,
      row_number, status, error_details,
      is_ding)
    values (#{vouchersTaskImportId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR},
      #{employeeStatus,jdbcType=INTEGER}, #{employeeName,jdbcType=VARCHAR}, #{employeePhone,jdbcType=VARCHAR},
      #{employeeDepartment,jdbcType=VARCHAR}, #{voucherTempletId,jdbcType=BIGINT}, #{voucherName,jdbcType=VARCHAR},
      #{voucherDenomination,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{rowNumber,jdbcType=INTEGER}, #{status,jdbcType=SMALLINT}, #{errorDetails,jdbcType=VARCHAR},
      #{isDing,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetails" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_task_import_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vouchersTaskImportId != null">
        vouchers_task_import_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="employeeStatus != null">
        employee_status,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="employeePhone != null">
        employee_phone,
      </if>
      <if test="employeeDepartment != null">
        employee_department,
      </if>
      <if test="voucherTempletId != null">
        voucher_templet_id,
      </if>
      <if test="voucherName != null">
        voucher_name,
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="rowNumber != null">
        row_number,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="errorDetails != null">
        error_details,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vouchersTaskImportId != null">
        #{vouchersTaskImportId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeStatus != null">
        #{employeeStatus,jdbcType=INTEGER},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartment != null">
        #{employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="voucherTempletId != null">
        #{voucherTempletId,jdbcType=BIGINT},
      </if>
      <if test="voucherName != null">
        #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rowNumber != null">
        #{rowNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="errorDetails != null">
        #{errorDetails,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetailsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from vouchers_task_import_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_import_details
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vouchersTaskImportId != null">
        vouchers_task_import_id = #{record.vouchersTaskImportId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeStatus != null">
        employee_status = #{record.employeeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.employeeName != null">
        employee_name = #{record.employeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeePhone != null">
        employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeDepartment != null">
        employee_department = #{record.employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherTempletId != null">
        voucher_templet_id = #{record.voucherTempletId,jdbcType=BIGINT},
      </if>
      <if test="record.voucherName != null">
        voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherDenomination != null">
        voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rowNumber != null">
        row_number = #{record.rowNumber,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.errorDetails != null">
        error_details = #{record.errorDetails,jdbcType=VARCHAR},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_import_details
    set id = #{record.id,jdbcType=BIGINT},
      vouchers_task_import_id = #{record.vouchersTaskImportId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      employee_status = #{record.employeeStatus,jdbcType=INTEGER},
      employee_name = #{record.employeeName,jdbcType=VARCHAR},
      employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      employee_department = #{record.employeeDepartment,jdbcType=VARCHAR},
      voucher_templet_id = #{record.voucherTempletId,jdbcType=BIGINT},
      voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      row_number = #{record.rowNumber,jdbcType=INTEGER},
      status = #{record.status,jdbcType=SMALLINT},
      error_details = #{record.errorDetails,jdbcType=VARCHAR},
      is_ding = #{record.isDing,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetails">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_import_details
    <set>
      <if test="vouchersTaskImportId != null">
        vouchers_task_import_id = #{vouchersTaskImportId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeStatus != null">
        employee_status = #{employeeStatus,jdbcType=INTEGER},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        employee_phone = #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartment != null">
        employee_department = #{employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="voucherTempletId != null">
        voucher_templet_id = #{voucherTempletId,jdbcType=BIGINT},
      </if>
      <if test="voucherName != null">
        voucher_name = #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rowNumber != null">
        row_number = #{rowNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="errorDetails != null">
        error_details = #{errorDetails,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetails">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_import_details
    set vouchers_task_import_id = #{vouchersTaskImportId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      employee_status = #{employeeStatus,jdbcType=INTEGER},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      employee_phone = #{employeePhone,jdbcType=VARCHAR},
      employee_department = #{employeeDepartment,jdbcType=VARCHAR},
      voucher_templet_id = #{voucherTempletId,jdbcType=BIGINT},
      voucher_name = #{voucherName,jdbcType=VARCHAR},
      voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      row_number = #{rowNumber,jdbcType=INTEGER},
      status = #{status,jdbcType=SMALLINT},
      error_details = #{errorDetails,jdbcType=VARCHAR},
      is_ding = #{isDing,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
