<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskImportDetailsExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportDetails">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="vouchers_task_import_id" jdbcType="VARCHAR" property="vouchersTaskImportId" />
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
        <result column="employee_status" jdbcType="INTEGER" property="employeeStatus" />
        <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
        <result column="employee_department" jdbcType="VARCHAR" property="employeeDepartment" />
        <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
        <result column="voucher_templet_id" jdbcType="BIGINT" property="voucherTempletId" />
        <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
        <result column="voucher_denomination" jdbcType="DECIMAL" property="voucherDenomination" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="row_number" jdbcType="INTEGER" property="rowNumber" />
        <result column="status" jdbcType="SMALLINT" property="status" />
        <result column="error_details" jdbcType="VARCHAR" property="errorDetails" />
    </resultMap>
    <insert id="insertList" parameterType="java.util.List">
        insert into vouchers_task_import_details (vouchers_task_import_id, employee_id, employee_status,
        employee_name, employee_department, employee_phone,
        voucher_templet_id, voucher_name, voucher_denomination,
        create_time, update_time, row_number,
        status, error_details)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            (#{record.vouchersTaskImportId,jdbcType=VARCHAR}, #{record.employeeId,jdbcType=VARCHAR}, #{record.employeeStatus,jdbcType=INTEGER},
            #{record.employeeName,jdbcType=VARCHAR}, #{record.employeeDepartment,jdbcType=VARCHAR}, #{record.employeePhone,jdbcType=VARCHAR},
            #{record.voucherTempletId,jdbcType=BIGINT}, #{record.voucherName,jdbcType=VARCHAR}, #{record.voucherDenomination,jdbcType=DECIMAL},
            #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateTime,jdbcType=TIMESTAMP}, #{record.rowNumber,jdbcType=INTEGER},
            #{record.status,jdbcType=SMALLINT}, #{record.errorDetails,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="countTotalAmount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select sum(voucher_denomination) from vouchers_task_import_details
        where vouchers_task_import_id =#{vouchersTaskImportId}
        <if test="voucherTaskStatus != null and voucherTaskStatus != -1">
            and status = #{voucherTaskStatus}
        </if>
    </select>

    <!--<select id="countTotalOperationAmount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select sum(operation_amount) from vouchers_task_details
        where vouchers_task_id =#{voucherTaskId}
        <if test="voucherTaskStatus != null and voucherTaskStatus != -1">
            and status = #{voucherTaskStatus}
        </if>
    </select>

    <select id="selectVoucherIdsByVoucherTaskId" parameterType="java.util.Map" resultType="java.lang.String">
        select voucher_id from vouchers_task_details
        where vouchers_task_id =#{voucherTaskId}
        <if test="offset != null and limit != null">
            limit ${offset}, ${limit}
        </if>
    </select>-->
</mapper>
