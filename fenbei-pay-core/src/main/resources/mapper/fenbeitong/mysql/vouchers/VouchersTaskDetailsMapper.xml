<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskDetailsMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetails">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vouchers_task_details_id" jdbcType="VARCHAR" property="vouchersTaskDetailsId" />
    <result column="vouchers_task_id" jdbcType="VARCHAR" property="vouchersTaskId" />
    <result column="vouchers_task_type" jdbcType="SMALLINT" property="vouchersTaskType" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="operation_amount" jdbcType="DECIMAL" property="operationAmount" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="voucher_id" jdbcType="VARCHAR" property="voucherId" />
    <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
    <result column="voucher_denomination" jdbcType="DECIMAL" property="voucherDenomination" />
    <result column="voucher_effective_time" jdbcType="TIMESTAMP" property="voucherEffectiveTime" />
    <result column="voucher_expiry_time" jdbcType="TIMESTAMP" property="voucherExpiryTime" />
    <result column="voucher_expiry_notice" jdbcType="INTEGER" property="voucherExpiryNotice" />
    <result column="voucher_expiry_notice_time" jdbcType="TIMESTAMP" property="voucherExpiryNoticeTime" />
    <result column="voucher_type_list" jdbcType="VARCHAR" property="voucherTypeList" />
    <result column="voucher_source" jdbcType="SMALLINT" property="voucherSource" />
    <result column="voucher_recovery_type" jdbcType="SMALLINT" property="voucherRecoveryType" />
    <result column="voucher_desc" jdbcType="VARCHAR" property="voucherDesc" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
    <result column="employee_department" jdbcType="VARCHAR" property="employeeDepartment" />
    <result column="employee_department_id" jdbcType="VARCHAR" property="employeeDepartmentId" />
    <result column="employee_department_full" jdbcType="VARCHAR" property="employeeDepartmentFull" />
    <result column="write_invoice_type" jdbcType="INTEGER" property="writeInvoiceType" />
    <result column="write_invoice_status" jdbcType="INTEGER" property="writeInvoiceStatus" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="can_transfer" jdbcType="INTEGER" property="canTransfer" />
    <result column="mongo_id" jdbcType="VARCHAR" property="mongoId" />
    <result column="voucher_effective_days" jdbcType="SMALLINT" property="voucherEffectiveDays" />
    <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
    <result column="voucher_model" jdbcType="INTEGER" property="voucherModel" />
    <result column="cost_attribution_sign" jdbcType="INTEGER" property="costAttributionSign" />
    <result column="cost_info" jdbcType="VARCHAR" property="costInfo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
    <result column="original_voucher_id" jdbcType="VARCHAR" property="originalVoucherId" />
    <result column="parent_voucher_id" jdbcType="VARCHAR" property="parentVoucherId" />
    <result column="popup" jdbcType="INTEGER" property="popup" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, vouchers_task_details_id, vouchers_task_id, vouchers_task_type, status, create_time,
    update_time, end_time, operation_amount, operation_user_id, biz_no, voucher_id, voucher_name,
    voucher_denomination, voucher_effective_time, voucher_expiry_time, voucher_expiry_notice, voucher_expiry_notice_time, voucher_type_list,
    voucher_source, voucher_recovery_type, voucher_desc, company_id, company_name, employee_id,
    employee_name, employee_phone, employee_department, employee_department_id, employee_department_full,
    write_invoice_type, write_invoice_status, bill_status, can_transfer, mongo_id, voucher_effective_days,
    deduction_account_type, account_model, account_sub_id, voucher_model, cost_attribution_sign, cost_info,
    bank_name, bank_account_no, is_ding, original_voucher_id, parent_voucher_id,popup
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetailsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_task_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from vouchers_task_details
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_task_details
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetailsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_task_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetails" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_task_details (vouchers_task_details_id, vouchers_task_id,
      vouchers_task_type, status, create_time,
      update_time, end_time, operation_amount,
      operation_user_id, biz_no, voucher_id,
      voucher_name, voucher_denomination, voucher_effective_time,
      voucher_expiry_time, voucher_expiry_notice, voucher_expiry_notice_time, voucher_type_list, voucher_source,
      voucher_recovery_type, voucher_desc, company_id,
      company_name, employee_id, employee_name,
      employee_phone, employee_department, employee_department_id,
      employee_department_full, write_invoice_type,
      write_invoice_status, bill_status, can_transfer,
      mongo_id, voucher_effective_days, deduction_account_type,
      account_model, account_sub_id, voucher_model,
      cost_attribution_sign, bank_name, bank_account_no,
      is_ding, original_voucher_id, parent_voucher_id
      )
    values (#{vouchersTaskDetailsId,jdbcType=VARCHAR}, #{vouchersTaskId,jdbcType=VARCHAR},
      #{vouchersTaskType,jdbcType=SMALLINT}, #{status,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{operationAmount,jdbcType=DECIMAL},
      #{operationUserId,jdbcType=VARCHAR}, #{bizNo,jdbcType=VARCHAR}, #{voucherId,jdbcType=VARCHAR},
      #{voucherName,jdbcType=VARCHAR}, #{voucherDenomination,jdbcType=DECIMAL}, #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      #{voucherExpiryTime,jdbcType=TIMESTAMP}, #{voucherExpiryNotice,jdbcType=INTEGER}, #{voucherExpiryNoticeTime,jdbcType=TIMESTAMP}, #{voucherTypeList,jdbcType=VARCHAR}, #{voucherSource,jdbcType=SMALLINT},
      #{voucherRecoveryType,jdbcType=SMALLINT}, #{voucherDesc,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR},
      #{companyName,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, #{employeeName,jdbcType=VARCHAR},
      #{employeePhone,jdbcType=VARCHAR}, #{employeeDepartment,jdbcType=VARCHAR}, #{employeeDepartmentId,jdbcType=VARCHAR},
      #{employeeDepartmentFull,jdbcType=VARCHAR}, #{writeInvoiceType,jdbcType=INTEGER},
      #{writeInvoiceStatus,jdbcType=INTEGER}, #{billStatus,jdbcType=INTEGER}, #{canTransfer,jdbcType=INTEGER},
      #{mongoId,jdbcType=VARCHAR}, #{voucherEffectiveDays,jdbcType=SMALLINT}, #{deductionAccountType,jdbcType=INTEGER},
      #{accountModel,jdbcType=INTEGER}, #{accountSubId,jdbcType=VARCHAR}, #{voucherModel,jdbcType=INTEGER},
      #{costAttributionSign,jdbcType=INTEGER}, #{bankName,jdbcType=VARCHAR}, #{bankAccountNo,jdbcType=VARCHAR},
      #{isDing,jdbcType=TINYINT}, #{originalVoucherId,jdbcType=VARCHAR}, #{parentVoucherId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetails" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_task_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vouchersTaskDetailsId != null">
        vouchers_task_details_id,
      </if>
      <if test="vouchersTaskId != null">
        vouchers_task_id,
      </if>
      <if test="vouchersTaskType != null">
        vouchers_task_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="operationAmount != null">
        operation_amount,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="voucherName != null">
        voucher_name,
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination,
      </if>
      <if test="voucherEffectiveTime != null">
        voucher_effective_time,
      </if>
      <if test="voucherExpiryTime != null">
        voucher_expiry_time,
      </if>
      <if test="voucherExpiryNotice != null">
        voucher_expiry_notice,
      </if>
      <if test="voucherExpiryNoticeTime != null">
        voucher_expiry_notice_time,
      </if>
      <if test="voucherTypeList != null">
        voucher_type_list,
      </if>
      <if test="voucherSource != null">
        voucher_source,
      </if>
      <if test="voucherRecoveryType != null">
        voucher_recovery_type,
      </if>
      <if test="voucherDesc != null">
        voucher_desc,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="employeePhone != null">
        employee_phone,
      </if>
      <if test="employeeDepartment != null">
        employee_department,
      </if>
      <if test="employeeDepartmentId != null">
        employee_department_id,
      </if>
      <if test="employeeDepartmentFull != null">
        employee_department_full,
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type,
      </if>
      <if test="writeInvoiceStatus != null">
        write_invoice_status,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="canTransfer != null">
        can_transfer,
      </if>
      <if test="mongoId != null">
        mongo_id,
      </if>
      <if test="voucherEffectiveDays != null">
        voucher_effective_days,
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type,
      </if>
      <if test="accountModel != null">
        account_model,
      </if>
      <if test="accountSubId != null">
        account_sub_id,
      </if>
      <if test="voucherModel != null">
        voucher_model,
      </if>
      <if test="costAttributionSign != null">
        cost_attribution_sign,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="originalVoucherId != null">
        original_voucher_id,
      </if>
      <if test="parentVoucherId != null">
        parent_voucher_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vouchersTaskDetailsId != null">
        #{vouchersTaskDetailsId,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskId != null">
        #{vouchersTaskId,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskType != null">
        #{vouchersTaskType,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationAmount != null">
        #{operationAmount,jdbcType=DECIMAL},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        #{voucherId,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="voucherEffectiveTime != null">
        #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryTime != null">
        #{voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryNotice != null">
        #{voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="voucherExpiryNoticeTime != null">
        #{voucherExpiryNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherTypeList != null">
        #{voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="voucherSource != null">
        #{voucherSource,jdbcType=SMALLINT},
      </if>
      <if test="voucherRecoveryType != null">
        #{voucherRecoveryType,jdbcType=SMALLINT},
      </if>
      <if test="voucherDesc != null">
        #{voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartment != null">
        #{employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartmentId != null">
        #{employeeDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartmentFull != null">
        #{employeeDepartmentFull,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="writeInvoiceStatus != null">
        #{writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="canTransfer != null">
        #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="mongoId != null">
        #{mongoId,jdbcType=VARCHAR},
      </if>
      <if test="voucherEffectiveDays != null">
        #{voucherEffectiveDays,jdbcType=SMALLINT},
      </if>
      <if test="deductionAccountType != null">
        #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="voucherModel != null">
        #{voucherModel,jdbcType=INTEGER},
      </if>
      <if test="costAttributionSign != null">
        #{costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
      <if test="originalVoucherId != null">
        #{originalVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="parentVoucherId != null">
        #{parentVoucherId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetailsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from vouchers_task_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_details
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vouchersTaskDetailsId != null">
        vouchers_task_details_id = #{record.vouchersTaskDetailsId,jdbcType=VARCHAR},
      </if>
      <if test="record.vouchersTaskId != null">
        vouchers_task_id = #{record.vouchersTaskId,jdbcType=VARCHAR},
      </if>
      <if test="record.vouchersTaskType != null">
        vouchers_task_type = #{record.vouchersTaskType,jdbcType=SMALLINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationAmount != null">
        operation_amount = #{record.operationAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.operationUserId != null">
        operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherId != null">
        voucher_id = #{record.voucherId,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherName != null">
        voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherDenomination != null">
        voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherEffectiveTime != null">
        voucher_effective_time = #{record.voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherExpiryTime != null">
        voucher_expiry_time = #{record.voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherExpiryNotice != null">
        voucher_expiry_notice = #{record.voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="record.voucherExpiryNoticeTime != null">
        voucher_expiry_notice_time = #{record.voucherExpiryNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherTypeList != null">
        voucher_type_list = #{record.voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherSource != null">
        voucher_source = #{record.voucherSource,jdbcType=SMALLINT},
      </if>
      <if test="record.voucherRecoveryType != null">
        voucher_recovery_type = #{record.voucherRecoveryType,jdbcType=SMALLINT},
      </if>
      <if test="record.voucherDesc != null">
        voucher_desc = #{record.voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeName != null">
        employee_name = #{record.employeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeePhone != null">
        employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeDepartment != null">
        employee_department = #{record.employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeDepartmentId != null">
        employee_department_id = #{record.employeeDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeDepartmentFull != null">
        employee_department_full = #{record.employeeDepartmentFull,jdbcType=VARCHAR},
      </if>
      <if test="record.writeInvoiceType != null">
        write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.writeInvoiceStatus != null">
        write_invoice_status = #{record.writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="record.billStatus != null">
        bill_status = #{record.billStatus,jdbcType=INTEGER},
      </if>
      <if test="record.canTransfer != null">
        can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      </if>
      <if test="record.mongoId != null">
        mongo_id = #{record.mongoId,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherEffectiveDays != null">
        voucher_effective_days = #{record.voucherEffectiveDays,jdbcType=SMALLINT},
      </if>
      <if test="record.deductionAccountType != null">
        deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.accountModel != null">
        account_model = #{record.accountModel,jdbcType=INTEGER},
      </if>
      <if test="record.accountSubId != null">
        account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherModel != null">
        voucher_model = #{record.voucherModel,jdbcType=INTEGER},
      </if>
      <if test="record.costAttributionSign != null">
        cost_attribution_sign = #{record.costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
      <if test="record.originalVoucherId != null">
        original_voucher_id = #{record.originalVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="record.parentVoucherId != null">
        parent_voucher_id = #{record.parentVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="record.popup != null">
        popup = #{record.popup,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_details
    set id = #{record.id,jdbcType=BIGINT},
      vouchers_task_details_id = #{record.vouchersTaskDetailsId,jdbcType=VARCHAR},
      vouchers_task_id = #{record.vouchersTaskId,jdbcType=VARCHAR},
      vouchers_task_type = #{record.vouchersTaskType,jdbcType=SMALLINT},
      status = #{record.status,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      operation_amount = #{record.operationAmount,jdbcType=DECIMAL},
      operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      voucher_id = #{record.voucherId,jdbcType=VARCHAR},
      voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      voucher_effective_time = #{record.voucherEffectiveTime,jdbcType=TIMESTAMP},
      voucher_expiry_time = #{record.voucherExpiryTime,jdbcType=TIMESTAMP},
      voucher_expiry_notice = #{record.voucherExpiryNotice,jdbcType=INTEGER},
      voucher_expiry_notice_time = #{record.voucherExpiryNoticeTime,jdbcType=TIMESTAMP},
      voucher_type_list = #{record.voucherTypeList,jdbcType=VARCHAR},
      voucher_source = #{record.voucherSource,jdbcType=SMALLINT},
      voucher_recovery_type = #{record.voucherRecoveryType,jdbcType=SMALLINT},
      voucher_desc = #{record.voucherDesc,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      employee_name = #{record.employeeName,jdbcType=VARCHAR},
      employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      employee_department = #{record.employeeDepartment,jdbcType=VARCHAR},
      employee_department_id = #{record.employeeDepartmentId,jdbcType=VARCHAR},
      employee_department_full = #{record.employeeDepartmentFull,jdbcType=VARCHAR},
      write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      write_invoice_status = #{record.writeInvoiceStatus,jdbcType=INTEGER},
      bill_status = #{record.billStatus,jdbcType=INTEGER},
      can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      mongo_id = #{record.mongoId,jdbcType=VARCHAR},
      voucher_effective_days = #{record.voucherEffectiveDays,jdbcType=SMALLINT},
      deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      account_model = #{record.accountModel,jdbcType=INTEGER},
      account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      voucher_model = #{record.voucherModel,jdbcType=INTEGER},
      cost_attribution_sign = #{record.costAttributionSign,jdbcType=INTEGER},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      is_ding = #{record.isDing,jdbcType=TINYINT},
      original_voucher_id = #{record.originalVoucherId,jdbcType=VARCHAR},
      parent_voucher_id = #{record.parentVoucherId,jdbcType=VARCHAR},
      popup = #{record.popup,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetails">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_details
    <set>
      <if test="vouchersTaskDetailsId != null">
        vouchers_task_details_id = #{vouchersTaskDetailsId,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskId != null">
        vouchers_task_id = #{vouchersTaskId,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskType != null">
        vouchers_task_type = #{vouchersTaskType,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationAmount != null">
        operation_amount = #{operationAmount,jdbcType=DECIMAL},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        voucher_name = #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="voucherEffectiveTime != null">
        voucher_effective_time = #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryTime != null">
        voucher_expiry_time = #{voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryNotice != null">
        voucher_expiry_notice = #{voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="voucherExpiryNoticeTime != null">
        voucher_expiry_notice_time = #{voucherExpiryNoticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherTypeList != null">
        voucher_type_list = #{voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="voucherSource != null">
        voucher_source = #{voucherSource,jdbcType=SMALLINT},
      </if>
      <if test="voucherRecoveryType != null">
        voucher_recovery_type = #{voucherRecoveryType,jdbcType=SMALLINT},
      </if>
      <if test="voucherDesc != null">
        voucher_desc = #{voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        employee_phone = #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartment != null">
        employee_department = #{employeeDepartment,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartmentId != null">
        employee_department_id = #{employeeDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="employeeDepartmentFull != null">
        employee_department_full = #{employeeDepartmentFull,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="writeInvoiceStatus != null">
        write_invoice_status = #{writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="canTransfer != null">
        can_transfer = #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="mongoId != null">
        mongo_id = #{mongoId,jdbcType=VARCHAR},
      </if>
      <if test="voucherEffectiveDays != null">
        voucher_effective_days = #{voucherEffectiveDays,jdbcType=SMALLINT},
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        account_model = #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="voucherModel != null">
        voucher_model = #{voucherModel,jdbcType=INTEGER},
      </if>
      <if test="costAttributionSign != null">
        cost_attribution_sign = #{costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
      <if test="originalVoucherId != null">
        original_voucher_id = #{originalVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="parentVoucherId != null">
        parent_voucher_id = #{parentVoucherId,jdbcType=VARCHAR},
      </if>
      <if test="popup != null">
        popup = #{popup,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetails">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_details
    set vouchers_task_details_id = #{vouchersTaskDetailsId,jdbcType=VARCHAR},
      vouchers_task_id = #{vouchersTaskId,jdbcType=VARCHAR},
      vouchers_task_type = #{vouchersTaskType,jdbcType=SMALLINT},
      status = #{status,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      operation_amount = #{operationAmount,jdbcType=DECIMAL},
      operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      voucher_id = #{voucherId,jdbcType=VARCHAR},
      voucher_name = #{voucherName,jdbcType=VARCHAR},
      voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      voucher_effective_time = #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      voucher_expiry_time = #{voucherExpiryTime,jdbcType=TIMESTAMP},
      voucher_expiry_notice = #{voucherExpiryNotice,jdbcType=INTEGER},
      voucher_expiry_notice_time = #{voucherExpiryNoticeTime,jdbcType=TIMESTAMP},
      voucher_type_list = #{voucherTypeList,jdbcType=VARCHAR},
      voucher_source = #{voucherSource,jdbcType=SMALLINT},
      voucher_recovery_type = #{voucherRecoveryType,jdbcType=SMALLINT},
      voucher_desc = #{voucherDesc,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      employee_phone = #{employeePhone,jdbcType=VARCHAR},
      employee_department = #{employeeDepartment,jdbcType=VARCHAR},
      employee_department_id = #{employeeDepartmentId,jdbcType=VARCHAR},
      employee_department_full = #{employeeDepartmentFull,jdbcType=VARCHAR},
      write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      write_invoice_status = #{writeInvoiceStatus,jdbcType=INTEGER},
      bill_status = #{billStatus,jdbcType=INTEGER},
      can_transfer = #{canTransfer,jdbcType=INTEGER},
      mongo_id = #{mongoId,jdbcType=VARCHAR},
      voucher_effective_days = #{voucherEffectiveDays,jdbcType=SMALLINT},
      deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      account_model = #{accountModel,jdbcType=INTEGER},
      account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      voucher_model = #{voucherModel,jdbcType=INTEGER},
      cost_attribution_sign = #{costAttributionSign,jdbcType=INTEGER},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      is_ding = #{isDing,jdbcType=TINYINT},
      original_voucher_id = #{originalVoucherId,jdbcType=VARCHAR},
      parent_voucher_id = #{parentVoucherId,jdbcType=VARCHAR},
      popup = #{popup,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryPopup" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from vouchers_task_details
    where employee_id = #{employeeId,jdbcType=VARCHAR}
    and popup = 1
    and  #{currentTime} between voucher_effective_time and voucher_expiry_time
    order by create_time desc
  </select>

  <update id="unPopup">
    update vouchers_task_details
    set popup = 0
    where employee_id = #{employeeId,jdbcType=VARCHAR}
    and popup=1
  </update>
</mapper>
