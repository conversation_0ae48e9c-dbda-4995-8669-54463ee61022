<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersCostAttributionExtMapper">
    <resultMap id="BaseResultMap"
               type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttribution">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="voucher_flow_id" jdbcType="VARCHAR" property="voucherFlowId"/>
        <result column="cost_attribution_id" jdbcType="VARCHAR" property="costAttributionId"/>
        <result column="cost_attribution_type" jdbcType="SMALLINT" property="costAttributionType"/>
        <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName"/>
        <result column="cost_attribution_path" jdbcType="VARCHAR" property="costAttributionPath"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <insert id="insertAll" parameterType="java.util.List">
        insert into vouchers_cost_attribution (voucher_flow_id, cost_attribution_id,
        cost_attribution_type, cost_attribution_name,
        cost_attribution_path, create_time)
        values
        <foreach collection="records" item="item" separator=",">
            (#{item.voucherFlowId,jdbcType=VARCHAR}, #{item.costAttributionId,jdbcType=VARCHAR},
            #{item.costAttributionType,jdbcType=SMALLINT}, #{item.costAttributionName,jdbcType=VARCHAR},
            #{item.costAttributionPath,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>