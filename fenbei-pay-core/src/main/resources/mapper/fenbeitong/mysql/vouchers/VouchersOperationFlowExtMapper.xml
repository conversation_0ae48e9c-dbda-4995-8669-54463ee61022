<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersOperationFlowExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="voucher_id" jdbcType="VARCHAR" property="voucherId" />
        <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="amount" jdbcType="DECIMAL" property="amount" />
        <result column="balance" jdbcType="DECIMAL" property="balance" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="marks" jdbcType="VARCHAR" property="marks" />
        <result column="business_type" jdbcType="SMALLINT" property="businessType" />
        <result column="business_sub_type" jdbcType="SMALLINT" property="businessSubType" />
        <result column="account_type" jdbcType="SMALLINT" property="accountType" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="order_snapshot" jdbcType="VARCHAR" property="orderSnapshot" />
        <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
        <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
        <result column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId" />
        <result column="company_id" jdbcType="VARCHAR" property="companyId" />
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
        <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
        <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
        <result column="employee_department" jdbcType="VARCHAR" property="employeeDepartment" />
        <result column="employee_department_id" jdbcType="VARCHAR" property="employeeDepartmentId" />
        <result column="employee_department_full" jdbcType="VARCHAR" property="employeeDepartmentFull" />
        <result column="write_invoice_type" jdbcType="INTEGER" property="writeInvoiceType" />
        <result column="write_invoice_status" jdbcType="INTEGER" property="writeInvoiceStatus" />
        <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
        <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
        <result column="account_model" jdbcType="INTEGER" property="accountModel" />
        <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
        <result column="account_sub_flow_id" jdbcType="VARCHAR" property="accountSubFlowId" />
        <result column="cost_attribution_sign" jdbcType="INTEGER" property="costAttributionSign" />
        <result column="voucher_grant_time" jdbcType="TIMESTAMP" property="voucherGrantTime" />
        <result column="voucher_source" jdbcType="INTEGER" property="voucherSource" />
        <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
        <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
        <result column="is_ding" jdbcType="TINYINT" property="isDing" />
        <result column="original_voucher_id" jdbcType="VARCHAR" property="originalVoucherId" />
        <result column="parent_voucher_id" jdbcType="VARCHAR" property="parentVoucherId" />
    </resultMap>
    <select id="countTotalAmount" parameterType="com.fenbeitong.fenbeipay.core.model.dto.VouchersFlowReqDTO"
            resultType="java.math.BigDecimal">
        select sum(amount) from vouchers_operation_flow
        where company_id = #{vouchersFlowReqDTO.companyId}
        <if test="vouchersFlowReqDTO.type != null">
            and type = #{vouchersFlowReqDTO.type}
        </if>
        <if test="vouchersFlowReqDTO.businessType != null">
            and business_type = #{vouchersFlowReqDTO.businessType}
        </if>
        <if test="vouchersFlowReqDTO.operationUserName != null and vouchersFlowReqDTO.operationUserName != ''">
            and employee_name like concat('%',#{vouchersFlowReqDTO.operationUserName},'%')
        </if>
        <if test="vouchersFlowReqDTO.operationUserPhone != null and vouchersFlowReqDTO.operationUserPhone != ''">
            and employee_phone = #{vouchersFlowReqDTO.operationUserPhone}
        </if>
        <if test="vouchersFlowReqDTO.writeInvoiceType != null">
            and write_invoice_type = #{vouchersFlowReqDTO.writeInvoiceType}
        </if>
        <if test="vouchersFlowReqDTO.writeInvoiceStatus != null">
            and write_invoice_status = #{vouchersFlowReqDTO.writeInvoiceStatus}
        </if>
        <if test=" vouchersFlowReqDTO.startTime != null and vouchersFlowReqDTO.endTime != null">
            and create_time between #{vouchersFlowReqDTO.startTime} and #{vouchersFlowReqDTO.endTime}
        </if>
        <if test="vouchersFlowReqDTO.voucherFlowId != null and vouchersFlowReqDTO.voucherFlowId != ''">
            and id = #{vouchersFlowReqDTO.voucherFlowId}
        </if>
        <if test="vouchersFlowReqDTO.accountSubFlowId != null and vouchersFlowReqDTO.accountSubFlowId != ''">
            and account_sub_flow_id = #{vouchersFlowReqDTO.accountSubFlowId}
        </if>
    </select>
    <insert id="insertAll" parameterType="java.util.List">
        insert into vouchers_operation_flow (id, voucher_id, voucher_name,
        order_no, amount, balance,
        type, marks, business_type,
        business_sub_type, account_type, create_time,
        order_snapshot, fb_order_id, biz_no,
        refund_order_id, company_id, employee_id,
        employee_name, employee_phone, employee_department,
        employee_department_id, employee_department_full,
        write_invoice_type, write_invoice_status, bill_status,
        deduction_account_type, account_model, account_sub_id,
        account_sub_flow_id, cost_attribution_sign,
        voucher_grant_time, voucher_source, bank_name, bank_account_no,
        is_ding, original_voucher_id, parent_voucher_id)
        values
        <foreach collection="records" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.voucherId,jdbcType=VARCHAR}, #{item.voucherName,jdbcType=VARCHAR},
            #{item.orderNo,jdbcType=VARCHAR}, #{item.amount,jdbcType=DECIMAL}, #{item.balance,jdbcType=DECIMAL},
            #{item.type,jdbcType=INTEGER}, #{item.marks,jdbcType=VARCHAR}, #{item.businessType,jdbcType=SMALLINT},
            #{item.businessSubType,jdbcType=SMALLINT}, #{item.accountType,jdbcType=SMALLINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.orderSnapshot,jdbcType=VARCHAR}, #{item.fbOrderId,jdbcType=VARCHAR}, #{item.bizNo,jdbcType=VARCHAR},
            #{item.refundOrderId,jdbcType=VARCHAR}, #{item.companyId,jdbcType=VARCHAR}, #{item.employeeId,jdbcType=VARCHAR},
            #{item.employeeName,jdbcType=VARCHAR}, #{item.employeePhone,jdbcType=VARCHAR}, #{item.employeeDepartment,jdbcType=VARCHAR},
            #{item.employeeDepartmentId,jdbcType=VARCHAR}, #{item.employeeDepartmentFull,jdbcType=VARCHAR},
            #{item.writeInvoiceType,jdbcType=INTEGER}, #{item.writeInvoiceStatus,jdbcType=INTEGER}, #{item.billStatus,jdbcType=INTEGER},
            #{item.deductionAccountType,jdbcType=INTEGER}, #{item.accountModel,jdbcType=INTEGER}, #{item.accountSubId,jdbcType=VARCHAR},
            #{item.accountSubFlowId,jdbcType=VARCHAR}, #{item.costAttributionSign,jdbcType=INTEGER},
            #{item.voucherGrantTime,jdbcType=TIMESTAMP}, #{item.voucherSource,jdbcType=INTEGER}, #{item.bankName,jdbcType=VARCHAR},
            #{item.bankAccountNo,jdbcType=VARCHAR}, #{item.isDing,jdbcType=TINYINT}, #{item.originalVoucherId,jdbcType=VARCHAR},
            #{item.parentVoucherId,jdbcType=VARCHAR})
        </foreach>
    </insert>


    <select id="selectFlowStatistics"
            parameterType="com.fenbeitong.fenbeipay.core.model.dto.VouchersStatisticsReqDTO"
            resultType="java.util.Map">
        select
        sum(case type when 5 then amount else 0 end) grantPrice,
        sum(case type when 1 then amount else 0 end) - sum(case type when 2 then amount else 0 end) consumptionPrice,
        sum(case type when 2 then amount else 0 end) refundPrice,
        sum(case type when 6 then amount else 0 end) transferOutPrice,
        sum(case type when 7 then amount else 0 end) transferInPrice,
        sum(case when type in (3, 4, 42, 8) then amount else 0 end) recoveryPrice,
        sum(case type when 41 then amount else 0 end) advanceTicketPrice
        from vouchers_operation_flow
        where voucher_source != 7
        and company_id = #{companyId}
        <if test="employeeId != null and employeeId != ''">
            and employee_id = #{employeeId}
        </if>
        <!--<if test="employeeDepartmentId != null and employeeDepartmentId != ''">
            and employee_department_id = #{employeeDepartmentId}
        </if>-->
        <if test="employeeDepartmentName != null and employeeDepartmentName != ''">
            and employee_department_full like CONCAT('%',#{employeeDepartmentName},'%')
        </if>
        <if test="startTime != null and startTime != ''">
            and voucher_grant_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and #{endTime} >= voucher_grant_time
        </if>
    </select>

    <select id="selectFlowStatisticsPage"
            parameterType="com.fenbeitong.fenbeipay.core.model.dto.VouchersStatisticsReqDTO"
            resultType="java.util.Map">
        select
        employee_id as employeeId,
        employee_department_id as employeeDepartmentId,
        employee_department_full as employeeDepartmentFull,
        sum(case type when 5 then amount else 0 end) grantPrice,
        sum(case type when 1 then amount else 0 end) - sum(case type when 2 then amount else 0 end) consumptionPrice,
        sum(case type when 6 then amount else 0 end) transferOutPrice,
        sum(case type when 7 then amount else 0 end) transferInPrice,
        sum(case when type in (3, 4, 42, 8) then amount else 0 end) recoveryPrice,
        sum(case type when 41 then amount else 0 end) advanceTicketPrice
        from vouchers_operation_flow
        where voucher_source != 7
        and company_id = #{companyId}
        <if test="employeeId != null and employeeId != ''">
            and employee_id = #{employeeId}
        </if>
        <!--<if test="employeeDepartmentId != null and employeeDepartmentId != ''">
            and employee_department_id = #{employeeDepartmentId}
        </if>-->
        <if test="employeeDepartmentName != null and employeeDepartmentName != ''">
            and employee_department_full like CONCAT('%',#{employeeDepartmentName},'%')
        </if>
        <if test="startTime != null and startTime != ''">
            and voucher_grant_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and #{endTime} >= voucher_grant_time
        </if>
        group by employee_id, employee_department_id
        <if test="pageSize != null">
            <choose>
                <when test="offset == null">
                    limit #{pageSize}
                </when>
                <otherwise>
                    limit #{offset}, #{pageSize}
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="countForFlowStatistics"
            parameterType="com.fenbeitong.fenbeipay.core.model.dto.VouchersStatisticsReqDTO"
            resultType="Integer">
        select count(1) total_number from (
        select
        employee_id
        from vouchers_operation_flow
        where voucher_source != 7
        and company_id = #{companyId}
        <if test="employeeId != null and employeeId != ''">
            and employee_id = #{employeeId}
        </if>
        <if test="employeeDepartmentId != null and employeeDepartmentId != ''">
            and employee_department_id = #{employeeDepartmentId}
        </if>
        <if test="employeeDepartmentName != null and employeeDepartmentName != ''">
            and employee_department_full like CONCAT('%',#{employeeDepartmentName},'%')
        </if>
        <if test="startTime != null and startTime != ''">
            and voucher_grant_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and #{endTime} >= voucher_grant_time
        </if>
        group by employee_id, employee_department_id)flow_group
    </select>

</mapper>
