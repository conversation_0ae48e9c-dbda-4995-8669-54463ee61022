<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersCostAttributionMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_flow_id" jdbcType="VARCHAR" property="voucherFlowId" />
    <result column="cost_attribution_id" jdbcType="VARCHAR" property="costAttributionId" />
    <result column="cost_attribution_type" jdbcType="SMALLINT" property="costAttributionType" />
    <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName" />
    <result column="cost_attribution_path" jdbcType="VARCHAR" property="costAttributionPath" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, voucher_flow_id, cost_attribution_id, cost_attribution_type, cost_attribution_name, 
    cost_attribution_path, create_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttributionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from vouchers_cost_attribution
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_cost_attribution
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttributionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttribution" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_cost_attribution (voucher_flow_id, cost_attribution_id, 
      cost_attribution_type, cost_attribution_name, 
      cost_attribution_path, create_time)
    values (#{voucherFlowId,jdbcType=VARCHAR}, #{costAttributionId,jdbcType=VARCHAR}, 
      #{costAttributionType,jdbcType=SMALLINT}, #{costAttributionName,jdbcType=VARCHAR}, 
      #{costAttributionPath,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttribution" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_cost_attribution
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="voucherFlowId != null">
        voucher_flow_id,
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id,
      </if>
      <if test="costAttributionType != null">
        cost_attribution_type,
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name,
      </if>
      <if test="costAttributionPath != null">
        cost_attribution_path,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="voucherFlowId != null">
        #{voucherFlowId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionId != null">
        #{costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionType != null">
        #{costAttributionType,jdbcType=SMALLINT},
      </if>
      <if test="costAttributionName != null">
        #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionPath != null">
        #{costAttributionPath,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttributionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from vouchers_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_cost_attribution
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.voucherFlowId != null">
        voucher_flow_id = #{record.voucherFlowId,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionId != null">
        cost_attribution_id = #{record.costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionType != null">
        cost_attribution_type = #{record.costAttributionType,jdbcType=SMALLINT},
      </if>
      <if test="record.costAttributionName != null">
        cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionPath != null">
        cost_attribution_path = #{record.costAttributionPath,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_cost_attribution
    set id = #{record.id,jdbcType=BIGINT},
      voucher_flow_id = #{record.voucherFlowId,jdbcType=VARCHAR},
      cost_attribution_id = #{record.costAttributionId,jdbcType=VARCHAR},
      cost_attribution_type = #{record.costAttributionType,jdbcType=SMALLINT},
      cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      cost_attribution_path = #{record.costAttributionPath,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_cost_attribution
    <set>
      <if test="voucherFlowId != null">
        voucher_flow_id = #{voucherFlowId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionType != null">
        cost_attribution_type = #{costAttributionType,jdbcType=SMALLINT},
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionPath != null">
        cost_attribution_path = #{costAttributionPath,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_cost_attribution
    set voucher_flow_id = #{voucherFlowId,jdbcType=VARCHAR},
      cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR},
      cost_attribution_type = #{costAttributionType,jdbcType=SMALLINT},
      cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      cost_attribution_path = #{costAttributionPath,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>