<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskDetailsExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskDetails">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="vouchers_task_details_id" jdbcType="VARCHAR" property="vouchersTaskDetailsId" />
        <result column="vouchers_task_id" jdbcType="VARCHAR" property="vouchersTaskId" />
        <result column="vouchers_task_type" jdbcType="SMALLINT" property="vouchersTaskType" />
        <result column="status" jdbcType="SMALLINT" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="operation_amount" jdbcType="DECIMAL" property="operationAmount" />
        <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
        <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
        <result column="voucher_id" jdbcType="VARCHAR" property="voucherId" />
        <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
        <result column="voucher_denomination" jdbcType="DECIMAL" property="voucherDenomination" />
        <result column="voucher_effective_time" jdbcType="TIMESTAMP" property="voucherEffectiveTime" />
        <result column="voucher_expiry_time" jdbcType="TIMESTAMP" property="voucherExpiryTime" />
        <result column="voucher_expiry_notice" jdbcType="TIMESTAMP" property="voucherExpiryNotice" />
        <result column="voucher_expiry_notice_time" jdbcType="TIMESTAMP" property="voucherExpiryNoticeTime" />
        <result column="voucher_type_list" jdbcType="VARCHAR" property="voucherTypeList" />
        <result column="voucher_source" jdbcType="SMALLINT" property="voucherSource" />
        <result column="voucher_recovery_type" jdbcType="SMALLINT" property="voucherRecoveryType" />
        <result column="voucher_desc" jdbcType="VARCHAR" property="voucherDesc" />
        <result column="company_id" jdbcType="VARCHAR" property="companyId" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
        <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
        <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
        <result column="employee_department" jdbcType="VARCHAR" property="employeeDepartment" />
        <result column="employee_department_id" jdbcType="VARCHAR" property="employeeDepartmentId" />
        <result column="employee_department_full" jdbcType="VARCHAR" property="employeeDepartmentFull" />
        <result column="write_invoice_type" jdbcType="INTEGER" property="writeInvoiceType" />
        <result column="write_invoice_status" jdbcType="INTEGER" property="writeInvoiceStatus" />
        <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
        <result column="can_transfer" jdbcType="INTEGER" property="canTransfer" />
        <result column="mongo_id" jdbcType="VARCHAR" property="mongoId" />
        <result column="voucher_effective_days" jdbcType="SMALLINT" property="voucherEffectiveDays" />
        <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
        <result column="account_model" jdbcType="INTEGER" property="accountModel" />
        <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
        <result column="voucher_model" jdbcType="INTEGER" property="voucherModel" />
        <result column="cost_attribution_sign" jdbcType="INTEGER" property="costAttributionSign" />
        <result column="cost_info" jdbcType="VARCHAR" property="costInfo" />
        <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
        <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
        <result column="is_ding" jdbcType="TINYINT" property="isDing" />
        <result column="original_voucher_id" jdbcType="VARCHAR" property="originalVoucherId" />
        <result column="parent_voucher_id" jdbcType="VARCHAR" property="parentVoucherId" />
    </resultMap>
    <insert id="insertList" parameterType="java.util.List">
        insert into vouchers_task_details
        (vouchers_task_details_id, vouchers_task_id, vouchers_task_type,
        status, create_time, update_time,
        end_time, operation_amount,
        operation_user_id, biz_no, voucher_id,
        voucher_name, voucher_denomination, voucher_effective_time,
        voucher_expiry_time, voucher_expiry_notice, voucher_expiry_notice_time, voucher_type_list, voucher_source,
        voucher_recovery_type, voucher_desc, company_id,
        company_name, employee_id, employee_name,
        employee_phone, employee_department, employee_department_id, employee_department_full,
        write_invoice_type, write_invoice_status, can_transfer, mongo_id, deduction_account_type, bill_status,
        account_model, account_sub_id, voucher_model, cost_attribution_sign, cost_info,
        bank_name, bank_account_no, is_ding, original_voucher_id, parent_voucher_id)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            (#{record.vouchersTaskDetailsId,jdbcType=VARCHAR}, #{record.vouchersTaskId,jdbcType=VARCHAR}, #{record.vouchersTaskType,jdbcType=SMALLINT},
            #{record.status,jdbcType=SMALLINT}, #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateTime,jdbcType=TIMESTAMP},
            #{record.endTime,jdbcType=TIMESTAMP}, #{record.operationAmount,jdbcType=DECIMAL},
            #{record.operationUserId,jdbcType=VARCHAR}, #{record.bizNo,jdbcType=VARCHAR}, #{record.voucherId,jdbcType=VARCHAR},
            #{record.voucherName,jdbcType=VARCHAR}, #{record.voucherDenomination,jdbcType=DECIMAL}, #{record.voucherEffectiveTime,jdbcType=TIMESTAMP},
            #{record.voucherExpiryTime,jdbcType=TIMESTAMP}, #{record.voucherExpiryNotice,jdbcType=INTEGER}, #{record.voucherExpiryNoticeTime,jdbcType=TIMESTAMP}, #{record.voucherTypeList,jdbcType=VARCHAR}, #{record.voucherSource,jdbcType=SMALLINT},
            #{record.voucherRecoveryType,jdbcType=SMALLINT}, #{record.voucherDesc,jdbcType=VARCHAR}, #{record.companyId,jdbcType=VARCHAR},
            #{record.companyName,jdbcType=VARCHAR}, #{record.employeeId,jdbcType=VARCHAR}, #{record.employeeName,jdbcType=VARCHAR},
            #{record.employeePhone,jdbcType=VARCHAR}, #{record.employeeDepartment,jdbcType=VARCHAR}, #{record.employeeDepartmentId,jdbcType=VARCHAR}, #{record.employeeDepartmentFull,jdbcType=VARCHAR},
            #{record.writeInvoiceType,jdbcType=INTEGER}, #{record.writeInvoiceStatus,jdbcType=INTEGER}, #{record.canTransfer,jdbcType=INTEGER}, #{record.mongoId,jdbcType=VARCHAR}, #{record.deductionAccountType,jdbcType=INTEGER},
            #{record.billStatus,jdbcType=INTEGER}, #{record.accountModel,jdbcType=INTEGER}, #{record.accountSubId,jdbcType=VARCHAR}, #{record.voucherModel,jdbcType=INTEGER},
            #{record.costAttributionSign,jdbcType=INTEGER}, #{record.costInfo,jdbcType=VARCHAR},
            #{record.bankName,jdbcType=VARCHAR}, #{record.bankAccountNo,jdbcType=VARCHAR},
            #{record.isDing,jdbcType=TINYINT}, #{record.originalVoucherId,jdbcType=VARCHAR}, #{record.parentVoucherId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="countTotalAmount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select sum(voucher_denomination) from vouchers_task_details
        where vouchers_task_id =#{voucherTaskId}
        <if test="voucherTaskStatus != null and voucherTaskStatus != -1">
            and status = #{voucherTaskStatus}
        </if>
    </select>

    <select id="countTotalOperationAmount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select sum(operation_amount) from vouchers_task_details
        where vouchers_task_id =#{voucherTaskId}
        <if test="voucherTaskStatus != null and voucherTaskStatus != -1">
            and status = #{voucherTaskStatus}
        </if>
    </select>

    <select id="countTotalOperation" parameterType="java.util.Map" resultType="java.util.Map">
        select sum(operation_amount) as 'totalAmount',count(id) as 'totalCount'
        from vouchers_task_details
        where vouchers_task_id =#{voucherTaskId}
        <if test="voucherTaskStatus != null and voucherTaskStatus != -1">
            and status = #{voucherTaskStatus}
        </if>
    </select>

    <select id="selectVoucherIdsByVoucherTaskId" parameterType="java.util.Map" resultType="java.lang.String">
        select voucher_id from vouchers_task_details
        where vouchers_task_id =#{voucherTaskId}
        <if test="offset != null and limit != null">
            limit ${offset}, ${limit}
        </if>
    </select>

    <select id="selectVoucherIdsAndAmountByVoucherTaskId" parameterType="java.util.Map" resultType="com.fenbeitong.fenbeipay.core.model.dto.VoucherIdAmtDTO">
        select voucher_id as voucherId,operation_amount as operateAmt from vouchers_task_details
        where vouchers_task_id =#{voucherTaskId}
        <if test="offset != null and limit != null">
            limit ${offset}, ${limit}
        </if>
    </select>
</mapper>
