<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskImportMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vouchers_task_import_id" jdbcType="VARCHAR" property="vouchersTaskImportId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="completion_rate" jdbcType="DECIMAL" property="completionRate" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="estimate_number" jdbcType="INTEGER" property="estimateNumber" />
    <result column="estimate_amount" jdbcType="DECIMAL" property="estimateAmount" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="file_bucket" jdbcType="VARCHAR" property="fileBucket" />
    <result column="file_host" jdbcType="VARCHAR" property="fileHost" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="write_invoice_type" jdbcType="INTEGER" property="writeInvoiceType" />
    <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
    <result column="cost_info" jdbcType="VARCHAR" property="costInfo" />
    <result column="date_of_expense" jdbcType="VARCHAR" property="dateOfExpense" />
    <result column="cost_status" jdbcType="INTEGER" property="costStatus" />
    <result column="task_desc" jdbcType="VARCHAR" property="taskDesc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, vouchers_task_import_id, company_id, create_time, update_time, completion_rate,
    status, estimate_number, estimate_amount, operation_user_id, operation_user_name,
    file_bucket, file_host, file_url, file_path, write_invoice_type, deduction_account_type, cost_info, cost_status, task_desc, date_of_expense
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_task_import
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from vouchers_task_import
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_task_import
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_task_import
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_task_import (id, vouchers_task_import_id, company_id,
      create_time, update_time, completion_rate,
      status, estimate_number, estimate_amount,
      operation_user_id, operation_user_name, file_bucket,
      file_host, file_url, file_path,
      write_invoice_type, deduction_account_type)
    values (#{id,jdbcType=BIGINT}, #{vouchersTaskImportId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{completionRate,jdbcType=DECIMAL},
      #{status,jdbcType=SMALLINT}, #{estimateNumber,jdbcType=INTEGER}, #{estimateAmount,jdbcType=DECIMAL},
      #{operationUserId,jdbcType=VARCHAR}, #{operationUserName,jdbcType=VARCHAR}, #{fileBucket,jdbcType=VARCHAR},
      #{fileHost,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR},
      #{writeInvoiceType,jdbcType=INTEGER}, #{deductionAccountType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_task_import
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vouchersTaskImportId != null">
        vouchers_task_import_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="completionRate != null">
        completion_rate,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="estimateNumber != null">
        estimate_number,
      </if>
      <if test="estimateAmount != null">
        estimate_amount,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="operationUserName != null">
        operation_user_name,
      </if>
      <if test="fileBucket != null">
        file_bucket,
      </if>
      <if test="fileHost != null">
        file_host,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type,
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type,
      </if>
      <if test="costInfo != null and costInfo != ''">
        cost_info,
      </if>
      <if test="dateOfExpense != null and dateOfExpense != ''">
        date_of_expense,
      </if>
      <if test="costStatus != null">
        cost_status,
      </if>
      <if test="taskDesc != null">
        task_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vouchersTaskImportId != null">
        #{vouchersTaskImportId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completionRate != null">
        #{completionRate,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="estimateNumber != null">
        #{estimateNumber,jdbcType=INTEGER},
      </if>
      <if test="estimateAmount != null">
        #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserName != null">
        #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="fileBucket != null">
        #{fileBucket,jdbcType=VARCHAR},
      </if>
      <if test="fileHost != null">
        #{fileHost,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="deductionAccountType != null">
        #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="costInfo != null and costInfo != ''">
        #{costInfo,jdbcType=VARCHAR},
      </if>
      <if test="dateOfExpense != null and dateOfExpense != ''">
        #{dateOfExpense,jdbcType=VARCHAR},
      </if>
      <if test="costStatus != null">
        #{costStatus,jdbcType=INTEGER},
      </if>
      <if test="taskDesc != null">
        #{taskDesc,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImportExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from vouchers_task_import
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_import
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vouchersTaskImportId != null">
        vouchers_task_import_id = #{record.vouchersTaskImportId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completionRate != null">
        completion_rate = #{record.completionRate,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.estimateNumber != null">
        estimate_number = #{record.estimateNumber,jdbcType=INTEGER},
      </if>
      <if test="record.estimateAmount != null">
        estimate_amount = #{record.estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.operationUserId != null">
        operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserName != null">
        operation_user_name = #{record.operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileBucket != null">
        file_bucket = #{record.fileBucket,jdbcType=VARCHAR},
      </if>
      <if test="record.fileHost != null">
        file_host = #{record.fileHost,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.filePath != null">
        file_path = #{record.filePath,jdbcType=VARCHAR},
      </if>
      <if test="record.writeInvoiceType != null">
        write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.deductionAccountType != null">
        deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_import
    set id = #{record.id,jdbcType=BIGINT},
      vouchers_task_import_id = #{record.vouchersTaskImportId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      completion_rate = #{record.completionRate,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=SMALLINT},
      estimate_number = #{record.estimateNumber,jdbcType=INTEGER},
      estimate_amount = #{record.estimateAmount,jdbcType=DECIMAL},
      operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      operation_user_name = #{record.operationUserName,jdbcType=VARCHAR},
      file_bucket = #{record.fileBucket,jdbcType=VARCHAR},
      file_host = #{record.fileHost,jdbcType=VARCHAR},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      file_path = #{record.filePath,jdbcType=VARCHAR},
      write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_import
    <set>
      <if test="vouchersTaskImportId != null">
        vouchers_task_import_id = #{vouchersTaskImportId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completionRate != null">
        completion_rate = #{completionRate,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="estimateNumber != null">
        estimate_number = #{estimateNumber,jdbcType=INTEGER},
      </if>
      <if test="estimateAmount != null">
        estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserName != null">
        operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="fileBucket != null">
        file_bucket = #{fileBucket,jdbcType=VARCHAR},
      </if>
      <if test="fileHost != null">
        file_host = #{fileHost,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_task_import
    set vouchers_task_import_id = #{vouchersTaskImportId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      completion_rate = #{completionRate,jdbcType=DECIMAL},
      status = #{status,jdbcType=SMALLINT},
      estimate_number = #{estimateNumber,jdbcType=INTEGER},
      estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      file_bucket = #{fileBucket,jdbcType=VARCHAR},
      file_host = #{fileHost,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      deduction_account_type = #{deductionAccountType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
