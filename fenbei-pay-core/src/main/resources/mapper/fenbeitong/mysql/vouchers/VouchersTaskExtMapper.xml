<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="vouchers_task_id" jdbcType="VARCHAR" property="vouchersTaskId" />
        <result column="vouchers_task_name" jdbcType="VARCHAR" property="vouchersTaskName" />
        <result column="vouchers_task_type" jdbcType="SMALLINT" property="vouchersTaskType" />
        <result column="vouchers_number" jdbcType="BIGINT" property="vouchersNumber" />
        <result column="vouchers_total_amount" jdbcType="DECIMAL" property="vouchersTotalAmount" />
        <result column="vouchers_operation_number" jdbcType="BIGINT" property="vouchersOperationNumber" />
        <result column="vouchers_operation_amount" jdbcType="DECIMAL" property="vouchersOperationAmount" />
        <result column="vouchers_info_json" jdbcType="VARCHAR" property="vouchersInfoJson" />
        <result column="voucher_recovery_type" jdbcType="SMALLINT" property="voucherRecoveryType" />
        <result column="status" jdbcType="SMALLINT" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="company_id" jdbcType="VARCHAR" property="companyId" />
        <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
        <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
        <result column="operation_user_phone" jdbcType="VARCHAR" property="operationUserPhone" />
        <result column="operation_user_department" jdbcType="VARCHAR" property="operationUserDepartment" />
        <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
        <result column="task_desc" jdbcType="VARCHAR" property="taskDesc" />
        <result column="task_failure_reasons" jdbcType="VARCHAR" property="taskFailureReasons" />
        <result column="write_invoice_type" jdbcType="INTEGER" property="writeInvoiceType" />
        <result column="write_invoice_status" jdbcType="INTEGER" property="writeInvoiceStatus" />
        <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
        <result column="can_transfer" jdbcType="INTEGER" property="canTransfer" />
        <result column="task_biz_id" jdbcType="VARCHAR" property="taskBizId" />
        <result column="total_recovery_amount" jdbcType="DECIMAL" property="totalRecoveryAmount" />
        <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
        <result column="account_model" jdbcType="INTEGER" property="accountModel" />
        <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
        <result column="account_sub_flow_id" jdbcType="VARCHAR" property="accountSubFlowId" />
        <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
        <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    </resultMap>

    <select id="countCanInvoiceVoucherTasks" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(*) from vouchers_task where
        company_id = #{reqDTO.companyId,jdbcType=VARCHAR}
        and vouchers_task_type != 2
        and write_invoice_type = 1
        and bill_status = 1
        and status = 2
        and vouchers_operation_amount > total_recovery_amount
        <if test="reqDTO.writeInvoiceStatus != null">
            and write_invoice_status = #{reqDTO.writeInvoiceStatus,jdbcType=INTEGER}
        </if>
        <if test="reqDTO.id != null">
            and id = #{reqDTO.id,jdbcType=BIGINT}
        </if>
        <if test="reqDTO.operationUserName != null">
            and operation_user_name = #{reqDTO.operationUserName,jdbcType=VARCHAR}
        </if>
        <if test="reqDTO.operationUserPhone != null">
            and operation_user_phone = #{reqDTO.operationUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="reqDTO.vouchersTaskType != null">
            and vouchers_task_type = #{reqDTO.vouchersTaskType,jdbcType=SMALLINT}
        </if>
        <if test="reqDTO.startTime != null and reqDTO.endTime != null">
            and create_time between #{reqDTO.startTime,jdbcType=TIMESTAMP} and #{reqDTO.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="reqDTO.idList != null">
            and id in
            <foreach close=")" collection="reqDTO.idList" item="id" index="index" open="(" separator=",">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="reqDTO.vouchersTaskId != null and reqDTO.vouchersTaskId != ''">
            and vouchers_task_id = #{reqDTO.vouchersTaskId,jdbcType=VARCHAR}
        </if>
        <if test="reqDTO.vouchersTaskIds != null">
            and vouchers_task_id in
            <foreach close=")" collection="reqDTO.vouchersTaskIds" item="vouchersTaskId" index="index" open="("
                     separator=",">
                #{vouchersTaskId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="queryCanInvoiceVoucherTasks" parameterType="java.util.Map" resultMap="BaseResultMap">
        select * from vouchers_task where
        company_id = #{reqDTO.companyId,jdbcType=VARCHAR}
        and vouchers_task_type != 2
        and write_invoice_type = 1
        and bill_status = 1
        and status = 2
        and vouchers_operation_amount > total_recovery_amount
        <if test="reqDTO.writeInvoiceStatus != null">
            and write_invoice_status = #{reqDTO.writeInvoiceStatus,jdbcType=INTEGER}
        </if>
        <if test="reqDTO.id != null">
            and id = #{reqDTO.id,jdbcType=BIGINT}
        </if>
        <if test="reqDTO.operationUserName != null">
            and operation_user_name = #{reqDTO.operationUserName,jdbcType=VARCHAR}
        </if>
        <if test="reqDTO.operationUserPhone != null">
            and operation_user_phone = #{reqDTO.operationUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="reqDTO.vouchersTaskType != null">
            and vouchers_task_type = #{reqDTO.vouchersTaskType,jdbcType=SMALLINT}
        </if>
        <if test="reqDTO.startTime != null and reqDTO.endTime != null">
            and create_time between #{reqDTO.startTime,jdbcType=TIMESTAMP} and #{reqDTO.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="reqDTO.idList != null">
            and id in
            <foreach close=")" collection="reqDTO.idList" item="id" index="index" open="(" separator=",">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="reqDTO.vouchersTaskId != null and reqDTO.vouchersTaskId != ''">
            and vouchers_task_id = #{reqDTO.vouchersTaskId,jdbcType=VARCHAR}
        </if>
        <if test="reqDTO.vouchersTaskIds != null">
            and vouchers_task_id in
            <foreach close=")" collection="reqDTO.vouchersTaskIds" item="vouchersTaskId" index="index" open="("
                     separator=",">
                #{vouchersTaskId,jdbcType=BIGINT}
            </foreach>
        </if>
        order by create_time desc
        <if test="reqDTO.offset != null and reqDTO.pageSize != null">
            limit ${reqDTO.offset}, ${reqDTO.pageSize}
        </if>

    </select>
    <select id="queryCanInvoiceVoucherTasksByTaskIds" parameterType="java.util.Map" resultMap="BaseResultMap">
        select * from vouchers_task where
        company_id = #{companyId,jdbcType=VARCHAR}
        and vouchers_task_type != 2
        and write_invoice_type = 1
        and bill_status = 1
        and status = 2
        and vouchers_operation_amount > total_recovery_amount
        <if test="taskIds != null">
            and vouchers_task_id in
            <foreach close=")" collection="taskIds" item="vouchersTaskId" index="index" open="(" separator=",">
                #{vouchersTaskId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <update id="finishVouchersTask" parameterType="java.lang.String">
        update vouchers_task vt
        set vt.vouchers_operation_number =(select count(1)
                                           from vouchers_task_details vtd
                                           where vtd.vouchers_task_id = #{vouchersTaskId,jdbcType=VARCHAR}
                                             and vtd.status = 2),
            vt.vouchers_operation_amount =(select ifnull(sum(vtd.operation_amount),0)
                                           from vouchers_task_details vtd
                                           where vtd.vouchers_task_id = #{vouchersTaskId,jdbcType=VARCHAR}
                                             and vtd.status = 2),
            vt.update_time               = now(),
            vt.end_time                  =now(),
            vt.status                    =2
        WHERE vt.vouchers_task_id = #{vouchersTaskId,jdbcType=VARCHAR}
    </update>

    <insert id="insertAll" parameterType="java.util.List">
        insert into vouchers_task
        (vouchers_task_id, vouchers_task_name,
        vouchers_task_type, vouchers_number, vouchers_total_amount,
        vouchers_operation_number, vouchers_operation_amount,
        vouchers_info_json, voucher_recovery_type,
        status, create_time, update_time,
        end_time, company_id, operation_user_id,
        operation_user_name, operation_user_phone,
        operation_user_department, biz_no, task_desc,
        task_failure_reasons, write_invoice_type, write_invoice_status,
        bill_status, can_transfer, task_biz_id,
        total_recovery_amount, deduction_account_type,
        account_model, account_sub_id, account_sub_flow_id,
        bank_name, bank_account_no)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            (#{record.vouchersTaskId,jdbcType=VARCHAR}, #{record.vouchersTaskName,jdbcType=VARCHAR},
            #{record.vouchersTaskType,jdbcType=SMALLINT}, #{record.vouchersNumber,jdbcType=BIGINT}, #{record.vouchersTotalAmount,jdbcType=DECIMAL},
            #{record.vouchersOperationNumber,jdbcType=BIGINT}, #{record.vouchersOperationAmount,jdbcType=DECIMAL},
            #{record.vouchersInfoJson,jdbcType=VARCHAR}, #{record.voucherRecoveryType,jdbcType=SMALLINT},
            #{record.status,jdbcType=SMALLINT}, #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateTime,jdbcType=TIMESTAMP},
            #{record.endTime,jdbcType=TIMESTAMP}, #{record.companyId,jdbcType=VARCHAR}, #{record.operationUserId,jdbcType=VARCHAR},
            #{record.operationUserName,jdbcType=VARCHAR}, #{record.operationUserPhone,jdbcType=VARCHAR},
            #{record.operationUserDepartment,jdbcType=VARCHAR}, #{record.bizNo,jdbcType=VARCHAR}, #{record.taskDesc,jdbcType=VARCHAR},
            #{record.taskFailureReasons,jdbcType=VARCHAR}, #{record.writeInvoiceType,jdbcType=INTEGER}, #{record.writeInvoiceStatus,jdbcType=INTEGER},
            #{record.billStatus,jdbcType=INTEGER}, #{record.canTransfer,jdbcType=INTEGER}, #{record.taskBizId,jdbcType=VARCHAR},
            #{record.totalRecoveryAmount,jdbcType=DECIMAL}, #{record.deductionAccountType,jdbcType=INTEGER},
            #{record.accountModel,jdbcType=INTEGER}, #{record.accountSubId,jdbcType=VARCHAR}, #{record.accountSubFlowId,jdbcType=VARCHAR},
            #{record.bankName,jdbcType=VARCHAR}, #{record.bankAccountNo,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>