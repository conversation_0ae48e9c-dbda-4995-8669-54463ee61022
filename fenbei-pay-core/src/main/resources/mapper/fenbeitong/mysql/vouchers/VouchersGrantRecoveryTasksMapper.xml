<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersGrantRecoveryTasksMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasks">
    <id column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="task_type" jdbcType="SMALLINT" property="taskType" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
    <result column="voucher_denomination" jdbcType="DECIMAL" property="voucherDenomination" />
    <result column="voucher_effective_time" jdbcType="TIMESTAMP" property="voucherEffectiveTime" />
    <result column="voucher_expiry_time" jdbcType="TIMESTAMP" property="voucherExpiryTime" />
    <result column="voucher_expiry_notice" jdbcType="INTEGER" property="voucherExpiryNotice" />
    <result column="voucher_type_list" jdbcType="VARCHAR" property="voucherTypeList" />
    <result column="can_transfer" jdbcType="INTEGER" property="canTransfer" />
    <result column="voucher_desc" jdbcType="VARCHAR" property="voucherDesc" />
    <result column="vouchers_number" jdbcType="INTEGER" property="vouchersNumber" />
    <result column="target_type" jdbcType="INTEGER" property="targetType" />
    <result column="target_ids" jdbcType="VARCHAR" property="targetIds" />
    <result column="employee_ids" jdbcType="VARCHAR" property="employeeIds" />
    <result column="voucher_ids" jdbcType="VARCHAR" property="voucherIds" />
    <result column="task_desc" jdbcType="VARCHAR" property="taskDesc" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="operation_user_phone" jdbcType="VARCHAR" property="operationUserPhone" />
    <result column="operation_user_department" jdbcType="VARCHAR" property="operationUserDepartment" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="voucher_rules" jdbcType="VARCHAR" property="voucherRules" />
    <result column="freezen_budget_freezing_id" jdbcType="VARCHAR" property="freezenBudgetFreezingId" />
    <result column="timing_grant_time" jdbcType="TIMESTAMP" property="timingGrantTime" />
    <result column="grant_type" jdbcType="INTEGER" property="grantType" />
    <result column="open_enable" jdbcType="INTEGER" property="openEnable" />
    <result column="background_url" jdbcType="VARCHAR" property="backgroundUrl" />
    <result column="background_name" jdbcType="VARCHAR" property="backgroundName" />
    <result column="notice_content" jdbcType="VARCHAR" property="noticeContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    task_id, task_type, company_id, create_time, end_time, status, voucher_name, voucher_denomination,
    voucher_effective_time, voucher_expiry_time,voucher_expiry_notice, voucher_type_list, can_transfer, voucher_desc,
    vouchers_number, target_type, target_ids, employee_ids, voucher_ids, task_desc, operation_user_id,
    operation_user_name, operation_user_phone, operation_user_department, biz_no, voucher_rules,
    freezen_budget_freezing_id, open_enable, background_url, background_name,notice_content
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasksExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_grant_recovery_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from vouchers_grant_recovery_tasks
    where task_id = #{taskId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from vouchers_grant_recovery_tasks
    where task_id = #{taskId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasksExample">
    delete from vouchers_grant_recovery_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasks">
    insert into vouchers_grant_recovery_tasks (task_id, task_type, company_id,
      create_time, end_time, status,
      voucher_name, voucher_denomination, voucher_effective_time,
      voucher_expiry_time,voucher_expiry_notice, voucher_type_list, can_transfer,
      voucher_desc, vouchers_number, target_type,
      target_ids, employee_ids, voucher_ids,
      task_desc, operation_user_id, operation_user_name,
      operation_user_phone, operation_user_department,
      biz_no, voucher_rules, freezen_budget_freezing_id
      )
    values (#{taskId,jdbcType=VARCHAR}, #{taskType,jdbcType=SMALLINT}, #{companyId,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{status,jdbcType=SMALLINT},
      #{voucherName,jdbcType=VARCHAR}, #{voucherDenomination,jdbcType=DECIMAL}, #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      #{voucherExpiryTime,jdbcType=TIMESTAMP}, #{voucherExpiryNotice,jdbcType=INTEGER}, #{voucherTypeList,jdbcType=VARCHAR}, #{canTransfer,jdbcType=INTEGER},
      #{voucherDesc,jdbcType=VARCHAR}, #{vouchersNumber,jdbcType=INTEGER}, #{targetType,jdbcType=INTEGER},
      #{targetIds,jdbcType=VARCHAR}, #{employeeIds,jdbcType=VARCHAR}, #{voucherIds,jdbcType=VARCHAR},
      #{taskDesc,jdbcType=VARCHAR}, #{operationUserId,jdbcType=VARCHAR}, #{operationUserName,jdbcType=VARCHAR},
      #{operationUserPhone,jdbcType=VARCHAR}, #{operationUserDepartment,jdbcType=VARCHAR},
      #{bizNo,jdbcType=VARCHAR}, #{voucherRules,jdbcType=VARCHAR}, #{freezenBudgetFreezingId,jdbcType=VARCHAR},
      #{timingGrantTime,jdbcType=TIMESTAMP}, #{grantType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasks">
    insert into vouchers_grant_recovery_tasks
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="voucherName != null">
        voucher_name,
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination,
      </if>
      <if test="voucherEffectiveTime != null">
        voucher_effective_time,
      </if>
      <if test="voucherExpiryTime != null">
        voucher_expiry_time,
      </if>
      <if test="voucherExpiryNotice != null">
        voucher_expiry_notice,
      </if>
      <if test="voucherTypeList != null">
        voucher_type_list,
      </if>
      <if test="canTransfer != null">
        can_transfer,
      </if>
      <if test="voucherDesc != null">
        voucher_desc,
      </if>
      <if test="vouchersNumber != null">
        vouchers_number,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="targetIds != null">
        target_ids,
      </if>
      <if test="employeeIds != null">
        employee_ids,
      </if>
      <if test="voucherIds != null">
        voucher_ids,
      </if>
      <if test="taskDesc != null">
        task_desc,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="operationUserName != null">
        operation_user_name,
      </if>
      <if test="operationUserPhone != null">
        operation_user_phone,
      </if>
      <if test="operationUserDepartment != null">
        operation_user_department,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="voucherRules != null">
        voucher_rules,
      </if>
      <if test="freezenBudgetFreezingId != null">
        freezen_budget_freezing_id,
      </if>
      <!-- new added -->
      <if test="timingGrantTime != null">
        timing_grant_time,
      </if>
      <if test="grantType != null">
        grant_type,
      </if>
      <if test="costInfo != null and costInfo != ''">
        cost_info,
      </if>
      <if test="dateOfExpense != null and dateOfExpense != ''">
        date_of_expense,
      </if>
      <if test="openEnable != null">
        open_enable,
      </if>
      <if test="backgroundUrl != null">
        background_url,
      </if>
      <if test="backgroundName != null">
        background_name,
      </if>
      <if test="noticeContent != null">
        notice_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=SMALLINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="voucherName != null">
        #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="voucherEffectiveTime != null">
        #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryTime != null">
        #{voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryNotice != null">
        #{voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="voucherTypeList != null">
        #{voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="canTransfer != null">
        #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="voucherDesc != null">
        #{voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="vouchersNumber != null">
        #{vouchersNumber,jdbcType=INTEGER},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetIds != null">
        #{targetIds,jdbcType=VARCHAR},
      </if>
      <if test="employeeIds != null">
        #{employeeIds,jdbcType=VARCHAR},
      </if>
      <if test="voucherIds != null">
        #{voucherIds,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null">
        #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserName != null">
        #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="operationUserPhone != null">
        #{operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="operationUserDepartment != null">
        #{operationUserDepartment,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherRules != null">
        #{voucherRules,jdbcType=VARCHAR},
      </if>
      <if test="freezenBudgetFreezingId != null">
        #{freezenBudgetFreezingId,jdbcType=VARCHAR},
      </if>
      <if test="timingGrantTime != null">
        #{timingGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grantType != null">
        #{grantType,jdbcType=INTEGER},
      </if>
      <if test="costInfo != null and costInfo != ''">
        #{costInfo,jdbcType=VARCHAR},
      </if>
      <if test="dateOfExpense != null and dateOfExpense != ''">
        #{dateOfExpense,jdbcType=VARCHAR},
      </if>
      <if test="openEnable != null">
        #{openEnable,jdbcType=INTEGER},
      </if>
      <if test="backgroundUrl != null">
        #{backgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="backgroundName != null">
        #{backgroundName,jdbcType=VARCHAR},
      </if>
      <if test="noticeContent != null">
        #{noticeContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasksExample" resultType="java.lang.Long">
    select count(*) from vouchers_grant_recovery_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vouchers_grant_recovery_tasks
    <set>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=SMALLINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.voucherName != null">
        voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherDenomination != null">
        voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherEffectiveTime != null">
        voucher_effective_time = #{record.voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherExpiryTime != null">
        voucher_expiry_time = #{record.voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherExpiryNotice != null">
        voucher_expiry_notice = #{record.voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="record.voucherTypeList != null">
        voucher_type_list = #{record.voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="record.canTransfer != null">
        can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      </if>
      <if test="record.voucherDesc != null">
        voucher_desc = #{record.voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.vouchersNumber != null">
        vouchers_number = #{record.vouchersNumber,jdbcType=INTEGER},
      </if>
      <if test="record.targetType != null">
        target_type = #{record.targetType,jdbcType=INTEGER},
      </if>
      <if test="record.targetIds != null">
        target_ids = #{record.targetIds,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeIds != null">
        employee_ids = #{record.employeeIds,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherIds != null">
        voucher_ids = #{record.voucherIds,jdbcType=VARCHAR},
      </if>
      <if test="record.taskDesc != null">
        task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserId != null">
        operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserName != null">
        operation_user_name = #{record.operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserPhone != null">
        operation_user_phone = #{record.operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserDepartment != null">
        operation_user_department = #{record.operationUserDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherRules != null">
        voucher_rules = #{record.voucherRules,jdbcType=VARCHAR},
      </if>
      <if test="record.freezenBudgetFreezingId != null">
        freezen_budget_freezing_id = #{record.freezenBudgetFreezingId,jdbcType=VARCHAR},
      </if>
      <if test="record.timingGrantTime != null">
        timing_grant_time = #{record.timingGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.grantType != null">
        grant_type = #{record.grantType,jdbcType=INTEGER}
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vouchers_grant_recovery_tasks
    set task_id = #{record.taskId,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=SMALLINT},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=SMALLINT},
      voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      voucher_effective_time = #{record.voucherEffectiveTime,jdbcType=TIMESTAMP},
      voucher_expiry_time = #{record.voucherExpiryTime,jdbcType=TIMESTAMP},
      voucher_expiry_notice = #{record.voucherExpiryNotice,jdbcType=INTEGER},
      voucher_type_list = #{record.voucherTypeList,jdbcType=VARCHAR},
      can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      voucher_desc = #{record.voucherDesc,jdbcType=VARCHAR},
      vouchers_number = #{record.vouchersNumber,jdbcType=INTEGER},
      target_type = #{record.targetType,jdbcType=INTEGER},
      target_ids = #{record.targetIds,jdbcType=VARCHAR},
      employee_ids = #{record.employeeIds,jdbcType=VARCHAR},
      voucher_ids = #{record.voucherIds,jdbcType=VARCHAR},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      operation_user_name = #{record.operationUserName,jdbcType=VARCHAR},
      operation_user_phone = #{record.operationUserPhone,jdbcType=VARCHAR},
      operation_user_department = #{record.operationUserDepartment,jdbcType=VARCHAR},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      voucher_rules = #{record.voucherRules,jdbcType=VARCHAR},
      freezen_budget_freezing_id = #{record.freezenBudgetFreezingId,jdbcType=VARCHAR},
      timing_grant_time = #{record.timingGrantTime,jdbcType=TIMESTAMP},
      grant_type = #{record.grantType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasks">
    update vouchers_grant_recovery_tasks
    <set>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=SMALLINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="voucherName != null">
        voucher_name = #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="voucherEffectiveTime != null">
        voucher_effective_time = #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryTime != null">
        voucher_expiry_time = #{voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryNotice != null">
        voucher_expiry_notice = #{voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="voucherTypeList != null">
        voucher_type_list = #{voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="canTransfer != null">
        can_transfer = #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="voucherDesc != null">
        voucher_desc = #{voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="vouchersNumber != null">
        vouchers_number = #{vouchersNumber,jdbcType=INTEGER},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetIds != null">
        target_ids = #{targetIds,jdbcType=VARCHAR},
      </if>
      <if test="employeeIds != null">
        employee_ids = #{employeeIds,jdbcType=VARCHAR},
      </if>
      <if test="voucherIds != null">
        voucher_ids = #{voucherIds,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null">
        task_desc = #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserName != null">
        operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="operationUserPhone != null">
        operation_user_phone = #{operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="operationUserDepartment != null">
        operation_user_department = #{operationUserDepartment,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherRules != null">
        voucher_rules = #{voucherRules,jdbcType=VARCHAR},
      </if>
      <if test="freezenBudgetFreezingId != null">
        freezen_budget_freezing_id = #{freezenBudgetFreezingId,jdbcType=VARCHAR},
      </if>
      <if test="timingGrantTime != null">
        timing_grant_time = #{timingGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grantType != null">
        grant_type = #{grantType,jdbcType=INTEGER},
      </if>
    </set>
    where task_id = #{taskId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersGrantRecoveryTasks">
    update vouchers_grant_recovery_tasks
    set task_type = #{taskType,jdbcType=SMALLINT},
      company_id = #{companyId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=SMALLINT},
      voucher_name = #{voucherName,jdbcType=VARCHAR},
      voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      voucher_effective_time = #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      voucher_expiry_time = #{voucherExpiryTime,jdbcType=TIMESTAMP},
      voucher_expiry_notice = #{voucherExpiryNotice,jdbcType=INTEGER},
      voucher_type_list = #{voucherTypeList,jdbcType=VARCHAR},
      can_transfer = #{canTransfer,jdbcType=INTEGER},
      voucher_desc = #{voucherDesc,jdbcType=VARCHAR},
      vouchers_number = #{vouchersNumber,jdbcType=INTEGER},
      target_type = #{targetType,jdbcType=INTEGER},
      target_ids = #{targetIds,jdbcType=VARCHAR},
      employee_ids = #{employeeIds,jdbcType=VARCHAR},
      voucher_ids = #{voucherIds,jdbcType=VARCHAR},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      operation_user_phone = #{operationUserPhone,jdbcType=VARCHAR},
      operation_user_department = #{operationUserDepartment,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      voucher_rules = #{voucherRules,jdbcType=VARCHAR},
      freezen_budget_freezing_id = #{freezenBudgetFreezingId,jdbcType=VARCHAR},
      timing_grant_time = #{timingGrantTime,jdbcType=TIMESTAMP},
      grant_type = #{grantType,jdbcType=INTEGER}
    where task_id = #{taskId,jdbcType=VARCHAR}
  </update>
</mapper>
