<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTempletMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_templet_id" jdbcType="VARCHAR" property="voucherTempletId" />
    <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
    <result column="voucher_term_type" jdbcType="SMALLINT" property="voucherTermType" />
    <result column="voucher_term_validity" jdbcType="VARCHAR" property="voucherTermValidity" />
    <result column="voucher_type_list" jdbcType="VARCHAR" property="voucherTypeList" />
    <result column="voucher_desc" jdbcType="VARCHAR" property="voucherDesc" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="write_invoice_type" jdbcType="INTEGER" property="writeInvoiceType" />
    <result column="can_transfer" jdbcType="INTEGER" property="canTransfer" />
    <result column="voucher_model" jdbcType="INTEGER" property="voucherModel" />
    <result column="cost_attribution_sign" jdbcType="INTEGER" property="costAttributionSign" />
    <result column="is_delete" jdbcType="SMALLINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="voucher_rules" jdbcType="VARCHAR" property="voucherRules" />
    <result column="voucher_denomination" jdbcType="DECIMAL" property="voucherDenomination" />
    <result column="voucher_effective_time" jdbcType="TIMESTAMP" property="voucherEffectiveTime" />
    <result column="voucher_expiry_time" jdbcType="TIMESTAMP" property="voucherExpiryTime" />
    <result column="voucher_expiry_notice" jdbcType="INTEGER" property="voucherExpiryNotice" />
    <result column="open_enable" jdbcType="INTEGER" property="openEnable" />
    <result column="background_url" jdbcType="VARCHAR" property="backgroundUrl" />
    <result column="background_name" jdbcType="VARCHAR" property="backgroundName" />
    <result column="notice_content" jdbcType="VARCHAR" property="noticeContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, voucher_templet_id, voucher_name, voucher_term_type, voucher_term_validity, voucher_type_list,
    voucher_desc, company_id, operation_user_id, write_invoice_type, can_transfer, voucher_model,
    cost_attribution_sign, is_delete, create_time, update_time, status, voucher_rules,
    voucher_denomination, voucher_effective_time, voucher_expiry_time, voucher_expiry_notice, open_enable , background_url, background_name, notice_content
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTempletExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_templet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from vouchers_templet
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_templet
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTempletExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vouchers_templet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_templet (id, voucher_templet_id, voucher_name,
      voucher_term_type, voucher_term_validity,
      voucher_type_list, voucher_desc, company_id,
      operation_user_id, write_invoice_type, can_transfer,
      voucher_model, cost_attribution_sign, is_delete,
      create_time, update_time, status,
      voucher_rules, voucher_denomination, voucher_effective_time,
      voucher_expiry_time, voucher_expiry_notice, open_enable , background_url, background_name, notice_content)
    values (#{id,jdbcType=BIGINT}, #{voucherTempletId,jdbcType=VARCHAR}, #{voucherName,jdbcType=VARCHAR},
      #{voucherTermType,jdbcType=SMALLINT}, #{voucherTermValidity,jdbcType=VARCHAR},
      #{voucherTypeList,jdbcType=VARCHAR}, #{voucherDesc,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR},
      #{operationUserId,jdbcType=VARCHAR}, #{writeInvoiceType,jdbcType=INTEGER}, #{canTransfer,jdbcType=INTEGER},
      #{voucherModel,jdbcType=INTEGER}, #{costAttributionSign,jdbcType=INTEGER}, #{isDelete,jdbcType=SMALLINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=SMALLINT},
      #{voucherRules,jdbcType=VARCHAR}, #{voucherDenomination,jdbcType=DECIMAL}, #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      #{voucherExpiryTime,jdbcType=TIMESTAMP},#{voucherExpiryNotice,jdbcType=INTEGER}, #{openEnable,jdbcType=INTEGER}, #{backgroundUrl,jdbcType=VARCHAR},#{backgroundName,jdbcType=VARCHAR},
    #{noticeContent,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vouchers_templet
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="voucherTempletId != null">
        voucher_templet_id,
      </if>
      <if test="voucherName != null">
        voucher_name,
      </if>
      <if test="voucherTermType != null">
        voucher_term_type,
      </if>
      <if test="voucherTermValidity != null">
        voucher_term_validity,
      </if>
      <if test="voucherTypeList != null">
        voucher_type_list,
      </if>
      <if test="voucherDesc != null">
        voucher_desc,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type,
      </if>
      <if test="canTransfer != null">
        can_transfer,
      </if>
      <if test="voucherModel != null">
        voucher_model,
      </if>
      <if test="costAttributionSign != null">
        cost_attribution_sign,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="voucherRules != null">
        voucher_rules,
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination,
      </if>
      <if test="voucherEffectiveTime != null">
        voucher_effective_time,
      </if>
      <if test="voucherExpiryTime != null">
        voucher_expiry_time,
      </if>
      <if test="voucherExpiryNotice != null">
        voucher_expiry_notice,
      </if>
      <if test="openEnable != null">
        open_enable,
      </if>
      <if test="backgroundUrl != null">
        background_url,
      </if>
      <if test="backgroundName != null">
        background_name,
      </if>
      <if test="noticeContent != null">
        notice_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="voucherTempletId != null">
        #{voucherTempletId,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherTermType != null">
        #{voucherTermType,jdbcType=SMALLINT},
      </if>
      <if test="voucherTermValidity != null">
        #{voucherTermValidity,jdbcType=VARCHAR},
      </if>
      <if test="voucherTypeList != null">
        #{voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="voucherDesc != null">
        #{voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="canTransfer != null">
        #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="voucherModel != null">
        #{voucherModel,jdbcType=INTEGER},
      </if>
      <if test="costAttributionSign != null">
        #{costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="voucherRules != null">
        #{voucherRules,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="voucherEffectiveTime != null">
        #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryTime != null">
        #{voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryNotice != null">
        #{voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="openEnable != null">
        #{openEnable,jdbcType=INTEGER},
      </if>
      <if test="backgroundUrl != null">
        #{backgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="backgroundName != null">
        #{backgroundName,jdbcType=VARCHAR},
      </if>
      <if test="noticeContent != null">
        #{noticeContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTempletExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from vouchers_templet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_templet
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.voucherTempletId != null">
        voucher_templet_id = #{record.voucherTempletId,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherName != null">
        voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherTermType != null">
        voucher_term_type = #{record.voucherTermType,jdbcType=SMALLINT},
      </if>
      <if test="record.voucherTermValidity != null">
        voucher_term_validity = #{record.voucherTermValidity,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherTypeList != null">
        voucher_type_list = #{record.voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherDesc != null">
        voucher_desc = #{record.voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserId != null">
        operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.writeInvoiceType != null">
        write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.canTransfer != null">
        can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      </if>
      <if test="record.voucherModel != null">
        voucher_model = #{record.voucherModel,jdbcType=INTEGER},
      </if>
      <if test="record.costAttributionSign != null">
        cost_attribution_sign = #{record.costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.voucherRules != null">
        voucher_rules = #{record.voucherRules,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherDenomination != null">
        voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherEffectiveTime != null">
        voucher_effective_time = #{record.voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherExpiryTime != null">
        voucher_expiry_time = #{record.voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.voucherExpiryNotice != null">
        voucher_expiry_notice = #{record.voucherExpiryNotice,jdbcType=INTEGER},
      </if>
      <if test="record.openEnable != null">
        open_enable = #{record.openEnable,jdbcType=INTEGER},
      </if>
      <if test="record.backgroundUrl != null">
        background_url = #{record.backgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.backgroundName != null">
        background_name = #{record.backgroundName,jdbcType=VARCHAR},
      </if>
      <if test="record.noticeContent != null">
        notice_content = #{record.noticeContent,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_templet
    set id = #{record.id,jdbcType=BIGINT},
      voucher_templet_id = #{record.voucherTempletId,jdbcType=VARCHAR},
      voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      voucher_term_type = #{record.voucherTermType,jdbcType=SMALLINT},
      voucher_term_validity = #{record.voucherTermValidity,jdbcType=VARCHAR},
      voucher_type_list = #{record.voucherTypeList,jdbcType=VARCHAR},
      voucher_desc = #{record.voucherDesc,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      voucher_model = #{record.voucherModel,jdbcType=INTEGER},
      cost_attribution_sign = #{record.costAttributionSign,jdbcType=INTEGER},
      is_delete = #{record.isDelete,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=SMALLINT},
      voucher_rules = #{record.voucherRules,jdbcType=VARCHAR},
      voucher_denomination = #{record.voucherDenomination,jdbcType=DECIMAL},
      voucher_effective_time = #{record.voucherEffectiveTime,jdbcType=TIMESTAMP},
      voucher_expiry_time = #{record.voucherExpiryTime,jdbcType=TIMESTAMP},
      voucher_expiry_notice = #{record.voucherExpiryNotice,jdbcType=INTEGER},
      open_enable = #{record.openEnable,jdbcType=INTEGER},
      background_url = #{record.backgroundUrl,jdbcType=VARCHAR},
      background_name = #{record.backgroundName,jdbcType=VARCHAR},
      notice_content = #{record.noticeContent,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_templet
    <set>
      <if test="voucherTempletId != null">
        voucher_templet_id = #{voucherTempletId,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        voucher_name = #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherTermType != null">
        voucher_term_type = #{voucherTermType,jdbcType=SMALLINT},
      </if>
      <if test="voucherTermValidity != null">
        voucher_term_validity = #{voucherTermValidity,jdbcType=VARCHAR},
      </if>
      <if test="voucherTypeList != null">
        voucher_type_list = #{voucherTypeList,jdbcType=VARCHAR},
      </if>
      <if test="voucherDesc != null">
        voucher_desc = #{voucherDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="canTransfer != null">
        can_transfer = #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="voucherModel != null">
        voucher_model = #{voucherModel,jdbcType=INTEGER},
      </if>
      <if test="costAttributionSign != null">
        cost_attribution_sign = #{costAttributionSign,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="voucherRules != null">
        voucher_rules = #{voucherRules,jdbcType=VARCHAR},
      </if>
      <if test="voucherDenomination != null">
        voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      </if>
      <if test="voucherEffectiveTime != null">
        voucher_effective_time = #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryTime != null">
        voucher_expiry_time = #{voucherExpiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherExpiryNotice != null">
        voucher_expiry_notice = #{voucherExpiryNotice,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTemplet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vouchers_templet
    set voucher_templet_id = #{voucherTempletId,jdbcType=VARCHAR},
      voucher_name = #{voucherName,jdbcType=VARCHAR},
      voucher_term_type = #{voucherTermType,jdbcType=SMALLINT},
      voucher_term_validity = #{voucherTermValidity,jdbcType=VARCHAR},
      voucher_type_list = #{voucherTypeList,jdbcType=VARCHAR},
      voucher_desc = #{voucherDesc,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      can_transfer = #{canTransfer,jdbcType=INTEGER},
      voucher_model = #{voucherModel,jdbcType=INTEGER},
      cost_attribution_sign = #{costAttributionSign,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=SMALLINT},
      voucher_rules = #{voucherRules,jdbcType=VARCHAR},
      voucher_denomination = #{voucherDenomination,jdbcType=DECIMAL},
      voucher_effective_time = #{voucherEffectiveTime,jdbcType=TIMESTAMP},
      voucher_expiry_time = #{voucherExpiryTime,jdbcType=TIMESTAMP}
      voucher_expiry_notice = #{voucherExpiryNotice,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
