<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VoucherTransferLastMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VoucherTransferLast">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="from_employee_id" jdbcType="VARCHAR" property="fromEmployeeId" />
    <result column="from_company_id" jdbcType="VARCHAR" property="fromCompanyId" />
    <result column="to_employee_id" jdbcType="VARCHAR" property="toEmployeeId" />
    <result column="to_company_id" jdbcType="VARCHAR" property="toCompanyId" />
    <result column="transfer_last_time" jdbcType="TIMESTAMP" property="transferLastTime" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, from_employee_id, from_company_id, to_employee_id, to_company_id, transfer_last_time, 
    delete_status, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VoucherTransferLastExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from voucher_transfer_last
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from voucher_transfer_last
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from voucher_transfer_last
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VoucherTransferLastExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from voucher_transfer_last
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VoucherTransferLast">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into voucher_transfer_last (id, from_employee_id, from_company_id, 
      to_employee_id, to_company_id, transfer_last_time, 
      delete_status, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{fromEmployeeId,jdbcType=VARCHAR}, #{fromCompanyId,jdbcType=VARCHAR}, 
      #{toEmployeeId,jdbcType=VARCHAR}, #{toCompanyId,jdbcType=VARCHAR}, #{transferLastTime,jdbcType=TIMESTAMP}, 
      #{deleteStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VoucherTransferLast">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into voucher_transfer_last
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fromEmployeeId != null">
        from_employee_id,
      </if>
      <if test="fromCompanyId != null">
        from_company_id,
      </if>
      <if test="toEmployeeId != null">
        to_employee_id,
      </if>
      <if test="toCompanyId != null">
        to_company_id,
      </if>
      <if test="transferLastTime != null">
        transfer_last_time,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fromEmployeeId != null">
        #{fromEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="fromCompanyId != null">
        #{fromCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="toEmployeeId != null">
        #{toEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="toCompanyId != null">
        #{toCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="transferLastTime != null">
        #{transferLastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VoucherTransferLastExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from voucher_transfer_last
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update voucher_transfer_last
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.fromEmployeeId != null">
        from_employee_id = #{record.fromEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.fromCompanyId != null">
        from_company_id = #{record.fromCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.toEmployeeId != null">
        to_employee_id = #{record.toEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.toCompanyId != null">
        to_company_id = #{record.toCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.transferLastTime != null">
        transfer_last_time = #{record.transferLastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteStatus != null">
        delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update voucher_transfer_last
    set id = #{record.id,jdbcType=VARCHAR},
      from_employee_id = #{record.fromEmployeeId,jdbcType=VARCHAR},
      from_company_id = #{record.fromCompanyId,jdbcType=VARCHAR},
      to_employee_id = #{record.toEmployeeId,jdbcType=VARCHAR},
      to_company_id = #{record.toCompanyId,jdbcType=VARCHAR},
      transfer_last_time = #{record.transferLastTime,jdbcType=TIMESTAMP},
      delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VoucherTransferLast">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update voucher_transfer_last
    <set>
      <if test="fromEmployeeId != null">
        from_employee_id = #{fromEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="fromCompanyId != null">
        from_company_id = #{fromCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="toEmployeeId != null">
        to_employee_id = #{toEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="toCompanyId != null">
        to_company_id = #{toCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="transferLastTime != null">
        transfer_last_time = #{transferLastTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VoucherTransferLast">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update voucher_transfer_last
    set from_employee_id = #{fromEmployeeId,jdbcType=VARCHAR},
      from_company_id = #{fromCompanyId,jdbcType=VARCHAR},
      to_employee_id = #{toEmployeeId,jdbcType=VARCHAR},
      to_company_id = #{toCompanyId,jdbcType=VARCHAR},
      transfer_last_time = #{transferLastTime,jdbcType=TIMESTAMP},
      delete_status = #{deleteStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>