<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountGrantRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountGrantRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="grant_record_id" jdbcType="VARCHAR" property="grantRecordId" />
    <result column="grant_amt" jdbcType="DECIMAL" property="grantAmt" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="grant_time" jdbcType="TIMESTAMP" property="grantTime" />
    <result column="customer_show" jdbcType="VARCHAR" property="customerShow" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="use_amt" jdbcType="DECIMAL" property="useAmt" />
    <result column="expire_amt" jdbcType="DECIMAL" property="expireAmt" />
    <result column="recall_amt" jdbcType="DECIMAL" property="recallAmt" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="common_json" jdbcType="VARCHAR" property="commonJson" />
    
    <result column="campaign_code" jdbcType="VARCHAR" property="campaignCode" />
  </resultMap>
  
  <select id="selectListToExpire" resultMap="BaseResultMap">
  	select * from person_account_grant_record 
  	<where>
  		<if test="expireTimeStart != null">
  			and expire_time <![CDATA[ > ]]> #{expireTimeStart}
  		</if>
  		
  		<if test="expireTimeEnd != null">
  			and expire_time <![CDATA[ < ]]> #{expireTimeEnd}
  		</if>
  		
  		<if test="employeeId != null">
  			and employee_id = #{employeeId}
  		</if>
  		
  		<if test="companyId != null">
  			and company_id = #{companyId}
  		</if>
  	</where>
  </select>
  
  <select id="selectListByExpireTime" resultMap="BaseResultMap">
  	select * from person_account_grant_record 
  	where status = 1 and balance <![CDATA[ > ]]> 0 and  expire_time <![CDATA[ <= ]]> #{expireTime}
  </select>
  
  <select id="selectByGrantRecordIdForUpdate" resultMap="BaseResultMap">
  	select * from person_account_grant_record 
  	where grant_record_id = #{grantRecordId}
  	for update
  </select>
  
</mapper>