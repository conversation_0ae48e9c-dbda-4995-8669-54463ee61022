<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountFlowExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow"
               extends="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountFlowMapper.BaseResultMap">
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, account_id, amount, grant_amount, account_type, order_type, business_type, order_id,
        company_id, balance, operator_id, operator_name, common_json, reason, create_time,grant_record_id
    </sql>
    <select id="selectPersonAccountFLowByExample"
            parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlowExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from person_account_flow
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null and offset != null">
            limit ${limit} offset ${offset}
        </if>
    </select>



    <!-- 批量插入生成员工账户分贝币发放流水 -->
    <insert id ="batchInsert" parameterType="java.util.List" >

        insert into person_account_flow (id, account_id, amount,grant_amount,business_type,order_id,company_id,reason,operator_id,operator_name,balance,common_json,create_time)
        values
        <foreach collection ="personAccountFlowList" item="personAccount" index= "index" separator =",">
            (
            #{personAccount.id},
            #{personAccount.accountId},
            #{personAccount.amount},
            #{personAccount.grantAmount},
            #{personAccount.businessType},
            #{personAccount.orderId},
            #{personAccount.companyId},
            #{personAccount.reason},
            #{personAccount.operatorId},
            #{personAccount.operatorName},
            #{personAccount.balance},
            #{personAccount.commonJson},
            #{personAccount.createTime}
            )
        </foreach>
    </insert>

    <select id="countByOrderIdAndActivityNo" resultType="java.lang.Long">
        select
        count(*)
        from person_account_flow
        <where>
            order_id = #{orderId}
            and activity_no = #{activityNo}
        </where>
    </select>
</mapper>