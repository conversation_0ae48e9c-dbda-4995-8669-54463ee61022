<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.config.PayCommonConfigMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.config.PayCommonConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="config_key" jdbcType="VARCHAR" property="configKey" />
    <result column="config_value" jdbcType="LONGVARCHAR" property="configValue" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, config_key, status, remark, create_time, update_time, config_value
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_pay_common_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <select id="selectByConfigKey" parameterType="string" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from tb_pay_common_config
    where config_key = #{configKey,jdbcType=VARCHAR}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from tb_pay_common_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.config.PayCommonConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tb_pay_common_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configKey != null">
        config_key,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="configValue != null">
        config_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configKey != null">
        #{configKey,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="configValue != null">
        #{configValue,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    
    on duplicate key update 
    <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
     
      <if test="configValue != null">
        config_value = #{configValue,jdbcType=LONGVARCHAR},
      </if>
      
      update_time = now()
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.config.PayCommonConfig">
    update tb_pay_common_config
    <set>
      <if test="configKey != null">
        config_key = #{configKey,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="configValue != null">
        config_value = #{configValue,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  
</mapper>