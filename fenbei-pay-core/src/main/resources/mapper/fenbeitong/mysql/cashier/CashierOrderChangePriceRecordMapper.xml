<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderChangePriceRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderChangePriceRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fb_request_no" jdbcType="VARCHAR" property="fbRequestNo" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="before_amount_all" jdbcType="DECIMAL" property="beforeAmountAll" />
    <result column="before_amount_personal" jdbcType="DECIMAL" property="beforeAmountPersonal" />
    <result column="before_amount_public" jdbcType="DECIMAL" property="beforeAmountPublic" />
    <result column="after_amount_all" jdbcType="DECIMAL" property="afterAmountAll" />
    <result column="after_amount_personal" jdbcType="DECIMAL" property="afterAmountPersonal" />
    <result column="after_amount_public" jdbcType="DECIMAL" property="afterAmountPublic" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, fb_request_no, fb_order_id, employee_id, company_id, before_amount_all, before_amount_personal, 
    before_amount_public, after_amount_all, after_amount_personal, after_amount_public, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderChangePriceRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cashier_order_change_price_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from cashier_order_change_price_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_change_price_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderChangePriceRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_change_price_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderChangePriceRecord" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_change_price_record (fb_request_no, fb_order_id, employee_id, 
      company_id, before_amount_all, before_amount_personal, 
      before_amount_public, after_amount_all, after_amount_personal, 
      after_amount_public, create_time, update_time
      )
    values (#{fbRequestNo,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=VARCHAR}, #{beforeAmountAll,jdbcType=DECIMAL}, #{beforeAmountPersonal,jdbcType=DECIMAL}, 
      #{beforeAmountPublic,jdbcType=DECIMAL}, #{afterAmountAll,jdbcType=DECIMAL}, #{afterAmountPersonal,jdbcType=DECIMAL}, 
      #{afterAmountPublic,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderChangePriceRecord" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_change_price_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fbRequestNo != null">
        fb_request_no,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="beforeAmountAll != null">
        before_amount_all,
      </if>
      <if test="beforeAmountPersonal != null">
        before_amount_personal,
      </if>
      <if test="beforeAmountPublic != null">
        before_amount_public,
      </if>
      <if test="afterAmountAll != null">
        after_amount_all,
      </if>
      <if test="afterAmountPersonal != null">
        after_amount_personal,
      </if>
      <if test="afterAmountPublic != null">
        after_amount_public,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fbRequestNo != null">
        #{fbRequestNo,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="beforeAmountAll != null">
        #{beforeAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="beforeAmountPersonal != null">
        #{beforeAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="beforeAmountPublic != null">
        #{beforeAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="afterAmountAll != null">
        #{afterAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="afterAmountPersonal != null">
        #{afterAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="afterAmountPublic != null">
        #{afterAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderChangePriceRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from cashier_order_change_price_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_change_price_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.fbRequestNo != null">
        fb_request_no = #{record.fbRequestNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.beforeAmountAll != null">
        before_amount_all = #{record.beforeAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="record.beforeAmountPersonal != null">
        before_amount_personal = #{record.beforeAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="record.beforeAmountPublic != null">
        before_amount_public = #{record.beforeAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="record.afterAmountAll != null">
        after_amount_all = #{record.afterAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="record.afterAmountPersonal != null">
        after_amount_personal = #{record.afterAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="record.afterAmountPublic != null">
        after_amount_public = #{record.afterAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_change_price_record
    set id = #{record.id,jdbcType=INTEGER},
      fb_request_no = #{record.fbRequestNo,jdbcType=VARCHAR},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      before_amount_all = #{record.beforeAmountAll,jdbcType=DECIMAL},
      before_amount_personal = #{record.beforeAmountPersonal,jdbcType=DECIMAL},
      before_amount_public = #{record.beforeAmountPublic,jdbcType=DECIMAL},
      after_amount_all = #{record.afterAmountAll,jdbcType=DECIMAL},
      after_amount_personal = #{record.afterAmountPersonal,jdbcType=DECIMAL},
      after_amount_public = #{record.afterAmountPublic,jdbcType=DECIMAL},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderChangePriceRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_change_price_record
    <set>
      <if test="fbRequestNo != null">
        fb_request_no = #{fbRequestNo,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="beforeAmountAll != null">
        before_amount_all = #{beforeAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="beforeAmountPersonal != null">
        before_amount_personal = #{beforeAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="beforeAmountPublic != null">
        before_amount_public = #{beforeAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="afterAmountAll != null">
        after_amount_all = #{afterAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="afterAmountPersonal != null">
        after_amount_personal = #{afterAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="afterAmountPublic != null">
        after_amount_public = #{afterAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderChangePriceRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_change_price_record
    set fb_request_no = #{fbRequestNo,jdbcType=VARCHAR},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      before_amount_all = #{beforeAmountAll,jdbcType=DECIMAL},
      before_amount_personal = #{beforeAmountPersonal,jdbcType=DECIMAL},
      before_amount_public = #{beforeAmountPublic,jdbcType=DECIMAL},
      after_amount_all = #{afterAmountAll,jdbcType=DECIMAL},
      after_amount_personal = #{afterAmountPersonal,jdbcType=DECIMAL},
      after_amount_public = #{afterAmountPublic,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>