<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.TbThirdPayBillTaskMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="tasks_id" jdbcType="VARCHAR" property="tasksId" />
    <result column="pay_account" jdbcType="VARCHAR" property="payAccount" />
    <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
    <result column="bill_time" jdbcType="VARCHAR" property="billTime" />
    <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
    <result column="task_retry_count" jdbcType="INTEGER" property="taskRetryCount" />
    <result column="task_retry_next_time" jdbcType="TIMESTAMP" property="taskRetryNextTime" />
    <result column="bill_file_origin_url" jdbcType="VARCHAR" property="billFileOriginUrl" />
    <result column="origin_file_type" jdbcType="VARCHAR" property="originFileType" />
    <result column="bill_file_download_url" jdbcType="VARCHAR" property="billFileDownloadUrl" />
    <result column="download_file_type" jdbcType="VARCHAR" property="downloadFileType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tasks_id, pay_account, pay_channel, bill_time, task_status, task_retry_count, 
    task_retry_next_time, bill_file_origin_url, origin_file_type, bill_file_download_url, 
    download_file_type, remark, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTaskExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_third_pay_bill_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tb_third_pay_bill_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tb_third_pay_bill_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTaskExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tb_third_pay_bill_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTask" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tb_third_pay_bill_task (id,tasks_id, pay_account, pay_channel,
      bill_time, task_status, task_retry_count, 
      task_retry_next_time, bill_file_origin_url, 
      origin_file_type, bill_file_download_url, download_file_type, 
      remark, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT},#{tasksId,jdbcType=VARCHAR}, #{payAccount,jdbcType=VARCHAR}, #{payChannel,jdbcType=VARCHAR},
      #{billTime,jdbcType=VARCHAR}, #{taskStatus,jdbcType=INTEGER}, #{taskRetryCount,jdbcType=INTEGER}, 
      #{taskRetryNextTime,jdbcType=TIMESTAMP}, #{billFileOriginUrl,jdbcType=VARCHAR}, 
      #{originFileType,jdbcType=VARCHAR}, #{billFileDownloadUrl,jdbcType=VARCHAR}, #{downloadFileType,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTask" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tb_third_pay_bill_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tasksId != null">
        tasks_id,
      </if>
      <if test="payAccount != null">
        pay_account,
      </if>
      <if test="payChannel != null">
        pay_channel,
      </if>
      <if test="billTime != null">
        bill_time,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="taskRetryCount != null">
        task_retry_count,
      </if>
      <if test="taskRetryNextTime != null">
        task_retry_next_time,
      </if>
      <if test="billFileOriginUrl != null">
        bill_file_origin_url,
      </if>
      <if test="originFileType != null">
        origin_file_type,
      </if>
      <if test="billFileDownloadUrl != null">
        bill_file_download_url,
      </if>
      <if test="downloadFileType != null">
        download_file_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tasksId != null">
        #{tasksId,jdbcType=VARCHAR},
      </if>
      <if test="payAccount != null">
        #{payAccount,jdbcType=VARCHAR},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=VARCHAR},
      </if>
      <if test="billTime != null">
        #{billTime,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="taskRetryCount != null">
        #{taskRetryCount,jdbcType=INTEGER},
      </if>
      <if test="taskRetryNextTime != null">
        #{taskRetryNextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billFileOriginUrl != null">
        #{billFileOriginUrl,jdbcType=VARCHAR},
      </if>
      <if test="originFileType != null">
        #{originFileType,jdbcType=VARCHAR},
      </if>
      <if test="billFileDownloadUrl != null">
        #{billFileDownloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="downloadFileType != null">
        #{downloadFileType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTaskExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tb_third_pay_bill_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tb_third_pay_bill_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tasksId != null">
        tasks_id = #{record.tasksId,jdbcType=VARCHAR},
      </if>
      <if test="record.payAccount != null">
        pay_account = #{record.payAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.payChannel != null">
        pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.billTime != null">
        bill_time = #{record.billTime,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.taskRetryCount != null">
        task_retry_count = #{record.taskRetryCount,jdbcType=INTEGER},
      </if>
      <if test="record.taskRetryNextTime != null">
        task_retry_next_time = #{record.taskRetryNextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.billFileOriginUrl != null">
        bill_file_origin_url = #{record.billFileOriginUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.originFileType != null">
        origin_file_type = #{record.originFileType,jdbcType=VARCHAR},
      </if>
      <if test="record.billFileDownloadUrl != null">
        bill_file_download_url = #{record.billFileDownloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadFileType != null">
        download_file_type = #{record.downloadFileType,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tb_third_pay_bill_task
    set id = #{record.id,jdbcType=BIGINT},
      tasks_id = #{record.tasksId,jdbcType=VARCHAR},
      pay_account = #{record.payAccount,jdbcType=VARCHAR},
      pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      bill_time = #{record.billTime,jdbcType=VARCHAR},
      task_status = #{record.taskStatus,jdbcType=INTEGER},
      task_retry_count = #{record.taskRetryCount,jdbcType=INTEGER},
      task_retry_next_time = #{record.taskRetryNextTime,jdbcType=TIMESTAMP},
      bill_file_origin_url = #{record.billFileOriginUrl,jdbcType=VARCHAR},
      origin_file_type = #{record.originFileType,jdbcType=VARCHAR},
      bill_file_download_url = #{record.billFileDownloadUrl,jdbcType=VARCHAR},
      download_file_type = #{record.downloadFileType,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tb_third_pay_bill_task
    <set>
      <if test="tasksId != null">
        tasks_id = #{tasksId,jdbcType=VARCHAR},
      </if>
      <if test="payAccount != null">
        pay_account = #{payAccount,jdbcType=VARCHAR},
      </if>
      <if test="payChannel != null">
        pay_channel = #{payChannel,jdbcType=VARCHAR},
      </if>
      <if test="billTime != null">
        bill_time = #{billTime,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="taskRetryCount != null">
        task_retry_count = #{taskRetryCount,jdbcType=INTEGER},
      </if>
      <if test="taskRetryNextTime != null">
        task_retry_next_time = #{taskRetryNextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billFileOriginUrl != null">
        bill_file_origin_url = #{billFileOriginUrl,jdbcType=VARCHAR},
      </if>
      <if test="originFileType != null">
        origin_file_type = #{originFileType,jdbcType=VARCHAR},
      </if>
      <if test="billFileDownloadUrl != null">
        bill_file_download_url = #{billFileDownloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="downloadFileType != null">
        download_file_type = #{downloadFileType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.TbThirdPayBillTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tb_third_pay_bill_task
    set tasks_id = #{tasksId,jdbcType=VARCHAR},
      pay_account = #{payAccount,jdbcType=VARCHAR},
      pay_channel = #{payChannel,jdbcType=VARCHAR},
      bill_time = #{billTime,jdbcType=VARCHAR},
      task_status = #{taskStatus,jdbcType=INTEGER},
      task_retry_count = #{taskRetryCount,jdbcType=INTEGER},
      task_retry_next_time = #{taskRetryNextTime,jdbcType=TIMESTAMP},
      bill_file_origin_url = #{billFileOriginUrl,jdbcType=VARCHAR},
      origin_file_type = #{originFileType,jdbcType=VARCHAR},
      bill_file_download_url = #{billFileDownloadUrl,jdbcType=VARCHAR},
      download_file_type = #{downloadFileType,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>