<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderSettlementBatchPreinfoListMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="sub_order_id" jdbcType="VARCHAR" property="subOrderId" />
    <result column="sub_order_status" jdbcType="INTEGER" property="subOrderStatus" />
    <result column="sub_order_pay_amount" jdbcType="DECIMAL" property="subOrderPayAmount" />
    <result column="currency_type" jdbcType="VARCHAR" property="currencyType" />
    <result column="payer_account_type" jdbcType="INTEGER" property="payerAccountType" />
    <result column="payer_account_name" jdbcType="VARCHAR" property="payerAccountName" />
    <result column="payer_account_id" jdbcType="VARCHAR" property="payerAccountId" />
    <result column="payer_bank_account_no" jdbcType="VARCHAR" property="payerBankAccountNo" />
    <result column="payer_bank_name" jdbcType="VARCHAR" property="payerBankName" />
    <result column="payer_bank_code" jdbcType="VARCHAR" property="payerBankCode" />
    <result column="receiver_bank_account_no" jdbcType="VARCHAR" property="receiverBankAccountNo" />
    <result column="receiver_bank_account_name" jdbcType="VARCHAR" property="receiverBankAccountName" />
    <result column="receiver_branch_bank_code" jdbcType="VARCHAR" property="receiverBranchBankCode" />
    <result column="receiver_branch_bank_name" jdbcType="VARCHAR" property="receiverBranchBankName" />
    <result column="receiver_bank_name" jdbcType="VARCHAR" property="receiverBankName" />
    <result column="receiver_employee_no" jdbcType="VARCHAR" property="receiverEmployeeNo" />
    <result column="receiver_employee_id" jdbcType="VARCHAR" property="receiverEmployeeId" />
    <result column="receiver_employee_idcard" jdbcType="VARCHAR" property="receiverEmployeeIdcard"/>
    <result column="receiver_employee_idcard_type" jdbcType="VARCHAR" property="receiverEmployeeIdcardType"/>
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="bank_trans_time" jdbcType="TIMESTAMP" property="bankTransTime" />
    <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo" />
    <result column="sync_bank_trans_no" jdbcType="VARCHAR" property="syncBankTransNo"/>
    <result column="pay_user_id" jdbcType="VARCHAR" property="payUserId" />
    <result column="pay_start_time" jdbcType="TIMESTAMP" property="payStartTime" />
    <result column="pay_end_time" jdbcType="TIMESTAMP" property="payEndTime" />
    <result column="fail_desc" jdbcType="VARCHAR" property="failDesc" />
    <result column="payment_purpose" jdbcType="VARCHAR" property="paymentPurpose"/>
    <result column="payment_remark" jdbcType="VARCHAR" property="paymentRemark"/>
    <result column="bank_check_code" jdbcType="VARCHAR" property="bankCheckCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, fb_order_id, order_type, company_id, batch_no, sub_order_id, sub_order_status, 
    sub_order_pay_amount, currency_type, payer_account_type, payer_account_name, payer_account_id, 
    payer_bank_account_no, payer_bank_name, payer_bank_code, receiver_bank_account_no, 
    receiver_bank_account_name, receiver_branch_bank_code, receiver_branch_bank_name, 
    receiver_bank_name, receiver_employee_no, receiver_employee_id, receiver_employee_idcard, receiver_employee_idcard_type, receiver_phone, bank_trans_time,
    bank_trans_no,sync_bank_trans_no, pay_user_id, pay_start_time, pay_end_time, fail_desc, payment_purpose,payment_remark,bank_check_code,
    create_time,update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoListExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cashier_order_settlement_batch_preinfo_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoListExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_settlement_batch_preinfo_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_settlement_batch_preinfo_list (id, fb_order_id, order_type, 
      company_id, batch_no, sub_order_id, 
      sub_order_status, sub_order_pay_amount, currency_type, 
      payer_account_type, payer_account_name, payer_account_id, 
      payer_bank_account_no, payer_bank_name, payer_bank_code, 
      receiver_bank_account_no, receiver_bank_account_name, 
      receiver_branch_bank_code, receiver_branch_bank_name, 
      receiver_bank_name, receiver_employee_no, receiver_employee_id, receiver_employee_idcard,receiver_employee_idcard_type,
      receiver_phone, bank_trans_time, bank_trans_no,sync_bank_trans_no,
      pay_user_id, pay_start_time, pay_end_time, 
      fail_desc,payment_purpose,payment_remark,bank_check_code,
      create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{fbOrderId,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{companyId,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{subOrderId,jdbcType=VARCHAR}, 
      #{subOrderStatus,jdbcType=INTEGER}, #{subOrderPayAmount,jdbcType=DECIMAL}, #{currencyType,jdbcType=VARCHAR}, 
      #{payerAccountType,jdbcType=INTEGER}, #{payerAccountName,jdbcType=VARCHAR}, #{payerAccountId,jdbcType=VARCHAR}, 
      #{payerBankAccountNo,jdbcType=VARCHAR}, #{payerBankName,jdbcType=VARCHAR}, #{payerBankCode,jdbcType=VARCHAR}, 
      #{receiverBankAccountNo,jdbcType=VARCHAR}, #{receiverBankAccountName,jdbcType=VARCHAR}, 
      #{receiverBranchBankCode,jdbcType=VARCHAR}, #{receiverBranchBankName,jdbcType=VARCHAR}, 
      #{receiverBankName,jdbcType=VARCHAR}, #{receiverEmployeeNo,jdbcType=VARCHAR}, #{receiverEmployeeId,jdbcType=VARCHAR}, #{receiverEmployeeIdcard,jdbcType=VARCHAR}, #{receiverEmployeeIdcardType,jdbcType=VARCHAR},
      #{receiverPhone,jdbcType=VARCHAR}, #{bankTransTime,jdbcType=TIMESTAMP}, #{bankTransNo,jdbcType=VARCHAR}, #{syncBankTransNo,jdbcType=VARCHAR},
      #{payUserId,jdbcType=VARCHAR}, #{payStartTime,jdbcType=TIMESTAMP}, #{payEndTime,jdbcType=TIMESTAMP}, 
      #{failDesc,jdbcType=VARCHAR},#{paymentPurpose,jdbcType=VARCHAR},#{paymentRemark,jdbcType=VARCHAR},#{bankCheckCode,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_settlement_batch_preinfo_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="subOrderId != null">
        sub_order_id,
      </if>
      <if test="subOrderStatus != null">
        sub_order_status,
      </if>
      <if test="subOrderPayAmount != null">
        sub_order_pay_amount,
      </if>
      <if test="currencyType != null">
        currency_type,
      </if>
      <if test="payerAccountType != null">
        payer_account_type,
      </if>
      <if test="payerAccountName != null">
        payer_account_name,
      </if>
      <if test="payerAccountId != null">
        payer_account_id,
      </if>
      <if test="payerBankAccountNo != null">
        payer_bank_account_no,
      </if>
      <if test="payerBankName != null">
        payer_bank_name,
      </if>
      <if test="payerBankCode != null">
        payer_bank_code,
      </if>
      <if test="receiverBankAccountNo != null">
        receiver_bank_account_no,
      </if>
      <if test="receiverBankAccountName != null">
        receiver_bank_account_name,
      </if>
      <if test="receiverBranchBankCode != null">
        receiver_branch_bank_code,
      </if>
      <if test="receiverBranchBankName != null">
        receiver_branch_bank_name,
      </if>
      <if test="receiverBankName != null">
        receiver_bank_name,
      </if>
      <if test="receiverEmployeeNo != null">
        receiver_employee_no,
      </if>
      <if test="receiverEmployeeId != null">
        receiver_employee_id,
      </if>
      <if test="receiverPhone != null">
        receiver_phone,
      </if>
      <if test="bankTransTime != null">
        bank_trans_time,
      </if>
      <if test="bankTransNo != null">
        bank_trans_no,
      </if>
      <if test="syncBankTransNo != null">
        sync_bank_trans_no,
      </if>
      <if test="payUserId != null">
        pay_user_id,
      </if>
      <if test="payStartTime != null">
        pay_start_time,
      </if>
      <if test="payEndTime != null">
        pay_end_time,
      </if>
      <if test="failDesc != null">
        fail_desc,
      </if>
      <if test="paymentPurpose != null">
        payment_purpose,
      </if>
      <if test="paymentRemark != null">
        payment_remark,
      </if>
      <if test="bankCheckCode != null">
        bank_check_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="subOrderId != null">
        #{subOrderId,jdbcType=VARCHAR},
      </if>
      <if test="subOrderStatus != null">
        #{subOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="subOrderPayAmount != null">
        #{subOrderPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="currencyType != null">
        #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="payerAccountType != null">
        #{payerAccountType,jdbcType=INTEGER},
      </if>
      <if test="payerAccountName != null">
        #{payerAccountName,jdbcType=VARCHAR},
      </if>
      <if test="payerAccountId != null">
        #{payerAccountId,jdbcType=VARCHAR},
      </if>
      <if test="payerBankAccountNo != null">
        #{payerBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="payerBankName != null">
        #{payerBankName,jdbcType=VARCHAR},
      </if>
      <if test="payerBankCode != null">
        #{payerBankCode,jdbcType=VARCHAR},
      </if>
      <if test="receiverBankAccountNo != null">
        #{receiverBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="receiverBankAccountName != null">
        #{receiverBankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="receiverBranchBankCode != null">
        #{receiverBranchBankCode,jdbcType=VARCHAR},
      </if>
      <if test="receiverBranchBankName != null">
        #{receiverBranchBankName,jdbcType=VARCHAR},
      </if>
      <if test="receiverBankName != null">
        #{receiverBankName,jdbcType=VARCHAR},
      </if>
      <if test="receiverEmployeeNo != null">
        #{receiverEmployeeNo,jdbcType=VARCHAR},
      </if>
      <if test="receiverEmployeeId != null">
        #{receiverEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="receiverPhone != null">
        #{receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="bankTransTime != null">
        #{bankTransTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankTransNo != null">
        #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="payUserId != null">
        #{payUserId,jdbcType=VARCHAR},
      </if>
      <if test="payStartTime != null">
        #{payStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payEndTime != null">
        #{payEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="failDesc != null">
        #{failDesc,jdbcType=VARCHAR},
      </if>
      <if test="paymentPurpose != null">
        #{paymentPurpose,jdbcType=VARCHAR},
      </if>
      <if test="paymentRemark != null">
        #{paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="bankCheckCode != null">
        #{bankCheckCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoListExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from cashier_order_settlement_batch_preinfo_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement_batch_preinfo_list
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.subOrderId != null">
        sub_order_id = #{record.subOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.subOrderStatus != null">
        sub_order_status = #{record.subOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.subOrderPayAmount != null">
        sub_order_pay_amount = #{record.subOrderPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.currencyType != null">
        currency_type = #{record.currencyType,jdbcType=VARCHAR},
      </if>
      <if test="record.payerAccountType != null">
        payer_account_type = #{record.payerAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.payerAccountName != null">
        payer_account_name = #{record.payerAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.payerAccountId != null">
        payer_account_id = #{record.payerAccountId,jdbcType=VARCHAR},
      </if>
      <if test="record.payerBankAccountNo != null">
        payer_bank_account_no = #{record.payerBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payerBankName != null">
        payer_bank_name = #{record.payerBankName,jdbcType=VARCHAR},
      </if>
      <if test="record.payerBankCode != null">
        payer_bank_code = #{record.payerBankCode,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverBankAccountNo != null">
        receiver_bank_account_no = #{record.receiverBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverBankAccountName != null">
        receiver_bank_account_name = #{record.receiverBankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverBranchBankCode != null">
        receiver_branch_bank_code = #{record.receiverBranchBankCode,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverBranchBankName != null">
        receiver_branch_bank_name = #{record.receiverBranchBankName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverBankName != null">
        receiver_bank_name = #{record.receiverBankName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverEmployeeNo != null">
        receiver_employee_no = #{record.receiverEmployeeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverEmployeeId != null">
        receiver_employee_id = #{record.receiverEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverEmployeeIdcard != null">
        receiver_employee_id = #{record.receiverEmployeeIdcard,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverPhone != null">
        receiver_phone = #{record.receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.bankTransTime != null">
        bank_trans_time = #{record.bankTransTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bankTransNo != null">
        bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="record.syncBankTransNo != null">
        sync_bank_trans_no = #{record.syncBankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payUserId != null">
        pay_user_id = #{record.payUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.payStartTime != null">
        pay_start_time = #{record.payStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payEndTime != null">
        pay_end_time = #{record.payEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.failDesc != null">
        fail_desc = #{record.failDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentPurpose != null">
        payment_purpose = #{record.paymentPurpose,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentRemark != null">
        payment_remark = #{record.paymentRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.bankCheckCode != null">
        bank_check_code = #{record.bankCheckCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement_batch_preinfo_list
    set id = #{record.id,jdbcType=BIGINT},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      sub_order_id = #{record.subOrderId,jdbcType=VARCHAR},
      sub_order_status = #{record.subOrderStatus,jdbcType=INTEGER},
      sub_order_pay_amount = #{record.subOrderPayAmount,jdbcType=DECIMAL},
      currency_type = #{record.currencyType,jdbcType=VARCHAR},
      payer_account_type = #{record.payerAccountType,jdbcType=INTEGER},
      payer_account_name = #{record.payerAccountName,jdbcType=VARCHAR},
      payer_account_id = #{record.payerAccountId,jdbcType=VARCHAR},
      payer_bank_account_no = #{record.payerBankAccountNo,jdbcType=VARCHAR},
      payer_bank_name = #{record.payerBankName,jdbcType=VARCHAR},
      payer_bank_code = #{record.payerBankCode,jdbcType=VARCHAR},
      receiver_bank_account_no = #{record.receiverBankAccountNo,jdbcType=VARCHAR},
      receiver_bank_account_name = #{record.receiverBankAccountName,jdbcType=VARCHAR},
      receiver_branch_bank_code = #{record.receiverBranchBankCode,jdbcType=VARCHAR},
      receiver_branch_bank_name = #{record.receiverBranchBankName,jdbcType=VARCHAR},
      receiver_bank_name = #{record.receiverBankName,jdbcType=VARCHAR},
      receiver_employee_no = #{record.receiverEmployeeNo,jdbcType=VARCHAR},
      receiver_employee_id = #{record.receiverEmployeeId,jdbcType=VARCHAR},
      receiver_employee_idcard = #{record.receiverEmployeeIdcard,jdbcType=VARCHAR},
    receiver_employee_idcard_type = #{record.receiverEmployeeIdcardType,jdbcType=VARCHAR},
      receiver_phone = #{record.receiverPhone,jdbcType=VARCHAR},
      bank_trans_time = #{record.bankTransTime,jdbcType=TIMESTAMP},
      bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      sync_bank_trans_no = #{record.syncBankTransNo,jdbcType=VARCHAR},
      pay_user_id = #{record.payUserId,jdbcType=VARCHAR},
      pay_start_time = #{record.payStartTime,jdbcType=TIMESTAMP},
      pay_end_time = #{record.payEndTime,jdbcType=TIMESTAMP},
      fail_desc = #{record.failDesc,jdbcType=VARCHAR},
      payment_purpose = #{record.paymentPurpose,jdbcType=VARCHAR},
      payment_remark = #{record.paymentRemark,jdbcType=VARCHAR},
      bank_check_code = #{record.bankCheckCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>