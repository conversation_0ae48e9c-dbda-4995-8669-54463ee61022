<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderCostAttributionMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="cost_attribution_id" jdbcType="VARCHAR" property="costAttributionId" />
    <result column="cost_attribution_type" jdbcType="SMALLINT" property="costAttributionType" />
    <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName" />
    <result column="p_cost_attribution" jdbcType="VARCHAR" property="pCostAttribution" />
    <result column="custom_ext" jdbcType="VARCHAR" property="customExt" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="cost_attribution_path" jdbcType="VARCHAR" property="costAttributionPath" />
    <result column="cost_attribution_opt" jdbcType="INTEGER" property="costAttributionOpt" />
    <result column="budget_opt" jdbcType="INTEGER" property="budgetOpt" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="system_ext" jdbcType="VARCHAR" property="systemExt" />
    <result column="use_personal_budget" jdbcType="INTEGER" property="usePersonalBudget" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, fb_order_id, cost_attribution_id, cost_attribution_type, cost_attribution_name, 
    p_cost_attribution, custom_ext, create_time, cost_attribution_path, cost_attribution_opt, 
    budget_opt, company_id, employee_id, order_type, system_ext, use_personal_budget
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttributionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cashier_order_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from cashier_order_cost_attribution
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_cost_attribution
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttributionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_cost_attribution (id, fb_order_id, cost_attribution_id, 
      cost_attribution_type, cost_attribution_name, 
      p_cost_attribution, custom_ext, create_time, 
      cost_attribution_path, cost_attribution_opt, 
      budget_opt, company_id, employee_id, 
      order_type, system_ext, use_personal_budget
      )
    values (#{id,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{costAttributionId,jdbcType=VARCHAR}, 
      #{costAttributionType,jdbcType=SMALLINT}, #{costAttributionName,jdbcType=VARCHAR}, 
      #{pCostAttribution,jdbcType=VARCHAR}, #{customExt,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{costAttributionPath,jdbcType=VARCHAR}, #{costAttributionOpt,jdbcType=INTEGER}, 
      #{budgetOpt,jdbcType=INTEGER}, #{companyId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, 
      #{orderType,jdbcType=INTEGER}, #{systemExt,jdbcType=VARCHAR}, #{usePersonalBudget,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_cost_attribution
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id,
      </if>
      <if test="costAttributionType != null">
        cost_attribution_type,
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name,
      </if>
      <if test="pCostAttribution != null">
        p_cost_attribution,
      </if>
      <if test="customExt != null">
        custom_ext,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="costAttributionPath != null">
        cost_attribution_path,
      </if>
      <if test="costAttributionOpt != null">
        cost_attribution_opt,
      </if>
      <if test="budgetOpt != null">
        budget_opt,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="systemExt != null">
        system_ext,
      </if>
      <if test="usePersonalBudget != null">
        use_personal_budget,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionId != null">
        #{costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionType != null">
        #{costAttributionType,jdbcType=SMALLINT},
      </if>
      <if test="costAttributionName != null">
        #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="pCostAttribution != null">
        #{pCostAttribution,jdbcType=VARCHAR},
      </if>
      <if test="customExt != null">
        #{customExt,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="costAttributionPath != null">
        #{costAttributionPath,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionOpt != null">
        #{costAttributionOpt,jdbcType=INTEGER},
      </if>
      <if test="budgetOpt != null">
        #{budgetOpt,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="systemExt != null">
        #{systemExt,jdbcType=VARCHAR},
      </if>
      <if test="usePersonalBudget != null">
        #{usePersonalBudget,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttributionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from cashier_order_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_cost_attribution
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionId != null">
        cost_attribution_id = #{record.costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionType != null">
        cost_attribution_type = #{record.costAttributionType,jdbcType=SMALLINT},
      </if>
      <if test="record.costAttributionName != null">
        cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="record.pCostAttribution != null">
        p_cost_attribution = #{record.pCostAttribution,jdbcType=VARCHAR},
      </if>
      <if test="record.customExt != null">
        custom_ext = #{record.customExt,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.costAttributionPath != null">
        cost_attribution_path = #{record.costAttributionPath,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionOpt != null">
        cost_attribution_opt = #{record.costAttributionOpt,jdbcType=INTEGER},
      </if>
      <if test="record.budgetOpt != null">
        budget_opt = #{record.budgetOpt,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.systemExt != null">
        system_ext = #{record.systemExt,jdbcType=VARCHAR},
      </if>
      <if test="record.usePersonalBudget != null">
        use_personal_budget = #{record.usePersonalBudget,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_cost_attribution
    set id = #{record.id,jdbcType=VARCHAR},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      cost_attribution_id = #{record.costAttributionId,jdbcType=VARCHAR},
      cost_attribution_type = #{record.costAttributionType,jdbcType=SMALLINT},
      cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      p_cost_attribution = #{record.pCostAttribution,jdbcType=VARCHAR},
      custom_ext = #{record.customExt,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      cost_attribution_path = #{record.costAttributionPath,jdbcType=VARCHAR},
      cost_attribution_opt = #{record.costAttributionOpt,jdbcType=INTEGER},
      budget_opt = #{record.budgetOpt,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      system_ext = #{record.systemExt,jdbcType=VARCHAR},
      use_personal_budget = #{record.usePersonalBudget,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_cost_attribution
    <set>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionType != null">
        cost_attribution_type = #{costAttributionType,jdbcType=SMALLINT},
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="pCostAttribution != null">
        p_cost_attribution = #{pCostAttribution,jdbcType=VARCHAR},
      </if>
      <if test="customExt != null">
        custom_ext = #{customExt,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="costAttributionPath != null">
        cost_attribution_path = #{costAttributionPath,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionOpt != null">
        cost_attribution_opt = #{costAttributionOpt,jdbcType=INTEGER},
      </if>
      <if test="budgetOpt != null">
        budget_opt = #{budgetOpt,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="systemExt != null">
        system_ext = #{systemExt,jdbcType=VARCHAR},
      </if>
      <if test="usePersonalBudget != null">
        use_personal_budget = #{usePersonalBudget,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_cost_attribution
    set fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR},
      cost_attribution_type = #{costAttributionType,jdbcType=SMALLINT},
      cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      p_cost_attribution = #{pCostAttribution,jdbcType=VARCHAR},
      custom_ext = #{customExt,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      cost_attribution_path = #{costAttributionPath,jdbcType=VARCHAR},
      cost_attribution_opt = #{costAttributionOpt,jdbcType=INTEGER},
      budget_opt = #{budgetOpt,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      system_ext = #{systemExt,jdbcType=VARCHAR},
      use_personal_budget = #{usePersonalBudget,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>