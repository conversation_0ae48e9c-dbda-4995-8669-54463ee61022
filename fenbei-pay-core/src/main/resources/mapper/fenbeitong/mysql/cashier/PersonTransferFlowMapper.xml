<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.PersonTransferFlowMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="target_id" jdbcType="VARCHAR" property="targetId" />
    <result column="transfer_amount" jdbcType="DECIMAL" property="transferAmount" />
    <result column="from_employee_id" jdbcType="VARCHAR" property="fromEmployeeId" />
    <result column="from_employee_name" jdbcType="VARCHAR" property="fromEmployeeName" />
    <result column="from_company_id" jdbcType="VARCHAR" property="fromCompanyId" />
    <result column="to_employee_id" jdbcType="VARCHAR" property="toEmployeeId" />
    <result column="to_employee_name" jdbcType="VARCHAR" property="toEmployeeName" />
    <result column="to_company_id" jdbcType="VARCHAR" property="toCompanyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="open_enable" jdbcType="TINYINT" property="openEnable" />
    <result column="background_url" jdbcType="VARCHAR" property="backgroundUrl" />
    <result column="background_name" jdbcType="VARBINARY" property="backgroundName" />
    <result column="notice_content" jdbcType="VARCHAR" property="noticeContent" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, source_id, target_id, transfer_amount, from_employee_id, from_employee_name, 
    from_company_id, to_employee_id, to_employee_name, to_company_id, create_time, batch_id,
    open_enable, background_url, background_name,notice_content, is_ding
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlowExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from person_transfer_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from person_transfer_flow
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_transfer_flow
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlowExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_transfer_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_transfer_flow (id, source_id, target_id, 
      transfer_amount, from_employee_id, from_employee_name, 
      from_company_id, to_employee_id, to_employee_name, 
      to_company_id, create_time, batch_id, 
      is_ding)
    values (#{id,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, #{targetId,jdbcType=VARCHAR}, 
      #{transferAmount,jdbcType=DECIMAL}, #{fromEmployeeId,jdbcType=VARCHAR}, #{fromEmployeeName,jdbcType=VARCHAR}, 
      #{fromCompanyId,jdbcType=VARCHAR}, #{toEmployeeId,jdbcType=VARCHAR}, #{toEmployeeName,jdbcType=VARCHAR}, 
      #{toCompanyId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{batchId,jdbcType=VARCHAR}, 
      #{isDing,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_transfer_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="transferAmount != null">
        transfer_amount,
      </if>
      <if test="fromEmployeeId != null">
        from_employee_id,
      </if>
      <if test="fromEmployeeName != null">
        from_employee_name,
      </if>
      <if test="fromCompanyId != null">
        from_company_id,
      </if>
      <if test="toEmployeeId != null">
        to_employee_id,
      </if>
      <if test="toEmployeeName != null">
        to_employee_name,
      </if>
      <if test="toCompanyId != null">
        to_company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="openEnable != null">
        open_enable,
      </if>
      <if test="backgroundUrl != null">
        background_url,
      </if>
      <if test="backgroundName != null">
        background_name,
      </if>
      <if test="noticeContent != null">
        notice_content,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="transferAmount != null">
        #{transferAmount,jdbcType=DECIMAL},
      </if>
      <if test="fromEmployeeId != null">
        #{fromEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="fromEmployeeName != null">
        #{fromEmployeeName,jdbcType=VARCHAR},
      </if>
      <if test="fromCompanyId != null">
        #{fromCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="toEmployeeId != null">
        #{toEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="toEmployeeName != null">
        #{toEmployeeName,jdbcType=VARCHAR},
      </if>
      <if test="toCompanyId != null">
        #{toCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="openEnable != null">
        #{openEnable,jdbcType=INTEGER},
      </if>
      <if test="backgroundUrl != null">
        #{backgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="backgroundName != null">
        #{backgroundName,jdbcType=VARCHAR},
      </if>
      <if test="noticeContent != null">
        #{noticeContent,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlowExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from person_transfer_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_transfer_flow
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.targetId != null">
        target_id = #{record.targetId,jdbcType=VARCHAR},
      </if>
      <if test="record.transferAmount != null">
        transfer_amount = #{record.transferAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.fromEmployeeId != null">
        from_employee_id = #{record.fromEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.fromEmployeeName != null">
        from_employee_name = #{record.fromEmployeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.fromCompanyId != null">
        from_company_id = #{record.fromCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.toEmployeeId != null">
        to_employee_id = #{record.toEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.toEmployeeName != null">
        to_employee_name = #{record.toEmployeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.toCompanyId != null">
        to_company_id = #{record.toCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=VARCHAR},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_transfer_flow
    set id = #{record.id,jdbcType=VARCHAR},
      source_id = #{record.sourceId,jdbcType=VARCHAR},
      target_id = #{record.targetId,jdbcType=VARCHAR},
      transfer_amount = #{record.transferAmount,jdbcType=DECIMAL},
      from_employee_id = #{record.fromEmployeeId,jdbcType=VARCHAR},
      from_employee_name = #{record.fromEmployeeName,jdbcType=VARCHAR},
      from_company_id = #{record.fromCompanyId,jdbcType=VARCHAR},
      to_employee_id = #{record.toEmployeeId,jdbcType=VARCHAR},
      to_employee_name = #{record.toEmployeeName,jdbcType=VARCHAR},
      to_company_id = #{record.toCompanyId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      batch_id = #{record.batchId,jdbcType=VARCHAR},
      is_ding = #{record.isDing,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_transfer_flow
    <set>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="transferAmount != null">
        transfer_amount = #{transferAmount,jdbcType=DECIMAL},
      </if>
      <if test="fromEmployeeId != null">
        from_employee_id = #{fromEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="fromEmployeeName != null">
        from_employee_name = #{fromEmployeeName,jdbcType=VARCHAR},
      </if>
      <if test="fromCompanyId != null">
        from_company_id = #{fromCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="toEmployeeId != null">
        to_employee_id = #{toEmployeeId,jdbcType=VARCHAR},
      </if>
      <if test="toEmployeeName != null">
        to_employee_name = #{toEmployeeName,jdbcType=VARCHAR},
      </if>
      <if test="toCompanyId != null">
        to_company_id = #{toCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_transfer_flow
    set source_id = #{sourceId,jdbcType=VARCHAR},
      target_id = #{targetId,jdbcType=VARCHAR},
      transfer_amount = #{transferAmount,jdbcType=DECIMAL},
      from_employee_id = #{fromEmployeeId,jdbcType=VARCHAR},
      from_employee_name = #{fromEmployeeName,jdbcType=VARCHAR},
      from_company_id = #{fromCompanyId,jdbcType=VARCHAR},
      to_employee_id = #{toEmployeeId,jdbcType=VARCHAR},
      to_employee_name = #{toEmployeeName,jdbcType=VARCHAR},
      to_company_id = #{toCompanyId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      batch_id = #{batchId,jdbcType=VARCHAR},
      is_ding = #{isDing,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>