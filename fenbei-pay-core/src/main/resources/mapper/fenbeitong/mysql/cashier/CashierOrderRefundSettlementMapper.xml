<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderRefundSettlementMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="refund_status" jdbcType="SMALLINT" property="refundStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="fb_trade_id" jdbcType="VARCHAR" property="fbTradeId" />
    <result column="root_order_id" jdbcType="VARCHAR" property="rootOrderId" />
    <result column="client_ip" jdbcType="VARCHAR" property="clientIp" />
    <result column="device" jdbcType="VARCHAR" property="device" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="refund_amount_all" jdbcType="DECIMAL" property="refundAmountAll" />
    <result column="refund_amount_personal" jdbcType="DECIMAL" property="refundAmountPersonal" />
    <result column="refund_amount_public" jdbcType="DECIMAL" property="refundAmountPublic" />
    <result column="refund_amount_company" jdbcType="DECIMAL" property="refundAmountCompany" />
    <result column="refund_amount_redcoupon" jdbcType="DECIMAL" property="refundAmountRedcoupon" />
    <result column="refund_amount_third" jdbcType="DECIMAL" property="refundAmountThird" />
    <result column="refund_amount_fbb" jdbcType="DECIMAL" property="refundAmountFbb" />
    <result column="refund_amount_voucher" jdbcType="DECIMAL" property="refundAmountVoucher" />
    <result column="refund_amount_bank" jdbcType="DECIMAL" property="refundAmountBank" />
    <result column="account_type" jdbcType="SMALLINT" property="accountType" />
    <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="cashier_txn_id" jdbcType="VARCHAR" property="cashierTxnId" />
    <result column="fb_order_name" jdbcType="VARCHAR" property="fbOrderName" />
    <result column="refund_txn_id" jdbcType="VARCHAR" property="refundTxnId" />
    <result column="biz_notify_url" jdbcType="VARCHAR" property="bizNotifyUrl" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="count_voucher" jdbcType="INTEGER" property="countVoucher" />
    <result column="voucher_company_id" jdbcType="VARCHAR" property="voucherCompanyId" />
    <result column="third_pay_channel" jdbcType="VARCHAR" property="thirdPayChannel" />
    <result column="refund_third_txn_id" jdbcType="VARCHAR" property="refundThirdTxnId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId" />
    <result column="callback_num" jdbcType="INTEGER" property="callbackNum" />
    <result column="callback_next" jdbcType="TIMESTAMP" property="callbackNext" />
    <result column="xe_status" jdbcType="INTEGER" property="xeStatus" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_sub_type" jdbcType="INTEGER" property="orderSubType" />
    <result column="business_mode" jdbcType="INTEGER" property="businessMode" />
    <result column="invoice_provide_status" jdbcType="INTEGER" property="invoiceProvideStatus" />
    <result column="scene_invoice_type" jdbcType="INTEGER" property="sceneInvoiceType" />
    <result column="invoice_provide_type" jdbcType="INTEGER" property="invoiceProvideType" />
    <result column="invoice_provide_name" jdbcType="VARCHAR" property="invoiceProvideName" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="cashier_refund_way" jdbcType="INTEGER" property="cashierRefundWay" />
    <result column="bank_redcoupon_refund_amount" jdbcType="DECIMAL" property="bankRedcouponRefundAmount" />
    <result column="bank_company_refund_amount" jdbcType="DECIMAL" property="bankCompanyRefundAmount" />
    <result column="cashier_public_refund_way" jdbcType="INTEGER" property="cashierPublicRefundWay" />
    <result column="voucher_invoice_refund_amount" jdbcType="DECIMAL" property="voucherInvoiceRefundAmount" />
    <result column="voucher_order_invoice_refund_amount" jdbcType="DECIMAL" property="voucherOrderInvoiceRefundAmount" />
    <result column="amount_voucher_individual" jdbcType="DECIMAL" property="amountVoucherIndividual" />
    <result column="amount_voucher_redcoupon" jdbcType="DECIMAL" property="amountVoucherRedcoupon" />
    <result column="refund_amount_reimburse_company" jdbcType="DECIMAL" property="refundAmountReimburseCompany" />
    <result column="refund_amount_reimburse_self" jdbcType="DECIMAL" property="refundAmountReimburseSelf" />
    <result column="order_payment_model" jdbcType="INTEGER" property="orderPaymentModel" />
    <result column="order_channel_type" jdbcType="INTEGER" property="orderChannelType" />
    <result column="bank_personal_refund_amount" jdbcType="DECIMAL" property="bankPersonalRefundAmount"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, refund_status, create_time, update_time, fb_order_id, fb_trade_id, root_order_id, 
    client_ip, device, currency, refund_amount_all, refund_amount_personal, refund_amount_public, 
    refund_amount_company, refund_amount_redcoupon, refund_amount_third, refund_amount_fbb, 
    refund_amount_voucher, refund_amount_bank, account_type, account_sub_id, account_model, 
    cashier_txn_id, fb_order_name, refund_txn_id, biz_notify_url, refund_reason, complete_time, 
    company_id, employee_id, count_voucher, voucher_company_id, third_pay_channel, refund_third_txn_id, 
    remark, refund_order_id, callback_num, callback_next, xe_status, order_type, order_sub_type, 
    business_mode, invoice_provide_status, scene_invoice_type, invoice_provide_type, 
    invoice_provide_name, bank_account_no, bank_trans_no, bank_name, cashier_refund_way, 
    bank_redcoupon_refund_amount, bank_company_refund_amount, cashier_public_refund_way, 
    voucher_invoice_refund_amount, voucher_order_invoice_refund_amount, amount_voucher_individual, 
    amount_voucher_redcoupon, refund_amount_reimburse_company, refund_amount_reimburse_self, 
    order_payment_model, order_channel_type,bank_personal_refund_amount
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cashier_order_refund_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from cashier_order_refund_settlement
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_refund_settlement
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_refund_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_refund_settlement (id, refund_status, create_time, 
      update_time, fb_order_id, fb_trade_id, 
      root_order_id, client_ip, device, 
      currency, refund_amount_all, refund_amount_personal, 
      refund_amount_public, refund_amount_company, 
      refund_amount_redcoupon, refund_amount_third, 
      refund_amount_fbb, refund_amount_voucher, refund_amount_bank, 
      account_type, account_sub_id, account_model, 
      cashier_txn_id, fb_order_name, refund_txn_id, 
      biz_notify_url, refund_reason, complete_time, 
      company_id, employee_id, count_voucher, 
      voucher_company_id, third_pay_channel, refund_third_txn_id, 
      remark, refund_order_id, callback_num, 
      callback_next, xe_status, order_type, 
      order_sub_type, business_mode, invoice_provide_status, 
      scene_invoice_type, invoice_provide_type, invoice_provide_name, 
      bank_account_no, bank_trans_no, bank_name, 
      cashier_refund_way, bank_redcoupon_refund_amount, 
      bank_company_refund_amount, cashier_public_refund_way, 
      voucher_invoice_refund_amount, voucher_order_invoice_refund_amount, 
      amount_voucher_individual, amount_voucher_redcoupon, 
      refund_amount_reimburse_company, refund_amount_reimburse_self, 
      order_payment_model, order_channel_type,bank_personal_refund_amount)
    values (#{id,jdbcType=VARCHAR}, #{refundStatus,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{fbOrderId,jdbcType=VARCHAR}, #{fbTradeId,jdbcType=VARCHAR}, 
      #{rootOrderId,jdbcType=VARCHAR}, #{clientIp,jdbcType=VARCHAR}, #{device,jdbcType=VARCHAR}, 
      #{currency,jdbcType=VARCHAR}, #{refundAmountAll,jdbcType=DECIMAL}, #{refundAmountPersonal,jdbcType=DECIMAL}, 
      #{refundAmountPublic,jdbcType=DECIMAL}, #{refundAmountCompany,jdbcType=DECIMAL}, 
      #{refundAmountRedcoupon,jdbcType=DECIMAL}, #{refundAmountThird,jdbcType=DECIMAL}, 
      #{refundAmountFbb,jdbcType=DECIMAL}, #{refundAmountVoucher,jdbcType=DECIMAL}, #{refundAmountBank,jdbcType=DECIMAL}, 
      #{accountType,jdbcType=SMALLINT}, #{accountSubId,jdbcType=VARCHAR}, #{accountModel,jdbcType=INTEGER}, 
      #{cashierTxnId,jdbcType=VARCHAR}, #{fbOrderName,jdbcType=VARCHAR}, #{refundTxnId,jdbcType=VARCHAR}, 
      #{bizNotifyUrl,jdbcType=VARCHAR}, #{refundReason,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP}, 
      #{companyId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, #{countVoucher,jdbcType=INTEGER}, 
      #{voucherCompanyId,jdbcType=VARCHAR}, #{thirdPayChannel,jdbcType=VARCHAR}, #{refundThirdTxnId,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{refundOrderId,jdbcType=VARCHAR}, #{callbackNum,jdbcType=INTEGER}, 
      #{callbackNext,jdbcType=TIMESTAMP}, #{xeStatus,jdbcType=INTEGER}, #{orderType,jdbcType=INTEGER}, 
      #{orderSubType,jdbcType=INTEGER}, #{businessMode,jdbcType=INTEGER}, #{invoiceProvideStatus,jdbcType=INTEGER}, 
      #{sceneInvoiceType,jdbcType=INTEGER}, #{invoiceProvideType,jdbcType=INTEGER}, #{invoiceProvideName,jdbcType=VARCHAR}, 
      #{bankAccountNo,jdbcType=VARCHAR}, #{bankTransNo,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{cashierRefundWay,jdbcType=INTEGER}, #{bankRedcouponRefundAmount,jdbcType=DECIMAL}, 
      #{bankCompanyRefundAmount,jdbcType=DECIMAL}, #{cashierPublicRefundWay,jdbcType=INTEGER}, 
      #{voucherInvoiceRefundAmount,jdbcType=DECIMAL}, #{voucherOrderInvoiceRefundAmount,jdbcType=DECIMAL}, 
      #{amountVoucherIndividual,jdbcType=DECIMAL}, #{amountVoucherRedcoupon,jdbcType=DECIMAL}, 
      #{refundAmountReimburseCompany,jdbcType=DECIMAL}, #{refundAmountReimburseSelf,jdbcType=DECIMAL}, 
      #{orderPaymentModel,jdbcType=INTEGER}, #{orderChannelType,jdbcType=INTEGER},
      #{bankPersonalRefundAmount,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_refund_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="fbTradeId != null">
        fb_trade_id,
      </if>
      <if test="rootOrderId != null">
        root_order_id,
      </if>
      <if test="clientIp != null">
        client_ip,
      </if>
      <if test="device != null">
        device,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="refundAmountAll != null">
        refund_amount_all,
      </if>
      <if test="refundAmountPersonal != null">
        refund_amount_personal,
      </if>
      <if test="refundAmountPublic != null">
        refund_amount_public,
      </if>
      <if test="refundAmountCompany != null">
        refund_amount_company,
      </if>
      <if test="refundAmountRedcoupon != null">
        refund_amount_redcoupon,
      </if>
      <if test="refundAmountThird != null">
        refund_amount_third,
      </if>
      <if test="refundAmountFbb != null">
        refund_amount_fbb,
      </if>
      <if test="refundAmountVoucher != null">
        refund_amount_voucher,
      </if>
      <if test="refundAmountBank != null">
        refund_amount_bank,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="accountSubId != null">
        account_sub_id,
      </if>
      <if test="accountModel != null">
        account_model,
      </if>
      <if test="cashierTxnId != null">
        cashier_txn_id,
      </if>
      <if test="fbOrderName != null">
        fb_order_name,
      </if>
      <if test="refundTxnId != null">
        refund_txn_id,
      </if>
      <if test="bizNotifyUrl != null">
        biz_notify_url,
      </if>
      <if test="refundReason != null">
        refund_reason,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="countVoucher != null">
        count_voucher,
      </if>
      <if test="voucherCompanyId != null">
        voucher_company_id,
      </if>
      <if test="thirdPayChannel != null">
        third_pay_channel,
      </if>
      <if test="refundThirdTxnId != null">
        refund_third_txn_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="refundOrderId != null">
        refund_order_id,
      </if>
      <if test="callbackNum != null">
        callback_num,
      </if>
      <if test="callbackNext != null">
        callback_next,
      </if>
      <if test="xeStatus != null">
        xe_status,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderSubType != null">
        order_sub_type,
      </if>
      <if test="businessMode != null">
        business_mode,
      </if>
      <if test="invoiceProvideStatus != null">
        invoice_provide_status,
      </if>
      <if test="sceneInvoiceType != null">
        scene_invoice_type,
      </if>
      <if test="invoiceProvideType != null">
        invoice_provide_type,
      </if>
      <if test="invoiceProvideName != null">
        invoice_provide_name,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="bankTransNo != null">
        bank_trans_no,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="cashierRefundWay != null">
        cashier_refund_way,
      </if>
      <if test="bankRedcouponRefundAmount != null">
        bank_redcoupon_refund_amount,
      </if>
      <if test="bankCompanyRefundAmount != null">
        bank_company_refund_amount,
      </if>
      <if test="cashierPublicRefundWay != null">
        cashier_public_refund_way,
      </if>
      <if test="voucherInvoiceRefundAmount != null">
        voucher_invoice_refund_amount,
      </if>
      <if test="voucherOrderInvoiceRefundAmount != null">
        voucher_order_invoice_refund_amount,
      </if>
      <if test="amountVoucherIndividual != null">
        amount_voucher_individual,
      </if>
      <if test="amountVoucherRedcoupon != null">
        amount_voucher_redcoupon,
      </if>
      <if test="refundAmountReimburseCompany != null">
        refund_amount_reimburse_company,
      </if>
      <if test="refundAmountReimburseSelf != null">
        refund_amount_reimburse_self,
      </if>
      <if test="orderPaymentModel != null">
        order_payment_model,
      </if>
      <if test="orderChannelType != null">
        order_channel_type,
      </if>
      <if test="bankPersonalRefundAmount != null">
        bank_personal_refund_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fbTradeId != null">
        #{fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        #{device,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="refundAmountAll != null">
        #{refundAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountPersonal != null">
        #{refundAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountPublic != null">
        #{refundAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountCompany != null">
        #{refundAmountCompany,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountRedcoupon != null">
        #{refundAmountRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountThird != null">
        #{refundAmountThird,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountFbb != null">
        #{refundAmountFbb,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountVoucher != null">
        #{refundAmountVoucher,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountBank != null">
        #{refundAmountBank,jdbcType=DECIMAL},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=SMALLINT},
      </if>
      <if test="accountSubId != null">
        #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="accountModel != null">
        #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="cashierTxnId != null">
        #{cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderName != null">
        #{fbOrderName,jdbcType=VARCHAR},
      </if>
      <if test="refundTxnId != null">
        #{refundTxnId,jdbcType=VARCHAR},
      </if>
      <if test="bizNotifyUrl != null">
        #{bizNotifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="refundReason != null">
        #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="countVoucher != null">
        #{countVoucher,jdbcType=INTEGER},
      </if>
      <if test="voucherCompanyId != null">
        #{voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="thirdPayChannel != null">
        #{thirdPayChannel,jdbcType=VARCHAR},
      </if>
      <if test="refundThirdTxnId != null">
        #{refundThirdTxnId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderId != null">
        #{refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="callbackNum != null">
        #{callbackNum,jdbcType=INTEGER},
      </if>
      <if test="callbackNext != null">
        #{callbackNext,jdbcType=TIMESTAMP},
      </if>
      <if test="xeStatus != null">
        #{xeStatus,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderSubType != null">
        #{orderSubType,jdbcType=INTEGER},
      </if>
      <if test="businessMode != null">
        #{businessMode,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideStatus != null">
        #{invoiceProvideStatus,jdbcType=INTEGER},
      </if>
      <if test="sceneInvoiceType != null">
        #{sceneInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideType != null">
        #{invoiceProvideType,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideName != null">
        #{invoiceProvideName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankTransNo != null">
        #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="cashierRefundWay != null">
        #{cashierRefundWay,jdbcType=INTEGER},
      </if>
      <if test="bankRedcouponRefundAmount != null">
        #{bankRedcouponRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankCompanyRefundAmount != null">
        #{bankCompanyRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashierPublicRefundWay != null">
        #{cashierPublicRefundWay,jdbcType=INTEGER},
      </if>
      <if test="voucherInvoiceRefundAmount != null">
        #{voucherInvoiceRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="voucherOrderInvoiceRefundAmount != null">
        #{voucherOrderInvoiceRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucherIndividual != null">
        #{amountVoucherIndividual,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucherRedcoupon != null">
        #{amountVoucherRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountReimburseCompany != null">
        #{refundAmountReimburseCompany,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountReimburseSelf != null">
        #{refundAmountReimburseSelf,jdbcType=DECIMAL},
      </if>
      <if test="orderPaymentModel != null">
        #{orderPaymentModel,jdbcType=INTEGER},
      </if>
      <if test="bankPersonalRefundAmount != null">
        #{bankPersonalRefundAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from cashier_order_refund_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_refund_settlement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.refundStatus != null">
        refund_status = #{record.refundStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbTradeId != null">
        fb_trade_id = #{record.fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="record.rootOrderId != null">
        root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.clientIp != null">
        client_ip = #{record.clientIp,jdbcType=VARCHAR},
      </if>
      <if test="record.device != null">
        device = #{record.device,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.refundAmountAll != null">
        refund_amount_all = #{record.refundAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountPersonal != null">
        refund_amount_personal = #{record.refundAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountPublic != null">
        refund_amount_public = #{record.refundAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountCompany != null">
        refund_amount_company = #{record.refundAmountCompany,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountRedcoupon != null">
        refund_amount_redcoupon = #{record.refundAmountRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountThird != null">
        refund_amount_third = #{record.refundAmountThird,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountFbb != null">
        refund_amount_fbb = #{record.refundAmountFbb,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountVoucher != null">
        refund_amount_voucher = #{record.refundAmountVoucher,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountBank != null">
        refund_amount_bank = #{record.refundAmountBank,jdbcType=DECIMAL},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=SMALLINT},
      </if>
      <if test="record.accountSubId != null">
        account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountModel != null">
        account_model = #{record.accountModel,jdbcType=INTEGER},
      </if>
      <if test="record.cashierTxnId != null">
        cashier_txn_id = #{record.cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderName != null">
        fb_order_name = #{record.fbOrderName,jdbcType=VARCHAR},
      </if>
      <if test="record.refundTxnId != null">
        refund_txn_id = #{record.refundTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNotifyUrl != null">
        biz_notify_url = #{record.bizNotifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.refundReason != null">
        refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.countVoucher != null">
        count_voucher = #{record.countVoucher,jdbcType=INTEGER},
      </if>
      <if test="record.voucherCompanyId != null">
        voucher_company_id = #{record.voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdPayChannel != null">
        third_pay_channel = #{record.thirdPayChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.refundThirdTxnId != null">
        refund_third_txn_id = #{record.refundThirdTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.refundOrderId != null">
        refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.callbackNum != null">
        callback_num = #{record.callbackNum,jdbcType=INTEGER},
      </if>
      <if test="record.callbackNext != null">
        callback_next = #{record.callbackNext,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xeStatus != null">
        xe_status = #{record.xeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderSubType != null">
        order_sub_type = #{record.orderSubType,jdbcType=INTEGER},
      </if>
      <if test="record.businessMode != null">
        business_mode = #{record.businessMode,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceProvideStatus != null">
        invoice_provide_status = #{record.invoiceProvideStatus,jdbcType=INTEGER},
      </if>
      <if test="record.sceneInvoiceType != null">
        scene_invoice_type = #{record.sceneInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceProvideType != null">
        invoice_provide_type = #{record.invoiceProvideType,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceProvideName != null">
        invoice_provide_name = #{record.invoiceProvideName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankTransNo != null">
        bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.cashierRefundWay != null">
        cashier_refund_way = #{record.cashierRefundWay,jdbcType=INTEGER},
      </if>
      <if test="record.bankRedcouponRefundAmount != null">
        bank_redcoupon_refund_amount = #{record.bankRedcouponRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.bankCompanyRefundAmount != null">
        bank_company_refund_amount = #{record.bankCompanyRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.cashierPublicRefundWay != null">
        cashier_public_refund_way = #{record.cashierPublicRefundWay,jdbcType=INTEGER},
      </if>
      <if test="record.voucherInvoiceRefundAmount != null">
        voucher_invoice_refund_amount = #{record.voucherInvoiceRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherOrderInvoiceRefundAmount != null">
        voucher_order_invoice_refund_amount = #{record.voucherOrderInvoiceRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.amountVoucherIndividual != null">
        amount_voucher_individual = #{record.amountVoucherIndividual,jdbcType=DECIMAL},
      </if>
      <if test="record.amountVoucherRedcoupon != null">
        amount_voucher_redcoupon = #{record.amountVoucherRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountReimburseCompany != null">
        refund_amount_reimburse_company = #{record.refundAmountReimburseCompany,jdbcType=DECIMAL},
      </if>
      <if test="record.refundAmountReimburseSelf != null">
        refund_amount_reimburse_self = #{record.refundAmountReimburseSelf,jdbcType=DECIMAL},
      </if>
      <if test="record.orderPaymentModel != null">
        order_payment_model = #{record.orderPaymentModel,jdbcType=INTEGER},
      </if>
      <if test="record.orderChannelType != null">
        order_channel_type = #{record.orderChannelType,jdbcType=INTEGER},
      </if>
      <if test="record.bankPersonalRefundAmount != null">
        bank_personal_refund_amount = #{record.bankPersonalRefundAmount,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_refund_settlement
    set id = #{record.id,jdbcType=VARCHAR},
      refund_status = #{record.refundStatus,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      fb_trade_id = #{record.fbTradeId,jdbcType=VARCHAR},
      root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      client_ip = #{record.clientIp,jdbcType=VARCHAR},
      device = #{record.device,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      refund_amount_all = #{record.refundAmountAll,jdbcType=DECIMAL},
      refund_amount_personal = #{record.refundAmountPersonal,jdbcType=DECIMAL},
      refund_amount_public = #{record.refundAmountPublic,jdbcType=DECIMAL},
      refund_amount_company = #{record.refundAmountCompany,jdbcType=DECIMAL},
      refund_amount_redcoupon = #{record.refundAmountRedcoupon,jdbcType=DECIMAL},
      refund_amount_third = #{record.refundAmountThird,jdbcType=DECIMAL},
      refund_amount_fbb = #{record.refundAmountFbb,jdbcType=DECIMAL},
      refund_amount_voucher = #{record.refundAmountVoucher,jdbcType=DECIMAL},
      refund_amount_bank = #{record.refundAmountBank,jdbcType=DECIMAL},
      account_type = #{record.accountType,jdbcType=SMALLINT},
      account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      account_model = #{record.accountModel,jdbcType=INTEGER},
      cashier_txn_id = #{record.cashierTxnId,jdbcType=VARCHAR},
      fb_order_name = #{record.fbOrderName,jdbcType=VARCHAR},
      refund_txn_id = #{record.refundTxnId,jdbcType=VARCHAR},
      biz_notify_url = #{record.bizNotifyUrl,jdbcType=VARCHAR},
      refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      count_voucher = #{record.countVoucher,jdbcType=INTEGER},
      voucher_company_id = #{record.voucherCompanyId,jdbcType=VARCHAR},
      third_pay_channel = #{record.thirdPayChannel,jdbcType=VARCHAR},
      refund_third_txn_id = #{record.refundThirdTxnId,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
      callback_num = #{record.callbackNum,jdbcType=INTEGER},
      callback_next = #{record.callbackNext,jdbcType=TIMESTAMP},
      xe_status = #{record.xeStatus,jdbcType=INTEGER},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_sub_type = #{record.orderSubType,jdbcType=INTEGER},
      business_mode = #{record.businessMode,jdbcType=INTEGER},
      invoice_provide_status = #{record.invoiceProvideStatus,jdbcType=INTEGER},
      scene_invoice_type = #{record.sceneInvoiceType,jdbcType=INTEGER},
      invoice_provide_type = #{record.invoiceProvideType,jdbcType=INTEGER},
      invoice_provide_name = #{record.invoiceProvideName,jdbcType=VARCHAR},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      cashier_refund_way = #{record.cashierRefundWay,jdbcType=INTEGER},
      bank_redcoupon_refund_amount = #{record.bankRedcouponRefundAmount,jdbcType=DECIMAL},
      bank_company_refund_amount = #{record.bankCompanyRefundAmount,jdbcType=DECIMAL},
      cashier_public_refund_way = #{record.cashierPublicRefundWay,jdbcType=INTEGER},
      voucher_invoice_refund_amount = #{record.voucherInvoiceRefundAmount,jdbcType=DECIMAL},
      voucher_order_invoice_refund_amount = #{record.voucherOrderInvoiceRefundAmount,jdbcType=DECIMAL},
      amount_voucher_individual = #{record.amountVoucherIndividual,jdbcType=DECIMAL},
      amount_voucher_redcoupon = #{record.amountVoucherRedcoupon,jdbcType=DECIMAL},
      refund_amount_reimburse_company = #{record.refundAmountReimburseCompany,jdbcType=DECIMAL},
      refund_amount_reimburse_self = #{record.refundAmountReimburseSelf,jdbcType=DECIMAL},
      order_payment_model = #{record.orderPaymentModel,jdbcType=INTEGER},
      order_channel_type = #{record.orderChannelType,jdbcType=INTEGER},
      bank_personal_refund_amount = #{record.bankPersonalRefundAmount,jdbcType=DECIMAL},
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_refund_settlement
    <set>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fbTradeId != null">
        fb_trade_id = #{fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        client_ip = #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        device = #{device,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="refundAmountAll != null">
        refund_amount_all = #{refundAmountAll,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountPersonal != null">
        refund_amount_personal = #{refundAmountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountPublic != null">
        refund_amount_public = #{refundAmountPublic,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountCompany != null">
        refund_amount_company = #{refundAmountCompany,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountRedcoupon != null">
        refund_amount_redcoupon = #{refundAmountRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountThird != null">
        refund_amount_third = #{refundAmountThird,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountFbb != null">
        refund_amount_fbb = #{refundAmountFbb,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountVoucher != null">
        refund_amount_voucher = #{refundAmountVoucher,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountBank != null">
        refund_amount_bank = #{refundAmountBank,jdbcType=DECIMAL},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=SMALLINT},
      </if>
      <if test="accountSubId != null">
        account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="accountModel != null">
        account_model = #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="cashierTxnId != null">
        cashier_txn_id = #{cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderName != null">
        fb_order_name = #{fbOrderName,jdbcType=VARCHAR},
      </if>
      <if test="refundTxnId != null">
        refund_txn_id = #{refundTxnId,jdbcType=VARCHAR},
      </if>
      <if test="bizNotifyUrl != null">
        biz_notify_url = #{bizNotifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="refundReason != null">
        refund_reason = #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="countVoucher != null">
        count_voucher = #{countVoucher,jdbcType=INTEGER},
      </if>
      <if test="voucherCompanyId != null">
        voucher_company_id = #{voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="thirdPayChannel != null">
        third_pay_channel = #{thirdPayChannel,jdbcType=VARCHAR},
      </if>
      <if test="refundThirdTxnId != null">
        refund_third_txn_id = #{refundThirdTxnId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderId != null">
        refund_order_id = #{refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="callbackNum != null">
        callback_num = #{callbackNum,jdbcType=INTEGER},
      </if>
      <if test="callbackNext != null">
        callback_next = #{callbackNext,jdbcType=TIMESTAMP},
      </if>
      <if test="xeStatus != null">
        xe_status = #{xeStatus,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderSubType != null">
        order_sub_type = #{orderSubType,jdbcType=INTEGER},
      </if>
      <if test="businessMode != null">
        business_mode = #{businessMode,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideStatus != null">
        invoice_provide_status = #{invoiceProvideStatus,jdbcType=INTEGER},
      </if>
      <if test="sceneInvoiceType != null">
        scene_invoice_type = #{sceneInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideType != null">
        invoice_provide_type = #{invoiceProvideType,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideName != null">
        invoice_provide_name = #{invoiceProvideName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankTransNo != null">
        bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="cashierRefundWay != null">
        cashier_refund_way = #{cashierRefundWay,jdbcType=INTEGER},
      </if>
      <if test="bankRedcouponRefundAmount != null">
        bank_redcoupon_refund_amount = #{bankRedcouponRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankCompanyRefundAmount != null">
        bank_company_refund_amount = #{bankCompanyRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashierPublicRefundWay != null">
        cashier_public_refund_way = #{cashierPublicRefundWay,jdbcType=INTEGER},
      </if>
      <if test="voucherInvoiceRefundAmount != null">
        voucher_invoice_refund_amount = #{voucherInvoiceRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="voucherOrderInvoiceRefundAmount != null">
        voucher_order_invoice_refund_amount = #{voucherOrderInvoiceRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucherIndividual != null">
        amount_voucher_individual = #{amountVoucherIndividual,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucherRedcoupon != null">
        amount_voucher_redcoupon = #{amountVoucherRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountReimburseCompany != null">
        refund_amount_reimburse_company = #{refundAmountReimburseCompany,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountReimburseSelf != null">
        refund_amount_reimburse_self = #{refundAmountReimburseSelf,jdbcType=DECIMAL},
      </if>
      <if test="orderPaymentModel != null">
        order_payment_model = #{orderPaymentModel,jdbcType=INTEGER},
      </if>
      <if test="orderChannelType != null">
        order_channel_type = #{orderChannelType,jdbcType=INTEGER},
      </if>
      <if test="bankPersonalRefundAmount != null">
        bank_personal_refund_amount = #{bankPersonalRefundAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_refund_settlement
    set refund_status = #{refundStatus,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      fb_trade_id = #{fbTradeId,jdbcType=VARCHAR},
      root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      client_ip = #{clientIp,jdbcType=VARCHAR},
      device = #{device,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      refund_amount_all = #{refundAmountAll,jdbcType=DECIMAL},
      refund_amount_personal = #{refundAmountPersonal,jdbcType=DECIMAL},
      refund_amount_public = #{refundAmountPublic,jdbcType=DECIMAL},
      refund_amount_company = #{refundAmountCompany,jdbcType=DECIMAL},
      refund_amount_redcoupon = #{refundAmountRedcoupon,jdbcType=DECIMAL},
      refund_amount_third = #{refundAmountThird,jdbcType=DECIMAL},
      refund_amount_fbb = #{refundAmountFbb,jdbcType=DECIMAL},
      refund_amount_voucher = #{refundAmountVoucher,jdbcType=DECIMAL},
      refund_amount_bank = #{refundAmountBank,jdbcType=DECIMAL},
      account_type = #{accountType,jdbcType=SMALLINT},
      account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      account_model = #{accountModel,jdbcType=INTEGER},
      cashier_txn_id = #{cashierTxnId,jdbcType=VARCHAR},
      fb_order_name = #{fbOrderName,jdbcType=VARCHAR},
      refund_txn_id = #{refundTxnId,jdbcType=VARCHAR},
      biz_notify_url = #{bizNotifyUrl,jdbcType=VARCHAR},
      refund_reason = #{refundReason,jdbcType=VARCHAR},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      count_voucher = #{countVoucher,jdbcType=INTEGER},
      voucher_company_id = #{voucherCompanyId,jdbcType=VARCHAR},
      third_pay_channel = #{thirdPayChannel,jdbcType=VARCHAR},
      refund_third_txn_id = #{refundThirdTxnId,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      refund_order_id = #{refundOrderId,jdbcType=VARCHAR},
      callback_num = #{callbackNum,jdbcType=INTEGER},
      callback_next = #{callbackNext,jdbcType=TIMESTAMP},
      xe_status = #{xeStatus,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=INTEGER},
      order_sub_type = #{orderSubType,jdbcType=INTEGER},
      business_mode = #{businessMode,jdbcType=INTEGER},
      invoice_provide_status = #{invoiceProvideStatus,jdbcType=INTEGER},
      scene_invoice_type = #{sceneInvoiceType,jdbcType=INTEGER},
      invoice_provide_type = #{invoiceProvideType,jdbcType=INTEGER},
      invoice_provide_name = #{invoiceProvideName,jdbcType=VARCHAR},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      cashier_refund_way = #{cashierRefundWay,jdbcType=INTEGER},
      bank_redcoupon_refund_amount = #{bankRedcouponRefundAmount,jdbcType=DECIMAL},
      bank_company_refund_amount = #{bankCompanyRefundAmount,jdbcType=DECIMAL},
      cashier_public_refund_way = #{cashierPublicRefundWay,jdbcType=INTEGER},
      voucher_invoice_refund_amount = #{voucherInvoiceRefundAmount,jdbcType=DECIMAL},
      voucher_order_invoice_refund_amount = #{voucherOrderInvoiceRefundAmount,jdbcType=DECIMAL},
      amount_voucher_individual = #{amountVoucherIndividual,jdbcType=DECIMAL},
      amount_voucher_redcoupon = #{amountVoucherRedcoupon,jdbcType=DECIMAL},
      refund_amount_reimburse_company = #{refundAmountReimburseCompany,jdbcType=DECIMAL},
      refund_amount_reimburse_self = #{refundAmountReimburseSelf,jdbcType=DECIMAL},
      order_payment_model = #{orderPaymentModel,jdbcType=INTEGER},
      order_channel_type = #{orderChannelType,jdbcType=INTEGER},
      bank_personal_refund_amount = #{bankPersonalRefundAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>