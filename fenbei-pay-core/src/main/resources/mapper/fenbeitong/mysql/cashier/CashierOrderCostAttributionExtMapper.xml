<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderCostAttributionExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
        <result column="cost_attribution_id" jdbcType="VARCHAR" property="costAttributionId" />
        <result column="cost_attribution_type" jdbcType="SMALLINT" property="costAttributionType" />
        <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName" />
        <result column="p_cost_attribution" jdbcType="VARCHAR" property="pCostAttribution" />
        <result column="custom_ext" jdbcType="VARCHAR" property="customExt" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="cost_attribution_path" jdbcType="VARCHAR" property="costAttributionPath" />
        <result column="cost_attribution_opt" jdbcType="INTEGER" property="costAttributionOpt" />
        <result column="budget_opt" jdbcType="INTEGER" property="budgetOpt" />
        <result column="company_id" jdbcType="VARCHAR" property="companyId" />
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
        <result column="order_type" jdbcType="INTEGER" property="orderType" />
        <result column="system_ext" jdbcType="VARCHAR" property="systemExt" />
        <result column="use_personal_budget" jdbcType="INTEGER" property="usePersonalBudget" />
    </resultMap>

    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, fb_order_id, cost_attribution_id, cost_attribution_type, cost_attribution_name,
        p_cost_attribution, custom_ext, create_time, cost_attribution_path, cost_attribution_opt,
        budget_opt, company_id, employee_id, order_type, system_ext
    </sql>

    <select id="getRefundOrderCostAttributionList"
            resultType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">

        select <include refid="Base_Column_List"/>
        from cashier_order_cost_attribution where 1=1
        <if test="record.costAttributionType != null">
            and cost_attribution_type = #{record.costAttributionType}
        </if>
        <if test="record.orderType != null">
            and order_type = #{record.orderType}
        </if>
        <if test="record.companyId != null">
            and company_id = #{record.companyId}
        </if>
        <if test="record.list != null">
            and fb_order_id in
            <foreach item="item" collection="record.list" separator="," open="(" close=")" index="">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY create_time DESC
        limit #{record.offset}, #{record.pageSize}
    </select>


    <select id="countRefundOrderCostAttributionList"
            resultType="java.lang.Integer" parameterType="com.fenbeitong.fenbeipay.core.model.vo.cashier.CostAttributionReqVo">

        select count(1)
        from cashier_order_cost_attribution where 1=1
        <if test="record.costAttributionType != null">
            and cost_attribution_type = #{record.costAttributionType}
        </if>
        <if test="record.orderType != null">
            and order_type = #{record.orderType}
        </if>
        <if test="record.companyId != null">
            and company_id = #{record.companyId}
        </if>
        <if test="record.list != null">
            and fb_order_id in
            <foreach item="item" collection="record.list" separator="," open="(" close=")" index="">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>

    </select>
    <select id="getCostAttributionList"
            resultType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">

        select <include refid="Base_Column_List"/>
        from cashier_order_cost_attribution
        where id in
        (select id from (select id
        from cashier_order_cost_attribution
        where 1=1
        <if test="record.costAttributionType != null">
            and cost_attribution_type = #{record.costAttributionType}
        </if>
        <if test="record.orderType != null">
            and order_type = #{record.orderType}
        </if>
        <if test="record.companyId != null">
            and company_id = #{record.companyId}
        </if>
        <if test="record.startTime != null and record.endTime != null">
            and create_time between #{record.startTime} and #{record.endTime}
        </if>
        limit #{record.offset}, #{record.pageSize}
        )tt)
        ORDER BY create_time DESC
    </select>

    <select id="countCostAttributionList"
            resultType="java.lang.Integer" parameterType="com.fenbeitong.fenbeipay.core.model.vo.cashier.CostAttributionReqVo">

        select count(1)
        from cashier_order_cost_attribution where 1=1
        <if test="record.costAttributionType != null">
            and cost_attribution_type = #{record.costAttributionType}
        </if>
        <if test="record.orderType != null">
            and order_type = #{record.orderType}
        </if>
        <if test="record.companyId != null">
            and company_id = #{record.companyId}
        </if>
        <if test="record.startTime != null and record.endTime != null">
            and create_time between #{record.startTime} and #{record.endTime}
        </if>

    </select>
</mapper>