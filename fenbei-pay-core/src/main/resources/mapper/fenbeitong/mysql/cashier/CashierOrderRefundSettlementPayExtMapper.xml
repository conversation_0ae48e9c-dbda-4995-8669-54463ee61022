<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderRefundSettlementPayExtMapper">

    <resultMap id="cashierRefundVoucherHistoryAmountStatVoResultMap" type="com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundVoucherHistoryAmountStatVo">
        <id column="voucher_id" jdbcType="VARCHAR" property="voucherId" />
        <result column="refund_amount" jdbcType="NUMERIC" property="refundAmount" />
    </resultMap>

    <select id="selectPaySumAmountByCashierTxnIdAndStatus" parameterType="map" resultMap="cashierRefundVoucherHistoryAmountStatVoResultMap">
        select  voucher_id ,
        sum(refund_amount) as refund_amount
        from cashier_order_refund_settlement_pay
        where 1=1
        <if test="cashierTxnId != null and cashierTxnId != '' ">
            and cashier_txn_id = #{cashierTxnId}
        </if>
        <if test="payChannel != null and payChannel != '' ">
            and pay_channel = #{payChannel}
        </if>
        <if test="accountSubType != null and accountSubType != '' ">
            and deduction_account_type = #{accountSubType}
        </if>
        and voucher_id != ''
        <if test="refundStatusList != null and refundStatusList.size > 0">
            and refund_status in
            <foreach collection="refundStatusList" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        group by voucher_id

    </select>

<select id="getRefundOrderCostList"
        resultType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution">

    select c.refund_order_id fbOrderId,co.cost_attribution_type costAttributionType, co.cost_attribution_name costAttributionName ,co.cost_attribution_id costAttributionId
    from cashier_order_refund_settlement c inner join cashier_order_cost_attribution co
    on c.fb_order_id = co.fb_order_id
    where 1=1
    <if test="record.costAttributionType != null">
        and co.cost_attribution_type = #{record.costAttributionType}
    </if>
    <if test="record.orderType != null">
        and c.order_type = #{record.orderType}
    </if>
    <if test="record.companyId != null">
        and c.company_id = #{record.companyId}
    </if>
    <if test="record.startTime != null and record.endTime != null">
        and c.create_time between #{record.startTime} and #{record.endTime}
    </if>
    ORDER BY c.create_time DESC
    limit #{record.offset}, #{record.pageSize}
</select>

    <select id="countRefundOrderCostList" resultType="java.lang.Integer">

        select count(1)
        from cashier_order_refund_settlement c inner join cashier_order_cost_attribution co
        on c.fb_order_id = co.fb_order_id
        where 1=1
        <if test="record.costAttributionType != null">
            and co.cost_attribution_type = #{record.costAttributionType}
        </if>
        <if test="record.orderType != null">
            and c.order_type = #{record.orderType}
        </if>
        <if test="record.companyId != null">
            and c.company_id = #{record.companyId}
        </if>
        <if test="record.startTime != null and record.endTime != null">
            and c.create_time between #{record.startTime} and #{record.endTime}
        </if>
    </select>

</mapper>