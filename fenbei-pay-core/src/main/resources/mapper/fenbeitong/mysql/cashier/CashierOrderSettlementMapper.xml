<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderSettlementMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="pay_status" jdbcType="SMALLINT" property="payStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="cashier_txn_id" jdbcType="VARCHAR" property="cashierTxnId" />
    <result column="fb_trade_id" jdbcType="VARCHAR" property="fbTradeId" />
    <result column="root_order_id" jdbcType="VARCHAR" property="rootOrderId" />
    <result column="order_root_type" jdbcType="INTEGER" property="orderRootType" />
    <result column="cashier_trade_type" jdbcType="INTEGER" property="cashierTradeType" />
    <result column="amount_all" jdbcType="DECIMAL" property="amountAll" />
    <result column="amount_personal" jdbcType="DECIMAL" property="amountPersonal" />
    <result column="amount_public" jdbcType="DECIMAL" property="amountPublic" />
    <result column="amount_company" jdbcType="DECIMAL" property="amountCompany" />
    <result column="amount_redcoupon" jdbcType="DECIMAL" property="amountRedcoupon" />
    <result column="amount_third" jdbcType="DECIMAL" property="amountThird" />
    <result column="amount_fbb" jdbcType="DECIMAL" property="amountFbb" />
    <result column="amount_voucher" jdbcType="DECIMAL" property="amountVoucher" />
    <result column="amount_voucher_individual" jdbcType="DECIMAL" property="amountVoucherIndividual" />
    <result column="amount_voucher_redcoupon" jdbcType="DECIMAL" property="amountVoucherRedcoupon" />
    <result column="amount_zero_tax" jdbcType="DECIMAL" property="amountZeroTax" />
    <result column="amount_coupon" jdbcType="DECIMAL" property="amountCoupon" />
    <result column="voucher_order_invoice_amount" jdbcType="DECIMAL" property="voucherOrderInvoiceAmount" />
    <result column="voucher_invoice_amount" jdbcType="DECIMAL" property="voucherInvoiceAmount" />
    <result column="company_pre_pay" jdbcType="SMALLINT" property="companyPrePay" />
    <result column="third_pay_channel" jdbcType="VARCHAR" property="thirdPayChannel" />
    <result column="amount_bank" jdbcType="DECIMAL" property="amountBank" />
    <result column="account_type" jdbcType="SMALLINT" property="accountType" />
    <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="fb_order_snapshot" jdbcType="VARCHAR" property="fbOrderSnapshot" />
    <result column="fb_order_name" jdbcType="VARCHAR" property="fbOrderName" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="count_voucher" jdbcType="INTEGER" property="countVoucher" />
    <result column="max_voucher" jdbcType="DECIMAL" property="maxVoucher" />
    <result column="voucher_company_id" jdbcType="VARCHAR" property="voucherCompanyId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_sub_type" jdbcType="INTEGER" property="orderSubType" />
    <result column="biz_callback_url" jdbcType="VARCHAR" property="bizCallbackUrl" />
    <result column="callback_num" jdbcType="INTEGER" property="callbackNum" />
    <result column="callback_next" jdbcType="TIMESTAMP" property="callbackNext" />
    <result column="deadline_time" jdbcType="TIMESTAMP" property="deadlineTime" />
    <result column="client_version" jdbcType="VARCHAR" property="clientVersion" />
    <result column="client_ip" jdbcType="VARCHAR" property="clientIp" />
    <result column="device" jdbcType="VARCHAR" property="device" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="client_lon" jdbcType="DOUBLE" property="clientLon" />
    <result column="client_lat" jdbcType="DOUBLE" property="clientLat" />
    <result column="person_account_id" jdbcType="VARCHAR" property="personAccountId" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="common_json" jdbcType="VARCHAR" property="commonJson" />
    <result column="third_txn_id" jdbcType="VARCHAR" property="thirdTxnId" />
    <result column="biz_pay_type" jdbcType="VARCHAR" property="bizPayType" />
    <result column="consumer_account_sub_type" jdbcType="SMALLINT" property="consumerAccountSubType" />
    <result column="company_no_settle_price" jdbcType="DECIMAL" property="companyNoSettlePrice" />
    <result column="amount_public_rule" jdbcType="DECIMAL" property="amountPublicRule" />
    <result column="operation_channel_type" jdbcType="INTEGER" property="operationChannelType" />
    <result column="customer_service_id" jdbcType="VARCHAR" property="customerServiceId" />
    <result column="customer_service_name" jdbcType="VARCHAR" property="customerServiceName" />
    <result column="business_mode" jdbcType="INTEGER" property="businessMode" />
    <result column="invoice_provide_status" jdbcType="INTEGER" property="invoiceProvideStatus" />
    <result column="scene_invoice_type" jdbcType="INTEGER" property="sceneInvoiceType" />
    <result column="invoice_provide_type" jdbcType="INTEGER" property="invoiceProvideType" />
    <result column="invoice_provide_name" jdbcType="VARCHAR" property="invoiceProvideName" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="redcoupon_can_pay" jdbcType="INTEGER" property="redcouponCanPay" />
    <result column="bank_company_amount" jdbcType="DECIMAL" property="bankCompanyAmount" />
    <result column="bank_redcoupon_amount" jdbcType="DECIMAL" property="bankRedcouponAmount" />
    <result column="amount_reimburse_company" jdbcType="DECIMAL" property="amountReimburseCompany" />
    <result column="amount_reimburse_self" jdbcType="DECIMAL" property="amountReimburseSelf" />
    <result column="order_payment_model" jdbcType="INTEGER" property="orderPaymentModel" />
    <result column="order_channel_type" jdbcType="INTEGER" property="orderChannelType" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
    <result column="bank_personal_amount" jdbcType="DECIMAL" property="bankPersonalAmount" />
    <result column="payment_company_id" jdbcType="VARCHAR" property="paymentCompanyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, pay_status, create_time, update_time, fb_order_id, cashier_txn_id, fb_trade_id, 
    root_order_id, order_root_type, cashier_trade_type, amount_all, amount_personal, 
    amount_public, amount_company, amount_redcoupon, amount_third, amount_fbb, amount_voucher, 
    amount_voucher_individual, amount_voucher_redcoupon, amount_zero_tax, amount_coupon, 
    voucher_order_invoice_amount, voucher_invoice_amount, company_pre_pay, third_pay_channel, 
    amount_bank, account_type, account_sub_id, account_model, fb_order_snapshot, fb_order_name, 
    employee_id, company_id, count_voucher, max_voucher, voucher_company_id, order_type, 
    order_sub_type, biz_callback_url, callback_num, callback_next, deadline_time, client_version, 
    client_ip, device, currency, client_lon, client_lat, person_account_id, cancel_reason, 
    complete_time, common_json, third_txn_id, biz_pay_type, consumer_account_sub_type, 
    company_no_settle_price, amount_public_rule, operation_channel_type, customer_service_id, 
    customer_service_name, business_mode, invoice_provide_status, scene_invoice_type, 
    invoice_provide_type, invoice_provide_name, bank_account_no, bank_trans_no, bank_name, 
    redcoupon_can_pay, bank_company_amount, bank_redcoupon_amount, amount_reimburse_company, 
    amount_reimburse_self, order_payment_model, order_channel_type, is_ding, bank_personal_amount, 
    payment_company_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cashier_order_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from cashier_order_settlement
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_settlement
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_settlement (id, pay_status, create_time, 
      update_time, fb_order_id, cashier_txn_id, 
      fb_trade_id, root_order_id, order_root_type, 
      cashier_trade_type, amount_all, amount_personal, 
      amount_public, amount_company, amount_redcoupon, 
      amount_third, amount_fbb, amount_voucher, 
      amount_voucher_individual, amount_voucher_redcoupon, 
      amount_zero_tax, amount_coupon, voucher_order_invoice_amount, 
      voucher_invoice_amount, company_pre_pay, third_pay_channel, 
      amount_bank, account_type, account_sub_id, 
      account_model, fb_order_snapshot, fb_order_name, 
      employee_id, company_id, count_voucher, 
      max_voucher, voucher_company_id, order_type, 
      order_sub_type, biz_callback_url, callback_num, 
      callback_next, deadline_time, client_version, 
      client_ip, device, currency, 
      client_lon, client_lat, person_account_id, 
      cancel_reason, complete_time, common_json, 
      third_txn_id, biz_pay_type, consumer_account_sub_type, 
      company_no_settle_price, amount_public_rule, 
      operation_channel_type, customer_service_id, 
      customer_service_name, business_mode, invoice_provide_status, 
      scene_invoice_type, invoice_provide_type, invoice_provide_name, 
      bank_account_no, bank_trans_no, bank_name, 
      redcoupon_can_pay, bank_company_amount, bank_redcoupon_amount, 
      amount_reimburse_company, amount_reimburse_self, 
      order_payment_model, order_channel_type, is_ding, 
      bank_personal_amount, payment_company_id)
    values (#{id,jdbcType=VARCHAR}, #{payStatus,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{fbOrderId,jdbcType=VARCHAR}, #{cashierTxnId,jdbcType=VARCHAR}, 
      #{fbTradeId,jdbcType=VARCHAR}, #{rootOrderId,jdbcType=VARCHAR}, #{orderRootType,jdbcType=INTEGER}, 
      #{cashierTradeType,jdbcType=INTEGER}, #{amountAll,jdbcType=DECIMAL}, #{amountPersonal,jdbcType=DECIMAL}, 
      #{amountPublic,jdbcType=DECIMAL}, #{amountCompany,jdbcType=DECIMAL}, #{amountRedcoupon,jdbcType=DECIMAL}, 
      #{amountThird,jdbcType=DECIMAL}, #{amountFbb,jdbcType=DECIMAL}, #{amountVoucher,jdbcType=DECIMAL}, 
      #{amountVoucherIndividual,jdbcType=DECIMAL}, #{amountVoucherRedcoupon,jdbcType=DECIMAL}, 
      #{amountZeroTax,jdbcType=DECIMAL}, #{amountCoupon,jdbcType=DECIMAL}, #{voucherOrderInvoiceAmount,jdbcType=DECIMAL}, 
      #{voucherInvoiceAmount,jdbcType=DECIMAL}, #{companyPrePay,jdbcType=SMALLINT}, #{thirdPayChannel,jdbcType=VARCHAR}, 
      #{amountBank,jdbcType=DECIMAL}, #{accountType,jdbcType=SMALLINT}, #{accountSubId,jdbcType=VARCHAR}, 
      #{accountModel,jdbcType=INTEGER}, #{fbOrderSnapshot,jdbcType=VARCHAR}, #{fbOrderName,jdbcType=VARCHAR}, 
      #{employeeId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{countVoucher,jdbcType=INTEGER}, 
      #{maxVoucher,jdbcType=DECIMAL}, #{voucherCompanyId,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{orderSubType,jdbcType=INTEGER}, #{bizCallbackUrl,jdbcType=VARCHAR}, #{callbackNum,jdbcType=INTEGER}, 
      #{callbackNext,jdbcType=TIMESTAMP}, #{deadlineTime,jdbcType=TIMESTAMP}, #{clientVersion,jdbcType=VARCHAR}, 
      #{clientIp,jdbcType=VARCHAR}, #{device,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, 
      #{clientLon,jdbcType=DOUBLE}, #{clientLat,jdbcType=DOUBLE}, #{personAccountId,jdbcType=VARCHAR}, 
      #{cancelReason,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP}, #{commonJson,jdbcType=VARCHAR}, 
      #{thirdTxnId,jdbcType=VARCHAR}, #{bizPayType,jdbcType=VARCHAR}, #{consumerAccountSubType,jdbcType=SMALLINT}, 
      #{companyNoSettlePrice,jdbcType=DECIMAL}, #{amountPublicRule,jdbcType=DECIMAL}, 
      #{operationChannelType,jdbcType=INTEGER}, #{customerServiceId,jdbcType=VARCHAR}, 
      #{customerServiceName,jdbcType=VARCHAR}, #{businessMode,jdbcType=INTEGER}, #{invoiceProvideStatus,jdbcType=INTEGER}, 
      #{sceneInvoiceType,jdbcType=INTEGER}, #{invoiceProvideType,jdbcType=INTEGER}, #{invoiceProvideName,jdbcType=VARCHAR}, 
      #{bankAccountNo,jdbcType=VARCHAR}, #{bankTransNo,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{redcouponCanPay,jdbcType=INTEGER}, #{bankCompanyAmount,jdbcType=DECIMAL}, #{bankRedcouponAmount,jdbcType=DECIMAL}, 
      #{amountReimburseCompany,jdbcType=DECIMAL}, #{amountReimburseSelf,jdbcType=DECIMAL}, 
      #{orderPaymentModel,jdbcType=INTEGER}, #{orderChannelType,jdbcType=INTEGER}, #{isDing,jdbcType=TINYINT}, 
      #{bankPersonalAmount,jdbcType=DECIMAL}, #{paymentCompanyId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="cashierTxnId != null">
        cashier_txn_id,
      </if>
      <if test="fbTradeId != null">
        fb_trade_id,
      </if>
      <if test="rootOrderId != null">
        root_order_id,
      </if>
      <if test="orderRootType != null">
        order_root_type,
      </if>
      <if test="cashierTradeType != null">
        cashier_trade_type,
      </if>
      <if test="amountAll != null">
        amount_all,
      </if>
      <if test="amountPersonal != null">
        amount_personal,
      </if>
      <if test="amountPublic != null">
        amount_public,
      </if>
      <if test="amountCompany != null">
        amount_company,
      </if>
      <if test="amountRedcoupon != null">
        amount_redcoupon,
      </if>
      <if test="amountThird != null">
        amount_third,
      </if>
      <if test="amountFbb != null">
        amount_fbb,
      </if>
      <if test="amountVoucher != null">
        amount_voucher,
      </if>
      <if test="amountVoucherIndividual != null">
        amount_voucher_individual,
      </if>
      <if test="amountVoucherRedcoupon != null">
        amount_voucher_redcoupon,
      </if>
      <if test="amountZeroTax != null">
        amount_zero_tax,
      </if>
      <if test="amountCoupon != null">
        amount_coupon,
      </if>
      <if test="voucherOrderInvoiceAmount != null">
        voucher_order_invoice_amount,
      </if>
      <if test="voucherInvoiceAmount != null">
        voucher_invoice_amount,
      </if>
      <if test="companyPrePay != null">
        company_pre_pay,
      </if>
      <if test="thirdPayChannel != null">
        third_pay_channel,
      </if>
      <if test="amountBank != null">
        amount_bank,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="accountSubId != null">
        account_sub_id,
      </if>
      <if test="accountModel != null">
        account_model,
      </if>
      <if test="fbOrderSnapshot != null">
        fb_order_snapshot,
      </if>
      <if test="fbOrderName != null">
        fb_order_name,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="countVoucher != null">
        count_voucher,
      </if>
      <if test="maxVoucher != null">
        max_voucher,
      </if>
      <if test="voucherCompanyId != null">
        voucher_company_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderSubType != null">
        order_sub_type,
      </if>
      <if test="bizCallbackUrl != null">
        biz_callback_url,
      </if>
      <if test="callbackNum != null">
        callback_num,
      </if>
      <if test="callbackNext != null">
        callback_next,
      </if>
      <if test="deadlineTime != null">
        deadline_time,
      </if>
      <if test="clientVersion != null">
        client_version,
      </if>
      <if test="clientIp != null">
        client_ip,
      </if>
      <if test="device != null">
        device,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="clientLon != null">
        client_lon,
      </if>
      <if test="clientLat != null">
        client_lat,
      </if>
      <if test="personAccountId != null">
        person_account_id,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="commonJson != null">
        common_json,
      </if>
      <if test="thirdTxnId != null">
        third_txn_id,
      </if>
      <if test="bizPayType != null">
        biz_pay_type,
      </if>
      <if test="consumerAccountSubType != null">
        consumer_account_sub_type,
      </if>
      <if test="companyNoSettlePrice != null">
        company_no_settle_price,
      </if>
      <if test="amountPublicRule != null">
        amount_public_rule,
      </if>
      <if test="operationChannelType != null">
        operation_channel_type,
      </if>
      <if test="customerServiceId != null">
        customer_service_id,
      </if>
      <if test="customerServiceName != null">
        customer_service_name,
      </if>
      <if test="businessMode != null">
        business_mode,
      </if>
      <if test="invoiceProvideStatus != null">
        invoice_provide_status,
      </if>
      <if test="sceneInvoiceType != null">
        scene_invoice_type,
      </if>
      <if test="invoiceProvideType != null">
        invoice_provide_type,
      </if>
      <if test="invoiceProvideName != null">
        invoice_provide_name,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="bankTransNo != null">
        bank_trans_no,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="redcouponCanPay != null">
        redcoupon_can_pay,
      </if>
      <if test="bankCompanyAmount != null">
        bank_company_amount,
      </if>
      <if test="bankRedcouponAmount != null">
        bank_redcoupon_amount,
      </if>
      <if test="amountReimburseCompany != null">
        amount_reimburse_company,
      </if>
      <if test="amountReimburseSelf != null">
        amount_reimburse_self,
      </if>
      <if test="orderPaymentModel != null">
        order_payment_model,
      </if>
      <if test="orderChannelType != null">
        order_channel_type,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="bankPersonalAmount != null">
        bank_personal_amount,
      </if>
      <if test="paymentCompanyId != null">
        payment_company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="cashierTxnId != null">
        #{cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbTradeId != null">
        #{fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderRootType != null">
        #{orderRootType,jdbcType=INTEGER},
      </if>
      <if test="cashierTradeType != null">
        #{cashierTradeType,jdbcType=INTEGER},
      </if>
      <if test="amountAll != null">
        #{amountAll,jdbcType=DECIMAL},
      </if>
      <if test="amountPersonal != null">
        #{amountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="amountPublic != null">
        #{amountPublic,jdbcType=DECIMAL},
      </if>
      <if test="amountCompany != null">
        #{amountCompany,jdbcType=DECIMAL},
      </if>
      <if test="amountRedcoupon != null">
        #{amountRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="amountThird != null">
        #{amountThird,jdbcType=DECIMAL},
      </if>
      <if test="amountFbb != null">
        #{amountFbb,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucher != null">
        #{amountVoucher,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucherIndividual != null">
        #{amountVoucherIndividual,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucherRedcoupon != null">
        #{amountVoucherRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="amountZeroTax != null">
        #{amountZeroTax,jdbcType=DECIMAL},
      </if>
      <if test="amountCoupon != null">
        #{amountCoupon,jdbcType=DECIMAL},
      </if>
      <if test="voucherOrderInvoiceAmount != null">
        #{voucherOrderInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="voucherInvoiceAmount != null">
        #{voucherInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="companyPrePay != null">
        #{companyPrePay,jdbcType=SMALLINT},
      </if>
      <if test="thirdPayChannel != null">
        #{thirdPayChannel,jdbcType=VARCHAR},
      </if>
      <if test="amountBank != null">
        #{amountBank,jdbcType=DECIMAL},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=SMALLINT},
      </if>
      <if test="accountSubId != null">
        #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="accountModel != null">
        #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="fbOrderSnapshot != null">
        #{fbOrderSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderName != null">
        #{fbOrderName,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="countVoucher != null">
        #{countVoucher,jdbcType=INTEGER},
      </if>
      <if test="maxVoucher != null">
        #{maxVoucher,jdbcType=DECIMAL},
      </if>
      <if test="voucherCompanyId != null">
        #{voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderSubType != null">
        #{orderSubType,jdbcType=INTEGER},
      </if>
      <if test="bizCallbackUrl != null">
        #{bizCallbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="callbackNum != null">
        #{callbackNum,jdbcType=INTEGER},
      </if>
      <if test="callbackNext != null">
        #{callbackNext,jdbcType=TIMESTAMP},
      </if>
      <if test="deadlineTime != null">
        #{deadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clientVersion != null">
        #{clientVersion,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        #{device,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="clientLon != null">
        #{clientLon,jdbcType=DOUBLE},
      </if>
      <if test="clientLat != null">
        #{clientLat,jdbcType=DOUBLE},
      </if>
      <if test="personAccountId != null">
        #{personAccountId,jdbcType=VARCHAR},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commonJson != null">
        #{commonJson,jdbcType=VARCHAR},
      </if>
      <if test="thirdTxnId != null">
        #{thirdTxnId,jdbcType=VARCHAR},
      </if>
      <if test="bizPayType != null">
        #{bizPayType,jdbcType=VARCHAR},
      </if>
      <if test="consumerAccountSubType != null">
        #{consumerAccountSubType,jdbcType=SMALLINT},
      </if>
      <if test="companyNoSettlePrice != null">
        #{companyNoSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="amountPublicRule != null">
        #{amountPublicRule,jdbcType=DECIMAL},
      </if>
      <if test="operationChannelType != null">
        #{operationChannelType,jdbcType=INTEGER},
      </if>
      <if test="customerServiceId != null">
        #{customerServiceId,jdbcType=VARCHAR},
      </if>
      <if test="customerServiceName != null">
        #{customerServiceName,jdbcType=VARCHAR},
      </if>
      <if test="businessMode != null">
        #{businessMode,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideStatus != null">
        #{invoiceProvideStatus,jdbcType=INTEGER},
      </if>
      <if test="sceneInvoiceType != null">
        #{sceneInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideType != null">
        #{invoiceProvideType,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideName != null">
        #{invoiceProvideName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankTransNo != null">
        #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="redcouponCanPay != null">
        #{redcouponCanPay,jdbcType=INTEGER},
      </if>
      <if test="bankCompanyAmount != null">
        #{bankCompanyAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankRedcouponAmount != null">
        #{bankRedcouponAmount,jdbcType=DECIMAL},
      </if>
      <if test="amountReimburseCompany != null">
        #{amountReimburseCompany,jdbcType=DECIMAL},
      </if>
      <if test="amountReimburseSelf != null">
        #{amountReimburseSelf,jdbcType=DECIMAL},
      </if>
      <if test="orderPaymentModel != null">
        #{orderPaymentModel,jdbcType=INTEGER},
      </if>
      <if test="orderChannelType != null">
        #{orderChannelType,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
      <if test="bankPersonalAmount != null">
        #{bankPersonalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentCompanyId != null">
        #{paymentCompanyId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from cashier_order_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.cashierTxnId != null">
        cashier_txn_id = #{record.cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbTradeId != null">
        fb_trade_id = #{record.fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="record.rootOrderId != null">
        root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderRootType != null">
        order_root_type = #{record.orderRootType,jdbcType=INTEGER},
      </if>
      <if test="record.cashierTradeType != null">
        cashier_trade_type = #{record.cashierTradeType,jdbcType=INTEGER},
      </if>
      <if test="record.amountAll != null">
        amount_all = #{record.amountAll,jdbcType=DECIMAL},
      </if>
      <if test="record.amountPersonal != null">
        amount_personal = #{record.amountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="record.amountPublic != null">
        amount_public = #{record.amountPublic,jdbcType=DECIMAL},
      </if>
      <if test="record.amountCompany != null">
        amount_company = #{record.amountCompany,jdbcType=DECIMAL},
      </if>
      <if test="record.amountRedcoupon != null">
        amount_redcoupon = #{record.amountRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="record.amountThird != null">
        amount_third = #{record.amountThird,jdbcType=DECIMAL},
      </if>
      <if test="record.amountFbb != null">
        amount_fbb = #{record.amountFbb,jdbcType=DECIMAL},
      </if>
      <if test="record.amountVoucher != null">
        amount_voucher = #{record.amountVoucher,jdbcType=DECIMAL},
      </if>
      <if test="record.amountVoucherIndividual != null">
        amount_voucher_individual = #{record.amountVoucherIndividual,jdbcType=DECIMAL},
      </if>
      <if test="record.amountVoucherRedcoupon != null">
        amount_voucher_redcoupon = #{record.amountVoucherRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="record.amountZeroTax != null">
        amount_zero_tax = #{record.amountZeroTax,jdbcType=DECIMAL},
      </if>
      <if test="record.amountCoupon != null">
        amount_coupon = #{record.amountCoupon,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherOrderInvoiceAmount != null">
        voucher_order_invoice_amount = #{record.voucherOrderInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherInvoiceAmount != null">
        voucher_invoice_amount = #{record.voucherInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.companyPrePay != null">
        company_pre_pay = #{record.companyPrePay,jdbcType=SMALLINT},
      </if>
      <if test="record.thirdPayChannel != null">
        third_pay_channel = #{record.thirdPayChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.amountBank != null">
        amount_bank = #{record.amountBank,jdbcType=DECIMAL},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=SMALLINT},
      </if>
      <if test="record.accountSubId != null">
        account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountModel != null">
        account_model = #{record.accountModel,jdbcType=INTEGER},
      </if>
      <if test="record.fbOrderSnapshot != null">
        fb_order_snapshot = #{record.fbOrderSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderName != null">
        fb_order_name = #{record.fbOrderName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.countVoucher != null">
        count_voucher = #{record.countVoucher,jdbcType=INTEGER},
      </if>
      <if test="record.maxVoucher != null">
        max_voucher = #{record.maxVoucher,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherCompanyId != null">
        voucher_company_id = #{record.voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderSubType != null">
        order_sub_type = #{record.orderSubType,jdbcType=INTEGER},
      </if>
      <if test="record.bizCallbackUrl != null">
        biz_callback_url = #{record.bizCallbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.callbackNum != null">
        callback_num = #{record.callbackNum,jdbcType=INTEGER},
      </if>
      <if test="record.callbackNext != null">
        callback_next = #{record.callbackNext,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deadlineTime != null">
        deadline_time = #{record.deadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.clientVersion != null">
        client_version = #{record.clientVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.clientIp != null">
        client_ip = #{record.clientIp,jdbcType=VARCHAR},
      </if>
      <if test="record.device != null">
        device = #{record.device,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.clientLon != null">
        client_lon = #{record.clientLon,jdbcType=DOUBLE},
      </if>
      <if test="record.clientLat != null">
        client_lat = #{record.clientLat,jdbcType=DOUBLE},
      </if>
      <if test="record.personAccountId != null">
        person_account_id = #{record.personAccountId,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelReason != null">
        cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.commonJson != null">
        common_json = #{record.commonJson,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdTxnId != null">
        third_txn_id = #{record.thirdTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.bizPayType != null">
        biz_pay_type = #{record.bizPayType,jdbcType=VARCHAR},
      </if>
      <if test="record.consumerAccountSubType != null">
        consumer_account_sub_type = #{record.consumerAccountSubType,jdbcType=SMALLINT},
      </if>
      <if test="record.companyNoSettlePrice != null">
        company_no_settle_price = #{record.companyNoSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.amountPublicRule != null">
        amount_public_rule = #{record.amountPublicRule,jdbcType=DECIMAL},
      </if>
      <if test="record.operationChannelType != null">
        operation_channel_type = #{record.operationChannelType,jdbcType=INTEGER},
      </if>
      <if test="record.customerServiceId != null">
        customer_service_id = #{record.customerServiceId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerServiceName != null">
        customer_service_name = #{record.customerServiceName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessMode != null">
        business_mode = #{record.businessMode,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceProvideStatus != null">
        invoice_provide_status = #{record.invoiceProvideStatus,jdbcType=INTEGER},
      </if>
      <if test="record.sceneInvoiceType != null">
        scene_invoice_type = #{record.sceneInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceProvideType != null">
        invoice_provide_type = #{record.invoiceProvideType,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceProvideName != null">
        invoice_provide_name = #{record.invoiceProvideName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankTransNo != null">
        bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.redcouponCanPay != null">
        redcoupon_can_pay = #{record.redcouponCanPay,jdbcType=INTEGER},
      </if>
      <if test="record.bankCompanyAmount != null">
        bank_company_amount = #{record.bankCompanyAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.bankRedcouponAmount != null">
        bank_redcoupon_amount = #{record.bankRedcouponAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.amountReimburseCompany != null">
        amount_reimburse_company = #{record.amountReimburseCompany,jdbcType=DECIMAL},
      </if>
      <if test="record.amountReimburseSelf != null">
        amount_reimburse_self = #{record.amountReimburseSelf,jdbcType=DECIMAL},
      </if>
      <if test="record.orderPaymentModel != null">
        order_payment_model = #{record.orderPaymentModel,jdbcType=INTEGER},
      </if>
      <if test="record.orderChannelType != null">
        order_channel_type = #{record.orderChannelType,jdbcType=INTEGER},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
      <if test="record.bankPersonalAmount != null">
        bank_personal_amount = #{record.bankPersonalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.paymentCompanyId != null">
        payment_company_id = #{record.paymentCompanyId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement
    set id = #{record.id,jdbcType=VARCHAR},
      pay_status = #{record.payStatus,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      cashier_txn_id = #{record.cashierTxnId,jdbcType=VARCHAR},
      fb_trade_id = #{record.fbTradeId,jdbcType=VARCHAR},
      root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      order_root_type = #{record.orderRootType,jdbcType=INTEGER},
      cashier_trade_type = #{record.cashierTradeType,jdbcType=INTEGER},
      amount_all = #{record.amountAll,jdbcType=DECIMAL},
      amount_personal = #{record.amountPersonal,jdbcType=DECIMAL},
      amount_public = #{record.amountPublic,jdbcType=DECIMAL},
      amount_company = #{record.amountCompany,jdbcType=DECIMAL},
      amount_redcoupon = #{record.amountRedcoupon,jdbcType=DECIMAL},
      amount_third = #{record.amountThird,jdbcType=DECIMAL},
      amount_fbb = #{record.amountFbb,jdbcType=DECIMAL},
      amount_voucher = #{record.amountVoucher,jdbcType=DECIMAL},
      amount_voucher_individual = #{record.amountVoucherIndividual,jdbcType=DECIMAL},
      amount_voucher_redcoupon = #{record.amountVoucherRedcoupon,jdbcType=DECIMAL},
      amount_zero_tax = #{record.amountZeroTax,jdbcType=DECIMAL},
      amount_coupon = #{record.amountCoupon,jdbcType=DECIMAL},
      voucher_order_invoice_amount = #{record.voucherOrderInvoiceAmount,jdbcType=DECIMAL},
      voucher_invoice_amount = #{record.voucherInvoiceAmount,jdbcType=DECIMAL},
      company_pre_pay = #{record.companyPrePay,jdbcType=SMALLINT},
      third_pay_channel = #{record.thirdPayChannel,jdbcType=VARCHAR},
      amount_bank = #{record.amountBank,jdbcType=DECIMAL},
      account_type = #{record.accountType,jdbcType=SMALLINT},
      account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      account_model = #{record.accountModel,jdbcType=INTEGER},
      fb_order_snapshot = #{record.fbOrderSnapshot,jdbcType=VARCHAR},
      fb_order_name = #{record.fbOrderName,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      count_voucher = #{record.countVoucher,jdbcType=INTEGER},
      max_voucher = #{record.maxVoucher,jdbcType=DECIMAL},
      voucher_company_id = #{record.voucherCompanyId,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_sub_type = #{record.orderSubType,jdbcType=INTEGER},
      biz_callback_url = #{record.bizCallbackUrl,jdbcType=VARCHAR},
      callback_num = #{record.callbackNum,jdbcType=INTEGER},
      callback_next = #{record.callbackNext,jdbcType=TIMESTAMP},
      deadline_time = #{record.deadlineTime,jdbcType=TIMESTAMP},
      client_version = #{record.clientVersion,jdbcType=VARCHAR},
      client_ip = #{record.clientIp,jdbcType=VARCHAR},
      device = #{record.device,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      client_lon = #{record.clientLon,jdbcType=DOUBLE},
      client_lat = #{record.clientLat,jdbcType=DOUBLE},
      person_account_id = #{record.personAccountId,jdbcType=VARCHAR},
      cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      common_json = #{record.commonJson,jdbcType=VARCHAR},
      third_txn_id = #{record.thirdTxnId,jdbcType=VARCHAR},
      biz_pay_type = #{record.bizPayType,jdbcType=VARCHAR},
      consumer_account_sub_type = #{record.consumerAccountSubType,jdbcType=SMALLINT},
      company_no_settle_price = #{record.companyNoSettlePrice,jdbcType=DECIMAL},
      amount_public_rule = #{record.amountPublicRule,jdbcType=DECIMAL},
      operation_channel_type = #{record.operationChannelType,jdbcType=INTEGER},
      customer_service_id = #{record.customerServiceId,jdbcType=VARCHAR},
      customer_service_name = #{record.customerServiceName,jdbcType=VARCHAR},
      business_mode = #{record.businessMode,jdbcType=INTEGER},
      invoice_provide_status = #{record.invoiceProvideStatus,jdbcType=INTEGER},
      scene_invoice_type = #{record.sceneInvoiceType,jdbcType=INTEGER},
      invoice_provide_type = #{record.invoiceProvideType,jdbcType=INTEGER},
      invoice_provide_name = #{record.invoiceProvideName,jdbcType=VARCHAR},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      redcoupon_can_pay = #{record.redcouponCanPay,jdbcType=INTEGER},
      bank_company_amount = #{record.bankCompanyAmount,jdbcType=DECIMAL},
      bank_redcoupon_amount = #{record.bankRedcouponAmount,jdbcType=DECIMAL},
      amount_reimburse_company = #{record.amountReimburseCompany,jdbcType=DECIMAL},
      amount_reimburse_self = #{record.amountReimburseSelf,jdbcType=DECIMAL},
      order_payment_model = #{record.orderPaymentModel,jdbcType=INTEGER},
      order_channel_type = #{record.orderChannelType,jdbcType=INTEGER},
      is_ding = #{record.isDing,jdbcType=TINYINT},
      bank_personal_amount = #{record.bankPersonalAmount,jdbcType=DECIMAL},
      payment_company_id = #{record.paymentCompanyId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement
    <set>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="cashierTxnId != null">
        cashier_txn_id = #{cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbTradeId != null">
        fb_trade_id = #{fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderRootType != null">
        order_root_type = #{orderRootType,jdbcType=INTEGER},
      </if>
      <if test="cashierTradeType != null">
        cashier_trade_type = #{cashierTradeType,jdbcType=INTEGER},
      </if>
      <if test="amountAll != null">
        amount_all = #{amountAll,jdbcType=DECIMAL},
      </if>
      <if test="amountPersonal != null">
        amount_personal = #{amountPersonal,jdbcType=DECIMAL},
      </if>
      <if test="amountPublic != null">
        amount_public = #{amountPublic,jdbcType=DECIMAL},
      </if>
      <if test="amountCompany != null">
        amount_company = #{amountCompany,jdbcType=DECIMAL},
      </if>
      <if test="amountRedcoupon != null">
        amount_redcoupon = #{amountRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="amountThird != null">
        amount_third = #{amountThird,jdbcType=DECIMAL},
      </if>
      <if test="amountFbb != null">
        amount_fbb = #{amountFbb,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucher != null">
        amount_voucher = #{amountVoucher,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucherIndividual != null">
        amount_voucher_individual = #{amountVoucherIndividual,jdbcType=DECIMAL},
      </if>
      <if test="amountVoucherRedcoupon != null">
        amount_voucher_redcoupon = #{amountVoucherRedcoupon,jdbcType=DECIMAL},
      </if>
      <if test="amountZeroTax != null">
        amount_zero_tax = #{amountZeroTax,jdbcType=DECIMAL},
      </if>
      <if test="amountCoupon != null">
        amount_coupon = #{amountCoupon,jdbcType=DECIMAL},
      </if>
      <if test="voucherOrderInvoiceAmount != null">
        voucher_order_invoice_amount = #{voucherOrderInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="voucherInvoiceAmount != null">
        voucher_invoice_amount = #{voucherInvoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="companyPrePay != null">
        company_pre_pay = #{companyPrePay,jdbcType=SMALLINT},
      </if>
      <if test="thirdPayChannel != null">
        third_pay_channel = #{thirdPayChannel,jdbcType=VARCHAR},
      </if>
      <if test="amountBank != null">
        amount_bank = #{amountBank,jdbcType=DECIMAL},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=SMALLINT},
      </if>
      <if test="accountSubId != null">
        account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="accountModel != null">
        account_model = #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="fbOrderSnapshot != null">
        fb_order_snapshot = #{fbOrderSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderName != null">
        fb_order_name = #{fbOrderName,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="countVoucher != null">
        count_voucher = #{countVoucher,jdbcType=INTEGER},
      </if>
      <if test="maxVoucher != null">
        max_voucher = #{maxVoucher,jdbcType=DECIMAL},
      </if>
      <if test="voucherCompanyId != null">
        voucher_company_id = #{voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderSubType != null">
        order_sub_type = #{orderSubType,jdbcType=INTEGER},
      </if>
      <if test="bizCallbackUrl != null">
        biz_callback_url = #{bizCallbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="callbackNum != null">
        callback_num = #{callbackNum,jdbcType=INTEGER},
      </if>
      <if test="callbackNext != null">
        callback_next = #{callbackNext,jdbcType=TIMESTAMP},
      </if>
      <if test="deadlineTime != null">
        deadline_time = #{deadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clientVersion != null">
        client_version = #{clientVersion,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        client_ip = #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        device = #{device,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="clientLon != null">
        client_lon = #{clientLon,jdbcType=DOUBLE},
      </if>
      <if test="clientLat != null">
        client_lat = #{clientLat,jdbcType=DOUBLE},
      </if>
      <if test="personAccountId != null">
        person_account_id = #{personAccountId,jdbcType=VARCHAR},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commonJson != null">
        common_json = #{commonJson,jdbcType=VARCHAR},
      </if>
      <if test="thirdTxnId != null">
        third_txn_id = #{thirdTxnId,jdbcType=VARCHAR},
      </if>
      <if test="bizPayType != null">
        biz_pay_type = #{bizPayType,jdbcType=VARCHAR},
      </if>
      <if test="consumerAccountSubType != null">
        consumer_account_sub_type = #{consumerAccountSubType,jdbcType=SMALLINT},
      </if>
      <if test="companyNoSettlePrice != null">
        company_no_settle_price = #{companyNoSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="amountPublicRule != null">
        amount_public_rule = #{amountPublicRule,jdbcType=DECIMAL},
      </if>
      <if test="operationChannelType != null">
        operation_channel_type = #{operationChannelType,jdbcType=INTEGER},
      </if>
      <if test="customerServiceId != null">
        customer_service_id = #{customerServiceId,jdbcType=VARCHAR},
      </if>
      <if test="customerServiceName != null">
        customer_service_name = #{customerServiceName,jdbcType=VARCHAR},
      </if>
      <if test="businessMode != null">
        business_mode = #{businessMode,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideStatus != null">
        invoice_provide_status = #{invoiceProvideStatus,jdbcType=INTEGER},
      </if>
      <if test="sceneInvoiceType != null">
        scene_invoice_type = #{sceneInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideType != null">
        invoice_provide_type = #{invoiceProvideType,jdbcType=INTEGER},
      </if>
      <if test="invoiceProvideName != null">
        invoice_provide_name = #{invoiceProvideName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankTransNo != null">
        bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="redcouponCanPay != null">
        redcoupon_can_pay = #{redcouponCanPay,jdbcType=INTEGER},
      </if>
      <if test="bankCompanyAmount != null">
        bank_company_amount = #{bankCompanyAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankRedcouponAmount != null">
        bank_redcoupon_amount = #{bankRedcouponAmount,jdbcType=DECIMAL},
      </if>
      <if test="amountReimburseCompany != null">
        amount_reimburse_company = #{amountReimburseCompany,jdbcType=DECIMAL},
      </if>
      <if test="amountReimburseSelf != null">
        amount_reimburse_self = #{amountReimburseSelf,jdbcType=DECIMAL},
      </if>
      <if test="orderPaymentModel != null">
        order_payment_model = #{orderPaymentModel,jdbcType=INTEGER},
      </if>
      <if test="orderChannelType != null">
        order_channel_type = #{orderChannelType,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
      <if test="bankPersonalAmount != null">
        bank_personal_amount = #{bankPersonalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentCompanyId != null">
        payment_company_id = #{paymentCompanyId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement
    set pay_status = #{payStatus,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      cashier_txn_id = #{cashierTxnId,jdbcType=VARCHAR},
      fb_trade_id = #{fbTradeId,jdbcType=VARCHAR},
      root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      order_root_type = #{orderRootType,jdbcType=INTEGER},
      cashier_trade_type = #{cashierTradeType,jdbcType=INTEGER},
      amount_all = #{amountAll,jdbcType=DECIMAL},
      amount_personal = #{amountPersonal,jdbcType=DECIMAL},
      amount_public = #{amountPublic,jdbcType=DECIMAL},
      amount_company = #{amountCompany,jdbcType=DECIMAL},
      amount_redcoupon = #{amountRedcoupon,jdbcType=DECIMAL},
      amount_third = #{amountThird,jdbcType=DECIMAL},
      amount_fbb = #{amountFbb,jdbcType=DECIMAL},
      amount_voucher = #{amountVoucher,jdbcType=DECIMAL},
      amount_voucher_individual = #{amountVoucherIndividual,jdbcType=DECIMAL},
      amount_voucher_redcoupon = #{amountVoucherRedcoupon,jdbcType=DECIMAL},
      amount_zero_tax = #{amountZeroTax,jdbcType=DECIMAL},
      amount_coupon = #{amountCoupon,jdbcType=DECIMAL},
      voucher_order_invoice_amount = #{voucherOrderInvoiceAmount,jdbcType=DECIMAL},
      voucher_invoice_amount = #{voucherInvoiceAmount,jdbcType=DECIMAL},
      company_pre_pay = #{companyPrePay,jdbcType=SMALLINT},
      third_pay_channel = #{thirdPayChannel,jdbcType=VARCHAR},
      amount_bank = #{amountBank,jdbcType=DECIMAL},
      account_type = #{accountType,jdbcType=SMALLINT},
      account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      account_model = #{accountModel,jdbcType=INTEGER},
      fb_order_snapshot = #{fbOrderSnapshot,jdbcType=VARCHAR},
      fb_order_name = #{fbOrderName,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      count_voucher = #{countVoucher,jdbcType=INTEGER},
      max_voucher = #{maxVoucher,jdbcType=DECIMAL},
      voucher_company_id = #{voucherCompanyId,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      order_sub_type = #{orderSubType,jdbcType=INTEGER},
      biz_callback_url = #{bizCallbackUrl,jdbcType=VARCHAR},
      callback_num = #{callbackNum,jdbcType=INTEGER},
      callback_next = #{callbackNext,jdbcType=TIMESTAMP},
      deadline_time = #{deadlineTime,jdbcType=TIMESTAMP},
      client_version = #{clientVersion,jdbcType=VARCHAR},
      client_ip = #{clientIp,jdbcType=VARCHAR},
      device = #{device,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      client_lon = #{clientLon,jdbcType=DOUBLE},
      client_lat = #{clientLat,jdbcType=DOUBLE},
      person_account_id = #{personAccountId,jdbcType=VARCHAR},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      common_json = #{commonJson,jdbcType=VARCHAR},
      third_txn_id = #{thirdTxnId,jdbcType=VARCHAR},
      biz_pay_type = #{bizPayType,jdbcType=VARCHAR},
      consumer_account_sub_type = #{consumerAccountSubType,jdbcType=SMALLINT},
      company_no_settle_price = #{companyNoSettlePrice,jdbcType=DECIMAL},
      amount_public_rule = #{amountPublicRule,jdbcType=DECIMAL},
      operation_channel_type = #{operationChannelType,jdbcType=INTEGER},
      customer_service_id = #{customerServiceId,jdbcType=VARCHAR},
      customer_service_name = #{customerServiceName,jdbcType=VARCHAR},
      business_mode = #{businessMode,jdbcType=INTEGER},
      invoice_provide_status = #{invoiceProvideStatus,jdbcType=INTEGER},
      scene_invoice_type = #{sceneInvoiceType,jdbcType=INTEGER},
      invoice_provide_type = #{invoiceProvideType,jdbcType=INTEGER},
      invoice_provide_name = #{invoiceProvideName,jdbcType=VARCHAR},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      redcoupon_can_pay = #{redcouponCanPay,jdbcType=INTEGER},
      bank_company_amount = #{bankCompanyAmount,jdbcType=DECIMAL},
      bank_redcoupon_amount = #{bankRedcouponAmount,jdbcType=DECIMAL},
      amount_reimburse_company = #{amountReimburseCompany,jdbcType=DECIMAL},
      amount_reimburse_self = #{amountReimburseSelf,jdbcType=DECIMAL},
      order_payment_model = #{orderPaymentModel,jdbcType=INTEGER},
      order_channel_type = #{orderChannelType,jdbcType=INTEGER},
      is_ding = #{isDing,jdbcType=TINYINT},
      bank_personal_amount = #{bankPersonalAmount,jdbcType=DECIMAL},
      payment_company_id = #{paymentCompanyId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectPageByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
     ca.*
    from cashier_order_settlement  ca inner join (
    select id from cashier_order_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
    ) ca1
     on ca.id=ca1.id;
  </select>

  <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_order_settlement
    where id = #{id,jdbcType=VARCHAR}
    for update
  </select>

  <!--查询风控拦截的订单-->
  <select id="selectThirdRiskByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_order_settlement
    where pay_status = #{payStatus}
    and order_root_type = #{orderRootType}
    <if test="id != null">
     and id > #{id}
    </if>
    order by id asc limit #{pageSize}
  </select>

  <update id="updatePayStatusByFbOrderId">
    update cashier_order_settlement
    set pay_status = #{payStatus}
    where fb_order_id = #{fbOrderId}
  </update>
</mapper>