<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderRefundSettlementPayMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="refund_status" jdbcType="SMALLINT" property="refundStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="cashier_txn_id" jdbcType="VARCHAR" property="cashierTxnId" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="fb_trade_id" jdbcType="VARCHAR" property="fbTradeId" />
    <result column="root_order_id" jdbcType="VARCHAR" property="rootOrderId" />
    <result column="voucher_id" jdbcType="VARCHAR" property="voucherId" />
    <result column="refund_pay_txn_id" jdbcType="VARCHAR" property="refundPayTxnId" />
    <result column="refund_txn_id" jdbcType="VARCHAR" property="refundTxnId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
    <result column="voucher_ex" jdbcType="VARCHAR" property="voucherEx" />
    <result column="voucher_company_id" jdbcType="VARCHAR" property="voucherCompanyId" />
    <result column="xe_status" jdbcType="INTEGER" property="xeStatus" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_redcoupon_refund_amount" jdbcType="DECIMAL" property="bankRedcouponRefundAmount" />
    <result column="bank_company_refund_amount" jdbcType="DECIMAL" property="bankCompanyRefundAmount" />
    <result column="voucher_invoice_type" jdbcType="INTEGER" property="voucherInvoiceType" />
    <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, refund_status, create_time, update_time, pay_channel, refund_amount, cashier_txn_id, 
    fb_order_id, fb_trade_id, root_order_id, voucher_id, refund_pay_txn_id, refund_txn_id, 
    company_id, employee_id, voucher_name, voucher_ex, voucher_company_id, xe_status, 
    bank_account_no, bank_trans_no, bank_name, bank_redcoupon_refund_amount, bank_company_refund_amount, 
    voucher_invoice_type, deduction_account_type, account_model, account_sub_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementPayExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cashier_order_refund_settlement_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from cashier_order_refund_settlement_pay
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_refund_settlement_pay
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementPayExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_refund_settlement_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_refund_settlement_pay (id, refund_status, create_time, 
      update_time, pay_channel, refund_amount, 
      cashier_txn_id, fb_order_id, fb_trade_id, 
      root_order_id, voucher_id, refund_pay_txn_id, 
      refund_txn_id, company_id, employee_id, 
      voucher_name, voucher_ex, voucher_company_id, 
      xe_status, bank_account_no, bank_trans_no, 
      bank_name, bank_redcoupon_refund_amount, bank_company_refund_amount, 
      voucher_invoice_type, deduction_account_type, 
      account_model, account_sub_id)
    values (#{id,jdbcType=VARCHAR}, #{refundStatus,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{payChannel,jdbcType=VARCHAR}, #{refundAmount,jdbcType=DECIMAL}, 
      #{cashierTxnId,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{fbTradeId,jdbcType=VARCHAR}, 
      #{rootOrderId,jdbcType=VARCHAR}, #{voucherId,jdbcType=VARCHAR}, #{refundPayTxnId,jdbcType=VARCHAR}, 
      #{refundTxnId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, 
      #{voucherName,jdbcType=VARCHAR}, #{voucherEx,jdbcType=VARCHAR}, #{voucherCompanyId,jdbcType=VARCHAR}, 
      #{xeStatus,jdbcType=INTEGER}, #{bankAccountNo,jdbcType=VARCHAR}, #{bankTransNo,jdbcType=VARCHAR}, 
      #{bankName,jdbcType=VARCHAR}, #{bankRedcouponRefundAmount,jdbcType=DECIMAL}, #{bankCompanyRefundAmount,jdbcType=DECIMAL}, 
      #{voucherInvoiceType,jdbcType=INTEGER}, #{deductionAccountType,jdbcType=INTEGER}, 
      #{accountModel,jdbcType=INTEGER}, #{accountSubId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_refund_settlement_pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="payChannel != null">
        pay_channel,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="cashierTxnId != null">
        cashier_txn_id,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="fbTradeId != null">
        fb_trade_id,
      </if>
      <if test="rootOrderId != null">
        root_order_id,
      </if>
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="refundPayTxnId != null">
        refund_pay_txn_id,
      </if>
      <if test="refundTxnId != null">
        refund_txn_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="voucherName != null">
        voucher_name,
      </if>
      <if test="voucherEx != null">
        voucher_ex,
      </if>
      <if test="voucherCompanyId != null">
        voucher_company_id,
      </if>
      <if test="xeStatus != null">
        xe_status,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="bankTransNo != null">
        bank_trans_no,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankRedcouponRefundAmount != null">
        bank_redcoupon_refund_amount,
      </if>
      <if test="bankCompanyRefundAmount != null">
        bank_company_refund_amount,
      </if>
      <if test="voucherInvoiceType != null">
        voucher_invoice_type,
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type,
      </if>
      <if test="accountModel != null">
        account_model,
      </if>
      <if test="accountSubId != null">
        account_sub_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashierTxnId != null">
        #{cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fbTradeId != null">
        #{fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        #{voucherId,jdbcType=VARCHAR},
      </if>
      <if test="refundPayTxnId != null">
        #{refundPayTxnId,jdbcType=VARCHAR},
      </if>
      <if test="refundTxnId != null">
        #{refundTxnId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherEx != null">
        #{voucherEx,jdbcType=VARCHAR},
      </if>
      <if test="voucherCompanyId != null">
        #{voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="xeStatus != null">
        #{xeStatus,jdbcType=INTEGER},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankTransNo != null">
        #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankRedcouponRefundAmount != null">
        #{bankRedcouponRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankCompanyRefundAmount != null">
        #{bankCompanyRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="voucherInvoiceType != null">
        #{voucherInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="deductionAccountType != null">
        #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        #{accountSubId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementPayExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from cashier_order_refund_settlement_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_refund_settlement_pay
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.refundStatus != null">
        refund_status = #{record.refundStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payChannel != null">
        pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.refundAmount != null">
        refund_amount = #{record.refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.cashierTxnId != null">
        cashier_txn_id = #{record.cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbTradeId != null">
        fb_trade_id = #{record.fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="record.rootOrderId != null">
        root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherId != null">
        voucher_id = #{record.voucherId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundPayTxnId != null">
        refund_pay_txn_id = #{record.refundPayTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundTxnId != null">
        refund_txn_id = #{record.refundTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherName != null">
        voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherEx != null">
        voucher_ex = #{record.voucherEx,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherCompanyId != null">
        voucher_company_id = #{record.voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.xeStatus != null">
        xe_status = #{record.xeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankTransNo != null">
        bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankRedcouponRefundAmount != null">
        bank_redcoupon_refund_amount = #{record.bankRedcouponRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.bankCompanyRefundAmount != null">
        bank_company_refund_amount = #{record.bankCompanyRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherInvoiceType != null">
        voucher_invoice_type = #{record.voucherInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.deductionAccountType != null">
        deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.accountModel != null">
        account_model = #{record.accountModel,jdbcType=INTEGER},
      </if>
      <if test="record.accountSubId != null">
        account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_refund_settlement_pay
    set id = #{record.id,jdbcType=VARCHAR},
      refund_status = #{record.refundStatus,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      refund_amount = #{record.refundAmount,jdbcType=DECIMAL},
      cashier_txn_id = #{record.cashierTxnId,jdbcType=VARCHAR},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      fb_trade_id = #{record.fbTradeId,jdbcType=VARCHAR},
      root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      voucher_id = #{record.voucherId,jdbcType=VARCHAR},
      refund_pay_txn_id = #{record.refundPayTxnId,jdbcType=VARCHAR},
      refund_txn_id = #{record.refundTxnId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      voucher_ex = #{record.voucherEx,jdbcType=VARCHAR},
      voucher_company_id = #{record.voucherCompanyId,jdbcType=VARCHAR},
      xe_status = #{record.xeStatus,jdbcType=INTEGER},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_redcoupon_refund_amount = #{record.bankRedcouponRefundAmount,jdbcType=DECIMAL},
      bank_company_refund_amount = #{record.bankCompanyRefundAmount,jdbcType=DECIMAL},
      voucher_invoice_type = #{record.voucherInvoiceType,jdbcType=INTEGER},
      deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      account_model = #{record.accountModel,jdbcType=INTEGER},
      account_sub_id = #{record.accountSubId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_refund_settlement_pay
    <set>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payChannel != null">
        pay_channel = #{payChannel,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashierTxnId != null">
        cashier_txn_id = #{cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fbTradeId != null">
        fb_trade_id = #{fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=VARCHAR},
      </if>
      <if test="refundPayTxnId != null">
        refund_pay_txn_id = #{refundPayTxnId,jdbcType=VARCHAR},
      </if>
      <if test="refundTxnId != null">
        refund_txn_id = #{refundTxnId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="voucherName != null">
        voucher_name = #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherEx != null">
        voucher_ex = #{voucherEx,jdbcType=VARCHAR},
      </if>
      <if test="voucherCompanyId != null">
        voucher_company_id = #{voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="xeStatus != null">
        xe_status = #{xeStatus,jdbcType=INTEGER},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankTransNo != null">
        bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankRedcouponRefundAmount != null">
        bank_redcoupon_refund_amount = #{bankRedcouponRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankCompanyRefundAmount != null">
        bank_company_refund_amount = #{bankCompanyRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="voucherInvoiceType != null">
        voucher_invoice_type = #{voucherInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        account_model = #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_refund_settlement_pay
    set refund_status = #{refundStatus,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      pay_channel = #{payChannel,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      cashier_txn_id = #{cashierTxnId,jdbcType=VARCHAR},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      fb_trade_id = #{fbTradeId,jdbcType=VARCHAR},
      root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      voucher_id = #{voucherId,jdbcType=VARCHAR},
      refund_pay_txn_id = #{refundPayTxnId,jdbcType=VARCHAR},
      refund_txn_id = #{refundTxnId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      voucher_name = #{voucherName,jdbcType=VARCHAR},
      voucher_ex = #{voucherEx,jdbcType=VARCHAR},
      voucher_company_id = #{voucherCompanyId,jdbcType=VARCHAR},
      xe_status = #{xeStatus,jdbcType=INTEGER},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_redcoupon_refund_amount = #{bankRedcouponRefundAmount,jdbcType=DECIMAL},
      bank_company_refund_amount = #{bankCompanyRefundAmount,jdbcType=DECIMAL},
      voucher_invoice_type = #{voucherInvoiceType,jdbcType=INTEGER},
      deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      account_model = #{accountModel,jdbcType=INTEGER},
      account_sub_id = #{accountSubId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>