<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderSettlementBatchPreinfoMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="payment_id" jdbcType="VARCHAR" property="paymentId" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="currency_type" jdbcType="VARCHAR" property="currencyType" />
    <result column="batch_status" jdbcType="INTEGER" property="batchStatus" />
    <result column="succeed_num" jdbcType="INTEGER" property="succeedNum" />
    <result column="succeed_amount" jdbcType="DECIMAL" property="succeedAmount" />
    <result column="failed_num" jdbcType="INTEGER" property="failedNum" />
    <result column="failed_amount" jdbcType="DECIMAL" property="failedAmount" />
    <result column="payment_purpose" jdbcType="VARCHAR" property="paymentPurpose" />
    <result column="payer_account_type" jdbcType="INTEGER" property="payerAccountType" />
    <result column="payer_account_name" jdbcType="VARCHAR" property="payerAccountName" />
    <result column="payer_account_id" jdbcType="VARCHAR" property="payerAccountId" />
    <result column="payer_bank_account_no" jdbcType="VARCHAR" property="payerBankAccountNo" />
    <result column="payer_bank_name" jdbcType="VARCHAR" property="payerBankName" />
    <result column="payer_bank_code" jdbcType="VARCHAR" property="payerBankCode" />
    <result column="pay_user_id" jdbcType="VARCHAR" property="payUserId" />
    <result column="pay_start_time" jdbcType="TIMESTAMP" property="payStartTime" />
    <result column="pay_end_time" jdbcType="TIMESTAMP" property="payEndTime" />
    <result column="fail_desc" jdbcType="VARCHAR" property="failDesc" />
    <result column="bank_check_code" jdbcType="VARCHAR" property="bankCheckCode" />
    <result column="callback_status" jdbcType="INTEGER" property="callbackStatus" />
    <result column="callback_num" jdbcType="INTEGER" property="callbackNum" />
    <result column="callback_next" jdbcType="TIMESTAMP" property="callbackNext" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, fb_order_id, order_type, batch_no,payment_id, total_num, total_amount, currency_type, batch_status,
    succeed_num, succeed_amount, failed_num, failed_amount, payment_purpose, payer_account_type, 
    payer_account_name, payer_account_id, payer_bank_account_no, payer_bank_name, payer_bank_code, 
    pay_user_id, pay_start_time, pay_end_time, fail_desc,bank_check_code, callback_status, callback_num,
    callback_next, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cashier_order_settlement_batch_preinfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_settlement_batch_preinfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_settlement_batch_preinfo (id, fb_order_id, order_type, 
      batch_no,payment_id, total_num, total_amount,
      currency_type, batch_status, succeed_num, 
      succeed_amount, failed_num, failed_amount, 
      payment_purpose, payer_account_type, payer_account_name, 
      payer_account_id, payer_bank_account_no, payer_bank_name, 
      payer_bank_code, pay_user_id, pay_start_time, 
      pay_end_time, fail_desc,bank_check_code, callback_status,
      callback_num, callback_next, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{fbOrderId,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{batchNo,jdbcType=VARCHAR},#{paymentId,jdbcType=VARCHAR}, #{totalNum,jdbcType=INTEGER}, #{totalAmount,jdbcType=DECIMAL},
      #{currencyType,jdbcType=VARCHAR}, #{batchStatus,jdbcType=INTEGER}, #{succeedNum,jdbcType=INTEGER}, 
      #{succeedAmount,jdbcType=DECIMAL}, #{failedNum,jdbcType=INTEGER}, #{failedAmount,jdbcType=DECIMAL}, 
      #{paymentPurpose,jdbcType=VARCHAR}, #{payerAccountType,jdbcType=INTEGER}, #{payerAccountName,jdbcType=VARCHAR}, 
      #{payerAccountId,jdbcType=VARCHAR}, #{payerBankAccountNo,jdbcType=VARCHAR}, #{payerBankName,jdbcType=VARCHAR}, 
      #{payerBankCode,jdbcType=VARCHAR}, #{payUserId,jdbcType=VARCHAR}, #{payStartTime,jdbcType=TIMESTAMP}, 
      #{payEndTime,jdbcType=TIMESTAMP}, #{failDesc,jdbcType=VARCHAR},#{bankCheckCode,jdbcType=VARCHAR}, #{callbackStatus,jdbcType=INTEGER},
      #{callbackNum,jdbcType=INTEGER}, #{callbackNext,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_settlement_batch_preinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="currencyType != null">
        currency_type,
      </if>
      <if test="batchStatus != null">
        batch_status,
      </if>
      <if test="succeedNum != null">
        succeed_num,
      </if>
      <if test="succeedAmount != null">
        succeed_amount,
      </if>
      <if test="failedNum != null">
        failed_num,
      </if>
      <if test="failedAmount != null">
        failed_amount,
      </if>
      <if test="paymentPurpose != null">
        payment_purpose,
      </if>
      <if test="payerAccountType != null">
        payer_account_type,
      </if>
      <if test="payerAccountName != null">
        payer_account_name,
      </if>
      <if test="payerAccountId != null">
        payer_account_id,
      </if>
      <if test="payerBankAccountNo != null">
        payer_bank_account_no,
      </if>
      <if test="payerBankName != null">
        payer_bank_name,
      </if>
      <if test="payerBankCode != null">
        payer_bank_code,
      </if>
      <if test="payUserId != null">
        pay_user_id,
      </if>
      <if test="payStartTime != null">
        pay_start_time,
      </if>
      <if test="payEndTime != null">
        pay_end_time,
      </if>
      <if test="failDesc != null">
        fail_desc,
      </if>
      <if test="bankCheckCode != null">
        bank_check_code,
      </if>
      <if test="callbackStatus != null">
        callback_status,
      </if>
      <if test="callbackNum != null">
        callback_num,
      </if>
      <if test="callbackNext != null">
        callback_next,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="currencyType != null">
        #{currencyType,jdbcType=VARCHAR},
      </if>
      <if test="batchStatus != null">
        #{batchStatus,jdbcType=INTEGER},
      </if>
      <if test="succeedNum != null">
        #{succeedNum,jdbcType=INTEGER},
      </if>
      <if test="succeedAmount != null">
        #{succeedAmount,jdbcType=DECIMAL},
      </if>
      <if test="failedNum != null">
        #{failedNum,jdbcType=INTEGER},
      </if>
      <if test="failedAmount != null">
        #{failedAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentPurpose != null">
        #{paymentPurpose,jdbcType=VARCHAR},
      </if>
      <if test="payerAccountType != null">
        #{payerAccountType,jdbcType=INTEGER},
      </if>
      <if test="payerAccountName != null">
        #{payerAccountName,jdbcType=VARCHAR},
      </if>
      <if test="payerAccountId != null">
        #{payerAccountId,jdbcType=VARCHAR},
      </if>
      <if test="payerBankAccountNo != null">
        #{payerBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="payerBankName != null">
        #{payerBankName,jdbcType=VARCHAR},
      </if>
      <if test="payerBankCode != null">
        #{payerBankCode,jdbcType=VARCHAR},
      </if>
      <if test="payUserId != null">
        #{payUserId,jdbcType=VARCHAR},
      </if>
      <if test="payStartTime != null">
        #{payStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payEndTime != null">
        #{payEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="failDesc != null">
        #{failDesc,jdbcType=VARCHAR},
      </if>
      <if test="callbackStatus != null">
        #{callbackStatus,jdbcType=INTEGER},
      </if>
      <if test="callbackNum != null">
        #{callbackNum,jdbcType=INTEGER},
      </if>
      <if test="callbackNext != null">
        #{callbackNext,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from cashier_order_settlement_batch_preinfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement_batch_preinfo
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentId != null">
        payment_id = #{record.paymentId,jdbcType=VARCHAR},
      </if>
      <if test="record.totalNum != null">
        total_num = #{record.totalNum,jdbcType=INTEGER},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.currencyType != null">
        currency_type = #{record.currencyType,jdbcType=VARCHAR},
      </if>
      <if test="record.batchStatus != null">
        batch_status = #{record.batchStatus,jdbcType=INTEGER},
      </if>
      <if test="record.succeedNum != null">
        succeed_num = #{record.succeedNum,jdbcType=INTEGER},
      </if>
      <if test="record.succeedAmount != null">
        succeed_amount = #{record.succeedAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.failedNum != null">
        failed_num = #{record.failedNum,jdbcType=INTEGER},
      </if>
      <if test="record.failedAmount != null">
        failed_amount = #{record.failedAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.paymentPurpose != null">
        payment_purpose = #{record.paymentPurpose,jdbcType=VARCHAR},
      </if>
      <if test="record.payerAccountType != null">
        payer_account_type = #{record.payerAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.payerAccountName != null">
        payer_account_name = #{record.payerAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.payerAccountId != null">
        payer_account_id = #{record.payerAccountId,jdbcType=VARCHAR},
      </if>
      <if test="record.payerBankAccountNo != null">
        payer_bank_account_no = #{record.payerBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payerBankName != null">
        payer_bank_name = #{record.payerBankName,jdbcType=VARCHAR},
      </if>
      <if test="record.payerBankCode != null">
        payer_bank_code = #{record.payerBankCode,jdbcType=VARCHAR},
      </if>
      <if test="record.payUserId != null">
        pay_user_id = #{record.payUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.payStartTime != null">
        pay_start_time = #{record.payStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payEndTime != null">
        pay_end_time = #{record.payEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.failDesc != null">
        fail_desc = #{record.failDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.bankCheckCode != null">
        bank_check_code = #{record.bankCheckCode,jdbcType=VARCHAR},
      </if>
      <if test="record.callbackStatus != null">
        callback_status = #{record.callbackStatus,jdbcType=INTEGER},
      </if>
      <if test="record.callbackNum != null">
        callback_num = #{record.callbackNum,jdbcType=INTEGER},
      </if>
      <if test="record.callbackNext != null">
        callback_next = #{record.callbackNext,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement_batch_preinfo
    set id = #{record.id,jdbcType=BIGINT},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      payment_id = #{record.paymentId,jdbcType=VARCHAR},
      total_num = #{record.totalNum,jdbcType=INTEGER},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      currency_type = #{record.currencyType,jdbcType=VARCHAR},
      batch_status = #{record.batchStatus,jdbcType=INTEGER},
      succeed_num = #{record.succeedNum,jdbcType=INTEGER},
      succeed_amount = #{record.succeedAmount,jdbcType=DECIMAL},
      failed_num = #{record.failedNum,jdbcType=INTEGER},
      failed_amount = #{record.failedAmount,jdbcType=DECIMAL},
      payment_purpose = #{record.paymentPurpose,jdbcType=VARCHAR},
      payer_account_type = #{record.payerAccountType,jdbcType=INTEGER},
      payer_account_name = #{record.payerAccountName,jdbcType=VARCHAR},
      payer_account_id = #{record.payerAccountId,jdbcType=VARCHAR},
      payer_bank_account_no = #{record.payerBankAccountNo,jdbcType=VARCHAR},
      payer_bank_name = #{record.payerBankName,jdbcType=VARCHAR},
      payer_bank_code = #{record.payerBankCode,jdbcType=VARCHAR},
      pay_user_id = #{record.payUserId,jdbcType=VARCHAR},
      pay_start_time = #{record.payStartTime,jdbcType=TIMESTAMP},
      pay_end_time = #{record.payEndTime,jdbcType=TIMESTAMP},
      fail_desc = #{record.failDesc,jdbcType=VARCHAR},
      bank_check_code = #{record.bankCheckCode,jdbcType=VARCHAR},
      callback_status = #{record.callbackStatus,jdbcType=INTEGER},
      callback_num = #{record.callbackNum,jdbcType=INTEGER},
      callback_next = #{record.callbackNext,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>