<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderRefundSettlementExtMapper">
    <resultMap id="cashierRefundSettlementAmountStatVoResultMap" type="com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundSettlementHistoryAmountStatVo">
        <id column="cashier_txn_id" jdbcType="VARCHAR" property="cashierTxnId" />
        <result column="refund_amount" jdbcType="NUMERIC" property="refundAmount" />
        <result column="refund_amount_public" jdbcType="NUMERIC" property="refundAmountPublic" />
        <result column="refund_amount_company" jdbcType="NUMERIC" property="refundAmountCompany" />
        <result column="refund_amount_redcoupon" jdbcType="NUMERIC" property="refundAmountRedcoupon" />
        <result column="refund_amount_personal" jdbcType="NUMERIC" property="refundAmountPersonal" />
        <result column="refund_amount_fbb" jdbcType="NUMERIC" property="refundAmountFbb" />
        <result column="refund_amount_voucher" jdbcType="NUMERIC" property="refundAmountVoucher" />
        <result column="refund_amount_voucher_individual" jdbcType="NUMERIC" property="refundAmountVoucherIndividual" />
        <result column="refund_amount_voucher_redcoupon" jdbcType="NUMERIC" property="refundAmountVoucherRedcoupon" />
        <result column="refund_amount_third" jdbcType="NUMERIC" property="refundAmountThird" />
        <result column="refund_amount_reimburse_company" jdbcType="DECIMAL" property="refundAmountReimburseCompany" />
        <result column="refund_amount_reimburse_self" jdbcType="DECIMAL" property="refundAmountReimburseSelf" />
        <result column="refund_amount_bank_personal" jdbcType="DECIMAL" property="refundAmountBankPersonal" />
    </resultMap>


    <resultMap id="cashierRefundByFbOrderIdStatVoResultMap" type="com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundSettlementHistoryAmountStatVo">
        <id column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
        <result column="refund_amount" jdbcType="NUMERIC" property="refundAmount" />
        <result column="refund_amount_public" jdbcType="NUMERIC" property="refundAmountPublic" />
        <result column="refund_amount_company" jdbcType="NUMERIC" property="refundAmountCompany" />
        <result column="refund_amount_redcoupon" jdbcType="NUMERIC" property="refundAmountRedcoupon" />
        <result column="refund_amount_personal" jdbcType="NUMERIC" property="refundAmountPersonal" />
        <result column="refund_amount_fbb" jdbcType="NUMERIC" property="refundAmountFbb" />
        <result column="refund_amount_voucher" jdbcType="NUMERIC" property="refundAmountVoucher" />
        <result column="refund_amount_voucher_individual" jdbcType="NUMERIC" property="refundAmountVoucherIndividual" />
        <result column="refund_amount_voucher_redcoupon" jdbcType="NUMERIC" property="refundAmountVoucherRedcoupon" />
        <result column="refund_amount_third" jdbcType="NUMERIC" property="refundAmountThird" />
        <result column="refund_amount_reimburse_company" jdbcType="DECIMAL" property="refundAmountReimburseCompany" />
        <result column="refund_amount_reimburse_self" jdbcType="DECIMAL" property="refundAmountReimburseSelf" />
    </resultMap>


    <resultMap id="cashierRefundByRefundOrderIdStatVoResultMap" type="com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundSettlementHistoryAmountStatVo">
        <id column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId" />
        <result column="refund_amount" jdbcType="NUMERIC" property="refundAmount" />
        <result column="refund_amount_public" jdbcType="NUMERIC" property="refundAmountPublic" />
        <result column="refund_amount_company" jdbcType="NUMERIC" property="refundAmountCompany" />
        <result column="refund_amount_redcoupon" jdbcType="NUMERIC" property="refundAmountRedcoupon" />
        <result column="refund_amount_personal" jdbcType="NUMERIC" property="refundAmountPersonal" />
        <result column="refund_amount_fbb" jdbcType="NUMERIC" property="refundAmountFbb" />
        <result column="refund_amount_voucher" jdbcType="NUMERIC" property="refundAmountVoucher" />
        <result column="refund_amount_voucher_individual" jdbcType="NUMERIC" property="refundAmountVoucherIndividual" />
        <result column="refund_amount_voucher_redcoupon" jdbcType="NUMERIC" property="refundAmountVoucherRedcoupon" />
        <result column="refund_amount_third" jdbcType="NUMERIC" property="refundAmountThird" />
        <result column="refund_amount_reimburse_company" jdbcType="DECIMAL" property="refundAmountReimburseCompany" />
        <result column="refund_amount_reimburse_self" jdbcType="DECIMAL" property="refundAmountReimburseSelf" />
    </resultMap>


    <resultMap id="cashierByFbOrderIdCountVoResultMap" type="com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundSettlementHistoryCountVo">
        <id column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
        <result column="count_all" jdbcType="NUMERIC" property="countAll" />
    </resultMap>

    <resultMap id="cashierByRefundOrderIdCountVoResultMap" type="com.fenbeitong.fenbeipay.core.model.vo.cashier.CashierRefundSettlementHistoryCountVo">
        <id column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId" />
        <result column="count_all" jdbcType="NUMERIC" property="countAll" />
    </resultMap>

    <sql id="where_clause_company">
        <if test="companyId != null">
            and company_id = #{companyId}
        </if>
        <if test="refundStatusList != null">
            and refund_status in
            <foreach collection="refundStatusList" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="categoryTypes != null">
            and order_type in
            <foreach collection="categoryTypes" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="startTime != null and endTime != null">
            and create_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <sql id="limit_page">
        <if test="limit != null">
            <if test="offset == null">
                limit ${limit}
            </if>
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
        </if>
    </sql>


    <select id="selectSumAmountByCashierTxnIdAndStatus" parameterType="map" resultMap="cashierRefundSettlementAmountStatVoResultMap">
        select  cashier_txn_id ,
        sum(refund_amount_all) as refund_amount ,
        sum(refund_amount_public) as refund_amount_public ,
        sum(refund_amount_company) as refund_amount_company ,
        sum(refund_amount_redcoupon) as refund_amount_redcoupon ,
        sum(refund_amount_personal) as refund_amount_personal ,
        sum(refund_amount_third) as refund_amount_third ,
        sum(refund_amount_fbb) as refund_amount_fbb ,
        sum(refund_amount_voucher) as refund_amount_voucher ,
        sum(amount_voucher_individual) as refund_amount_voucher_individual ,
        sum(amount_voucher_redcoupon) as refund_amount_voucher_redcoupon,
        sum(refund_amount_third) as refund_amount_third,
        sum(refund_amount_reimburse_company) as refund_amount_reimburse_company,
        sum(refund_amount_reimburse_self) as refund_amount_reimburse_self,
        sum(bank_personal_refund_amount) as refund_amount_bank_personal
        from cashier_order_refund_settlement
        where xe_status = 0
        <if test="cashierTxnId != null and cashierTxnId != '' ">
            and cashier_txn_id = #{cashierTxnId}
        </if>

        <if test="refundStatusList != null">
            and refund_status in
            <foreach collection="refundStatusList" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        group by cashier_txn_id

    </select>


    <select id="selectCountByRefundOrderIdAndStatus" parameterType="map" resultType="java.lang.Integer" >
        select count(distinct refund_order_id) as count_all
        from cashier_order_refund_settlement
        where xe_status = 0
        <include refid="where_clause_company" />
    </select>


    <select id="selectSumAmountByRefundOrderIdAndStatus" parameterType="map" resultMap="cashierRefundByRefundOrderIdStatVoResultMap">
        select  refund_order_id ,
        sum(refund_amount_all) as refund_amount ,
        sum(refund_amount_public) as refund_amount_public ,
        sum(refund_amount_company) as refund_amount_company ,
        sum(refund_amount_redcoupon) as refund_amount_redcoupon ,
        sum(refund_amount_personal) as refund_amount_personal ,
        sum(refund_amount_third) as refund_amount_third ,
        sum(refund_amount_fbb) as refund_amount_fbb ,
        sum(refund_amount_voucher) as refund_amount_voucher ,
        sum(amount_voucher_individual) as refund_amount_voucher_individual ,
        sum(amount_voucher_redcoupon) as refund_amount_voucher_redcoupon,
        sum(refund_amount_third) as refund_amount_third,
        sum(refund_amount_reimburse_company) as refund_amount_reimburse_company,
        sum(refund_amount_reimburse_self) as refund_amount_reimburse_self
        from cashier_order_refund_settlement
        where xe_status = 0
        <include refid="where_clause_company" />
        group by refund_order_id
        <include refid="limit_page" />
    </select>
</mapper>