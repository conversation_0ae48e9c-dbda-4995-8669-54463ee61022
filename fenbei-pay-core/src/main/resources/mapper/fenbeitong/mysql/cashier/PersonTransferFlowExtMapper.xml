<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.PersonTransferFlowExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.PersonTransferFlow">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="target_id" jdbcType="VARCHAR" property="targetId"/>
        <result column="transfer_amount" jdbcType="DECIMAL" property="transferAmount"/>
        <result column="from_employee_id" jdbcType="VARCHAR" property="fromEmployeeId"/>
        <result column="from_employee_name" jdbcType="VARCHAR" property="fromEmployeeName"/>
        <result column="from_company_id" jdbcType="VARCHAR" property="fromCompanyId"/>
        <result column="to_employee_id" jdbcType="VARCHAR" property="toEmployeeId"/>
        <result column="to_employee_name" jdbcType="VARCHAR" property="toEmployeeName"/>
        <result column="to_company_id" jdbcType="VARCHAR" property="toCompanyId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="batch_id" jdbcType="VARCHAR" property="batchId"/>
        <result column="open_enable" jdbcType="TINYINT" property="openEnable" />
        <result column="background_url" jdbcType="VARCHAR" property="backgroundUrl" />
        <result column="background_name" jdbcType="VARBINARY" property="backgroundName" />
        <result column="notice_content" jdbcType="VARCHAR" property="noticeContent" />
        <result column="is_ding" jdbcType="TINYINT" property="isDing"/>
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into person_transfer_flow (id, source_id, target_id,
        transfer_amount, from_employee_id, from_employee_name,
        from_company_id, to_employee_id, to_employee_name,
        to_company_id, create_time, batch_id, open_enable,
        background_url, background_name, notice_content, is_ding)
        values
        <foreach collection="records" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.sourceId,jdbcType=VARCHAR}, #{item.targetId,jdbcType=VARCHAR},
            #{item.transferAmount,jdbcType=DECIMAL}, #{item.fromEmployeeId,jdbcType=VARCHAR},
            #{item.fromEmployeeName,jdbcType=VARCHAR},
            #{item.fromCompanyId,jdbcType=VARCHAR}, #{item.toEmployeeId,jdbcType=VARCHAR},
            #{item.toEmployeeName,jdbcType=VARCHAR},#{item.toCompanyId,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.batchId,jdbcType=VARCHAR},
            #{item.openEnable,jdbcType=INTEGER}, #{item.backgroundUrl,jdbcType=VARCHAR},
            #{item.backgroundName,jdbcType=VARCHAR},#{item.noticeContent,jdbcType=VARCHAR},
            #{item.isDing,jdbcType=TINYINT})
        </foreach>
    </insert>

</mapper>