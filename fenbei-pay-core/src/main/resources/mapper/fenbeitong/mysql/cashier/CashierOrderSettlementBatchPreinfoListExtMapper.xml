<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderSettlementBatchPreinfoListExtMapper">
    <resultMap id="BaseResultMap"
               type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementBatchPreinfoList">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="sub_order_id" jdbcType="VARCHAR" property="subOrderId"/>
        <result column="sub_order_status" jdbcType="INTEGER" property="subOrderStatus"/>
        <result column="sub_order_pay_amount" jdbcType="DECIMAL" property="subOrderPayAmount"/>
        <result column="currency_type" jdbcType="VARCHAR" property="currencyType"/>
        <result column="payer_account_type" jdbcType="INTEGER" property="payerAccountType"/>
        <result column="payer_account_name" jdbcType="VARCHAR" property="payerAccountName"/>
        <result column="payer_account_id" jdbcType="VARCHAR" property="payerAccountId"/>
        <result column="payer_bank_account_no" jdbcType="VARCHAR" property="payerBankAccountNo"/>
        <result column="payer_bank_name" jdbcType="VARCHAR" property="payerBankName"/>
        <result column="payer_bank_code" jdbcType="VARCHAR" property="payerBankCode"/>
        <result column="receiver_bank_account_no" jdbcType="VARCHAR" property="receiverBankAccountNo"/>
        <result column="receiver_bank_account_name" jdbcType="VARCHAR" property="receiverBankAccountName"/>
        <result column="receiver_branch_bank_code" jdbcType="VARCHAR" property="receiverBranchBankCode"/>
        <result column="receiver_branch_bank_name" jdbcType="VARCHAR" property="receiverBranchBankName"/>
        <result column="receiver_bank_name" jdbcType="VARCHAR" property="receiverBankName"/>
        <result column="receiver_employee_no" jdbcType="VARCHAR" property="receiverEmployeeNo"/>
        <result column="receiver_employee_id" jdbcType="VARCHAR" property="receiverEmployeeId"/>
        <result column="receiver_employee_idcard" jdbcType="VARCHAR" property="receiverEmployeeIdcard"/>
        <result column="receiver_employee_idcard_type" jdbcType="VARCHAR" property="receiverEmployeeIdcardType"/>
        <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone"/>
        <result column="bank_trans_time" jdbcType="TIMESTAMP" property="bankTransTime"/>
        <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo"/>
        <result column="sync_bank_trans_no" jdbcType="VARCHAR" property="syncBankTransNo"/>
        <result column="pay_user_id" jdbcType="VARCHAR" property="payUserId"/>
        <result column="pay_start_time" jdbcType="TIMESTAMP" property="payStartTime"/>
        <result column="pay_end_time" jdbcType="TIMESTAMP" property="payEndTime"/>
        <result column="fail_desc" jdbcType="VARCHAR" property="failDesc"/>
        <result column="payment_purpose" jdbcType="VARCHAR" property="paymentPurpose"/>
        <result column="payment_remark" jdbcType="VARCHAR" property="paymentRemark"/>
        <result column="bank_check_code" jdbcType="VARCHAR" property="bankCheckCode" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into cashier_order_settlement_batch_preinfo_list (id,fb_order_id,order_type,company_id,batch_no,
        sub_order_id, sub_order_status, sub_order_pay_amount,currency_type,payer_account_type,
        payer_account_name, payer_account_id, payer_bank_account_no,payer_bank_name,payer_bank_code,
        receiver_bank_account_no,receiver_bank_account_name,receiver_branch_bank_code,receiver_branch_bank_name,receiver_bank_name,
        receiver_employee_id,receiver_employee_no,receiver_employee_idcard,receiver_employee_idcard_type,receiver_phone,bank_trans_time,bank_trans_no,sync_bank_trans_no,
        pay_user_id,pay_start_time,pay_end_time,fail_desc,payment_purpose,payment_remark,bank_check_code,
        create_time, update_time)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            (
            #{record.id}, #{record.fbOrderId},#{record.orderType},#{record.companyId},#{record.batchNo},
            #{record.subOrderId},#{record.subOrderStatus},#{record.subOrderPayAmount},#{record.currencyType},#{record.payerAccountType},
            #{record.payerAccountName},#{record.payerAccountId},#{record.payerBankAccountNo},#{record.payerBankName},#{record.payerBankCode},
            #{record.receiverBankAccountNo},#{record.receiverBankAccountName},#{record.receiverBranchBankCode},#{record.receiverBranchBankName},#{record.receiverBankName},
            #{record.receiverEmployeeId},#{record.receiverEmployeeNo},#{record.receiverEmployeeIdcard},#{record.receiverEmployeeIdcardType},#{record.receiverPhone},#{record.bankTransTime},#{record.bankTransNo},#{record.syncBankTransNo},
            #{record.payUserId},#{record.payStartTime},#{record.payEndTime},#{record.failDesc},#{record.paymentPurpose},#{record.paymentRemark},#{record.bankCheckCode},
            #{record.createTime},#{record.updateTime}
            )
        </foreach>
    </insert>
    <update id="batchUpdate2Failed" parameterType="java.util.List">
        update cashier_order_settlement_batch_preinfo_list
        set
            sub_order_status = 3
        <where>
            sub_order_id in
            <foreach item="subOrderId" index="index" collection="records" open="(" separator="," close=")">
                #{subOrderId}
            </foreach>
            and batch_no = #{batchNo}
        </where>
    </update>
    <update id="batchUpdate2Succeed" parameterType="java.util.List">
        update cashier_order_settlement_batch_preinfo_list
        set
        sub_order_status = 1
        <where>
            sub_order_id in
            <foreach item="subOrderId" index="index" collection="records" open="(" separator="," close=")">
                #{subOrderId}
            </foreach>
            and batch_no = #{batchNo}
        </where>
    </update>

</mapper>