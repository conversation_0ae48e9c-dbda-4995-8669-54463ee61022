<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierChannelPayOrderMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierChannelPayOrder">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="pay_txn_id" jdbcType="VARCHAR" property="payTxnId" />
    <result column="cashier_txn_id" jdbcType="VARCHAR" property="cashierTxnId" />
    <result column="refund_txn_id" jdbcType="VARCHAR" property="refundTxnId" />
    <result column="pay_account" jdbcType="VARCHAR" property="payAccount" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="receive_account" jdbcType="VARCHAR" property="receiveAccount" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

  <sql id="Base_Column_List">
    id, channel,order_type, fb_order_id, company_id,pay_txn_id, cashier_txn_id,refund_txn_id,pay_account,amount,receive_account, order_status, create_time, update_time,remark
  </sql>

  <select id="queryById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_channel_pay_order
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <select id="queryByIdForUpdate" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_channel_pay_order
    where id = #{id,jdbcType=VARCHAR}
    for update
  </select>

  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierChannelPayOrder">
    insert into cashier_channel_pay_order (
      id, channel,order_type, fb_order_id, company_id,pay_txn_id,
                                           cashier_txn_id,refund_txn_id,pay_account,amount,receive_account, order_status
    )
    values (#{id,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR},#{payTxnId,jdbcType=VARCHAR},
            #{cashierTxnId,jdbcType=VARCHAR},#{refundTxnId,jdbcType=VARCHAR},#{payAccount,jdbcType=VARCHAR},#{amount,jdbcType=DECIMAL},#{receiveAccount,jdbcType=VARCHAR},#{orderStatus,jdbcType=VARCHAR}
    )
  </insert>

  <select id="queryToBePaidByCompanyId" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_channel_pay_order
    where company_id = #{companyId,jdbcType=VARCHAR}
    and order_status = 1
    and create_time > #{beginTime,jdbcType=TIMESTAMP}
    limit 100
  </select>


  <update id="updateStatus" parameterType="map">
    update cashier_channel_pay_order
    set order_status = #{orderStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="queryByFbOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_channel_pay_order
    where fb_order_id = #{fbOrderId,jdbcType=VARCHAR}
    and order_type = 'PAY'
  </select>
  <select id="queryOrderByOrderStatus" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_channel_pay_order
    where order_status = #{orderStatus,jdbcType=TINYINT}
    limit 100
  </select>

</mapper>