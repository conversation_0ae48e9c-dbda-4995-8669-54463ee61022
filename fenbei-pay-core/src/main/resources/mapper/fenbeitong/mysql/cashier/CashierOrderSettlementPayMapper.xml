<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderSettlementPayMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="pay_status" jdbcType="SMALLINT" property="payStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="cashier_txn_id" jdbcType="VARCHAR" property="cashierTxnId" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="fb_trade_id" jdbcType="VARCHAR" property="fbTradeId" />
    <result column="root_order_id" jdbcType="VARCHAR" property="rootOrderId" />
    <result column="order_root_type" jdbcType="INTEGER" property="orderRootType" />
    <result column="cashier_trade_type" jdbcType="INTEGER" property="cashierTradeType" />
    <result column="pay_txn_id" jdbcType="VARCHAR" property="payTxnId" />
    <result column="voucher_id" jdbcType="VARCHAR" property="voucherId" />
    <result column="del_status" jdbcType="SMALLINT" property="delStatus" />
    <result column="voucher_name" jdbcType="VARCHAR" property="voucherName" />
    <result column="voucher_ex" jdbcType="VARCHAR" property="voucherEx" />
    <result column="voucher_company_id" jdbcType="VARCHAR" property="voucherCompanyId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="consumer_account_sub_type" jdbcType="SMALLINT" property="consumerAccountSubType" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="seller_bank_no" jdbcType="VARCHAR" property="sellerBankNo" />
    <result column="seller_bank_name" jdbcType="VARCHAR" property="sellerBankName" />
    <result column="seller_bank_acct_name" jdbcType="VARCHAR" property="sellerBankAcctName" />
    <result column="bank_company_amount" jdbcType="DECIMAL" property="bankCompanyAmount" />
    <result column="bank_redcoupon_amount" jdbcType="DECIMAL" property="bankRedcouponAmount" />
    <result column="voucher_invoice_type" jdbcType="INTEGER" property="voucherInvoiceType" />
    <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, pay_status, create_time, update_time, pay_channel, amount, cashier_txn_id, fb_order_id, 
    fb_trade_id, root_order_id, order_root_type, cashier_trade_type, pay_txn_id, voucher_id, 
    del_status, voucher_name, voucher_ex, voucher_company_id, company_id, employee_id, 
    consumer_account_sub_type, bank_account_no, bank_trans_no, bank_name, seller_bank_no, 
    seller_bank_name, seller_bank_acct_name, bank_company_amount, bank_redcoupon_amount, 
    voucher_invoice_type, deduction_account_type, account_model, account_sub_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPayExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cashier_order_settlement_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from cashier_order_settlement_pay
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_settlement_pay
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPayExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from cashier_order_settlement_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_settlement_pay (id, pay_status, create_time, 
      update_time, pay_channel, amount, 
      cashier_txn_id, fb_order_id, fb_trade_id, 
      root_order_id, order_root_type, cashier_trade_type, 
      pay_txn_id, voucher_id, del_status, 
      voucher_name, voucher_ex, voucher_company_id, 
      company_id, employee_id, consumer_account_sub_type, 
      bank_account_no, bank_trans_no, bank_name, 
      seller_bank_no, seller_bank_name, seller_bank_acct_name, 
      bank_company_amount, bank_redcoupon_amount, 
      voucher_invoice_type, deduction_account_type, 
      account_model, account_sub_id)
    values (#{id,jdbcType=VARCHAR}, #{payStatus,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{payChannel,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{cashierTxnId,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{fbTradeId,jdbcType=VARCHAR}, 
      #{rootOrderId,jdbcType=VARCHAR}, #{orderRootType,jdbcType=INTEGER}, #{cashierTradeType,jdbcType=INTEGER}, 
      #{payTxnId,jdbcType=VARCHAR}, #{voucherId,jdbcType=VARCHAR}, #{delStatus,jdbcType=SMALLINT}, 
      #{voucherName,jdbcType=VARCHAR}, #{voucherEx,jdbcType=VARCHAR}, #{voucherCompanyId,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, #{consumerAccountSubType,jdbcType=SMALLINT}, 
      #{bankAccountNo,jdbcType=VARCHAR}, #{bankTransNo,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{sellerBankNo,jdbcType=VARCHAR}, #{sellerBankName,jdbcType=VARCHAR}, #{sellerBankAcctName,jdbcType=VARCHAR}, 
      #{bankCompanyAmount,jdbcType=DECIMAL}, #{bankRedcouponAmount,jdbcType=DECIMAL}, 
      #{voucherInvoiceType,jdbcType=INTEGER}, #{deductionAccountType,jdbcType=INTEGER}, 
      #{accountModel,jdbcType=INTEGER}, #{accountSubId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into cashier_order_settlement_pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="payChannel != null">
        pay_channel,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="cashierTxnId != null">
        cashier_txn_id,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="fbTradeId != null">
        fb_trade_id,
      </if>
      <if test="rootOrderId != null">
        root_order_id,
      </if>
      <if test="orderRootType != null">
        order_root_type,
      </if>
      <if test="cashierTradeType != null">
        cashier_trade_type,
      </if>
      <if test="payTxnId != null">
        pay_txn_id,
      </if>
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="delStatus != null">
        del_status,
      </if>
      <if test="voucherName != null">
        voucher_name,
      </if>
      <if test="voucherEx != null">
        voucher_ex,
      </if>
      <if test="voucherCompanyId != null">
        voucher_company_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="consumerAccountSubType != null">
        consumer_account_sub_type,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="bankTransNo != null">
        bank_trans_no,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="sellerBankNo != null">
        seller_bank_no,
      </if>
      <if test="sellerBankName != null">
        seller_bank_name,
      </if>
      <if test="sellerBankAcctName != null">
        seller_bank_acct_name,
      </if>
      <if test="bankCompanyAmount != null">
        bank_company_amount,
      </if>
      <if test="bankRedcouponAmount != null">
        bank_redcoupon_amount,
      </if>
      <if test="voucherInvoiceType != null">
        voucher_invoice_type,
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type,
      </if>
      <if test="accountModel != null">
        account_model,
      </if>
      <if test="accountSubId != null">
        account_sub_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="cashierTxnId != null">
        #{cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fbTradeId != null">
        #{fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderRootType != null">
        #{orderRootType,jdbcType=INTEGER},
      </if>
      <if test="cashierTradeType != null">
        #{cashierTradeType,jdbcType=INTEGER},
      </if>
      <if test="payTxnId != null">
        #{payTxnId,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        #{voucherId,jdbcType=VARCHAR},
      </if>
      <if test="delStatus != null">
        #{delStatus,jdbcType=SMALLINT},
      </if>
      <if test="voucherName != null">
        #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherEx != null">
        #{voucherEx,jdbcType=VARCHAR},
      </if>
      <if test="voucherCompanyId != null">
        #{voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="consumerAccountSubType != null">
        #{consumerAccountSubType,jdbcType=SMALLINT},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankTransNo != null">
        #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="sellerBankNo != null">
        #{sellerBankNo,jdbcType=VARCHAR},
      </if>
      <if test="sellerBankName != null">
        #{sellerBankName,jdbcType=VARCHAR},
      </if>
      <if test="sellerBankAcctName != null">
        #{sellerBankAcctName,jdbcType=VARCHAR},
      </if>
      <if test="bankCompanyAmount != null">
        #{bankCompanyAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankRedcouponAmount != null">
        #{bankRedcouponAmount,jdbcType=DECIMAL},
      </if>
      <if test="voucherInvoiceType != null">
        #{voucherInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="deductionAccountType != null">
        #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        #{accountSubId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPayExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from cashier_order_settlement_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement_pay
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payChannel != null">
        pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.cashierTxnId != null">
        cashier_txn_id = #{record.cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbTradeId != null">
        fb_trade_id = #{record.fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="record.rootOrderId != null">
        root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderRootType != null">
        order_root_type = #{record.orderRootType,jdbcType=INTEGER},
      </if>
      <if test="record.cashierTradeType != null">
        cashier_trade_type = #{record.cashierTradeType,jdbcType=INTEGER},
      </if>
      <if test="record.payTxnId != null">
        pay_txn_id = #{record.payTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherId != null">
        voucher_id = #{record.voucherId,jdbcType=VARCHAR},
      </if>
      <if test="record.delStatus != null">
        del_status = #{record.delStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.voucherName != null">
        voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherEx != null">
        voucher_ex = #{record.voucherEx,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherCompanyId != null">
        voucher_company_id = #{record.voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.consumerAccountSubType != null">
        consumer_account_sub_type = #{record.consumerAccountSubType,jdbcType=SMALLINT},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankTransNo != null">
        bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerBankNo != null">
        seller_bank_no = #{record.sellerBankNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerBankName != null">
        seller_bank_name = #{record.sellerBankName,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerBankAcctName != null">
        seller_bank_acct_name = #{record.sellerBankAcctName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankCompanyAmount != null">
        bank_company_amount = #{record.bankCompanyAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.bankRedcouponAmount != null">
        bank_redcoupon_amount = #{record.bankRedcouponAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.voucherInvoiceType != null">
        voucher_invoice_type = #{record.voucherInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.deductionAccountType != null">
        deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.accountModel != null">
        account_model = #{record.accountModel,jdbcType=INTEGER},
      </if>
      <if test="record.accountSubId != null">
        account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement_pay
    set id = #{record.id,jdbcType=VARCHAR},
      pay_status = #{record.payStatus,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DECIMAL},
      cashier_txn_id = #{record.cashierTxnId,jdbcType=VARCHAR},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      fb_trade_id = #{record.fbTradeId,jdbcType=VARCHAR},
      root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      order_root_type = #{record.orderRootType,jdbcType=INTEGER},
      cashier_trade_type = #{record.cashierTradeType,jdbcType=INTEGER},
      pay_txn_id = #{record.payTxnId,jdbcType=VARCHAR},
      voucher_id = #{record.voucherId,jdbcType=VARCHAR},
      del_status = #{record.delStatus,jdbcType=SMALLINT},
      voucher_name = #{record.voucherName,jdbcType=VARCHAR},
      voucher_ex = #{record.voucherEx,jdbcType=VARCHAR},
      voucher_company_id = #{record.voucherCompanyId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      consumer_account_sub_type = #{record.consumerAccountSubType,jdbcType=SMALLINT},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      seller_bank_no = #{record.sellerBankNo,jdbcType=VARCHAR},
      seller_bank_name = #{record.sellerBankName,jdbcType=VARCHAR},
      seller_bank_acct_name = #{record.sellerBankAcctName,jdbcType=VARCHAR},
      bank_company_amount = #{record.bankCompanyAmount,jdbcType=DECIMAL},
      bank_redcoupon_amount = #{record.bankRedcouponAmount,jdbcType=DECIMAL},
      voucher_invoice_type = #{record.voucherInvoiceType,jdbcType=INTEGER},
      deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      account_model = #{record.accountModel,jdbcType=INTEGER},
      account_sub_id = #{record.accountSubId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement_pay
    <set>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payChannel != null">
        pay_channel = #{payChannel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="cashierTxnId != null">
        cashier_txn_id = #{cashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fbTradeId != null">
        fb_trade_id = #{fbTradeId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderRootType != null">
        order_root_type = #{orderRootType,jdbcType=INTEGER},
      </if>
      <if test="cashierTradeType != null">
        cashier_trade_type = #{cashierTradeType,jdbcType=INTEGER},
      </if>
      <if test="payTxnId != null">
        pay_txn_id = #{payTxnId,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=VARCHAR},
      </if>
      <if test="delStatus != null">
        del_status = #{delStatus,jdbcType=SMALLINT},
      </if>
      <if test="voucherName != null">
        voucher_name = #{voucherName,jdbcType=VARCHAR},
      </if>
      <if test="voucherEx != null">
        voucher_ex = #{voucherEx,jdbcType=VARCHAR},
      </if>
      <if test="voucherCompanyId != null">
        voucher_company_id = #{voucherCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="consumerAccountSubType != null">
        consumer_account_sub_type = #{consumerAccountSubType,jdbcType=SMALLINT},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankTransNo != null">
        bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="sellerBankNo != null">
        seller_bank_no = #{sellerBankNo,jdbcType=VARCHAR},
      </if>
      <if test="sellerBankName != null">
        seller_bank_name = #{sellerBankName,jdbcType=VARCHAR},
      </if>
      <if test="sellerBankAcctName != null">
        seller_bank_acct_name = #{sellerBankAcctName,jdbcType=VARCHAR},
      </if>
      <if test="bankCompanyAmount != null">
        bank_company_amount = #{bankCompanyAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankRedcouponAmount != null">
        bank_redcoupon_amount = #{bankRedcouponAmount,jdbcType=DECIMAL},
      </if>
      <if test="voucherInvoiceType != null">
        voucher_invoice_type = #{voucherInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        account_model = #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementPay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update cashier_order_settlement_pay
    set pay_status = #{payStatus,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      pay_channel = #{payChannel,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      cashier_txn_id = #{cashierTxnId,jdbcType=VARCHAR},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      fb_trade_id = #{fbTradeId,jdbcType=VARCHAR},
      root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      order_root_type = #{orderRootType,jdbcType=INTEGER},
      cashier_trade_type = #{cashierTradeType,jdbcType=INTEGER},
      pay_txn_id = #{payTxnId,jdbcType=VARCHAR},
      voucher_id = #{voucherId,jdbcType=VARCHAR},
      del_status = #{delStatus,jdbcType=SMALLINT},
      voucher_name = #{voucherName,jdbcType=VARCHAR},
      voucher_ex = #{voucherEx,jdbcType=VARCHAR},
      voucher_company_id = #{voucherCompanyId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      consumer_account_sub_type = #{consumerAccountSubType,jdbcType=SMALLINT},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      seller_bank_no = #{sellerBankNo,jdbcType=VARCHAR},
      seller_bank_name = #{sellerBankName,jdbcType=VARCHAR},
      seller_bank_acct_name = #{sellerBankAcctName,jdbcType=VARCHAR},
      bank_company_amount = #{bankCompanyAmount,jdbcType=DECIMAL},
      bank_redcoupon_amount = #{bankRedcouponAmount,jdbcType=DECIMAL},
      voucher_invoice_type = #{voucherInvoiceType,jdbcType=INTEGER},
      deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      account_model = #{accountModel,jdbcType=INTEGER},
      account_sub_id = #{accountSubId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_order_settlement_pay
    where id = #{id,jdbcType=VARCHAR}
    for update
  </select>
</mapper>