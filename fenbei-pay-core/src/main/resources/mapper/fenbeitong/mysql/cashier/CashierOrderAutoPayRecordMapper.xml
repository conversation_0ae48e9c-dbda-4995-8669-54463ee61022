<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.cashier.CashierOrderAutoPayRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderAutoPayRecord">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="cashier_order_id" jdbcType="VARCHAR" property="cashierOrderId" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, cashier_order_id, fb_order_id, company_id, order_type, status, create_time, update_time
  </sql>

  <select id="queryById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_order_auto_pay_record
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <select id="queryByIdForUpdate" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_order_settlement
    where id = #{id,jdbcType=VARCHAR}
    for update
  </select>

  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderAutoPayRecord">
    insert into cashier_order_auto_pay_record (
        id, cashier_order_id, fb_order_id, company_id, order_type, status
    )
    values (#{id,jdbcType=VARCHAR}, #{cashierOrderId,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR},
        #{orderType,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}
    )
  </insert>

  <select id="queryToBePaidByCompanyId" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_order_auto_pay_record
    where company_id = #{companyId,jdbcType=VARCHAR}
    and status = 1
    and create_time > #{beginTime,jdbcType=TIMESTAMP}
    limit 100
  </select>


  <update id="updateStatus" parameterType="map">
    update cashier_order_auto_pay_record
    set status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="queryByCashierOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cashier_order_auto_pay_record
    where cashier_order_id = #{cashierOrderId,jdbcType=VARCHAR}
  </select>

</mapper>