<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PersonPayRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sign_info" jdbcType="VARCHAR" property="signInfo" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="actual_amount" jdbcType="BIGINT" property="actualAmount" />
    <result column="return_msg" jdbcType="VARCHAR" property="returnMsg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="fb_order_status" jdbcType="INTEGER" property="fbOrderStatus" />
    <result column="charge_id" jdbcType="VARCHAR" property="chargeId" />
    <result column="account_type" jdbcType="SMALLINT" property="accountType" />
    <result column="refund_order_status" jdbcType="INTEGER" property="refundOrderStatus" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, employee_id, account_id, fb_order_id, amount, channel, status, sign_info, 
    type, company_id, actual_amount, return_msg, create_time, update_time, receive_time, 
    complete_time, fb_order_status, charge_id, account_type, refund_order_status,trade_no
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from person_pay_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from person_pay_record
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_pay_record
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_pay_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_pay_record (id, order_id, employee_id, 
      account_id, fb_order_id, amount, 
      channel, status, sign_info, 
      type, company_id, actual_amount, 
      return_msg, create_time, update_time, 
      receive_time, complete_time, fb_order_status, 
      charge_id, account_type, refund_order_status,trade_no
      )
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, 
      #{accountId,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, 
      #{channel,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{signInfo,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{companyId,jdbcType=VARCHAR}, #{actualAmount,jdbcType=BIGINT}, 
      #{returnMsg,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{receiveTime,jdbcType=TIMESTAMP}, #{completeTime,jdbcType=TIMESTAMP}, #{fbOrderStatus,jdbcType=INTEGER}, 
      #{chargeId,jdbcType=VARCHAR}, #{accountType,jdbcType=SMALLINT}, #{refundOrderStatus,jdbcType=INTEGER}
      #{tradeNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_pay_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="signInfo != null">
        sign_info,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="actualAmount != null">
        actual_amount,
      </if>
      <if test="returnMsg != null">
        return_msg,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="fbOrderStatus != null">
        fb_order_status,
      </if>
      <if test="chargeId != null">
        charge_id,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="refundOrderStatus != null">
        refund_order_status,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="signInfo != null">
        #{signInfo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="actualAmount != null">
        #{actualAmount,jdbcType=BIGINT},
      </if>
      <if test="returnMsg != null">
        #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fbOrderStatus != null">
        #{fbOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="chargeId != null">
        #{chargeId,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=SMALLINT},
      </if>
      <if test="refundOrderStatus != null">
        #{refundOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from person_pay_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.signInfo != null">
        sign_info = #{record.signInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.actualAmount != null">
        actual_amount = #{record.actualAmount,jdbcType=BIGINT},
      </if>
      <if test="record.returnMsg != null">
        return_msg = #{record.returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.receiveTime != null">
        receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fbOrderStatus != null">
        fb_order_status = #{record.fbOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.chargeId != null">
        charge_id = #{record.chargeId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=SMALLINT},
      </if>
      <if test="record.refundOrderStatus != null">
        refund_order_status = #{record.refundOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_record
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=VARCHAR},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      channel = #{record.channel,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      sign_info = #{record.signInfo,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      actual_amount = #{record.actualAmount,jdbcType=BIGINT},
      return_msg = #{record.returnMsg,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      fb_order_status = #{record.fbOrderStatus,jdbcType=INTEGER},
      charge_id = #{record.chargeId,jdbcType=VARCHAR},
      account_type = #{record.accountType,jdbcType=SMALLINT},
      refund_order_status = #{record.refundOrderStatus,jdbcType=INTEGER},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_record
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="signInfo != null">
        sign_info = #{signInfo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="actualAmount != null">
        actual_amount = #{actualAmount,jdbcType=BIGINT},
      </if>
      <if test="returnMsg != null">
        return_msg = #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fbOrderStatus != null">
        fb_order_status = #{fbOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="chargeId != null">
        charge_id = #{chargeId,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=SMALLINT},
      </if>
      <if test="refundOrderStatus != null">
        refund_order_status = #{refundOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_record
    set order_id = #{orderId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=VARCHAR},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      channel = #{channel,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sign_info = #{signInfo,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=VARCHAR},
      actual_amount = #{actualAmount,jdbcType=BIGINT},
      return_msg = #{returnMsg,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      fb_order_status = #{fbOrderStatus,jdbcType=INTEGER},
      charge_id = #{chargeId,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=SMALLINT},
      refund_order_status = #{refundOrderStatus,jdbcType=INTEGER},
      trade_no = #{tradeNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>