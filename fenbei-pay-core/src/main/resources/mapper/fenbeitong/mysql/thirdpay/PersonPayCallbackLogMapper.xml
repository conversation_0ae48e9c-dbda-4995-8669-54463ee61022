<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PersonPayCallbackLogMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="merch_id" jdbcType="VARCHAR" property="merchId" />
    <result column="charge_id" jdbcType="VARCHAR" property="chargeId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="ret_code" jdbcType="VARCHAR" property="retCode" />
    <result column="ret_info" jdbcType="VARCHAR" property="retInfo" />
    <result column="optional" jdbcType="VARCHAR" property="optional" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="sign_type" jdbcType="VARCHAR" property="signType" />
    <result column="sign_info" jdbcType="VARCHAR" property="signInfo" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="return_msg" jdbcType="VARCHAR" property="returnMsg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="device" jdbcType="VARCHAR" property="device" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, amount, merch_id, charge_id, order_no, ret_code, ret_info, optional, version, 
    sign_type, sign_info, receive_time, complete_time, return_msg, create_time, type, 
    operate_type, status, channel, device
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from person_pay_callback_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from person_pay_callback_log
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_pay_callback_log
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_pay_callback_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_pay_callback_log (id, amount, merch_id, 
      charge_id, order_no, ret_code, 
      ret_info, optional, version, 
      sign_type, sign_info, receive_time, 
      complete_time, return_msg, create_time, 
      type, operate_type, status, 
      channel, device)
    values (#{id,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, #{merchId,jdbcType=VARCHAR}, 
      #{chargeId,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{retCode,jdbcType=VARCHAR}, 
      #{retInfo,jdbcType=VARCHAR}, #{optional,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, 
      #{signType,jdbcType=VARCHAR}, #{signInfo,jdbcType=VARCHAR}, #{receiveTime,jdbcType=TIMESTAMP}, 
      #{completeTime,jdbcType=TIMESTAMP}, #{returnMsg,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=INTEGER}, #{operateType,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, 
      #{channel,jdbcType=VARCHAR}, #{device,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_pay_callback_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="merchId != null">
        merch_id,
      </if>
      <if test="chargeId != null">
        charge_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="retCode != null">
        ret_code,
      </if>
      <if test="retInfo != null">
        ret_info,
      </if>
      <if test="optional != null">
        optional,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="signType != null">
        sign_type,
      </if>
      <if test="signInfo != null">
        sign_info,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="returnMsg != null">
        return_msg,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="device != null">
        device,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="merchId != null">
        #{merchId,jdbcType=VARCHAR},
      </if>
      <if test="chargeId != null">
        #{chargeId,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="retCode != null">
        #{retCode,jdbcType=VARCHAR},
      </if>
      <if test="retInfo != null">
        #{retInfo,jdbcType=VARCHAR},
      </if>
      <if test="optional != null">
        #{optional,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="signType != null">
        #{signType,jdbcType=VARCHAR},
      </if>
      <if test="signInfo != null">
        #{signInfo,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnMsg != null">
        #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        #{device,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from person_pay_callback_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_callback_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.merchId != null">
        merch_id = #{record.merchId,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeId != null">
        charge_id = #{record.chargeId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.retCode != null">
        ret_code = #{record.retCode,jdbcType=VARCHAR},
      </if>
      <if test="record.retInfo != null">
        ret_info = #{record.retInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.optional != null">
        optional = #{record.optional,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.signType != null">
        sign_type = #{record.signType,jdbcType=VARCHAR},
      </if>
      <if test="record.signInfo != null">
        sign_info = #{record.signInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveTime != null">
        receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.returnMsg != null">
        return_msg = #{record.returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.device != null">
        device = #{record.device,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_callback_log
    set id = #{record.id,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      merch_id = #{record.merchId,jdbcType=VARCHAR},
      charge_id = #{record.chargeId,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      ret_code = #{record.retCode,jdbcType=VARCHAR},
      ret_info = #{record.retInfo,jdbcType=VARCHAR},
      optional = #{record.optional,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      sign_type = #{record.signType,jdbcType=VARCHAR},
      sign_info = #{record.signInfo,jdbcType=VARCHAR},
      receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      return_msg = #{record.returnMsg,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=INTEGER},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      device = #{record.device,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_callback_log
    <set>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="merchId != null">
        merch_id = #{merchId,jdbcType=VARCHAR},
      </if>
      <if test="chargeId != null">
        charge_id = #{chargeId,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="retCode != null">
        ret_code = #{retCode,jdbcType=VARCHAR},
      </if>
      <if test="retInfo != null">
        ret_info = #{retInfo,jdbcType=VARCHAR},
      </if>
      <if test="optional != null">
        optional = #{optional,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="signType != null">
        sign_type = #{signType,jdbcType=VARCHAR},
      </if>
      <if test="signInfo != null">
        sign_info = #{signInfo,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnMsg != null">
        return_msg = #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        device = #{device,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_callback_log
    set amount = #{amount,jdbcType=BIGINT},
      merch_id = #{merchId,jdbcType=VARCHAR},
      charge_id = #{chargeId,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      ret_code = #{retCode,jdbcType=VARCHAR},
      ret_info = #{retInfo,jdbcType=VARCHAR},
      optional = #{optional,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      sign_type = #{signType,jdbcType=VARCHAR},
      sign_info = #{signInfo,jdbcType=VARCHAR},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      return_msg = #{returnMsg,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      operate_type = #{operateType,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      device = #{device,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>