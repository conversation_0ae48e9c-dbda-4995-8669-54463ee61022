<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PersonPayRefundLogMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRefundLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="receive_msg" jdbcType="VARCHAR" property="receiveMsg" />
    <result column="return_msg" jdbcType="VARCHAR" property="returnMsg" />
    <result column="fql_request_msg" jdbcType="VARCHAR" property="fqlRequestMsg" />
    <result column="cj_repons_msg" jdbcType="VARCHAR" property="cjReponsMsg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_no, channel, amount, notify_url, receive_time, complete_time, receive_msg, 
    return_msg, fql_request_msg, cj_repons_msg
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRefundLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from person_pay_refund_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from person_pay_refund_log
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_pay_refund_log
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRefundLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_pay_refund_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRefundLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_pay_refund_log (id, order_no, channel, 
      amount, notify_url, receive_time, 
      complete_time, receive_msg, return_msg, 
      fql_request_msg, cj_repons_msg)
    values (#{id,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, 
      #{amount,jdbcType=INTEGER}, #{notifyUrl,jdbcType=VARCHAR}, #{receiveTime,jdbcType=TIMESTAMP}, 
      #{completeTime,jdbcType=TIMESTAMP}, #{receiveMsg,jdbcType=VARCHAR}, #{returnMsg,jdbcType=VARCHAR}, 
      #{fqlRequestMsg,jdbcType=VARCHAR}, #{cjReponsMsg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRefundLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_pay_refund_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="notifyUrl != null">
        notify_url,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="receiveMsg != null">
        receive_msg,
      </if>
      <if test="returnMsg != null">
        return_msg,
      </if>
      <if test="fqlRequestMsg != null">
        fql_request_msg,
      </if>
      <if test="cjReponsMsg != null">
        cj_repons_msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveMsg != null">
        #{receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="returnMsg != null">
        #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="fqlRequestMsg != null">
        #{fqlRequestMsg,jdbcType=VARCHAR},
      </if>
      <if test="cjReponsMsg != null">
        #{cjReponsMsg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRefundLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from person_pay_refund_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_refund_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=INTEGER},
      </if>
      <if test="record.notifyUrl != null">
        notify_url = #{record.notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveTime != null">
        receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.receiveMsg != null">
        receive_msg = #{record.receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.returnMsg != null">
        return_msg = #{record.returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.fqlRequestMsg != null">
        fql_request_msg = #{record.fqlRequestMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.cjReponsMsg != null">
        cj_repons_msg = #{record.cjReponsMsg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_refund_log
    set id = #{record.id,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=INTEGER},
      notify_url = #{record.notifyUrl,jdbcType=VARCHAR},
      receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      receive_msg = #{record.receiveMsg,jdbcType=VARCHAR},
      return_msg = #{record.returnMsg,jdbcType=VARCHAR},
      fql_request_msg = #{record.fqlRequestMsg,jdbcType=VARCHAR},
      cj_repons_msg = #{record.cjReponsMsg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRefundLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_refund_log
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="notifyUrl != null">
        notify_url = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveMsg != null">
        receive_msg = #{receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="returnMsg != null">
        return_msg = #{returnMsg,jdbcType=VARCHAR},
      </if>
      <if test="fqlRequestMsg != null">
        fql_request_msg = #{fqlRequestMsg,jdbcType=VARCHAR},
      </if>
      <if test="cjReponsMsg != null">
        cj_repons_msg = #{cjReponsMsg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRefundLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_pay_refund_log
    set order_no = #{orderNo,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=INTEGER},
      notify_url = #{notifyUrl,jdbcType=VARCHAR},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      receive_msg = #{receiveMsg,jdbcType=VARCHAR},
      return_msg = #{returnMsg,jdbcType=VARCHAR},
      fql_request_msg = #{fqlRequestMsg,jdbcType=VARCHAR},
      cj_repons_msg = #{cjReponsMsg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>