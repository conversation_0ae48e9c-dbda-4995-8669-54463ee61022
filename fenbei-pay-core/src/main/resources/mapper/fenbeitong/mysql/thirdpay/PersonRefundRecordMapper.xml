<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PersonRefundRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="txn_id" jdbcType="VARCHAR" property="txnId" />
    <result column="refund_no" jdbcType="VARCHAR" property="refundNo" />
    <result column="refund_amount" jdbcType="INTEGER" property="refundAmount" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="ex_code" jdbcType="VARCHAR" property="exCode" />
    <result column="ex_msg" jdbcType="VARCHAR" property="exMsg" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="notifyurl" jdbcType="VARCHAR" property="notifyurl" />
    <result column="refund_order_status" jdbcType="VARCHAR" property="refundOrderStatus" />
    <result column="retry_num" jdbcType="INTEGER" property="retryNum" />
    <result column="next_retry_time" jdbcType="TIMESTAMP" property="nextRetryTime" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, txn_id, refund_no, refund_amount, refund_reason, refund_status, receive_time, 
    create_time, update_time, complete_time, ex_code, ex_msg, channel, notifyurl, refund_order_status, 
    retry_num, next_retry_time,trade_no
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from person_refund_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from person_refund_record
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_refund_record
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_refund_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_refund_record (id, order_id, txn_id, 
      refund_no, refund_amount, refund_reason, 
      refund_status, receive_time, create_time, 
      update_time, complete_time, ex_code, 
      ex_msg, channel, notifyurl, 
      refund_order_status, retry_num, next_retry_time,
      trade_no
      )
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{txnId,jdbcType=VARCHAR}, 
      #{refundNo,jdbcType=VARCHAR}, #{refundAmount,jdbcType=INTEGER}, #{refundReason,jdbcType=VARCHAR}, 
      #{refundStatus,jdbcType=INTEGER}, #{receiveTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{completeTime,jdbcType=TIMESTAMP}, #{exCode,jdbcType=VARCHAR}, 
      #{exMsg,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, #{notifyurl,jdbcType=VARCHAR}, 
      #{refundOrderStatus,jdbcType=VARCHAR}, #{retryNum,jdbcType=INTEGER}, #{nextRetryTime,jdbcType=TIMESTAMP},
      #{tradeNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_refund_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="txnId != null">
        txn_id,
      </if>
      <if test="refundNo != null">
        refund_no,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="refundReason != null">
        refund_reason,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="exCode != null">
        ex_code,
      </if>
      <if test="exMsg != null">
        ex_msg,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="notifyurl != null">
        notifyurl,
      </if>
      <if test="refundOrderStatus != null">
        refund_order_status,
      </if>
      <if test="retryNum != null">
        retry_num,
      </if>
      <if test="nextRetryTime != null">
        next_retry_time,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="txnId != null">
        #{txnId,jdbcType=VARCHAR},
      </if>
      <if test="refundNo != null">
        #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=INTEGER},
      </if>
      <if test="refundReason != null">
        #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="exCode != null">
        #{exCode,jdbcType=VARCHAR},
      </if>
      <if test="exMsg != null">
        #{exMsg,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="notifyurl != null">
        #{notifyurl,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderStatus != null">
        #{refundOrderStatus,jdbcType=VARCHAR},
      </if>
      <if test="retryNum != null">
        #{retryNum,jdbcType=INTEGER},
      </if>
      <if test="nextRetryTime != null">
        #{nextRetryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from person_refund_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_refund_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.txnId != null">
        txn_id = #{record.txnId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundNo != null">
        refund_no = #{record.refundNo,jdbcType=VARCHAR},
      </if>
      <if test="record.refundAmount != null">
        refund_amount = #{record.refundAmount,jdbcType=INTEGER},
      </if>
      <if test="record.refundReason != null">
        refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      </if>
      <if test="record.refundStatus != null">
        refund_status = #{record.refundStatus,jdbcType=INTEGER},
      </if>
      <if test="record.receiveTime != null">
        receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.exCode != null">
        ex_code = #{record.exCode,jdbcType=VARCHAR},
      </if>
      <if test="record.exMsg != null">
        ex_msg = #{record.exMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.notifyurl != null">
        notifyurl = #{record.notifyurl,jdbcType=VARCHAR},
      </if>
      <if test="record.refundOrderStatus != null">
        refund_order_status = #{record.refundOrderStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.retryNum != null">
        retry_num = #{record.retryNum,jdbcType=INTEGER},
      </if>
      <if test="record.nextRetryTime != null">
        next_retry_time = #{record.nextRetryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_refund_record
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      txn_id = #{record.txnId,jdbcType=VARCHAR},
      refund_no = #{record.refundNo,jdbcType=VARCHAR},
      refund_amount = #{record.refundAmount,jdbcType=INTEGER},
      refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      refund_status = #{record.refundStatus,jdbcType=INTEGER},
      receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      ex_code = #{record.exCode,jdbcType=VARCHAR},
      ex_msg = #{record.exMsg,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      notifyurl = #{record.notifyurl,jdbcType=VARCHAR},
      refund_order_status = #{record.refundOrderStatus,jdbcType=VARCHAR},
      retry_num = #{record.retryNum,jdbcType=INTEGER},
      next_retry_time = #{record.nextRetryTime,jdbcType=TIMESTAMP},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_refund_record
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="txnId != null">
        txn_id = #{txnId,jdbcType=VARCHAR},
      </if>
      <if test="refundNo != null">
        refund_no = #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=INTEGER},
      </if>
      <if test="refundReason != null">
        refund_reason = #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="exCode != null">
        ex_code = #{exCode,jdbcType=VARCHAR},
      </if>
      <if test="exMsg != null">
        ex_msg = #{exMsg,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="notifyurl != null">
        notifyurl = #{notifyurl,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderStatus != null">
        refund_order_status = #{refundOrderStatus,jdbcType=VARCHAR},
      </if>
      <if test="retryNum != null">
        retry_num = #{retryNum,jdbcType=INTEGER},
      </if>
      <if test="nextRetryTime != null">
        next_retry_time = #{nextRetryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextRetryTime != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_refund_record
    set order_id = #{orderId,jdbcType=VARCHAR},
      txn_id = #{txnId,jdbcType=VARCHAR},
      refund_no = #{refundNo,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=INTEGER},
      refund_reason = #{refundReason,jdbcType=VARCHAR},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      ex_code = #{exCode,jdbcType=VARCHAR},
      ex_msg = #{exMsg,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      notifyurl = #{notifyurl,jdbcType=VARCHAR},
      refund_order_status = #{refundOrderStatus,jdbcType=VARCHAR},
      retry_num = #{retryNum,jdbcType=INTEGER},
      next_retry_time = #{nextRetryTime,jdbcType=TIMESTAMP},
      trade_no = #{tradeNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>