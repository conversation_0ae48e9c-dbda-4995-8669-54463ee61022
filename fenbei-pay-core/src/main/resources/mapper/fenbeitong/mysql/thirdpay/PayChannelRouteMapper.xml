<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PayChannelRouteMapper">
	<resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PayChannelRoute">
	    <id column="id" jdbcType="INTEGER" property="id" />
	    <result column="pay_vendor_name" jdbcType="VARCHAR" property="payVendorName" />
	    <result column="pay_vendor_code" jdbcType="VARCHAR" property="payVendorCode" />
	    <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName" />
	    <result column="pay_channel_code" jdbcType="VARCHAR" property="payChannelCode" />
	    <result column="type" jdbcType="INTEGER" property="type" />
	    <result column="status" jdbcType="INTEGER" property="status" />
	    <result column="specified" jdbcType="INTEGER" property="specified" />
	    <result column="availability" jdbcType="INTEGER" property="availability" />
	    <result column="stability" jdbcType="INTEGER" property="stability" />
	    <result column="user_experience" jdbcType="INTEGER" property="userExperience" />
	    <result column="weight" jdbcType="INTEGER" property="weight" />
	    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
	    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
	  </resultMap>

	<sql id="Base_Column_List">
		id, pay_vendor_name, pay_vendor_code, pay_channel_name, pay_channel_code,
		type, status,
		specified, availability, stability, user_experience, weight, create_time,
		update_time
	</sql>
	
	<select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from tb_pay_channel_route
		where id = #{id,jdbcType=INTEGER}
	</select>
	
	<select id="selectByVendorCode" parameterType="string" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from tb_pay_channel_route
		where pay_vendor_code = #{payVendorCode,jdbcType=VARCHAR}
	</select>
	
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from tb_pay_channel_route
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	<insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PayChannelRoute">
		<selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into tb_pay_channel_route (pay_vendor_name, pay_vendor_code,
		pay_channel_name,
		pay_channel_code, type, status,
		specified, availability, stability,
		user_experience, weight, create_time,
		update_time)
		values (#{payVendorName,jdbcType=VARCHAR},
		#{payVendorCode,jdbcType=VARCHAR}, #{payChannelName,jdbcType=VARCHAR},
		#{payChannelCode,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
		#{status,jdbcType=INTEGER},
		#{specified,jdbcType=INTEGER}, #{availability,jdbcType=INTEGER}, #{stability,jdbcType=INTEGER},
		#{userExperience,jdbcType=INTEGER}, #{weight,jdbcType=INTEGER},
		#{createTime,jdbcType=TIMESTAMP},
		#{updateTime,jdbcType=TIMESTAMP})
		on duplicate key update 
		<trim prefix="" suffixOverrides=",">
			<if test="payVendorName != null">
				pay_vendor_name = #{payVendorName,jdbcType=VARCHAR},
			</if>
			<if test="payVendorCode != null">
				pay_vendor_code = #{payVendorCode,jdbcType=VARCHAR},
			</if>
			<if test="payChannelName != null">
				pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
			</if>
			<if test="payChannelCode != null">
				pay_channel_code = #{payChannelCode,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="specified != null">
				specified = #{specified,jdbcType=INTEGER},
			</if>
			<if test="availability != null">
				availability = #{availability,jdbcType=INTEGER},
			</if>
			<if test="stability != null">
				stability = #{stability,jdbcType=INTEGER},
			</if>
			<if test="userExperience != null">
				user_experience = #{userExperience,jdbcType=INTEGER},
			</if>
			<if test="weight != null">
				weight = #{weight,jdbcType=INTEGER},
			</if>
			update_time = now(),
		</trim>
		
	</insert>
	
	<insert id="batchInsert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PayChannelRoute">
		insert into tb_pay_channel_route (pay_vendor_name, pay_vendor_code,
		pay_channel_name,
		pay_channel_code, type, status,
		specified, availability, stability,
		user_experience, weight, create_time,
		update_time)
		values 
		<foreach collection="list" separator="," item="r" >
			(#{r.payVendorName,jdbcType=VARCHAR},
			#{r.payVendorCode,jdbcType=VARCHAR}, #{r.payChannelName,jdbcType=VARCHAR},
			#{r.payChannelCode,jdbcType=VARCHAR}, #{r.type,jdbcType=INTEGER},
			#{r.status,jdbcType=INTEGER},
			#{r.specified,jdbcType=INTEGER}, #{r.availability,jdbcType=INTEGER}, #{r.stability,jdbcType=INTEGER},
			#{r.userExperience,jdbcType=INTEGER}, #{r.weight,jdbcType=INTEGER},
			now(),now())
		</foreach>
		on duplicate key update 
			pay_vendor_name = values(pay_vendor_name),
			pay_vendor_code = values(pay_vendor_code),
			pay_channel_name = values(pay_channel_name),
			pay_channel_code = values(pay_channel_code),
			type = values(type),
			status = values(status),
			specified = values(specified),
			availability = values(availability),
			stability = values(stability),
			user_experience = values(user_experience),
			weight = values(weight),
			update_time = now()
	</insert>
	
	<insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PayChannelRoute">
		<selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into tb_pay_channel_route
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="payVendorName != null">
				pay_vendor_name,
			</if>
			<if test="payVendorCode != null">
				pay_vendor_code,
			</if>
			<if test="payChannelName != null">
				pay_channel_name,
			</if>
			<if test="payChannelCode != null">
				pay_channel_code,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="specified != null">
				specified,
			</if>
			<if test="availability != null">
				availability,
			</if>
			<if test="stability != null">
				stability,
			</if>
			<if test="userExperience != null">
				user_experience,
			</if>
			<if test="weight != null">
				weight,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="payVendorName != null">
				#{payVendorName,jdbcType=VARCHAR},
			</if>
			<if test="payVendorCode != null">
				#{payVendorCode,jdbcType=VARCHAR},
			</if>
			<if test="payChannelName != null">
				#{payChannelName,jdbcType=VARCHAR},
			</if>
			<if test="payChannelCode != null">
				#{payChannelCode,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				#{type,jdbcType=INTEGER},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="specified != null">
				#{specified,jdbcType=INTEGER},
			</if>
			<if test="availability != null">
				#{availability,jdbcType=INTEGER},
			</if>
			<if test="stability != null">
				#{stability,jdbcType=INTEGER},
			</if>
			<if test="userExperience != null">
				#{userExperience,jdbcType=INTEGER},
			</if>
			<if test="weight != null">
				#{weight,jdbcType=INTEGER},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
		on duplicate key update 
		<trim prefix="" suffixOverrides=",">
			<if test="payVendorName != null">
				pay_vendor_name = #{payVendorName,jdbcType=VARCHAR},
			</if>
			<if test="payVendorCode != null">
				pay_vendor_code = #{payVendorCode,jdbcType=VARCHAR},
			</if>
			<if test="payChannelName != null">
				pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
			</if>
			<if test="payChannelCode != null">
				pay_channel_code = #{payChannelCode,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="specified != null">
				specified = #{specified,jdbcType=INTEGER},
			</if>
			<if test="availability != null">
				availability = #{availability,jdbcType=INTEGER},
			</if>
			<if test="stability != null">
				stability = #{stability,jdbcType=INTEGER},
			</if>
			<if test="userExperience != null">
				user_experience = #{userExperience,jdbcType=INTEGER},
			</if>
			<if test="weight != null">
				weight = #{weight,jdbcType=INTEGER},
			</if>
			update_time = now(),
		</trim>
	</insert>
	
	<update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PayChannelRoute">
		update tb_pay_channel_route
		<set>
			<if test="payVendorName != null">
				pay_vendor_name = #{payVendorName,jdbcType=VARCHAR},
			</if>
			<if test="payVendorCode != null">
				pay_vendor_code = #{payVendorCode,jdbcType=VARCHAR},
			</if>
			<if test="payChannelName != null">
				pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
			</if>
			<if test="payChannelCode != null">
				pay_channel_code = #{payChannelCode,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="specified != null">
				specified = #{specified,jdbcType=INTEGER},
			</if>
			<if test="availability != null">
				availability = #{availability,jdbcType=INTEGER},
			</if>
			<if test="stability != null">
				stability = #{stability,jdbcType=INTEGER},
			</if>
			<if test="userExperience != null">
				user_experience = #{userExperience,jdbcType=INTEGER},
			</if>
			<if test="weight != null">
				weight = #{weight,jdbcType=INTEGER},
			</if>
			
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>