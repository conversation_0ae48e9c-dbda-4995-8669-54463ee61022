<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.bigdata.HoloAdsBillFlowRelationADMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsBillFlowRelationAD">
    <result column="account_flow_id" jdbcType="VARCHAR" property="accountFlowId" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="account_sub_type" jdbcType="INTEGER" property="accountSubType" />
    <result column="balance" jdbcType="NUMERIC" property="balance" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="receipt_status" jdbcType="INTEGER" property="receiptStatus" />
    <result column="receipt_url" jdbcType="VARCHAR" property="receiptUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="operation_amount" jdbcType="NUMERIC" property="operationAmount" />
    <result column="operation_channel_type" jdbcType="INTEGER" property="operationChannelType" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="category_type" jdbcType="INTEGER" property="categoryType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="re_biz_no" jdbcType="VARCHAR" property="reBizNo" />
    <result column="sync_bank_status" jdbcType="INTEGER" property="syncBankStatus" />
    <result column="sync_bank_time" jdbcType="TIMESTAMP" property="syncBankTime" />
    <result column="sync_bank_trans_no" jdbcType="VARCHAR" property="syncBankTransNo" />
    <result column="target_account" jdbcType="VARCHAR" property="targetAccount" />
    <result column="target_account_sub_type" jdbcType="INTEGER" property="targetAccountSubType" />
    <result column="target_bank_full_name" jdbcType="VARCHAR" property="targetBankFullName" />
    <result column="target_bank_name" jdbcType="VARCHAR" property="targetBankName" />
    <result column="trade_type" jdbcType="INTEGER" property="tradeType" />
    <result column="company_main_name" jdbcType="VARCHAR" property="companyMainName" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="target_flow_bank_account_no" jdbcType="VARCHAR" property="targetFlowBankAccountNo" />
    <result column="target_flow_bank_name" jdbcType="VARCHAR" property="targetFlowBankName" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="amt_company_account_pay" jdbcType="NUMERIC" property="amtCompanyAccountPay" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="bill_date" jdbcType="TIMESTAMP" property="billDate" />
    <result column="user_visible_state" jdbcType="INTEGER" property="userVisibleState" />
    <result column="flow_flag" jdbcType="INTEGER" property="flowFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    account_flow_id, account_model, account_sub_type, balance, bank_account_no, bank_name, 
    bank_trans_no, biz_id, company_id, receipt_status, receipt_url, create_time, operation_amount, 
    operation_channel_type, operation_type, operation_user_id, operation_user_name, category_type, 
    remark, re_biz_no, sync_bank_status, sync_bank_time, sync_bank_trans_no, target_account, 
    target_account_sub_type, target_bank_full_name, target_bank_name, trade_type, company_main_name, 
    employee_name, target_flow_bank_account_no, target_flow_bank_name, order_id, amt_company_account_pay,
    product_id, bill_no, bill_date, user_visible_state, flow_flag
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsBillFlowRelationADExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from holo_ads_bill_flow_relation_a_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>

  <select id="selectSummaryInfo" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsBillFlowRelationADExample" resultMap="BaseResultMap">
    select account_flow_id, order_id, amt_company_account_pay, product_id, bill_no, bill_date, user_visible_state
    from holo_ads_bill_flow_relation_a_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>

  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsBillFlowRelationADExample" resultType="java.lang.Long">
    select count(*) from holo_ads_bill_flow_relation_a_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
</mapper>