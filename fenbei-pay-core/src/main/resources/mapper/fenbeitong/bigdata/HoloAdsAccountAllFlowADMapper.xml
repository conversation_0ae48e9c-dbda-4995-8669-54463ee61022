<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.bigdata.HoloAdsAccountAllFlowADMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowAD">
    <result column="flow_flag" jdbcType="INTEGER" property="flowFlag" />
    <result column="account_flow_id" jdbcType="VARCHAR" property="accountFlowId" />
    <result column="account_general_id" jdbcType="VARCHAR" property="accountGeneralId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="account_public_id" jdbcType="VARCHAR" property="accountPublicId" />
    <result column="account_sub_type" jdbcType="INTEGER" property="accountSubType" />
    <result column="balance" jdbcType="NUMERIC" property="balance" />
    <result column="bank_account_acct_name" jdbcType="VARCHAR" property="bankAccountAcctName" />
    <result column="bank_account_name" jdbcType="VARCHAR" property="bankAccountName" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="bank_acct_id" jdbcType="VARCHAR" property="bankAcctId" />
    <result column="bank_channel_name" jdbcType="VARCHAR" property="bankChannelName" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="card_model" jdbcType="INTEGER" property="cardModel" />
    <result column="trans_time" jdbcType="TIMESTAMP" property="transTime" />
    <result column="payer_bank_acct_name" jdbcType="VARCHAR" property="payerBankAcctName" />
    <result column="payer_bank_name" jdbcType="VARCHAR" property="payerBankName" />
    <result column="payer_bank_no" jdbcType="VARCHAR" property="payerBankNo" />
    <result column="payer_company_name" jdbcType="VARCHAR" property="payerCompanyName" />
    <result column="company_account_id" jdbcType="VARCHAR" property="companyAccountId" />
    <result column="callback_next" jdbcType="TIMESTAMP" property="callbackNext" />
    <result column="callback_num" jdbcType="VARCHAR" property="callbackNum" />
    <result column="cashier_txn_id" jdbcType="VARCHAR" property="cashierTxnId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="company_main_id" jdbcType="VARCHAR" property="companyMainId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_model" jdbcType="INTEGER" property="companyModel" />
    <result column="receipt_status" jdbcType="INTEGER" property="receiptStatus" />
    <result column="receipt_time" jdbcType="VARCHAR" property="receiptTime" />
    <result column="receipt_url" jdbcType="VARCHAR" property="receiptUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="current_balance" jdbcType="NUMERIC" property="currentBalance" />
    <result column="fund_platform" jdbcType="INTEGER" property="fundPlatform" />
    <result column="customer_service_id" jdbcType="VARCHAR" property="customerServiceId" />
    <result column="customer_service_name" jdbcType="VARCHAR" property="customerServiceName" />
    <result column="current_amount" jdbcType="NUMERIC" property="currentAmount" />
    <result column="direct_acct_type" jdbcType="VARCHAR" property="directAcctType" />
    <result column="fee_amount" jdbcType="NUMERIC" property="feeAmount" />
    <result column="init_credit" jdbcType="NUMERIC" property="initCredit" />
    <result column="operation_amount" jdbcType="NUMERIC" property="operationAmount" />
    <result column="operation_channel_type" jdbcType="INTEGER" property="operationChannelType" />
    <result column="operation_desc" jdbcType="VARCHAR" property="operationDesc" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="operation_type_desc" jdbcType="VARCHAR" property="operationTypeDesc" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="operation_user_phone" jdbcType="VARCHAR" property="operationUserPhone" />
    <result column="order_channel_scope" jdbcType="INTEGER" property="orderChannelScope" />
    <result column="order_channel_type" jdbcType="INTEGER" property="orderChannelType" />
    <result column="order_channel_type_desc" jdbcType="VARCHAR" property="orderChannelTypeDesc" />
    <result column="category_type" jdbcType="INTEGER" property="categoryType" />
    <result column="payment_purpose" jdbcType="VARCHAR" property="paymentPurpose" />
    <result column="refund_txn_id" jdbcType="VARCHAR" property="refundTxnId" />
    <result column="redcounpon_act_id" jdbcType="VARCHAR" property="redcounponActId" />
    <result column="redcounpon_act_name" jdbcType="VARCHAR" property="redcounponActName" />
    <result column="redcoupon_id" jdbcType="VARCHAR" property="redcouponId" />
    <result column="redcoupon_type" jdbcType="VARCHAR" property="redcouponType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="request_no" jdbcType="VARCHAR" property="requestNo" />
    <result column="re_biz_no" jdbcType="VARCHAR" property="reBizNo" />
    <result column="receive_bank_acct_name" jdbcType="VARCHAR" property="receiveBankAcctName" />
    <result column="receive_bank_name" jdbcType="VARCHAR" property="receiveBankName" />
    <result column="receive_bank_no" jdbcType="VARCHAR" property="receiveBankNo" />
    <result column="receive_company_name" jdbcType="VARCHAR" property="receiveCompanyName" />
    <result column="sync_bank_amount" jdbcType="NUMERIC" property="syncBankAmount" />
    <result column="sync_bank_status" jdbcType="INTEGER" property="syncBankStatus" />
    <result column="sync_bank_time" jdbcType="TIMESTAMP" property="syncBankTime" />
    <result column="sync_bank_trans_no" jdbcType="VARCHAR" property="syncBankTransNo" />
    <result column="target_account" jdbcType="VARCHAR" property="targetAccount" />
    <result column="target_account_sub_type" jdbcType="INTEGER" property="targetAccountSubType" />
    <result column="target_bank_account_name" jdbcType="VARCHAR" property="targetBankAccountName" />
    <result column="target_bank_acct_id" jdbcType="VARCHAR" property="targetBankAcctId" />
    <result column="target_bank_full_name" jdbcType="VARCHAR" property="targetBankFullName" />
    <result column="target_bank_name" jdbcType="VARCHAR" property="targetBankName" />
    <result column="trade_type" jdbcType="INTEGER" property="tradeType" />
    <result column="company_main_name" jdbcType="VARCHAR" property="companyMainName" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="target_flow_bank_account_no" jdbcType="VARCHAR" property="targetFlowBankAccountNo" />
    <result column="target_flow_bank_name" jdbcType="VARCHAR" property="targetFlowBankName" />
    <result column="operation_user_company_id" jdbcType="VARCHAR" property="operationUserCompanyId" />
    <result column="operation_user_company_name" jdbcType="VARCHAR" property="operationUserCompanyName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    flow_flag, account_flow_id, account_general_id, account_id, account_model, account_public_id, 
    account_sub_type, balance, bank_account_acct_name, bank_account_name, bank_account_no, 
    bank_acct_id, bank_channel_name, bank_name, bank_trans_no, biz_id, card_model, trans_time, 
    payer_bank_acct_name, payer_bank_name, payer_bank_no, payer_company_name, company_account_id, 
    callback_next, callback_num, cashier_txn_id, company_id, company_main_id, company_name, 
    company_model, receipt_status, receipt_time, receipt_url, create_time, current_balance, 
    fund_platform, customer_service_id, customer_service_name, current_amount, direct_acct_type, 
    fee_amount, init_credit, operation_amount, operation_channel_type, operation_desc, 
    operation_type, operation_type_desc, operation_user_id, operation_user_name, operation_user_phone, operation_user_company_id, operation_user_company_name,
    order_channel_scope, order_channel_type, order_channel_type_desc, category_type, 
    payment_purpose, refund_txn_id, redcounpon_act_id, redcounpon_act_name,
    redcoupon_id, redcoupon_type, remark, request_no, re_biz_no, receive_bank_acct_name, 
    receive_bank_name, receive_bank_no, receive_company_name, sync_bank_amount, sync_bank_status, 
    sync_bank_time, sync_bank_trans_no, target_account, target_account_sub_type, target_bank_account_name, 
    target_bank_acct_id, target_bank_full_name, target_bank_name, trade_type, company_main_name, 
    employee_name, target_flow_bank_account_no, target_flow_bank_name
  </sql>

  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowADExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from holo_ads_account_all_flow_a_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>

  <select id="selectAccountFlowIds" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowADExample" resultType="java.lang.String">
    select
    <if test="distinct">
      distinct
    </if>
    account_flow_id
    from holo_ads_account_all_flow_a_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>

  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowADExample" resultType="java.lang.Long">
    select count(*) from holo_ads_account_all_flow_a_d
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
</mapper>