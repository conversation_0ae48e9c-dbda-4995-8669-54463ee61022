<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:mongo="http://www.springframework.org/schema/data/mongo"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	   	   http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
   		   http://www.springframework.org/schema/data/mongo
    		   http://www.springframework.org/schema/data/mongo/spring-mongo.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <!-- To translate any MongoExceptions thrown in @Repository annotated classes -->
    <context:annotation-config/>

    <!--这里使用mongo:mongo-client-->
    <mongo:mongo-client id="mongo" replica-set="${mongo.hostport}" credentials="${mongo.username}:${mongo.passwd}@${mongo.db}">
        <mongo:client-options
                min-connections-per-host="5"
                connections-per-host="20"
                connect-timeout="100000"
                max-wait-time="300000"
                heartbeat-frequency="2000"
                heartbeat-connect-timeout="100000"
                max-connection-life-time="150000"
                threads-allowed-to-block-for-connection-multiplier="5"
                write-concern="SAFE"/>
    </mongo:mongo-client>

    <mongo:db-factory id="mongoDbFactory" mongo-ref="mongo" dbname="${mongo.db}" />

    <bean id="mappingConverter" class="org.springframework.data.mongodb.core.convert.MappingMongoConverter">
        <constructor-arg ref="mongoDbFactory" />
        <constructor-arg ref="mappingContext" />
        <property name="typeMapper" ref="defaultMongoTypeMapper" />
    </bean>

    <bean id="mongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
        <constructor-arg name="mongoDbFactory" ref="mongoDbFactory"/>
        <constructor-arg name="mongoConverter" ref="mappingConverter" />
        <property name="writeConcern">
            <util:constant static-field="com.mongodb.WriteConcern.SAFE" />
        </property>
        <property name="writeResultChecking" value="EXCEPTION" />
    </bean>

    <bean id="mappingContext" class="org.springframework.data.mongodb.core.mapping.MongoMappingContext" />

    <!-- 默认Mongodb类型映射 -->
    <bean id="defaultMongoTypeMapper" class="org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper">
        <!-- 这里设置为空,可以把 spring data mongodb 多余保存的_class字段去掉 -->
        <constructor-arg name="typeKey">
            <null/>
        </constructor-arg>
    </bean>
</beans>
