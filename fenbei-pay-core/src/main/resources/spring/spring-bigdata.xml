<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:task="http://www.springframework.org/schema/task"
        xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.2.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.2.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.3.xsd
        http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd"
       default-lazy-init="true">

    <!--<import resource="spring-kms.xml"/>-->

    <bean id="bigDataDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.billflow.driverClassName}"/>
        <property name="jdbcUrl" value="${db.billflow.jdbcUrl}"/>
        <property name="username" value="${db.billflow.username}"/>
        <property name="password" value="${db.billflow.password}"/>
        <!-- 连接只读数据库时配置为true，保证安全 -->
        <property name="readOnly" value="false"/>
        <!-- 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 缺省:30秒 -->
        <property name="connectionTimeout" value="${db.billflow.connectionTimeout}"/>
        <!-- 一个连接idle状态的最大时长（毫秒），超时则被释放（retired），缺省:10分钟 -->
        <property name="idleTimeout" value="${db.billflow.idleTimeout}"/>
        <!-- 一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒，参考MySQL wait_timeout参数（show variables like '%timeout%';） -->
        <property name="maxLifetime" value="${db.billflow.maxLifetime}"/>
        <!-- 连接池中保持的最小空闲连接数。缺省值：同maximumPoolSize -->
        <property name="minimumIdle" value="${db.billflow.minimumIdle}"/>
        <!-- 连接池中允许的最大连接数。缺省值：10；推荐的公式：((core_count * 2) + effective_spindle_count) -->
        <property name="maximumPoolSize" value="${db.billflow.maximumPoolSize}"/>
    </bean>

    <bean id="bigDataTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="bigDataDataSource"/>
        <qualifier value="bigData"/>
    </bean>

    <bean id="bigDataSqlSessionFactory" class="org.mybatis.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="bigDataDataSource"/>
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
        <property name="mapperLocations" value="classpath:mapper/fenbeitong/bigdata/**/*.xml"/>
        <property name="typeAliasesPackage" value="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata"/>
    </bean>

    <bean id="bigDataMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.fenbeitong.fenbeipay.core.dao.fenbeitong.bigdata"/>
        <property name="sqlSessionFactoryBeanName" value="bigDataSqlSessionFactory"/>
    </bean>
    
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.ICompanyInfoService"
                     id="iCompanyInfoService" check="false"/>

    <tx:annotation-driven transaction-manager="bigDataTransactionManager" proxy-target-class="true"/>

</beans>