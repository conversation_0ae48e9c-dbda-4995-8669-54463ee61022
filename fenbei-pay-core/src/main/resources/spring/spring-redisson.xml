<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:redisson="http://redisson.org/schema/redisson"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
       http://redisson.org/schema/redisson http://redisson.org/schema/redisson/redisson.xsd"
       default-lazy-init="true"
>
    <context:annotation-config/>

    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <!--  单台redis机器配置-->
    <redisson:client id="redissonClient">
        <redisson:single-server address="${redis.address}"
                                database="${redis.database}"
                                password="${redis.password}"
                                idle-connection-timeout="10000"
                                connect-timeout="10000"
                                timeout="3000"
                                retry-attempts="3"
                                retry-interval="1500"
                                connection-minimum-idle-size="10"
                                connection-pool-size="30"/>
    </redisson:client>
    <!-- redis集群配置
<redisson:client id="redissonClient" >
    <redisson:cluster-servers scan-interval="10000">
        <redisson:node-address value="*************:7000"></redisson:node-address>
        <redisson:node-address value="*************:7001"></redisson:node-address>
        <redisson:node-address value="*************:7002"></redisson:node-address>
        <redisson:node-address value="*************:7003"></redisson:node-address>
        <redisson:node-address value="*************:7004"></redisson:node-address>
        <redisson:node-address value="*************:7005"></redisson:node-address>
    </redisson:cluster-servers>
</redisson:client>-->
</beans>