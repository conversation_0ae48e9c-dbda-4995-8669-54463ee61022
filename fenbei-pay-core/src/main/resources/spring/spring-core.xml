<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.2.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.2.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.3.xsd"
       default-lazy-init="true">

    <!--<import resource="spring-kms.xml"/>-->

    <!-- fenbeitong数据源 -->
    <bean id="fenbeitongDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.fenbeitong.driver}"/>
        <property name="jdbcUrl" value="${db.fenbeitong.url}"/>
        <property name="username" value="${db.fenbeitong.username}"/>
        <property name="password" value="${db.fenbeitong.password}"/>
        <!-- 连接只读数据库时配置为true，保证安全 -->
        <property name="readOnly" value="false"/>
        <!-- 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 缺省:30秒 -->
        <property name="connectionTimeout" value="${db.fenbeitong.connectionTimeout}"/>
        <!-- 一个连接idle状态的最大时长（毫秒），超时则被释放（retired），缺省:10分钟 -->
        <property name="idleTimeout" value="${db.fenbeitong.idleTimeout}"/>
        <!-- 一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒，参考MySQL wait_timeout参数（show variables like '%timeout%';） -->
        <property name="maxLifetime" value="${db.fenbeitong.maxLifetime}"/>
        <!-- 连接池中保持的最小空闲连接数。缺省值：同maximumPoolSize -->
        <property name="minimumIdle" value="${db.fenbeitong.minimumIdle}"/>
        <!-- 连接池中允许的最大连接数。缺省值：10；推荐的公式：((core_count * 2) + effective_spindle_count) -->
        <property name="maximumPoolSize" value="${db.fenbeitong.maximumPoolSize}"/>
    </bean>

    <!-- fenbeitong事务管理器 -->
    <bean id="fenbeitongTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="fenbeitongDataSource"/>
        <qualifier value="fenbeitong"/>
    </bean>

    <!-- fenbeitong MyBatis配置 -->
    <bean id="fenbeitongSqlSessionFactory" class="org.mybatis.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="fenbeitongDataSource"/>
        <!--指定mybatis-config文件-->
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
        <!-- 类别称，注册后可在mapper.xml里只写类名（不带包名） -->
        <!--<property name="typeAliasesPackage" value="com.fenbeitong.usercenter.core.entity.fenbeitong"/>-->
        <!-- 指定mapper.xml文件位置 -->
        <property name="mapperLocations"
                  value="classpath:mapper/fenbeitong/psql/*/*.xml"/>
    </bean>

    <!-- 扫描basePackage下所有接口-->
    <bean id="fenbeitongMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage"
                  value="com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql"/>
        <property name="sqlSessionFactoryBeanName" value="fenbeitongSqlSessionFactory"/>
    </bean>

    <!-- 使用注解定义事务 -->
    <tx:annotation-driven transaction-manager="fenbeitongTransactionManager" proxy-target-class="true"/>

    <!-- 使用cglib实现aop动态代理 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>


    <!--异步线程-->
    <task:annotation-driven executor="asyncExecutor"/>
    <task:executor id="asyncExecutor" pool-size="4-200" queue-capacity="10" keep-alive="600" rejection-policy="CALLER_RUNS" />
</beans>