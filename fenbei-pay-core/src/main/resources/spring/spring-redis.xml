<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:redis="http://www.springframework.org/schema/redis"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/redis http://www.springframework.org/schema/redis/spring-redis-1.0.xsd
        http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd"
       default-lazy-init="true">

    <cache:annotation-driven/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="redisConnectionFactory"
          class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="hostName" value="${redis.hostName}"/>
        <property name="port" value="${redis.port}"/>
        <property name="password" value="${redis.password}"/>
        <property name="database" value="${redis.database}"/>
        <property name="poolConfig">
            <bean class="redis.clients.jedis.JedisPoolConfig">
                <property name="maxTotal" value="${redis.pool.maxTotal}"/>
                <property name="maxIdle" value="${redis.pool.maxIdle}"/>
                <property name="maxWaitMillis" value="${redis.pool.maxWaitMillis}"/>
                <property name="timeBetweenEvictionRunsMillis" value="${redis.pool.timeBetweenEvictionRunsMillis}"/>
                <property name="minEvictableIdleTimeMillis" value="${redis.pool.minEvictableIdleTimeMillis}"/>
                <property name="testOnBorrow" value="${redis.pool.testOnBorrow}"/>
            </bean>
        </property>
    </bean>

    <bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="redisConnectionFactory"/>
        <property name="keySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="valueSerializer">
            <bean class="org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer"/>
        </property>
        <property name="hashKeySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="hashValueSerializer">
            <bean class="org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer"/>
        </property>
    </bean>
    <redis:listener-container>
        <redis:listener ref="grantTaskConsumer" method="handleGrantFbb" topic="${redis.fbb.task.grant.top}"/>
        <redis:listener ref="recallTaskConsumer" method="handleRecallFbb" topic="${redis.fbb.task.recall.top}"/>
        <redis:listener ref="voucherTaskConsumer" method="handleVouchersTask" topic="${redis.voucher.task.task.top}"/>
        <redis:listener ref="voucherTaskConsumer" method="handleVouchersTaskDetails" topic="${redis.voucher.task.details.top}"/>
    </redis:listener-container>

</beans>