<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:task="http://www.springframework.org/schema/task"
        xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd"
       default-lazy-init="true">

    <bean id="kmsService" class="com.fenbeitong.security.kms.alicloud.KmsServiceAliCloudImpl">
    </bean>

    <bean id="dbMasterFenbeipayPassword" class="com.fenbeitong.security.kms.value.KmsStringValue" depends-on="kmsService">
        <property name="encryptedKey" value="${db.master.fenbeipay.password}"/>
    </bean>

    <bean id="dsSlaveFenbeipayPassword" class="com.fenbeitong.security.kms.value.KmsStringValue" depends-on="kmsService">
        <property name="encryptedKey" value="${ds.slave.fenbeipay.password}"/>
    </bean>

    <bean id="dbFenbeitong1Password" class="com.fenbeitong.security.kms.value.KmsStringValue" depends-on="kmsService">
        <property name="encryptedKey" value="${db.fenbeitong1.password}"/>
    </bean>

    <bean id="dbSlaveFenbeipayPassword" class="com.fenbeitong.security.kms.value.KmsStringValue" depends-on="kmsService">
        <property name="encryptedKey" value="${db.slave.fenbeipay.password}"/>
    </bean>

    <bean id="dbBillflowPassword" class="com.fenbeitong.security.kms.value.KmsStringValue" depends-on="kmsService">
        <property name="encryptedKey" value="${db.billflow.password}"/>
    </bean>

    <bean id="dbFenbeitongPassword" class="com.fenbeitong.security.kms.value.KmsStringValue" depends-on="kmsService">
        <property name="encryptedKey" value="${db.fenbeitong.password}"/>
    </bean>

</beans>