<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <!-- 当返回数据类型为map，设置callSettersOnNulls会把值为null的key也返回 -->
        <setting name="callSettersOnNulls" value="true"/>
        <!-- 下划线转换驼峰 -->
        <setting name="mapUnderscoreToCamelCase" value="true" />
        <!-- 打印sql -->
        <setting name="logImpl" value="STDOUT_LOGGING" />
    </settings>
</configuration>