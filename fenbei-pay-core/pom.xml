<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fenbei-pay</artifactId>
        <groupId>com.fenbeitong</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fenbei-pay-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.luastar</groupId>
            <artifactId>swift-tools</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>usercenter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-activity-api</artifactId>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>

        <!-- http工具 start -->
        <dependency>
            <groupId>com.jakewharton.retrofit</groupId>
            <artifactId>retrofit2-rxjava2-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-jackson</artifactId>
        </dependency>
        <!-- http工具 end -->

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>eventbus_2.11</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
        </dependency>-->

        <!--三方DB-->
        <!--mybatis、mybatis-spring、Spring核心包在finhub-common中-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>net.jodah</groupId>
            <artifactId>expiringmap</artifactId>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper</artifactId>
        </dependency>

        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-generator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-21</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.luastar</groupId>
            <artifactId>swift-i18n</artifactId>
        </dependency>
        <!-- kms dependency -->
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-security</artifactId>
            <version>1.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-bank-api</artifactId>
        </dependency>
    </dependencies>


</project>
